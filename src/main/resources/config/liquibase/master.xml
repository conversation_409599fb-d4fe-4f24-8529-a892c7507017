<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <include file="config/liquibase/changelog/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090548_added_entity_PValue.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090549_added_entity_Property.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090550_added_entity_PropertyValue.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090551_added_entity_Category.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090552_added_entity_CategoryProperty.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090553_added_entity_CategoryPropertyValue.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090554_added_entity_PropertyValueGroup.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090555_added_entity_BrandInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090556_added_entity_CategoryBrand.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090732_added_entity_AuctionSpu.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090733_added_entity_SPUImg.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20180417090734_added_entity_CSPU.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
</databaseChangeLog>
