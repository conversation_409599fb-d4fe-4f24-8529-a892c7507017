<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <property name="now" value="now()" dbms="h2"/>

    <property name="now" value="now()" dbms="mysql"/>
    <property name="autoIncrement" value="true"/>

    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql"/>

    <!--
        Added the entity AuctionSpu.
    -->
    <changeSet id="20180417090732-1" author="jhipster">
        <createTable tableName="auction_spu">
            <column name="id" type="bigint" autoIncrement="${autoIncrement}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="category_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <column name="brand_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <column name="p_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>

            <column name="name" type="varchar(100)">
                <constraints nullable="false" />
            </column>

            <column name="market_price" type="integer">
                <constraints nullable="true" />
            </column>

            <column name="approval_number" type="varchar(32)">
                <constraints nullable="true" />
            </column>

            <column name="jhi_specification" type="varchar(32)">
                <constraints nullable="true" />
            </column>

            <column name="market_time" type="timestamp">
                <constraints nullable="true" />
            </column>

            <column name="packing_list" type="varchar(200)">
                <constraints nullable="true" />
            </column>

            <column name="description" type="varchar(4096)">
                <constraints nullable="true" />
            </column>

            <column name="status" type="integer">
                <constraints nullable="false" />
            </column>

            <column name="pic_url" type="varchar(1000)">
                <constraints nullable="true" />
            </column>

            <column name="tag" type="varchar(4096)">
                <constraints nullable="true" />
            </column>

            <column name="extra_info" type="varchar(4096)">
                <constraints nullable="true" />
            </column>

            <column name="features" type="varchar(4096)">
                <constraints nullable="true" />
            </column>

            <column name="query_extend" type="varchar(512)">
                <constraints nullable="true" />
            </column>

            <column name="keypv" type="varchar(512)">
                <constraints nullable="true" />
            </column>

            <column name="compv" type="varchar(1024)">
                <constraints nullable="true" />
            </column>

            <column name="source" type="integer">
                <constraints nullable="true" />
            </column>

            <column name="version" type="bigint">
                <constraints nullable="true" />
            </column>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>
        <dropDefaultValue tableName="auction_spu" columnName="market_time" columnDataType="datetime"/>

    </changeSet>
    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here, do not remove-->
</databaseChangeLog>
