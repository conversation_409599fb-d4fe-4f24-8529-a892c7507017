# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: http://www.jhipster.tech/profiles/
# More information on configuration properties: http://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
    level:
        ROOT: DEBUG
        com.cowell.scib: DEBUG
        io.github.jhipster: DEBUG

eureka:
    instance:
        prefer-ip-address: true
    client:
        service-url:
            defaultZone: http://admin:${jhipster.registry.password}@************:8761/eureka/

com:
    cowell:
        metric:
            undertow:
                enabled: true


spring:
    profiles:
        active: dev
        include: swagger,no-liquibase
    devtools:
        restart:
            enabled: true
        livereload:
            enabled: false # we use gulp + BrowserSync for livereload
    jackson:
        serialization:
            indent_output: true
    ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: ********************************************************************************************************************************************************************************************************************************************************
        username: scrmrw
        password: 123qaz!@#
        initial-size: 2
        min-idle: 1
        max-active: 10
        max-wait: 60000
        validation-query: select 1
        validation-query-timeout: 10000
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 50
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 600000
        filters: stat

    datasource-dgms0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *****************************************************************************************************************************************************************************************
        username: scrmrw
        password: 123qaz!@#
        initial-size: 5
        min-idle: 1
        max-active: 5
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
        jmx-enabled: true
        validation-query: select 1
        test-on-borrow: true
        test-while-idle: true

    datasource-dgms1:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *****************************************************************************************************************************************************************************************
        username: scrmrw
        password: 123qaz!@#
        initial-size: 5
        min-idle: 1
        max-active: 5
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
        jmx-enabled: true
        validation-query: select 1
        test-on-borrow: true
        test-while-idle: true

    datasource-Tidb:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *****************************************************************************************************************************************************************
        username: supply-chain
        password: YWluCg1#=
        initial-size: 5
        min-idle: 1
        max-active: 5
        max-wait: 60000
        validationQuery: SELECT 1
        validationQueryTimeout: 10000
        testWhileIdle: true
        testOnBorrow: true
        testOnReturn: false
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000

    mail:
        host: localhost
        port: 25
        username:
        password:
    messages:
        cache-seconds: 1
    thymeleaf:
        cache: false
    zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
        base-url: http://localhost:9411
        enabled: false
        locator:
            discovery:
                enabled: true
    rocketmq:
        scib:
            # NameServer地址
            name-server-address: ***********:9876;***********:9876
    cloud:
        stream:
            kafka:
                binder:
                    brokers: ************:9092,************:9092,************:9092
                    zkNodes: ************:2181,************:2181,************:2181/kafka-elk
            bindings:
                sleuth:      #这里用stream给我们提供的默认output，后面会讲到自定义output
                    destination: sleuth
                    content-type: application/json
                    producer:
                        headerMode: raw
    sleuth:
        stream:
            enabled: false

apache:
    rocketmq:
        namesrvAddr: ***********:9876;***********:9876
        product:
            retryTimesWhenSend: 3
            sendMsgTimeout: 8000
        cosumer:
            maxReconsumeTimes: 3
            consumeThreadMax: 1
            consumeThreadMin: 1
        scib:
            #单店推荐 (请求获取提成数据)
            goods-commission-calculate:
                producer-topic: PERFORMANCE_STORE_RECOMMEND_CALCULATE_TOPIC_DEV
                producer-group: PERFORMANCE_STORE_RECOMMEND_CALCULATE_GROUP_DEV
            #单店推荐 (提成数据结果接收)
            goods-commission-result:
                consumer-topic: PERFORMANCE_STORE_RECOMMEND_RESULT_TOPIC_DEV
                consumer-group: PERFORMANCE_STORE_RECOMMEND_RESULT_GROUP_DEV
            #必备目录新增(手动)自产自销
            necessary-goods-add-topic: PERFORMANCE_STORE_RECOMMEND_RESULT_TOPIC_DEV
            necessary-goods-add-topic-group: PERFORMANCE_STORE_RECOMMEND_RESULT_TOPIC_DEV
            # 一店一目全层级数据入五级必备表消息，自产自销
            level-necessary:
                topic: LEVEL_FIVE_NECESSARY_DATA_TOPIC_DEV
                group: LEVEL_FIVE_NECESSARY_DATA_GROUP_DEV
            # 组货任务结果校验
            six-level-necessary-check:
                topic: SIX_LEVEL_NECESSARY_CHECK_TOPIC_DEV
                group: SIX_LEVEL_NECESSARY_CHECK_GROUP_DEV
            # 季节品更新一店一目最小陈列量
            season-goods:
                topic: SEASON_GOODS_TOPIC_DEV
                group: SEASON_GOODS_GROUP_DEV

liquibase:
    contexts: dev

# ===================================================================
# To enable SSL, generate a certificate using:
# keytool -genkey -alias scib -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# https://maximilian-boehm.com/hp2121/Create-a-Java-Keystore-JKS-from-Let-s-Encrypt-Certificates.htm
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#    port: 8443
#    ssl:
#        key-store: keystore.p12
#        key-store-password: <your-password>
#        key-store-type: PKCS12
#        key-alias: scib
# ===================================================================
server:
    port: 9062

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: http://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    http:
        version: V_1_1 # To use HTTP/2 you will need SSL support (see above the "server.ssl" configuration)
        # CORS is disabled by default on microservices, as you should access them through a gateway.
        # If you want to enable it, please uncomment the configuration below.
        # cors:
        # allowed-origins: "*"
        # allowed-methods: "*"
        # allowed-headers: "*"
        # exposed-headers: "Authorization,Link,X-Total-Count"
        # allow-credentials: true
        # max-age: 1800
    security:
        client-authorization:
            access-token-uri: http://uaa/oauth/token
            token-service-id: uaa
            client-id: internal
            client-secret: internal
    mail: # specific JHipster mail property, for standard properties see MailProperties
        from: scib@localhost
        base-url: http://127.0.0.1:9060
    metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
        jmx.enabled: true
        graphite: # Use the "graphite" Maven profile to have the Graphite dependencies
            enabled: false
            host: localhost
            port: 2003
            prefix: scib
        prometheus: # Use the "prometheus" Maven profile to have the Prometheus dependencies
            enabled: false
            endpoint: /prometheusMetrics
        logs: # Reports Dropwizard metrics in the logs
            enabled: false
            report-frequency: 60 # in seconds
    logging:
        logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
            enabled: false
            host: localhost
            port: 5000
            queue-size: 512
        spectator-metrics: # Reports Spectator Circuit Breaker metrics in the logs
            enabled: false
            # edit spring.metrics.export.delay-millis to set report frequency

oauth2:
    signature-verification:
        public-key-endpoint-uri: http://uaa/oauth/token_key
        #ttl for public keys to verify JWT tokens (in ms)
        ttl: 3600000
        #max. rate at which public keys will be fetched (in ms)
        public-key-refresh-rate-limit: 10000
    web-client-configuration:
        #keep in sync with UAA configuration
        client-id: web_app
        secret: changeit

#Tencent cos、sms config
tencent:
    deve:
        region: ap-beijing
        appId: 1256038144
        secretId: AKID53pPq6r2nRzMYFTETWN6d3IMXcWFOYWK
        secretKey: qW4ThmfaXRBkYxCKGE0e7Uow0zoR3Yt2
        baseUrl: https://BUCKET.cos.REGION.myqcloud.com/
        defaultBucketName: gjscrm
    cos:
        region: ap-beijing
        secretId: AKIDgNzcAuqfH9IUVQBH3zy5eAX7gALUhzUw
        secretKey: 7g3vfJpr2nXk0ps48eDgsIqUav528Iqx
        baseUrl: https://b2b-test-1256841541.cos.ap-beijing.myqcloud.com/
        defaultBucketName: b2b-test-1256841541

#xxl-job
xxl:
    job:
        admin:
            addresses: http://xxl-job-admin/xxl-job-admin
        executor:
            ip:
            port: 9999
            appname: scib
            logretentiondays: -1
            logpath: /data/applogs/xxl-job/jobhandler
        accessToken:

#Redis
spring.redisson:
    address[0]: redis://common_microservice_redis-01_test.cowelltech.com:6379
    read-mode: MASTER

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# http://www.jhipster.tech/common-application-properties/
# ===================================================================

application:



#本地开发联调标识
local:
    dev:
        flag: true

cowell:
    common:
        securityaudit:
            audit: false
apollo:
    meta: http://apollo


# api 管控
security.api.white_req_url: https://api-store-test-internal.gaojihealth.cn/nyuwa/api/intranet/records/290/cowell_api?filter=app_name,eq,scib&filter=is_white,eq,否
security.api.white_increment_url: https://api-test-internal.gaojihealth.cn/nyuwa/api/intranet/records/290/cowell_api?filter=app_name,eq,scib&filter=is_white,eq,否&filter=gmt_update,bt,{startTime},{endTime}
security.api.report_health_url: https://api-test-internal.gaojihealth.cn/nyuwa/api/intranet/mdd/290/bu_app/batchSaveMerge?indexName={indexName}
security.api.open_log: false
security.api.processKey: 290.cowell_api.scib
apollosdkconfig:
    is_online: false
    apollo_env: DEV
    cookie_val: bb07bf1a2ee6bd855646708a51c68362f6a7b3e8
    list:
        - business_key: cowellApiProcessor
          app_id: nyuwa
          cur_namespace: middleware.nyuwaCommon
          accurate_keys: 290.cowell_api.scib,urgent_open
permission_config:
    open: false
