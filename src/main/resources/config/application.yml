# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: http://www.jhipster.tech/profiles/
# More information on configuration properties: http://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

eureka:
    client:
        enabled: true
        healthcheck:
            enabled: true
        fetch-registry: true
        register-with-eureka: true
        instance-info-replication-interval-seconds: 10
        registry-fetch-interval-seconds: 10
    instance:
        appname: scib
        instanceId: scib:${spring.application.instance-id:${random.value}}
        lease-renewal-interval-in-seconds: 5
        lease-expiration-duration-in-seconds: 10
        status-page-url-path: ${management.context-path}/info
        health-check-url-path: ${management.context-path}/health
        metadata-map:
            zone: primary # This is needed for the load balancer
            profile: ${spring.profiles.active}
            version: ${info.project.version}
ribbon:
    eureka:
        enabled: true
# See https://github.com/Netflix/Hystrix/wiki/Configuration
#hystrix:
#    command:
#        default:
#            execution:
#                isolation:
#                    thread:
#                        timeoutInMilliseconds: 10000
# See https://github.com/spring-cloud/spring-cloud-netflix/issues/1330
feign:
    hystrix:
        enabled: true

hystrix:
    share-security-context: true
    command:
        default:
            fallback:
                isolation:
                    semaphore:
                        maxConcurrentRequests: 100 #回退最大线程数
            circuitBreaker:
                requestVolumeThreshold: 500 #熔断失败个数，默认20，现在设置为1000
            execution:
                timeout:
                    enabled: true #熔断器超时设置
                isolation:
                    thread:
                        timeoutInMilliseconds: 10000
    threadpool:
        default:
            coreSize: 20 #并发执行最大线程数，默认10
            maxQueueSize: 100 #BlockingQueue的最大队列数，默认值-1
            allowMaximumSizeToDivergeFromCoreSize: true
            maximumSize: 200
            queueSizeRejectionThreshold: 100 #即使maxQueueSize没有达到，达到queueSizeRejectionThreshold该值后，请求也会被拒绝，默认值5

management:
    security:
        roles: ADMIN,ACTUATOR
    context-path: /management
    info:
        git:
            mode: full
    health:
        mail:
            enabled: false # When using the MailService, configure an SMTP server and set this to true
spring:
    profiles:
        active: dev
        include: swagger,no-liquibase
    application:
        name: scib
    jackson:
        serialization.write_dates_as_timestamps: false
#    jpa:
#        open-in-view: false
#        hibernate:
#            #            ddl-auto: update
#            ddl-auto: none
#            naming:
#                physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
#                implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    messages:
        basename: i18n/messages
    mvc:
        favicon:
            enabled: false
    thymeleaf:
        mode: XHTML
security:
    basic:
        enabled: false
    oauth2:
        resource:
            filter-scib: 3

server:
    session:
        cookie:
            http-only: true

info:
    project:
        version: #project.version#

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: http://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    async:
        core-pool-size: 2
        max-pool-size: 50
        queue-capacity: 10000
        # By default CORS is disabled. Uncomment to enable.
        #cors:
        #allowed-origins: "*"
        #allowed-methods: "*"
        #allowed-headers: "*"
        #exposed-headers: "Authorization,Link,X-Total-Count"
        #allow-credentials: true
        #max-age: 1800
    mail:
        from: scib@localhost
    swagger:
        default-include-pattern: /api/.*
        title: scib API
        description: scib API documentation
        version: 0.0.1
        terms-of-service-url:
        contact-name:
        contact-url:
        contact-email:
        license:
        license-url:
    ribbon:
        display-on-active-profiles: dev

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# http://www.jhipster.tech/common-application-properties/
# ===================================================================

application:

#限流
spring.application.name: scib
feign.sentinel.enabled: true
feign.hystrix.enabled: false
spring.cloud.sentinel.transport.dashboard: sentinel-admin:80


spring.cloud.sentinel.datasource.flow.apollo.namespace-name: middleware.rule
spring.cloud.sentinel.datasource.flow.apollo.flow-rules-key: ${spring.application.name}-flow-rules
spring.cloud.sentinel.datasource.flow.apollo.default-flow-rule-value: []
spring.cloud.sentinel.datasource.flow.apollo.rule-type: flow

spring.cloud.sentinel.datasource.degrade.apollo.namespace-name: middleware.rule
spring.cloud.sentinel.datasource.degrade.apollo.flow-rules-key: ${spring.application.name}-degrade-rules
spring.cloud.sentinel.datasource.degrade.apollo.default-flow-rule-value: []
spring.cloud.sentinel.datasource.degrade.apollo.rule-type: degrade

testframe:
    refresh:
        txt: "test string 3"
