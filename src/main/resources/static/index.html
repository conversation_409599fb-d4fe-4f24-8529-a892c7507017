<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>JHipster microservice homepage</title>
    <style>
        ::-moz-selection {
            background: #b3d4fc;
            text-shadow: none;
        }

        ::selection {
            background: #b3d4fc;
            text-shadow: none;
        }

        html {
            padding: 30px 10px;
            font-size: 20px;
            line-height: 1.4;
            color: #737373;
            background: #262c31;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        html,
        input {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }

        body {
            max-width: 1000px;
            _width: 500px;
            padding: 30px 20px 50px;
            border: 1px solid #b3b3b3;
            border-radius: 4px;
            margin: 0 auto;
            box-shadow: 0 1px 10px #a7a7a7, inset 0 1px 0 #fff;
            background: #fcfcfc;
        }

        h1 {
            margin: 0 10px;
            font-size: 50px;
            text-align: center;
        }

        h1 span {
            color: #bbb;
        }

        h3 {
            margin: 1.5em 0 0.5em;
        }

        p {
            margin: 1em 0;
        }

        ul {
            padding: 0 0 0 40px;
            margin: 1em 0;
        }

        .container {
            max-width: 800px;
            _width: 380px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Welcome, Java Hipster!</h1>

    <p>This application is a microservice, which has been generated using <a href="http://www.jhipster.tech/">JHipster</a>.</p>

    <ul>
        <li>It does not have a front-end. The front-end should be generated on a JHipster gateway</li>
        <li>It is serving REST APIs, under the '/api' URLs.</li>
        <li>Swagger documentation endpoint for those APIs is at <a href="/v2/api-docs">/v2/api-docs</a>, but if you want access to the full Swagger UI, you should use a JHipster gateway, which will serve as an API developer portal</li>
    </ul>

    <h2>
        If you have any question on JHipster:
    </h2>

    <ul>
        <li><a href="http://www.jhipster.tech/" target="_blank">JHipster homepage</a></li>
        <li><a href="http://stackoverflow.com/tags/jhipster/info" target="_blank">JHipster on Stack Overflow</a></li>
        <li><a href="https://github.com/jhipster/generator-jhipster/issues?state=open" target="_blank">JHipster bug tracker</a></li>
        <li><a href="https://gitter.im/jhipster/generator-jhipster" target="_blank">JHipster public chat room</a></li>
        <li><a href="https://twitter.com/java_hipster" target="_blank">follow @java_hipster on Twitter</a></li>
    </ul>

    <p>
        <span>If you like JHipster, don't forget to give us a star on</span> <a href="https://github.com/jhipster/generator-jhipster" target="_blank">GitHub</a>!
    </p>

</div>
</body>
</html>
