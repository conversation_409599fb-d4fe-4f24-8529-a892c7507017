<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.DevelopModuleRecordFileExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entity.DevelopModuleRecordFile">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode" />
        <result column="develop_version" jdbcType="VARCHAR" property="developVersion" />
        <result column="code_version_key" jdbcType="VARCHAR" property="codeVersionKey" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    </resultMap>

    <insert id="batchInsertSelective" parameterType="java.util.List">
        insert into develop_module_record_file ( module_code, develop_version, code_version_key, file_name, file_url )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.moduleCode,jdbcType=VARCHAR}, #{item.developVersion,jdbcType=VARCHAR},
            #{item.codeVersionKey,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}, #{item.fileUrl,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="batchDeleteSelective"  parameterType="java.util.List">
        <foreach collection="idList" item="item" index="index" separator=";">
            delete from `develop_module_record_file` where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <delete id="batchDeleteByKey">
        delete from `develop_module_record_file` where code_version_key = #{codeVersionKey,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteFileByCodeAndVersion">
        delete from `develop_module_record_file` where module_code = #{code,jdbcType=VARCHAR} and develop_version = #{version,jdbcType=VARCHAR}
    </delete>
</mapper>