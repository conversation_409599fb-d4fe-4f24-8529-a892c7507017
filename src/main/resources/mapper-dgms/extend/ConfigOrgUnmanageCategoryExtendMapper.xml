<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.ConfigOrgUnmanageCategoryExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.ConfigOrgUnmanageCategory">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="config_id" jdbcType="BIGINT" property="configId" />
        <result column="detail_id" jdbcType="BIGINT" property="detailId" />
        <result column="plat_org_id" jdbcType="BIGINT" property="platOrgId" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="goods_category_id" jdbcType="BIGINT" property="goodsCategoryId" />
        <result column="goods_category_type" jdbcType="INTEGER" property="goodsCategoryType" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.ConfigOrgUnmanageCategory" useGeneratedKeys="true">
        insert into config_org_unmanage_category (config_id, detail_id,
        plat_org_id, store_code, store_name, goods_category_id, goods_category_type,
        created_by, created_name, updated_by, updated_name)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.configId,jdbcType=BIGINT}, #{item.detailId,jdbcType=BIGINT}, #{item.platOrgId,jdbcType=BIGINT},
            #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR},#{item.goodsCategoryId,jdbcType=BIGINT}, #{item.goodsCategoryType,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=BIGINT},#{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteByConfig">
        delete from `config_org_unmanage_category` where config_id = #{configId,jdbcType=BIGINT} and detail_id = #{detailId,jdbcType=BIGINT}
    </delete>
</mapper>