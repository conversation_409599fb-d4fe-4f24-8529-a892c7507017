<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.NecessaryContentsExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.NecessaryContents">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="necessary_tag" jdbcType="VARCHAR" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_type" jdbcType="INTEGER" property="orgType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="store_type_prop" jdbcType="VARCHAR" property="storeTypeProp" />
    <result column="store_type_prop_name" jdbcType="VARCHAR" property="storeTypePropName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category_id" jdbcType="BIGINT" property="middleCategoryId" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category_id" jdbcType="BIGINT" property="smallCategoryId" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="composition" jdbcType="VARCHAR" property="composition" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="purchase_attr" jdbcType="VARCHAR" property="purchaseAttr" />
    <result column="choose_reason" jdbcType="VARCHAR" property="chooseReason" />
    <result column="invalid_reason" jdbcType="VARCHAR" property="invalidReason" />
    <result column="store_sales_rate" jdbcType="DECIMAL" property="storeSalesRate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, necessary_tag, necessary_tag_name, org_id, org_name, org_type, platform_org_id,
    platform_name, company_org_id, business_id, company_code, company_name, store_org_id,
    store_id, store_code, store_name, store_type, store_type_name, store_type_prop, store_type_prop_name,
    province, city, area, category_id, category_name, middle_category_id, middle_category_name,
    small_category_id, small_category_name, sub_category_id, sub_category_name, composition, 
    goods_no, bar_code, goods_common_name, goods_name, goods_unit, description, specifications, 
    dosage_form, manufacturer, approval_number, purchase_attr, choose_reason, invalid_reason, 
    store_sales_rate, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.NecessaryContents" useGeneratedKeys="true">
    insert into necessary_contents (necessary_tag, necessary_tag_name, org_id, 
      org_name, org_type, platform_org_id, platform_name,
      company_org_id, business_id, company_code,
      company_name, store_org_id, store_id, 
      store_code, store_name, store_type, 
      store_type_name, store_type_prop, store_type_prop_name, 
      province, city, area, 
      category_id, category_name, middle_category_id, 
      middle_category_name, small_category_id, small_category_name, 
      sub_category_id, sub_category_name, composition, 
      goods_no, bar_code, goods_common_name, 
      goods_name, goods_unit, description, 
      specifications, dosage_form, manufacturer, 
      approval_number, purchase_attr, choose_reason, 
      invalid_reason, store_sales_rate, `status`, 
      created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.necessaryTag,jdbcType=VARCHAR}, #{item.necessaryTagName,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT},
        #{item.orgName,jdbcType=VARCHAR}, #{item.orgType,jdbcType=INTEGER}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},
        #{item.companyOrgId,jdbcType=BIGINT}, #{item.businessId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR},
        #{item.companyName,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
        #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.storeType,jdbcType=VARCHAR},
        #{item.storeTypeName,jdbcType=VARCHAR}, #{item.storeTypeProp,jdbcType=VARCHAR}, #{item.storeTypePropName,jdbcType=VARCHAR},
        #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR},
        #{item.categoryId,jdbcType=BIGINT}, #{item.categoryName,jdbcType=VARCHAR}, #{item.middleCategoryId,jdbcType=BIGINT},
        #{item.middleCategoryName,jdbcType=VARCHAR}, #{item.smallCategoryId,jdbcType=BIGINT}, #{item.smallCategoryName,jdbcType=VARCHAR},
        #{item.subCategoryId,jdbcType=BIGINT}, #{item.subCategoryName,jdbcType=VARCHAR}, #{item.composition,jdbcType=VARCHAR},
        #{item.goodsNo,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR},
        #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
        #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
        #{item.approvalNumber,jdbcType=VARCHAR}, #{item.purchaseAttr,jdbcType=VARCHAR}, #{item.chooseReason,jdbcType=VARCHAR},
        #{item.invalidReason,jdbcType=VARCHAR}, #{item.storeSalesRate,jdbcType=DECIMAL}, #{item.status,jdbcType=TINYINT},
        #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
        #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
