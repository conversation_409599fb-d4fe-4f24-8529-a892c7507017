<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.BundlingTaskInfoExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.BundlingTaskInfo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
        <result column="task_name" jdbcType="VARCHAR" property="taskName" />
        <result column="task_type" jdbcType="TINYINT" property="taskType" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="memo" jdbcType="VARCHAR" property="memo" />
        <result column="commit_by" jdbcType="BIGINT" property="commitBy" />
        <result column="commit_name" jdbcType="VARCHAR" property="commitName" />
        <result column="issued_by" jdbcType="BIGINT" property="issuedBy" />
        <result column="issued_name" jdbcType="VARCHAR" property="issuedName" />
        <result column="cancel_by" jdbcType="BIGINT" property="cancelBy" />
        <result column="cancel_name" jdbcType="VARCHAR" property="cancelName" />
        <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
        <result column="gmt_commit" jdbcType="TIMESTAMP" property="gmtCommit" />
        <result column="gmt_calculated" jdbcType="TIMESTAMP" property="gmtCalculated" />
        <result column="gmt_issued" jdbcType="TIMESTAMP" property="gmtIssued" />
        <result column="gmt_cancel" jdbcType="TIMESTAMP" property="gmtCancel" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_code, task_name, task_type, org_id, org_name, memo, commit_by, commit_name,
    issued_by, issued_name, cancel_by, cancel_name, task_status, gmt_commit, gmt_calculated,
    gmt_issued, gmt_cancel, `status`, gmt_create, gmt_update, extend, version, created_by,
    created_name, updated_by, updated_name
    </sql>

    <sql id="searchModuleRecordFromWhere">
        from bundling_task_info
        where status=0
        and task_status NOT IN (0)
        <if test="listParam.taskCode != null and listParam.taskCode != ''">
            and task_code = #{listParam.taskCode,jdbcType=VARCHAR}
        </if>
        <if test="listParam.createBy != null">
            and created_by = #{listParam.createBy,jdbcType=BIGINT}
        </if>
        <if test="listParam.taskName != null and listParam.taskName != ''">
            and task_name like concat('%', #{listParam.taskName},'%')
        </if>
        <if test="listParam.taskTypeList != null and listParam.taskTypeList.size() != 0">
            and task_type IN
            <foreach collection="listParam.taskTypeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="listParam.createdName != null and listParam.createdName != ''">
            and created_name = #{listParam.createdName}
        </if>
        <if test="listParam.commitName != null and listParam.commitName != ''">
            and commit_name = #{listParam.commitName}
        </if>
        <if test="listParam.issuedName != null and listParam.issuedName != ''">
            and issued_name = #{listParam.issuedName}
        </if>

        <if test="listParam.gmtCreateStart != null and listParam.gmtCreateEnd != null">
            AND gmt_create BETWEEN #{listParam.gmtCreateStart} AND #{listParam.gmtCreateEnd}
        </if>
        <if test="listParam.gmtCommitStart != null and listParam.gmtCommitEnd != null">
            AND gmt_commit BETWEEN #{listParam.gmtCommitStart} AND #{listParam.gmtCommitEnd}
        </if>
        <if test="listParam.gmtIssuedStart != null and listParam.gmtIssuedEnd != null">
            AND gmt_issued BETWEEN #{listParam.gmtIssuedStart} AND #{listParam.gmtIssuedEnd}
        </if>

        <if test="listParam.taskIdList != null and listParam.taskIdList.size() != 0">
            and id IN
            <foreach collection="listParam.taskIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="listParam.plateOrgId != null">
            AND org_id = #{listParam.plateOrgId}
        </if>
        <if test="listParam.taskStatus != null and listParam.taskStatus.size() != 0">
            and task_status IN
            <foreach collection="listParam.taskStatus" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="searchBundlTaskInfoCount" resultType="java.lang.Long">
        select count(*)
        <include refid="searchModuleRecordFromWhere"></include>
    </select>

    <select id="searchBundlTaskInfoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="searchModuleRecordFromWhere"></include>
        <if test="listParam.taskType != 5">
            order by gmt_update desc
        </if>
        <if test="listParam.taskType == 5">
            order by gmt_create desc
        </if>
        LIMIT #{listParam.offset},#{listParam.perPage}
    </select>

    <select id="searchTaskIdWithOrgIdAndTaskType" resultType="java.lang.Long">
        SELECT id FROM `bundling_task_info`
        WHERE
        org_id = #{orgId}
        <if test="taskStatusList != null and taskStatusList.size() != 0">
            and task_status IN
            <foreach collection="taskStatusList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskTypeList != null and taskTypeList.size() != 0">
            and task_type IN
            <foreach collection="taskTypeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>

</mapper>