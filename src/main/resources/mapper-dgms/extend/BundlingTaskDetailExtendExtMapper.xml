<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtendExtMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.BundlingTaskDetailExtend">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
        <result column="task_detail_id" jdbcType="BIGINT" property="taskDetailId" />
        <result column="extend_type" jdbcType="TINYINT" property="extendType" />
        <result column="keyword" jdbcType="VARCHAR" property="keyword" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, task_code, task_detail_id, extend_type, keyword, `status`, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
    </sql>

    <delete id="deleteDetailByTaskId">
        delete from `bundling_task_detail_extend` where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <insert id="batchInsert">
        insert into bundling_task_detail_extend (task_id, task_code, task_detail_id, extend_type, keyword,
        created_by, created_name, updated_by, updated_name, extend)
        values
        <if test="list != null and list.size() != 0">
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.taskId,jdbcType=BIGINT}, #{item.taskCode,jdbcType=VARCHAR}, #{item.taskDetailId,jdbcType=BIGINT},
                #{item.extendType,jdbcType=TINYINT}, #{item.keyword,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT},
                #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR},
                #{item.extend,jdbcType=VARCHAR})
            </foreach>
        </if>
    </insert>

    <select id="selectExtendByDetailId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bundling_task_detail_extend
        where status=0
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="taskDetailIdList != null and taskDetailIdList.size() != 0">
            and task_detail_id IN
            <foreach collection="taskDetailIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countTaskDetailExtend" resultType="java.lang.Long">
        SELECT count(*)
        FROM bundling_task_detail_extend
        where status=0
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="taskDetailIdList != null and taskDetailIdList.size() != 0">
            and task_detail_id IN
            <foreach collection="taskDetailIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectDetailExtendByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bundling_task_detail_extend
        where status=0
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="taskDetailIdList != null and taskDetailIdList.size() != 0">
            and task_detail_id IN
            <foreach collection="taskDetailIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="page >= 0">
            limit ${page} , ${perPage}
        </if>
    </select>

</mapper>