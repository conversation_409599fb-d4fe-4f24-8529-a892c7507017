<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.PlanMedicalInsuranceContentsExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.PlanMedicalInsuranceContents">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, province, city, goods_no, gmt_create, gmt_update, extend, version, `status`, 
    create_by_id, create_by, update_by_id, update_by
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.PlanMedicalInsuranceContents" useGeneratedKeys="true">
    insert into plan_medical_insurance_contents (province, city, goods_no, 
     create_by_id,
      create_by, update_by_id, update_by
      )
    values
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.createById,jdbcType=BIGINT},
      #{item.createBy,jdbcType=VARCHAR}, #{item.updateById,jdbcType=BIGINT}, #{item.updateBy,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectExistsByExample" resultType="com.cowell.scib.entityDgms.PlanMedicalInsuranceContents"
          parameterType="com.cowell.scib.entityDgms.PlanMedicalInsuranceContentsExample">
    select province, city from plan_medical_insurance_contents
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by province, city
  </select>
</mapper>
