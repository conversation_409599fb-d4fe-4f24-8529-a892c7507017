<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.DevelopModuleRecordExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entity.DevelopModuleRecord">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode" />
        <result column="develop_version" jdbcType="VARCHAR" property="developVersion" />
        <result column="develop_type" jdbcType="TINYINT" property="developType" />
        <result column="develop_title" jdbcType="VARCHAR" property="developTitle" />
        <result column="develop_content" jdbcType="VARCHAR" property="developContent" />
        <result column="develop_status" jdbcType="TINYINT" property="developStatus" />
        <result column="develop_time" jdbcType="TIMESTAMP" property="developTime" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="image_urls" jdbcType="VARCHAR" property="imageUrls" />
    </resultMap>
    <sql id="Base_Column_List">
        id, module_code, develop_version, develop_type, develop_title, develop_content, develop_status,
    develop_time, created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
    </sql>

    <sql id="searchModuleRecordFromWhere">
        from develop_module_record
        where 1=1
        <if test="moduleCode != null and moduleCode != ''">
            and module_code = #{moduleCode}
        </if>
    </sql>

    <select id="searchModuleRecordCount" resultType="java.lang.Long">
        select count(*)
        <include refid="searchModuleRecordFromWhere"></include>
    </select>

    <select id="searchModuleRecordList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="searchModuleRecordFromWhere"></include>
        order by develop_time desc
        LIMIT #{offset},#{perPage}
    </select>

    <select id="searchRencentModuleRecordList" resultMap="BaseResultMap">
        SELECT * FROM `develop_module_record` WHERE module_code = #{moduleCode} AND DATE(develop_time) &lt;= NOW() AND DATE(develop_time) >= DATE_SUB(CURDATE(), INTERVAL #{day} DAY) ORDER BY develop_time DESC, gmt_update DESC LIMIT 1
    </select>
</mapper>