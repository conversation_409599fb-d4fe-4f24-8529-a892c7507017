<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.MdmTaskDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.MdmTaskDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="necessary_tag" jdbcType="INTEGER" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, store_org_id, store_id, store_code, store_name, goods_no, bar_code,
    goods_common_name, goods_name, goods_unit, description, specifications, dosage_form,
    manufacturer, approval_number, necessary_tag, necessary_tag_name, min_display_quantity,
    push_status, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.MdmTaskDetail" useGeneratedKeys="true">
    insert into mdm_task_detail (id, task_id, store_org_id, store_id,
      store_code, store_name, goods_no,
      bar_code, goods_common_name, goods_name,
      goods_unit, description, specifications,
      dosage_form, manufacturer, approval_number, necessary_tag, necessary_tag_name,
      min_display_quantity, push_status, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.id,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
      #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR},
      #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.approvalNumber,jdbcType=VARCHAR}, #{item.necessaryTag,jdbcType=INTEGER},#{item.necessaryTagName,jdbcType=VARCHAR},
      #{item.minDisplayQuantity,jdbcType=DECIMAL}, #{item.pushStatus,jdbcType=TINYINT}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <delete id="deleteByTaskId">
    delete mdm_task_detail where task_id = #{taskId, jdbcType=BIGINT}
  </delete>
  <select id="selectDetailCount"
            resultType="com.cowell.scib.service.dto.necessaryContents.MdmTaskDetailCountDTO">
      select task_id as taskId, count(*) as detailCount, push_status as pushStatus from mdm_task_detail
      where task_id in
      <foreach collection="taskIds" item="taskId" index="index" open="(" close=")" separator="," >
        #{taskId,jdbcType=BIGINT}
      </foreach>
      and push_status in (1,2)
      group by task_id, push_status
    </select>

  <update id="batchUpdatePushStatus">
    update mdm_task_detail set push_status= #{pushStatus,jdbcType=TINYINT}
    where task_id = #{taskId, jdbcType=BIGINT}
    <if test="ids != null and ids.size != 0">
      and id IN
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id,jdbcType=BIGINT}
      </foreach>
    </if>
  </update>

  <select id="selectMdmDetail" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mdm_task_detail
    where task_id = #{taskId, jdbcType=BIGINT}
    <if test="pushStatus!=null">
      and push_status = #{pushStatus, jdbcType=TINYINT}
    </if>
    <if test="createdBy!=null">
      and created_by = #{createdBy, jdbcType=BIGINT}
    </if>
    <if test="id!=null">
      and id &gt;=#{id, jdbcType=BIGINT}
    </if>
    order by id asc
    <if test="size != null">
      limit ${size}
    </if>
  </select>
</mapper>
