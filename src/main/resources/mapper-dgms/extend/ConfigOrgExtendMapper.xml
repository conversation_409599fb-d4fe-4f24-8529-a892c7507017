<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.ConfigOrgExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.ConfigOrg">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="config_type" jdbcType="TINYINT" property="configType" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="out_id" jdbcType="BIGINT" property="outId" />
        <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>
    <sql id="Base_Column_List">
        id, config_type, org_id, org_name, out_id, sap_code, `status`, gmt_create, gmt_update,
    extend, version, created_by, created_name, updated_by, updated_name
    </sql>

    <select id="selectConfigByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from config_org
        where status=0
        and org_id = #{orgId,jdbcType=BIGINT}
        and config_type = #{configType,jdbcType=TINYINT}
        <if test="version != null">
            and version = #{version,jdbcType=INTEGER}
        </if>
    </select>
    <select id="selectConfigDBByOrgId" resultType="com.cowell.scib.service.dto.rule.ConfigOrgDBStyleDTO">
        select
        <include refid="Base_Column_List"/>
        from config_org
        where status=0
        and org_id = #{orgId,jdbcType=BIGINT}
        and config_type = #{configType,jdbcType=TINYINT}
    </select>
</mapper>
