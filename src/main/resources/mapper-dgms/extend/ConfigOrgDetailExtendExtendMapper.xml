<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.ConfigOrgDetailExtendExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.ConfigOrgDetailExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="detail_id" jdbcType="BIGINT" property="detailId" />
    <result column="extend_type" jdbcType="TINYINT" property="extendType" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, config_id, detail_id, extend_type, keyword, `status`, gmt_create, gmt_update,
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.ConfigOrgDetailExtend" useGeneratedKeys="true">
    insert into config_org_detail_extend (config_id, detail_id, extend_type,
      keyword, extend, created_by,
      created_name, updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.configId,jdbcType=BIGINT}, #{item.detailId,jdbcType=BIGINT}, #{item.extendType,jdbcType=TINYINT},
      #{item.keyword,jdbcType=VARCHAR}, #{item.extend,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
      </foreach>
  </insert>

  <delete id="delByConfinIdAndDetailIdAndGoods">
    delete from config_org_detail_extend
    where config_id = #{configId,jdbcType=BIGINT}
    and detail_id = #{detailId,jdbcType=TINYINT}
    <if test="list != null and list.size() != 0">
      and keyword IN
      <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </delete>


  <select id="selectByDetailId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from config_org_detail_extend
    where status=0
    <if test="configId != null">
      and config_id = #{configId,jdbcType=BIGINT}
    </if>
    <if test="detailId != null">
      and detail_id = #{detailId,jdbcType=BIGINT}
    </if>
  </select>

  <select id="selectByDetailIdAndType" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from config_org_detail_extend
    where status=0
    <if test="configId != null">
      and config_id = #{configId,jdbcType=BIGINT}
    </if>
    <if test="detailId != null">
      and detail_id = #{detailId,jdbcType=BIGINT}
    </if>
    <if test="extendType != null">
      and extend_type = #{extendType,jdbcType=TINYINT}
    </if>
  </select>

  <select id="selectByDetailExtendByPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from config_org_detail_extend
    where status=0
    <if test="configId != null">
      and config_id = #{configId,jdbcType=BIGINT}
    </if>
    <if test="detailId != null">
      and detail_id = #{detailId,jdbcType=BIGINT}
    </if>
    <if test="page >= 0">
      limit ${page} , ${perPage}
    </if>
  </select>

  <select id="countConfigOrgDetailExtend" resultType="java.lang.Long">
    SELECT count(0)
    FROM config_org_detail_extend
    where status=0
    <if test="configId != null">
      and config_id = #{configId,jdbcType=BIGINT}
    </if>
    <if test="detailId != null">
      and detail_id = #{detailId,jdbcType=BIGINT}
    </if>
  </select>
  <select id="selectConfigExtendDBByConfigIds" resultType="com.cowell.scib.service.dto.rule.ConfigOrgDetailExtendDBStyleDTO">
    select
    <include refid="Base_Column_List"/>
    FROM config_org_detail_extend
    where status=0
    and config_id in
    <foreach collection="configIds" item="configId" index="index" separator="," open="(" close=")">
      #{configId}
    </foreach>
  </select>

  <insert id="insertConfigOrgDetailExtendList">
    insert into config_org_detail_extend (config_id, detail_id, extend_type,
    keyword, `status`, gmt_create,
    gmt_update, extend, version,
    created_by, created_name, updated_by,
    updated_name)
    values
    <if test="list != null and list.size() != 0">
      <foreach collection="list" item="item" index="index" separator=",">
        (#{item.configId,jdbcType=BIGINT}, #{item.detailId,jdbcType=BIGINT}, #{item.extendType,jdbcType=TINYINT},
        #{item.keyword,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
        #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
        #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
        #{item.updatedName,jdbcType=VARCHAR})
      </foreach>
    </if>
  </insert>

  <delete id="deleteByconfigIdAndType">
    delete from config_org_detail_extend
    where config_id = #{configId,jdbcType=BIGINT}
    and extend_type = #{extendType,jdbcType=TINYINT}
  </delete>
</mapper>
