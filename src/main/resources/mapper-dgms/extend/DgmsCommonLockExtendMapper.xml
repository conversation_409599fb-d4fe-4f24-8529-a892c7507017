<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.DgmsCommonLockExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.DgmsCommonLock">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="lock_key" jdbcType="VARCHAR" property="lockKey" />
    <result column="thread_id" jdbcType="BIGINT" property="threadId" />
    <result column="entry_count" jdbcType="INTEGER" property="entryCount" />
    <result column="host_ip" jdbcType="VARCHAR" property="hostIp" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, lock_key, thread_id, entry_count, host_ip, gmt_create
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.DgmsCommonLock" useGeneratedKeys="true">
    insert into dgms_common_lock (lock_key, thread_id, entry_count, 
      host_ip)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.lockKey,jdbcType=VARCHAR}, #{item.threadId,jdbcType=BIGINT}, #{item.entryCount,jdbcType=INTEGER},
      #{item.hostIp,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectLockKeyByExample" resultType="java.lang.String"
          parameterType="com.cowell.scib.entityDgms.DgmsCommonLockExample">
    select
    lock_key
    from dgms_common_lock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
</mapper>
