<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.OnlineHotGoodsExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.OnlineHotGoods">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="goods_source" jdbcType="VARCHAR" property="goodsSource" />
        <result column="goods_year" jdbcType="VARCHAR" property="goodsYear" />
        <result column="goods_time_dimension" jdbcType="VARCHAR" property="goodsTimeDimension" />
        <result column="goods_time_frame" jdbcType="VARCHAR" property="goodsTimeFrame" />
        <result column="goods_province" jdbcType="VARCHAR" property="goodsProvince" />
        <result column="goods_city" jdbcType="VARCHAR" property="goodsCity" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_rank" jdbcType="VARCHAR" property="goodsRank" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
        <result column="create_by_id" jdbcType="BIGINT" property="createById" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>

    <sql id="Base_Column_List">
        id, goods_source, goods_year, goods_time_dimension, goods_time_frame, goods_province,
    goods_city, bar_code, goods_name, goods_rank, extend, version, gmt_create, gmt_update,
    is_delete, create_by_id, create_by, update_by_id, update_by
    </sql>

    <select id="exitHotGoodsCount" resultType="java.lang.Integer">
        select count(*) from online_hot_goods where 1=1 and is_delete=0
        <trim>
            <if test="goodsSource != null">
                and goods_source = #{goodsSource,jdbcType=VARCHAR}
            </if>
            <if test="goodsYear != null">
                and goods_year = #{goodsYear,jdbcType=VARCHAR}
            </if>
            <if test="goodsTimeDimension != null">
                and goods_time_dimension = #{goodsTimeDimension,jdbcType=VARCHAR}
            </if>
            <if test="goodsTimeFrame != null">
                and goods_time_frame = #{goodsTimeFrame,jdbcType=VARCHAR}
            </if>
        </trim>
        limit 1;
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.OnlineHotGoods" useGeneratedKeys="true">
        insert into online_hot_goods (goods_source, goods_year, goods_time_dimension, goods_time_frame, goods_province,
        goods_city, bar_code, goods_name, goods_rank, create_by_id, create_by, update_by_id, update_by)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.goodsSource,jdbcType=VARCHAR}, #{item.goodsYear,jdbcType=VARCHAR},
            #{item.goodsTimeDimension,jdbcType=VARCHAR}, #{item.goodsTimeFrame,jdbcType=VARCHAR}, #{item.goodsProvince,jdbcType=VARCHAR},
            #{item.goodsCity,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
            #{item.goodsRank,jdbcType=VARCHAR}, #{item.createById,jdbcType=BIGINT},
            #{item.createBy,jdbcType=VARCHAR}, #{item.updateById,jdbcType=BIGINT},
            #{item.updateBy,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>