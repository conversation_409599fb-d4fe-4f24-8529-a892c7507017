<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.BundlingTaskStoreDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
        <result column="bundl_store" jdbcType="VARCHAR" property="bundlStore" />
        <result column="bundl_advice_able" jdbcType="TINYINT" property="bundlAdviceAble" />
        <result column="bundl_confirm_able" jdbcType="TINYINT" property="bundlConfirmAble" />
        <result column="business_id" jdbcType="BIGINT" property="businessId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
        <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="sales_level" jdbcType="VARCHAR" property="salesLevel" />
        <result column="trading_area" jdbcType="VARCHAR" property="tradingArea" />
        <result column="province" jdbcType="VARCHAR" property="province" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="area" jdbcType="VARCHAR" property="area" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="store_status" jdbcType="VARCHAR" property="storeStatus" />
        <result column="open_date" jdbcType="VARCHAR" property="openDate" />
        <result column="close_date" jdbcType="VARCHAR" property="closeDate" />
        <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />
        <result column="format" jdbcType="VARCHAR" property="format" />
        <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
        <result column="special_type" jdbcType="VARCHAR" property="specialType" />
        <result column="dtp" jdbcType="VARCHAR" property="dtp" />
        <result column="plat_store_type_code" jdbcType="VARCHAR" property="platStoreTypeCode" />
        <result column="store_type_code" jdbcType="VARCHAR" property="storeTypeCode" />
        <result column="zs_store_type_code" jdbcType="VARCHAR" property="zsStoreTypeCode" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, task_code, bundl_store, bundl_advice_able, bundl_confirm_able, business_id,
    store_id, store_org_id, company_org_id, company_code, store_code, company_name, store_name,
    sales_level, trading_area, province, city, area, address, store_status, open_date,
    close_date, store_attr, format, operation_type, special_type, dtp, plat_store_type_code,
    store_type_code, zs_store_type_code, `status`, gmt_create, gmt_update, extend, version,
    created_by, created_name, updated_by, updated_name
    </sql>

    <sql id="searchModuleRecordFromWhere">
        from bundling_task_store_detail
        where status=0
        <if test="confirmParam.taskId != null and confirmParam.taskId != ''">
            and task_id = #{confirmParam.taskId}
        </if>
        <if test="confirmParam.storeCodeList != null and confirmParam.storeCodeList.size() != 0">
            and store_code IN
            <foreach collection="confirmParam.storeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="searchBundlTaskStoreCount" resultType="java.lang.Long">
        select count(*)
        <include refid="searchModuleRecordFromWhere"></include>
    </select>

    <select id="searchBundlTaskStoreList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="searchModuleRecordFromWhere"></include>
        order by id desc
        LIMIT #{confirmParam.offset},#{confirmParam.perPage}
    </select>

    <insert id="batchInsert">
        insert into bundling_task_store_detail (task_id, task_code, bundl_store, bundl_advice_able, bundl_confirm_able,
        business_id, store_id, store_org_id, company_org_id, company_code, store_code, company_name,
        store_name, sales_level, trading_area, province, city, area, address, store_status,
        open_date, close_date, store_attr, format, operation_type, special_type, dtp,
        created_by, created_name, updated_by, updated_name, extend, plat_store_type_code, store_type_code,
        zs_store_type_code)
        values
        <if test="list != null and list.size() != 0">
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.taskId,jdbcType=BIGINT}, #{item.taskCode,jdbcType=VARCHAR},
                #{item.bundlStore,jdbcType=VARCHAR}, #{item.bundlAdviceAble,jdbcType=TINYINT},
                #{item.bundlConfirmAble,jdbcType=TINYINT}, #{item.businessId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
                #{item.storeOrgId,jdbcType=BIGINT}, #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR},
                #{item.storeCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR},
                #{item.salesLevel,jdbcType=VARCHAR}, #{item.tradingArea,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR},
                #{item.city,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR},
                #{item.storeStatus,jdbcType=VARCHAR}, #{item.openDate,jdbcType=VARCHAR}, #{item.closeDate,jdbcType=VARCHAR},
                #{item.storeAttr,jdbcType=VARCHAR}, #{item.format,jdbcType=VARCHAR}, #{item.operationType,jdbcType=VARCHAR},
                #{item.specialType,jdbcType=VARCHAR}, #{item.dtp,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT},
                #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR},
                #{item.extend,jdbcType=VARCHAR}, #{item.platStoreTypeCode,jdbcType=VARCHAR}, #{item.storeTypeCode,jdbcType=VARCHAR},
                #{item.zsStoreTypeCode,jdbcType=VARCHAR})
            </foreach>
        </if>
    </insert>

    <delete id="deleteDetailByTaskId">
        delete from `bundling_task_store_detail` where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <select id="searchBundlTaskStoreListByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bundling_task_store_detail
        where task_id = #{taskId,jdbcType=BIGINT}
    </select>

    <select id="searchBundlTaskStoreListByConfirm" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bundling_task_store_detail
        where task_id = #{taskId,jdbcType=BIGINT}
        and bundl_confirm_able = #{bundlConfirmAble,jdbcType=TINYINT}
    </select>

    <select id="countBundlTaskStoreListByTaskId" resultType="java.lang.Long">
        select
        count(*)
        from bundling_task_store_detail
        where task_id = #{taskId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="countBundlTaskStoreListByConfirm" resultType="java.lang.Long">
        select
                count(*)
        from bundling_task_store_detail
        where task_id = #{taskId,jdbcType=BIGINT}
        and bundl_confirm_able = #{bundlConfirmAble,jdbcType=TINYINT}
        limit 1
    </select>

    <select id="searchBundlTaskStoreListByTaskIdAndStoreType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bundling_task_store_detail
        where task_id = #{taskId,jdbcType=BIGINT}
        <if test="platStoreTypeCodeList != null and platStoreTypeCodeList.size() != 0">
            and plat_store_type_code IN
            <foreach collection="platStoreTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="storeTypeCodeList != null and storeTypeCodeList.size() != 0">
            and store_type_code IN
            <foreach collection="storeTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="zsStoreTypeCodeList != null and zsStoreTypeCodeList.size() != 0">
            and zs_store_type_code IN
            <foreach collection="zsStoreTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="searchStoreIdListByTaskIdAndStoreType" resultType="java.lang.Long">
        select
        store_id
        from bundling_task_store_detail
        where task_id = #{taskId,jdbcType=BIGINT} and bundl_confirm_able =1
        <if test="companyOrgId != null">
            and company_org_id=#{companyOrgId,jdbcType=BIGINT}
        </if>
        <if test="city != null">
            and city=#{city,jdbcType=VARCHAR}
        </if>
        <if test="platStoreTypeCodeList != null and platStoreTypeCodeList.size() != 0">
            and plat_store_type_code IN
            <foreach collection="platStoreTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="storeTypeCodeList != null and storeTypeCodeList.size() != 0">
            and store_type_code IN
            <foreach collection="storeTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="zsStoreTypeCodeList != null and zsStoreTypeCodeList.size() != 0">
            and zs_store_type_code IN
            <foreach collection="zsStoreTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="searchStoreCodeByTaskCode" resultType="java.lang.String">
        select
          store_code
        from bundling_task_store_detail
        where 1=1
        <if test="taskIdList != null and taskIdList.size() != 0">
            and task_id IN
            <foreach collection="taskIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by store_code
    </select>
    <select id="searchTaskCodeBytaskIdListAndStoreCode" resultType="java.lang.String">
        select
        task_code
        from bundling_task_store_detail
        where store_code = #{storeSapCode}
        <if test="taskIdList != null and taskIdList.size() != 0">
            and task_id IN
            <foreach collection="taskIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by task_code
    </select>


    <update id="batchUpdateType" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update bundling_task_store_detail
            set bundl_advice_able = #{item.bundlAdviceAble}, bundl_confirm_able = #{item.bundlConfirmAble}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>