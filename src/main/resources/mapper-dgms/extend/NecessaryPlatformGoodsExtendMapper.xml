<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.NecessaryPlatformGoodsExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.NecessaryPlatformGoods">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category_id" jdbcType="BIGINT" property="middleCategoryId" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category_id" jdbcType="BIGINT" property="smallCategoryId" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="composition" jdbcType="VARCHAR" property="composition" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="purchase_attr" jdbcType="VARCHAR" property="purchaseAttr" />
    <result column="choose_reason" jdbcType="VARCHAR" property="chooseReason" />
    <result column="store_sales_rate" jdbcType="DECIMAL" property="storeSalesRate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap id="CommonResultMap" type="com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category_id" jdbcType="BIGINT" property="middleCategoryId" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category_id" jdbcType="BIGINT" property="smallCategoryId" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="composition" jdbcType="VARCHAR" property="composition" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="purchase_attr" jdbcType="VARCHAR" property="purchaseAttr" />
    <result column="choose_reason" jdbcType="VARCHAR" property="chooseReason" />
    <result column="store_sales_rate" jdbcType="DECIMAL" property="storeSalesRate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_org_id, platform_name, store_type, category_id, category_name, middle_category_id, 
    middle_category_name, small_category_id, small_category_name, sub_category_id, sub_category_name, 
    composition, goods_no, bar_code, goods_common_name, goods_name, goods_unit, description, 
    specifications, dosage_form, manufacturer, approval_number, purchase_attr, choose_reason, 
    store_sales_rate, `status`, gmt_create, gmt_update, extend, version, created_by,
    created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.NecessaryPlatformGoods" useGeneratedKeys="true">
    insert into necessary_platform_goods (platform_org_id, platform_name, store_type, 
      category_id, category_name, middle_category_id, 
      middle_category_name, small_category_id, small_category_name, 
      sub_category_id, sub_category_name, composition, 
      goods_no, bar_code, goods_common_name, 
      goods_name, goods_unit, description, 
      specifications, dosage_form, manufacturer, 
      approval_number, purchase_attr, choose_reason,
      store_sales_rate, created_by,
      created_name, updated_by, updated_name,version
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR}, #{item.storeType,jdbcType=VARCHAR},
      #{item.categoryId,jdbcType=BIGINT}, #{item.categoryName,jdbcType=VARCHAR}, #{item.middleCategoryId,jdbcType=BIGINT},
      #{item.middleCategoryName,jdbcType=VARCHAR}, #{item.smallCategoryId,jdbcType=BIGINT}, #{item.smallCategoryName,jdbcType=VARCHAR},
      #{item.subCategoryId,jdbcType=BIGINT}, #{item.subCategoryName,jdbcType=VARCHAR}, #{item.composition,jdbcType=VARCHAR},
      #{item.goodsNo,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR},
      #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
      #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.approvalNumber,jdbcType=VARCHAR}, #{item.purchaseAttr,jdbcType=VARCHAR}, #{item.chooseReason,jdbcType=VARCHAR},
      #{item.storeSalesRate,jdbcType=DECIMAL}, #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}, #{item.version,jdbcType=BIGINT}
      )
    </foreach>
  </insert>
  <select id="queryNecessary" resultMap="CommonResultMap">
    select
    <include refid="Base_Column_List" />
    from necessary_platform_goods
    <if test="param != null">
      <include refid="Query_Where" />
    </if>
    <if test="start != null and pageSize != null">
    limit #{start,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </if>
  </select>
  <select id="countNecessary" resultType="java.lang.Long">
    select
    count(*)
    from necessary_platform_goods
    <if test="param != null">
      <include refid="Query_Where" />
    </if>
  </select>
    <select id="selectExistsGoods" resultType="java.lang.String">
      select goods_no from necessary_platform_goods
      where platform_org_id = #{platformOrgId,jdbcType=BIGINT}
        <if test="goodsNos != null and goodsNos.size() != 0">
            and goods_no in
            <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
                #{goodsNo,jdbcType=VARCHAR}
            </foreach>
        </if>
      <if test="storeTypes != null and storeTypes.size() != 0">
        and store_type in
        <foreach collection="storeTypes" item="storeType" index="index" open="(" close=")" separator=",">
          #{storeType,jdbcType=VARCHAR}
        </foreach>
      </if>
      group by goods_no
    </select>

    <select id="selectExistsGoodsAndStoreType" resultType="com.cowell.scib.service.dto.necessaryContents.NecessaryTaskGoodsCommonDTO">
        select store_type as storeType, goods_no as goodsNo from necessary_platform_goods
        where platform_org_id = #{platformOrgId,jdbcType=BIGINT}
        <if test="goodsNos != null and goodsNos.size() != 0">
            and goods_no in
            <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
                #{goodsNo,jdbcType=VARCHAR}
            </foreach>
        </if>
        and store_type in
        <foreach collection="storeTypes" item="storeType" index="index" open="(" close=")" separator=",">
            #{storeType,jdbcType=VARCHAR}
        </foreach>
    </select>
  <select id="selectCompanyNecessaryExistsGoods"
          resultType="com.cowell.scib.service.dto.necessaryContents.NecessaryPlatformCountDTO">
    select goods_no as goodsNo, category_id as categoryId, count(*) as `count` from necessary_platform_goods where platform_org_id = #{platformOrgId,jdbcType=BIGINT}
    <if test="goodsNos != null and goodsNos.size() != 0">
      and goods_no in
      <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo,jdbcType=VARCHAR}
      </foreach>
    </if>
    group by goods_no
  </select>

  <sql id = "Query_Where">
  <where>
    <if test="param.queryPlatformOrgIdList != null and param.queryPlatformOrgIdList.size > 0">
      and platform_org_id in
      <foreach collection="param.queryPlatformOrgIdList" item="platformOrgId" index="index" open="(" close=")" separator=",">
        #{platformOrgId,jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="param.storeType != null and param.storeType.size > 0">
      and store_type in
      <foreach collection="param.storeType" item="storeType" index="index" open="(" close=")" separator=",">
        #{storeType ,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="param.categoryId != null">
      and category_id = #{param.categoryId ,jdbcType=BIGINT}
    </if>
    <if test="param.middleCategoryId != null">
      and middle_category_id = #{param.middleCategoryId ,jdbcType=BIGINT}
    </if>
    <if test="param.smallCategoryId != null">
      and small_category_id = #{param.smallCategoryId ,jdbcType=BIGINT}
    </if>
    <if test="param.subCategoryId != null">
      and sub_category_id = #{param.subCategoryId ,jdbcType=BIGINT}
    </if>
    <if test="param.goodsNoList != null and param.goodsNoList.size > 0">
      and goods_no in
      <foreach collection="param.goodsNoList" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo}
      </foreach>
    </if>
    <if test="param.goodsName != null and param.goodsName != ''">
      and goods_name like #{param.goodsName ,jdbcType=VARCHAR}
    </if>
    <if test="param.composition != null and param.composition != ''">
      and composition = #{param.composition ,jdbcType=VARCHAR}
    </if>
  </where>
  </sql>
  <delete id="batchDel">
    delete from necessary_platform_goods where
    <if test="ids != null and ids.size > 0">
      id in
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </delete>

  <update id="updateVersion">
    update necessary_platform_goods set version=0 where
    <if test="ids != null and ids.size > 0">
      id in
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

</mapper>
