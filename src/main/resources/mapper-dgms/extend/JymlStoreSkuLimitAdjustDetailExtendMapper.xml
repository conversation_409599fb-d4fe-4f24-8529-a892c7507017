<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.JymlStoreSkuLimitAdjustDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_id" jdbcType="BIGINT" property="adjustId" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="old_store_type" jdbcType="VARCHAR" property="oldStoreType" />
    <result column="old_store_type_name" jdbcType="VARCHAR" property="oldStoreTypeName" />
    <result column="old_sku_max_limit" jdbcType="INTEGER" property="oldSkuMaxLimit" />
    <result column="old_sku_lower_limit" jdbcType="INTEGER" property="oldSkuLowerLimit" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="sku_max_limit" jdbcType="INTEGER" property="skuMaxLimit" />
    <result column="sku_lower_limit" jdbcType="INTEGER" property="skuLowerLimit" />
    <result column="old_configure_id" jdbcType="BIGINT" property="oldConfigureId" />
    <result column="configure_id" jdbcType="BIGINT" property="configureId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, adjust_id, adjust_code, category, category_name, middle_category, middle_category_name, 
    small_category, small_category_name, sub_category, sub_category_name, old_store_type, 
    old_store_type_name, old_sku_max_limit, old_sku_lower_limit, store_type, store_type_name, 
    sku_max_limit, sku_lower_limit, old_configure_id, configure_id, `status`, extend, 
    version, created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_detail (adjust_id, adjust_code, category,
      category_name, middle_category, middle_category_name,
      small_category, small_category_name, sub_category,
      sub_category_name, old_store_type, old_store_type_name,
      old_sku_max_limit, old_sku_lower_limit, store_type,
      store_type_name, sku_max_limit, sku_lower_limit,
      old_configure_id, configure_id, created_by,
      created_name, updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.adjustId,jdbcType=BIGINT}, #{item.adjustCode,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR},
      #{item.categoryName,jdbcType=VARCHAR}, #{item.middleCategory,jdbcType=VARCHAR}, #{item.middleCategoryName,jdbcType=VARCHAR},
      #{item.smallCategory,jdbcType=VARCHAR}, #{item.smallCategoryName,jdbcType=VARCHAR}, #{item.subCategory,jdbcType=VARCHAR},
      #{item.subCategoryName,jdbcType=VARCHAR}, #{item.oldStoreType,jdbcType=VARCHAR}, #{item.oldStoreTypeName,jdbcType=VARCHAR},
      #{item.oldSkuMaxLimit,jdbcType=INTEGER}, #{item.oldSkuLowerLimit,jdbcType=INTEGER}, #{item.storeType,jdbcType=VARCHAR},
      #{item.storeTypeName,jdbcType=VARCHAR}, #{item.skuMaxLimit,jdbcType=INTEGER}, #{item.skuLowerLimit,jdbcType=INTEGER},
      #{item.oldConfigureId,jdbcType=BIGINT}, #{item.configureId,jdbcType=BIGINT}, #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
