<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.DgmsCommonQueueExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.DgmsCommonQueue">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="queue_key" jdbcType="VARCHAR" property="queueKey" />
    <result column="queue_value" jdbcType="VARCHAR" property="queueValue" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, queue_key, queue_value, gmt_create
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.DgmsCommonQueue" useGeneratedKeys="true">
    insert into dgms_common_queue (queue_key, queue_value
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
     (#{item.queueKey,jdbcType=VARCHAR}, #{item.queueValue,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectMinIdByKeys" resultType="java.lang.Long"
          parameterType="java.util.List">
    select min(id) from dgms_common_queue where 1=1
    <if test="queueKeys != null and queueKeys.size > 0">
      and queue_key in
      <foreach collection="queueKeys" item="queueKey" index="index" separator="," open="("
               close=")">
        #{queueKey,jdbcType=VARCHAR}
      </foreach>
    </if>
    group by queue_key
  </select>
</mapper>
