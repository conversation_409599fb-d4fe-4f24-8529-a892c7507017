<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.StoreGoodsInfoExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.StoreGoodsInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="necessary_tag" jdbcType="TINYINT" property="necessaryTag" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_org_id, store_id, store_code, goods_no, sub_category_id, necessary_tag,
    effect_status, `status`, min_display_quantity, gmt_create, gmt_update, extend, version,
    created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.StoreGoodsInfo" useGeneratedKeys="true">
    insert into store_goods_info (id, store_org_id, store_id, store_code,
      goods_no, sub_category_id, necessary_tag,
      effect_status, min_display_quantity,
      created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.id,jdbcType=BIGINT}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
      #{item.goodsNo,jdbcType=VARCHAR}, #{item.subCategoryId,jdbcType=BIGINT}, #{item.necessaryTag,jdbcType=TINYINT},
      #{item.effectStatus,jdbcType=TINYINT}, #{item.minDisplayQuantity,jdbcType=DECIMAL},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="batchDel">
    update store_goods_info set necessary_tag=0,min_display_quantity=0,updated_name=#{updatedName,jdbcType=VARCHAR}  where
    <if test="ids != null and ids.size > 0">
      id in
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    and store_id=#{storeId,jdbcType=BIGINT}
  </update>

  <update id="batchUpdateNecessaryTag">
    update store_goods_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="necessary_tag=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.necessaryTag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="effect_status=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}    then #{item.effectStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="updated_name=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.updatedName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="updated_by=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.updatedBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="min_display_quantity=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.minDisplayQuantity,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where
    store_id=#{storeId,jdbcType=BIGINT}
    and  id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>

  </update>

  <update id="batchUpdateEffectStatus">
    update store_goods_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="effect_status=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.effectStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="updated_name=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}  then #{item.updatedName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="updated_by=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.updatedBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="min_display_quantity=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.minDisplayQuantity,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where
    store_id=#{storeId,jdbcType=BIGINT}
    and  id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchUpdateNecessaryTagByGoodsNo">
    update store_goods_info set necessary_tag=0,min_display_quantity=0
    where goods_no=#{goodsNo,jdbcType=VARCHAR}
    and store_id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </update>
    <update id="updateNecessaryTagByIds">
      update store_goods_info set necessary_tag = #{necessaryTag, jdbcType=TINYINT}, min_display_quantity = #{minDisplayQuantity, jdbcType=DECIMAL}
      where store_id = #{storeId, jdbcType=BIGINT}
      and id in
      <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
        #{id,jdbcType=BIGINT}
      </foreach>
    </update>
  <update id="updateMinDisplayQuantity">
    update store_goods_info set min_display_quantity = #{minDisplayQuantity, jdbcType=DECIMAL}
    where store_id = #{storeId, jdbcType=BIGINT}
    and goods_no in
    <foreach collection="goodsNos" index="index" item="goodsNo" separator="," open="(" close=")">
      #{goodsNo,jdbcType=VARCHAR}
    </foreach>
  </update>
  <select id="selectExistsGoods" resultType="java.lang.String">
    select goods_no from store_goods_info
    where necessary_tag > 0
    and effect_status = 1
    <if test="storeIds != null and storeIds.size() != 0">
      and store_id in
      <foreach collection="storeIds" item="storeId" index="index" open="(" close=")" separator=",">
        #{storeId,jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="goodsNos != null and goodsNos.size() != 0">
      and goods_no in
      <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>
    <select id="selectExistsGoodsNoStatus" resultType="java.lang.String">
    select goods_no from store_goods_info
    where store_id = #{storeId}
    <if test="goodsNos != null and goodsNos.size() != 0">
      and goods_no in
      <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>
</mapper>
