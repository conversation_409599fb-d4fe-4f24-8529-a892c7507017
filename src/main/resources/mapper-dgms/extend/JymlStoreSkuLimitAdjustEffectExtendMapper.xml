<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.JymlStoreSkuLimitAdjustEffectExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_id" jdbcType="BIGINT" property="adjustId" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="old_store_type" jdbcType="VARCHAR" property="oldStoreType" />
    <result column="old_store_type_name" jdbcType="VARCHAR" property="oldStoreTypeName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, adjust_id, platform_org_id, platform_name, company_org_id, business_id, company_code, 
    company_name, province, store_org_id, store_id, store_code, store_name, city, category,
    category_name, middle_category, middle_category_name, small_category, small_category_name,
    sub_category, sub_category_name, old_store_type, old_store_type_name, store_type,
    store_type_name, `status`, extend, version, created_by, created_name, updated_by,
    updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_effect (adjust_id, platform_org_id, platform_name, 
      company_org_id, business_id, company_code, 
      company_name, province, store_org_id, store_id,
      store_code, store_name, city, 
      category, category_name, middle_category, 
      middle_category_name, small_category, small_category_name, 
      sub_category, sub_category_name, old_store_type, 
      old_store_type_name, store_type, store_type_name, 
      created_by, created_name, updated_by,
      updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.adjustId,jdbcType=BIGINT}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},
      #{item.companyOrgId,jdbcType=BIGINT}, #{item.businessId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR},
      #{item.companyName,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR},
      #{item.category,jdbcType=VARCHAR}, #{item.categoryName,jdbcType=VARCHAR}, #{item.middleCategory,jdbcType=VARCHAR},
      #{item.middleCategoryName,jdbcType=VARCHAR}, #{item.smallCategory,jdbcType=VARCHAR}, #{item.smallCategoryName,jdbcType=VARCHAR},
      #{item.subCategory,jdbcType=VARCHAR}, #{item.subCategoryName,jdbcType=VARCHAR}, #{item.oldStoreType,jdbcType=VARCHAR},
      #{item.oldStoreTypeName,jdbcType=VARCHAR}, #{item.storeType,jdbcType=VARCHAR}, #{item.storeTypeName,jdbcType=VARCHAR},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectWithCtrlCategory" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from (
        select * ,CONCAT(category,middle_category,small_category,sub_category) as ctrlCategory, CONCAT(category_name,middle_category_name,small_category_name,sub_category_name) as ctrlCategoryName
        from jyml_store_sku_limit_adjust_effect
        where store_org_id in
          <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator=",">
            #{storeOrgId}
          </foreach>
    ) as a
    where store_org_id in
    <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator=",">
      #{storeOrgId}
    </foreach>
        <if test="ctrlCategoryNames != null and ctrlCategoryNames.size > 0">
          and a.ctrlCategoryName in
          <foreach collection="ctrlCategoryNames" item="ctrlCategoryName" index="index" open="(" close=")" separator=",">
            #{ctrlCategoryName}
          </foreach>
        </if>
        <if test="ctrlCategorys != null and ctrlCategorys.size > 0">
          and a.ctrlCategory in
          <foreach collection="ctrlCategorys" item="ctrlCategory" index="index" open="(" close=")" separator=",">
            #{ctrlCategory}
          </foreach>
        </if>
  </select>
</mapper>
