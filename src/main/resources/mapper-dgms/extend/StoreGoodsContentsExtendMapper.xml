<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.StoreGoodsContentsExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.StoreGoodsContents">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="necessary_tag" jdbcType="INTEGER" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="manage_status" jdbcType="INTEGER" property="manageStatus" />
    <result column="suggest_manage_status" jdbcType="INTEGER" property="suggestManageStatus" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_distr" jdbcType="VARCHAR" property="forbidDistr" />
    <result column="forbid_sale" jdbcType="VARCHAR" property="forbidSale" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_return_allot" jdbcType="VARCHAR" property="forbidReturnAllot" />
    <result column="sensitive" jdbcType="VARCHAR" property="sensitive" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="apply_limit_upper" jdbcType="DECIMAL" property="applyLimitUpper" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_org_id, store_id, store_code, goods_no, sub_category_id, necessary_tag, 
    necessary_tag_name, manage_status, suggest_manage_status, forbid_apply, forbid_distr,
    forbid_sale, forbid_return_warehouse, forbid_return_allot, `sensitive`, push_level,
    min_display_quantity, apply_limit_upper, effect_status, `status`, gmt_create, gmt_update,
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.StoreGoodsContents" useGeneratedKeys="true">
    insert into store_goods_contents (id, store_org_id, store_id, store_code,
      goods_no, sub_category_id, necessary_tag, 
      necessary_tag_name, manage_status,
      suggest_manage_status,
      forbid_apply, forbid_distr, forbid_sale, 
      forbid_return_warehouse, forbid_return_allot, 
      `sensitive`, push_level, min_display_quantity, 
      apply_limit_upper, effect_status, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.id,jdbcType=BIGINT}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
    #{item.goodsNo,jdbcType=VARCHAR}, #{item.subCategoryId,jdbcType=BIGINT}, #{item.necessaryTag,jdbcType=INTEGER},
    #{item.necessaryTagName,jdbcType=VARCHAR}, #{item.manageStatus,jdbcType=INTEGER},
    #{item.suggestManageStatus,jdbcType=INTEGER},
    #{item.forbidApply,jdbcType=VARCHAR}, #{item.forbidDistr,jdbcType=VARCHAR}, #{item.forbidSale,jdbcType=VARCHAR},
    #{item.forbidReturnWarehouse,jdbcType=VARCHAR}, #{item.forbidReturnAllot,jdbcType=VARCHAR},
    #{item.sensitive,jdbcType=VARCHAR}, #{item.pushLevel,jdbcType=VARCHAR}, #{item.minDisplayQuantity,jdbcType=DECIMAL},
    #{item.applyLimitUpper,jdbcType=DECIMAL}, #{item.effectStatus,jdbcType=TINYINT}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
    #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
    <update id="batchUpdate" parameterType="java.util.List">
      update store_goods_contents
      <trim prefix="set" suffixOverrides=",">
        <trim prefix="necessary_tag=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.necessaryTag,jdbcType=INTEGER}
          </foreach>
        </trim>
        <trim prefix="necessary_tag_name=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.necessaryTagName,jdbcType=VARCHAR}
          </foreach>
        </trim>
        <trim prefix="manage_status=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}    then #{item.manageStatus,jdbcType=INTEGER}
          </foreach>
        </trim>
        <trim prefix="suggest_manage_status=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}    then #{item.suggestManageStatus,jdbcType=INTEGER}
          </foreach>
        </trim>
        <trim prefix="updated_name=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.updatedName,jdbcType=VARCHAR}
          </foreach>
        </trim>
        <trim prefix="updated_by=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.updatedBy,jdbcType=BIGINT}
          </foreach>
        </trim>
        <trim prefix="min_display_quantity=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.minDisplayQuantity,jdbcType=DECIMAL}
          </foreach>
        </trim>
        <trim prefix="forbid_apply=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and id=#{item.id,jdbcType=BIGINT}   then #{item.forbidApply,jdbcType=VARCHAR}
          </foreach>
        </trim>
      </trim>
      where
      store_id=#{storeId,jdbcType=BIGINT}
      and id in
      <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
        #{item.id,jdbcType=BIGINT}
      </foreach>
    </update>
    <update id="batchUpdateManageStatus" parameterType="java.util.List">
      update store_goods_contents
      <trim prefix="set" suffixOverrides=",">
        <trim prefix="manage_status=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and goods_no=#{item.goodsNo,jdbcType=VARCHAR}    then #{item.manageStatus,jdbcType=INTEGER}
          </foreach>
        </trim>
        <trim prefix="suggest_manage_status=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and goods_no=#{item.goodsNo,jdbcType=VARCHAR}    then #{item.suggestManageStatus,jdbcType=INTEGER}
          </foreach>
        </trim>
      </trim>
      where
      store_id=#{storeId,jdbcType=BIGINT}
      and goods_no in
      <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
        #{item.goodsNo,jdbcType=VARCHAR}
      </foreach>
    </update>
    <update id="batchUpdateCategory" parameterType="java.util.List">
      update store_goods_contents
      <trim prefix="set" suffixOverrides=",">
        <trim prefix="sub_category_id=case" suffix="end,">
          <foreach collection="list" item="item" index="index">
            WHEN store_id=#{storeId,jdbcType=BIGINT}  and goods_no=#{item.goodsNo,jdbcType=VARCHAR} then #{item.subCategoryId,jdbcType=BIGINT}
          </foreach>
        </trim>
      </trim>
      where
      store_id=#{storeId,jdbcType=BIGINT}
      and goods_no in
      <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
        #{item.goodsNo,jdbcType=VARCHAR}
      </foreach>
    </update>
</mapper>
