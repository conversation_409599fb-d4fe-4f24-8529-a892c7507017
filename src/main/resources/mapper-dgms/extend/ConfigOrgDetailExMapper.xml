<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.ConfigOrgDetailExMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.ConfigOrgDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="config_id" jdbcType="BIGINT" property="configId" />
        <result column="perproty_type" jdbcType="TINYINT" property="perprotyType" />
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
        <result column="perproty_value" jdbcType="VARCHAR" property="perprotyValue" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>
    <sql id="Base_Column_List">
        id, config_id, perproty_type, dict_code, perproty_value, `status`, gmt_create, gmt_update,
        extend, version, created_by, created_name, updated_by, updated_name
    </sql>

    <select id="selectByConfigIdAndDictCodes"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from config_org_detail
        where status=0
        and config_id = #{configId,jdbcType=BIGINT}
        <if test="list != null and list.size() != 0">
            and dict_code IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="selectByConfigIdAndTypeAndDictCodes"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from config_org_detail
        where status=0
        and config_id = #{configId,jdbcType=BIGINT}
        and perproty_type = #{perprotyType,jdbcType=TINYINT}
        <if test="list != null and list.size() != 0">
            and dict_code IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectConfigDetailDBByConfigIds" resultType="com.cowell.scib.service.dto.rule.ConfigOrgDetailDBStyleDTO">
        select
        <include refid="Base_Column_List" />
        from config_org_detail
        where status=0
        and config_id in
        <foreach collection="configIds" item="configId" index="index" separator="," open="(" close=")">
            #{configId}
        </foreach>
    </select>

    <insert id="insertConfigOrgDetailList">
        insert into config_org_detail (config_id, perproty_type, dict_code,
        perproty_value, `status`, gmt_create,
        gmt_update, extend, version,
        created_by, created_name, updated_by,
        updated_name)
        values
        <if test="list != null and list.size() != 0">
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.configId,jdbcType=BIGINT}, #{item.perprotyType,jdbcType=TINYINT}, #{item.dictCode,jdbcType=VARCHAR},
                #{item.perprotyValue,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
                #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
                #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
                #{item.updatedName,jdbcType=VARCHAR})
            </foreach>
        </if>
    </insert>

    <delete id="deleteByconfigIdAndDictCodes">
        delete from config_org_detail
        where config_id = #{configId,jdbcType=BIGINT}
        <if test="list != null and list.size() != 0">
            and dict_code IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </delete>

</mapper>
