<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.CommonDictionaryExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.CommonDictionary">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
        <result column="scope_code" jdbcType="VARCHAR" property="scopeCode" />
        <result column="scope_desc" jdbcType="VARCHAR" property="scopeDesc" />
        <result column="edit_able" jdbcType="TINYINT" property="editAble" />
        <result column="alias" jdbcType="VARCHAR" property="alias" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
        <result column="create_by_id" jdbcType="BIGINT" property="createById" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    </resultMap>

    <sql id="Base_Column_List">
        id, dict_name, dict_code, default_value, scope_code, scope_desc, edit_able, `alias`,
    gmt_create, gmt_update, extend, version, is_delete, create_by_id, create_by, update_by,
    update_by_id
    </sql>

    <select id="selectByScopeCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_dictionary
        where  scope_code = #{scopeCode,jdbcType=VARCHAR} and is_delete=0
    </select>

    <select id="selectListByScopeCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_dictionary
        where 1=1
        <if test="scopeCodeList != null and scopeCodeList.size() != 0">
            and scope_code IN
            <foreach collection="scopeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and is_delete=0
    </select>

    <select id="selectByDicCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_dictionary
        where  dict_code = #{dicCode,jdbcType=VARCHAR} and is_delete=0
    </select>

    <select id="selectDefValueByScopeCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_dictionary
        where  scope_code = #{scopeCode,jdbcType=VARCHAR} and is_delete=0 and default_value IS NOT NULL
    </select>
</mapper>