<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.BundlingTaskDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
        <result column="task_step" jdbcType="TINYINT" property="taskStep" />
        <result column="perproty_type" jdbcType="TINYINT" property="perprotyType" />
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
        <result column="perproty_value" jdbcType="VARCHAR" property="perprotyValue" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, task_code, task_step, perproty_type, dict_code, perproty_value, `status`, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
    </sql>

    <select id="listTaskDetailByListParam"  resultMap="BaseResultMap">
        select
            id, task_id, task_code, perproty_type, dict_code, perproty_value
        from bundling_task_detail
        where 1=1
        and status = 0
        <if test="listParam.createBy != null">
            and created_by = #{listParam.createBy,jdbcType=BIGINT}
        </if>
        <if test="listParam.bundlGoodsBigKinds != null and listParam.bundlGoodsBigKinds.size() != 0">
            and perproty_value IN
            <foreach collection="listParam.bundlGoodsBigKinds" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="listParam.commpanyOrgIds != null and listParam.commpanyOrgIds.size() != 0">
            and perproty_value IN
            <foreach collection="listParam.commpanyOrgIds" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="listParam.bundlStores != null and listParam.bundlStores.size() != 0">
            and perproty_value IN
            <foreach collection="listParam.bundlStores" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="listParam.zsStores != null and listParam.zsStores.size() != 0">
            and perproty_value IN
            <foreach collection="listParam.zsStores" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="listParam.disposeStores != null and listParam.disposeStores.size() != 0">
            and perproty_value IN
            <foreach collection="listParam.disposeStores" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listTaskDetailByStep"  resultMap="BaseResultMap">
        select
        id, task_id, task_code, perproty_type, dict_code, perproty_value
        from bundling_task_detail
        where 1=1
        and status = 0
        and task_step = 1
        and perproty_type = 2
        <if test="listParam.createBy != null">
            and created_by = #{listParam.createBy,jdbcType=BIGINT}
        </if>
        <if test="listParam.taskIdList != null and listParam.taskIdList.size() != 0">
            and task_id IN
            <foreach collection="listParam.taskIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="listStoreZsDisposeType"  resultType="java.lang.String">
        select
        distinct perproty_value
        from bundling_task_detail
        where 1=1
        and status = 0
        and perproty_type = 2
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="storeTypeList != null and storeTypeList.size() != 0">
            and dict_code IN
            <foreach collection="storeTypeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByTaskIdAndDictCodes"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bundling_task_detail
        where 1=1
        and status = 0
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="dictCodeList != null and dictCodeList.size() != 0">
            and dict_code IN
            <foreach collection="dictCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectDictValueListByCode"  resultType="java.lang.String">
        select
        perproty_value
        from bundling_task_detail
        where 1=1
        and status = 0
        and perproty_type = 2
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="dictCodeList != null and dictCodeList.size() != 0">
            and dict_code IN
            <foreach collection="dictCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByTaskIdAndStep"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bundling_task_detail
        where 1=1
        and status = 0
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="taskStep != null">
            and task_step = #{taskStep,jdbcType=TINYINT}
        </if>
    </select>

    <delete id="deleteDetailByStep">
        delete from `bundling_task_detail` where task_id = #{taskId,jdbcType=BIGINT}
        and task_step = #{taskStep,jdbcType=TINYINT}
    </delete>

    <insert id="batchInsert">
        insert into bundling_task_detail (task_id, task_code, task_step, perproty_type, dict_code, perproty_value,
        created_by, created_name, updated_by, updated_name)
        values
        <if test="list != null and list.size() != 0">
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.taskId,jdbcType=BIGINT}, #{item.taskCode,jdbcType=VARCHAR},
                #{item.taskStep,jdbcType=TINYINT}, #{item.perprotyType,jdbcType=TINYINT}, #{item.dictCode,jdbcType=VARCHAR},
                #{item.perprotyValue,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
                #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
            </foreach>
        </if>
    </insert>

    <select id="selectDetailByTaskId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bundling_task_detail
        where status=0
        and task_id = #{taskId,jdbcType=BIGINT}
    </select>
</mapper>