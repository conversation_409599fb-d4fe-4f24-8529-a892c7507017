<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.CommonEnumsExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.CommonEnums">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="property_code" jdbcType="VARCHAR" property="propertyCode" />
        <result column="property_desc" jdbcType="VARCHAR" property="propertyDesc" />
        <result column="enum_name" jdbcType="VARCHAR" property="enumName" />
        <result column="enum_value" jdbcType="VARCHAR" property="enumValue" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
        <result column="create_by_id" jdbcType="BIGINT" property="createById" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>

    <sql id="Base_Column_List">
        id, property_code, property_desc, enum_name, enum_value, gmt_create, gmt_update,
    extend, version, is_delete, create_by_id, create_by, update_by_id, update_by
    </sql>

    <select id="selectByDicCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_enums
        where  property_code = #{dicCode,jdbcType=VARCHAR} and is_delete=0
    </select>

    <select id="selectByDicCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_enums
        where is_delete=0
        <trim>
            <if test="dicCodeList != null and dicCodeList.size>0">
                and property_code in
                <foreach collection="dicCodeList" item="item" index="index" separator="," open="("
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
        order by id asc
    </select>

    <select id="selectByDicCodeAndValueList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_enums
        where is_delete=0
        <trim>
            <if test="dicCodeList != null and dicCodeList.size>0">
                and property_code in
                <foreach collection="dicCodeList" item="item" index="index" separator="," open="("
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
        <trim>
            <if test="enumValueList != null and enumValueList.size>0">
                and enum_value in
                <foreach collection="enumValueList" item="item" index="index" separator="," open="("
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="selectByDicName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from common_enums
        where  enum_name = #{dicName,jdbcType=VARCHAR} and is_delete=0
    </select>
</mapper>