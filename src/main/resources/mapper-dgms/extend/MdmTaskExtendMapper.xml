<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.MdmTaskExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.MdmTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_source" jdbcType="TINYINT" property="taskSource" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="detail_count" jdbcType="INTEGER" property="detailCount" />
    <result column="bundling_task_id" jdbcType="BIGINT" property="bundlingTaskId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_source, task_status, detail_count, bundling_task_id, `status`, remarks,
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <update id="batchUpdate" parameterType="map">
    update mdm_task
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="task_status=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.taskStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>
