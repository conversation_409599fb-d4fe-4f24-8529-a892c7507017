<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.MinDisplayChangeRecordExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.MinDisplayChangeRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, store_org_id, store_id, store_code, store_name, goods_no, goods_name, min_display_quantity,
        `status`, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by,
        updated_name
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.MinDisplayChangeRecord" useGeneratedKeys="true">
        insert into min_display_change_record (store_org_id, store_id, store_code,
        store_name, goods_no, goods_name,
        min_display_quantity, `status`, gmt_create,
        gmt_update, extend, version,
        created_by, created_name, updated_by,
        updated_name)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
            #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
            #{item.minDisplayQuantity,jdbcType=DECIMAL}, #{item.status,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedName,jdbcType=VARCHAR})
        </foreach>
    </insert>


    <delete id="batchDel">
        delete from min_display_change_record where 1=1
        <if test="storeIdList != null and storeIdList.size > 0">
            and store_id in
            <foreach collection="storeIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="goodsNoList != null and goodsNoList.size > 0">
            and goods_no in
            <foreach collection="goodsNoList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </delete>
</mapper>