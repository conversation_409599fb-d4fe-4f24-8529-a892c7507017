<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.StoreGoodsProcessExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.StoreGoodsProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="necessary_tag" jdbcType="INTEGER" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_org_id, store_id, store_code, goods_no, sub_category_id, necessary_tag, 
    necessary_tag_name, min_display_quantity, effect_status, `status`, gmt_create, gmt_update, 
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.StoreGoodsProcess" useGeneratedKeys="true">
    insert into store_goods_process (id, store_org_id, store_id, store_code,
      goods_no, sub_category_id, necessary_tag, 
      necessary_tag_name, min_display_quantity, effect_status, 
      created_by,
      created_name, updated_by, updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.id,jdbcType=BIGINT}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
            #{item.goodsNo,jdbcType=VARCHAR}, #{item.subCategoryId,jdbcType=BIGINT}, #{item.necessaryTag,jdbcType=INTEGER},
            #{item.necessaryTagName,jdbcType=VARCHAR}, #{item.minDisplayQuantity,jdbcType=DECIMAL}, #{item.effectStatus,jdbcType=TINYINT},
            #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
    <select id="selectGoodsNecessaryTag" resultType="com.cowell.scib.entityDgms.StoreGoodsProcess">
      select goods_no as goodsNo, min(necessary_tag) as necessaryTag, necessary_tag_name as necessaryTagName
      from store_goods_process
    where store_id = #{storeId}
      and goods_no in
    <foreach collection="goodsNos" item="goodsNo" index="index" separator="," open="(" close=")">
      #{goodsNo}
    </foreach>
      group by goods_no, necessary_tag, necessary_tag_name
    </select>
</mapper>
