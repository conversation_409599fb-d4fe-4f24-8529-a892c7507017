<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.extend.MdmPushStoreGoodsErrorRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.MdmPushStoreGoodsErrorRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="auto_code" jdbcType="VARCHAR" property="autoCode" />
    <result column="min_display_quantity" jdbcType="VARCHAR" property="minDisplayQuantity" />
    <result column="necessary_tag_desc" jdbcType="VARCHAR" property="necessaryTagDesc" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, store_code, goods_no, auto_code, min_display_quantity, necessary_tag_desc, 
    error_msg, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.MdmPushStoreGoodsErrorRecord" useGeneratedKeys="true">
    insert into mdm_push_store_goods_error_record (company_code, store_code, goods_no, 
      auto_code, min_display_quantity, necessary_tag_desc, 
      error_msg
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.companyCode,jdbcType=VARCHAR}, #{item.storeCode,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.autoCode,jdbcType=VARCHAR}, #{item.minDisplayQuantity,jdbcType=VARCHAR}, #{item.necessaryTagDesc,jdbcType=VARCHAR},
      #{item.errorMsg,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>
