<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.JymlAdjustmentCycleMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlAdjustmentCycle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="start_month" jdbcType="TIMESTAMP" property="startMonth" />
    <result column="adjust_channel_open_date" jdbcType="DATE" property="adjustChannelOpenDate" />
    <result column="days_after_start_date_forbid_effect" jdbcType="INTEGER" property="daysAfterStartDateForbidEffect" />
    <result column="days_after_effect_close_channel" jdbcType="INTEGER" property="daysAfterEffectCloseChannel" />
    <result column="adjust_frequency" jdbcType="VARCHAR" property="adjustFrequency" />
    <result column="need_confirm" jdbcType="INTEGER" property="needConfirm" />
    <result column="send_wx_notice" jdbcType="INTEGER" property="sendWxNotice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, start_month, adjust_channel_open_date, days_after_start_date_forbid_effect, 
    days_after_effect_close_channel, adjust_frequency, need_confirm, send_wx_notice, 
    `status`, gmt_create, gmt_update, extend, version, create_by_id, create_by, update_by_id, 
    update_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_adjustment_cycle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_adjustment_cycle
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_adjustment_cycle
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycleExample">
    delete from jyml_adjustment_cycle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycle">
    insert into jyml_adjustment_cycle (id, org_id, org_name, 
      start_month, adjust_channel_open_date, days_after_start_date_forbid_effect, 
      days_after_effect_close_channel, adjust_frequency, 
      need_confirm, send_wx_notice, `status`, 
      gmt_create, gmt_update, extend, 
      version, create_by_id, create_by, 
      update_by_id, update_by)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{startMonth,jdbcType=TIMESTAMP}, #{adjustChannelOpenDate,jdbcType=DATE}, #{daysAfterStartDateForbidEffect,jdbcType=INTEGER}, 
      #{daysAfterEffectCloseChannel,jdbcType=INTEGER}, #{adjustFrequency,jdbcType=VARCHAR}, 
      #{needConfirm,jdbcType=INTEGER}, #{sendWxNotice,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createById,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, 
      #{updateById,jdbcType=BIGINT}, #{updateBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycle">
    insert into jyml_adjustment_cycle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="startMonth != null">
        start_month,
      </if>
      <if test="adjustChannelOpenDate != null">
        adjust_channel_open_date,
      </if>
      <if test="daysAfterStartDateForbidEffect != null">
        days_after_start_date_forbid_effect,
      </if>
      <if test="daysAfterEffectCloseChannel != null">
        days_after_effect_close_channel,
      </if>
      <if test="adjustFrequency != null">
        adjust_frequency,
      </if>
      <if test="needConfirm != null">
        need_confirm,
      </if>
      <if test="sendWxNotice != null">
        send_wx_notice,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="startMonth != null">
        #{startMonth,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustChannelOpenDate != null">
        #{adjustChannelOpenDate,jdbcType=DATE},
      </if>
      <if test="daysAfterStartDateForbidEffect != null">
        #{daysAfterStartDateForbidEffect,jdbcType=INTEGER},
      </if>
      <if test="daysAfterEffectCloseChannel != null">
        #{daysAfterEffectCloseChannel,jdbcType=INTEGER},
      </if>
      <if test="adjustFrequency != null">
        #{adjustFrequency,jdbcType=VARCHAR},
      </if>
      <if test="needConfirm != null">
        #{needConfirm,jdbcType=INTEGER},
      </if>
      <if test="sendWxNotice != null">
        #{sendWxNotice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycleExample" resultType="java.lang.Long">
    select count(*) from jyml_adjustment_cycle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_adjustment_cycle
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.startMonth != null">
        start_month = #{record.startMonth,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adjustChannelOpenDate != null">
        adjust_channel_open_date = #{record.adjustChannelOpenDate,jdbcType=DATE},
      </if>
      <if test="record.daysAfterStartDateForbidEffect != null">
        days_after_start_date_forbid_effect = #{record.daysAfterStartDateForbidEffect,jdbcType=INTEGER},
      </if>
      <if test="record.daysAfterEffectCloseChannel != null">
        days_after_effect_close_channel = #{record.daysAfterEffectCloseChannel,jdbcType=INTEGER},
      </if>
      <if test="record.adjustFrequency != null">
        adjust_frequency = #{record.adjustFrequency,jdbcType=VARCHAR},
      </if>
      <if test="record.needConfirm != null">
        need_confirm = #{record.needConfirm,jdbcType=INTEGER},
      </if>
      <if test="record.sendWxNotice != null">
        send_wx_notice = #{record.sendWxNotice,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_adjustment_cycle
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      start_month = #{record.startMonth,jdbcType=TIMESTAMP},
      adjust_channel_open_date = #{record.adjustChannelOpenDate,jdbcType=DATE},
      days_after_start_date_forbid_effect = #{record.daysAfterStartDateForbidEffect,jdbcType=INTEGER},
      days_after_effect_close_channel = #{record.daysAfterEffectCloseChannel,jdbcType=INTEGER},
      adjust_frequency = #{record.adjustFrequency,jdbcType=VARCHAR},
      need_confirm = #{record.needConfirm,jdbcType=INTEGER},
      send_wx_notice = #{record.sendWxNotice,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycle">
    update jyml_adjustment_cycle
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="startMonth != null">
        start_month = #{startMonth,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustChannelOpenDate != null">
        adjust_channel_open_date = #{adjustChannelOpenDate,jdbcType=DATE},
      </if>
      <if test="daysAfterStartDateForbidEffect != null">
        days_after_start_date_forbid_effect = #{daysAfterStartDateForbidEffect,jdbcType=INTEGER},
      </if>
      <if test="daysAfterEffectCloseChannel != null">
        days_after_effect_close_channel = #{daysAfterEffectCloseChannel,jdbcType=INTEGER},
      </if>
      <if test="adjustFrequency != null">
        adjust_frequency = #{adjustFrequency,jdbcType=VARCHAR},
      </if>
      <if test="needConfirm != null">
        need_confirm = #{needConfirm,jdbcType=INTEGER},
      </if>
      <if test="sendWxNotice != null">
        send_wx_notice = #{sendWxNotice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.JymlAdjustmentCycle">
    update jyml_adjustment_cycle
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      start_month = #{startMonth,jdbcType=TIMESTAMP},
      adjust_channel_open_date = #{adjustChannelOpenDate,jdbcType=DATE},
      days_after_start_date_forbid_effect = #{daysAfterStartDateForbidEffect,jdbcType=INTEGER},
      days_after_effect_close_channel = #{daysAfterEffectCloseChannel,jdbcType=INTEGER},
      adjust_frequency = #{adjustFrequency,jdbcType=VARCHAR},
      need_confirm = #{needConfirm,jdbcType=INTEGER},
      send_wx_notice = #{sendWxNotice,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>