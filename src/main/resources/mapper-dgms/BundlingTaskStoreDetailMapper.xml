<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.BundlingTaskStoreDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.BundlingTaskStoreDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
    <result column="bundl_store" jdbcType="VARCHAR" property="bundlStore" />
    <result column="bundl_advice_able" jdbcType="TINYINT" property="bundlAdviceAble" />
    <result column="bundl_confirm_able" jdbcType="TINYINT" property="bundlConfirmAble" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="sales_level" jdbcType="VARCHAR" property="salesLevel" />
    <result column="trading_area" jdbcType="VARCHAR" property="tradingArea" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="store_status" jdbcType="VARCHAR" property="storeStatus" />
    <result column="open_date" jdbcType="VARCHAR" property="openDate" />
    <result column="close_date" jdbcType="VARCHAR" property="closeDate" />
    <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />
    <result column="format" jdbcType="VARCHAR" property="format" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="special_type" jdbcType="VARCHAR" property="specialType" />
    <result column="dtp" jdbcType="VARCHAR" property="dtp" />
    <result column="plat_store_type_code" jdbcType="VARCHAR" property="platStoreTypeCode" />
    <result column="store_type_code" jdbcType="VARCHAR" property="storeTypeCode" />
    <result column="zs_store_type_code" jdbcType="VARCHAR" property="zsStoreTypeCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, task_code, bundl_store, bundl_advice_able, bundl_confirm_able, business_id, 
    store_id, store_org_id, company_org_id, company_code, store_code, company_name, store_name, 
    sales_level, trading_area, province, city, area, address, store_status, open_date, 
    close_date, store_attr, format, operation_type, special_type, dtp, plat_store_type_code, 
    store_type_code, zs_store_type_code, `status`, gmt_create, gmt_update, extend, version, 
    created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bundling_task_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bundling_task_store_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bundling_task_store_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetailExample">
    delete from bundling_task_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetail">
    insert into bundling_task_store_detail (id, task_id, task_code, 
      bundl_store, bundl_advice_able, bundl_confirm_able, 
      business_id, store_id, store_org_id, 
      company_org_id, company_code, store_code, 
      company_name, store_name, sales_level, 
      trading_area, province, city, 
      area, address, store_status, 
      open_date, close_date, store_attr, 
      format, operation_type, special_type, 
      dtp, plat_store_type_code, store_type_code, 
      zs_store_type_code, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{taskCode,jdbcType=VARCHAR}, 
      #{bundlStore,jdbcType=VARCHAR}, #{bundlAdviceAble,jdbcType=TINYINT}, #{bundlConfirmAble,jdbcType=TINYINT}, 
      #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{storeOrgId,jdbcType=BIGINT}, 
      #{companyOrgId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{salesLevel,jdbcType=VARCHAR}, 
      #{tradingArea,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{storeStatus,jdbcType=VARCHAR}, 
      #{openDate,jdbcType=VARCHAR}, #{closeDate,jdbcType=VARCHAR}, #{storeAttr,jdbcType=VARCHAR}, 
      #{format,jdbcType=VARCHAR}, #{operationType,jdbcType=VARCHAR}, #{specialType,jdbcType=VARCHAR}, 
      #{dtp,jdbcType=VARCHAR}, #{platStoreTypeCode,jdbcType=VARCHAR}, #{storeTypeCode,jdbcType=VARCHAR}, 
      #{zsStoreTypeCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetail">
    insert into bundling_task_store_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskCode != null">
        task_code,
      </if>
      <if test="bundlStore != null">
        bundl_store,
      </if>
      <if test="bundlAdviceAble != null">
        bundl_advice_able,
      </if>
      <if test="bundlConfirmAble != null">
        bundl_confirm_able,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="salesLevel != null">
        sales_level,
      </if>
      <if test="tradingArea != null">
        trading_area,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="storeStatus != null">
        store_status,
      </if>
      <if test="openDate != null">
        open_date,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="storeAttr != null">
        store_attr,
      </if>
      <if test="format != null">
        format,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="specialType != null">
        special_type,
      </if>
      <if test="dtp != null">
        dtp,
      </if>
      <if test="platStoreTypeCode != null">
        plat_store_type_code,
      </if>
      <if test="storeTypeCode != null">
        store_type_code,
      </if>
      <if test="zsStoreTypeCode != null">
        zs_store_type_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="bundlStore != null">
        #{bundlStore,jdbcType=VARCHAR},
      </if>
      <if test="bundlAdviceAble != null">
        #{bundlAdviceAble,jdbcType=TINYINT},
      </if>
      <if test="bundlConfirmAble != null">
        #{bundlConfirmAble,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="salesLevel != null">
        #{salesLevel,jdbcType=VARCHAR},
      </if>
      <if test="tradingArea != null">
        #{tradingArea,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="storeStatus != null">
        #{storeStatus,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        #{openDate,jdbcType=VARCHAR},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        #{storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="format != null">
        #{format,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="specialType != null">
        #{specialType,jdbcType=VARCHAR},
      </if>
      <if test="dtp != null">
        #{dtp,jdbcType=VARCHAR},
      </if>
      <if test="platStoreTypeCode != null">
        #{platStoreTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeCode != null">
        #{storeTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="zsStoreTypeCode != null">
        #{zsStoreTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetailExample" resultType="java.lang.Long">
    select count(*) from bundling_task_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bundling_task_store_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskCode != null">
        task_code = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bundlStore != null">
        bundl_store = #{record.bundlStore,jdbcType=VARCHAR},
      </if>
      <if test="record.bundlAdviceAble != null">
        bundl_advice_able = #{record.bundlAdviceAble,jdbcType=TINYINT},
      </if>
      <if test="record.bundlConfirmAble != null">
        bundl_confirm_able = #{record.bundlConfirmAble,jdbcType=TINYINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.salesLevel != null">
        sales_level = #{record.salesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.tradingArea != null">
        trading_area = #{record.tradingArea,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.storeStatus != null">
        store_status = #{record.storeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.openDate != null">
        open_date = #{record.openDate,jdbcType=VARCHAR},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=VARCHAR},
      </if>
      <if test="record.storeAttr != null">
        store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.format != null">
        format = #{record.format,jdbcType=VARCHAR},
      </if>
      <if test="record.operationType != null">
        operation_type = #{record.operationType,jdbcType=VARCHAR},
      </if>
      <if test="record.specialType != null">
        special_type = #{record.specialType,jdbcType=VARCHAR},
      </if>
      <if test="record.dtp != null">
        dtp = #{record.dtp,jdbcType=VARCHAR},
      </if>
      <if test="record.platStoreTypeCode != null">
        plat_store_type_code = #{record.platStoreTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeTypeCode != null">
        store_type_code = #{record.storeTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.zsStoreTypeCode != null">
        zs_store_type_code = #{record.zsStoreTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bundling_task_store_detail
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      task_code = #{record.taskCode,jdbcType=VARCHAR},
      bundl_store = #{record.bundlStore,jdbcType=VARCHAR},
      bundl_advice_able = #{record.bundlAdviceAble,jdbcType=TINYINT},
      bundl_confirm_able = #{record.bundlConfirmAble,jdbcType=TINYINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      sales_level = #{record.salesLevel,jdbcType=VARCHAR},
      trading_area = #{record.tradingArea,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      store_status = #{record.storeStatus,jdbcType=VARCHAR},
      open_date = #{record.openDate,jdbcType=VARCHAR},
      close_date = #{record.closeDate,jdbcType=VARCHAR},
      store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      format = #{record.format,jdbcType=VARCHAR},
      operation_type = #{record.operationType,jdbcType=VARCHAR},
      special_type = #{record.specialType,jdbcType=VARCHAR},
      dtp = #{record.dtp,jdbcType=VARCHAR},
      plat_store_type_code = #{record.platStoreTypeCode,jdbcType=VARCHAR},
      store_type_code = #{record.storeTypeCode,jdbcType=VARCHAR},
      zs_store_type_code = #{record.zsStoreTypeCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetail">
    update bundling_task_store_detail
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskCode != null">
        task_code = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="bundlStore != null">
        bundl_store = #{bundlStore,jdbcType=VARCHAR},
      </if>
      <if test="bundlAdviceAble != null">
        bundl_advice_able = #{bundlAdviceAble,jdbcType=TINYINT},
      </if>
      <if test="bundlConfirmAble != null">
        bundl_confirm_able = #{bundlConfirmAble,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="salesLevel != null">
        sales_level = #{salesLevel,jdbcType=VARCHAR},
      </if>
      <if test="tradingArea != null">
        trading_area = #{tradingArea,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="storeStatus != null">
        store_status = #{storeStatus,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        open_date = #{openDate,jdbcType=VARCHAR},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        store_attr = #{storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="format != null">
        format = #{format,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="specialType != null">
        special_type = #{specialType,jdbcType=VARCHAR},
      </if>
      <if test="dtp != null">
        dtp = #{dtp,jdbcType=VARCHAR},
      </if>
      <if test="platStoreTypeCode != null">
        plat_store_type_code = #{platStoreTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeCode != null">
        store_type_code = #{storeTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="zsStoreTypeCode != null">
        zs_store_type_code = #{zsStoreTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.BundlingTaskStoreDetail">
    update bundling_task_store_detail
    set task_id = #{taskId,jdbcType=BIGINT},
      task_code = #{taskCode,jdbcType=VARCHAR},
      bundl_store = #{bundlStore,jdbcType=VARCHAR},
      bundl_advice_able = #{bundlAdviceAble,jdbcType=TINYINT},
      bundl_confirm_able = #{bundlConfirmAble,jdbcType=TINYINT},
      business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      sales_level = #{salesLevel,jdbcType=VARCHAR},
      trading_area = #{tradingArea,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      store_status = #{storeStatus,jdbcType=VARCHAR},
      open_date = #{openDate,jdbcType=VARCHAR},
      close_date = #{closeDate,jdbcType=VARCHAR},
      store_attr = #{storeAttr,jdbcType=VARCHAR},
      format = #{format,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      special_type = #{specialType,jdbcType=VARCHAR},
      dtp = #{dtp,jdbcType=VARCHAR},
      plat_store_type_code = #{platStoreTypeCode,jdbcType=VARCHAR},
      store_type_code = #{storeTypeCode,jdbcType=VARCHAR},
      zs_store_type_code = #{zsStoreTypeCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>