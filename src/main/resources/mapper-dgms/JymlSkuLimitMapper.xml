<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.JymlSkuLimitMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlSkuLimit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="property_code" jdbcType="VARCHAR" property="propertyCode" />
    <result column="property_desc" jdbcType="VARCHAR" property="propertyDesc" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="store_group_type" jdbcType="VARCHAR" property="storeGroupType" />
    <result column="store_group_code" jdbcType="VARCHAR" property="storeGroupCode" />
    <result column="total_sku" jdbcType="INTEGER" property="totalSku" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, property_code, property_desc, platform_org_id, platform_name, company_org_id, 
    company_name, province, city, store_group_type, store_group_code, total_sku, `status`, 
    gmt_create, gmt_update, extend, version, create_by_id, create_by, update_by_id, update_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.JymlSkuLimitExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_sku_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_sku_limit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_sku_limit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.JymlSkuLimitExample">
    delete from jyml_sku_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlSkuLimit" useGeneratedKeys="true">
    insert into jyml_sku_limit (property_code, property_desc, platform_org_id, 
      platform_name, company_org_id, company_name, 
      province, city, store_group_type, 
      store_group_code, total_sku, `status`, 
      gmt_create, gmt_update, extend, 
      version, create_by_id, create_by, 
      update_by_id, update_by)
    values (#{propertyCode,jdbcType=VARCHAR}, #{propertyDesc,jdbcType=VARCHAR}, #{platformOrgId,jdbcType=BIGINT}, 
      #{platformName,jdbcType=VARCHAR}, #{companyOrgId,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{storeGroupType,jdbcType=VARCHAR}, 
      #{storeGroupCode,jdbcType=VARCHAR}, #{totalSku,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createById,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, 
      #{updateById,jdbcType=BIGINT}, #{updateBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlSkuLimit" useGeneratedKeys="true">
    insert into jyml_sku_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="propertyCode != null">
        property_code,
      </if>
      <if test="propertyDesc != null">
        property_desc,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="storeGroupType != null">
        store_group_type,
      </if>
      <if test="storeGroupCode != null">
        store_group_code,
      </if>
      <if test="totalSku != null">
        total_sku,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="propertyCode != null">
        #{propertyCode,jdbcType=VARCHAR},
      </if>
      <if test="propertyDesc != null">
        #{propertyDesc,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupType != null">
        #{storeGroupType,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupCode != null">
        #{storeGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="totalSku != null">
        #{totalSku,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.JymlSkuLimitExample" resultType="java.lang.Long">
    select count(*) from jyml_sku_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_sku_limit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.propertyCode != null">
        property_code = #{record.propertyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.propertyDesc != null">
        property_desc = #{record.propertyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGroupType != null">
        store_group_type = #{record.storeGroupType,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGroupCode != null">
        store_group_code = #{record.storeGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.totalSku != null">
        total_sku = #{record.totalSku,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_sku_limit
    set id = #{record.id,jdbcType=BIGINT},
      property_code = #{record.propertyCode,jdbcType=VARCHAR},
      property_desc = #{record.propertyDesc,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      store_group_type = #{record.storeGroupType,jdbcType=VARCHAR},
      store_group_code = #{record.storeGroupCode,jdbcType=VARCHAR},
      total_sku = #{record.totalSku,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.JymlSkuLimit">
    update jyml_sku_limit
    <set>
      <if test="propertyCode != null">
        property_code = #{propertyCode,jdbcType=VARCHAR},
      </if>
      <if test="propertyDesc != null">
        property_desc = #{propertyDesc,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupType != null">
        store_group_type = #{storeGroupType,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupCode != null">
        store_group_code = #{storeGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="totalSku != null">
        total_sku = #{totalSku,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.JymlSkuLimit">
    update jyml_sku_limit
    set property_code = #{propertyCode,jdbcType=VARCHAR},
      property_desc = #{propertyDesc,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      store_group_type = #{storeGroupType,jdbcType=VARCHAR},
      store_group_code = #{storeGroupCode,jdbcType=VARCHAR},
      total_sku = #{totalSku,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>