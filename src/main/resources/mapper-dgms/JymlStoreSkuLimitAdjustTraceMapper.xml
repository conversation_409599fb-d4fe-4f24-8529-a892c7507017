<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.JymlStoreSkuLimitAdjustTraceMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTrace">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="oa_code" jdbcType="VARCHAR" property="oaCode" />
    <result column="step" jdbcType="INTEGER" property="step" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="step_describe" jdbcType="VARCHAR" property="stepDescribe" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, adjust_code, oa_code, step, step_name, step_describe, created_by, created_name, 
    gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTraceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_limit_adjust_trace
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_store_sku_limit_adjust_trace
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_limit_adjust_trace
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTraceExample">
    delete from jyml_store_sku_limit_adjust_trace
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTrace" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_trace (adjust_code, oa_code, step, 
      step_name, step_describe, created_by, 
      created_name, gmt_create)
    values (#{adjustCode,jdbcType=VARCHAR}, #{oaCode,jdbcType=VARCHAR}, #{step,jdbcType=INTEGER}, 
      #{stepName,jdbcType=VARCHAR}, #{stepDescribe,jdbcType=VARCHAR}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTrace" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_trace
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="oaCode != null">
        oa_code,
      </if>
      <if test="step != null">
        step,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="stepDescribe != null">
        step_describe,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="oaCode != null">
        #{oaCode,jdbcType=VARCHAR},
      </if>
      <if test="step != null">
        #{step,jdbcType=INTEGER},
      </if>
      <if test="stepName != null">
        #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="stepDescribe != null">
        #{stepDescribe,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTraceExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_limit_adjust_trace
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_limit_adjust_trace
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.oaCode != null">
        oa_code = #{record.oaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.step != null">
        step = #{record.step,jdbcType=INTEGER},
      </if>
      <if test="record.stepName != null">
        step_name = #{record.stepName,jdbcType=VARCHAR},
      </if>
      <if test="record.stepDescribe != null">
        step_describe = #{record.stepDescribe,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_limit_adjust_trace
    set id = #{record.id,jdbcType=BIGINT},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      oa_code = #{record.oaCode,jdbcType=VARCHAR},
      step = #{record.step,jdbcType=INTEGER},
      step_name = #{record.stepName,jdbcType=VARCHAR},
      step_describe = #{record.stepDescribe,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTrace">
    update jyml_store_sku_limit_adjust_trace
    <set>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="oaCode != null">
        oa_code = #{oaCode,jdbcType=VARCHAR},
      </if>
      <if test="step != null">
        step = #{step,jdbcType=INTEGER},
      </if>
      <if test="stepName != null">
        step_name = #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="stepDescribe != null">
        step_describe = #{stepDescribe,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTrace">
    update jyml_store_sku_limit_adjust_trace
    set adjust_code = #{adjustCode,jdbcType=VARCHAR},
      oa_code = #{oaCode,jdbcType=VARCHAR},
      step = #{step,jdbcType=INTEGER},
      step_name = #{stepName,jdbcType=VARCHAR},
      step_describe = #{stepDescribe,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>