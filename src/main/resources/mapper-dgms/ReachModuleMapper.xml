<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.ReachModuleMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entity.ReachModule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reach_module" jdbcType="BIGINT" property="reachModule" />
    <result column="reach_group_name" jdbcType="VARCHAR" property="reachGroupName" />
    <result column="select_method" jdbcType="INTEGER" property="selectMethod" />
    <result column="reach_persons" jdbcType="VARCHAR" property="reachPersons" />
    <result column="reach_person_codes" jdbcType="VARCHAR" property="reachPersonCodes" />
    <result column="reach_person_names" jdbcType="VARCHAR" property="reachPersonNames" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="BIGINT" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, reach_module, reach_group_name, select_method, reach_persons, reach_person_codes, 
    reach_person_names, creator_id, creator_name, create_time, update_id, update_name, 
    update_time, `status`, extend
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entity.ReachModuleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from reach_module
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from reach_module
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from reach_module
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entity.ReachModuleExample">
    delete from reach_module
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entity.ReachModule">
    insert into reach_module (id, reach_module, reach_group_name, 
      select_method, reach_persons, reach_person_codes, 
      reach_person_names, creator_id, creator_name, 
      create_time, update_id, update_name, 
      update_time, `status`, extend
      )
    values (#{id,jdbcType=BIGINT}, #{reachModule,jdbcType=BIGINT}, #{reachGroupName,jdbcType=VARCHAR}, 
      #{selectMethod,jdbcType=INTEGER}, #{reachPersons,jdbcType=VARCHAR}, #{reachPersonCodes,jdbcType=VARCHAR}, 
      #{reachPersonNames,jdbcType=VARCHAR}, #{creatorId,jdbcType=BIGINT}, #{creatorName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=BIGINT}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{extend,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entity.ReachModule">
    insert into reach_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reachModule != null">
        reach_module,
      </if>
      <if test="reachGroupName != null">
        reach_group_name,
      </if>
      <if test="selectMethod != null">
        select_method,
      </if>
      <if test="reachPersons != null">
        reach_persons,
      </if>
      <if test="reachPersonCodes != null">
        reach_person_codes,
      </if>
      <if test="reachPersonNames != null">
        reach_person_names,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reachModule != null">
        #{reachModule,jdbcType=BIGINT},
      </if>
      <if test="reachGroupName != null">
        #{reachGroupName,jdbcType=VARCHAR},
      </if>
      <if test="selectMethod != null">
        #{selectMethod,jdbcType=INTEGER},
      </if>
      <if test="reachPersons != null">
        #{reachPersons,jdbcType=VARCHAR},
      </if>
      <if test="reachPersonCodes != null">
        #{reachPersonCodes,jdbcType=VARCHAR},
      </if>
      <if test="reachPersonNames != null">
        #{reachPersonNames,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=BIGINT},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entity.ReachModuleExample" resultType="java.lang.Long">
    select count(*) from reach_module
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update reach_module
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reachModule != null">
        reach_module = #{record.reachModule,jdbcType=BIGINT},
      </if>
      <if test="record.reachGroupName != null">
        reach_group_name = #{record.reachGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.selectMethod != null">
        select_method = #{record.selectMethod,jdbcType=INTEGER},
      </if>
      <if test="record.reachPersons != null">
        reach_persons = #{record.reachPersons,jdbcType=VARCHAR},
      </if>
      <if test="record.reachPersonCodes != null">
        reach_person_codes = #{record.reachPersonCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.reachPersonNames != null">
        reach_person_names = #{record.reachPersonNames,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateId != null">
        update_id = #{record.updateId,jdbcType=BIGINT},
      </if>
      <if test="record.updateName != null">
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update reach_module
    set id = #{record.id,jdbcType=BIGINT},
      reach_module = #{record.reachModule,jdbcType=BIGINT},
      reach_group_name = #{record.reachGroupName,jdbcType=VARCHAR},
      select_method = #{record.selectMethod,jdbcType=INTEGER},
      reach_persons = #{record.reachPersons,jdbcType=VARCHAR},
      reach_person_codes = #{record.reachPersonCodes,jdbcType=VARCHAR},
      reach_person_names = #{record.reachPersonNames,jdbcType=VARCHAR},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_id = #{record.updateId,jdbcType=BIGINT},
      update_name = #{record.updateName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=INTEGER},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entity.ReachModule">
    update reach_module
    <set>
      <if test="reachModule != null">
        reach_module = #{reachModule,jdbcType=BIGINT},
      </if>
      <if test="reachGroupName != null">
        reach_group_name = #{reachGroupName,jdbcType=VARCHAR},
      </if>
      <if test="selectMethod != null">
        select_method = #{selectMethod,jdbcType=INTEGER},
      </if>
      <if test="reachPersons != null">
        reach_persons = #{reachPersons,jdbcType=VARCHAR},
      </if>
      <if test="reachPersonCodes != null">
        reach_person_codes = #{reachPersonCodes,jdbcType=VARCHAR},
      </if>
      <if test="reachPersonNames != null">
        reach_person_names = #{reachPersonNames,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=BIGINT},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entity.ReachModule">
    update reach_module
    set reach_module = #{reachModule,jdbcType=BIGINT},
      reach_group_name = #{reachGroupName,jdbcType=VARCHAR},
      select_method = #{selectMethod,jdbcType=INTEGER},
      reach_persons = #{reachPersons,jdbcType=VARCHAR},
      reach_person_codes = #{reachPersonCodes,jdbcType=VARCHAR},
      reach_person_names = #{reachPersonNames,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=BIGINT},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=BIGINT},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>