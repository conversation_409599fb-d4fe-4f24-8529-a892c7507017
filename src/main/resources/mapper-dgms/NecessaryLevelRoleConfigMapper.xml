<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.NecessaryLevelRoleConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.NecessaryLevelRoleConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="necessary_tag" jdbcType="VARCHAR" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="roles" jdbcType="VARCHAR" property="roles" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, necessary_tag, necessary_tag_name, roles, gmt_create, gmt_update, 
    extend, version, `status`, create_by_id, create_by, update_by_id, update_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from necessary_level_role_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from necessary_level_role_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from necessary_level_role_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfigExample">
    delete from necessary_level_role_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfig" useGeneratedKeys="true">
    insert into necessary_level_role_config (org_id, org_name, necessary_tag, 
      necessary_tag_name, roles, gmt_create, 
      gmt_update, extend, version, 
      `status`, create_by_id, create_by, 
      update_by_id, update_by)
    values (#{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{necessaryTag,jdbcType=VARCHAR}, 
      #{necessaryTagName,jdbcType=VARCHAR}, #{roles,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{createById,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, 
      #{updateById,jdbcType=BIGINT}, #{updateBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfig" useGeneratedKeys="true">
    insert into necessary_level_role_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="necessaryTag != null">
        necessary_tag,
      </if>
      <if test="necessaryTagName != null">
        necessary_tag_name,
      </if>
      <if test="roles != null">
        roles,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="necessaryTag != null">
        #{necessaryTag,jdbcType=VARCHAR},
      </if>
      <if test="necessaryTagName != null">
        #{necessaryTagName,jdbcType=VARCHAR},
      </if>
      <if test="roles != null">
        #{roles,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfigExample" resultType="java.lang.Long">
    select count(*) from necessary_level_role_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update necessary_level_role_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.necessaryTag != null">
        necessary_tag = #{record.necessaryTag,jdbcType=VARCHAR},
      </if>
      <if test="record.necessaryTagName != null">
        necessary_tag_name = #{record.necessaryTagName,jdbcType=VARCHAR},
      </if>
      <if test="record.roles != null">
        roles = #{record.roles,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update necessary_level_role_config
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      necessary_tag = #{record.necessaryTag,jdbcType=VARCHAR},
      necessary_tag_name = #{record.necessaryTagName,jdbcType=VARCHAR},
      roles = #{record.roles,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=INTEGER},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfig">
    update necessary_level_role_config
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="necessaryTag != null">
        necessary_tag = #{necessaryTag,jdbcType=VARCHAR},
      </if>
      <if test="necessaryTagName != null">
        necessary_tag_name = #{necessaryTagName,jdbcType=VARCHAR},
      </if>
      <if test="roles != null">
        roles = #{roles,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.NecessaryLevelRoleConfig">
    update necessary_level_role_config
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      necessary_tag = #{necessaryTag,jdbcType=VARCHAR},
      necessary_tag_name = #{necessaryTagName,jdbcType=VARCHAR},
      roles = #{roles,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>