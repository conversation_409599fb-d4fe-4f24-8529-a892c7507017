<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.DevelopModuleRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entity.DevelopModuleRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="module_code" jdbcType="VARCHAR" property="moduleCode" />
    <result column="develop_version" jdbcType="VARCHAR" property="developVersion" />
    <result column="develop_type" jdbcType="TINYINT" property="developType" />
    <result column="develop_title" jdbcType="VARCHAR" property="developTitle" />
    <result column="develop_content" jdbcType="VARCHAR" property="developContent" />
    <result column="develop_status" jdbcType="TINYINT" property="developStatus" />
    <result column="develop_time" jdbcType="TIMESTAMP" property="developTime" />
    <result column="image_urls" jdbcType="VARCHAR" property="imageUrls" />
    <result column="reach_channels" jdbcType="VARCHAR" property="reachChannels" />
    <result column="reach_groupIds" jdbcType="VARCHAR" property="reachGroupids" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, module_code, develop_version, develop_type, develop_title, develop_content, develop_status, 
    develop_time, image_urls, reach_channels, reach_groupIds, extend, created_by, created_name, 
    updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entity.DevelopModuleRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from develop_module_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from develop_module_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from develop_module_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entity.DevelopModuleRecordExample">
    delete from develop_module_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entity.DevelopModuleRecord">
    insert into develop_module_record (id, module_code, develop_version, 
      develop_type, develop_title, develop_content, 
      develop_status, develop_time, image_urls, 
      reach_channels, reach_groupIds, extend, 
      created_by, created_name, updated_by, 
      updated_name, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{moduleCode,jdbcType=VARCHAR}, #{developVersion,jdbcType=VARCHAR}, 
      #{developType,jdbcType=TINYINT}, #{developTitle,jdbcType=VARCHAR}, #{developContent,jdbcType=VARCHAR}, 
      #{developStatus,jdbcType=TINYINT}, #{developTime,jdbcType=TIMESTAMP}, #{imageUrls,jdbcType=VARCHAR}, 
      #{reachChannels,jdbcType=VARCHAR}, #{reachGroupids,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entity.DevelopModuleRecord">
    insert into develop_module_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="moduleCode != null">
        module_code,
      </if>
      <if test="developVersion != null">
        develop_version,
      </if>
      <if test="developType != null">
        develop_type,
      </if>
      <if test="developTitle != null">
        develop_title,
      </if>
      <if test="developContent != null">
        develop_content,
      </if>
      <if test="developStatus != null">
        develop_status,
      </if>
      <if test="developTime != null">
        develop_time,
      </if>
      <if test="imageUrls != null">
        image_urls,
      </if>
      <if test="reachChannels != null">
        reach_channels,
      </if>
      <if test="reachGroupids != null">
        reach_groupIds,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="moduleCode != null">
        #{moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="developVersion != null">
        #{developVersion,jdbcType=VARCHAR},
      </if>
      <if test="developType != null">
        #{developType,jdbcType=TINYINT},
      </if>
      <if test="developTitle != null">
        #{developTitle,jdbcType=VARCHAR},
      </if>
      <if test="developContent != null">
        #{developContent,jdbcType=VARCHAR},
      </if>
      <if test="developStatus != null">
        #{developStatus,jdbcType=TINYINT},
      </if>
      <if test="developTime != null">
        #{developTime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageUrls != null">
        #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="reachChannels != null">
        #{reachChannels,jdbcType=VARCHAR},
      </if>
      <if test="reachGroupids != null">
        #{reachGroupids,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entity.DevelopModuleRecordExample" resultType="java.lang.Long">
    select count(*) from develop_module_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update develop_module_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.moduleCode != null">
        module_code = #{record.moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.developVersion != null">
        develop_version = #{record.developVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.developType != null">
        develop_type = #{record.developType,jdbcType=TINYINT},
      </if>
      <if test="record.developTitle != null">
        develop_title = #{record.developTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.developContent != null">
        develop_content = #{record.developContent,jdbcType=VARCHAR},
      </if>
      <if test="record.developStatus != null">
        develop_status = #{record.developStatus,jdbcType=TINYINT},
      </if>
      <if test="record.developTime != null">
        develop_time = #{record.developTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.imageUrls != null">
        image_urls = #{record.imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="record.reachChannels != null">
        reach_channels = #{record.reachChannels,jdbcType=VARCHAR},
      </if>
      <if test="record.reachGroupids != null">
        reach_groupIds = #{record.reachGroupids,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update develop_module_record
    set id = #{record.id,jdbcType=INTEGER},
      module_code = #{record.moduleCode,jdbcType=VARCHAR},
      develop_version = #{record.developVersion,jdbcType=VARCHAR},
      develop_type = #{record.developType,jdbcType=TINYINT},
      develop_title = #{record.developTitle,jdbcType=VARCHAR},
      develop_content = #{record.developContent,jdbcType=VARCHAR},
      develop_status = #{record.developStatus,jdbcType=TINYINT},
      develop_time = #{record.developTime,jdbcType=TIMESTAMP},
      image_urls = #{record.imageUrls,jdbcType=VARCHAR},
      reach_channels = #{record.reachChannels,jdbcType=VARCHAR},
      reach_groupIds = #{record.reachGroupids,jdbcType=VARCHAR},
      extend = #{record.extend,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entity.DevelopModuleRecord">
    update develop_module_record
    <set>
      <if test="moduleCode != null">
        module_code = #{moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="developVersion != null">
        develop_version = #{developVersion,jdbcType=VARCHAR},
      </if>
      <if test="developType != null">
        develop_type = #{developType,jdbcType=TINYINT},
      </if>
      <if test="developTitle != null">
        develop_title = #{developTitle,jdbcType=VARCHAR},
      </if>
      <if test="developContent != null">
        develop_content = #{developContent,jdbcType=VARCHAR},
      </if>
      <if test="developStatus != null">
        develop_status = #{developStatus,jdbcType=TINYINT},
      </if>
      <if test="developTime != null">
        develop_time = #{developTime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageUrls != null">
        image_urls = #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="reachChannels != null">
        reach_channels = #{reachChannels,jdbcType=VARCHAR},
      </if>
      <if test="reachGroupids != null">
        reach_groupIds = #{reachGroupids,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entity.DevelopModuleRecord">
    update develop_module_record
    set module_code = #{moduleCode,jdbcType=VARCHAR},
      develop_version = #{developVersion,jdbcType=VARCHAR},
      develop_type = #{developType,jdbcType=TINYINT},
      develop_title = #{developTitle,jdbcType=VARCHAR},
      develop_content = #{developContent,jdbcType=VARCHAR},
      develop_status = #{developStatus,jdbcType=TINYINT},
      develop_time = #{developTime,jdbcType=TIMESTAMP},
      image_urls = #{imageUrls,jdbcType=VARCHAR},
      reach_channels = #{reachChannels,jdbcType=VARCHAR},
      reach_groupIds = #{reachGroupids,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>