<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.MdmTaskMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.MdmTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_source" jdbcType="TINYINT" property="taskSource" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="detail_count" jdbcType="INTEGER" property="detailCount" />
    <result column="bundling_task_id" jdbcType="BIGINT" property="bundlingTaskId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_source, task_status, detail_count, bundling_task_id, `status`, remarks, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.MdmTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mdm_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mdm_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mdm_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.MdmTaskExample">
    delete from mdm_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.MdmTask" useGeneratedKeys="true">
    insert into mdm_task (task_source, task_status, detail_count,
    bundling_task_id, `status`,remarks, gmt_create,
    gmt_update, extend, version,
    created_by, created_name, updated_by,
    updated_name)
    values (#{taskSource,jdbcType=TINYINT}, #{taskStatus,jdbcType=TINYINT}, #{detailCount,jdbcType=INTEGER},
    #{bundlingTaskId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT},#{remarks,jdbcType=VARCHAR},#{gmtCreate,jdbcType=TIMESTAMP},
    #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER},
    #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT},
    #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.MdmTask" useGeneratedKeys="true">
    insert into mdm_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskSource != null">
        task_source,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="detailCount != null">
        detail_count,
      </if>
      <if test="bundlingTaskId != null">
        bundling_task_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskSource != null">
        #{taskSource,jdbcType=TINYINT},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="detailCount != null">
        #{detailCount,jdbcType=INTEGER},
      </if>
      <if test="bundlingTaskId != null">
        #{bundlingTaskId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.MdmTaskExample" resultType="java.lang.Long">
    select count(*) from mdm_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mdm_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskSource != null">
        task_source = #{record.taskSource,jdbcType=TINYINT},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.detailCount != null">
        detail_count = #{record.detailCount,jdbcType=INTEGER},
      </if>
      <if test="record.bundlingTaskId != null">
        bundling_task_id = #{record.bundlingTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mdm_task
    set id = #{record.id,jdbcType=BIGINT},
    task_source = #{record.taskSource,jdbcType=TINYINT},
    task_status = #{record.taskStatus,jdbcType=TINYINT},
    detail_count = #{record.detailCount,jdbcType=INTEGER},
    bundling_task_id = #{record.bundlingTaskId,jdbcType=BIGINT},
    `status` = #{record.status,jdbcType=TINYINT},
    remarks = #{record.remarks,jdbcType=VARCHAR},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{record.extend,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=INTEGER},
    created_by = #{record.createdBy,jdbcType=BIGINT},
    created_name = #{record.createdName,jdbcType=VARCHAR},
    updated_by = #{record.updatedBy,jdbcType=BIGINT},
    updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.MdmTask">
    update mdm_task
    <set>
      <if test="taskSource != null">
        task_source = #{taskSource,jdbcType=TINYINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="detailCount != null">
        detail_count = #{detailCount,jdbcType=INTEGER},
      </if>
      <if test="bundlingTaskId != null">
        bundling_task_id = #{bundlingTaskId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.MdmTask">
    update mdm_task
    set task_source = #{taskSource,jdbcType=TINYINT},
    task_status = #{taskStatus,jdbcType=TINYINT},
    detail_count = #{detailCount,jdbcType=INTEGER},
    bundling_task_id = #{bundlingTaskId,jdbcType=BIGINT},
    `status` = #{status,jdbcType=TINYINT},
    remarks = #{remarks,jdbcType=VARCHAR},
    gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{extend,jdbcType=VARCHAR},
    version = #{version,jdbcType=INTEGER},
    created_by = #{createdBy,jdbcType=BIGINT},
    created_name = #{createdName,jdbcType=VARCHAR},
    updated_by = #{updatedBy,jdbcType=BIGINT},
    updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>