<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.StoreGoodsContentsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.StoreGoodsContents">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="necessary_tag" jdbcType="INTEGER" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="manage_status" jdbcType="INTEGER" property="manageStatus" />
    <result column="suggest_manage_status" jdbcType="INTEGER" property="suggestManageStatus" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_distr" jdbcType="VARCHAR" property="forbidDistr" />
    <result column="forbid_sale" jdbcType="VARCHAR" property="forbidSale" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_return_allot" jdbcType="VARCHAR" property="forbidReturnAllot" />
    <result column="sensitive" jdbcType="VARCHAR" property="sensitive" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="apply_limit_upper" jdbcType="DECIMAL" property="applyLimitUpper" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_org_id, store_id, store_code, goods_no, sub_category_id, necessary_tag, 
    necessary_tag_name, manage_status, suggest_manage_status, forbid_apply, forbid_distr, 
    forbid_sale, forbid_return_warehouse, forbid_return_allot, `sensitive`, push_level, 
    min_display_quantity, apply_limit_upper, effect_status, `status`, gmt_create, gmt_update, 
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.StoreGoodsContentsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from store_goods_contents
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from store_goods_contents
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_goods_contents
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.StoreGoodsContentsExample">
    delete from store_goods_contents
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.StoreGoodsContents" useGeneratedKeys="true">
    insert into store_goods_contents (store_org_id, store_id, store_code, 
      goods_no, sub_category_id, necessary_tag, 
      necessary_tag_name, manage_status, suggest_manage_status, 
      forbid_apply, forbid_distr, forbid_sale, 
      forbid_return_warehouse, forbid_return_allot, 
      `sensitive`, push_level, min_display_quantity, 
      apply_limit_upper, effect_status, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{storeOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{subCategoryId,jdbcType=BIGINT}, #{necessaryTag,jdbcType=INTEGER}, 
      #{necessaryTagName,jdbcType=VARCHAR}, #{manageStatus,jdbcType=INTEGER}, #{suggestManageStatus,jdbcType=INTEGER}, 
      #{forbidApply,jdbcType=VARCHAR}, #{forbidDistr,jdbcType=VARCHAR}, #{forbidSale,jdbcType=VARCHAR}, 
      #{forbidReturnWarehouse,jdbcType=VARCHAR}, #{forbidReturnAllot,jdbcType=VARCHAR}, 
      #{sensitive,jdbcType=VARCHAR}, #{pushLevel,jdbcType=VARCHAR}, #{minDisplayQuantity,jdbcType=DECIMAL}, 
      #{applyLimitUpper,jdbcType=DECIMAL}, #{effectStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.StoreGoodsContents" useGeneratedKeys="true">
    insert into store_goods_contents
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="subCategoryId != null">
        sub_category_id,
      </if>
      <if test="necessaryTag != null">
        necessary_tag,
      </if>
      <if test="necessaryTagName != null">
        necessary_tag_name,
      </if>
      <if test="manageStatus != null">
        manage_status,
      </if>
      <if test="suggestManageStatus != null">
        suggest_manage_status,
      </if>
      <if test="forbidApply != null">
        forbid_apply,
      </if>
      <if test="forbidDistr != null">
        forbid_distr,
      </if>
      <if test="forbidSale != null">
        forbid_sale,
      </if>
      <if test="forbidReturnWarehouse != null">
        forbid_return_warehouse,
      </if>
      <if test="forbidReturnAllot != null">
        forbid_return_allot,
      </if>
      <if test="sensitive != null">
        `sensitive`,
      </if>
      <if test="pushLevel != null">
        push_level,
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity,
      </if>
      <if test="applyLimitUpper != null">
        apply_limit_upper,
      </if>
      <if test="effectStatus != null">
        effect_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        #{subCategoryId,jdbcType=BIGINT},
      </if>
      <if test="necessaryTag != null">
        #{necessaryTag,jdbcType=INTEGER},
      </if>
      <if test="necessaryTagName != null">
        #{necessaryTagName,jdbcType=VARCHAR},
      </if>
      <if test="manageStatus != null">
        #{manageStatus,jdbcType=INTEGER},
      </if>
      <if test="suggestManageStatus != null">
        #{suggestManageStatus,jdbcType=INTEGER},
      </if>
      <if test="forbidApply != null">
        #{forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="forbidDistr != null">
        #{forbidDistr,jdbcType=VARCHAR},
      </if>
      <if test="forbidSale != null">
        #{forbidSale,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnWarehouse != null">
        #{forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnAllot != null">
        #{forbidReturnAllot,jdbcType=VARCHAR},
      </if>
      <if test="sensitive != null">
        #{sensitive,jdbcType=VARCHAR},
      </if>
      <if test="pushLevel != null">
        #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="minDisplayQuantity != null">
        #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="applyLimitUpper != null">
        #{applyLimitUpper,jdbcType=DECIMAL},
      </if>
      <if test="effectStatus != null">
        #{effectStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.StoreGoodsContentsExample" resultType="java.lang.Long">
    select count(*) from store_goods_contents
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update store_goods_contents
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryId != null">
        sub_category_id = #{record.subCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.necessaryTag != null">
        necessary_tag = #{record.necessaryTag,jdbcType=INTEGER},
      </if>
      <if test="record.necessaryTagName != null">
        necessary_tag_name = #{record.necessaryTagName,jdbcType=VARCHAR},
      </if>
      <if test="record.manageStatus != null">
        manage_status = #{record.manageStatus,jdbcType=INTEGER},
      </if>
      <if test="record.suggestManageStatus != null">
        suggest_manage_status = #{record.suggestManageStatus,jdbcType=INTEGER},
      </if>
      <if test="record.forbidApply != null">
        forbid_apply = #{record.forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidDistr != null">
        forbid_distr = #{record.forbidDistr,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidSale != null">
        forbid_sale = #{record.forbidSale,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidReturnWarehouse != null">
        forbid_return_warehouse = #{record.forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidReturnAllot != null">
        forbid_return_allot = #{record.forbidReturnAllot,jdbcType=VARCHAR},
      </if>
      <if test="record.sensitive != null">
        `sensitive` = #{record.sensitive,jdbcType=VARCHAR},
      </if>
      <if test="record.pushLevel != null">
        push_level = #{record.pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.minDisplayQuantity != null">
        min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.applyLimitUpper != null">
        apply_limit_upper = #{record.applyLimitUpper,jdbcType=DECIMAL},
      </if>
      <if test="record.effectStatus != null">
        effect_status = #{record.effectStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update store_goods_contents
    set id = #{record.id,jdbcType=BIGINT},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      sub_category_id = #{record.subCategoryId,jdbcType=BIGINT},
      necessary_tag = #{record.necessaryTag,jdbcType=INTEGER},
      necessary_tag_name = #{record.necessaryTagName,jdbcType=VARCHAR},
      manage_status = #{record.manageStatus,jdbcType=INTEGER},
      suggest_manage_status = #{record.suggestManageStatus,jdbcType=INTEGER},
      forbid_apply = #{record.forbidApply,jdbcType=VARCHAR},
      forbid_distr = #{record.forbidDistr,jdbcType=VARCHAR},
      forbid_sale = #{record.forbidSale,jdbcType=VARCHAR},
      forbid_return_warehouse = #{record.forbidReturnWarehouse,jdbcType=VARCHAR},
      forbid_return_allot = #{record.forbidReturnAllot,jdbcType=VARCHAR},
      `sensitive` = #{record.sensitive,jdbcType=VARCHAR},
      push_level = #{record.pushLevel,jdbcType=VARCHAR},
      min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      apply_limit_upper = #{record.applyLimitUpper,jdbcType=DECIMAL},
      effect_status = #{record.effectStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.StoreGoodsContents">
    update store_goods_contents
    <set>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        sub_category_id = #{subCategoryId,jdbcType=BIGINT},
      </if>
      <if test="necessaryTag != null">
        necessary_tag = #{necessaryTag,jdbcType=INTEGER},
      </if>
      <if test="necessaryTagName != null">
        necessary_tag_name = #{necessaryTagName,jdbcType=VARCHAR},
      </if>
      <if test="manageStatus != null">
        manage_status = #{manageStatus,jdbcType=INTEGER},
      </if>
      <if test="suggestManageStatus != null">
        suggest_manage_status = #{suggestManageStatus,jdbcType=INTEGER},
      </if>
      <if test="forbidApply != null">
        forbid_apply = #{forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="forbidDistr != null">
        forbid_distr = #{forbidDistr,jdbcType=VARCHAR},
      </if>
      <if test="forbidSale != null">
        forbid_sale = #{forbidSale,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnWarehouse != null">
        forbid_return_warehouse = #{forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnAllot != null">
        forbid_return_allot = #{forbidReturnAllot,jdbcType=VARCHAR},
      </if>
      <if test="sensitive != null">
        `sensitive` = #{sensitive,jdbcType=VARCHAR},
      </if>
      <if test="pushLevel != null">
        push_level = #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="applyLimitUpper != null">
        apply_limit_upper = #{applyLimitUpper,jdbcType=DECIMAL},
      </if>
      <if test="effectStatus != null">
        effect_status = #{effectStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.StoreGoodsContents">
    update store_goods_contents
    set store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      sub_category_id = #{subCategoryId,jdbcType=BIGINT},
      necessary_tag = #{necessaryTag,jdbcType=INTEGER},
      necessary_tag_name = #{necessaryTagName,jdbcType=VARCHAR},
      manage_status = #{manageStatus,jdbcType=INTEGER},
      suggest_manage_status = #{suggestManageStatus,jdbcType=INTEGER},
      forbid_apply = #{forbidApply,jdbcType=VARCHAR},
      forbid_distr = #{forbidDistr,jdbcType=VARCHAR},
      forbid_sale = #{forbidSale,jdbcType=VARCHAR},
      forbid_return_warehouse = #{forbidReturnWarehouse,jdbcType=VARCHAR},
      forbid_return_allot = #{forbidReturnAllot,jdbcType=VARCHAR},
      `sensitive` = #{sensitive,jdbcType=VARCHAR},
      push_level = #{pushLevel,jdbcType=VARCHAR},
      min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      apply_limit_upper = #{applyLimitUpper,jdbcType=DECIMAL},
      effect_status = #{effectStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>