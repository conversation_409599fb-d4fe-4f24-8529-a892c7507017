<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.CommonEnumsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.CommonEnums">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="property_code" jdbcType="VARCHAR" property="propertyCode" />
    <result column="property_desc" jdbcType="VARCHAR" property="propertyDesc" />
    <result column="enum_name" jdbcType="VARCHAR" property="enumName" />
    <result column="enum_value" jdbcType="VARCHAR" property="enumValue" />
    <result column="tag_id" jdbcType="BIGINT" property="tagId" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, property_code, property_desc, enum_name, enum_value, tag_id, tag_name, gmt_create, 
    gmt_update, extend, version, is_delete, create_by_id, create_by, update_by_id, update_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.CommonEnumsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from common_enums
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from common_enums
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from common_enums
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.CommonEnumsExample">
    delete from common_enums
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.CommonEnums" useGeneratedKeys="true">
    insert into common_enums (property_code, property_desc, enum_name, 
      enum_value, tag_id, tag_name, 
      gmt_create, gmt_update, extend, 
      version, is_delete, create_by_id, 
      create_by, update_by_id, update_by
      )
    values (#{propertyCode,jdbcType=VARCHAR}, #{propertyDesc,jdbcType=VARCHAR}, #{enumName,jdbcType=VARCHAR}, 
      #{enumValue,jdbcType=VARCHAR}, #{tagId,jdbcType=BIGINT}, #{tagName,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{createById,jdbcType=BIGINT}, 
      #{createBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{updateBy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.CommonEnums" useGeneratedKeys="true">
    insert into common_enums
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="propertyCode != null">
        property_code,
      </if>
      <if test="propertyDesc != null">
        property_desc,
      </if>
      <if test="enumName != null">
        enum_name,
      </if>
      <if test="enumValue != null">
        enum_value,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="tagName != null">
        tag_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="propertyCode != null">
        #{propertyCode,jdbcType=VARCHAR},
      </if>
      <if test="propertyDesc != null">
        #{propertyDesc,jdbcType=VARCHAR},
      </if>
      <if test="enumName != null">
        #{enumName,jdbcType=VARCHAR},
      </if>
      <if test="enumValue != null">
        #{enumValue,jdbcType=VARCHAR},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="tagName != null">
        #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.CommonEnumsExample" resultType="java.lang.Long">
    select count(*) from common_enums
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update common_enums
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.propertyCode != null">
        property_code = #{record.propertyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.propertyDesc != null">
        property_desc = #{record.propertyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.enumName != null">
        enum_name = #{record.enumName,jdbcType=VARCHAR},
      </if>
      <if test="record.enumValue != null">
        enum_value = #{record.enumValue,jdbcType=VARCHAR},
      </if>
      <if test="record.tagId != null">
        tag_id = #{record.tagId,jdbcType=BIGINT},
      </if>
      <if test="record.tagName != null">
        tag_name = #{record.tagName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update common_enums
    set id = #{record.id,jdbcType=BIGINT},
      property_code = #{record.propertyCode,jdbcType=VARCHAR},
      property_desc = #{record.propertyDesc,jdbcType=VARCHAR},
      enum_name = #{record.enumName,jdbcType=VARCHAR},
      enum_value = #{record.enumValue,jdbcType=VARCHAR},
      tag_id = #{record.tagId,jdbcType=BIGINT},
      tag_name = #{record.tagName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      is_delete = #{record.isDelete,jdbcType=INTEGER},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.CommonEnums">
    update common_enums
    <set>
      <if test="propertyCode != null">
        property_code = #{propertyCode,jdbcType=VARCHAR},
      </if>
      <if test="propertyDesc != null">
        property_desc = #{propertyDesc,jdbcType=VARCHAR},
      </if>
      <if test="enumName != null">
        enum_name = #{enumName,jdbcType=VARCHAR},
      </if>
      <if test="enumValue != null">
        enum_value = #{enumValue,jdbcType=VARCHAR},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=BIGINT},
      </if>
      <if test="tagName != null">
        tag_name = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.CommonEnums">
    update common_enums
    set property_code = #{propertyCode,jdbcType=VARCHAR},
      property_desc = #{propertyDesc,jdbcType=VARCHAR},
      enum_name = #{enumName,jdbcType=VARCHAR},
      enum_value = #{enumValue,jdbcType=VARCHAR},
      tag_id = #{tagId,jdbcType=BIGINT},
      tag_name = #{tagName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=INTEGER},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>