<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.JymlStoreSkuLimitAdjustDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_id" jdbcType="BIGINT" property="adjustId" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="old_store_type" jdbcType="VARCHAR" property="oldStoreType" />
    <result column="old_store_type_name" jdbcType="VARCHAR" property="oldStoreTypeName" />
    <result column="old_sku_max_limit" jdbcType="INTEGER" property="oldSkuMaxLimit" />
    <result column="old_sku_lower_limit" jdbcType="INTEGER" property="oldSkuLowerLimit" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="sku_max_limit" jdbcType="INTEGER" property="skuMaxLimit" />
    <result column="sku_lower_limit" jdbcType="INTEGER" property="skuLowerLimit" />
    <result column="old_configure_id" jdbcType="BIGINT" property="oldConfigureId" />
    <result column="configure_id" jdbcType="BIGINT" property="configureId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, adjust_id, adjust_code, category, category_name, middle_category, middle_category_name, 
    small_category, small_category_name, sub_category, sub_category_name, old_store_type, 
    old_store_type_name, old_sku_max_limit, old_sku_lower_limit, store_type, store_type_name, 
    sku_max_limit, sku_lower_limit, old_configure_id, configure_id, `status`, extend, 
    version, created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_limit_adjust_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_store_sku_limit_adjust_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_limit_adjust_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetailExample">
    delete from jyml_store_sku_limit_adjust_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_detail (adjust_id, adjust_code, category, 
      category_name, middle_category, middle_category_name, 
      small_category, small_category_name, sub_category, 
      sub_category_name, old_store_type, old_store_type_name, 
      old_sku_max_limit, old_sku_lower_limit, store_type, 
      store_type_name, sku_max_limit, sku_lower_limit, 
      old_configure_id, configure_id, `status`, 
      extend, version, created_by, 
      created_name, updated_by, updated_name, 
      gmt_create, gmt_update)
    values (#{adjustId,jdbcType=BIGINT}, #{adjustCode,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, 
      #{categoryName,jdbcType=VARCHAR}, #{middleCategory,jdbcType=VARCHAR}, #{middleCategoryName,jdbcType=VARCHAR}, 
      #{smallCategory,jdbcType=VARCHAR}, #{smallCategoryName,jdbcType=VARCHAR}, #{subCategory,jdbcType=VARCHAR}, 
      #{subCategoryName,jdbcType=VARCHAR}, #{oldStoreType,jdbcType=VARCHAR}, #{oldStoreTypeName,jdbcType=VARCHAR}, 
      #{oldSkuMaxLimit,jdbcType=INTEGER}, #{oldSkuLowerLimit,jdbcType=INTEGER}, #{storeType,jdbcType=VARCHAR}, 
      #{storeTypeName,jdbcType=VARCHAR}, #{skuMaxLimit,jdbcType=INTEGER}, #{skuLowerLimit,jdbcType=INTEGER}, 
      #{oldConfigureId,jdbcType=BIGINT}, #{configureId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustId != null">
        adjust_id,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="middleCategory != null">
        middle_category,
      </if>
      <if test="middleCategoryName != null">
        middle_category_name,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="smallCategoryName != null">
        small_category_name,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="oldStoreType != null">
        old_store_type,
      </if>
      <if test="oldStoreTypeName != null">
        old_store_type_name,
      </if>
      <if test="oldSkuMaxLimit != null">
        old_sku_max_limit,
      </if>
      <if test="oldSkuLowerLimit != null">
        old_sku_lower_limit,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
      <if test="storeTypeName != null">
        store_type_name,
      </if>
      <if test="skuMaxLimit != null">
        sku_max_limit,
      </if>
      <if test="skuLowerLimit != null">
        sku_lower_limit,
      </if>
      <if test="oldConfigureId != null">
        old_configure_id,
      </if>
      <if test="configureId != null">
        configure_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustId != null">
        #{adjustId,jdbcType=BIGINT},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreType != null">
        #{oldStoreType,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreTypeName != null">
        #{oldStoreTypeName,jdbcType=VARCHAR},
      </if>
      <if test="oldSkuMaxLimit != null">
        #{oldSkuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="oldSkuLowerLimit != null">
        #{oldSkuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeName != null">
        #{storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="skuMaxLimit != null">
        #{skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="skuLowerLimit != null">
        #{skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="oldConfigureId != null">
        #{oldConfigureId,jdbcType=BIGINT},
      </if>
      <if test="configureId != null">
        #{configureId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetailExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_limit_adjust_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_limit_adjust_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adjustId != null">
        adjust_id = #{record.adjustId,jdbcType=BIGINT},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategoryName != null">
        middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.oldStoreType != null">
        old_store_type = #{record.oldStoreType,jdbcType=VARCHAR},
      </if>
      <if test="record.oldStoreTypeName != null">
        old_store_type_name = #{record.oldStoreTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.oldSkuMaxLimit != null">
        old_sku_max_limit = #{record.oldSkuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="record.oldSkuLowerLimit != null">
        old_sku_lower_limit = #{record.oldSkuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=VARCHAR},
      </if>
      <if test="record.storeTypeName != null">
        store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuMaxLimit != null">
        sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuLowerLimit != null">
        sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.oldConfigureId != null">
        old_configure_id = #{record.oldConfigureId,jdbcType=BIGINT},
      </if>
      <if test="record.configureId != null">
        configure_id = #{record.configureId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_limit_adjust_detail
    set id = #{record.id,jdbcType=BIGINT},
      adjust_id = #{record.adjustId,jdbcType=BIGINT},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      small_category = #{record.smallCategory,jdbcType=VARCHAR},
      small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      sub_category = #{record.subCategory,jdbcType=VARCHAR},
      sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      old_store_type = #{record.oldStoreType,jdbcType=VARCHAR},
      old_store_type_name = #{record.oldStoreTypeName,jdbcType=VARCHAR},
      old_sku_max_limit = #{record.oldSkuMaxLimit,jdbcType=INTEGER},
      old_sku_lower_limit = #{record.oldSkuLowerLimit,jdbcType=INTEGER},
      store_type = #{record.storeType,jdbcType=VARCHAR},
      store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
      sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
      sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
      old_configure_id = #{record.oldConfigureId,jdbcType=BIGINT},
      configure_id = #{record.configureId,jdbcType=BIGINT},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail">
    update jyml_store_sku_limit_adjust_detail
    <set>
      <if test="adjustId != null">
        adjust_id = #{adjustId,jdbcType=BIGINT},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        middle_category = #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreType != null">
        old_store_type = #{oldStoreType,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreTypeName != null">
        old_store_type_name = #{oldStoreTypeName,jdbcType=VARCHAR},
      </if>
      <if test="oldSkuMaxLimit != null">
        old_sku_max_limit = #{oldSkuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="oldSkuLowerLimit != null">
        old_sku_lower_limit = #{oldSkuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeName != null">
        store_type_name = #{storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="skuMaxLimit != null">
        sku_max_limit = #{skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="skuLowerLimit != null">
        sku_lower_limit = #{skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="oldConfigureId != null">
        old_configure_id = #{oldConfigureId,jdbcType=BIGINT},
      </if>
      <if test="configureId != null">
        configure_id = #{configureId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail">
    update jyml_store_sku_limit_adjust_detail
    set adjust_id = #{adjustId,jdbcType=BIGINT},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      category_name = #{categoryName,jdbcType=VARCHAR},
      middle_category = #{middleCategory,jdbcType=VARCHAR},
      middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      small_category = #{smallCategory,jdbcType=VARCHAR},
      small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      sub_category = #{subCategory,jdbcType=VARCHAR},
      sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      old_store_type = #{oldStoreType,jdbcType=VARCHAR},
      old_store_type_name = #{oldStoreTypeName,jdbcType=VARCHAR},
      old_sku_max_limit = #{oldSkuMaxLimit,jdbcType=INTEGER},
      old_sku_lower_limit = #{oldSkuLowerLimit,jdbcType=INTEGER},
      store_type = #{storeType,jdbcType=VARCHAR},
      store_type_name = #{storeTypeName,jdbcType=VARCHAR},
      sku_max_limit = #{skuMaxLimit,jdbcType=INTEGER},
      sku_lower_limit = #{skuLowerLimit,jdbcType=INTEGER},
      old_configure_id = #{oldConfigureId,jdbcType=BIGINT},
      configure_id = #{configureId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>