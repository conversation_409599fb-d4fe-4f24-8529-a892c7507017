<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.DgmsCommonLockMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.DgmsCommonLock">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="lock_key" jdbcType="VARCHAR" property="lockKey" />
    <result column="thread_id" jdbcType="BIGINT" property="threadId" />
    <result column="entry_count" jdbcType="INTEGER" property="entryCount" />
    <result column="host_ip" jdbcType="VARCHAR" property="hostIp" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, lock_key, thread_id, entry_count, host_ip, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.DgmsCommonLockExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dgms_common_lock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dgms_common_lock
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from dgms_common_lock
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.DgmsCommonLockExample">
    delete from dgms_common_lock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.DgmsCommonLock" useGeneratedKeys="true">
    insert into dgms_common_lock (lock_key, thread_id, entry_count, 
      host_ip, gmt_create)
    values (#{lockKey,jdbcType=VARCHAR}, #{threadId,jdbcType=BIGINT}, #{entryCount,jdbcType=INTEGER}, 
      #{hostIp,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.DgmsCommonLock" useGeneratedKeys="true">
    insert into dgms_common_lock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lockKey != null">
        lock_key,
      </if>
      <if test="threadId != null">
        thread_id,
      </if>
      <if test="entryCount != null">
        entry_count,
      </if>
      <if test="hostIp != null">
        host_ip,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lockKey != null">
        #{lockKey,jdbcType=VARCHAR},
      </if>
      <if test="threadId != null">
        #{threadId,jdbcType=BIGINT},
      </if>
      <if test="entryCount != null">
        #{entryCount,jdbcType=INTEGER},
      </if>
      <if test="hostIp != null">
        #{hostIp,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.DgmsCommonLockExample" resultType="java.lang.Long">
    select count(*) from dgms_common_lock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dgms_common_lock
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.lockKey != null">
        lock_key = #{record.lockKey,jdbcType=VARCHAR},
      </if>
      <if test="record.threadId != null">
        thread_id = #{record.threadId,jdbcType=BIGINT},
      </if>
      <if test="record.entryCount != null">
        entry_count = #{record.entryCount,jdbcType=INTEGER},
      </if>
      <if test="record.hostIp != null">
        host_ip = #{record.hostIp,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dgms_common_lock
    set id = #{record.id,jdbcType=BIGINT},
      lock_key = #{record.lockKey,jdbcType=VARCHAR},
      thread_id = #{record.threadId,jdbcType=BIGINT},
      entry_count = #{record.entryCount,jdbcType=INTEGER},
      host_ip = #{record.hostIp,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.DgmsCommonLock">
    update dgms_common_lock
    <set>
      <if test="lockKey != null">
        lock_key = #{lockKey,jdbcType=VARCHAR},
      </if>
      <if test="threadId != null">
        thread_id = #{threadId,jdbcType=BIGINT},
      </if>
      <if test="entryCount != null">
        entry_count = #{entryCount,jdbcType=INTEGER},
      </if>
      <if test="hostIp != null">
        host_ip = #{hostIp,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.DgmsCommonLock">
    update dgms_common_lock
    set lock_key = #{lockKey,jdbcType=VARCHAR},
      thread_id = #{threadId,jdbcType=BIGINT},
      entry_count = #{entryCount,jdbcType=INTEGER},
      host_ip = #{hostIp,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>