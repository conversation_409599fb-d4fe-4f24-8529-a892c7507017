<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.OnlineHotGoodsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.OnlineHotGoods">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="goods_source" jdbcType="VARCHAR" property="goodsSource" />
    <result column="goods_year" jdbcType="VARCHAR" property="goodsYear" />
    <result column="goods_time_dimension" jdbcType="VARCHAR" property="goodsTimeDimension" />
    <result column="goods_time_frame" jdbcType="VARCHAR" property="goodsTimeFrame" />
    <result column="goods_province" jdbcType="VARCHAR" property="goodsProvince" />
    <result column="goods_city" jdbcType="VARCHAR" property="goodsCity" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_rank" jdbcType="VARCHAR" property="goodsRank" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, goods_source, goods_year, goods_time_dimension, goods_time_frame, goods_province, 
    goods_city, bar_code, goods_name, goods_rank, extend, version, gmt_create, gmt_update, 
    is_delete, create_by_id, create_by, update_by_id, update_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.OnlineHotGoodsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from online_hot_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from online_hot_goods
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from online_hot_goods
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.OnlineHotGoodsExample">
    delete from online_hot_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityDgms.OnlineHotGoods">
    insert into online_hot_goods (id, goods_source, goods_year, 
      goods_time_dimension, goods_time_frame, goods_province, 
      goods_city, bar_code, goods_name, 
      goods_rank, extend, version, 
      gmt_create, gmt_update, is_delete, 
      create_by_id, create_by, update_by_id, 
      update_by)
    values (#{id,jdbcType=BIGINT}, #{goodsSource,jdbcType=VARCHAR}, #{goodsYear,jdbcType=VARCHAR}, 
      #{goodsTimeDimension,jdbcType=VARCHAR}, #{goodsTimeFrame,jdbcType=VARCHAR}, #{goodsProvince,jdbcType=VARCHAR}, 
      #{goodsCity,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{goodsRank,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER}, 
      #{createById,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityDgms.OnlineHotGoods">
    insert into online_hot_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="goodsSource != null">
        goods_source,
      </if>
      <if test="goodsYear != null">
        goods_year,
      </if>
      <if test="goodsTimeDimension != null">
        goods_time_dimension,
      </if>
      <if test="goodsTimeFrame != null">
        goods_time_frame,
      </if>
      <if test="goodsProvince != null">
        goods_province,
      </if>
      <if test="goodsCity != null">
        goods_city,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsRank != null">
        goods_rank,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="goodsSource != null">
        #{goodsSource,jdbcType=VARCHAR},
      </if>
      <if test="goodsYear != null">
        #{goodsYear,jdbcType=VARCHAR},
      </if>
      <if test="goodsTimeDimension != null">
        #{goodsTimeDimension,jdbcType=VARCHAR},
      </if>
      <if test="goodsTimeFrame != null">
        #{goodsTimeFrame,jdbcType=VARCHAR},
      </if>
      <if test="goodsProvince != null">
        #{goodsProvince,jdbcType=VARCHAR},
      </if>
      <if test="goodsCity != null">
        #{goodsCity,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsRank != null">
        #{goodsRank,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.OnlineHotGoodsExample" resultType="java.lang.Long">
    select count(*) from online_hot_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update online_hot_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.goodsSource != null">
        goods_source = #{record.goodsSource,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsYear != null">
        goods_year = #{record.goodsYear,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsTimeDimension != null">
        goods_time_dimension = #{record.goodsTimeDimension,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsTimeFrame != null">
        goods_time_frame = #{record.goodsTimeFrame,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsProvince != null">
        goods_province = #{record.goodsProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCity != null">
        goods_city = #{record.goodsCity,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsRank != null">
        goods_rank = #{record.goodsRank,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update online_hot_goods
    set id = #{record.id,jdbcType=BIGINT},
      goods_source = #{record.goodsSource,jdbcType=VARCHAR},
      goods_year = #{record.goodsYear,jdbcType=VARCHAR},
      goods_time_dimension = #{record.goodsTimeDimension,jdbcType=VARCHAR},
      goods_time_frame = #{record.goodsTimeFrame,jdbcType=VARCHAR},
      goods_province = #{record.goodsProvince,jdbcType=VARCHAR},
      goods_city = #{record.goodsCity,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_rank = #{record.goodsRank,jdbcType=VARCHAR},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=INTEGER},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.OnlineHotGoods">
    update online_hot_goods
    <set>
      <if test="goodsSource != null">
        goods_source = #{goodsSource,jdbcType=VARCHAR},
      </if>
      <if test="goodsYear != null">
        goods_year = #{goodsYear,jdbcType=VARCHAR},
      </if>
      <if test="goodsTimeDimension != null">
        goods_time_dimension = #{goodsTimeDimension,jdbcType=VARCHAR},
      </if>
      <if test="goodsTimeFrame != null">
        goods_time_frame = #{goodsTimeFrame,jdbcType=VARCHAR},
      </if>
      <if test="goodsProvince != null">
        goods_province = #{goodsProvince,jdbcType=VARCHAR},
      </if>
      <if test="goodsCity != null">
        goods_city = #{goodsCity,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsRank != null">
        goods_rank = #{goodsRank,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.OnlineHotGoods">
    update online_hot_goods
    set goods_source = #{goodsSource,jdbcType=VARCHAR},
      goods_year = #{goodsYear,jdbcType=VARCHAR},
      goods_time_dimension = #{goodsTimeDimension,jdbcType=VARCHAR},
      goods_time_frame = #{goodsTimeFrame,jdbcType=VARCHAR},
      goods_province = #{goodsProvince,jdbcType=VARCHAR},
      goods_city = #{goodsCity,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_rank = #{goodsRank,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=INTEGER},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>