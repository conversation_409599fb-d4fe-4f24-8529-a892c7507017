<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.JymlStoreSkuLimitAdjustEffectMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_id" jdbcType="BIGINT" property="adjustId" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="old_store_type" jdbcType="VARCHAR" property="oldStoreType" />
    <result column="old_store_type_name" jdbcType="VARCHAR" property="oldStoreTypeName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, adjust_id, platform_org_id, platform_name, company_org_id, business_id, company_code, 
    company_name, province, store_org_id, store_id, store_code, store_name, city, category, 
    category_name, middle_category, middle_category_name, small_category, small_category_name, 
    sub_category, sub_category_name, old_store_type, old_store_type_name, store_type, 
    store_type_name, `status`, extend, version, created_by, created_name, updated_by, 
    updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_limit_adjust_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_store_sku_limit_adjust_effect
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_limit_adjust_effect
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffectExample">
    delete from jyml_store_sku_limit_adjust_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_effect (adjust_id, platform_org_id, platform_name, 
      company_org_id, business_id, company_code, 
      company_name, province, store_org_id, 
      store_id, store_code, store_name, 
      city, category, category_name, 
      middle_category, middle_category_name, small_category, 
      small_category_name, sub_category, sub_category_name, 
      old_store_type, old_store_type_name, store_type, 
      store_type_name, `status`, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{adjustId,jdbcType=BIGINT}, #{platformOrgId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, 
      #{companyOrgId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, 
      #{storeId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, 
      #{middleCategory,jdbcType=VARCHAR}, #{middleCategoryName,jdbcType=VARCHAR}, #{smallCategory,jdbcType=VARCHAR}, 
      #{smallCategoryName,jdbcType=VARCHAR}, #{subCategory,jdbcType=VARCHAR}, #{subCategoryName,jdbcType=VARCHAR}, 
      #{oldStoreType,jdbcType=VARCHAR}, #{oldStoreTypeName,jdbcType=VARCHAR}, #{storeType,jdbcType=VARCHAR}, 
      #{storeTypeName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect" useGeneratedKeys="true">
    insert into jyml_store_sku_limit_adjust_effect
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustId != null">
        adjust_id,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="middleCategory != null">
        middle_category,
      </if>
      <if test="middleCategoryName != null">
        middle_category_name,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="smallCategoryName != null">
        small_category_name,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="oldStoreType != null">
        old_store_type,
      </if>
      <if test="oldStoreTypeName != null">
        old_store_type_name,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
      <if test="storeTypeName != null">
        store_type_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustId != null">
        #{adjustId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreType != null">
        #{oldStoreType,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreTypeName != null">
        #{oldStoreTypeName,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeName != null">
        #{storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffectExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_limit_adjust_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_limit_adjust_effect
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adjustId != null">
        adjust_id = #{record.adjustId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategoryName != null">
        middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.oldStoreType != null">
        old_store_type = #{record.oldStoreType,jdbcType=VARCHAR},
      </if>
      <if test="record.oldStoreTypeName != null">
        old_store_type_name = #{record.oldStoreTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=VARCHAR},
      </if>
      <if test="record.storeTypeName != null">
        store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_limit_adjust_effect
    set id = #{record.id,jdbcType=BIGINT},
      adjust_id = #{record.adjustId,jdbcType=BIGINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      small_category = #{record.smallCategory,jdbcType=VARCHAR},
      small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      sub_category = #{record.subCategory,jdbcType=VARCHAR},
      sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      old_store_type = #{record.oldStoreType,jdbcType=VARCHAR},
      old_store_type_name = #{record.oldStoreTypeName,jdbcType=VARCHAR},
      store_type = #{record.storeType,jdbcType=VARCHAR},
      store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect">
    update jyml_store_sku_limit_adjust_effect
    <set>
      <if test="adjustId != null">
        adjust_id = #{adjustId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        middle_category = #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreType != null">
        old_store_type = #{oldStoreType,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreTypeName != null">
        old_store_type_name = #{oldStoreTypeName,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeName != null">
        store_type_name = #{storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect">
    update jyml_store_sku_limit_adjust_effect
    set adjust_id = #{adjustId,jdbcType=BIGINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      category_name = #{categoryName,jdbcType=VARCHAR},
      middle_category = #{middleCategory,jdbcType=VARCHAR},
      middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      small_category = #{smallCategory,jdbcType=VARCHAR},
      small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      sub_category = #{subCategory,jdbcType=VARCHAR},
      sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      old_store_type = #{oldStoreType,jdbcType=VARCHAR},
      old_store_type_name = #{oldStoreTypeName,jdbcType=VARCHAR},
      store_type = #{storeType,jdbcType=VARCHAR},
      store_type_name = #{storeTypeName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>