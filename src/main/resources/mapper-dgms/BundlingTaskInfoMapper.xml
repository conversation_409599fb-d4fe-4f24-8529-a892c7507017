<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.BundlingTaskInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.BundlingTaskInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="select_id" jdbcType="BIGINT" property="selectId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="commit_by" jdbcType="BIGINT" property="commitBy" />
    <result column="commit_name" jdbcType="VARCHAR" property="commitName" />
    <result column="issued_by" jdbcType="BIGINT" property="issuedBy" />
    <result column="issued_name" jdbcType="VARCHAR" property="issuedName" />
    <result column="cancel_by" jdbcType="BIGINT" property="cancelBy" />
    <result column="cancel_name" jdbcType="VARCHAR" property="cancelName" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="gmt_commit" jdbcType="TIMESTAMP" property="gmtCommit" />
    <result column="gmt_calculated" jdbcType="TIMESTAMP" property="gmtCalculated" />
    <result column="gmt_issued" jdbcType="TIMESTAMP" property="gmtIssued" />
    <result column="gmt_cancel" jdbcType="TIMESTAMP" property="gmtCancel" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_code, task_name, task_type, select_id, org_id, org_name, memo, commit_by, 
    commit_name, issued_by, issued_name, cancel_by, cancel_name, task_status, gmt_commit, 
    gmt_calculated, gmt_issued, gmt_cancel, `status`, gmt_create, gmt_update, extend, 
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bundling_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bundling_task_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bundling_task_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfoExample">
    delete from bundling_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfo" useGeneratedKeys="true">
    insert into bundling_task_info (id, task_code, task_name, 
      task_type, select_id, org_id, 
      org_name, memo, commit_by, 
      commit_name, issued_by, issued_name, 
      cancel_by, cancel_name, task_status, 
      gmt_commit, gmt_calculated, gmt_issued, 
      gmt_cancel, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=BIGINT}, #{taskCode,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=TINYINT}, #{selectId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, 
      #{orgName,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, #{commitBy,jdbcType=BIGINT}, 
      #{commitName,jdbcType=VARCHAR}, #{issuedBy,jdbcType=BIGINT}, #{issuedName,jdbcType=VARCHAR}, 
      #{cancelBy,jdbcType=BIGINT}, #{cancelName,jdbcType=VARCHAR}, #{taskStatus,jdbcType=TINYINT}, 
      #{gmtCommit,jdbcType=TIMESTAMP}, #{gmtCalculated,jdbcType=TIMESTAMP}, #{gmtIssued,jdbcType=TIMESTAMP}, 
      #{gmtCancel,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfo" useGeneratedKeys="true">
    insert into bundling_task_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskCode != null">
        task_code,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="selectId != null">
        select_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="commitBy != null">
        commit_by,
      </if>
      <if test="commitName != null">
        commit_name,
      </if>
      <if test="issuedBy != null">
        issued_by,
      </if>
      <if test="issuedName != null">
        issued_name,
      </if>
      <if test="cancelBy != null">
        cancel_by,
      </if>
      <if test="cancelName != null">
        cancel_name,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="gmtCommit != null">
        gmt_commit,
      </if>
      <if test="gmtCalculated != null">
        gmt_calculated,
      </if>
      <if test="gmtIssued != null">
        gmt_issued,
      </if>
      <if test="gmtCancel != null">
        gmt_cancel,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="selectId != null">
        #{selectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="commitBy != null">
        #{commitBy,jdbcType=BIGINT},
      </if>
      <if test="commitName != null">
        #{commitName,jdbcType=VARCHAR},
      </if>
      <if test="issuedBy != null">
        #{issuedBy,jdbcType=BIGINT},
      </if>
      <if test="issuedName != null">
        #{issuedName,jdbcType=VARCHAR},
      </if>
      <if test="cancelBy != null">
        #{cancelBy,jdbcType=BIGINT},
      </if>
      <if test="cancelName != null">
        #{cancelName,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="gmtCommit != null">
        #{gmtCommit,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCalculated != null">
        #{gmtCalculated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtIssued != null">
        #{gmtIssued,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCancel != null">
        #{gmtCancel,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfoExample" resultType="java.lang.Long">
    select count(*) from bundling_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bundling_task_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskCode != null">
        task_code = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.selectId != null">
        select_id = #{record.selectId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.commitBy != null">
        commit_by = #{record.commitBy,jdbcType=BIGINT},
      </if>
      <if test="record.commitName != null">
        commit_name = #{record.commitName,jdbcType=VARCHAR},
      </if>
      <if test="record.issuedBy != null">
        issued_by = #{record.issuedBy,jdbcType=BIGINT},
      </if>
      <if test="record.issuedName != null">
        issued_name = #{record.issuedName,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelBy != null">
        cancel_by = #{record.cancelBy,jdbcType=BIGINT},
      </if>
      <if test="record.cancelName != null">
        cancel_name = #{record.cancelName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCommit != null">
        gmt_commit = #{record.gmtCommit,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCalculated != null">
        gmt_calculated = #{record.gmtCalculated,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtIssued != null">
        gmt_issued = #{record.gmtIssued,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCancel != null">
        gmt_cancel = #{record.gmtCancel,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bundling_task_info
    set id = #{record.id,jdbcType=BIGINT},
      task_code = #{record.taskCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=TINYINT},
      select_id = #{record.selectId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      memo = #{record.memo,jdbcType=VARCHAR},
      commit_by = #{record.commitBy,jdbcType=BIGINT},
      commit_name = #{record.commitName,jdbcType=VARCHAR},
      issued_by = #{record.issuedBy,jdbcType=BIGINT},
      issued_name = #{record.issuedName,jdbcType=VARCHAR},
      cancel_by = #{record.cancelBy,jdbcType=BIGINT},
      cancel_name = #{record.cancelName,jdbcType=VARCHAR},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      gmt_commit = #{record.gmtCommit,jdbcType=TIMESTAMP},
      gmt_calculated = #{record.gmtCalculated,jdbcType=TIMESTAMP},
      gmt_issued = #{record.gmtIssued,jdbcType=TIMESTAMP},
      gmt_cancel = #{record.gmtCancel,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfo">
    update bundling_task_info
    <set>
      <if test="taskCode != null">
        task_code = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="selectId != null">
        select_id = #{selectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="commitBy != null">
        commit_by = #{commitBy,jdbcType=BIGINT},
      </if>
      <if test="commitName != null">
        commit_name = #{commitName,jdbcType=VARCHAR},
      </if>
      <if test="issuedBy != null">
        issued_by = #{issuedBy,jdbcType=BIGINT},
      </if>
      <if test="issuedName != null">
        issued_name = #{issuedName,jdbcType=VARCHAR},
      </if>
      <if test="cancelBy != null">
        cancel_by = #{cancelBy,jdbcType=BIGINT},
      </if>
      <if test="cancelName != null">
        cancel_name = #{cancelName,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="gmtCommit != null">
        gmt_commit = #{gmtCommit,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCalculated != null">
        gmt_calculated = #{gmtCalculated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtIssued != null">
        gmt_issued = #{gmtIssued,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCancel != null">
        gmt_cancel = #{gmtCancel,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.BundlingTaskInfo">
    update bundling_task_info
    set task_code = #{taskCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=TINYINT},
      select_id = #{selectId,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      commit_by = #{commitBy,jdbcType=BIGINT},
      commit_name = #{commitName,jdbcType=VARCHAR},
      issued_by = #{issuedBy,jdbcType=BIGINT},
      issued_name = #{issuedName,jdbcType=VARCHAR},
      cancel_by = #{cancelBy,jdbcType=BIGINT},
      cancel_name = #{cancelName,jdbcType=VARCHAR},
      task_status = #{taskStatus,jdbcType=TINYINT},
      gmt_commit = #{gmtCommit,jdbcType=TIMESTAMP},
      gmt_calculated = #{gmtCalculated,jdbcType=TIMESTAMP},
      gmt_issued = #{gmtIssued,jdbcType=TIMESTAMP},
      gmt_cancel = #{gmtCancel,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>