<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperDgms.CommonDictionaryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityDgms.CommonDictionary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
    <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
    <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
    <result column="scope_code" jdbcType="VARCHAR" property="scopeCode" />
    <result column="scope_desc" jdbcType="VARCHAR" property="scopeDesc" />
    <result column="edit_able" jdbcType="TINYINT" property="editAble" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dict_name, dict_code, default_value, scope_code, scope_desc, edit_able, `alias`, 
    gmt_create, gmt_update, extend, version, is_delete, create_by_id, create_by, update_by, 
    update_by_id
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityDgms.CommonDictionaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from common_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from common_dictionary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from common_dictionary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityDgms.CommonDictionaryExample">
    delete from common_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityDgms.CommonDictionary">
    insert into common_dictionary (id, dict_name, dict_code, 
      default_value, scope_code, scope_desc, 
      edit_able, `alias`, gmt_create, 
      gmt_update, extend, version, 
      is_delete, create_by_id, create_by, 
      update_by, update_by_id)
    values (#{id,jdbcType=BIGINT}, #{dictName,jdbcType=VARCHAR}, #{dictCode,jdbcType=VARCHAR}, 
      #{defaultValue,jdbcType=VARCHAR}, #{scopeCode,jdbcType=VARCHAR}, #{scopeDesc,jdbcType=VARCHAR}, 
      #{editAble,jdbcType=TINYINT}, #{alias,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=INTEGER}, #{createById,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityDgms.CommonDictionary">
    insert into common_dictionary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dictName != null">
        dict_name,
      </if>
      <if test="dictCode != null">
        dict_code,
      </if>
      <if test="defaultValue != null">
        default_value,
      </if>
      <if test="scopeCode != null">
        scope_code,
      </if>
      <if test="scopeDesc != null">
        scope_desc,
      </if>
      <if test="editAble != null">
        edit_able,
      </if>
      <if test="alias != null">
        `alias`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dictName != null">
        #{dictName,jdbcType=VARCHAR},
      </if>
      <if test="dictCode != null">
        #{dictCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="scopeCode != null">
        #{scopeCode,jdbcType=VARCHAR},
      </if>
      <if test="scopeDesc != null">
        #{scopeDesc,jdbcType=VARCHAR},
      </if>
      <if test="editAble != null">
        #{editAble,jdbcType=TINYINT},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityDgms.CommonDictionaryExample" resultType="java.lang.Long">
    select count(*) from common_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update common_dictionary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dictName != null">
        dict_name = #{record.dictName,jdbcType=VARCHAR},
      </if>
      <if test="record.dictCode != null">
        dict_code = #{record.dictCode,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultValue != null">
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.scopeCode != null">
        scope_code = #{record.scopeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.scopeDesc != null">
        scope_desc = #{record.scopeDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.editAble != null">
        edit_able = #{record.editAble,jdbcType=TINYINT},
      </if>
      <if test="record.alias != null">
        `alias` = #{record.alias,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update common_dictionary
    set id = #{record.id,jdbcType=BIGINT},
      dict_name = #{record.dictName,jdbcType=VARCHAR},
      dict_code = #{record.dictCode,jdbcType=VARCHAR},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      scope_code = #{record.scopeCode,jdbcType=VARCHAR},
      scope_desc = #{record.scopeDesc,jdbcType=VARCHAR},
      edit_able = #{record.editAble,jdbcType=TINYINT},
      `alias` = #{record.alias,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      is_delete = #{record.isDelete,jdbcType=INTEGER},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityDgms.CommonDictionary">
    update common_dictionary
    <set>
      <if test="dictName != null">
        dict_name = #{dictName,jdbcType=VARCHAR},
      </if>
      <if test="dictCode != null">
        dict_code = #{dictCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="scopeCode != null">
        scope_code = #{scopeCode,jdbcType=VARCHAR},
      </if>
      <if test="scopeDesc != null">
        scope_desc = #{scopeDesc,jdbcType=VARCHAR},
      </if>
      <if test="editAble != null">
        edit_able = #{editAble,jdbcType=TINYINT},
      </if>
      <if test="alias != null">
        `alias` = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityDgms.CommonDictionary">
    update common_dictionary
    set dict_name = #{dictName,jdbcType=VARCHAR},
      dict_code = #{dictCode,jdbcType=VARCHAR},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      scope_code = #{scopeCode,jdbcType=VARCHAR},
      scope_desc = #{scopeDesc,jdbcType=VARCHAR},
      edit_able = #{editAble,jdbcType=TINYINT},
      `alias` = #{alias,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=INTEGER},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>