<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.DgmsSingleStoreRecommendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.DgmsSingleStoreRecommend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="recommend_date" jdbcType="DATE" property="recommendDate" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="promotion_able" jdbcType="TINYINT" property="promotionAble" />
    <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
    <result column="promotion_way" jdbcType="VARCHAR" property="promotionWay" />
    <result column="threshold_info" jdbcType="VARCHAR" property="thresholdInfo" />
    <result column="fav_info" jdbcType="VARCHAR" property="favInfo" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, recommend_date, store_code, goods_no, promotion_able, promotion_name, promotion_way, 
    threshold_info, fav_info, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dgms_single_store_recommend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dgms_single_store_recommend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from dgms_single_store_recommend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommendExample">
    delete from dgms_single_store_recommend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommend" useGeneratedKeys="true">
    insert into dgms_single_store_recommend (recommend_date, store_code, goods_no, 
      promotion_able, promotion_name, promotion_way, 
      threshold_info, fav_info, gmt_create, 
      gmt_update)
    values (#{recommendDate,jdbcType=DATE}, #{storeCode,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{promotionAble,jdbcType=TINYINT}, #{promotionName,jdbcType=VARCHAR}, #{promotionWay,jdbcType=VARCHAR}, 
      #{thresholdInfo,jdbcType=VARCHAR}, #{favInfo,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommend" useGeneratedKeys="true">
    insert into dgms_single_store_recommend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recommendDate != null">
        recommend_date,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="promotionAble != null">
        promotion_able,
      </if>
      <if test="promotionName != null">
        promotion_name,
      </if>
      <if test="promotionWay != null">
        promotion_way,
      </if>
      <if test="thresholdInfo != null">
        threshold_info,
      </if>
      <if test="favInfo != null">
        fav_info,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recommendDate != null">
        #{recommendDate,jdbcType=DATE},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionAble != null">
        #{promotionAble,jdbcType=TINYINT},
      </if>
      <if test="promotionName != null">
        #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionWay != null">
        #{promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="thresholdInfo != null">
        #{thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="favInfo != null">
        #{favInfo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommendExample" resultType="java.lang.Long">
    select count(*) from dgms_single_store_recommend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dgms_single_store_recommend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.recommendDate != null">
        recommend_date = #{record.recommendDate,jdbcType=DATE},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionAble != null">
        promotion_able = #{record.promotionAble,jdbcType=TINYINT},
      </if>
      <if test="record.promotionName != null">
        promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionWay != null">
        promotion_way = #{record.promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.thresholdInfo != null">
        threshold_info = #{record.thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.favInfo != null">
        fav_info = #{record.favInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dgms_single_store_recommend
    set id = #{record.id,jdbcType=BIGINT},
      recommend_date = #{record.recommendDate,jdbcType=DATE},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      promotion_able = #{record.promotionAble,jdbcType=TINYINT},
      promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      promotion_way = #{record.promotionWay,jdbcType=VARCHAR},
      threshold_info = #{record.thresholdInfo,jdbcType=VARCHAR},
      fav_info = #{record.favInfo,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommend">
    update dgms_single_store_recommend
    <set>
      <if test="recommendDate != null">
        recommend_date = #{recommendDate,jdbcType=DATE},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionAble != null">
        promotion_able = #{promotionAble,jdbcType=TINYINT},
      </if>
      <if test="promotionName != null">
        promotion_name = #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionWay != null">
        promotion_way = #{promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="thresholdInfo != null">
        threshold_info = #{thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="favInfo != null">
        fav_info = #{favInfo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.DgmsSingleStoreRecommend">
    update dgms_single_store_recommend
    set recommend_date = #{recommendDate,jdbcType=DATE},
      store_code = #{storeCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      promotion_able = #{promotionAble,jdbcType=TINYINT},
      promotion_name = #{promotionName,jdbcType=VARCHAR},
      promotion_way = #{promotionWay,jdbcType=VARCHAR},
      threshold_info = #{thresholdInfo,jdbcType=VARCHAR},
      fav_info = #{favInfo,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>