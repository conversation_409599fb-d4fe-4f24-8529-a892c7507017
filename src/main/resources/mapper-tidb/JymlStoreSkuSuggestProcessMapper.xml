<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.JymlStoreSkuSuggestProcessMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="zdt_start_notice" jdbcType="VARCHAR" property="zdtStartNotice" />
    <result column="zdt_start_notice_falg" jdbcType="INTEGER" property="zdtStartNoticeFalg" />
    <result column="begin_process_time" jdbcType="TIMESTAMP" property="beginProcessTime" />
    <result column="scheduled_process_time" jdbcType="TIMESTAMP" property="scheduledProcessTime" />
    <result column="end_process_time" jdbcType="TIMESTAMP" property="endProcessTime" />
    <result column="confirmed" jdbcType="INTEGER" property="confirmed" />
    <result column="zdt_end_notice" jdbcType="VARCHAR" property="zdtEndNotice" />
    <result column="zdt_end_notice_falg" jdbcType="INTEGER" property="zdtEndNoticeFalg" />
    <result column="next_begin_process_time" jdbcType="TIMESTAMP" property="nextBeginProcessTime" />
    <result column="next_end_process_time" jdbcType="TIMESTAMP" property="nextEndProcessTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_org_id, store_id, store_code, zdt_start_notice, zdt_start_notice_falg,
    begin_process_time, scheduled_process_time, end_process_time, confirmed, zdt_end_notice,
    zdt_end_notice_falg, next_begin_process_time, next_end_process_time, `status`, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_suggest_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jyml_store_sku_suggest_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_suggest_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessExample">
    delete from jyml_store_sku_suggest_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcess">
    insert into jyml_store_sku_suggest_process (id, business_org_id, store_id,
    store_code, zdt_start_notice, zdt_start_notice_falg,
    begin_process_time, scheduled_process_time,
    end_process_time, confirmed, zdt_end_notice,
    zdt_end_notice_falg, next_begin_process_time,
    next_end_process_time, `status`, gmt_create,
    gmt_update, extend, version,
    created_by, created_name, updated_by,
    updated_name)
    values (#{id,jdbcType=BIGINT}, #{businessOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
    #{storeCode,jdbcType=VARCHAR}, #{zdtStartNotice,jdbcType=VARCHAR}, #{zdtStartNoticeFalg,jdbcType=INTEGER},
    #{beginProcessTime,jdbcType=TIMESTAMP}, #{scheduledProcessTime,jdbcType=TIMESTAMP},
    #{endProcessTime,jdbcType=TIMESTAMP}, #{confirmed,jdbcType=INTEGER}, #{zdtEndNotice,jdbcType=VARCHAR},
    #{zdtEndNoticeFalg,jdbcType=INTEGER}, #{nextBeginProcessTime,jdbcType=TIMESTAMP},
    #{nextEndProcessTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
    #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=BIGINT},
    #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT},
    #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcess">
    insert into jyml_store_sku_suggest_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="zdtStartNotice != null">
        zdt_start_notice,
      </if>
      <if test="zdtStartNoticeFalg != null">
        zdt_start_notice_falg,
      </if>
      <if test="beginProcessTime != null">
        begin_process_time,
      </if>
      <if test="scheduledProcessTime != null">
        scheduled_process_time,
      </if>
      <if test="endProcessTime != null">
        end_process_time,
      </if>
      <if test="confirmed != null">
        confirmed,
      </if>
      <if test="zdtEndNotice != null">
        zdt_end_notice,
      </if>
      <if test="zdtEndNoticeFalg != null">
        zdt_end_notice_falg,
      </if>
      <if test="nextBeginProcessTime != null">
        next_begin_process_time,
      </if>
      <if test="nextEndProcessTime != null">
        next_end_process_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="zdtStartNotice != null">
        #{zdtStartNotice,jdbcType=VARCHAR},
      </if>
      <if test="zdtStartNoticeFalg != null">
        #{zdtStartNoticeFalg,jdbcType=INTEGER},
      </if>
      <if test="beginProcessTime != null">
        #{beginProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduledProcessTime != null">
        #{scheduledProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endProcessTime != null">
        #{endProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmed != null">
        #{confirmed,jdbcType=INTEGER},
      </if>
      <if test="zdtEndNotice != null">
        #{zdtEndNotice,jdbcType=VARCHAR},
      </if>
      <if test="zdtEndNoticeFalg != null">
        #{zdtEndNoticeFalg,jdbcType=INTEGER},
      </if>
      <if test="nextBeginProcessTime != null">
        #{nextBeginProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextEndProcessTime != null">
        #{nextEndProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_suggest_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_suggest_process
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.zdtStartNotice != null">
        zdt_start_notice = #{record.zdtStartNotice,jdbcType=VARCHAR},
      </if>
      <if test="record.zdtStartNoticeFalg != null">
        zdt_start_notice_falg = #{record.zdtStartNoticeFalg,jdbcType=INTEGER},
      </if>
      <if test="record.beginProcessTime != null">
        begin_process_time = #{record.beginProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scheduledProcessTime != null">
        scheduled_process_time = #{record.scheduledProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endProcessTime != null">
        end_process_time = #{record.endProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmed != null">
        confirmed = #{record.confirmed,jdbcType=INTEGER},
      </if>
      <if test="record.zdtEndNotice != null">
        zdt_end_notice = #{record.zdtEndNotice,jdbcType=VARCHAR},
      </if>
      <if test="record.zdtEndNoticeFalg != null">
        zdt_end_notice_falg = #{record.zdtEndNoticeFalg,jdbcType=INTEGER},
      </if>
      <if test="record.nextBeginProcessTime != null">
        next_begin_process_time = #{record.nextBeginProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.nextEndProcessTime != null">
        next_end_process_time = #{record.nextEndProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_suggest_process
    set id = #{record.id,jdbcType=BIGINT},
    business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
    store_id = #{record.storeId,jdbcType=BIGINT},
    store_code = #{record.storeCode,jdbcType=VARCHAR},
    zdt_start_notice = #{record.zdtStartNotice,jdbcType=VARCHAR},
    zdt_start_notice_falg = #{record.zdtStartNoticeFalg,jdbcType=INTEGER},
    begin_process_time = #{record.beginProcessTime,jdbcType=TIMESTAMP},
    scheduled_process_time = #{record.scheduledProcessTime,jdbcType=TIMESTAMP},
    end_process_time = #{record.endProcessTime,jdbcType=TIMESTAMP},
    confirmed = #{record.confirmed,jdbcType=INTEGER},
    zdt_end_notice = #{record.zdtEndNotice,jdbcType=VARCHAR},
    zdt_end_notice_falg = #{record.zdtEndNoticeFalg,jdbcType=INTEGER},
    next_begin_process_time = #{record.nextBeginProcessTime,jdbcType=TIMESTAMP},
    next_end_process_time = #{record.nextEndProcessTime,jdbcType=TIMESTAMP},
    `status` = #{record.status,jdbcType=TINYINT},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{record.extend,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=BIGINT},
    created_by = #{record.createdBy,jdbcType=BIGINT},
    created_name = #{record.createdName,jdbcType=VARCHAR},
    updated_by = #{record.updatedBy,jdbcType=BIGINT},
    updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcess">
    update jyml_store_sku_suggest_process
    <set>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="zdtStartNotice != null">
        zdt_start_notice = #{zdtStartNotice,jdbcType=VARCHAR},
      </if>
      <if test="zdtStartNoticeFalg != null">
        zdt_start_notice_falg = #{zdtStartNoticeFalg,jdbcType=INTEGER},
      </if>
      <if test="beginProcessTime != null">
        begin_process_time = #{beginProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduledProcessTime != null">
        scheduled_process_time = #{scheduledProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endProcessTime != null">
        end_process_time = #{endProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmed != null">
        confirmed = #{confirmed,jdbcType=INTEGER},
      </if>
      <if test="zdtEndNotice != null">
        zdt_end_notice = #{zdtEndNotice,jdbcType=VARCHAR},
      </if>
      <if test="zdtEndNoticeFalg != null">
        zdt_end_notice_falg = #{zdtEndNoticeFalg,jdbcType=INTEGER},
      </if>
      <if test="nextBeginProcessTime != null">
        next_begin_process_time = #{nextBeginProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextEndProcessTime != null">
        next_end_process_time = #{nextEndProcessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcess">
    update jyml_store_sku_suggest_process
    set business_org_id = #{businessOrgId,jdbcType=BIGINT},
    store_id = #{storeId,jdbcType=BIGINT},
    store_code = #{storeCode,jdbcType=VARCHAR},
    zdt_start_notice = #{zdtStartNotice,jdbcType=VARCHAR},
    zdt_start_notice_falg = #{zdtStartNoticeFalg,jdbcType=INTEGER},
    begin_process_time = #{beginProcessTime,jdbcType=TIMESTAMP},
    scheduled_process_time = #{scheduledProcessTime,jdbcType=TIMESTAMP},
    end_process_time = #{endProcessTime,jdbcType=TIMESTAMP},
    confirmed = #{confirmed,jdbcType=INTEGER},
    zdt_end_notice = #{zdtEndNotice,jdbcType=VARCHAR},
    zdt_end_notice_falg = #{zdtEndNoticeFalg,jdbcType=INTEGER},
    next_begin_process_time = #{nextBeginProcessTime,jdbcType=TIMESTAMP},
    next_end_process_time = #{nextEndProcessTime,jdbcType=TIMESTAMP},
    `status` = #{status,jdbcType=TINYINT},
    gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{extend,jdbcType=VARCHAR},
    version = #{version,jdbcType=BIGINT},
    created_by = #{createdBy,jdbcType=BIGINT},
    created_name = #{createdName,jdbcType=VARCHAR},
    updated_by = #{updatedBy,jdbcType=BIGINT},
    updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>