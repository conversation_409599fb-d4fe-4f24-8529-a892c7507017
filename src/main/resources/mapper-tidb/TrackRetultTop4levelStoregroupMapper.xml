<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackRetultTop4levelStoregroupMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="data_from_v2" jdbcType="VARCHAR" property="dataFromV2" />
    <result column="orgId" jdbcType="VARCHAR" property="orgid" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="revise_store_group" jdbcType="VARCHAR" property="reviseStoreGroup" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goodsname" jdbcType="VARCHAR" property="goodsname" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="bak" jdbcType="VARCHAR" property="bak" />
    <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
    <result column="goodsspec" jdbcType="VARCHAR" property="goodsspec" />
    <result column="jx_cate1_name" jdbcType="VARCHAR" property="jxCate1Name" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
    <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
    <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
    <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="is_otc" jdbcType="VARCHAR" property="isOtc" />
    <result column="flag_disease" jdbcType="VARCHAR" property="flagDisease" />
    <result column="grossprofit" jdbcType="VARCHAR" property="grossprofit" />
    <result column="taotai_type" jdbcType="VARCHAR" property="taotaiType" />
    <result column="stjb" jdbcType="VARCHAR" property="stjb" />
    <result column="specialattributes_type" jdbcType="VARCHAR" property="specialattributesType" />
    <result column="in_stock_rate" jdbcType="VARCHAR" property="inStockRate" />
    <result column="in_sales_rate" jdbcType="VARCHAR" property="inSalesRate" />
    <result column="sku_int" jdbcType="VARCHAR" property="skuInt" />
    <result column="ph_org_bz_flag" jdbcType="VARCHAR" property="phOrgBzFlag" />
    <result column="num_cum_90" jdbcType="VARCHAR" property="numCum90" />
    <result column="amt_cum_90" jdbcType="VARCHAR" property="amtCum90" />
    <result column="profit_amt_cum_90" jdbcType="VARCHAR" property="profitAmtCum90" />
    <result column="profit_rate_90" jdbcType="VARCHAR" property="profitRate90" />
    <result column="amt_rate_org_90" jdbcType="VARCHAR" property="amtRateOrg90" />
    <result column="amt_rate_comp_90" jdbcType="VARCHAR" property="amtRateComp90" />
    <result column="num_cum_180" jdbcType="VARCHAR" property="numCum180" />
    <result column="amt_cum_180" jdbcType="VARCHAR" property="amtCum180" />
    <result column="profit_amt_cum_180" jdbcType="VARCHAR" property="profitAmtCum180" />
    <result column="profit_rate_180" jdbcType="VARCHAR" property="profitRate180" />
    <result column="amt_rate_org_180" jdbcType="VARCHAR" property="amtRateOrg180" />
    <result column="amt_rate_comp_180" jdbcType="VARCHAR" property="amtRateComp180" />
    <result column="store_num" jdbcType="VARCHAR" property="storeNum" />
    <result column="retail_price" jdbcType="VARCHAR" property="retailPrice" />
    <result column="revise_num_cum_90" jdbcType="VARCHAR" property="reviseNumCum90" />
    <result column="revise_amt_cum_90" jdbcType="VARCHAR" property="reviseAmtCum90" />
    <result column="revise_profit_amt_cum_90" jdbcType="VARCHAR" property="reviseProfitAmtCum90" />
    <result column="revise_profit_rate_90" jdbcType="VARCHAR" property="reviseProfitRate90" />
    <result column="revise_num_cum_180" jdbcType="VARCHAR" property="reviseNumCum180" />
    <result column="revise_amt_cum_180" jdbcType="VARCHAR" property="reviseAmtCum180" />
    <result column="revise_profit_amt_cum_180" jdbcType="VARCHAR" property="reviseProfitAmtCum180" />
    <result column="revise_profit_rate_180" jdbcType="VARCHAR" property="reviseProfitRate180" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, zone_new, data_from, data_from_v2, orgId, city, revise_store_group, 
    goods_id, goodsname, `level`, bak, goodsunit, goodsspec, jx_cate1_name, manufacturer, 
    classone_name, classtwo_name, classthree_name, classfour_name, component, is_otc, 
    flag_disease, grossprofit, taotai_type, stjb, specialattributes_type, in_stock_rate, 
    in_sales_rate, sku_int, ph_org_bz_flag, num_cum_90, amt_cum_90, profit_amt_cum_90, 
    profit_rate_90, amt_rate_org_90, amt_rate_comp_90, num_cum_180, amt_cum_180, profit_amt_cum_180, 
    profit_rate_180, amt_rate_org_180, amt_rate_comp_180, store_num, retail_price, revise_num_cum_90, 
    revise_amt_cum_90, revise_profit_amt_cum_90, revise_profit_rate_90, revise_num_cum_180, 
    revise_amt_cum_180, revise_profit_amt_cum_180, revise_profit_rate_180, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_retult_top4level_storegroup
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_retult_top4level_storegroup
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_retult_top4level_storegroup
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroupExample">
    delete from track_retult_top4level_storegroup
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup" useGeneratedKeys="true">
    insert into track_retult_top4level_storegroup (task_id, zone_new, data_from, 
      data_from_v2, orgId, city, 
      revise_store_group, goods_id, goodsname, 
      `level`, bak, goodsunit, 
      goodsspec, jx_cate1_name, manufacturer, 
      classone_name, classtwo_name, classthree_name, 
      classfour_name, component, is_otc, 
      flag_disease, grossprofit, taotai_type, 
      stjb, specialattributes_type, in_stock_rate, 
      in_sales_rate, sku_int, ph_org_bz_flag, 
      num_cum_90, amt_cum_90, profit_amt_cum_90, 
      profit_rate_90, amt_rate_org_90, amt_rate_comp_90, 
      num_cum_180, amt_cum_180, profit_amt_cum_180, 
      profit_rate_180, amt_rate_org_180, amt_rate_comp_180, 
      store_num, retail_price, revise_num_cum_90, 
      revise_amt_cum_90, revise_profit_amt_cum_90, revise_profit_rate_90, 
      revise_num_cum_180, revise_amt_cum_180, revise_profit_amt_cum_180, 
      revise_profit_rate_180, gmt_create)
    values (#{taskId,jdbcType=BIGINT}, #{zoneNew,jdbcType=VARCHAR}, #{dataFrom,jdbcType=VARCHAR}, 
      #{dataFromV2,jdbcType=VARCHAR}, #{orgid,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{reviseStoreGroup,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR}, #{goodsname,jdbcType=VARCHAR}, 
      #{level,jdbcType=VARCHAR}, #{bak,jdbcType=VARCHAR}, #{goodsunit,jdbcType=VARCHAR}, 
      #{goodsspec,jdbcType=VARCHAR}, #{jxCate1Name,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{classoneName,jdbcType=VARCHAR}, #{classtwoName,jdbcType=VARCHAR}, #{classthreeName,jdbcType=VARCHAR}, 
      #{classfourName,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, #{isOtc,jdbcType=VARCHAR}, 
      #{flagDisease,jdbcType=VARCHAR}, #{grossprofit,jdbcType=VARCHAR}, #{taotaiType,jdbcType=VARCHAR}, 
      #{stjb,jdbcType=VARCHAR}, #{specialattributesType,jdbcType=VARCHAR}, #{inStockRate,jdbcType=VARCHAR}, 
      #{inSalesRate,jdbcType=VARCHAR}, #{skuInt,jdbcType=VARCHAR}, #{phOrgBzFlag,jdbcType=VARCHAR}, 
      #{numCum90,jdbcType=VARCHAR}, #{amtCum90,jdbcType=VARCHAR}, #{profitAmtCum90,jdbcType=VARCHAR}, 
      #{profitRate90,jdbcType=VARCHAR}, #{amtRateOrg90,jdbcType=VARCHAR}, #{amtRateComp90,jdbcType=VARCHAR}, 
      #{numCum180,jdbcType=VARCHAR}, #{amtCum180,jdbcType=VARCHAR}, #{profitAmtCum180,jdbcType=VARCHAR}, 
      #{profitRate180,jdbcType=VARCHAR}, #{amtRateOrg180,jdbcType=VARCHAR}, #{amtRateComp180,jdbcType=VARCHAR}, 
      #{storeNum,jdbcType=VARCHAR}, #{retailPrice,jdbcType=VARCHAR}, #{reviseNumCum90,jdbcType=VARCHAR}, 
      #{reviseAmtCum90,jdbcType=VARCHAR}, #{reviseProfitAmtCum90,jdbcType=VARCHAR}, #{reviseProfitRate90,jdbcType=VARCHAR}, 
      #{reviseNumCum180,jdbcType=VARCHAR}, #{reviseAmtCum180,jdbcType=VARCHAR}, #{reviseProfitAmtCum180,jdbcType=VARCHAR}, 
      #{reviseProfitRate180,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup" useGeneratedKeys="true">
    insert into track_retult_top4level_storegroup
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="zoneNew != null">
        zone_new,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="dataFromV2 != null">
        data_from_v2,
      </if>
      <if test="orgid != null">
        orgId,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="reviseStoreGroup != null">
        revise_store_group,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsname != null">
        goodsname,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="bak != null">
        bak,
      </if>
      <if test="goodsunit != null">
        goodsunit,
      </if>
      <if test="goodsspec != null">
        goodsspec,
      </if>
      <if test="jxCate1Name != null">
        jx_cate1_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="classoneName != null">
        classone_name,
      </if>
      <if test="classtwoName != null">
        classtwo_name,
      </if>
      <if test="classthreeName != null">
        classthree_name,
      </if>
      <if test="classfourName != null">
        classfour_name,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="isOtc != null">
        is_otc,
      </if>
      <if test="flagDisease != null">
        flag_disease,
      </if>
      <if test="grossprofit != null">
        grossprofit,
      </if>
      <if test="taotaiType != null">
        taotai_type,
      </if>
      <if test="stjb != null">
        stjb,
      </if>
      <if test="specialattributesType != null">
        specialattributes_type,
      </if>
      <if test="inStockRate != null">
        in_stock_rate,
      </if>
      <if test="inSalesRate != null">
        in_sales_rate,
      </if>
      <if test="skuInt != null">
        sku_int,
      </if>
      <if test="phOrgBzFlag != null">
        ph_org_bz_flag,
      </if>
      <if test="numCum90 != null">
        num_cum_90,
      </if>
      <if test="amtCum90 != null">
        amt_cum_90,
      </if>
      <if test="profitAmtCum90 != null">
        profit_amt_cum_90,
      </if>
      <if test="profitRate90 != null">
        profit_rate_90,
      </if>
      <if test="amtRateOrg90 != null">
        amt_rate_org_90,
      </if>
      <if test="amtRateComp90 != null">
        amt_rate_comp_90,
      </if>
      <if test="numCum180 != null">
        num_cum_180,
      </if>
      <if test="amtCum180 != null">
        amt_cum_180,
      </if>
      <if test="profitAmtCum180 != null">
        profit_amt_cum_180,
      </if>
      <if test="profitRate180 != null">
        profit_rate_180,
      </if>
      <if test="amtRateOrg180 != null">
        amt_rate_org_180,
      </if>
      <if test="amtRateComp180 != null">
        amt_rate_comp_180,
      </if>
      <if test="storeNum != null">
        store_num,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="reviseNumCum90 != null">
        revise_num_cum_90,
      </if>
      <if test="reviseAmtCum90 != null">
        revise_amt_cum_90,
      </if>
      <if test="reviseProfitAmtCum90 != null">
        revise_profit_amt_cum_90,
      </if>
      <if test="reviseProfitRate90 != null">
        revise_profit_rate_90,
      </if>
      <if test="reviseNumCum180 != null">
        revise_num_cum_180,
      </if>
      <if test="reviseAmtCum180 != null">
        revise_amt_cum_180,
      </if>
      <if test="reviseProfitAmtCum180 != null">
        revise_profit_amt_cum_180,
      </if>
      <if test="reviseProfitRate180 != null">
        revise_profit_rate_180,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="dataFromV2 != null">
        #{dataFromV2,jdbcType=VARCHAR},
      </if>
      <if test="orgid != null">
        #{orgid,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="reviseStoreGroup != null">
        #{reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsname != null">
        #{goodsname,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="bak != null">
        #{bak,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="goodsspec != null">
        #{goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="jxCate1Name != null">
        #{jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="isOtc != null">
        #{isOtc,jdbcType=VARCHAR},
      </if>
      <if test="flagDisease != null">
        #{flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="grossprofit != null">
        #{grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="taotaiType != null">
        #{taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="stjb != null">
        #{stjb,jdbcType=VARCHAR},
      </if>
      <if test="specialattributesType != null">
        #{specialattributesType,jdbcType=VARCHAR},
      </if>
      <if test="inStockRate != null">
        #{inStockRate,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRate != null">
        #{inSalesRate,jdbcType=VARCHAR},
      </if>
      <if test="skuInt != null">
        #{skuInt,jdbcType=VARCHAR},
      </if>
      <if test="phOrgBzFlag != null">
        #{phOrgBzFlag,jdbcType=VARCHAR},
      </if>
      <if test="numCum90 != null">
        #{numCum90,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90 != null">
        #{amtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum90 != null">
        #{profitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90 != null">
        #{profitRate90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg90 != null">
        #{amtRateOrg90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp90 != null">
        #{amtRateComp90,jdbcType=VARCHAR},
      </if>
      <if test="numCum180 != null">
        #{numCum180,jdbcType=VARCHAR},
      </if>
      <if test="amtCum180 != null">
        #{amtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum180 != null">
        #{profitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitRate180 != null">
        #{profitRate180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg180 != null">
        #{amtRateOrg180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp180 != null">
        #{amtRateComp180,jdbcType=VARCHAR},
      </if>
      <if test="storeNum != null">
        #{storeNum,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum90 != null">
        #{reviseNumCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum90 != null">
        #{reviseAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum90 != null">
        #{reviseProfitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate90 != null">
        #{reviseProfitRate90,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum180 != null">
        #{reviseNumCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum180 != null">
        #{reviseAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum180 != null">
        #{reviseProfitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate180 != null">
        #{reviseProfitRate180,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroupExample" resultType="java.lang.Long">
    select count(*) from track_retult_top4level_storegroup
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_retult_top4level_storegroup
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.zoneNew != null">
        zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFromV2 != null">
        data_from_v2 = #{record.dataFromV2,jdbcType=VARCHAR},
      </if>
      <if test="record.orgid != null">
        orgId = #{record.orgid,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseStoreGroup != null">
        revise_store_group = #{record.reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsname != null">
        goodsname = #{record.goodsname,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.bak != null">
        bak = #{record.bak,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsunit != null">
        goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsspec != null">
        goodsspec = #{record.goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="record.jxCate1Name != null">
        jx_cate1_name = #{record.jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.classoneName != null">
        classone_name = #{record.classoneName,jdbcType=VARCHAR},
      </if>
      <if test="record.classtwoName != null">
        classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="record.classthreeName != null">
        classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="record.classfourName != null">
        classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.isOtc != null">
        is_otc = #{record.isOtc,jdbcType=VARCHAR},
      </if>
      <if test="record.flagDisease != null">
        flag_disease = #{record.flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="record.grossprofit != null">
        grossprofit = #{record.grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="record.taotaiType != null">
        taotai_type = #{record.taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="record.stjb != null">
        stjb = #{record.stjb,jdbcType=VARCHAR},
      </if>
      <if test="record.specialattributesType != null">
        specialattributes_type = #{record.specialattributesType,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockRate != null">
        in_stock_rate = #{record.inStockRate,jdbcType=VARCHAR},
      </if>
      <if test="record.inSalesRate != null">
        in_sales_rate = #{record.inSalesRate,jdbcType=VARCHAR},
      </if>
      <if test="record.skuInt != null">
        sku_int = #{record.skuInt,jdbcType=VARCHAR},
      </if>
      <if test="record.phOrgBzFlag != null">
        ph_org_bz_flag = #{record.phOrgBzFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum90 != null">
        num_cum_90 = #{record.numCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum90 != null">
        amt_cum_90 = #{record.amtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum90 != null">
        profit_amt_cum_90 = #{record.profitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate90 != null">
        profit_rate_90 = #{record.profitRate90,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateOrg90 != null">
        amt_rate_org_90 = #{record.amtRateOrg90,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateComp90 != null">
        amt_rate_comp_90 = #{record.amtRateComp90,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum180 != null">
        num_cum_180 = #{record.numCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum180 != null">
        amt_cum_180 = #{record.amtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum180 != null">
        profit_amt_cum_180 = #{record.profitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate180 != null">
        profit_rate_180 = #{record.profitRate180,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateOrg180 != null">
        amt_rate_org_180 = #{record.amtRateOrg180,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateComp180 != null">
        amt_rate_comp_180 = #{record.amtRateComp180,jdbcType=VARCHAR},
      </if>
      <if test="record.storeNum != null">
        store_num = #{record.storeNum,jdbcType=VARCHAR},
      </if>
      <if test="record.retailPrice != null">
        retail_price = #{record.retailPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseNumCum90 != null">
        revise_num_cum_90 = #{record.reviseNumCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseAmtCum90 != null">
        revise_amt_cum_90 = #{record.reviseAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitAmtCum90 != null">
        revise_profit_amt_cum_90 = #{record.reviseProfitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitRate90 != null">
        revise_profit_rate_90 = #{record.reviseProfitRate90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseNumCum180 != null">
        revise_num_cum_180 = #{record.reviseNumCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseAmtCum180 != null">
        revise_amt_cum_180 = #{record.reviseAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitAmtCum180 != null">
        revise_profit_amt_cum_180 = #{record.reviseProfitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitRate180 != null">
        revise_profit_rate_180 = #{record.reviseProfitRate180,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_retult_top4level_storegroup
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      data_from_v2 = #{record.dataFromV2,jdbcType=VARCHAR},
      orgId = #{record.orgid,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      revise_store_group = #{record.reviseStoreGroup,jdbcType=VARCHAR},
      goods_id = #{record.goodsId,jdbcType=VARCHAR},
      goodsname = #{record.goodsname,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=VARCHAR},
      bak = #{record.bak,jdbcType=VARCHAR},
      goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      goodsspec = #{record.goodsspec,jdbcType=VARCHAR},
      jx_cate1_name = #{record.jxCate1Name,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      classone_name = #{record.classoneName,jdbcType=VARCHAR},
      classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      is_otc = #{record.isOtc,jdbcType=VARCHAR},
      flag_disease = #{record.flagDisease,jdbcType=VARCHAR},
      grossprofit = #{record.grossprofit,jdbcType=VARCHAR},
      taotai_type = #{record.taotaiType,jdbcType=VARCHAR},
      stjb = #{record.stjb,jdbcType=VARCHAR},
      specialattributes_type = #{record.specialattributesType,jdbcType=VARCHAR},
      in_stock_rate = #{record.inStockRate,jdbcType=VARCHAR},
      in_sales_rate = #{record.inSalesRate,jdbcType=VARCHAR},
      sku_int = #{record.skuInt,jdbcType=VARCHAR},
      ph_org_bz_flag = #{record.phOrgBzFlag,jdbcType=VARCHAR},
      num_cum_90 = #{record.numCum90,jdbcType=VARCHAR},
      amt_cum_90 = #{record.amtCum90,jdbcType=VARCHAR},
      profit_amt_cum_90 = #{record.profitAmtCum90,jdbcType=VARCHAR},
      profit_rate_90 = #{record.profitRate90,jdbcType=VARCHAR},
      amt_rate_org_90 = #{record.amtRateOrg90,jdbcType=VARCHAR},
      amt_rate_comp_90 = #{record.amtRateComp90,jdbcType=VARCHAR},
      num_cum_180 = #{record.numCum180,jdbcType=VARCHAR},
      amt_cum_180 = #{record.amtCum180,jdbcType=VARCHAR},
      profit_amt_cum_180 = #{record.profitAmtCum180,jdbcType=VARCHAR},
      profit_rate_180 = #{record.profitRate180,jdbcType=VARCHAR},
      amt_rate_org_180 = #{record.amtRateOrg180,jdbcType=VARCHAR},
      amt_rate_comp_180 = #{record.amtRateComp180,jdbcType=VARCHAR},
      store_num = #{record.storeNum,jdbcType=VARCHAR},
      retail_price = #{record.retailPrice,jdbcType=VARCHAR},
      revise_num_cum_90 = #{record.reviseNumCum90,jdbcType=VARCHAR},
      revise_amt_cum_90 = #{record.reviseAmtCum90,jdbcType=VARCHAR},
      revise_profit_amt_cum_90 = #{record.reviseProfitAmtCum90,jdbcType=VARCHAR},
      revise_profit_rate_90 = #{record.reviseProfitRate90,jdbcType=VARCHAR},
      revise_num_cum_180 = #{record.reviseNumCum180,jdbcType=VARCHAR},
      revise_amt_cum_180 = #{record.reviseAmtCum180,jdbcType=VARCHAR},
      revise_profit_amt_cum_180 = #{record.reviseProfitAmtCum180,jdbcType=VARCHAR},
      revise_profit_rate_180 = #{record.reviseProfitRate180,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup">
    update track_retult_top4level_storegroup
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        zone_new = #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="dataFromV2 != null">
        data_from_v2 = #{dataFromV2,jdbcType=VARCHAR},
      </if>
      <if test="orgid != null">
        orgId = #{orgid,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="reviseStoreGroup != null">
        revise_store_group = #{reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsname != null">
        goodsname = #{goodsname,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="bak != null">
        bak = #{bak,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        goodsunit = #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="goodsspec != null">
        goodsspec = #{goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="jxCate1Name != null">
        jx_cate1_name = #{jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        classone_name = #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        classthree_name = #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        classfour_name = #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="isOtc != null">
        is_otc = #{isOtc,jdbcType=VARCHAR},
      </if>
      <if test="flagDisease != null">
        flag_disease = #{flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="grossprofit != null">
        grossprofit = #{grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="taotaiType != null">
        taotai_type = #{taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="stjb != null">
        stjb = #{stjb,jdbcType=VARCHAR},
      </if>
      <if test="specialattributesType != null">
        specialattributes_type = #{specialattributesType,jdbcType=VARCHAR},
      </if>
      <if test="inStockRate != null">
        in_stock_rate = #{inStockRate,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRate != null">
        in_sales_rate = #{inSalesRate,jdbcType=VARCHAR},
      </if>
      <if test="skuInt != null">
        sku_int = #{skuInt,jdbcType=VARCHAR},
      </if>
      <if test="phOrgBzFlag != null">
        ph_org_bz_flag = #{phOrgBzFlag,jdbcType=VARCHAR},
      </if>
      <if test="numCum90 != null">
        num_cum_90 = #{numCum90,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90 != null">
        amt_cum_90 = #{amtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum90 != null">
        profit_amt_cum_90 = #{profitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90 != null">
        profit_rate_90 = #{profitRate90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg90 != null">
        amt_rate_org_90 = #{amtRateOrg90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp90 != null">
        amt_rate_comp_90 = #{amtRateComp90,jdbcType=VARCHAR},
      </if>
      <if test="numCum180 != null">
        num_cum_180 = #{numCum180,jdbcType=VARCHAR},
      </if>
      <if test="amtCum180 != null">
        amt_cum_180 = #{amtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum180 != null">
        profit_amt_cum_180 = #{profitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitRate180 != null">
        profit_rate_180 = #{profitRate180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg180 != null">
        amt_rate_org_180 = #{amtRateOrg180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp180 != null">
        amt_rate_comp_180 = #{amtRateComp180,jdbcType=VARCHAR},
      </if>
      <if test="storeNum != null">
        store_num = #{storeNum,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum90 != null">
        revise_num_cum_90 = #{reviseNumCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum90 != null">
        revise_amt_cum_90 = #{reviseAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum90 != null">
        revise_profit_amt_cum_90 = #{reviseProfitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate90 != null">
        revise_profit_rate_90 = #{reviseProfitRate90,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum180 != null">
        revise_num_cum_180 = #{reviseNumCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum180 != null">
        revise_amt_cum_180 = #{reviseAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum180 != null">
        revise_profit_amt_cum_180 = #{reviseProfitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate180 != null">
        revise_profit_rate_180 = #{reviseProfitRate180,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup">
    update track_retult_top4level_storegroup
    set task_id = #{taskId,jdbcType=BIGINT},
      zone_new = #{zoneNew,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      data_from_v2 = #{dataFromV2,jdbcType=VARCHAR},
      orgId = #{orgid,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      revise_store_group = #{reviseStoreGroup,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=VARCHAR},
      goodsname = #{goodsname,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      bak = #{bak,jdbcType=VARCHAR},
      goodsunit = #{goodsunit,jdbcType=VARCHAR},
      goodsspec = #{goodsspec,jdbcType=VARCHAR},
      jx_cate1_name = #{jxCate1Name,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      classone_name = #{classoneName,jdbcType=VARCHAR},
      classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      classthree_name = #{classthreeName,jdbcType=VARCHAR},
      classfour_name = #{classfourName,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      is_otc = #{isOtc,jdbcType=VARCHAR},
      flag_disease = #{flagDisease,jdbcType=VARCHAR},
      grossprofit = #{grossprofit,jdbcType=VARCHAR},
      taotai_type = #{taotaiType,jdbcType=VARCHAR},
      stjb = #{stjb,jdbcType=VARCHAR},
      specialattributes_type = #{specialattributesType,jdbcType=VARCHAR},
      in_stock_rate = #{inStockRate,jdbcType=VARCHAR},
      in_sales_rate = #{inSalesRate,jdbcType=VARCHAR},
      sku_int = #{skuInt,jdbcType=VARCHAR},
      ph_org_bz_flag = #{phOrgBzFlag,jdbcType=VARCHAR},
      num_cum_90 = #{numCum90,jdbcType=VARCHAR},
      amt_cum_90 = #{amtCum90,jdbcType=VARCHAR},
      profit_amt_cum_90 = #{profitAmtCum90,jdbcType=VARCHAR},
      profit_rate_90 = #{profitRate90,jdbcType=VARCHAR},
      amt_rate_org_90 = #{amtRateOrg90,jdbcType=VARCHAR},
      amt_rate_comp_90 = #{amtRateComp90,jdbcType=VARCHAR},
      num_cum_180 = #{numCum180,jdbcType=VARCHAR},
      amt_cum_180 = #{amtCum180,jdbcType=VARCHAR},
      profit_amt_cum_180 = #{profitAmtCum180,jdbcType=VARCHAR},
      profit_rate_180 = #{profitRate180,jdbcType=VARCHAR},
      amt_rate_org_180 = #{amtRateOrg180,jdbcType=VARCHAR},
      amt_rate_comp_180 = #{amtRateComp180,jdbcType=VARCHAR},
      store_num = #{storeNum,jdbcType=VARCHAR},
      retail_price = #{retailPrice,jdbcType=VARCHAR},
      revise_num_cum_90 = #{reviseNumCum90,jdbcType=VARCHAR},
      revise_amt_cum_90 = #{reviseAmtCum90,jdbcType=VARCHAR},
      revise_profit_amt_cum_90 = #{reviseProfitAmtCum90,jdbcType=VARCHAR},
      revise_profit_rate_90 = #{reviseProfitRate90,jdbcType=VARCHAR},
      revise_num_cum_180 = #{reviseNumCum180,jdbcType=VARCHAR},
      revise_amt_cum_180 = #{reviseAmtCum180,jdbcType=VARCHAR},
      revise_profit_amt_cum_180 = #{reviseProfitAmtCum180,jdbcType=VARCHAR},
      revise_profit_rate_180 = #{reviseProfitRate180,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>