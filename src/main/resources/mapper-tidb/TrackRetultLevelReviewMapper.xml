<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackRetultLevelReviewMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultLevelReview">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="revise_store_group" jdbcType="VARCHAR" property="reviseStoreGroup" />
    <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
    <result column="org_cnt_v3" jdbcType="VARCHAR" property="orgCntV3" />
    <result column="sku_cnt" jdbcType="VARCHAR" property="skuCnt" />
    <result column="sku_cnt_jt" jdbcType="VARCHAR" property="skuCntJt" />
    <result column="sku_cnt_pt" jdbcType="VARCHAR" property="skuCntPt" />
    <result column="sku_cnt_qy" jdbcType="VARCHAR" property="skuCntQy" />
    <result column="sku_cnt_dxbb" jdbcType="VARCHAR" property="skuCntDxbb" />
    <result column="sku_cnt_dxxp" jdbcType="VARCHAR" property="skuCntDxxp" />
    <result column="sku_cnt_dd" jdbcType="VARCHAR" property="skuCntDd" />
    <result column="sku_cnt_jt_qy_dx" jdbcType="VARCHAR" property="skuCntJtQyDx" />
    <result column="sku_cnt_no_dd" jdbcType="VARCHAR" property="skuCntNoDd" />
    <result column="sku_cnt_xz" jdbcType="VARCHAR" property="skuCntXz" />
    <result column="sku_cnt_js" jdbcType="VARCHAR" property="skuCntJs" />
    <result column="sku_rate_top4" jdbcType="VARCHAR" property="skuRateTop4" />
    <result column="sku_rate_no_dd" jdbcType="VARCHAR" property="skuRateNoDd" />
    <result column="comp_cnt" jdbcType="VARCHAR" property="compCnt" />
    <result column="comp_cnt_jt" jdbcType="VARCHAR" property="compCntJt" />
    <result column="comp_cnt_pt" jdbcType="VARCHAR" property="compCntPt" />
    <result column="comp_cnt_qy" jdbcType="VARCHAR" property="compCntQy" />
    <result column="comp_cnt_dxbb" jdbcType="VARCHAR" property="compCntDxbb" />
    <result column="comp_cnt_dxxp" jdbcType="VARCHAR" property="compCntDxxp" />
    <result column="comp_cnt_dd" jdbcType="VARCHAR" property="compCntDd" />
    <result column="comp_cnt_jt_qy_dx" jdbcType="VARCHAR" property="compCntJtQyDx" />
    <result column="comp_cnt_no_dd" jdbcType="VARCHAR" property="compCntNoDd" />
    <result column="comp_cnt_xz" jdbcType="VARCHAR" property="compCntXz" />
    <result column="comp_cnt_js" jdbcType="VARCHAR" property="compCntJs" />
    <result column="comp_rate_top4" jdbcType="VARCHAR" property="compRateTop4" />
    <result column="comp_rate_no_dd" jdbcType="VARCHAR" property="compRateNoDd" />
    <result column="class_cnt" jdbcType="VARCHAR" property="classCnt" />
    <result column="class_cnt_jt" jdbcType="VARCHAR" property="classCntJt" />
    <result column="class_cnt_pt" jdbcType="VARCHAR" property="classCntPt" />
    <result column="class_cnt_qy" jdbcType="VARCHAR" property="classCntQy" />
    <result column="class_cnt_dxbb" jdbcType="VARCHAR" property="classCntDxbb" />
    <result column="class_cnt_dxxp" jdbcType="VARCHAR" property="classCntDxxp" />
    <result column="class_cnt_dd" jdbcType="VARCHAR" property="classCntDd" />
    <result column="class_cnt_jt_qy_dx" jdbcType="VARCHAR" property="classCntJtQyDx" />
    <result column="class_cnt_no_dd" jdbcType="VARCHAR" property="classCntNoDd" />
    <result column="class_cnt_xz" jdbcType="VARCHAR" property="classCntXz" />
    <result column="class_cnt_js" jdbcType="VARCHAR" property="classCntJs" />
    <result column="amt_cum_30" jdbcType="VARCHAR" property="amtCum30" />
    <result column="amt_cum_30_jt" jdbcType="VARCHAR" property="amtCum30Jt" />
    <result column="amt_cum_30_pt" jdbcType="VARCHAR" property="amtCum30Pt" />
    <result column="amt_cum_30_qy" jdbcType="VARCHAR" property="amtCum30Qy" />
    <result column="amt_cum_30_dxbb" jdbcType="VARCHAR" property="amtCum30Dxbb" />
    <result column="amt_cum_30_dxxp" jdbcType="VARCHAR" property="amtCum30Dxxp" />
    <result column="amt_cum_30_dd" jdbcType="VARCHAR" property="amtCum30Dd" />
    <result column="amt_cum_30_jt_qy_dx" jdbcType="VARCHAR" property="amtCum30JtQyDx" />
    <result column="amt_cum_30_no_dd" jdbcType="VARCHAR" property="amtCum30NoDd" />
    <result column="amt_cum_30_xz" jdbcType="VARCHAR" property="amtCum30Xz" />
    <result column="amt_cum_30_js" jdbcType="VARCHAR" property="amtCum30Js" />
    <result column="profit_cum_30" jdbcType="VARCHAR" property="profitCum30" />
    <result column="profit_cum_30_jt" jdbcType="VARCHAR" property="profitCum30Jt" />
    <result column="profit_cum_30_pt" jdbcType="VARCHAR" property="profitCum30Pt" />
    <result column="profit_cum_30_qy" jdbcType="VARCHAR" property="profitCum30Qy" />
    <result column="profit_cum_30_dxbb" jdbcType="VARCHAR" property="profitCum30Dxbb" />
    <result column="profit_cum_30_dxxp" jdbcType="VARCHAR" property="profitCum30Dxxp" />
    <result column="profit_cum_30_dd" jdbcType="VARCHAR" property="profitCum30Dd" />
    <result column="profit_cum_30_jt_qy_dx" jdbcType="VARCHAR" property="profitCum30JtQyDx" />
    <result column="profit_cum_30_no_dd" jdbcType="VARCHAR" property="profitCum30NoDd" />
    <result column="profit_cum_30_xz" jdbcType="VARCHAR" property="profitCum30Xz" />
    <result column="profit_cum_30_js" jdbcType="VARCHAR" property="profitCum30Js" />
    <result column="profit_cum_30_rate" jdbcType="VARCHAR" property="profitCum30Rate" />
    <result column="profit_cum_30_rate_jt" jdbcType="VARCHAR" property="profitCum30RateJt" />
    <result column="profit_cum_30_rate_pt" jdbcType="VARCHAR" property="profitCum30RatePt" />
    <result column="profit_cum_30_rate_qy" jdbcType="VARCHAR" property="profitCum30RateQy" />
    <result column="profit_cum_30_rate_dxbb" jdbcType="VARCHAR" property="profitCum30RateDxbb" />
    <result column="profit_cum_30_rate_dxxp" jdbcType="VARCHAR" property="profitCum30RateDxxp" />
    <result column="profit_cum_30_rate_dd" jdbcType="VARCHAR" property="profitCum30RateDd" />
    <result column="profit_cum_30_rate_jt_qy_dx" jdbcType="VARCHAR" property="profitCum30RateJtQyDx" />
    <result column="profit_cum_30_rate_no_dd" jdbcType="VARCHAR" property="profitCum30RateNoDd" />
    <result column="profit_cum_30_rate_xz" jdbcType="VARCHAR" property="profitCum30RateXz" />
    <result column="profit_cum_30_rate_js" jdbcType="VARCHAR" property="profitCum30RateJs" />
    <result column="amt_cum_30_month" jdbcType="VARCHAR" property="amtCum30Month" />
    <result column="amt_cum_30_month_nodtp" jdbcType="VARCHAR" property="amtCum30MonthNodtp" />
    <result column="amt_cum_30_month_nozy" jdbcType="VARCHAR" property="amtCum30MonthNozy" />
    <result column="amt_cum_30_rate" jdbcType="VARCHAR" property="amtCum30Rate" />
    <result column="amt_cum_30_rate_jt" jdbcType="VARCHAR" property="amtCum30RateJt" />
    <result column="amt_cum_30_rate_pt" jdbcType="VARCHAR" property="amtCum30RatePt" />
    <result column="amt_cum_30_rate_qt" jdbcType="VARCHAR" property="amtCum30RateQt" />
    <result column="amt_cum_30_rate_dxbb" jdbcType="VARCHAR" property="amtCum30RateDxbb" />
    <result column="amt_cum_30_rate_dxxp" jdbcType="VARCHAR" property="amtCum30RateDxxp" />
    <result column="amt_cum_30_rate_dd" jdbcType="VARCHAR" property="amtCum30RateDd" />
    <result column="amt_cum_30_rate_jt_qy_dx" jdbcType="VARCHAR" property="amtCum30RateJtQyDx" />
    <result column="amt_cum_30_rate_no_dd" jdbcType="VARCHAR" property="amtCum30RateNoDd" />
    <result column="amt_cum_30_rate_xz" jdbcType="VARCHAR" property="amtCum30RateXz" />
    <result column="amt_cum_30_rate_js" jdbcType="VARCHAR" property="amtCum30RateJs" />
    <result column="amt_cum_30_rate_nodtp" jdbcType="VARCHAR" property="amtCum30RateNodtp" />
    <result column="amt_cum_30_rate_jt_nodtp" jdbcType="VARCHAR" property="amtCum30RateJtNodtp" />
    <result column="amt_cum_30_rate_pt_nodtp" jdbcType="VARCHAR" property="amtCum30RatePtNodtp" />
    <result column="amt_cum_30_rate_qt_nodtp" jdbcType="VARCHAR" property="amtCum30RateQtNodtp" />
    <result column="amt_cum_30_rate_dxbb_nodtp" jdbcType="VARCHAR" property="amtCum30RateDxbbNodtp" />
    <result column="amt_cum_30_rate_dxxp_nodtp" jdbcType="VARCHAR" property="amtCum30RateDxxpNodtp" />
    <result column="amt_cum_30_rate_dd_nodtp" jdbcType="VARCHAR" property="amtCum30RateDdNodtp" />
    <result column="amt_cum_30_rate_jt_qy_dx_nodtp" jdbcType="VARCHAR" property="amtCum30RateJtQyDxNodtp" />
    <result column="amt_cum_30_rate_no_dd_nodtp" jdbcType="VARCHAR" property="amtCum30RateNoDdNodtp" />
    <result column="amt_cum_30_rate_xz_nodtp" jdbcType="VARCHAR" property="amtCum30RateXzNodtp" />
    <result column="amt_cum_30_rate_js_nodtp" jdbcType="VARCHAR" property="amtCum30RateJsNodtp" />
    <result column="amt_cum_30_rate_nozy" jdbcType="VARCHAR" property="amtCum30RateNozy" />
    <result column="amt_cum_30_rate_jt_nozy" jdbcType="VARCHAR" property="amtCum30RateJtNozy" />
    <result column="amt_cum_30_rate_pt_nozy" jdbcType="VARCHAR" property="amtCum30RatePtNozy" />
    <result column="amt_cum_30_rate_qt_nozy" jdbcType="VARCHAR" property="amtCum30RateQtNozy" />
    <result column="amt_cum_30_rate_dxbb_nozy" jdbcType="VARCHAR" property="amtCum30RateDxbbNozy" />
    <result column="amt_cum_30_rate_dxxp_nozy" jdbcType="VARCHAR" property="amtCum30RateDxxpNozy" />
    <result column="amt_cum_30_rate_dd_nozy" jdbcType="VARCHAR" property="amtCum30RateDdNozy" />
    <result column="amt_cum_30_rate_jt_qy_dx_nozy" jdbcType="VARCHAR" property="amtCum30RateJtQyDxNozy" />
    <result column="amt_cum_30_rate_no_dd_nozy" jdbcType="VARCHAR" property="amtCum30RateNoDdNozy" />
    <result column="amt_cum_30_rate_xz_nozy" jdbcType="VARCHAR" property="amtCum30RateXzNozy" />
    <result column="amt_cum_30_rate_js_nozy" jdbcType="VARCHAR" property="amtCum30RateJsNozy" />
    <result column="stock_cum_30" jdbcType="VARCHAR" property="stockCum30" />
    <result column="stock_cum_30_jt" jdbcType="VARCHAR" property="stockCum30Jt" />
    <result column="stock_cum_30_pt" jdbcType="VARCHAR" property="stockCum30Pt" />
    <result column="stock_cum_30_qy" jdbcType="VARCHAR" property="stockCum30Qy" />
    <result column="stock_cum_30_dxbb" jdbcType="VARCHAR" property="stockCum30Dxbb" />
    <result column="stock_cum_30_dxxp" jdbcType="VARCHAR" property="stockCum30Dxxp" />
    <result column="stock_cum_30_dd" jdbcType="VARCHAR" property="stockCum30Dd" />
    <result column="stock_cum_30_jt_qy_dx" jdbcType="VARCHAR" property="stockCum30JtQyDx" />
    <result column="stock_cum_30_no_dd" jdbcType="VARCHAR" property="stockCum30NoDd" />
    <result column="stock_cum_30_new" jdbcType="VARCHAR" property="stockCum30New" />
    <result column="stock_cum_30_js" jdbcType="VARCHAR" property="stockCum30Js" />
    <result column="avg_stock_cum_30_new" jdbcType="VARCHAR" property="avgStockCum30New" />
    <result column="avg_stock_cum_30_js" jdbcType="VARCHAR" property="avgStockCum30Js" />
    <result column="avg_sku_cnt_xz" jdbcType="VARCHAR" property="avgSkuCntXz" />
    <result column="avg_sku_cnt_js" jdbcType="VARCHAR" property="avgSkuCntJs" />
    <result column="avg_cnt" jdbcType="VARCHAR" property="avgCnt" />
    <result column="amt_cum_30_null" jdbcType="VARCHAR" property="amtCum30Null" />
    <result column="avg_amt_cum_30_null" jdbcType="VARCHAR" property="avgAmtCum30Null" />
    <result column="amt_cum_30_new" jdbcType="VARCHAR" property="amtCum30New" />
    <result column="avg_amt_cum_30_new" jdbcType="VARCHAR" property="avgAmtCum30New" />
    <result column="profit_cum_30_new" jdbcType="VARCHAR" property="profitCum30New" />
    <result column="profit_cum_30_rate_new" jdbcType="VARCHAR" property="profitCum30RateNew" />
    <result column="amt_cum_30_rate_new" jdbcType="VARCHAR" property="amtCum30RateNew" />
    <result column="amt_cum_30_rate_new_nodtp" jdbcType="VARCHAR" property="amtCum30RateNewNodtp" />
    <result column="amt_cum_30_rate_new_nozy" jdbcType="VARCHAR" property="amtCum30RateNewNozy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, zone_new, chain_name, city, revise_store_group, classone_name, org_cnt_v3,
    sku_cnt, sku_cnt_jt, sku_cnt_pt, sku_cnt_qy, sku_cnt_dxbb, sku_cnt_dxxp, sku_cnt_dd,
    sku_cnt_jt_qy_dx, sku_cnt_no_dd, sku_cnt_xz, sku_cnt_js, sku_rate_top4, sku_rate_no_dd,
    comp_cnt, comp_cnt_jt, comp_cnt_pt, comp_cnt_qy, comp_cnt_dxbb, comp_cnt_dxxp, comp_cnt_dd,
    comp_cnt_jt_qy_dx, comp_cnt_no_dd, comp_cnt_xz, comp_cnt_js, comp_rate_top4, comp_rate_no_dd,
    class_cnt, class_cnt_jt, class_cnt_pt, class_cnt_qy, class_cnt_dxbb, class_cnt_dxxp,
    class_cnt_dd, class_cnt_jt_qy_dx, class_cnt_no_dd, class_cnt_xz, class_cnt_js, amt_cum_30,
    amt_cum_30_jt, amt_cum_30_pt, amt_cum_30_qy, amt_cum_30_dxbb, amt_cum_30_dxxp, amt_cum_30_dd,
    amt_cum_30_jt_qy_dx, amt_cum_30_no_dd, amt_cum_30_xz, amt_cum_30_js, profit_cum_30,
    profit_cum_30_jt, profit_cum_30_pt, profit_cum_30_qy, profit_cum_30_dxbb, profit_cum_30_dxxp,
    profit_cum_30_dd, profit_cum_30_jt_qy_dx, profit_cum_30_no_dd, profit_cum_30_xz,
    profit_cum_30_js, profit_cum_30_rate, profit_cum_30_rate_jt, profit_cum_30_rate_pt,
    profit_cum_30_rate_qy, profit_cum_30_rate_dxbb, profit_cum_30_rate_dxxp, profit_cum_30_rate_dd,
    profit_cum_30_rate_jt_qy_dx, profit_cum_30_rate_no_dd, profit_cum_30_rate_xz, profit_cum_30_rate_js,
    amt_cum_30_month, amt_cum_30_month_nodtp, amt_cum_30_month_nozy, amt_cum_30_rate,
    amt_cum_30_rate_jt, amt_cum_30_rate_pt, amt_cum_30_rate_qt, amt_cum_30_rate_dxbb,
    amt_cum_30_rate_dxxp, amt_cum_30_rate_dd, amt_cum_30_rate_jt_qy_dx, amt_cum_30_rate_no_dd,
    amt_cum_30_rate_xz, amt_cum_30_rate_js, amt_cum_30_rate_nodtp, amt_cum_30_rate_jt_nodtp,
    amt_cum_30_rate_pt_nodtp, amt_cum_30_rate_qt_nodtp, amt_cum_30_rate_dxbb_nodtp, amt_cum_30_rate_dxxp_nodtp,
    amt_cum_30_rate_dd_nodtp, amt_cum_30_rate_jt_qy_dx_nodtp, amt_cum_30_rate_no_dd_nodtp,
    amt_cum_30_rate_xz_nodtp, amt_cum_30_rate_js_nodtp, amt_cum_30_rate_nozy, amt_cum_30_rate_jt_nozy,
    amt_cum_30_rate_pt_nozy, amt_cum_30_rate_qt_nozy, amt_cum_30_rate_dxbb_nozy, amt_cum_30_rate_dxxp_nozy,
    amt_cum_30_rate_dd_nozy, amt_cum_30_rate_jt_qy_dx_nozy, amt_cum_30_rate_no_dd_nozy,
    amt_cum_30_rate_xz_nozy, amt_cum_30_rate_js_nozy, stock_cum_30, stock_cum_30_jt,
    stock_cum_30_pt, stock_cum_30_qy, stock_cum_30_dxbb, stock_cum_30_dxxp, stock_cum_30_dd,
    stock_cum_30_jt_qy_dx, stock_cum_30_no_dd, stock_cum_30_new, stock_cum_30_js, avg_stock_cum_30_new,
    avg_stock_cum_30_js, avg_sku_cnt_xz, avg_sku_cnt_js, avg_cnt, amt_cum_30_null, avg_amt_cum_30_null,
    amt_cum_30_new, avg_amt_cum_30_new, profit_cum_30_new, profit_cum_30_rate_new, amt_cum_30_rate_new,
    amt_cum_30_rate_new_nodtp, amt_cum_30_rate_new_nozy, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReviewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_retult_level_review
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_retult_level_review
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_retult_level_review
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReviewExample">
    delete from track_retult_level_review
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReview" useGeneratedKeys="true">
    insert into track_retult_level_review (id, task_id, zone_new,
    chain_name, city, revise_store_group,
    classone_name, org_cnt_v3, sku_cnt,
    sku_cnt_jt, sku_cnt_pt, sku_cnt_qy,
    sku_cnt_dxbb, sku_cnt_dxxp, sku_cnt_dd,
    sku_cnt_jt_qy_dx, sku_cnt_no_dd, sku_cnt_xz,
    sku_cnt_js, sku_rate_top4, sku_rate_no_dd,
    comp_cnt, comp_cnt_jt, comp_cnt_pt,
    comp_cnt_qy, comp_cnt_dxbb, comp_cnt_dxxp,
    comp_cnt_dd, comp_cnt_jt_qy_dx, comp_cnt_no_dd,
    comp_cnt_xz, comp_cnt_js, comp_rate_top4,
    comp_rate_no_dd, class_cnt, class_cnt_jt,
    class_cnt_pt, class_cnt_qy, class_cnt_dxbb,
    class_cnt_dxxp, class_cnt_dd, class_cnt_jt_qy_dx,
    class_cnt_no_dd, class_cnt_xz, class_cnt_js,
    amt_cum_30, amt_cum_30_jt, amt_cum_30_pt,
    amt_cum_30_qy, amt_cum_30_dxbb, amt_cum_30_dxxp,
    amt_cum_30_dd, amt_cum_30_jt_qy_dx, amt_cum_30_no_dd,
    amt_cum_30_xz, amt_cum_30_js, profit_cum_30,
    profit_cum_30_jt, profit_cum_30_pt, profit_cum_30_qy,
    profit_cum_30_dxbb, profit_cum_30_dxxp, profit_cum_30_dd,
    profit_cum_30_jt_qy_dx, profit_cum_30_no_dd, profit_cum_30_xz,
    profit_cum_30_js, profit_cum_30_rate, profit_cum_30_rate_jt,
    profit_cum_30_rate_pt, profit_cum_30_rate_qy, profit_cum_30_rate_dxbb,
    profit_cum_30_rate_dxxp, profit_cum_30_rate_dd,
    profit_cum_30_rate_jt_qy_dx, profit_cum_30_rate_no_dd,
    profit_cum_30_rate_xz, profit_cum_30_rate_js, amt_cum_30_month,
    amt_cum_30_month_nodtp, amt_cum_30_month_nozy,
    amt_cum_30_rate, amt_cum_30_rate_jt, amt_cum_30_rate_pt,
    amt_cum_30_rate_qt, amt_cum_30_rate_dxbb, amt_cum_30_rate_dxxp,
    amt_cum_30_rate_dd, amt_cum_30_rate_jt_qy_dx, amt_cum_30_rate_no_dd,
    amt_cum_30_rate_xz, amt_cum_30_rate_js, amt_cum_30_rate_nodtp,
    amt_cum_30_rate_jt_nodtp, amt_cum_30_rate_pt_nodtp,
    amt_cum_30_rate_qt_nodtp, amt_cum_30_rate_dxbb_nodtp,
    amt_cum_30_rate_dxxp_nodtp, amt_cum_30_rate_dd_nodtp,
    amt_cum_30_rate_jt_qy_dx_nodtp, amt_cum_30_rate_no_dd_nodtp,
    amt_cum_30_rate_xz_nodtp, amt_cum_30_rate_js_nodtp,
    amt_cum_30_rate_nozy, amt_cum_30_rate_jt_nozy, amt_cum_30_rate_pt_nozy,
    amt_cum_30_rate_qt_nozy, amt_cum_30_rate_dxbb_nozy,
    amt_cum_30_rate_dxxp_nozy, amt_cum_30_rate_dd_nozy,
    amt_cum_30_rate_jt_qy_dx_nozy, amt_cum_30_rate_no_dd_nozy,
    amt_cum_30_rate_xz_nozy, amt_cum_30_rate_js_nozy,
    stock_cum_30, stock_cum_30_jt, stock_cum_30_pt,
    stock_cum_30_qy, stock_cum_30_dxbb, stock_cum_30_dxxp,
    stock_cum_30_dd, stock_cum_30_jt_qy_dx, stock_cum_30_no_dd,
    stock_cum_30_new, stock_cum_30_js, avg_stock_cum_30_new,
    avg_stock_cum_30_js, avg_sku_cnt_xz, avg_sku_cnt_js,
    avg_cnt, amt_cum_30_null, avg_amt_cum_30_null,
    amt_cum_30_new, avg_amt_cum_30_new, profit_cum_30_new,
    profit_cum_30_rate_new, amt_cum_30_rate_new, amt_cum_30_rate_new_nodtp,
    amt_cum_30_rate_new_nozy, gmt_create)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{zoneNew,jdbcType=VARCHAR},
    #{chainName,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{reviseStoreGroup,jdbcType=VARCHAR},
    #{classoneName,jdbcType=VARCHAR}, #{orgCntV3,jdbcType=VARCHAR}, #{skuCnt,jdbcType=VARCHAR},
    #{skuCntJt,jdbcType=VARCHAR}, #{skuCntPt,jdbcType=VARCHAR}, #{skuCntQy,jdbcType=VARCHAR},
    #{skuCntDxbb,jdbcType=VARCHAR}, #{skuCntDxxp,jdbcType=VARCHAR}, #{skuCntDd,jdbcType=VARCHAR},
    #{skuCntJtQyDx,jdbcType=VARCHAR}, #{skuCntNoDd,jdbcType=VARCHAR}, #{skuCntXz,jdbcType=VARCHAR},
    #{skuCntJs,jdbcType=VARCHAR}, #{skuRateTop4,jdbcType=VARCHAR}, #{skuRateNoDd,jdbcType=VARCHAR},
    #{compCnt,jdbcType=VARCHAR}, #{compCntJt,jdbcType=VARCHAR}, #{compCntPt,jdbcType=VARCHAR},
    #{compCntQy,jdbcType=VARCHAR}, #{compCntDxbb,jdbcType=VARCHAR}, #{compCntDxxp,jdbcType=VARCHAR},
    #{compCntDd,jdbcType=VARCHAR}, #{compCntJtQyDx,jdbcType=VARCHAR}, #{compCntNoDd,jdbcType=VARCHAR},
    #{compCntXz,jdbcType=VARCHAR}, #{compCntJs,jdbcType=VARCHAR}, #{compRateTop4,jdbcType=VARCHAR},
    #{compRateNoDd,jdbcType=VARCHAR}, #{classCnt,jdbcType=VARCHAR}, #{classCntJt,jdbcType=VARCHAR},
    #{classCntPt,jdbcType=VARCHAR}, #{classCntQy,jdbcType=VARCHAR}, #{classCntDxbb,jdbcType=VARCHAR},
    #{classCntDxxp,jdbcType=VARCHAR}, #{classCntDd,jdbcType=VARCHAR}, #{classCntJtQyDx,jdbcType=VARCHAR},
    #{classCntNoDd,jdbcType=VARCHAR}, #{classCntXz,jdbcType=VARCHAR}, #{classCntJs,jdbcType=VARCHAR},
    #{amtCum30,jdbcType=VARCHAR}, #{amtCum30Jt,jdbcType=VARCHAR}, #{amtCum30Pt,jdbcType=VARCHAR},
    #{amtCum30Qy,jdbcType=VARCHAR}, #{amtCum30Dxbb,jdbcType=VARCHAR}, #{amtCum30Dxxp,jdbcType=VARCHAR},
    #{amtCum30Dd,jdbcType=VARCHAR}, #{amtCum30JtQyDx,jdbcType=VARCHAR}, #{amtCum30NoDd,jdbcType=VARCHAR},
    #{amtCum30Xz,jdbcType=VARCHAR}, #{amtCum30Js,jdbcType=VARCHAR}, #{profitCum30,jdbcType=VARCHAR},
    #{profitCum30Jt,jdbcType=VARCHAR}, #{profitCum30Pt,jdbcType=VARCHAR}, #{profitCum30Qy,jdbcType=VARCHAR},
    #{profitCum30Dxbb,jdbcType=VARCHAR}, #{profitCum30Dxxp,jdbcType=VARCHAR}, #{profitCum30Dd,jdbcType=VARCHAR},
    #{profitCum30JtQyDx,jdbcType=VARCHAR}, #{profitCum30NoDd,jdbcType=VARCHAR}, #{profitCum30Xz,jdbcType=VARCHAR},
    #{profitCum30Js,jdbcType=VARCHAR}, #{profitCum30Rate,jdbcType=VARCHAR}, #{profitCum30RateJt,jdbcType=VARCHAR},
    #{profitCum30RatePt,jdbcType=VARCHAR}, #{profitCum30RateQy,jdbcType=VARCHAR}, #{profitCum30RateDxbb,jdbcType=VARCHAR},
    #{profitCum30RateDxxp,jdbcType=VARCHAR}, #{profitCum30RateDd,jdbcType=VARCHAR},
    #{profitCum30RateJtQyDx,jdbcType=VARCHAR}, #{profitCum30RateNoDd,jdbcType=VARCHAR},
    #{profitCum30RateXz,jdbcType=VARCHAR}, #{profitCum30RateJs,jdbcType=VARCHAR}, #{amtCum30Month,jdbcType=VARCHAR},
    #{amtCum30MonthNodtp,jdbcType=VARCHAR}, #{amtCum30MonthNozy,jdbcType=VARCHAR},
    #{amtCum30Rate,jdbcType=VARCHAR}, #{amtCum30RateJt,jdbcType=VARCHAR}, #{amtCum30RatePt,jdbcType=VARCHAR},
    #{amtCum30RateQt,jdbcType=VARCHAR}, #{amtCum30RateDxbb,jdbcType=VARCHAR}, #{amtCum30RateDxxp,jdbcType=VARCHAR},
    #{amtCum30RateDd,jdbcType=VARCHAR}, #{amtCum30RateJtQyDx,jdbcType=VARCHAR}, #{amtCum30RateNoDd,jdbcType=VARCHAR},
    #{amtCum30RateXz,jdbcType=VARCHAR}, #{amtCum30RateJs,jdbcType=VARCHAR}, #{amtCum30RateNodtp,jdbcType=VARCHAR},
    #{amtCum30RateJtNodtp,jdbcType=VARCHAR}, #{amtCum30RatePtNodtp,jdbcType=VARCHAR},
    #{amtCum30RateQtNodtp,jdbcType=VARCHAR}, #{amtCum30RateDxbbNodtp,jdbcType=VARCHAR},
    #{amtCum30RateDxxpNodtp,jdbcType=VARCHAR}, #{amtCum30RateDdNodtp,jdbcType=VARCHAR},
    #{amtCum30RateJtQyDxNodtp,jdbcType=VARCHAR}, #{amtCum30RateNoDdNodtp,jdbcType=VARCHAR},
    #{amtCum30RateXzNodtp,jdbcType=VARCHAR}, #{amtCum30RateJsNodtp,jdbcType=VARCHAR},
    #{amtCum30RateNozy,jdbcType=VARCHAR}, #{amtCum30RateJtNozy,jdbcType=VARCHAR}, #{amtCum30RatePtNozy,jdbcType=VARCHAR},
    #{amtCum30RateQtNozy,jdbcType=VARCHAR}, #{amtCum30RateDxbbNozy,jdbcType=VARCHAR},
    #{amtCum30RateDxxpNozy,jdbcType=VARCHAR}, #{amtCum30RateDdNozy,jdbcType=VARCHAR},
    #{amtCum30RateJtQyDxNozy,jdbcType=VARCHAR}, #{amtCum30RateNoDdNozy,jdbcType=VARCHAR},
    #{amtCum30RateXzNozy,jdbcType=VARCHAR}, #{amtCum30RateJsNozy,jdbcType=VARCHAR},
    #{stockCum30,jdbcType=VARCHAR}, #{stockCum30Jt,jdbcType=VARCHAR}, #{stockCum30Pt,jdbcType=VARCHAR},
    #{stockCum30Qy,jdbcType=VARCHAR}, #{stockCum30Dxbb,jdbcType=VARCHAR}, #{stockCum30Dxxp,jdbcType=VARCHAR},
    #{stockCum30Dd,jdbcType=VARCHAR}, #{stockCum30JtQyDx,jdbcType=VARCHAR}, #{stockCum30NoDd,jdbcType=VARCHAR},
    #{stockCum30New,jdbcType=VARCHAR}, #{stockCum30Js,jdbcType=VARCHAR}, #{avgStockCum30New,jdbcType=VARCHAR},
    #{avgStockCum30Js,jdbcType=VARCHAR}, #{avgSkuCntXz,jdbcType=VARCHAR}, #{avgSkuCntJs,jdbcType=VARCHAR},
    #{avgCnt,jdbcType=VARCHAR}, #{amtCum30Null,jdbcType=VARCHAR}, #{avgAmtCum30Null,jdbcType=VARCHAR},
    #{amtCum30New,jdbcType=VARCHAR}, #{avgAmtCum30New,jdbcType=VARCHAR}, #{profitCum30New,jdbcType=VARCHAR},
    #{profitCum30RateNew,jdbcType=VARCHAR}, #{amtCum30RateNew,jdbcType=VARCHAR}, #{amtCum30RateNewNodtp,jdbcType=VARCHAR},
    #{amtCum30RateNewNozy,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReview" useGeneratedKeys="true">
    insert into track_retult_level_review
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="zoneNew != null">
        zone_new,
      </if>
      <if test="chainName != null">
        chain_name,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="reviseStoreGroup != null">
        revise_store_group,
      </if>
      <if test="classoneName != null">
        classone_name,
      </if>
      <if test="orgCntV3 != null">
        org_cnt_v3,
      </if>
      <if test="skuCnt != null">
        sku_cnt,
      </if>
      <if test="skuCntJt != null">
        sku_cnt_jt,
      </if>
      <if test="skuCntPt != null">
        sku_cnt_pt,
      </if>
      <if test="skuCntQy != null">
        sku_cnt_qy,
      </if>
      <if test="skuCntDxbb != null">
        sku_cnt_dxbb,
      </if>
      <if test="skuCntDxxp != null">
        sku_cnt_dxxp,
      </if>
      <if test="skuCntDd != null">
        sku_cnt_dd,
      </if>
      <if test="skuCntJtQyDx != null">
        sku_cnt_jt_qy_dx,
      </if>
      <if test="skuCntNoDd != null">
        sku_cnt_no_dd,
      </if>
      <if test="skuCntXz != null">
        sku_cnt_xz,
      </if>
      <if test="skuCntJs != null">
        sku_cnt_js,
      </if>
      <if test="skuRateTop4 != null">
        sku_rate_top4,
      </if>
      <if test="skuRateNoDd != null">
        sku_rate_no_dd,
      </if>
      <if test="compCnt != null">
        comp_cnt,
      </if>
      <if test="compCntJt != null">
        comp_cnt_jt,
      </if>
      <if test="compCntPt != null">
        comp_cnt_pt,
      </if>
      <if test="compCntQy != null">
        comp_cnt_qy,
      </if>
      <if test="compCntDxbb != null">
        comp_cnt_dxbb,
      </if>
      <if test="compCntDxxp != null">
        comp_cnt_dxxp,
      </if>
      <if test="compCntDd != null">
        comp_cnt_dd,
      </if>
      <if test="compCntJtQyDx != null">
        comp_cnt_jt_qy_dx,
      </if>
      <if test="compCntNoDd != null">
        comp_cnt_no_dd,
      </if>
      <if test="compCntXz != null">
        comp_cnt_xz,
      </if>
      <if test="compCntJs != null">
        comp_cnt_js,
      </if>
      <if test="compRateTop4 != null">
        comp_rate_top4,
      </if>
      <if test="compRateNoDd != null">
        comp_rate_no_dd,
      </if>
      <if test="classCnt != null">
        class_cnt,
      </if>
      <if test="classCntJt != null">
        class_cnt_jt,
      </if>
      <if test="classCntPt != null">
        class_cnt_pt,
      </if>
      <if test="classCntQy != null">
        class_cnt_qy,
      </if>
      <if test="classCntDxbb != null">
        class_cnt_dxbb,
      </if>
      <if test="classCntDxxp != null">
        class_cnt_dxxp,
      </if>
      <if test="classCntDd != null">
        class_cnt_dd,
      </if>
      <if test="classCntJtQyDx != null">
        class_cnt_jt_qy_dx,
      </if>
      <if test="classCntNoDd != null">
        class_cnt_no_dd,
      </if>
      <if test="classCntXz != null">
        class_cnt_xz,
      </if>
      <if test="classCntJs != null">
        class_cnt_js,
      </if>
      <if test="amtCum30 != null">
        amt_cum_30,
      </if>
      <if test="amtCum30Jt != null">
        amt_cum_30_jt,
      </if>
      <if test="amtCum30Pt != null">
        amt_cum_30_pt,
      </if>
      <if test="amtCum30Qy != null">
        amt_cum_30_qy,
      </if>
      <if test="amtCum30Dxbb != null">
        amt_cum_30_dxbb,
      </if>
      <if test="amtCum30Dxxp != null">
        amt_cum_30_dxxp,
      </if>
      <if test="amtCum30Dd != null">
        amt_cum_30_dd,
      </if>
      <if test="amtCum30JtQyDx != null">
        amt_cum_30_jt_qy_dx,
      </if>
      <if test="amtCum30NoDd != null">
        amt_cum_30_no_dd,
      </if>
      <if test="amtCum30Xz != null">
        amt_cum_30_xz,
      </if>
      <if test="amtCum30Js != null">
        amt_cum_30_js,
      </if>
      <if test="profitCum30 != null">
        profit_cum_30,
      </if>
      <if test="profitCum30Jt != null">
        profit_cum_30_jt,
      </if>
      <if test="profitCum30Pt != null">
        profit_cum_30_pt,
      </if>
      <if test="profitCum30Qy != null">
        profit_cum_30_qy,
      </if>
      <if test="profitCum30Dxbb != null">
        profit_cum_30_dxbb,
      </if>
      <if test="profitCum30Dxxp != null">
        profit_cum_30_dxxp,
      </if>
      <if test="profitCum30Dd != null">
        profit_cum_30_dd,
      </if>
      <if test="profitCum30JtQyDx != null">
        profit_cum_30_jt_qy_dx,
      </if>
      <if test="profitCum30NoDd != null">
        profit_cum_30_no_dd,
      </if>
      <if test="profitCum30Xz != null">
        profit_cum_30_xz,
      </if>
      <if test="profitCum30Js != null">
        profit_cum_30_js,
      </if>
      <if test="profitCum30Rate != null">
        profit_cum_30_rate,
      </if>
      <if test="profitCum30RateJt != null">
        profit_cum_30_rate_jt,
      </if>
      <if test="profitCum30RatePt != null">
        profit_cum_30_rate_pt,
      </if>
      <if test="profitCum30RateQy != null">
        profit_cum_30_rate_qy,
      </if>
      <if test="profitCum30RateDxbb != null">
        profit_cum_30_rate_dxbb,
      </if>
      <if test="profitCum30RateDxxp != null">
        profit_cum_30_rate_dxxp,
      </if>
      <if test="profitCum30RateDd != null">
        profit_cum_30_rate_dd,
      </if>
      <if test="profitCum30RateJtQyDx != null">
        profit_cum_30_rate_jt_qy_dx,
      </if>
      <if test="profitCum30RateNoDd != null">
        profit_cum_30_rate_no_dd,
      </if>
      <if test="profitCum30RateXz != null">
        profit_cum_30_rate_xz,
      </if>
      <if test="profitCum30RateJs != null">
        profit_cum_30_rate_js,
      </if>
      <if test="amtCum30Month != null">
        amt_cum_30_month,
      </if>
      <if test="amtCum30MonthNodtp != null">
        amt_cum_30_month_nodtp,
      </if>
      <if test="amtCum30MonthNozy != null">
        amt_cum_30_month_nozy,
      </if>
      <if test="amtCum30Rate != null">
        amt_cum_30_rate,
      </if>
      <if test="amtCum30RateJt != null">
        amt_cum_30_rate_jt,
      </if>
      <if test="amtCum30RatePt != null">
        amt_cum_30_rate_pt,
      </if>
      <if test="amtCum30RateQt != null">
        amt_cum_30_rate_qt,
      </if>
      <if test="amtCum30RateDxbb != null">
        amt_cum_30_rate_dxbb,
      </if>
      <if test="amtCum30RateDxxp != null">
        amt_cum_30_rate_dxxp,
      </if>
      <if test="amtCum30RateDd != null">
        amt_cum_30_rate_dd,
      </if>
      <if test="amtCum30RateJtQyDx != null">
        amt_cum_30_rate_jt_qy_dx,
      </if>
      <if test="amtCum30RateNoDd != null">
        amt_cum_30_rate_no_dd,
      </if>
      <if test="amtCum30RateXz != null">
        amt_cum_30_rate_xz,
      </if>
      <if test="amtCum30RateJs != null">
        amt_cum_30_rate_js,
      </if>
      <if test="amtCum30RateNodtp != null">
        amt_cum_30_rate_nodtp,
      </if>
      <if test="amtCum30RateJtNodtp != null">
        amt_cum_30_rate_jt_nodtp,
      </if>
      <if test="amtCum30RatePtNodtp != null">
        amt_cum_30_rate_pt_nodtp,
      </if>
      <if test="amtCum30RateQtNodtp != null">
        amt_cum_30_rate_qt_nodtp,
      </if>
      <if test="amtCum30RateDxbbNodtp != null">
        amt_cum_30_rate_dxbb_nodtp,
      </if>
      <if test="amtCum30RateDxxpNodtp != null">
        amt_cum_30_rate_dxxp_nodtp,
      </if>
      <if test="amtCum30RateDdNodtp != null">
        amt_cum_30_rate_dd_nodtp,
      </if>
      <if test="amtCum30RateJtQyDxNodtp != null">
        amt_cum_30_rate_jt_qy_dx_nodtp,
      </if>
      <if test="amtCum30RateNoDdNodtp != null">
        amt_cum_30_rate_no_dd_nodtp,
      </if>
      <if test="amtCum30RateXzNodtp != null">
        amt_cum_30_rate_xz_nodtp,
      </if>
      <if test="amtCum30RateJsNodtp != null">
        amt_cum_30_rate_js_nodtp,
      </if>
      <if test="amtCum30RateNozy != null">
        amt_cum_30_rate_nozy,
      </if>
      <if test="amtCum30RateJtNozy != null">
        amt_cum_30_rate_jt_nozy,
      </if>
      <if test="amtCum30RatePtNozy != null">
        amt_cum_30_rate_pt_nozy,
      </if>
      <if test="amtCum30RateQtNozy != null">
        amt_cum_30_rate_qt_nozy,
      </if>
      <if test="amtCum30RateDxbbNozy != null">
        amt_cum_30_rate_dxbb_nozy,
      </if>
      <if test="amtCum30RateDxxpNozy != null">
        amt_cum_30_rate_dxxp_nozy,
      </if>
      <if test="amtCum30RateDdNozy != null">
        amt_cum_30_rate_dd_nozy,
      </if>
      <if test="amtCum30RateJtQyDxNozy != null">
        amt_cum_30_rate_jt_qy_dx_nozy,
      </if>
      <if test="amtCum30RateNoDdNozy != null">
        amt_cum_30_rate_no_dd_nozy,
      </if>
      <if test="amtCum30RateXzNozy != null">
        amt_cum_30_rate_xz_nozy,
      </if>
      <if test="amtCum30RateJsNozy != null">
        amt_cum_30_rate_js_nozy,
      </if>
      <if test="stockCum30 != null">
        stock_cum_30,
      </if>
      <if test="stockCum30Jt != null">
        stock_cum_30_jt,
      </if>
      <if test="stockCum30Pt != null">
        stock_cum_30_pt,
      </if>
      <if test="stockCum30Qy != null">
        stock_cum_30_qy,
      </if>
      <if test="stockCum30Dxbb != null">
        stock_cum_30_dxbb,
      </if>
      <if test="stockCum30Dxxp != null">
        stock_cum_30_dxxp,
      </if>
      <if test="stockCum30Dd != null">
        stock_cum_30_dd,
      </if>
      <if test="stockCum30JtQyDx != null">
        stock_cum_30_jt_qy_dx,
      </if>
      <if test="stockCum30NoDd != null">
        stock_cum_30_no_dd,
      </if>
      <if test="stockCum30New != null">
        stock_cum_30_new,
      </if>
      <if test="stockCum30Js != null">
        stock_cum_30_js,
      </if>
      <if test="avgStockCum30New != null">
        avg_stock_cum_30_new,
      </if>
      <if test="avgStockCum30Js != null">
        avg_stock_cum_30_js,
      </if>
      <if test="avgSkuCntXz != null">
        avg_sku_cnt_xz,
      </if>
      <if test="avgSkuCntJs != null">
        avg_sku_cnt_js,
      </if>
      <if test="avgCnt != null">
        avg_cnt,
      </if>
      <if test="amtCum30Null != null">
        amt_cum_30_null,
      </if>
      <if test="avgAmtCum30Null != null">
        avg_amt_cum_30_null,
      </if>
      <if test="amtCum30New != null">
        amt_cum_30_new,
      </if>
      <if test="avgAmtCum30New != null">
        avg_amt_cum_30_new,
      </if>
      <if test="profitCum30New != null">
        profit_cum_30_new,
      </if>
      <if test="profitCum30RateNew != null">
        profit_cum_30_rate_new,
      </if>
      <if test="amtCum30RateNew != null">
        amt_cum_30_rate_new,
      </if>
      <if test="amtCum30RateNewNodtp != null">
        amt_cum_30_rate_new_nodtp,
      </if>
      <if test="amtCum30RateNewNozy != null">
        amt_cum_30_rate_new_nozy,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="chainName != null">
        #{chainName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="reviseStoreGroup != null">
        #{reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="orgCntV3 != null">
        #{orgCntV3,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt != null">
        #{skuCnt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntJt != null">
        #{skuCntJt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntPt != null">
        #{skuCntPt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntQy != null">
        #{skuCntQy,jdbcType=VARCHAR},
      </if>
      <if test="skuCntDxbb != null">
        #{skuCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="skuCntDxxp != null">
        #{skuCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="skuCntDd != null">
        #{skuCntDd,jdbcType=VARCHAR},
      </if>
      <if test="skuCntJtQyDx != null">
        #{skuCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNoDd != null">
        #{skuCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="skuCntXz != null">
        #{skuCntXz,jdbcType=VARCHAR},
      </if>
      <if test="skuCntJs != null">
        #{skuCntJs,jdbcType=VARCHAR},
      </if>
      <if test="skuRateTop4 != null">
        #{skuRateTop4,jdbcType=VARCHAR},
      </if>
      <if test="skuRateNoDd != null">
        #{skuRateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="compCnt != null">
        #{compCnt,jdbcType=VARCHAR},
      </if>
      <if test="compCntJt != null">
        #{compCntJt,jdbcType=VARCHAR},
      </if>
      <if test="compCntPt != null">
        #{compCntPt,jdbcType=VARCHAR},
      </if>
      <if test="compCntQy != null">
        #{compCntQy,jdbcType=VARCHAR},
      </if>
      <if test="compCntDxbb != null">
        #{compCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="compCntDxxp != null">
        #{compCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="compCntDd != null">
        #{compCntDd,jdbcType=VARCHAR},
      </if>
      <if test="compCntJtQyDx != null">
        #{compCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="compCntNoDd != null">
        #{compCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="compCntXz != null">
        #{compCntXz,jdbcType=VARCHAR},
      </if>
      <if test="compCntJs != null">
        #{compCntJs,jdbcType=VARCHAR},
      </if>
      <if test="compRateTop4 != null">
        #{compRateTop4,jdbcType=VARCHAR},
      </if>
      <if test="compRateNoDd != null">
        #{compRateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="classCnt != null">
        #{classCnt,jdbcType=VARCHAR},
      </if>
      <if test="classCntJt != null">
        #{classCntJt,jdbcType=VARCHAR},
      </if>
      <if test="classCntPt != null">
        #{classCntPt,jdbcType=VARCHAR},
      </if>
      <if test="classCntQy != null">
        #{classCntQy,jdbcType=VARCHAR},
      </if>
      <if test="classCntDxbb != null">
        #{classCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="classCntDxxp != null">
        #{classCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="classCntDd != null">
        #{classCntDd,jdbcType=VARCHAR},
      </if>
      <if test="classCntJtQyDx != null">
        #{classCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="classCntNoDd != null">
        #{classCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="classCntXz != null">
        #{classCntXz,jdbcType=VARCHAR},
      </if>
      <if test="classCntJs != null">
        #{classCntJs,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30 != null">
        #{amtCum30,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Jt != null">
        #{amtCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Pt != null">
        #{amtCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Qy != null">
        #{amtCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Dxbb != null">
        #{amtCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Dxxp != null">
        #{amtCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Dd != null">
        #{amtCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30JtQyDx != null">
        #{amtCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30NoDd != null">
        #{amtCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Xz != null">
        #{amtCum30Xz,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Js != null">
        #{amtCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30 != null">
        #{profitCum30,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Jt != null">
        #{profitCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Pt != null">
        #{profitCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Qy != null">
        #{profitCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Dxbb != null">
        #{profitCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Dxxp != null">
        #{profitCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Dd != null">
        #{profitCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30JtQyDx != null">
        #{profitCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30NoDd != null">
        #{profitCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Xz != null">
        #{profitCum30Xz,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Js != null">
        #{profitCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Rate != null">
        #{profitCum30Rate,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateJt != null">
        #{profitCum30RateJt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RatePt != null">
        #{profitCum30RatePt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateQy != null">
        #{profitCum30RateQy,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateDxbb != null">
        #{profitCum30RateDxbb,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateDxxp != null">
        #{profitCum30RateDxxp,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateDd != null">
        #{profitCum30RateDd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateJtQyDx != null">
        #{profitCum30RateJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateNoDd != null">
        #{profitCum30RateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateXz != null">
        #{profitCum30RateXz,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateJs != null">
        #{profitCum30RateJs,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Month != null">
        #{amtCum30Month,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30MonthNodtp != null">
        #{amtCum30MonthNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30MonthNozy != null">
        #{amtCum30MonthNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Rate != null">
        #{amtCum30Rate,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJt != null">
        #{amtCum30RateJt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RatePt != null">
        #{amtCum30RatePt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateQt != null">
        #{amtCum30RateQt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxbb != null">
        #{amtCum30RateDxbb,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxxp != null">
        #{amtCum30RateDxxp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDd != null">
        #{amtCum30RateDd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtQyDx != null">
        #{amtCum30RateJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNoDd != null">
        #{amtCum30RateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateXz != null">
        #{amtCum30RateXz,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJs != null">
        #{amtCum30RateJs,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNodtp != null">
        #{amtCum30RateNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtNodtp != null">
        #{amtCum30RateJtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RatePtNodtp != null">
        #{amtCum30RatePtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateQtNodtp != null">
        #{amtCum30RateQtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxbbNodtp != null">
        #{amtCum30RateDxbbNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxxpNodtp != null">
        #{amtCum30RateDxxpNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDdNodtp != null">
        #{amtCum30RateDdNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtQyDxNodtp != null">
        #{amtCum30RateJtQyDxNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNoDdNodtp != null">
        #{amtCum30RateNoDdNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateXzNodtp != null">
        #{amtCum30RateXzNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJsNodtp != null">
        #{amtCum30RateJsNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNozy != null">
        #{amtCum30RateNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtNozy != null">
        #{amtCum30RateJtNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RatePtNozy != null">
        #{amtCum30RatePtNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateQtNozy != null">
        #{amtCum30RateQtNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxbbNozy != null">
        #{amtCum30RateDxbbNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxxpNozy != null">
        #{amtCum30RateDxxpNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDdNozy != null">
        #{amtCum30RateDdNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtQyDxNozy != null">
        #{amtCum30RateJtQyDxNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNoDdNozy != null">
        #{amtCum30RateNoDdNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateXzNozy != null">
        #{amtCum30RateXzNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJsNozy != null">
        #{amtCum30RateJsNozy,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30 != null">
        #{stockCum30,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Jt != null">
        #{stockCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Pt != null">
        #{stockCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Qy != null">
        #{stockCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Dxbb != null">
        #{stockCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Dxxp != null">
        #{stockCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Dd != null">
        #{stockCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30JtQyDx != null">
        #{stockCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30NoDd != null">
        #{stockCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30New != null">
        #{stockCum30New,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Js != null">
        #{stockCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="avgStockCum30New != null">
        #{avgStockCum30New,jdbcType=VARCHAR},
      </if>
      <if test="avgStockCum30Js != null">
        #{avgStockCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntXz != null">
        #{avgSkuCntXz,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntJs != null">
        #{avgSkuCntJs,jdbcType=VARCHAR},
      </if>
      <if test="avgCnt != null">
        #{avgCnt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Null != null">
        #{amtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30Null != null">
        #{avgAmtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30New != null">
        #{amtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30New != null">
        #{avgAmtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30New != null">
        #{profitCum30New,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateNew != null">
        #{profitCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNew != null">
        #{amtCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNewNodtp != null">
        #{amtCum30RateNewNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNewNozy != null">
        #{amtCum30RateNewNozy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReviewExample" resultType="java.lang.Long">
    select count(*) from track_retult_level_review
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_retult_level_review
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.zoneNew != null">
        zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="record.chainName != null">
        chain_name = #{record.chainName,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseStoreGroup != null">
        revise_store_group = #{record.reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.classoneName != null">
        classone_name = #{record.classoneName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCntV3 != null">
        org_cnt_v3 = #{record.orgCntV3,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt != null">
        sku_cnt = #{record.skuCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntJt != null">
        sku_cnt_jt = #{record.skuCntJt,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntPt != null">
        sku_cnt_pt = #{record.skuCntPt,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntQy != null">
        sku_cnt_qy = #{record.skuCntQy,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntDxbb != null">
        sku_cnt_dxbb = #{record.skuCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntDxxp != null">
        sku_cnt_dxxp = #{record.skuCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntDd != null">
        sku_cnt_dd = #{record.skuCntDd,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntJtQyDx != null">
        sku_cnt_jt_qy_dx = #{record.skuCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNoDd != null">
        sku_cnt_no_dd = #{record.skuCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntXz != null">
        sku_cnt_xz = #{record.skuCntXz,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntJs != null">
        sku_cnt_js = #{record.skuCntJs,jdbcType=VARCHAR},
      </if>
      <if test="record.skuRateTop4 != null">
        sku_rate_top4 = #{record.skuRateTop4,jdbcType=VARCHAR},
      </if>
      <if test="record.skuRateNoDd != null">
        sku_rate_no_dd = #{record.skuRateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.compCnt != null">
        comp_cnt = #{record.compCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntJt != null">
        comp_cnt_jt = #{record.compCntJt,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntPt != null">
        comp_cnt_pt = #{record.compCntPt,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntQy != null">
        comp_cnt_qy = #{record.compCntQy,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntDxbb != null">
        comp_cnt_dxbb = #{record.compCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntDxxp != null">
        comp_cnt_dxxp = #{record.compCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntDd != null">
        comp_cnt_dd = #{record.compCntDd,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntJtQyDx != null">
        comp_cnt_jt_qy_dx = #{record.compCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntNoDd != null">
        comp_cnt_no_dd = #{record.compCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntXz != null">
        comp_cnt_xz = #{record.compCntXz,jdbcType=VARCHAR},
      </if>
      <if test="record.compCntJs != null">
        comp_cnt_js = #{record.compCntJs,jdbcType=VARCHAR},
      </if>
      <if test="record.compRateTop4 != null">
        comp_rate_top4 = #{record.compRateTop4,jdbcType=VARCHAR},
      </if>
      <if test="record.compRateNoDd != null">
        comp_rate_no_dd = #{record.compRateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.classCnt != null">
        class_cnt = #{record.classCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntJt != null">
        class_cnt_jt = #{record.classCntJt,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntPt != null">
        class_cnt_pt = #{record.classCntPt,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntQy != null">
        class_cnt_qy = #{record.classCntQy,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntDxbb != null">
        class_cnt_dxbb = #{record.classCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntDxxp != null">
        class_cnt_dxxp = #{record.classCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntDd != null">
        class_cnt_dd = #{record.classCntDd,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntJtQyDx != null">
        class_cnt_jt_qy_dx = #{record.classCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntNoDd != null">
        class_cnt_no_dd = #{record.classCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntXz != null">
        class_cnt_xz = #{record.classCntXz,jdbcType=VARCHAR},
      </if>
      <if test="record.classCntJs != null">
        class_cnt_js = #{record.classCntJs,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30 != null">
        amt_cum_30 = #{record.amtCum30,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Jt != null">
        amt_cum_30_jt = #{record.amtCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Pt != null">
        amt_cum_30_pt = #{record.amtCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Qy != null">
        amt_cum_30_qy = #{record.amtCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Dxbb != null">
        amt_cum_30_dxbb = #{record.amtCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Dxxp != null">
        amt_cum_30_dxxp = #{record.amtCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Dd != null">
        amt_cum_30_dd = #{record.amtCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30JtQyDx != null">
        amt_cum_30_jt_qy_dx = #{record.amtCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30NoDd != null">
        amt_cum_30_no_dd = #{record.amtCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Xz != null">
        amt_cum_30_xz = #{record.amtCum30Xz,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Js != null">
        amt_cum_30_js = #{record.amtCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30 != null">
        profit_cum_30 = #{record.profitCum30,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Jt != null">
        profit_cum_30_jt = #{record.profitCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Pt != null">
        profit_cum_30_pt = #{record.profitCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Qy != null">
        profit_cum_30_qy = #{record.profitCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Dxbb != null">
        profit_cum_30_dxbb = #{record.profitCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Dxxp != null">
        profit_cum_30_dxxp = #{record.profitCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Dd != null">
        profit_cum_30_dd = #{record.profitCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30JtQyDx != null">
        profit_cum_30_jt_qy_dx = #{record.profitCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30NoDd != null">
        profit_cum_30_no_dd = #{record.profitCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Xz != null">
        profit_cum_30_xz = #{record.profitCum30Xz,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Js != null">
        profit_cum_30_js = #{record.profitCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30Rate != null">
        profit_cum_30_rate = #{record.profitCum30Rate,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateJt != null">
        profit_cum_30_rate_jt = #{record.profitCum30RateJt,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RatePt != null">
        profit_cum_30_rate_pt = #{record.profitCum30RatePt,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateQy != null">
        profit_cum_30_rate_qy = #{record.profitCum30RateQy,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateDxbb != null">
        profit_cum_30_rate_dxbb = #{record.profitCum30RateDxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateDxxp != null">
        profit_cum_30_rate_dxxp = #{record.profitCum30RateDxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateDd != null">
        profit_cum_30_rate_dd = #{record.profitCum30RateDd,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateJtQyDx != null">
        profit_cum_30_rate_jt_qy_dx = #{record.profitCum30RateJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateNoDd != null">
        profit_cum_30_rate_no_dd = #{record.profitCum30RateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateXz != null">
        profit_cum_30_rate_xz = #{record.profitCum30RateXz,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateJs != null">
        profit_cum_30_rate_js = #{record.profitCum30RateJs,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Month != null">
        amt_cum_30_month = #{record.amtCum30Month,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30MonthNodtp != null">
        amt_cum_30_month_nodtp = #{record.amtCum30MonthNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30MonthNozy != null">
        amt_cum_30_month_nozy = #{record.amtCum30MonthNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Rate != null">
        amt_cum_30_rate = #{record.amtCum30Rate,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJt != null">
        amt_cum_30_rate_jt = #{record.amtCum30RateJt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RatePt != null">
        amt_cum_30_rate_pt = #{record.amtCum30RatePt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateQt != null">
        amt_cum_30_rate_qt = #{record.amtCum30RateQt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDxbb != null">
        amt_cum_30_rate_dxbb = #{record.amtCum30RateDxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDxxp != null">
        amt_cum_30_rate_dxxp = #{record.amtCum30RateDxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDd != null">
        amt_cum_30_rate_dd = #{record.amtCum30RateDd,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJtQyDx != null">
        amt_cum_30_rate_jt_qy_dx = #{record.amtCum30RateJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNoDd != null">
        amt_cum_30_rate_no_dd = #{record.amtCum30RateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateXz != null">
        amt_cum_30_rate_xz = #{record.amtCum30RateXz,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJs != null">
        amt_cum_30_rate_js = #{record.amtCum30RateJs,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNodtp != null">
        amt_cum_30_rate_nodtp = #{record.amtCum30RateNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJtNodtp != null">
        amt_cum_30_rate_jt_nodtp = #{record.amtCum30RateJtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RatePtNodtp != null">
        amt_cum_30_rate_pt_nodtp = #{record.amtCum30RatePtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateQtNodtp != null">
        amt_cum_30_rate_qt_nodtp = #{record.amtCum30RateQtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDxbbNodtp != null">
        amt_cum_30_rate_dxbb_nodtp = #{record.amtCum30RateDxbbNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDxxpNodtp != null">
        amt_cum_30_rate_dxxp_nodtp = #{record.amtCum30RateDxxpNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDdNodtp != null">
        amt_cum_30_rate_dd_nodtp = #{record.amtCum30RateDdNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJtQyDxNodtp != null">
        amt_cum_30_rate_jt_qy_dx_nodtp = #{record.amtCum30RateJtQyDxNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNoDdNodtp != null">
        amt_cum_30_rate_no_dd_nodtp = #{record.amtCum30RateNoDdNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateXzNodtp != null">
        amt_cum_30_rate_xz_nodtp = #{record.amtCum30RateXzNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJsNodtp != null">
        amt_cum_30_rate_js_nodtp = #{record.amtCum30RateJsNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNozy != null">
        amt_cum_30_rate_nozy = #{record.amtCum30RateNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJtNozy != null">
        amt_cum_30_rate_jt_nozy = #{record.amtCum30RateJtNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RatePtNozy != null">
        amt_cum_30_rate_pt_nozy = #{record.amtCum30RatePtNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateQtNozy != null">
        amt_cum_30_rate_qt_nozy = #{record.amtCum30RateQtNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDxbbNozy != null">
        amt_cum_30_rate_dxbb_nozy = #{record.amtCum30RateDxbbNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDxxpNozy != null">
        amt_cum_30_rate_dxxp_nozy = #{record.amtCum30RateDxxpNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateDdNozy != null">
        amt_cum_30_rate_dd_nozy = #{record.amtCum30RateDdNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJtQyDxNozy != null">
        amt_cum_30_rate_jt_qy_dx_nozy = #{record.amtCum30RateJtQyDxNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNoDdNozy != null">
        amt_cum_30_rate_no_dd_nozy = #{record.amtCum30RateNoDdNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateXzNozy != null">
        amt_cum_30_rate_xz_nozy = #{record.amtCum30RateXzNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateJsNozy != null">
        amt_cum_30_rate_js_nozy = #{record.amtCum30RateJsNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30 != null">
        stock_cum_30 = #{record.stockCum30,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Jt != null">
        stock_cum_30_jt = #{record.stockCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Pt != null">
        stock_cum_30_pt = #{record.stockCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Qy != null">
        stock_cum_30_qy = #{record.stockCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Dxbb != null">
        stock_cum_30_dxbb = #{record.stockCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Dxxp != null">
        stock_cum_30_dxxp = #{record.stockCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Dd != null">
        stock_cum_30_dd = #{record.stockCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30JtQyDx != null">
        stock_cum_30_jt_qy_dx = #{record.stockCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30NoDd != null">
        stock_cum_30_no_dd = #{record.stockCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30New != null">
        stock_cum_30_new = #{record.stockCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.stockCum30Js != null">
        stock_cum_30_js = #{record.stockCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="record.avgStockCum30New != null">
        avg_stock_cum_30_new = #{record.avgStockCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.avgStockCum30Js != null">
        avg_stock_cum_30_js = #{record.avgStockCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntXz != null">
        avg_sku_cnt_xz = #{record.avgSkuCntXz,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntJs != null">
        avg_sku_cnt_js = #{record.avgSkuCntJs,jdbcType=VARCHAR},
      </if>
      <if test="record.avgCnt != null">
        avg_cnt = #{record.avgCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Null != null">
        amt_cum_30_null = #{record.amtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="record.avgAmtCum30Null != null">
        avg_amt_cum_30_null = #{record.avgAmtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30New != null">
        amt_cum_30_new = #{record.amtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.avgAmtCum30New != null">
        avg_amt_cum_30_new = #{record.avgAmtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30New != null">
        profit_cum_30_new = #{record.profitCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateNew != null">
        profit_cum_30_rate_new = #{record.profitCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNew != null">
        amt_cum_30_rate_new = #{record.amtCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNewNodtp != null">
        amt_cum_30_rate_new_nodtp = #{record.amtCum30RateNewNodtp,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNewNozy != null">
        amt_cum_30_rate_new_nozy = #{record.amtCum30RateNewNozy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_retult_level_review
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      chain_name = #{record.chainName,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      revise_store_group = #{record.reviseStoreGroup,jdbcType=VARCHAR},
      classone_name = #{record.classoneName,jdbcType=VARCHAR},
      org_cnt_v3 = #{record.orgCntV3,jdbcType=VARCHAR},
      sku_cnt = #{record.skuCnt,jdbcType=VARCHAR},
      sku_cnt_jt = #{record.skuCntJt,jdbcType=VARCHAR},
      sku_cnt_pt = #{record.skuCntPt,jdbcType=VARCHAR},
      sku_cnt_qy = #{record.skuCntQy,jdbcType=VARCHAR},
      sku_cnt_dxbb = #{record.skuCntDxbb,jdbcType=VARCHAR},
      sku_cnt_dxxp = #{record.skuCntDxxp,jdbcType=VARCHAR},
      sku_cnt_dd = #{record.skuCntDd,jdbcType=VARCHAR},
      sku_cnt_jt_qy_dx = #{record.skuCntJtQyDx,jdbcType=VARCHAR},
      sku_cnt_no_dd = #{record.skuCntNoDd,jdbcType=VARCHAR},
      sku_cnt_xz = #{record.skuCntXz,jdbcType=VARCHAR},
      sku_cnt_js = #{record.skuCntJs,jdbcType=VARCHAR},
      sku_rate_top4 = #{record.skuRateTop4,jdbcType=VARCHAR},
      sku_rate_no_dd = #{record.skuRateNoDd,jdbcType=VARCHAR},
      comp_cnt = #{record.compCnt,jdbcType=VARCHAR},
      comp_cnt_jt = #{record.compCntJt,jdbcType=VARCHAR},
      comp_cnt_pt = #{record.compCntPt,jdbcType=VARCHAR},
      comp_cnt_qy = #{record.compCntQy,jdbcType=VARCHAR},
      comp_cnt_dxbb = #{record.compCntDxbb,jdbcType=VARCHAR},
      comp_cnt_dxxp = #{record.compCntDxxp,jdbcType=VARCHAR},
      comp_cnt_dd = #{record.compCntDd,jdbcType=VARCHAR},
      comp_cnt_jt_qy_dx = #{record.compCntJtQyDx,jdbcType=VARCHAR},
      comp_cnt_no_dd = #{record.compCntNoDd,jdbcType=VARCHAR},
      comp_cnt_xz = #{record.compCntXz,jdbcType=VARCHAR},
      comp_cnt_js = #{record.compCntJs,jdbcType=VARCHAR},
      comp_rate_top4 = #{record.compRateTop4,jdbcType=VARCHAR},
      comp_rate_no_dd = #{record.compRateNoDd,jdbcType=VARCHAR},
      class_cnt = #{record.classCnt,jdbcType=VARCHAR},
      class_cnt_jt = #{record.classCntJt,jdbcType=VARCHAR},
      class_cnt_pt = #{record.classCntPt,jdbcType=VARCHAR},
      class_cnt_qy = #{record.classCntQy,jdbcType=VARCHAR},
      class_cnt_dxbb = #{record.classCntDxbb,jdbcType=VARCHAR},
      class_cnt_dxxp = #{record.classCntDxxp,jdbcType=VARCHAR},
      class_cnt_dd = #{record.classCntDd,jdbcType=VARCHAR},
      class_cnt_jt_qy_dx = #{record.classCntJtQyDx,jdbcType=VARCHAR},
      class_cnt_no_dd = #{record.classCntNoDd,jdbcType=VARCHAR},
      class_cnt_xz = #{record.classCntXz,jdbcType=VARCHAR},
      class_cnt_js = #{record.classCntJs,jdbcType=VARCHAR},
      amt_cum_30 = #{record.amtCum30,jdbcType=VARCHAR},
      amt_cum_30_jt = #{record.amtCum30Jt,jdbcType=VARCHAR},
      amt_cum_30_pt = #{record.amtCum30Pt,jdbcType=VARCHAR},
      amt_cum_30_qy = #{record.amtCum30Qy,jdbcType=VARCHAR},
      amt_cum_30_dxbb = #{record.amtCum30Dxbb,jdbcType=VARCHAR},
      amt_cum_30_dxxp = #{record.amtCum30Dxxp,jdbcType=VARCHAR},
      amt_cum_30_dd = #{record.amtCum30Dd,jdbcType=VARCHAR},
      amt_cum_30_jt_qy_dx = #{record.amtCum30JtQyDx,jdbcType=VARCHAR},
      amt_cum_30_no_dd = #{record.amtCum30NoDd,jdbcType=VARCHAR},
      amt_cum_30_xz = #{record.amtCum30Xz,jdbcType=VARCHAR},
      amt_cum_30_js = #{record.amtCum30Js,jdbcType=VARCHAR},
      profit_cum_30 = #{record.profitCum30,jdbcType=VARCHAR},
      profit_cum_30_jt = #{record.profitCum30Jt,jdbcType=VARCHAR},
      profit_cum_30_pt = #{record.profitCum30Pt,jdbcType=VARCHAR},
      profit_cum_30_qy = #{record.profitCum30Qy,jdbcType=VARCHAR},
      profit_cum_30_dxbb = #{record.profitCum30Dxbb,jdbcType=VARCHAR},
      profit_cum_30_dxxp = #{record.profitCum30Dxxp,jdbcType=VARCHAR},
      profit_cum_30_dd = #{record.profitCum30Dd,jdbcType=VARCHAR},
      profit_cum_30_jt_qy_dx = #{record.profitCum30JtQyDx,jdbcType=VARCHAR},
      profit_cum_30_no_dd = #{record.profitCum30NoDd,jdbcType=VARCHAR},
      profit_cum_30_xz = #{record.profitCum30Xz,jdbcType=VARCHAR},
      profit_cum_30_js = #{record.profitCum30Js,jdbcType=VARCHAR},
      profit_cum_30_rate = #{record.profitCum30Rate,jdbcType=VARCHAR},
      profit_cum_30_rate_jt = #{record.profitCum30RateJt,jdbcType=VARCHAR},
      profit_cum_30_rate_pt = #{record.profitCum30RatePt,jdbcType=VARCHAR},
      profit_cum_30_rate_qy = #{record.profitCum30RateQy,jdbcType=VARCHAR},
      profit_cum_30_rate_dxbb = #{record.profitCum30RateDxbb,jdbcType=VARCHAR},
      profit_cum_30_rate_dxxp = #{record.profitCum30RateDxxp,jdbcType=VARCHAR},
      profit_cum_30_rate_dd = #{record.profitCum30RateDd,jdbcType=VARCHAR},
      profit_cum_30_rate_jt_qy_dx = #{record.profitCum30RateJtQyDx,jdbcType=VARCHAR},
      profit_cum_30_rate_no_dd = #{record.profitCum30RateNoDd,jdbcType=VARCHAR},
      profit_cum_30_rate_xz = #{record.profitCum30RateXz,jdbcType=VARCHAR},
      profit_cum_30_rate_js = #{record.profitCum30RateJs,jdbcType=VARCHAR},
      amt_cum_30_month = #{record.amtCum30Month,jdbcType=VARCHAR},
      amt_cum_30_month_nodtp = #{record.amtCum30MonthNodtp,jdbcType=VARCHAR},
      amt_cum_30_month_nozy = #{record.amtCum30MonthNozy,jdbcType=VARCHAR},
      amt_cum_30_rate = #{record.amtCum30Rate,jdbcType=VARCHAR},
      amt_cum_30_rate_jt = #{record.amtCum30RateJt,jdbcType=VARCHAR},
      amt_cum_30_rate_pt = #{record.amtCum30RatePt,jdbcType=VARCHAR},
      amt_cum_30_rate_qt = #{record.amtCum30RateQt,jdbcType=VARCHAR},
      amt_cum_30_rate_dxbb = #{record.amtCum30RateDxbb,jdbcType=VARCHAR},
      amt_cum_30_rate_dxxp = #{record.amtCum30RateDxxp,jdbcType=VARCHAR},
      amt_cum_30_rate_dd = #{record.amtCum30RateDd,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_qy_dx = #{record.amtCum30RateJtQyDx,jdbcType=VARCHAR},
      amt_cum_30_rate_no_dd = #{record.amtCum30RateNoDd,jdbcType=VARCHAR},
      amt_cum_30_rate_xz = #{record.amtCum30RateXz,jdbcType=VARCHAR},
      amt_cum_30_rate_js = #{record.amtCum30RateJs,jdbcType=VARCHAR},
      amt_cum_30_rate_nodtp = #{record.amtCum30RateNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_nodtp = #{record.amtCum30RateJtNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_pt_nodtp = #{record.amtCum30RatePtNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_qt_nodtp = #{record.amtCum30RateQtNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_dxbb_nodtp = #{record.amtCum30RateDxbbNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_dxxp_nodtp = #{record.amtCum30RateDxxpNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_dd_nodtp = #{record.amtCum30RateDdNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_qy_dx_nodtp = #{record.amtCum30RateJtQyDxNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_no_dd_nodtp = #{record.amtCum30RateNoDdNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_xz_nodtp = #{record.amtCum30RateXzNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_js_nodtp = #{record.amtCum30RateJsNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_nozy = #{record.amtCum30RateNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_nozy = #{record.amtCum30RateJtNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_pt_nozy = #{record.amtCum30RatePtNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_qt_nozy = #{record.amtCum30RateQtNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_dxbb_nozy = #{record.amtCum30RateDxbbNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_dxxp_nozy = #{record.amtCum30RateDxxpNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_dd_nozy = #{record.amtCum30RateDdNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_qy_dx_nozy = #{record.amtCum30RateJtQyDxNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_no_dd_nozy = #{record.amtCum30RateNoDdNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_xz_nozy = #{record.amtCum30RateXzNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_js_nozy = #{record.amtCum30RateJsNozy,jdbcType=VARCHAR},
      stock_cum_30 = #{record.stockCum30,jdbcType=VARCHAR},
      stock_cum_30_jt = #{record.stockCum30Jt,jdbcType=VARCHAR},
      stock_cum_30_pt = #{record.stockCum30Pt,jdbcType=VARCHAR},
      stock_cum_30_qy = #{record.stockCum30Qy,jdbcType=VARCHAR},
      stock_cum_30_dxbb = #{record.stockCum30Dxbb,jdbcType=VARCHAR},
      stock_cum_30_dxxp = #{record.stockCum30Dxxp,jdbcType=VARCHAR},
      stock_cum_30_dd = #{record.stockCum30Dd,jdbcType=VARCHAR},
      stock_cum_30_jt_qy_dx = #{record.stockCum30JtQyDx,jdbcType=VARCHAR},
      stock_cum_30_no_dd = #{record.stockCum30NoDd,jdbcType=VARCHAR},
      stock_cum_30_new = #{record.stockCum30New,jdbcType=VARCHAR},
      stock_cum_30_js = #{record.stockCum30Js,jdbcType=VARCHAR},
      avg_stock_cum_30_new = #{record.avgStockCum30New,jdbcType=VARCHAR},
      avg_stock_cum_30_js = #{record.avgStockCum30Js,jdbcType=VARCHAR},
      avg_sku_cnt_xz = #{record.avgSkuCntXz,jdbcType=VARCHAR},
      avg_sku_cnt_js = #{record.avgSkuCntJs,jdbcType=VARCHAR},
      avg_cnt = #{record.avgCnt,jdbcType=VARCHAR},
      amt_cum_30_null = #{record.amtCum30Null,jdbcType=VARCHAR},
      avg_amt_cum_30_null = #{record.avgAmtCum30Null,jdbcType=VARCHAR},
      amt_cum_30_new = #{record.amtCum30New,jdbcType=VARCHAR},
      avg_amt_cum_30_new = #{record.avgAmtCum30New,jdbcType=VARCHAR},
      profit_cum_30_new = #{record.profitCum30New,jdbcType=VARCHAR},
      profit_cum_30_rate_new = #{record.profitCum30RateNew,jdbcType=VARCHAR},
      amt_cum_30_rate_new = #{record.amtCum30RateNew,jdbcType=VARCHAR},
      amt_cum_30_rate_new_nodtp = #{record.amtCum30RateNewNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_new_nozy = #{record.amtCum30RateNewNozy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReview">
    update track_retult_level_review
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        zone_new = #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="chainName != null">
        chain_name = #{chainName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="reviseStoreGroup != null">
        revise_store_group = #{reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        classone_name = #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="orgCntV3 != null">
        org_cnt_v3 = #{orgCntV3,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt != null">
        sku_cnt = #{skuCnt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntJt != null">
        sku_cnt_jt = #{skuCntJt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntPt != null">
        sku_cnt_pt = #{skuCntPt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntQy != null">
        sku_cnt_qy = #{skuCntQy,jdbcType=VARCHAR},
      </if>
      <if test="skuCntDxbb != null">
        sku_cnt_dxbb = #{skuCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="skuCntDxxp != null">
        sku_cnt_dxxp = #{skuCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="skuCntDd != null">
        sku_cnt_dd = #{skuCntDd,jdbcType=VARCHAR},
      </if>
      <if test="skuCntJtQyDx != null">
        sku_cnt_jt_qy_dx = #{skuCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNoDd != null">
        sku_cnt_no_dd = #{skuCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="skuCntXz != null">
        sku_cnt_xz = #{skuCntXz,jdbcType=VARCHAR},
      </if>
      <if test="skuCntJs != null">
        sku_cnt_js = #{skuCntJs,jdbcType=VARCHAR},
      </if>
      <if test="skuRateTop4 != null">
        sku_rate_top4 = #{skuRateTop4,jdbcType=VARCHAR},
      </if>
      <if test="skuRateNoDd != null">
        sku_rate_no_dd = #{skuRateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="compCnt != null">
        comp_cnt = #{compCnt,jdbcType=VARCHAR},
      </if>
      <if test="compCntJt != null">
        comp_cnt_jt = #{compCntJt,jdbcType=VARCHAR},
      </if>
      <if test="compCntPt != null">
        comp_cnt_pt = #{compCntPt,jdbcType=VARCHAR},
      </if>
      <if test="compCntQy != null">
        comp_cnt_qy = #{compCntQy,jdbcType=VARCHAR},
      </if>
      <if test="compCntDxbb != null">
        comp_cnt_dxbb = #{compCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="compCntDxxp != null">
        comp_cnt_dxxp = #{compCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="compCntDd != null">
        comp_cnt_dd = #{compCntDd,jdbcType=VARCHAR},
      </if>
      <if test="compCntJtQyDx != null">
        comp_cnt_jt_qy_dx = #{compCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="compCntNoDd != null">
        comp_cnt_no_dd = #{compCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="compCntXz != null">
        comp_cnt_xz = #{compCntXz,jdbcType=VARCHAR},
      </if>
      <if test="compCntJs != null">
        comp_cnt_js = #{compCntJs,jdbcType=VARCHAR},
      </if>
      <if test="compRateTop4 != null">
        comp_rate_top4 = #{compRateTop4,jdbcType=VARCHAR},
      </if>
      <if test="compRateNoDd != null">
        comp_rate_no_dd = #{compRateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="classCnt != null">
        class_cnt = #{classCnt,jdbcType=VARCHAR},
      </if>
      <if test="classCntJt != null">
        class_cnt_jt = #{classCntJt,jdbcType=VARCHAR},
      </if>
      <if test="classCntPt != null">
        class_cnt_pt = #{classCntPt,jdbcType=VARCHAR},
      </if>
      <if test="classCntQy != null">
        class_cnt_qy = #{classCntQy,jdbcType=VARCHAR},
      </if>
      <if test="classCntDxbb != null">
        class_cnt_dxbb = #{classCntDxbb,jdbcType=VARCHAR},
      </if>
      <if test="classCntDxxp != null">
        class_cnt_dxxp = #{classCntDxxp,jdbcType=VARCHAR},
      </if>
      <if test="classCntDd != null">
        class_cnt_dd = #{classCntDd,jdbcType=VARCHAR},
      </if>
      <if test="classCntJtQyDx != null">
        class_cnt_jt_qy_dx = #{classCntJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="classCntNoDd != null">
        class_cnt_no_dd = #{classCntNoDd,jdbcType=VARCHAR},
      </if>
      <if test="classCntXz != null">
        class_cnt_xz = #{classCntXz,jdbcType=VARCHAR},
      </if>
      <if test="classCntJs != null">
        class_cnt_js = #{classCntJs,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30 != null">
        amt_cum_30 = #{amtCum30,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Jt != null">
        amt_cum_30_jt = #{amtCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Pt != null">
        amt_cum_30_pt = #{amtCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Qy != null">
        amt_cum_30_qy = #{amtCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Dxbb != null">
        amt_cum_30_dxbb = #{amtCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Dxxp != null">
        amt_cum_30_dxxp = #{amtCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Dd != null">
        amt_cum_30_dd = #{amtCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30JtQyDx != null">
        amt_cum_30_jt_qy_dx = #{amtCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30NoDd != null">
        amt_cum_30_no_dd = #{amtCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Xz != null">
        amt_cum_30_xz = #{amtCum30Xz,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Js != null">
        amt_cum_30_js = #{amtCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30 != null">
        profit_cum_30 = #{profitCum30,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Jt != null">
        profit_cum_30_jt = #{profitCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Pt != null">
        profit_cum_30_pt = #{profitCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Qy != null">
        profit_cum_30_qy = #{profitCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Dxbb != null">
        profit_cum_30_dxbb = #{profitCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Dxxp != null">
        profit_cum_30_dxxp = #{profitCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Dd != null">
        profit_cum_30_dd = #{profitCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30JtQyDx != null">
        profit_cum_30_jt_qy_dx = #{profitCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30NoDd != null">
        profit_cum_30_no_dd = #{profitCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Xz != null">
        profit_cum_30_xz = #{profitCum30Xz,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Js != null">
        profit_cum_30_js = #{profitCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30Rate != null">
        profit_cum_30_rate = #{profitCum30Rate,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateJt != null">
        profit_cum_30_rate_jt = #{profitCum30RateJt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RatePt != null">
        profit_cum_30_rate_pt = #{profitCum30RatePt,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateQy != null">
        profit_cum_30_rate_qy = #{profitCum30RateQy,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateDxbb != null">
        profit_cum_30_rate_dxbb = #{profitCum30RateDxbb,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateDxxp != null">
        profit_cum_30_rate_dxxp = #{profitCum30RateDxxp,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateDd != null">
        profit_cum_30_rate_dd = #{profitCum30RateDd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateJtQyDx != null">
        profit_cum_30_rate_jt_qy_dx = #{profitCum30RateJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateNoDd != null">
        profit_cum_30_rate_no_dd = #{profitCum30RateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateXz != null">
        profit_cum_30_rate_xz = #{profitCum30RateXz,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateJs != null">
        profit_cum_30_rate_js = #{profitCum30RateJs,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Month != null">
        amt_cum_30_month = #{amtCum30Month,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30MonthNodtp != null">
        amt_cum_30_month_nodtp = #{amtCum30MonthNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30MonthNozy != null">
        amt_cum_30_month_nozy = #{amtCum30MonthNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Rate != null">
        amt_cum_30_rate = #{amtCum30Rate,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJt != null">
        amt_cum_30_rate_jt = #{amtCum30RateJt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RatePt != null">
        amt_cum_30_rate_pt = #{amtCum30RatePt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateQt != null">
        amt_cum_30_rate_qt = #{amtCum30RateQt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxbb != null">
        amt_cum_30_rate_dxbb = #{amtCum30RateDxbb,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxxp != null">
        amt_cum_30_rate_dxxp = #{amtCum30RateDxxp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDd != null">
        amt_cum_30_rate_dd = #{amtCum30RateDd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtQyDx != null">
        amt_cum_30_rate_jt_qy_dx = #{amtCum30RateJtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNoDd != null">
        amt_cum_30_rate_no_dd = #{amtCum30RateNoDd,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateXz != null">
        amt_cum_30_rate_xz = #{amtCum30RateXz,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJs != null">
        amt_cum_30_rate_js = #{amtCum30RateJs,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNodtp != null">
        amt_cum_30_rate_nodtp = #{amtCum30RateNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtNodtp != null">
        amt_cum_30_rate_jt_nodtp = #{amtCum30RateJtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RatePtNodtp != null">
        amt_cum_30_rate_pt_nodtp = #{amtCum30RatePtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateQtNodtp != null">
        amt_cum_30_rate_qt_nodtp = #{amtCum30RateQtNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxbbNodtp != null">
        amt_cum_30_rate_dxbb_nodtp = #{amtCum30RateDxbbNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxxpNodtp != null">
        amt_cum_30_rate_dxxp_nodtp = #{amtCum30RateDxxpNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDdNodtp != null">
        amt_cum_30_rate_dd_nodtp = #{amtCum30RateDdNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtQyDxNodtp != null">
        amt_cum_30_rate_jt_qy_dx_nodtp = #{amtCum30RateJtQyDxNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNoDdNodtp != null">
        amt_cum_30_rate_no_dd_nodtp = #{amtCum30RateNoDdNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateXzNodtp != null">
        amt_cum_30_rate_xz_nodtp = #{amtCum30RateXzNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJsNodtp != null">
        amt_cum_30_rate_js_nodtp = #{amtCum30RateJsNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNozy != null">
        amt_cum_30_rate_nozy = #{amtCum30RateNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtNozy != null">
        amt_cum_30_rate_jt_nozy = #{amtCum30RateJtNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RatePtNozy != null">
        amt_cum_30_rate_pt_nozy = #{amtCum30RatePtNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateQtNozy != null">
        amt_cum_30_rate_qt_nozy = #{amtCum30RateQtNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxbbNozy != null">
        amt_cum_30_rate_dxbb_nozy = #{amtCum30RateDxbbNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDxxpNozy != null">
        amt_cum_30_rate_dxxp_nozy = #{amtCum30RateDxxpNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateDdNozy != null">
        amt_cum_30_rate_dd_nozy = #{amtCum30RateDdNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJtQyDxNozy != null">
        amt_cum_30_rate_jt_qy_dx_nozy = #{amtCum30RateJtQyDxNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNoDdNozy != null">
        amt_cum_30_rate_no_dd_nozy = #{amtCum30RateNoDdNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateXzNozy != null">
        amt_cum_30_rate_xz_nozy = #{amtCum30RateXzNozy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateJsNozy != null">
        amt_cum_30_rate_js_nozy = #{amtCum30RateJsNozy,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30 != null">
        stock_cum_30 = #{stockCum30,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Jt != null">
        stock_cum_30_jt = #{stockCum30Jt,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Pt != null">
        stock_cum_30_pt = #{stockCum30Pt,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Qy != null">
        stock_cum_30_qy = #{stockCum30Qy,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Dxbb != null">
        stock_cum_30_dxbb = #{stockCum30Dxbb,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Dxxp != null">
        stock_cum_30_dxxp = #{stockCum30Dxxp,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Dd != null">
        stock_cum_30_dd = #{stockCum30Dd,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30JtQyDx != null">
        stock_cum_30_jt_qy_dx = #{stockCum30JtQyDx,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30NoDd != null">
        stock_cum_30_no_dd = #{stockCum30NoDd,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30New != null">
        stock_cum_30_new = #{stockCum30New,jdbcType=VARCHAR},
      </if>
      <if test="stockCum30Js != null">
        stock_cum_30_js = #{stockCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="avgStockCum30New != null">
        avg_stock_cum_30_new = #{avgStockCum30New,jdbcType=VARCHAR},
      </if>
      <if test="avgStockCum30Js != null">
        avg_stock_cum_30_js = #{avgStockCum30Js,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntXz != null">
        avg_sku_cnt_xz = #{avgSkuCntXz,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntJs != null">
        avg_sku_cnt_js = #{avgSkuCntJs,jdbcType=VARCHAR},
      </if>
      <if test="avgCnt != null">
        avg_cnt = #{avgCnt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Null != null">
        amt_cum_30_null = #{amtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30Null != null">
        avg_amt_cum_30_null = #{avgAmtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30New != null">
        amt_cum_30_new = #{amtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30New != null">
        avg_amt_cum_30_new = #{avgAmtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30New != null">
        profit_cum_30_new = #{profitCum30New,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateNew != null">
        profit_cum_30_rate_new = #{profitCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNew != null">
        amt_cum_30_rate_new = #{amtCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNewNodtp != null">
        amt_cum_30_rate_new_nodtp = #{amtCum30RateNewNodtp,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNewNozy != null">
        amt_cum_30_rate_new_nozy = #{amtCum30RateNewNozy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackRetultLevelReview">
    update track_retult_level_review
    set task_id = #{taskId,jdbcType=BIGINT},
      zone_new = #{zoneNew,jdbcType=VARCHAR},
      chain_name = #{chainName,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      revise_store_group = #{reviseStoreGroup,jdbcType=VARCHAR},
      classone_name = #{classoneName,jdbcType=VARCHAR},
      org_cnt_v3 = #{orgCntV3,jdbcType=VARCHAR},
      sku_cnt = #{skuCnt,jdbcType=VARCHAR},
      sku_cnt_jt = #{skuCntJt,jdbcType=VARCHAR},
      sku_cnt_pt = #{skuCntPt,jdbcType=VARCHAR},
      sku_cnt_qy = #{skuCntQy,jdbcType=VARCHAR},
      sku_cnt_dxbb = #{skuCntDxbb,jdbcType=VARCHAR},
      sku_cnt_dxxp = #{skuCntDxxp,jdbcType=VARCHAR},
      sku_cnt_dd = #{skuCntDd,jdbcType=VARCHAR},
      sku_cnt_jt_qy_dx = #{skuCntJtQyDx,jdbcType=VARCHAR},
      sku_cnt_no_dd = #{skuCntNoDd,jdbcType=VARCHAR},
      sku_cnt_xz = #{skuCntXz,jdbcType=VARCHAR},
      sku_cnt_js = #{skuCntJs,jdbcType=VARCHAR},
      sku_rate_top4 = #{skuRateTop4,jdbcType=VARCHAR},
      sku_rate_no_dd = #{skuRateNoDd,jdbcType=VARCHAR},
      comp_cnt = #{compCnt,jdbcType=VARCHAR},
      comp_cnt_jt = #{compCntJt,jdbcType=VARCHAR},
      comp_cnt_pt = #{compCntPt,jdbcType=VARCHAR},
      comp_cnt_qy = #{compCntQy,jdbcType=VARCHAR},
      comp_cnt_dxbb = #{compCntDxbb,jdbcType=VARCHAR},
      comp_cnt_dxxp = #{compCntDxxp,jdbcType=VARCHAR},
      comp_cnt_dd = #{compCntDd,jdbcType=VARCHAR},
      comp_cnt_jt_qy_dx = #{compCntJtQyDx,jdbcType=VARCHAR},
      comp_cnt_no_dd = #{compCntNoDd,jdbcType=VARCHAR},
      comp_cnt_xz = #{compCntXz,jdbcType=VARCHAR},
      comp_cnt_js = #{compCntJs,jdbcType=VARCHAR},
      comp_rate_top4 = #{compRateTop4,jdbcType=VARCHAR},
      comp_rate_no_dd = #{compRateNoDd,jdbcType=VARCHAR},
      class_cnt = #{classCnt,jdbcType=VARCHAR},
      class_cnt_jt = #{classCntJt,jdbcType=VARCHAR},
      class_cnt_pt = #{classCntPt,jdbcType=VARCHAR},
      class_cnt_qy = #{classCntQy,jdbcType=VARCHAR},
      class_cnt_dxbb = #{classCntDxbb,jdbcType=VARCHAR},
      class_cnt_dxxp = #{classCntDxxp,jdbcType=VARCHAR},
      class_cnt_dd = #{classCntDd,jdbcType=VARCHAR},
      class_cnt_jt_qy_dx = #{classCntJtQyDx,jdbcType=VARCHAR},
      class_cnt_no_dd = #{classCntNoDd,jdbcType=VARCHAR},
      class_cnt_xz = #{classCntXz,jdbcType=VARCHAR},
      class_cnt_js = #{classCntJs,jdbcType=VARCHAR},
      amt_cum_30 = #{amtCum30,jdbcType=VARCHAR},
      amt_cum_30_jt = #{amtCum30Jt,jdbcType=VARCHAR},
      amt_cum_30_pt = #{amtCum30Pt,jdbcType=VARCHAR},
      amt_cum_30_qy = #{amtCum30Qy,jdbcType=VARCHAR},
      amt_cum_30_dxbb = #{amtCum30Dxbb,jdbcType=VARCHAR},
      amt_cum_30_dxxp = #{amtCum30Dxxp,jdbcType=VARCHAR},
      amt_cum_30_dd = #{amtCum30Dd,jdbcType=VARCHAR},
      amt_cum_30_jt_qy_dx = #{amtCum30JtQyDx,jdbcType=VARCHAR},
      amt_cum_30_no_dd = #{amtCum30NoDd,jdbcType=VARCHAR},
      amt_cum_30_xz = #{amtCum30Xz,jdbcType=VARCHAR},
      amt_cum_30_js = #{amtCum30Js,jdbcType=VARCHAR},
      profit_cum_30 = #{profitCum30,jdbcType=VARCHAR},
      profit_cum_30_jt = #{profitCum30Jt,jdbcType=VARCHAR},
      profit_cum_30_pt = #{profitCum30Pt,jdbcType=VARCHAR},
      profit_cum_30_qy = #{profitCum30Qy,jdbcType=VARCHAR},
      profit_cum_30_dxbb = #{profitCum30Dxbb,jdbcType=VARCHAR},
      profit_cum_30_dxxp = #{profitCum30Dxxp,jdbcType=VARCHAR},
      profit_cum_30_dd = #{profitCum30Dd,jdbcType=VARCHAR},
      profit_cum_30_jt_qy_dx = #{profitCum30JtQyDx,jdbcType=VARCHAR},
      profit_cum_30_no_dd = #{profitCum30NoDd,jdbcType=VARCHAR},
      profit_cum_30_xz = #{profitCum30Xz,jdbcType=VARCHAR},
      profit_cum_30_js = #{profitCum30Js,jdbcType=VARCHAR},
      profit_cum_30_rate = #{profitCum30Rate,jdbcType=VARCHAR},
      profit_cum_30_rate_jt = #{profitCum30RateJt,jdbcType=VARCHAR},
      profit_cum_30_rate_pt = #{profitCum30RatePt,jdbcType=VARCHAR},
      profit_cum_30_rate_qy = #{profitCum30RateQy,jdbcType=VARCHAR},
      profit_cum_30_rate_dxbb = #{profitCum30RateDxbb,jdbcType=VARCHAR},
      profit_cum_30_rate_dxxp = #{profitCum30RateDxxp,jdbcType=VARCHAR},
      profit_cum_30_rate_dd = #{profitCum30RateDd,jdbcType=VARCHAR},
      profit_cum_30_rate_jt_qy_dx = #{profitCum30RateJtQyDx,jdbcType=VARCHAR},
      profit_cum_30_rate_no_dd = #{profitCum30RateNoDd,jdbcType=VARCHAR},
      profit_cum_30_rate_xz = #{profitCum30RateXz,jdbcType=VARCHAR},
      profit_cum_30_rate_js = #{profitCum30RateJs,jdbcType=VARCHAR},
      amt_cum_30_month = #{amtCum30Month,jdbcType=VARCHAR},
      amt_cum_30_month_nodtp = #{amtCum30MonthNodtp,jdbcType=VARCHAR},
      amt_cum_30_month_nozy = #{amtCum30MonthNozy,jdbcType=VARCHAR},
      amt_cum_30_rate = #{amtCum30Rate,jdbcType=VARCHAR},
      amt_cum_30_rate_jt = #{amtCum30RateJt,jdbcType=VARCHAR},
      amt_cum_30_rate_pt = #{amtCum30RatePt,jdbcType=VARCHAR},
      amt_cum_30_rate_qt = #{amtCum30RateQt,jdbcType=VARCHAR},
      amt_cum_30_rate_dxbb = #{amtCum30RateDxbb,jdbcType=VARCHAR},
      amt_cum_30_rate_dxxp = #{amtCum30RateDxxp,jdbcType=VARCHAR},
      amt_cum_30_rate_dd = #{amtCum30RateDd,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_qy_dx = #{amtCum30RateJtQyDx,jdbcType=VARCHAR},
      amt_cum_30_rate_no_dd = #{amtCum30RateNoDd,jdbcType=VARCHAR},
      amt_cum_30_rate_xz = #{amtCum30RateXz,jdbcType=VARCHAR},
      amt_cum_30_rate_js = #{amtCum30RateJs,jdbcType=VARCHAR},
      amt_cum_30_rate_nodtp = #{amtCum30RateNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_nodtp = #{amtCum30RateJtNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_pt_nodtp = #{amtCum30RatePtNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_qt_nodtp = #{amtCum30RateQtNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_dxbb_nodtp = #{amtCum30RateDxbbNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_dxxp_nodtp = #{amtCum30RateDxxpNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_dd_nodtp = #{amtCum30RateDdNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_qy_dx_nodtp = #{amtCum30RateJtQyDxNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_no_dd_nodtp = #{amtCum30RateNoDdNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_xz_nodtp = #{amtCum30RateXzNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_js_nodtp = #{amtCum30RateJsNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_nozy = #{amtCum30RateNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_nozy = #{amtCum30RateJtNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_pt_nozy = #{amtCum30RatePtNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_qt_nozy = #{amtCum30RateQtNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_dxbb_nozy = #{amtCum30RateDxbbNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_dxxp_nozy = #{amtCum30RateDxxpNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_dd_nozy = #{amtCum30RateDdNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_jt_qy_dx_nozy = #{amtCum30RateJtQyDxNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_no_dd_nozy = #{amtCum30RateNoDdNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_xz_nozy = #{amtCum30RateXzNozy,jdbcType=VARCHAR},
      amt_cum_30_rate_js_nozy = #{amtCum30RateJsNozy,jdbcType=VARCHAR},
      stock_cum_30 = #{stockCum30,jdbcType=VARCHAR},
      stock_cum_30_jt = #{stockCum30Jt,jdbcType=VARCHAR},
      stock_cum_30_pt = #{stockCum30Pt,jdbcType=VARCHAR},
      stock_cum_30_qy = #{stockCum30Qy,jdbcType=VARCHAR},
      stock_cum_30_dxbb = #{stockCum30Dxbb,jdbcType=VARCHAR},
      stock_cum_30_dxxp = #{stockCum30Dxxp,jdbcType=VARCHAR},
      stock_cum_30_dd = #{stockCum30Dd,jdbcType=VARCHAR},
      stock_cum_30_jt_qy_dx = #{stockCum30JtQyDx,jdbcType=VARCHAR},
      stock_cum_30_no_dd = #{stockCum30NoDd,jdbcType=VARCHAR},
      stock_cum_30_new = #{stockCum30New,jdbcType=VARCHAR},
      stock_cum_30_js = #{stockCum30Js,jdbcType=VARCHAR},
      avg_stock_cum_30_new = #{avgStockCum30New,jdbcType=VARCHAR},
      avg_stock_cum_30_js = #{avgStockCum30Js,jdbcType=VARCHAR},
      avg_sku_cnt_xz = #{avgSkuCntXz,jdbcType=VARCHAR},
      avg_sku_cnt_js = #{avgSkuCntJs,jdbcType=VARCHAR},
      avg_cnt = #{avgCnt,jdbcType=VARCHAR},
      amt_cum_30_null = #{amtCum30Null,jdbcType=VARCHAR},
      avg_amt_cum_30_null = #{avgAmtCum30Null,jdbcType=VARCHAR},
      amt_cum_30_new = #{amtCum30New,jdbcType=VARCHAR},
      avg_amt_cum_30_new = #{avgAmtCum30New,jdbcType=VARCHAR},
      profit_cum_30_new = #{profitCum30New,jdbcType=VARCHAR},
      profit_cum_30_rate_new = #{profitCum30RateNew,jdbcType=VARCHAR},
      amt_cum_30_rate_new = #{amtCum30RateNew,jdbcType=VARCHAR},
      amt_cum_30_rate_new_nodtp = #{amtCum30RateNewNodtp,jdbcType=VARCHAR},
      amt_cum_30_rate_new_nozy = #{amtCum30RateNewNozy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>