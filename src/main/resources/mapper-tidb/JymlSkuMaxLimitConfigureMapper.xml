<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.JymlSkuMaxLimitConfigureMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="sku_max_limit" jdbcType="INTEGER" property="skuMaxLimit" />
    <result column="sku_suggested_limit" jdbcType="INTEGER" property="skuSuggestedLimit" />
    <result column="sku_lower_limit" jdbcType="INTEGER" property="skuLowerLimit" />
    <result column="sku_suggested_lower_limit" jdbcType="INTEGER" property="skuSuggestedLowerLimit" />
    <result column="ingredient_count" jdbcType="INTEGER" property="ingredientCount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_org_id, platform_name, business_org_id, business_name, city, store_type,
    store_type_name, category, category_name, middle_category, middle_category_name,
    small_category, small_category_name, sub_category, sub_category_name, sku_max_limit,
    sku_suggested_limit, sku_lower_limit, sku_suggested_lower_limit, ingredient_count,
    `status`, gmt_create, gmt_update, create_by, update_by, extend, version, env, create_by_id,
    update_by_id, rx_otc
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_sku_max_limit_configure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jyml_sku_max_limit_configure
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_sku_max_limit_configure
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample">
    delete from jyml_sku_max_limit_configure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    insert into jyml_sku_max_limit_configure (id, platform_org_id, platform_name,
    business_org_id, business_name, city,
    store_type, store_type_name, category,
    category_name, middle_category, middle_category_name,
    small_category, small_category_name, sub_category,
    sub_category_name, sku_max_limit, sku_suggested_limit,
    sku_lower_limit, sku_suggested_lower_limit,
    ingredient_count, `status`, gmt_create,
    gmt_update, create_by, update_by,
    extend, version, env,
    create_by_id, update_by_id, rx_otc
    )
    values (#{id,jdbcType=BIGINT}, #{platformOrgId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR},
    #{businessOrgId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
    #{storeType,jdbcType=VARCHAR}, #{storeTypeName,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR},
    #{categoryName,jdbcType=VARCHAR}, #{middleCategory,jdbcType=VARCHAR}, #{middleCategoryName,jdbcType=VARCHAR},
    #{smallCategory,jdbcType=VARCHAR}, #{smallCategoryName,jdbcType=VARCHAR}, #{subCategory,jdbcType=VARCHAR},
    #{subCategoryName,jdbcType=VARCHAR}, #{skuMaxLimit,jdbcType=INTEGER}, #{skuSuggestedLimit,jdbcType=INTEGER},
    #{skuLowerLimit,jdbcType=INTEGER}, #{skuSuggestedLowerLimit,jdbcType=INTEGER},
    #{ingredientCount,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
    #{gmtUpdate,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
    #{extend,jdbcType=VARCHAR}, #{version,jdbcType=BIGINT}, #{env,jdbcType=VARCHAR},
    #{createById,jdbcType=BIGINT}, #{updateById,jdbcType=BIGINT}, #{rxOtc,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    insert into jyml_sku_max_limit_configure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
      <if test="storeTypeName != null">
        store_type_name,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="middleCategory != null">
        middle_category,
      </if>
      <if test="middleCategoryName != null">
        middle_category_name,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="smallCategoryName != null">
        small_category_name,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="skuMaxLimit != null">
        sku_max_limit,
      </if>
      <if test="skuSuggestedLimit != null">
        sku_suggested_limit,
      </if>
      <if test="skuLowerLimit != null">
        sku_lower_limit,
      </if>
      <if test="skuSuggestedLowerLimit != null">
        sku_suggested_lower_limit,
      </if>
      <if test="ingredientCount != null">
        ingredient_count,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="env != null">
        env,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="rxOtc != null">
        rx_otc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeName != null">
        #{storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="skuMaxLimit != null">
        #{skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="skuSuggestedLimit != null">
        #{skuSuggestedLimit,jdbcType=INTEGER},
      </if>
      <if test="skuLowerLimit != null">
        #{skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="skuSuggestedLowerLimit != null">
        #{skuSuggestedLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="ingredientCount != null">
        #{ingredientCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="env != null">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="rxOtc != null">
        #{rxOtc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample" resultType="java.lang.Long">
    select count(*) from jyml_sku_max_limit_configure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_sku_max_limit_configure
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=VARCHAR},
      </if>
      <if test="record.storeTypeName != null">
        store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategoryName != null">
        middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuMaxLimit != null">
        sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuSuggestedLimit != null">
        sku_suggested_limit = #{record.skuSuggestedLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuLowerLimit != null">
        sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuSuggestedLowerLimit != null">
        sku_suggested_lower_limit = #{record.skuSuggestedLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.ingredientCount != null">
        ingredient_count = #{record.ingredientCount,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.env != null">
        env = #{record.env,jdbcType=VARCHAR},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.rxOtc != null">
        rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_sku_max_limit_configure
    set id = #{record.id,jdbcType=BIGINT},
    platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
    platform_name = #{record.platformName,jdbcType=VARCHAR},
    business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
    business_name = #{record.businessName,jdbcType=VARCHAR},
    city = #{record.city,jdbcType=VARCHAR},
    store_type = #{record.storeType,jdbcType=VARCHAR},
    store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
    category = #{record.category,jdbcType=VARCHAR},
    category_name = #{record.categoryName,jdbcType=VARCHAR},
    middle_category = #{record.middleCategory,jdbcType=VARCHAR},
    middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
    small_category = #{record.smallCategory,jdbcType=VARCHAR},
    small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
    sub_category = #{record.subCategory,jdbcType=VARCHAR},
    sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
    sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
    sku_suggested_limit = #{record.skuSuggestedLimit,jdbcType=INTEGER},
    sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
    sku_suggested_lower_limit = #{record.skuSuggestedLowerLimit,jdbcType=INTEGER},
    ingredient_count = #{record.ingredientCount,jdbcType=INTEGER},
    `status` = #{record.status,jdbcType=TINYINT},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
    create_by = #{record.createBy,jdbcType=VARCHAR},
    update_by = #{record.updateBy,jdbcType=VARCHAR},
    extend = #{record.extend,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=BIGINT},
    env = #{record.env,jdbcType=VARCHAR},
    create_by_id = #{record.createById,jdbcType=BIGINT},
    update_by_id = #{record.updateById,jdbcType=BIGINT},
    rx_otc = #{record.rxOtc,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    update jyml_sku_max_limit_configure
    <set>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeName != null">
        store_type_name = #{storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        middle_category = #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="skuMaxLimit != null">
        sku_max_limit = #{skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="skuSuggestedLimit != null">
        sku_suggested_limit = #{skuSuggestedLimit,jdbcType=INTEGER},
      </if>
      <if test="skuLowerLimit != null">
        sku_lower_limit = #{skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="skuSuggestedLowerLimit != null">
        sku_suggested_lower_limit = #{skuSuggestedLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="ingredientCount != null">
        ingredient_count = #{ingredientCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="env != null">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="rxOtc != null">
        rx_otc = #{rxOtc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    update jyml_sku_max_limit_configure
    set platform_org_id = #{platformOrgId,jdbcType=BIGINT},
    platform_name = #{platformName,jdbcType=VARCHAR},
    business_org_id = #{businessOrgId,jdbcType=BIGINT},
    business_name = #{businessName,jdbcType=VARCHAR},
    city = #{city,jdbcType=VARCHAR},
    store_type = #{storeType,jdbcType=VARCHAR},
    store_type_name = #{storeTypeName,jdbcType=VARCHAR},
    category = #{category,jdbcType=VARCHAR},
    category_name = #{categoryName,jdbcType=VARCHAR},
    middle_category = #{middleCategory,jdbcType=VARCHAR},
    middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
    small_category = #{smallCategory,jdbcType=VARCHAR},
    small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
    sub_category = #{subCategory,jdbcType=VARCHAR},
    sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
    sku_max_limit = #{skuMaxLimit,jdbcType=INTEGER},
    sku_suggested_limit = #{skuSuggestedLimit,jdbcType=INTEGER},
    sku_lower_limit = #{skuLowerLimit,jdbcType=INTEGER},
    sku_suggested_lower_limit = #{skuSuggestedLowerLimit,jdbcType=INTEGER},
    ingredient_count = #{ingredientCount,jdbcType=INTEGER},
    `status` = #{status,jdbcType=TINYINT},
    gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=VARCHAR},
    update_by = #{updateBy,jdbcType=VARCHAR},
    extend = #{extend,jdbcType=VARCHAR},
    version = #{version,jdbcType=BIGINT},
    env = #{env,jdbcType=VARCHAR},
    create_by_id = #{createById,jdbcType=BIGINT},
    update_by_id = #{updateById,jdbcType=BIGINT},
    rx_otc = #{rxOtc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <resultMap id="OptionResultMap" type="com.cowell.scib.service.dto.manageContents.ManageDropOptionDTO">
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
  </resultMap>

  <select id="selectOptionsByExample" parameterType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample" resultMap="OptionResultMap">
    select
    <choose>
      <when test="field == 'version'">
        version
      </when>
      <when test="field == 'city'">
        city
      </when>
      <when test="field == 'category'">
         category, category_name
      </when>
      <when test="field == 'middle_category'">
         middle_category, middle_category_name
      </when>
      <when test="field == 'small_category'">
         small_category, small_category_name
      </when>
      <otherwise>
         sub_category, sub_category_name
      </otherwise>
    </choose>
    from jyml_sku_max_limit_configure
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <choose>
      <when test="field == 'version'">
        group by  version
      </when>
      <when test="field == 'city'">
        group by  city
      </when>
      <when test="field == 'category'">
        group by  category, category_name
      </when>
      <when test="field == 'middle_category'">
        group by  middle_category, middle_category_name
      </when>
      <when test="field == 'small_category'">
        group by  small_category, small_category_name
      </when>
      <otherwise>
        group by sub_category, sub_category_name
      </otherwise>
    </choose>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    <if test="example.limit != null">
      <if test="example.offset != null">
        limit ${example.offset}, ${example.limit}
      </if>
      <if test="example.offset == null">
        limit ${example.limit}
      </if>
    </if>
  </select>

</mapper>
