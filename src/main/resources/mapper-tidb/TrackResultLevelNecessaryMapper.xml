<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackResultLevelNecessaryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackResultLevelNecessary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="plat_orgid" jdbcType="VARCHAR" property="platOrgid" />
    <result column="compid" jdbcType="VARCHAR" property="compid" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="store_group" jdbcType="VARCHAR" property="storeGroup" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="bak" jdbcType="VARCHAR" property="bak" />
    <result column="store_concentration" jdbcType="VARCHAR" property="storeConcentration" />
    <result column="store_sale_rate" jdbcType="VARCHAR" property="storeSaleRate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, `level`, plat_orgid, compid, city, store_group, store_code, goods_id, 
    bak, store_concentration, store_sale_rate, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_result_level_necessary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_result_level_necessary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_result_level_necessary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessaryExample">
    delete from track_result_level_necessary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessary" useGeneratedKeys="true">
    insert into track_result_level_necessary (task_id, `level`, plat_orgid, 
      compid, city, store_group, 
      store_code, goods_id, bak, 
      store_concentration, store_sale_rate, gmt_create
      )
    values (#{taskId,jdbcType=BIGINT}, #{level,jdbcType=INTEGER}, #{platOrgid,jdbcType=VARCHAR}, 
      #{compid,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{storeGroup,jdbcType=VARCHAR}, 
      #{storeCode,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR}, #{bak,jdbcType=VARCHAR}, 
      #{storeConcentration,jdbcType=VARCHAR}, #{storeSaleRate,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessary" useGeneratedKeys="true">
    insert into track_result_level_necessary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="platOrgid != null">
        plat_orgid,
      </if>
      <if test="compid != null">
        compid,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="storeGroup != null">
        store_group,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="bak != null">
        bak,
      </if>
      <if test="storeConcentration != null">
        store_concentration,
      </if>
      <if test="storeSaleRate != null">
        store_sale_rate,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="platOrgid != null">
        #{platOrgid,jdbcType=VARCHAR},
      </if>
      <if test="compid != null">
        #{compid,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeGroup != null">
        #{storeGroup,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="bak != null">
        #{bak,jdbcType=VARCHAR},
      </if>
      <if test="storeConcentration != null">
        #{storeConcentration,jdbcType=VARCHAR},
      </if>
      <if test="storeSaleRate != null">
        #{storeSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessaryExample" resultType="java.lang.Long">
    select count(*) from track_result_level_necessary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_result_level_necessary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.platOrgid != null">
        plat_orgid = #{record.platOrgid,jdbcType=VARCHAR},
      </if>
      <if test="record.compid != null">
        compid = #{record.compid,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGroup != null">
        store_group = #{record.storeGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=VARCHAR},
      </if>
      <if test="record.bak != null">
        bak = #{record.bak,jdbcType=VARCHAR},
      </if>
      <if test="record.storeConcentration != null">
        store_concentration = #{record.storeConcentration,jdbcType=VARCHAR},
      </if>
      <if test="record.storeSaleRate != null">
        store_sale_rate = #{record.storeSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_result_level_necessary
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      `level` = #{record.level,jdbcType=INTEGER},
      plat_orgid = #{record.platOrgid,jdbcType=VARCHAR},
      compid = #{record.compid,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      store_group = #{record.storeGroup,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      goods_id = #{record.goodsId,jdbcType=VARCHAR},
      bak = #{record.bak,jdbcType=VARCHAR},
      store_concentration = #{record.storeConcentration,jdbcType=VARCHAR},
      store_sale_rate = #{record.storeSaleRate,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessary">
    update track_result_level_necessary
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=INTEGER},
      </if>
      <if test="platOrgid != null">
        plat_orgid = #{platOrgid,jdbcType=VARCHAR},
      </if>
      <if test="compid != null">
        compid = #{compid,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeGroup != null">
        store_group = #{storeGroup,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="bak != null">
        bak = #{bak,jdbcType=VARCHAR},
      </if>
      <if test="storeConcentration != null">
        store_concentration = #{storeConcentration,jdbcType=VARCHAR},
      </if>
      <if test="storeSaleRate != null">
        store_sale_rate = #{storeSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackResultLevelNecessary">
    update track_result_level_necessary
    set task_id = #{taskId,jdbcType=BIGINT},
      `level` = #{level,jdbcType=INTEGER},
      plat_orgid = #{platOrgid,jdbcType=VARCHAR},
      compid = #{compid,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      store_group = #{storeGroup,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=VARCHAR},
      bak = #{bak,jdbcType=VARCHAR},
      store_concentration = #{storeConcentration,jdbcType=VARCHAR},
      store_sale_rate = #{storeSaleRate,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>