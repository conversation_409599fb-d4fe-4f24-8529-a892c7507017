<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackRetultEfficiencyAnalyseMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="revise_types" jdbcType="VARCHAR" property="reviseTypes" />
    <result column="org_cnt_v2" jdbcType="VARCHAR" property="orgCntV2" />
    <result column="org_cnt_v3" jdbcType="VARCHAR" property="orgCntV3" />
    <result column="cnt_v3" jdbcType="VARCHAR" property="cntV3" />
    <result column="cnt_v3_v2" jdbcType="VARCHAR" property="cntV3V2" />
    <result column="cnt_xz" jdbcType="VARCHAR" property="cntXz" />
    <result column="cnt_tc" jdbcType="VARCHAR" property="cntTc" />
    <result column="avg_sku_cnt" jdbcType="VARCHAR" property="avgSkuCnt" />
    <result column="avg_sku_cnt_v3_v2" jdbcType="VARCHAR" property="avgSkuCntV3V2" />
    <result column="avg_sku_cnt_tc" jdbcType="VARCHAR" property="avgSkuCntTc" />
    <result column="avg_sku_cnt_jc" jdbcType="VARCHAR" property="avgSkuCntJc" />
    <result column="avg_sku_cnt_dc" jdbcType="VARCHAR" property="avgSkuCntDc" />
    <result column="avg_sku_cnt_aa" jdbcType="VARCHAR" property="avgSkuCntAa" />
    <result column="avg_sku_cnt_a1" jdbcType="VARCHAR" property="avgSkuCntA1" />
    <result column="amt_cum_30_total" jdbcType="VARCHAR" property="amtCum30Total" />
    <result column="amt_cum_30_rate_v3_total" jdbcType="VARCHAR" property="amtCum30RateV3Total" />
    <result column="amt_cum_30_rate_v3_v2_total" jdbcType="VARCHAR" property="amtCum30RateV3V2Total" />
    <result column="amt_cum_30_v3_v2_total" jdbcType="VARCHAR" property="amtCum30V3V2Total" />
    <result column="amt_cum_30_v3_v2_1_total" jdbcType="VARCHAR" property="amtCum30V3V21Total" />
    <result column="amt_cum_30_rate_v3_total_aa" jdbcType="VARCHAR" property="amtCum30RateV3TotalAa" />
    <result column="amt_cum_30_rate_v3_total_a1" jdbcType="VARCHAR" property="amtCum30RateV3TotalA1" />
    <result column="amt_cum_30_rate_v3_total_tc" jdbcType="VARCHAR" property="amtCum30RateV3TotalTc" />
    <result column="amt_cum_30_rate_v3_total_jc" jdbcType="VARCHAR" property="amtCum30RateV3TotalJc" />
    <result column="amt_cum_30_rate_v3_total_dc" jdbcType="VARCHAR" property="amtCum30RateV3TotalDc" />
    <result column="amt_cum_30_rate_v3_total_on" jdbcType="VARCHAR" property="amtCum30RateV3TotalOn" />
    <result column="amt_cum_30_rate_v3_total_off" jdbcType="VARCHAR" property="amtCum30RateV3TotalOff" />
    <result column="profit_cum_30_v3_total" jdbcType="VARCHAR" property="profitCum30V3Total" />
    <result column="profit_cum_30_v3_v2_total" jdbcType="VARCHAR" property="profitCum30V3V2Total" />
    <result column="profit_cum_30_v3_v2_1_total" jdbcType="VARCHAR" property="profitCum30V3V21Total" />
    <result column="profit_cum_30_rate_v3_total" jdbcType="VARCHAR" property="profitCum30RateV3Total" />
    <result column="amt_cum_30_rate_gx_v3_total" jdbcType="VARCHAR" property="amtCum30RateGxV3Total" />
    <result column="profit_cum_30_rate_v3_v2_total" jdbcType="VARCHAR" property="profitCum30RateV3V2Total" />
    <result column="profit_cum_30_rate_v3_total_aa" jdbcType="VARCHAR" property="profitCum30RateV3TotalAa" />
    <result column="profit_cum_30_rate_v3_total_a1" jdbcType="VARCHAR" property="profitCum30RateV3TotalA1" />
    <result column="profit_cum_30_rate_v3_total_tc" jdbcType="VARCHAR" property="profitCum30RateV3TotalTc" />
    <result column="profit_cum_30_rate_v3_total_jc" jdbcType="VARCHAR" property="profitCum30RateV3TotalJc" />
    <result column="profit_cum_30_rate_v3_total_dc" jdbcType="VARCHAR" property="profitCum30RateV3TotalDc" />
    <result column="profit_cum_30_rate_v3_total_on" jdbcType="VARCHAR" property="profitCum30RateV3TotalOn" />
    <result column="profit_cum_30_rate_v3_total_off" jdbcType="VARCHAR" property="profitCum30RateV3TotalOff" />
    <result column="avg_cnt" jdbcType="VARCHAR" property="avgCnt" />
    <result column="amt_cum_30_null" jdbcType="VARCHAR" property="amtCum30Null" />
    <result column="avg_amt_cum_30_null" jdbcType="VARCHAR" property="avgAmtCum30Null" />
    <result column="amt_cum_30_new" jdbcType="VARCHAR" property="amtCum30New" />
    <result column="avg_amt_cum_30_new" jdbcType="VARCHAR" property="avgAmtCum30New" />
    <result column="amt_cum_30_rate_new" jdbcType="VARCHAR" property="amtCum30RateNew" />
    <result column="profit_cum_30_new" jdbcType="VARCHAR" property="profitCum30New" />
    <result column="profit_cum_30_rate_new" jdbcType="VARCHAR" property="profitCum30RateNew" />
    <result column="amt_cum_30_v3" jdbcType="VARCHAR" property="amtCum30V3" />
    <result column="amt_cum_30_rate_v3" jdbcType="VARCHAR" property="amtCum30RateV3" />
    <result column="amt_cum_30_rate_v3_v2" jdbcType="VARCHAR" property="amtCum30RateV3V2" />
    <result column="amt_cum_30_v3_v2" jdbcType="VARCHAR" property="amtCum30V3V2" />
    <result column="amt_cum_30_v3_v2_1" jdbcType="VARCHAR" property="amtCum30V3V21" />
    <result column="amt_cum_30_rate_v3_aa" jdbcType="VARCHAR" property="amtCum30RateV3Aa" />
    <result column="amt_cum_30_rate_v3_a1" jdbcType="VARCHAR" property="amtCum30RateV3A1" />
    <result column="amt_cum_30_rate_v3_tc" jdbcType="VARCHAR" property="amtCum30RateV3Tc" />
    <result column="amt_cum_30_rate_v3_jc" jdbcType="VARCHAR" property="amtCum30RateV3Jc" />
    <result column="amt_cum_30_rate_v3_dc" jdbcType="VARCHAR" property="amtCum30RateV3Dc" />
    <result column="amt_cum_30_rate_v3_on" jdbcType="VARCHAR" property="amtCum30RateV3On" />
    <result column="amt_cum_30_rate_v3_off" jdbcType="VARCHAR" property="amtCum30RateV3Off" />
    <result column="profit_cum_30_v3" jdbcType="VARCHAR" property="profitCum30V3" />
    <result column="profit_cum_30_v3_v2" jdbcType="VARCHAR" property="profitCum30V3V2" />
    <result column="profit_cum_30_v3_v2_1" jdbcType="VARCHAR" property="profitCum30V3V21" />
    <result column="profit_cum_30_rate_v3" jdbcType="VARCHAR" property="profitCum30RateV3" />
    <result column="amt_cum_30_rate_gx_v3" jdbcType="VARCHAR" property="amtCum30RateGxV3" />
    <result column="profit_cum_30_rate_v3_v2" jdbcType="VARCHAR" property="profitCum30RateV3V2" />
    <result column="profit_cum_30_rate_v3_aa" jdbcType="VARCHAR" property="profitCum30RateV3Aa" />
    <result column="profit_cum_30_rate_v3_a1" jdbcType="VARCHAR" property="profitCum30RateV3A1" />
    <result column="profit_cum_30_rate_v3_tc" jdbcType="VARCHAR" property="profitCum30RateV3Tc" />
    <result column="profit_cum_30_rate_v3_jc" jdbcType="VARCHAR" property="profitCum30RateV3Jc" />
    <result column="profit_cum_30_rate_v3_dc" jdbcType="VARCHAR" property="profitCum30RateV3Dc" />
    <result column="profit_cum_30_rate_v3_on" jdbcType="VARCHAR" property="profitCum30RateV3On" />
    <result column="profit_cum_30_rate_v3_off" jdbcType="VARCHAR" property="profitCum30RateV3Off" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, zone_new, chain_name, revise_types, org_cnt_v2, org_cnt_v3, cnt_v3, 
    cnt_v3_v2, cnt_xz, cnt_tc, avg_sku_cnt, avg_sku_cnt_v3_v2, avg_sku_cnt_tc, avg_sku_cnt_jc, 
    avg_sku_cnt_dc, avg_sku_cnt_aa, avg_sku_cnt_a1, amt_cum_30_total, amt_cum_30_rate_v3_total, 
    amt_cum_30_rate_v3_v2_total, amt_cum_30_v3_v2_total, amt_cum_30_v3_v2_1_total, amt_cum_30_rate_v3_total_aa, 
    amt_cum_30_rate_v3_total_a1, amt_cum_30_rate_v3_total_tc, amt_cum_30_rate_v3_total_jc, 
    amt_cum_30_rate_v3_total_dc, amt_cum_30_rate_v3_total_on, amt_cum_30_rate_v3_total_off, 
    profit_cum_30_v3_total, profit_cum_30_v3_v2_total, profit_cum_30_v3_v2_1_total, profit_cum_30_rate_v3_total, 
    amt_cum_30_rate_gx_v3_total, profit_cum_30_rate_v3_v2_total, profit_cum_30_rate_v3_total_aa, 
    profit_cum_30_rate_v3_total_a1, profit_cum_30_rate_v3_total_tc, profit_cum_30_rate_v3_total_jc, 
    profit_cum_30_rate_v3_total_dc, profit_cum_30_rate_v3_total_on, profit_cum_30_rate_v3_total_off, 
    avg_cnt, amt_cum_30_null, avg_amt_cum_30_null, amt_cum_30_new, avg_amt_cum_30_new, 
    amt_cum_30_rate_new, profit_cum_30_new, profit_cum_30_rate_new, amt_cum_30_v3, amt_cum_30_rate_v3, 
    amt_cum_30_rate_v3_v2, amt_cum_30_v3_v2, amt_cum_30_v3_v2_1, amt_cum_30_rate_v3_aa, 
    amt_cum_30_rate_v3_a1, amt_cum_30_rate_v3_tc, amt_cum_30_rate_v3_jc, amt_cum_30_rate_v3_dc, 
    amt_cum_30_rate_v3_on, amt_cum_30_rate_v3_off, profit_cum_30_v3, profit_cum_30_v3_v2, 
    profit_cum_30_v3_v2_1, profit_cum_30_rate_v3, amt_cum_30_rate_gx_v3, profit_cum_30_rate_v3_v2, 
    profit_cum_30_rate_v3_aa, profit_cum_30_rate_v3_a1, profit_cum_30_rate_v3_tc, profit_cum_30_rate_v3_jc, 
    profit_cum_30_rate_v3_dc, profit_cum_30_rate_v3_on, profit_cum_30_rate_v3_off, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_retult_efficiency_analyse
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_retult_efficiency_analyse
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_retult_efficiency_analyse
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyseExample">
    delete from track_retult_efficiency_analyse
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse" useGeneratedKeys="true">
    insert into track_retult_efficiency_analyse (task_id, zone_new, chain_name, 
      revise_types, org_cnt_v2, org_cnt_v3, 
      cnt_v3, cnt_v3_v2, cnt_xz, 
      cnt_tc, avg_sku_cnt, avg_sku_cnt_v3_v2, 
      avg_sku_cnt_tc, avg_sku_cnt_jc, avg_sku_cnt_dc, 
      avg_sku_cnt_aa, avg_sku_cnt_a1, amt_cum_30_total, 
      amt_cum_30_rate_v3_total, amt_cum_30_rate_v3_v2_total, 
      amt_cum_30_v3_v2_total, amt_cum_30_v3_v2_1_total, 
      amt_cum_30_rate_v3_total_aa, amt_cum_30_rate_v3_total_a1, 
      amt_cum_30_rate_v3_total_tc, amt_cum_30_rate_v3_total_jc, 
      amt_cum_30_rate_v3_total_dc, amt_cum_30_rate_v3_total_on, 
      amt_cum_30_rate_v3_total_off, profit_cum_30_v3_total, 
      profit_cum_30_v3_v2_total, profit_cum_30_v3_v2_1_total, 
      profit_cum_30_rate_v3_total, amt_cum_30_rate_gx_v3_total, 
      profit_cum_30_rate_v3_v2_total, profit_cum_30_rate_v3_total_aa, 
      profit_cum_30_rate_v3_total_a1, profit_cum_30_rate_v3_total_tc, 
      profit_cum_30_rate_v3_total_jc, profit_cum_30_rate_v3_total_dc, 
      profit_cum_30_rate_v3_total_on, profit_cum_30_rate_v3_total_off, 
      avg_cnt, amt_cum_30_null, avg_amt_cum_30_null, 
      amt_cum_30_new, avg_amt_cum_30_new, amt_cum_30_rate_new, 
      profit_cum_30_new, profit_cum_30_rate_new, amt_cum_30_v3, 
      amt_cum_30_rate_v3, amt_cum_30_rate_v3_v2, amt_cum_30_v3_v2, 
      amt_cum_30_v3_v2_1, amt_cum_30_rate_v3_aa, amt_cum_30_rate_v3_a1, 
      amt_cum_30_rate_v3_tc, amt_cum_30_rate_v3_jc, amt_cum_30_rate_v3_dc, 
      amt_cum_30_rate_v3_on, amt_cum_30_rate_v3_off, profit_cum_30_v3, 
      profit_cum_30_v3_v2, profit_cum_30_v3_v2_1, profit_cum_30_rate_v3, 
      amt_cum_30_rate_gx_v3, profit_cum_30_rate_v3_v2, 
      profit_cum_30_rate_v3_aa, profit_cum_30_rate_v3_a1, 
      profit_cum_30_rate_v3_tc, profit_cum_30_rate_v3_jc, 
      profit_cum_30_rate_v3_dc, profit_cum_30_rate_v3_on, 
      profit_cum_30_rate_v3_off, gmt_create)
    values (#{taskId,jdbcType=BIGINT}, #{zoneNew,jdbcType=VARCHAR}, #{chainName,jdbcType=VARCHAR}, 
      #{reviseTypes,jdbcType=VARCHAR}, #{orgCntV2,jdbcType=VARCHAR}, #{orgCntV3,jdbcType=VARCHAR}, 
      #{cntV3,jdbcType=VARCHAR}, #{cntV3V2,jdbcType=VARCHAR}, #{cntXz,jdbcType=VARCHAR}, 
      #{cntTc,jdbcType=VARCHAR}, #{avgSkuCnt,jdbcType=VARCHAR}, #{avgSkuCntV3V2,jdbcType=VARCHAR}, 
      #{avgSkuCntTc,jdbcType=VARCHAR}, #{avgSkuCntJc,jdbcType=VARCHAR}, #{avgSkuCntDc,jdbcType=VARCHAR}, 
      #{avgSkuCntAa,jdbcType=VARCHAR}, #{avgSkuCntA1,jdbcType=VARCHAR}, #{amtCum30Total,jdbcType=VARCHAR}, 
      #{amtCum30RateV3Total,jdbcType=VARCHAR}, #{amtCum30RateV3V2Total,jdbcType=VARCHAR}, 
      #{amtCum30V3V2Total,jdbcType=VARCHAR}, #{amtCum30V3V21Total,jdbcType=VARCHAR}, 
      #{amtCum30RateV3TotalAa,jdbcType=VARCHAR}, #{amtCum30RateV3TotalA1,jdbcType=VARCHAR}, 
      #{amtCum30RateV3TotalTc,jdbcType=VARCHAR}, #{amtCum30RateV3TotalJc,jdbcType=VARCHAR}, 
      #{amtCum30RateV3TotalDc,jdbcType=VARCHAR}, #{amtCum30RateV3TotalOn,jdbcType=VARCHAR}, 
      #{amtCum30RateV3TotalOff,jdbcType=VARCHAR}, #{profitCum30V3Total,jdbcType=VARCHAR}, 
      #{profitCum30V3V2Total,jdbcType=VARCHAR}, #{profitCum30V3V21Total,jdbcType=VARCHAR}, 
      #{profitCum30RateV3Total,jdbcType=VARCHAR}, #{amtCum30RateGxV3Total,jdbcType=VARCHAR}, 
      #{profitCum30RateV3V2Total,jdbcType=VARCHAR}, #{profitCum30RateV3TotalAa,jdbcType=VARCHAR}, 
      #{profitCum30RateV3TotalA1,jdbcType=VARCHAR}, #{profitCum30RateV3TotalTc,jdbcType=VARCHAR}, 
      #{profitCum30RateV3TotalJc,jdbcType=VARCHAR}, #{profitCum30RateV3TotalDc,jdbcType=VARCHAR}, 
      #{profitCum30RateV3TotalOn,jdbcType=VARCHAR}, #{profitCum30RateV3TotalOff,jdbcType=VARCHAR}, 
      #{avgCnt,jdbcType=VARCHAR}, #{amtCum30Null,jdbcType=VARCHAR}, #{avgAmtCum30Null,jdbcType=VARCHAR}, 
      #{amtCum30New,jdbcType=VARCHAR}, #{avgAmtCum30New,jdbcType=VARCHAR}, #{amtCum30RateNew,jdbcType=VARCHAR}, 
      #{profitCum30New,jdbcType=VARCHAR}, #{profitCum30RateNew,jdbcType=VARCHAR}, #{amtCum30V3,jdbcType=VARCHAR}, 
      #{amtCum30RateV3,jdbcType=VARCHAR}, #{amtCum30RateV3V2,jdbcType=VARCHAR}, #{amtCum30V3V2,jdbcType=VARCHAR}, 
      #{amtCum30V3V21,jdbcType=VARCHAR}, #{amtCum30RateV3Aa,jdbcType=VARCHAR}, #{amtCum30RateV3A1,jdbcType=VARCHAR}, 
      #{amtCum30RateV3Tc,jdbcType=VARCHAR}, #{amtCum30RateV3Jc,jdbcType=VARCHAR}, #{amtCum30RateV3Dc,jdbcType=VARCHAR}, 
      #{amtCum30RateV3On,jdbcType=VARCHAR}, #{amtCum30RateV3Off,jdbcType=VARCHAR}, #{profitCum30V3,jdbcType=VARCHAR}, 
      #{profitCum30V3V2,jdbcType=VARCHAR}, #{profitCum30V3V21,jdbcType=VARCHAR}, #{profitCum30RateV3,jdbcType=VARCHAR}, 
      #{amtCum30RateGxV3,jdbcType=VARCHAR}, #{profitCum30RateV3V2,jdbcType=VARCHAR}, 
      #{profitCum30RateV3Aa,jdbcType=VARCHAR}, #{profitCum30RateV3A1,jdbcType=VARCHAR}, 
      #{profitCum30RateV3Tc,jdbcType=VARCHAR}, #{profitCum30RateV3Jc,jdbcType=VARCHAR}, 
      #{profitCum30RateV3Dc,jdbcType=VARCHAR}, #{profitCum30RateV3On,jdbcType=VARCHAR}, 
      #{profitCum30RateV3Off,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse" useGeneratedKeys="true">
    insert into track_retult_efficiency_analyse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="zoneNew != null">
        zone_new,
      </if>
      <if test="chainName != null">
        chain_name,
      </if>
      <if test="reviseTypes != null">
        revise_types,
      </if>
      <if test="orgCntV2 != null">
        org_cnt_v2,
      </if>
      <if test="orgCntV3 != null">
        org_cnt_v3,
      </if>
      <if test="cntV3 != null">
        cnt_v3,
      </if>
      <if test="cntV3V2 != null">
        cnt_v3_v2,
      </if>
      <if test="cntXz != null">
        cnt_xz,
      </if>
      <if test="cntTc != null">
        cnt_tc,
      </if>
      <if test="avgSkuCnt != null">
        avg_sku_cnt,
      </if>
      <if test="avgSkuCntV3V2 != null">
        avg_sku_cnt_v3_v2,
      </if>
      <if test="avgSkuCntTc != null">
        avg_sku_cnt_tc,
      </if>
      <if test="avgSkuCntJc != null">
        avg_sku_cnt_jc,
      </if>
      <if test="avgSkuCntDc != null">
        avg_sku_cnt_dc,
      </if>
      <if test="avgSkuCntAa != null">
        avg_sku_cnt_aa,
      </if>
      <if test="avgSkuCntA1 != null">
        avg_sku_cnt_a1,
      </if>
      <if test="amtCum30Total != null">
        amt_cum_30_total,
      </if>
      <if test="amtCum30RateV3Total != null">
        amt_cum_30_rate_v3_total,
      </if>
      <if test="amtCum30RateV3V2Total != null">
        amt_cum_30_rate_v3_v2_total,
      </if>
      <if test="amtCum30V3V2Total != null">
        amt_cum_30_v3_v2_total,
      </if>
      <if test="amtCum30V3V21Total != null">
        amt_cum_30_v3_v2_1_total,
      </if>
      <if test="amtCum30RateV3TotalAa != null">
        amt_cum_30_rate_v3_total_aa,
      </if>
      <if test="amtCum30RateV3TotalA1 != null">
        amt_cum_30_rate_v3_total_a1,
      </if>
      <if test="amtCum30RateV3TotalTc != null">
        amt_cum_30_rate_v3_total_tc,
      </if>
      <if test="amtCum30RateV3TotalJc != null">
        amt_cum_30_rate_v3_total_jc,
      </if>
      <if test="amtCum30RateV3TotalDc != null">
        amt_cum_30_rate_v3_total_dc,
      </if>
      <if test="amtCum30RateV3TotalOn != null">
        amt_cum_30_rate_v3_total_on,
      </if>
      <if test="amtCum30RateV3TotalOff != null">
        amt_cum_30_rate_v3_total_off,
      </if>
      <if test="profitCum30V3Total != null">
        profit_cum_30_v3_total,
      </if>
      <if test="profitCum30V3V2Total != null">
        profit_cum_30_v3_v2_total,
      </if>
      <if test="profitCum30V3V21Total != null">
        profit_cum_30_v3_v2_1_total,
      </if>
      <if test="profitCum30RateV3Total != null">
        profit_cum_30_rate_v3_total,
      </if>
      <if test="amtCum30RateGxV3Total != null">
        amt_cum_30_rate_gx_v3_total,
      </if>
      <if test="profitCum30RateV3V2Total != null">
        profit_cum_30_rate_v3_v2_total,
      </if>
      <if test="profitCum30RateV3TotalAa != null">
        profit_cum_30_rate_v3_total_aa,
      </if>
      <if test="profitCum30RateV3TotalA1 != null">
        profit_cum_30_rate_v3_total_a1,
      </if>
      <if test="profitCum30RateV3TotalTc != null">
        profit_cum_30_rate_v3_total_tc,
      </if>
      <if test="profitCum30RateV3TotalJc != null">
        profit_cum_30_rate_v3_total_jc,
      </if>
      <if test="profitCum30RateV3TotalDc != null">
        profit_cum_30_rate_v3_total_dc,
      </if>
      <if test="profitCum30RateV3TotalOn != null">
        profit_cum_30_rate_v3_total_on,
      </if>
      <if test="profitCum30RateV3TotalOff != null">
        profit_cum_30_rate_v3_total_off,
      </if>
      <if test="avgCnt != null">
        avg_cnt,
      </if>
      <if test="amtCum30Null != null">
        amt_cum_30_null,
      </if>
      <if test="avgAmtCum30Null != null">
        avg_amt_cum_30_null,
      </if>
      <if test="amtCum30New != null">
        amt_cum_30_new,
      </if>
      <if test="avgAmtCum30New != null">
        avg_amt_cum_30_new,
      </if>
      <if test="amtCum30RateNew != null">
        amt_cum_30_rate_new,
      </if>
      <if test="profitCum30New != null">
        profit_cum_30_new,
      </if>
      <if test="profitCum30RateNew != null">
        profit_cum_30_rate_new,
      </if>
      <if test="amtCum30V3 != null">
        amt_cum_30_v3,
      </if>
      <if test="amtCum30RateV3 != null">
        amt_cum_30_rate_v3,
      </if>
      <if test="amtCum30RateV3V2 != null">
        amt_cum_30_rate_v3_v2,
      </if>
      <if test="amtCum30V3V2 != null">
        amt_cum_30_v3_v2,
      </if>
      <if test="amtCum30V3V21 != null">
        amt_cum_30_v3_v2_1,
      </if>
      <if test="amtCum30RateV3Aa != null">
        amt_cum_30_rate_v3_aa,
      </if>
      <if test="amtCum30RateV3A1 != null">
        amt_cum_30_rate_v3_a1,
      </if>
      <if test="amtCum30RateV3Tc != null">
        amt_cum_30_rate_v3_tc,
      </if>
      <if test="amtCum30RateV3Jc != null">
        amt_cum_30_rate_v3_jc,
      </if>
      <if test="amtCum30RateV3Dc != null">
        amt_cum_30_rate_v3_dc,
      </if>
      <if test="amtCum30RateV3On != null">
        amt_cum_30_rate_v3_on,
      </if>
      <if test="amtCum30RateV3Off != null">
        amt_cum_30_rate_v3_off,
      </if>
      <if test="profitCum30V3 != null">
        profit_cum_30_v3,
      </if>
      <if test="profitCum30V3V2 != null">
        profit_cum_30_v3_v2,
      </if>
      <if test="profitCum30V3V21 != null">
        profit_cum_30_v3_v2_1,
      </if>
      <if test="profitCum30RateV3 != null">
        profit_cum_30_rate_v3,
      </if>
      <if test="amtCum30RateGxV3 != null">
        amt_cum_30_rate_gx_v3,
      </if>
      <if test="profitCum30RateV3V2 != null">
        profit_cum_30_rate_v3_v2,
      </if>
      <if test="profitCum30RateV3Aa != null">
        profit_cum_30_rate_v3_aa,
      </if>
      <if test="profitCum30RateV3A1 != null">
        profit_cum_30_rate_v3_a1,
      </if>
      <if test="profitCum30RateV3Tc != null">
        profit_cum_30_rate_v3_tc,
      </if>
      <if test="profitCum30RateV3Jc != null">
        profit_cum_30_rate_v3_jc,
      </if>
      <if test="profitCum30RateV3Dc != null">
        profit_cum_30_rate_v3_dc,
      </if>
      <if test="profitCum30RateV3On != null">
        profit_cum_30_rate_v3_on,
      </if>
      <if test="profitCum30RateV3Off != null">
        profit_cum_30_rate_v3_off,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="chainName != null">
        #{chainName,jdbcType=VARCHAR},
      </if>
      <if test="reviseTypes != null">
        #{reviseTypes,jdbcType=VARCHAR},
      </if>
      <if test="orgCntV2 != null">
        #{orgCntV2,jdbcType=VARCHAR},
      </if>
      <if test="orgCntV3 != null">
        #{orgCntV3,jdbcType=VARCHAR},
      </if>
      <if test="cntV3 != null">
        #{cntV3,jdbcType=VARCHAR},
      </if>
      <if test="cntV3V2 != null">
        #{cntV3V2,jdbcType=VARCHAR},
      </if>
      <if test="cntXz != null">
        #{cntXz,jdbcType=VARCHAR},
      </if>
      <if test="cntTc != null">
        #{cntTc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCnt != null">
        #{avgSkuCnt,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntV3V2 != null">
        #{avgSkuCntV3V2,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntTc != null">
        #{avgSkuCntTc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntJc != null">
        #{avgSkuCntJc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntDc != null">
        #{avgSkuCntDc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntAa != null">
        #{avgSkuCntAa,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntA1 != null">
        #{avgSkuCntA1,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Total != null">
        #{amtCum30Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Total != null">
        #{amtCum30RateV3Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3V2Total != null">
        #{amtCum30RateV3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V2Total != null">
        #{amtCum30V3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V21Total != null">
        #{amtCum30V3V21Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalAa != null">
        #{amtCum30RateV3TotalAa,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalA1 != null">
        #{amtCum30RateV3TotalA1,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalTc != null">
        #{amtCum30RateV3TotalTc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalJc != null">
        #{amtCum30RateV3TotalJc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalDc != null">
        #{amtCum30RateV3TotalDc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalOn != null">
        #{amtCum30RateV3TotalOn,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalOff != null">
        #{amtCum30RateV3TotalOff,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3Total != null">
        #{profitCum30V3Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V2Total != null">
        #{profitCum30V3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V21Total != null">
        #{profitCum30V3V21Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Total != null">
        #{profitCum30RateV3Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateGxV3Total != null">
        #{amtCum30RateGxV3Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3V2Total != null">
        #{profitCum30RateV3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalAa != null">
        #{profitCum30RateV3TotalAa,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalA1 != null">
        #{profitCum30RateV3TotalA1,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalTc != null">
        #{profitCum30RateV3TotalTc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalJc != null">
        #{profitCum30RateV3TotalJc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalDc != null">
        #{profitCum30RateV3TotalDc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalOn != null">
        #{profitCum30RateV3TotalOn,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalOff != null">
        #{profitCum30RateV3TotalOff,jdbcType=VARCHAR},
      </if>
      <if test="avgCnt != null">
        #{avgCnt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Null != null">
        #{amtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30Null != null">
        #{avgAmtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30New != null">
        #{amtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30New != null">
        #{avgAmtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNew != null">
        #{amtCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30New != null">
        #{profitCum30New,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateNew != null">
        #{profitCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3 != null">
        #{amtCum30V3,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3 != null">
        #{amtCum30RateV3,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3V2 != null">
        #{amtCum30RateV3V2,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V2 != null">
        #{amtCum30V3V2,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V21 != null">
        #{amtCum30V3V21,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Aa != null">
        #{amtCum30RateV3Aa,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3A1 != null">
        #{amtCum30RateV3A1,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Tc != null">
        #{amtCum30RateV3Tc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Jc != null">
        #{amtCum30RateV3Jc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Dc != null">
        #{amtCum30RateV3Dc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3On != null">
        #{amtCum30RateV3On,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Off != null">
        #{amtCum30RateV3Off,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3 != null">
        #{profitCum30V3,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V2 != null">
        #{profitCum30V3V2,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V21 != null">
        #{profitCum30V3V21,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3 != null">
        #{profitCum30RateV3,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateGxV3 != null">
        #{amtCum30RateGxV3,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3V2 != null">
        #{profitCum30RateV3V2,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Aa != null">
        #{profitCum30RateV3Aa,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3A1 != null">
        #{profitCum30RateV3A1,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Tc != null">
        #{profitCum30RateV3Tc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Jc != null">
        #{profitCum30RateV3Jc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Dc != null">
        #{profitCum30RateV3Dc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3On != null">
        #{profitCum30RateV3On,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Off != null">
        #{profitCum30RateV3Off,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyseExample" resultType="java.lang.Long">
    select count(*) from track_retult_efficiency_analyse
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_retult_efficiency_analyse
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.zoneNew != null">
        zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="record.chainName != null">
        chain_name = #{record.chainName,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseTypes != null">
        revise_types = #{record.reviseTypes,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCntV2 != null">
        org_cnt_v2 = #{record.orgCntV2,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCntV3 != null">
        org_cnt_v3 = #{record.orgCntV3,jdbcType=VARCHAR},
      </if>
      <if test="record.cntV3 != null">
        cnt_v3 = #{record.cntV3,jdbcType=VARCHAR},
      </if>
      <if test="record.cntV3V2 != null">
        cnt_v3_v2 = #{record.cntV3V2,jdbcType=VARCHAR},
      </if>
      <if test="record.cntXz != null">
        cnt_xz = #{record.cntXz,jdbcType=VARCHAR},
      </if>
      <if test="record.cntTc != null">
        cnt_tc = #{record.cntTc,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCnt != null">
        avg_sku_cnt = #{record.avgSkuCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntV3V2 != null">
        avg_sku_cnt_v3_v2 = #{record.avgSkuCntV3V2,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntTc != null">
        avg_sku_cnt_tc = #{record.avgSkuCntTc,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntJc != null">
        avg_sku_cnt_jc = #{record.avgSkuCntJc,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntDc != null">
        avg_sku_cnt_dc = #{record.avgSkuCntDc,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntAa != null">
        avg_sku_cnt_aa = #{record.avgSkuCntAa,jdbcType=VARCHAR},
      </if>
      <if test="record.avgSkuCntA1 != null">
        avg_sku_cnt_a1 = #{record.avgSkuCntA1,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Total != null">
        amt_cum_30_total = #{record.amtCum30Total,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3Total != null">
        amt_cum_30_rate_v3_total = #{record.amtCum30RateV3Total,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3V2Total != null">
        amt_cum_30_rate_v3_v2_total = #{record.amtCum30RateV3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30V3V2Total != null">
        amt_cum_30_v3_v2_total = #{record.amtCum30V3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30V3V21Total != null">
        amt_cum_30_v3_v2_1_total = #{record.amtCum30V3V21Total,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalAa != null">
        amt_cum_30_rate_v3_total_aa = #{record.amtCum30RateV3TotalAa,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalA1 != null">
        amt_cum_30_rate_v3_total_a1 = #{record.amtCum30RateV3TotalA1,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalTc != null">
        amt_cum_30_rate_v3_total_tc = #{record.amtCum30RateV3TotalTc,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalJc != null">
        amt_cum_30_rate_v3_total_jc = #{record.amtCum30RateV3TotalJc,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalDc != null">
        amt_cum_30_rate_v3_total_dc = #{record.amtCum30RateV3TotalDc,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalOn != null">
        amt_cum_30_rate_v3_total_on = #{record.amtCum30RateV3TotalOn,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3TotalOff != null">
        amt_cum_30_rate_v3_total_off = #{record.amtCum30RateV3TotalOff,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30V3Total != null">
        profit_cum_30_v3_total = #{record.profitCum30V3Total,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30V3V2Total != null">
        profit_cum_30_v3_v2_total = #{record.profitCum30V3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30V3V21Total != null">
        profit_cum_30_v3_v2_1_total = #{record.profitCum30V3V21Total,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3Total != null">
        profit_cum_30_rate_v3_total = #{record.profitCum30RateV3Total,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateGxV3Total != null">
        amt_cum_30_rate_gx_v3_total = #{record.amtCum30RateGxV3Total,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3V2Total != null">
        profit_cum_30_rate_v3_v2_total = #{record.profitCum30RateV3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalAa != null">
        profit_cum_30_rate_v3_total_aa = #{record.profitCum30RateV3TotalAa,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalA1 != null">
        profit_cum_30_rate_v3_total_a1 = #{record.profitCum30RateV3TotalA1,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalTc != null">
        profit_cum_30_rate_v3_total_tc = #{record.profitCum30RateV3TotalTc,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalJc != null">
        profit_cum_30_rate_v3_total_jc = #{record.profitCum30RateV3TotalJc,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalDc != null">
        profit_cum_30_rate_v3_total_dc = #{record.profitCum30RateV3TotalDc,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalOn != null">
        profit_cum_30_rate_v3_total_on = #{record.profitCum30RateV3TotalOn,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3TotalOff != null">
        profit_cum_30_rate_v3_total_off = #{record.profitCum30RateV3TotalOff,jdbcType=VARCHAR},
      </if>
      <if test="record.avgCnt != null">
        avg_cnt = #{record.avgCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30Null != null">
        amt_cum_30_null = #{record.amtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="record.avgAmtCum30Null != null">
        avg_amt_cum_30_null = #{record.avgAmtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30New != null">
        amt_cum_30_new = #{record.amtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.avgAmtCum30New != null">
        avg_amt_cum_30_new = #{record.avgAmtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateNew != null">
        amt_cum_30_rate_new = #{record.amtCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30New != null">
        profit_cum_30_new = #{record.profitCum30New,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateNew != null">
        profit_cum_30_rate_new = #{record.profitCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30V3 != null">
        amt_cum_30_v3 = #{record.amtCum30V3,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3 != null">
        amt_cum_30_rate_v3 = #{record.amtCum30RateV3,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3V2 != null">
        amt_cum_30_rate_v3_v2 = #{record.amtCum30RateV3V2,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30V3V2 != null">
        amt_cum_30_v3_v2 = #{record.amtCum30V3V2,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30V3V21 != null">
        amt_cum_30_v3_v2_1 = #{record.amtCum30V3V21,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3Aa != null">
        amt_cum_30_rate_v3_aa = #{record.amtCum30RateV3Aa,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3A1 != null">
        amt_cum_30_rate_v3_a1 = #{record.amtCum30RateV3A1,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3Tc != null">
        amt_cum_30_rate_v3_tc = #{record.amtCum30RateV3Tc,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3Jc != null">
        amt_cum_30_rate_v3_jc = #{record.amtCum30RateV3Jc,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3Dc != null">
        amt_cum_30_rate_v3_dc = #{record.amtCum30RateV3Dc,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3On != null">
        amt_cum_30_rate_v3_on = #{record.amtCum30RateV3On,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateV3Off != null">
        amt_cum_30_rate_v3_off = #{record.amtCum30RateV3Off,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30V3 != null">
        profit_cum_30_v3 = #{record.profitCum30V3,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30V3V2 != null">
        profit_cum_30_v3_v2 = #{record.profitCum30V3V2,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30V3V21 != null">
        profit_cum_30_v3_v2_1 = #{record.profitCum30V3V21,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3 != null">
        profit_cum_30_rate_v3 = #{record.profitCum30RateV3,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum30RateGxV3 != null">
        amt_cum_30_rate_gx_v3 = #{record.amtCum30RateGxV3,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3V2 != null">
        profit_cum_30_rate_v3_v2 = #{record.profitCum30RateV3V2,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3Aa != null">
        profit_cum_30_rate_v3_aa = #{record.profitCum30RateV3Aa,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3A1 != null">
        profit_cum_30_rate_v3_a1 = #{record.profitCum30RateV3A1,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3Tc != null">
        profit_cum_30_rate_v3_tc = #{record.profitCum30RateV3Tc,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3Jc != null">
        profit_cum_30_rate_v3_jc = #{record.profitCum30RateV3Jc,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3Dc != null">
        profit_cum_30_rate_v3_dc = #{record.profitCum30RateV3Dc,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3On != null">
        profit_cum_30_rate_v3_on = #{record.profitCum30RateV3On,jdbcType=VARCHAR},
      </if>
      <if test="record.profitCum30RateV3Off != null">
        profit_cum_30_rate_v3_off = #{record.profitCum30RateV3Off,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_retult_efficiency_analyse
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      chain_name = #{record.chainName,jdbcType=VARCHAR},
      revise_types = #{record.reviseTypes,jdbcType=VARCHAR},
      org_cnt_v2 = #{record.orgCntV2,jdbcType=VARCHAR},
      org_cnt_v3 = #{record.orgCntV3,jdbcType=VARCHAR},
      cnt_v3 = #{record.cntV3,jdbcType=VARCHAR},
      cnt_v3_v2 = #{record.cntV3V2,jdbcType=VARCHAR},
      cnt_xz = #{record.cntXz,jdbcType=VARCHAR},
      cnt_tc = #{record.cntTc,jdbcType=VARCHAR},
      avg_sku_cnt = #{record.avgSkuCnt,jdbcType=VARCHAR},
      avg_sku_cnt_v3_v2 = #{record.avgSkuCntV3V2,jdbcType=VARCHAR},
      avg_sku_cnt_tc = #{record.avgSkuCntTc,jdbcType=VARCHAR},
      avg_sku_cnt_jc = #{record.avgSkuCntJc,jdbcType=VARCHAR},
      avg_sku_cnt_dc = #{record.avgSkuCntDc,jdbcType=VARCHAR},
      avg_sku_cnt_aa = #{record.avgSkuCntAa,jdbcType=VARCHAR},
      avg_sku_cnt_a1 = #{record.avgSkuCntA1,jdbcType=VARCHAR},
      amt_cum_30_total = #{record.amtCum30Total,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total = #{record.amtCum30RateV3Total,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_v2_total = #{record.amtCum30RateV3V2Total,jdbcType=VARCHAR},
      amt_cum_30_v3_v2_total = #{record.amtCum30V3V2Total,jdbcType=VARCHAR},
      amt_cum_30_v3_v2_1_total = #{record.amtCum30V3V21Total,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_aa = #{record.amtCum30RateV3TotalAa,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_a1 = #{record.amtCum30RateV3TotalA1,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_tc = #{record.amtCum30RateV3TotalTc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_jc = #{record.amtCum30RateV3TotalJc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_dc = #{record.amtCum30RateV3TotalDc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_on = #{record.amtCum30RateV3TotalOn,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_off = #{record.amtCum30RateV3TotalOff,jdbcType=VARCHAR},
      profit_cum_30_v3_total = #{record.profitCum30V3Total,jdbcType=VARCHAR},
      profit_cum_30_v3_v2_total = #{record.profitCum30V3V2Total,jdbcType=VARCHAR},
      profit_cum_30_v3_v2_1_total = #{record.profitCum30V3V21Total,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total = #{record.profitCum30RateV3Total,jdbcType=VARCHAR},
      amt_cum_30_rate_gx_v3_total = #{record.amtCum30RateGxV3Total,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_v2_total = #{record.profitCum30RateV3V2Total,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_aa = #{record.profitCum30RateV3TotalAa,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_a1 = #{record.profitCum30RateV3TotalA1,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_tc = #{record.profitCum30RateV3TotalTc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_jc = #{record.profitCum30RateV3TotalJc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_dc = #{record.profitCum30RateV3TotalDc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_on = #{record.profitCum30RateV3TotalOn,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_off = #{record.profitCum30RateV3TotalOff,jdbcType=VARCHAR},
      avg_cnt = #{record.avgCnt,jdbcType=VARCHAR},
      amt_cum_30_null = #{record.amtCum30Null,jdbcType=VARCHAR},
      avg_amt_cum_30_null = #{record.avgAmtCum30Null,jdbcType=VARCHAR},
      amt_cum_30_new = #{record.amtCum30New,jdbcType=VARCHAR},
      avg_amt_cum_30_new = #{record.avgAmtCum30New,jdbcType=VARCHAR},
      amt_cum_30_rate_new = #{record.amtCum30RateNew,jdbcType=VARCHAR},
      profit_cum_30_new = #{record.profitCum30New,jdbcType=VARCHAR},
      profit_cum_30_rate_new = #{record.profitCum30RateNew,jdbcType=VARCHAR},
      amt_cum_30_v3 = #{record.amtCum30V3,jdbcType=VARCHAR},
      amt_cum_30_rate_v3 = #{record.amtCum30RateV3,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_v2 = #{record.amtCum30RateV3V2,jdbcType=VARCHAR},
      amt_cum_30_v3_v2 = #{record.amtCum30V3V2,jdbcType=VARCHAR},
      amt_cum_30_v3_v2_1 = #{record.amtCum30V3V21,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_aa = #{record.amtCum30RateV3Aa,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_a1 = #{record.amtCum30RateV3A1,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_tc = #{record.amtCum30RateV3Tc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_jc = #{record.amtCum30RateV3Jc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_dc = #{record.amtCum30RateV3Dc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_on = #{record.amtCum30RateV3On,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_off = #{record.amtCum30RateV3Off,jdbcType=VARCHAR},
      profit_cum_30_v3 = #{record.profitCum30V3,jdbcType=VARCHAR},
      profit_cum_30_v3_v2 = #{record.profitCum30V3V2,jdbcType=VARCHAR},
      profit_cum_30_v3_v2_1 = #{record.profitCum30V3V21,jdbcType=VARCHAR},
      profit_cum_30_rate_v3 = #{record.profitCum30RateV3,jdbcType=VARCHAR},
      amt_cum_30_rate_gx_v3 = #{record.amtCum30RateGxV3,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_v2 = #{record.profitCum30RateV3V2,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_aa = #{record.profitCum30RateV3Aa,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_a1 = #{record.profitCum30RateV3A1,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_tc = #{record.profitCum30RateV3Tc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_jc = #{record.profitCum30RateV3Jc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_dc = #{record.profitCum30RateV3Dc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_on = #{record.profitCum30RateV3On,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_off = #{record.profitCum30RateV3Off,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse">
    update track_retult_efficiency_analyse
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        zone_new = #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="chainName != null">
        chain_name = #{chainName,jdbcType=VARCHAR},
      </if>
      <if test="reviseTypes != null">
        revise_types = #{reviseTypes,jdbcType=VARCHAR},
      </if>
      <if test="orgCntV2 != null">
        org_cnt_v2 = #{orgCntV2,jdbcType=VARCHAR},
      </if>
      <if test="orgCntV3 != null">
        org_cnt_v3 = #{orgCntV3,jdbcType=VARCHAR},
      </if>
      <if test="cntV3 != null">
        cnt_v3 = #{cntV3,jdbcType=VARCHAR},
      </if>
      <if test="cntV3V2 != null">
        cnt_v3_v2 = #{cntV3V2,jdbcType=VARCHAR},
      </if>
      <if test="cntXz != null">
        cnt_xz = #{cntXz,jdbcType=VARCHAR},
      </if>
      <if test="cntTc != null">
        cnt_tc = #{cntTc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCnt != null">
        avg_sku_cnt = #{avgSkuCnt,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntV3V2 != null">
        avg_sku_cnt_v3_v2 = #{avgSkuCntV3V2,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntTc != null">
        avg_sku_cnt_tc = #{avgSkuCntTc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntJc != null">
        avg_sku_cnt_jc = #{avgSkuCntJc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntDc != null">
        avg_sku_cnt_dc = #{avgSkuCntDc,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntAa != null">
        avg_sku_cnt_aa = #{avgSkuCntAa,jdbcType=VARCHAR},
      </if>
      <if test="avgSkuCntA1 != null">
        avg_sku_cnt_a1 = #{avgSkuCntA1,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Total != null">
        amt_cum_30_total = #{amtCum30Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Total != null">
        amt_cum_30_rate_v3_total = #{amtCum30RateV3Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3V2Total != null">
        amt_cum_30_rate_v3_v2_total = #{amtCum30RateV3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V2Total != null">
        amt_cum_30_v3_v2_total = #{amtCum30V3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V21Total != null">
        amt_cum_30_v3_v2_1_total = #{amtCum30V3V21Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalAa != null">
        amt_cum_30_rate_v3_total_aa = #{amtCum30RateV3TotalAa,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalA1 != null">
        amt_cum_30_rate_v3_total_a1 = #{amtCum30RateV3TotalA1,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalTc != null">
        amt_cum_30_rate_v3_total_tc = #{amtCum30RateV3TotalTc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalJc != null">
        amt_cum_30_rate_v3_total_jc = #{amtCum30RateV3TotalJc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalDc != null">
        amt_cum_30_rate_v3_total_dc = #{amtCum30RateV3TotalDc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalOn != null">
        amt_cum_30_rate_v3_total_on = #{amtCum30RateV3TotalOn,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3TotalOff != null">
        amt_cum_30_rate_v3_total_off = #{amtCum30RateV3TotalOff,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3Total != null">
        profit_cum_30_v3_total = #{profitCum30V3Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V2Total != null">
        profit_cum_30_v3_v2_total = #{profitCum30V3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V21Total != null">
        profit_cum_30_v3_v2_1_total = #{profitCum30V3V21Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Total != null">
        profit_cum_30_rate_v3_total = #{profitCum30RateV3Total,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateGxV3Total != null">
        amt_cum_30_rate_gx_v3_total = #{amtCum30RateGxV3Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3V2Total != null">
        profit_cum_30_rate_v3_v2_total = #{profitCum30RateV3V2Total,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalAa != null">
        profit_cum_30_rate_v3_total_aa = #{profitCum30RateV3TotalAa,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalA1 != null">
        profit_cum_30_rate_v3_total_a1 = #{profitCum30RateV3TotalA1,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalTc != null">
        profit_cum_30_rate_v3_total_tc = #{profitCum30RateV3TotalTc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalJc != null">
        profit_cum_30_rate_v3_total_jc = #{profitCum30RateV3TotalJc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalDc != null">
        profit_cum_30_rate_v3_total_dc = #{profitCum30RateV3TotalDc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalOn != null">
        profit_cum_30_rate_v3_total_on = #{profitCum30RateV3TotalOn,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3TotalOff != null">
        profit_cum_30_rate_v3_total_off = #{profitCum30RateV3TotalOff,jdbcType=VARCHAR},
      </if>
      <if test="avgCnt != null">
        avg_cnt = #{avgCnt,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30Null != null">
        amt_cum_30_null = #{amtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30Null != null">
        avg_amt_cum_30_null = #{avgAmtCum30Null,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30New != null">
        amt_cum_30_new = #{amtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="avgAmtCum30New != null">
        avg_amt_cum_30_new = #{avgAmtCum30New,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateNew != null">
        amt_cum_30_rate_new = #{amtCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30New != null">
        profit_cum_30_new = #{profitCum30New,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateNew != null">
        profit_cum_30_rate_new = #{profitCum30RateNew,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3 != null">
        amt_cum_30_v3 = #{amtCum30V3,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3 != null">
        amt_cum_30_rate_v3 = #{amtCum30RateV3,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3V2 != null">
        amt_cum_30_rate_v3_v2 = #{amtCum30RateV3V2,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V2 != null">
        amt_cum_30_v3_v2 = #{amtCum30V3V2,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30V3V21 != null">
        amt_cum_30_v3_v2_1 = #{amtCum30V3V21,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Aa != null">
        amt_cum_30_rate_v3_aa = #{amtCum30RateV3Aa,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3A1 != null">
        amt_cum_30_rate_v3_a1 = #{amtCum30RateV3A1,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Tc != null">
        amt_cum_30_rate_v3_tc = #{amtCum30RateV3Tc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Jc != null">
        amt_cum_30_rate_v3_jc = #{amtCum30RateV3Jc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Dc != null">
        amt_cum_30_rate_v3_dc = #{amtCum30RateV3Dc,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3On != null">
        amt_cum_30_rate_v3_on = #{amtCum30RateV3On,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateV3Off != null">
        amt_cum_30_rate_v3_off = #{amtCum30RateV3Off,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3 != null">
        profit_cum_30_v3 = #{profitCum30V3,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V2 != null">
        profit_cum_30_v3_v2 = #{profitCum30V3V2,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30V3V21 != null">
        profit_cum_30_v3_v2_1 = #{profitCum30V3V21,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3 != null">
        profit_cum_30_rate_v3 = #{profitCum30RateV3,jdbcType=VARCHAR},
      </if>
      <if test="amtCum30RateGxV3 != null">
        amt_cum_30_rate_gx_v3 = #{amtCum30RateGxV3,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3V2 != null">
        profit_cum_30_rate_v3_v2 = #{profitCum30RateV3V2,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Aa != null">
        profit_cum_30_rate_v3_aa = #{profitCum30RateV3Aa,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3A1 != null">
        profit_cum_30_rate_v3_a1 = #{profitCum30RateV3A1,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Tc != null">
        profit_cum_30_rate_v3_tc = #{profitCum30RateV3Tc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Jc != null">
        profit_cum_30_rate_v3_jc = #{profitCum30RateV3Jc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Dc != null">
        profit_cum_30_rate_v3_dc = #{profitCum30RateV3Dc,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3On != null">
        profit_cum_30_rate_v3_on = #{profitCum30RateV3On,jdbcType=VARCHAR},
      </if>
      <if test="profitCum30RateV3Off != null">
        profit_cum_30_rate_v3_off = #{profitCum30RateV3Off,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse">
    update track_retult_efficiency_analyse
    set task_id = #{taskId,jdbcType=BIGINT},
      zone_new = #{zoneNew,jdbcType=VARCHAR},
      chain_name = #{chainName,jdbcType=VARCHAR},
      revise_types = #{reviseTypes,jdbcType=VARCHAR},
      org_cnt_v2 = #{orgCntV2,jdbcType=VARCHAR},
      org_cnt_v3 = #{orgCntV3,jdbcType=VARCHAR},
      cnt_v3 = #{cntV3,jdbcType=VARCHAR},
      cnt_v3_v2 = #{cntV3V2,jdbcType=VARCHAR},
      cnt_xz = #{cntXz,jdbcType=VARCHAR},
      cnt_tc = #{cntTc,jdbcType=VARCHAR},
      avg_sku_cnt = #{avgSkuCnt,jdbcType=VARCHAR},
      avg_sku_cnt_v3_v2 = #{avgSkuCntV3V2,jdbcType=VARCHAR},
      avg_sku_cnt_tc = #{avgSkuCntTc,jdbcType=VARCHAR},
      avg_sku_cnt_jc = #{avgSkuCntJc,jdbcType=VARCHAR},
      avg_sku_cnt_dc = #{avgSkuCntDc,jdbcType=VARCHAR},
      avg_sku_cnt_aa = #{avgSkuCntAa,jdbcType=VARCHAR},
      avg_sku_cnt_a1 = #{avgSkuCntA1,jdbcType=VARCHAR},
      amt_cum_30_total = #{amtCum30Total,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total = #{amtCum30RateV3Total,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_v2_total = #{amtCum30RateV3V2Total,jdbcType=VARCHAR},
      amt_cum_30_v3_v2_total = #{amtCum30V3V2Total,jdbcType=VARCHAR},
      amt_cum_30_v3_v2_1_total = #{amtCum30V3V21Total,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_aa = #{amtCum30RateV3TotalAa,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_a1 = #{amtCum30RateV3TotalA1,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_tc = #{amtCum30RateV3TotalTc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_jc = #{amtCum30RateV3TotalJc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_dc = #{amtCum30RateV3TotalDc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_on = #{amtCum30RateV3TotalOn,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_total_off = #{amtCum30RateV3TotalOff,jdbcType=VARCHAR},
      profit_cum_30_v3_total = #{profitCum30V3Total,jdbcType=VARCHAR},
      profit_cum_30_v3_v2_total = #{profitCum30V3V2Total,jdbcType=VARCHAR},
      profit_cum_30_v3_v2_1_total = #{profitCum30V3V21Total,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total = #{profitCum30RateV3Total,jdbcType=VARCHAR},
      amt_cum_30_rate_gx_v3_total = #{amtCum30RateGxV3Total,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_v2_total = #{profitCum30RateV3V2Total,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_aa = #{profitCum30RateV3TotalAa,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_a1 = #{profitCum30RateV3TotalA1,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_tc = #{profitCum30RateV3TotalTc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_jc = #{profitCum30RateV3TotalJc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_dc = #{profitCum30RateV3TotalDc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_on = #{profitCum30RateV3TotalOn,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_total_off = #{profitCum30RateV3TotalOff,jdbcType=VARCHAR},
      avg_cnt = #{avgCnt,jdbcType=VARCHAR},
      amt_cum_30_null = #{amtCum30Null,jdbcType=VARCHAR},
      avg_amt_cum_30_null = #{avgAmtCum30Null,jdbcType=VARCHAR},
      amt_cum_30_new = #{amtCum30New,jdbcType=VARCHAR},
      avg_amt_cum_30_new = #{avgAmtCum30New,jdbcType=VARCHAR},
      amt_cum_30_rate_new = #{amtCum30RateNew,jdbcType=VARCHAR},
      profit_cum_30_new = #{profitCum30New,jdbcType=VARCHAR},
      profit_cum_30_rate_new = #{profitCum30RateNew,jdbcType=VARCHAR},
      amt_cum_30_v3 = #{amtCum30V3,jdbcType=VARCHAR},
      amt_cum_30_rate_v3 = #{amtCum30RateV3,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_v2 = #{amtCum30RateV3V2,jdbcType=VARCHAR},
      amt_cum_30_v3_v2 = #{amtCum30V3V2,jdbcType=VARCHAR},
      amt_cum_30_v3_v2_1 = #{amtCum30V3V21,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_aa = #{amtCum30RateV3Aa,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_a1 = #{amtCum30RateV3A1,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_tc = #{amtCum30RateV3Tc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_jc = #{amtCum30RateV3Jc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_dc = #{amtCum30RateV3Dc,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_on = #{amtCum30RateV3On,jdbcType=VARCHAR},
      amt_cum_30_rate_v3_off = #{amtCum30RateV3Off,jdbcType=VARCHAR},
      profit_cum_30_v3 = #{profitCum30V3,jdbcType=VARCHAR},
      profit_cum_30_v3_v2 = #{profitCum30V3V2,jdbcType=VARCHAR},
      profit_cum_30_v3_v2_1 = #{profitCum30V3V21,jdbcType=VARCHAR},
      profit_cum_30_rate_v3 = #{profitCum30RateV3,jdbcType=VARCHAR},
      amt_cum_30_rate_gx_v3 = #{amtCum30RateGxV3,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_v2 = #{profitCum30RateV3V2,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_aa = #{profitCum30RateV3Aa,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_a1 = #{profitCum30RateV3A1,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_tc = #{profitCum30RateV3Tc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_jc = #{profitCum30RateV3Jc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_dc = #{profitCum30RateV3Dc,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_on = #{profitCum30RateV3On,jdbcType=VARCHAR},
      profit_cum_30_rate_v3_off = #{profitCum30RateV3Off,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>