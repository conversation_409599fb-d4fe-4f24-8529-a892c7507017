<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.JymlStoreSkuConfirmDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="process_detail_id" jdbcType="BIGINT" property="processDetailId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="category_path" jdbcType="VARCHAR" property="categoryPath" />
    <result column="system_suggest" jdbcType="INTEGER" property="systemSuggest" />
    <result column="previous_review_result" jdbcType="INTEGER" property="previousReviewResult" />
    <result column="my_confirm" jdbcType="INTEGER" property="myConfirm" />
    <result column="review_result" jdbcType="INTEGER" property="reviewResult" />
    <result column="submit_by" jdbcType="BIGINT" property="submitBy" />
    <result column="submit_name" jdbcType="VARCHAR" property="submitName" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="review_by" jdbcType="BIGINT" property="reviewBy" />
    <result column="review_name" jdbcType="VARCHAR" property="reviewName" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_org_id, store_id, store_code, process_detail_id, goods_no, category, 
    middle_category, small_category, sub_category, category_path, system_suggest, previous_review_result, 
    my_confirm, review_result, submit_by, submit_name, submit_time, review_by, review_name, 
    review_time, version, `status`, gmt_create, gmt_update, extend, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_confirm_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_store_sku_confirm_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_confirm_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetailExample">
    delete from jyml_store_sku_confirm_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>

  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail">
    insert into jyml_store_sku_confirm_detail (id, business_org_id, store_id, 
      store_code, process_detail_id, goods_no, 
      category, middle_category, small_category, 
      sub_category, category_path, system_suggest, 
      previous_review_result, my_confirm, review_result, 
      submit_by, submit_name, submit_time, 
      review_by, review_name, review_time, 
      version, `status`, gmt_create, 
      gmt_update, extend, created_by, 
      created_name, updated_by, updated_name
      )
    values (#{id,jdbcType=BIGINT}, #{businessOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{processDetailId,jdbcType=BIGINT}, #{goodsNo,jdbcType=VARCHAR}, 
      #{category,jdbcType=VARCHAR}, #{middleCategory,jdbcType=VARCHAR}, #{smallCategory,jdbcType=VARCHAR}, 
      #{subCategory,jdbcType=VARCHAR}, #{categoryPath,jdbcType=VARCHAR}, #{systemSuggest,jdbcType=INTEGER}, 
      #{previousReviewResult,jdbcType=INTEGER}, #{myConfirm,jdbcType=INTEGER}, #{reviewResult,jdbcType=INTEGER}, 
      #{submitBy,jdbcType=BIGINT}, #{submitName,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{reviewBy,jdbcType=BIGINT}, #{reviewName,jdbcType=VARCHAR}, #{reviewTime,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail">
    insert into jyml_store_sku_confirm_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="processDetailId != null">
        process_detail_id,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="middleCategory != null">
        middle_category,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="categoryPath != null">
        category_path,
      </if>
      <if test="systemSuggest != null">
        system_suggest,
      </if>
      <if test="previousReviewResult != null">
        previous_review_result,
      </if>
      <if test="myConfirm != null">
        my_confirm,
      </if>
      <if test="reviewResult != null">
        review_result,
      </if>
      <if test="submitBy != null">
        submit_by,
      </if>
      <if test="submitName != null">
        submit_name,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="reviewBy != null">
        review_by,
      </if>
      <if test="reviewName != null">
        review_name,
      </if>
      <if test="reviewTime != null">
        review_time,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="processDetailId != null">
        #{processDetailId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="categoryPath != null">
        #{categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="systemSuggest != null">
        #{systemSuggest,jdbcType=INTEGER},
      </if>
      <if test="previousReviewResult != null">
        #{previousReviewResult,jdbcType=INTEGER},
      </if>
      <if test="myConfirm != null">
        #{myConfirm,jdbcType=INTEGER},
      </if>
      <if test="reviewResult != null">
        #{reviewResult,jdbcType=INTEGER},
      </if>
      <if test="submitBy != null">
        #{submitBy,jdbcType=BIGINT},
      </if>
      <if test="submitName != null">
        #{submitName,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewBy != null">
        #{reviewBy,jdbcType=BIGINT},
      </if>
      <if test="reviewName != null">
        #{reviewName,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null">
        #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetailExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_confirm_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_confirm_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.processDetailId != null">
        process_detail_id = #{record.processDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryPath != null">
        category_path = #{record.categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="record.systemSuggest != null">
        system_suggest = #{record.systemSuggest,jdbcType=INTEGER},
      </if>
      <if test="record.previousReviewResult != null">
        previous_review_result = #{record.previousReviewResult,jdbcType=INTEGER},
      </if>
      <if test="record.myConfirm != null">
        my_confirm = #{record.myConfirm,jdbcType=INTEGER},
      </if>
      <if test="record.reviewResult != null">
        review_result = #{record.reviewResult,jdbcType=INTEGER},
      </if>
      <if test="record.submitBy != null">
        submit_by = #{record.submitBy,jdbcType=BIGINT},
      </if>
      <if test="record.submitName != null">
        submit_name = #{record.submitName,jdbcType=VARCHAR},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reviewBy != null">
        review_by = #{record.reviewBy,jdbcType=BIGINT},
      </if>
      <if test="record.reviewName != null">
        review_name = #{record.reviewName,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewTime != null">
        review_time = #{record.reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_confirm_detail
    set id = #{record.id,jdbcType=BIGINT},
      business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      process_detail_id = #{record.processDetailId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      small_category = #{record.smallCategory,jdbcType=VARCHAR},
      sub_category = #{record.subCategory,jdbcType=VARCHAR},
      category_path = #{record.categoryPath,jdbcType=VARCHAR},
      system_suggest = #{record.systemSuggest,jdbcType=INTEGER},
      previous_review_result = #{record.previousReviewResult,jdbcType=INTEGER},
      my_confirm = #{record.myConfirm,jdbcType=INTEGER},
      review_result = #{record.reviewResult,jdbcType=INTEGER},
      submit_by = #{record.submitBy,jdbcType=BIGINT},
      submit_name = #{record.submitName,jdbcType=VARCHAR},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      review_by = #{record.reviewBy,jdbcType=BIGINT},
      review_name = #{record.reviewName,jdbcType=VARCHAR},
      review_time = #{record.reviewTime,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=BIGINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail">
    update jyml_store_sku_confirm_detail
    <set>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="processDetailId != null">
        process_detail_id = #{processDetailId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        middle_category = #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="categoryPath != null">
        category_path = #{categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="systemSuggest != null">
        system_suggest = #{systemSuggest,jdbcType=INTEGER},
      </if>
      <if test="previousReviewResult != null">
        previous_review_result = #{previousReviewResult,jdbcType=INTEGER},
      </if>
      <if test="myConfirm != null">
        my_confirm = #{myConfirm,jdbcType=INTEGER},
      </if>
      <if test="reviewResult != null">
        review_result = #{reviewResult,jdbcType=INTEGER},
      </if>
      <if test="submitBy != null">
        submit_by = #{submitBy,jdbcType=BIGINT},
      </if>
      <if test="submitName != null">
        submit_name = #{submitName,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewBy != null">
        review_by = #{reviewBy,jdbcType=BIGINT},
      </if>
      <if test="reviewName != null">
        review_name = #{reviewName,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null">
        review_time = #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail">
    update jyml_store_sku_confirm_detail
    set business_org_id = #{businessOrgId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      process_detail_id = #{processDetailId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      middle_category = #{middleCategory,jdbcType=VARCHAR},
      small_category = #{smallCategory,jdbcType=VARCHAR},
      sub_category = #{subCategory,jdbcType=VARCHAR},
      category_path = #{categoryPath,jdbcType=VARCHAR},
      system_suggest = #{systemSuggest,jdbcType=INTEGER},
      previous_review_result = #{previousReviewResult,jdbcType=INTEGER},
      my_confirm = #{myConfirm,jdbcType=INTEGER},
      review_result = #{reviewResult,jdbcType=INTEGER},
      submit_by = #{submitBy,jdbcType=BIGINT},
      submit_name = #{submitName,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      review_by = #{reviewBy,jdbcType=BIGINT},
      review_name = #{reviewName,jdbcType=VARCHAR},
      review_time = #{reviewTime,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 批量插入方法 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into jyml_store_sku_confirm_detail (
      business_org_id, store_id, store_code, process_detail_id, goods_no, category, middle_category,
      small_category, sub_category, category_path, system_suggest, previous_review_result, my_confirm,
      review_result, submit_by, submit_name, submit_time, review_by, review_name, review_time,
      version, `status`, extend, created_by, created_name, updated_by, updated_name
    ) values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.businessOrgId,jdbcType=BIGINT},
        #{item.storeId,jdbcType=BIGINT},
        #{item.storeCode,jdbcType=VARCHAR},
        #{item.processDetailId,jdbcType=BIGINT},
        #{item.goodsNo,jdbcType=VARCHAR},
        #{item.category,jdbcType=VARCHAR},
        #{item.middleCategory,jdbcType=VARCHAR},
        #{item.smallCategory,jdbcType=VARCHAR},
        #{item.subCategory,jdbcType=VARCHAR},
        #{item.categoryPath,jdbcType=VARCHAR},
        #{item.systemSuggest,jdbcType=INTEGER},
        #{item.previousReviewResult,jdbcType=INTEGER},
        #{item.myConfirm,jdbcType=INTEGER},
        #{item.reviewResult,jdbcType=INTEGER},
        #{item.submitBy,jdbcType=BIGINT},
        #{item.submitName,jdbcType=VARCHAR},
        #{item.submitTime,jdbcType=TIMESTAMP},
        #{item.reviewBy,jdbcType=BIGINT},
        #{item.reviewName,jdbcType=VARCHAR},
        #{item.reviewTime,jdbcType=TIMESTAMP},
        #{item.version,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.extend,jdbcType=VARCHAR},
        #{item.createdBy,jdbcType=BIGINT},
        #{item.createdName,jdbcType=VARCHAR},
        #{item.updatedBy,jdbcType=BIGINT},
        #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>