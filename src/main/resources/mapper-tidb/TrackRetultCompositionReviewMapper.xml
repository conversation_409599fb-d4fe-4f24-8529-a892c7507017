<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackRetultCompositionReviewMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultCompositionReview">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="store_group" jdbcType="VARCHAR" property="storeGroup" />
    <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
    <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
    <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
    <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="sku_cnt" jdbcType="VARCHAR" property="skuCnt" />
    <result column="sku_cnt_total_2" jdbcType="VARCHAR" property="skuCntTotal2" />
    <result column="sku_cnt_2" jdbcType="VARCHAR" property="skuCnt2" />
    <result column="sku_cnt_new_2" jdbcType="VARCHAR" property="skuCntNew2" />
    <result column="num_cum_30_2" jdbcType="VARCHAR" property="numCum302" />
    <result column="amt_cum_30_2" jdbcType="VARCHAR" property="amtCum302" />
    <result column="amt_rate_30_2" jdbcType="VARCHAR" property="amtRate302" />
    <result column="profit_amt_cum_30_2" jdbcType="VARCHAR" property="profitAmtCum302" />
    <result column="profit_rate_30_2" jdbcType="VARCHAR" property="profitRate302" />
    <result column="amt_inv_new_2" jdbcType="VARCHAR" property="amtInvNew2" />
    <result column="sku_cnt_total_3" jdbcType="VARCHAR" property="skuCntTotal3" />
    <result column="sku_cnt_3" jdbcType="VARCHAR" property="skuCnt3" />
    <result column="sku_cnt_new_3" jdbcType="VARCHAR" property="skuCntNew3" />
    <result column="num_cum_30_3" jdbcType="VARCHAR" property="numCum303" />
    <result column="amt_cum_30_3" jdbcType="VARCHAR" property="amtCum303" />
    <result column="amt_rate_30_3" jdbcType="VARCHAR" property="amtRate303" />
    <result column="profit_amt_cum_30_3" jdbcType="VARCHAR" property="profitAmtCum303" />
    <result column="profit_rate_30_3" jdbcType="VARCHAR" property="profitRate303" />
    <result column="amt_inv_new_3" jdbcType="VARCHAR" property="amtInvNew3" />
    <result column="sku_cnt_total_4" jdbcType="VARCHAR" property="skuCntTotal4" />
    <result column="sku_cnt_4" jdbcType="VARCHAR" property="skuCnt4" />
    <result column="sku_cnt_new_4" jdbcType="VARCHAR" property="skuCntNew4" />
    <result column="num_cum_30_4" jdbcType="VARCHAR" property="numCum304" />
    <result column="amt_cum_30_4" jdbcType="VARCHAR" property="amtCum304" />
    <result column="amt_rate_30_4" jdbcType="VARCHAR" property="amtRate304" />
    <result column="profit_amt_cum_30_4" jdbcType="VARCHAR" property="profitAmtCum304" />
    <result column="profit_rate_30_4" jdbcType="VARCHAR" property="profitRate304" />
    <result column="amt_inv_new_4" jdbcType="VARCHAR" property="amtInvNew4" />
    <result column="sku_cnt_total_5" jdbcType="VARCHAR" property="skuCntTotal5" />
    <result column="sku_cnt_5" jdbcType="VARCHAR" property="skuCnt5" />
    <result column="sku_cnt_new_5" jdbcType="VARCHAR" property="skuCntNew5" />
    <result column="num_cum_30_5" jdbcType="VARCHAR" property="numCum305" />
    <result column="amt_cum_30_5" jdbcType="VARCHAR" property="amtCum305" />
    <result column="amt_rate_30_5" jdbcType="VARCHAR" property="amtRate305" />
    <result column="profit_amt_cum_30_5" jdbcType="VARCHAR" property="profitAmtCum305" />
    <result column="profit_rate_30_5" jdbcType="VARCHAR" property="profitRate305" />
    <result column="amt_inv_new_5" jdbcType="VARCHAR" property="amtInvNew5" />
    <result column="sku_cnt_total_6" jdbcType="VARCHAR" property="skuCntTotal6" />
    <result column="sku_cnt_6" jdbcType="VARCHAR" property="skuCnt6" />
    <result column="sku_cnt_new_6" jdbcType="VARCHAR" property="skuCntNew6" />
    <result column="num_cum_30_6" jdbcType="VARCHAR" property="numCum306" />
    <result column="amt_cum_30_6" jdbcType="VARCHAR" property="amtCum306" />
    <result column="amt_rate_30_6" jdbcType="VARCHAR" property="amtRate306" />
    <result column="profit_amt_cum_30_6" jdbcType="VARCHAR" property="profitAmtCum306" />
    <result column="profit_rate_30_6" jdbcType="VARCHAR" property="profitRate306" />
    <result column="amt_inv_new_6" jdbcType="VARCHAR" property="amtInvNew6" />
    <result column="sku_cnt_total_7" jdbcType="VARCHAR" property="skuCntTotal7" />
    <result column="sku_cnt_7" jdbcType="VARCHAR" property="skuCnt7" />
    <result column="sku_cnt_new_7" jdbcType="VARCHAR" property="skuCntNew7" />
    <result column="num_cum_30_7" jdbcType="VARCHAR" property="numCum307" />
    <result column="amt_cum_30_7" jdbcType="VARCHAR" property="amtCum307" />
    <result column="amt_rate_30_7" jdbcType="VARCHAR" property="amtRate307" />
    <result column="profit_amt_cum_30_7" jdbcType="VARCHAR" property="profitAmtCum307" />
    <result column="profit_rate_30_7" jdbcType="VARCHAR" property="profitRate307" />
    <result column="amt_inv_new_7" jdbcType="VARCHAR" property="amtInvNew7" />
    <result column="sku_cnt_total_8" jdbcType="VARCHAR" property="skuCntTotal8" />
    <result column="sku_cnt_8" jdbcType="VARCHAR" property="skuCnt8" />
    <result column="sku_cnt_new_8" jdbcType="VARCHAR" property="skuCntNew8" />
    <result column="num_cum_30_8" jdbcType="VARCHAR" property="numCum308" />
    <result column="amt_cum_30_8" jdbcType="VARCHAR" property="amtCum308" />
    <result column="amt_rate_30_8" jdbcType="VARCHAR" property="amtRate308" />
    <result column="profit_amt_cum_30_8" jdbcType="VARCHAR" property="profitAmtCum308" />
    <result column="profit_rate_30_8" jdbcType="VARCHAR" property="profitRate308" />
    <result column="amt_inv_new_8" jdbcType="VARCHAR" property="amtInvNew8" />
    <result column="sku_cnt_total_9" jdbcType="VARCHAR" property="skuCntTotal9" />
    <result column="sku_cnt_9" jdbcType="VARCHAR" property="skuCnt9" />
    <result column="sku_cnt_new_9" jdbcType="VARCHAR" property="skuCntNew9" />
    <result column="num_cum_30_9" jdbcType="VARCHAR" property="numCum309" />
    <result column="amt_cum_30_9" jdbcType="VARCHAR" property="amtCum309" />
    <result column="amt_rate_30_9" jdbcType="VARCHAR" property="amtRate309" />
    <result column="profit_amt_cum_30_9" jdbcType="VARCHAR" property="profitAmtCum309" />
    <result column="profit_rate_30_9" jdbcType="VARCHAR" property="profitRate309" />
    <result column="amt_inv_new_9" jdbcType="VARCHAR" property="amtInvNew9" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, zone_new, chain_name, city, store_group, classone_name, classtwo_name, 
    classthree_name, classfour_name, component, sku_cnt, sku_cnt_total_2, sku_cnt_2, 
    sku_cnt_new_2, num_cum_30_2, amt_cum_30_2, amt_rate_30_2, profit_amt_cum_30_2, profit_rate_30_2, 
    amt_inv_new_2, sku_cnt_total_3, sku_cnt_3, sku_cnt_new_3, num_cum_30_3, amt_cum_30_3, 
    amt_rate_30_3, profit_amt_cum_30_3, profit_rate_30_3, amt_inv_new_3, sku_cnt_total_4, 
    sku_cnt_4, sku_cnt_new_4, num_cum_30_4, amt_cum_30_4, amt_rate_30_4, profit_amt_cum_30_4, 
    profit_rate_30_4, amt_inv_new_4, sku_cnt_total_5, sku_cnt_5, sku_cnt_new_5, num_cum_30_5, 
    amt_cum_30_5, amt_rate_30_5, profit_amt_cum_30_5, profit_rate_30_5, amt_inv_new_5, 
    sku_cnt_total_6, sku_cnt_6, sku_cnt_new_6, num_cum_30_6, amt_cum_30_6, amt_rate_30_6, 
    profit_amt_cum_30_6, profit_rate_30_6, amt_inv_new_6, sku_cnt_total_7, sku_cnt_7, 
    sku_cnt_new_7, num_cum_30_7, amt_cum_30_7, amt_rate_30_7, profit_amt_cum_30_7, profit_rate_30_7, 
    amt_inv_new_7, sku_cnt_total_8, sku_cnt_8, sku_cnt_new_8, num_cum_30_8, amt_cum_30_8, 
    amt_rate_30_8, profit_amt_cum_30_8, profit_rate_30_8, amt_inv_new_8, sku_cnt_total_9, 
    sku_cnt_9, sku_cnt_new_9, num_cum_30_9, amt_cum_30_9, amt_rate_30_9, profit_amt_cum_30_9, 
    profit_rate_30_9, amt_inv_new_9, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReviewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_retult_composition_review
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_retult_composition_review
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_retult_composition_review
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReviewExample">
    delete from track_retult_composition_review
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReview" useGeneratedKeys="true">
    insert into track_retult_composition_review (task_id, zone_new, chain_name, 
      city, store_group, classone_name, 
      classtwo_name, classthree_name, classfour_name, 
      component, sku_cnt, sku_cnt_total_2, 
      sku_cnt_2, sku_cnt_new_2, num_cum_30_2, 
      amt_cum_30_2, amt_rate_30_2, profit_amt_cum_30_2, 
      profit_rate_30_2, amt_inv_new_2, sku_cnt_total_3, 
      sku_cnt_3, sku_cnt_new_3, num_cum_30_3, 
      amt_cum_30_3, amt_rate_30_3, profit_amt_cum_30_3, 
      profit_rate_30_3, amt_inv_new_3, sku_cnt_total_4, 
      sku_cnt_4, sku_cnt_new_4, num_cum_30_4, 
      amt_cum_30_4, amt_rate_30_4, profit_amt_cum_30_4, 
      profit_rate_30_4, amt_inv_new_4, sku_cnt_total_5, 
      sku_cnt_5, sku_cnt_new_5, num_cum_30_5, 
      amt_cum_30_5, amt_rate_30_5, profit_amt_cum_30_5, 
      profit_rate_30_5, amt_inv_new_5, sku_cnt_total_6, 
      sku_cnt_6, sku_cnt_new_6, num_cum_30_6, 
      amt_cum_30_6, amt_rate_30_6, profit_amt_cum_30_6, 
      profit_rate_30_6, amt_inv_new_6, sku_cnt_total_7, 
      sku_cnt_7, sku_cnt_new_7, num_cum_30_7, 
      amt_cum_30_7, amt_rate_30_7, profit_amt_cum_30_7, 
      profit_rate_30_7, amt_inv_new_7, sku_cnt_total_8, 
      sku_cnt_8, sku_cnt_new_8, num_cum_30_8, 
      amt_cum_30_8, amt_rate_30_8, profit_amt_cum_30_8, 
      profit_rate_30_8, amt_inv_new_8, sku_cnt_total_9, 
      sku_cnt_9, sku_cnt_new_9, num_cum_30_9, 
      amt_cum_30_9, amt_rate_30_9, profit_amt_cum_30_9, 
      profit_rate_30_9, amt_inv_new_9, gmt_create
      )
    values (#{taskId,jdbcType=BIGINT}, #{zoneNew,jdbcType=VARCHAR}, #{chainName,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{storeGroup,jdbcType=VARCHAR}, #{classoneName,jdbcType=VARCHAR}, 
      #{classtwoName,jdbcType=VARCHAR}, #{classthreeName,jdbcType=VARCHAR}, #{classfourName,jdbcType=VARCHAR}, 
      #{component,jdbcType=VARCHAR}, #{skuCnt,jdbcType=VARCHAR}, #{skuCntTotal2,jdbcType=VARCHAR}, 
      #{skuCnt2,jdbcType=VARCHAR}, #{skuCntNew2,jdbcType=VARCHAR}, #{numCum302,jdbcType=VARCHAR}, 
      #{amtCum302,jdbcType=VARCHAR}, #{amtRate302,jdbcType=VARCHAR}, #{profitAmtCum302,jdbcType=VARCHAR}, 
      #{profitRate302,jdbcType=VARCHAR}, #{amtInvNew2,jdbcType=VARCHAR}, #{skuCntTotal3,jdbcType=VARCHAR}, 
      #{skuCnt3,jdbcType=VARCHAR}, #{skuCntNew3,jdbcType=VARCHAR}, #{numCum303,jdbcType=VARCHAR}, 
      #{amtCum303,jdbcType=VARCHAR}, #{amtRate303,jdbcType=VARCHAR}, #{profitAmtCum303,jdbcType=VARCHAR}, 
      #{profitRate303,jdbcType=VARCHAR}, #{amtInvNew3,jdbcType=VARCHAR}, #{skuCntTotal4,jdbcType=VARCHAR}, 
      #{skuCnt4,jdbcType=VARCHAR}, #{skuCntNew4,jdbcType=VARCHAR}, #{numCum304,jdbcType=VARCHAR}, 
      #{amtCum304,jdbcType=VARCHAR}, #{amtRate304,jdbcType=VARCHAR}, #{profitAmtCum304,jdbcType=VARCHAR}, 
      #{profitRate304,jdbcType=VARCHAR}, #{amtInvNew4,jdbcType=VARCHAR}, #{skuCntTotal5,jdbcType=VARCHAR}, 
      #{skuCnt5,jdbcType=VARCHAR}, #{skuCntNew5,jdbcType=VARCHAR}, #{numCum305,jdbcType=VARCHAR}, 
      #{amtCum305,jdbcType=VARCHAR}, #{amtRate305,jdbcType=VARCHAR}, #{profitAmtCum305,jdbcType=VARCHAR}, 
      #{profitRate305,jdbcType=VARCHAR}, #{amtInvNew5,jdbcType=VARCHAR}, #{skuCntTotal6,jdbcType=VARCHAR}, 
      #{skuCnt6,jdbcType=VARCHAR}, #{skuCntNew6,jdbcType=VARCHAR}, #{numCum306,jdbcType=VARCHAR}, 
      #{amtCum306,jdbcType=VARCHAR}, #{amtRate306,jdbcType=VARCHAR}, #{profitAmtCum306,jdbcType=VARCHAR}, 
      #{profitRate306,jdbcType=VARCHAR}, #{amtInvNew6,jdbcType=VARCHAR}, #{skuCntTotal7,jdbcType=VARCHAR}, 
      #{skuCnt7,jdbcType=VARCHAR}, #{skuCntNew7,jdbcType=VARCHAR}, #{numCum307,jdbcType=VARCHAR}, 
      #{amtCum307,jdbcType=VARCHAR}, #{amtRate307,jdbcType=VARCHAR}, #{profitAmtCum307,jdbcType=VARCHAR}, 
      #{profitRate307,jdbcType=VARCHAR}, #{amtInvNew7,jdbcType=VARCHAR}, #{skuCntTotal8,jdbcType=VARCHAR}, 
      #{skuCnt8,jdbcType=VARCHAR}, #{skuCntNew8,jdbcType=VARCHAR}, #{numCum308,jdbcType=VARCHAR}, 
      #{amtCum308,jdbcType=VARCHAR}, #{amtRate308,jdbcType=VARCHAR}, #{profitAmtCum308,jdbcType=VARCHAR}, 
      #{profitRate308,jdbcType=VARCHAR}, #{amtInvNew8,jdbcType=VARCHAR}, #{skuCntTotal9,jdbcType=VARCHAR}, 
      #{skuCnt9,jdbcType=VARCHAR}, #{skuCntNew9,jdbcType=VARCHAR}, #{numCum309,jdbcType=VARCHAR}, 
      #{amtCum309,jdbcType=VARCHAR}, #{amtRate309,jdbcType=VARCHAR}, #{profitAmtCum309,jdbcType=VARCHAR}, 
      #{profitRate309,jdbcType=VARCHAR}, #{amtInvNew9,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReview" useGeneratedKeys="true">
    insert into track_retult_composition_review
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="zoneNew != null">
        zone_new,
      </if>
      <if test="chainName != null">
        chain_name,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="storeGroup != null">
        store_group,
      </if>
      <if test="classoneName != null">
        classone_name,
      </if>
      <if test="classtwoName != null">
        classtwo_name,
      </if>
      <if test="classthreeName != null">
        classthree_name,
      </if>
      <if test="classfourName != null">
        classfour_name,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="skuCnt != null">
        sku_cnt,
      </if>
      <if test="skuCntTotal2 != null">
        sku_cnt_total_2,
      </if>
      <if test="skuCnt2 != null">
        sku_cnt_2,
      </if>
      <if test="skuCntNew2 != null">
        sku_cnt_new_2,
      </if>
      <if test="numCum302 != null">
        num_cum_30_2,
      </if>
      <if test="amtCum302 != null">
        amt_cum_30_2,
      </if>
      <if test="amtRate302 != null">
        amt_rate_30_2,
      </if>
      <if test="profitAmtCum302 != null">
        profit_amt_cum_30_2,
      </if>
      <if test="profitRate302 != null">
        profit_rate_30_2,
      </if>
      <if test="amtInvNew2 != null">
        amt_inv_new_2,
      </if>
      <if test="skuCntTotal3 != null">
        sku_cnt_total_3,
      </if>
      <if test="skuCnt3 != null">
        sku_cnt_3,
      </if>
      <if test="skuCntNew3 != null">
        sku_cnt_new_3,
      </if>
      <if test="numCum303 != null">
        num_cum_30_3,
      </if>
      <if test="amtCum303 != null">
        amt_cum_30_3,
      </if>
      <if test="amtRate303 != null">
        amt_rate_30_3,
      </if>
      <if test="profitAmtCum303 != null">
        profit_amt_cum_30_3,
      </if>
      <if test="profitRate303 != null">
        profit_rate_30_3,
      </if>
      <if test="amtInvNew3 != null">
        amt_inv_new_3,
      </if>
      <if test="skuCntTotal4 != null">
        sku_cnt_total_4,
      </if>
      <if test="skuCnt4 != null">
        sku_cnt_4,
      </if>
      <if test="skuCntNew4 != null">
        sku_cnt_new_4,
      </if>
      <if test="numCum304 != null">
        num_cum_30_4,
      </if>
      <if test="amtCum304 != null">
        amt_cum_30_4,
      </if>
      <if test="amtRate304 != null">
        amt_rate_30_4,
      </if>
      <if test="profitAmtCum304 != null">
        profit_amt_cum_30_4,
      </if>
      <if test="profitRate304 != null">
        profit_rate_30_4,
      </if>
      <if test="amtInvNew4 != null">
        amt_inv_new_4,
      </if>
      <if test="skuCntTotal5 != null">
        sku_cnt_total_5,
      </if>
      <if test="skuCnt5 != null">
        sku_cnt_5,
      </if>
      <if test="skuCntNew5 != null">
        sku_cnt_new_5,
      </if>
      <if test="numCum305 != null">
        num_cum_30_5,
      </if>
      <if test="amtCum305 != null">
        amt_cum_30_5,
      </if>
      <if test="amtRate305 != null">
        amt_rate_30_5,
      </if>
      <if test="profitAmtCum305 != null">
        profit_amt_cum_30_5,
      </if>
      <if test="profitRate305 != null">
        profit_rate_30_5,
      </if>
      <if test="amtInvNew5 != null">
        amt_inv_new_5,
      </if>
      <if test="skuCntTotal6 != null">
        sku_cnt_total_6,
      </if>
      <if test="skuCnt6 != null">
        sku_cnt_6,
      </if>
      <if test="skuCntNew6 != null">
        sku_cnt_new_6,
      </if>
      <if test="numCum306 != null">
        num_cum_30_6,
      </if>
      <if test="amtCum306 != null">
        amt_cum_30_6,
      </if>
      <if test="amtRate306 != null">
        amt_rate_30_6,
      </if>
      <if test="profitAmtCum306 != null">
        profit_amt_cum_30_6,
      </if>
      <if test="profitRate306 != null">
        profit_rate_30_6,
      </if>
      <if test="amtInvNew6 != null">
        amt_inv_new_6,
      </if>
      <if test="skuCntTotal7 != null">
        sku_cnt_total_7,
      </if>
      <if test="skuCnt7 != null">
        sku_cnt_7,
      </if>
      <if test="skuCntNew7 != null">
        sku_cnt_new_7,
      </if>
      <if test="numCum307 != null">
        num_cum_30_7,
      </if>
      <if test="amtCum307 != null">
        amt_cum_30_7,
      </if>
      <if test="amtRate307 != null">
        amt_rate_30_7,
      </if>
      <if test="profitAmtCum307 != null">
        profit_amt_cum_30_7,
      </if>
      <if test="profitRate307 != null">
        profit_rate_30_7,
      </if>
      <if test="amtInvNew7 != null">
        amt_inv_new_7,
      </if>
      <if test="skuCntTotal8 != null">
        sku_cnt_total_8,
      </if>
      <if test="skuCnt8 != null">
        sku_cnt_8,
      </if>
      <if test="skuCntNew8 != null">
        sku_cnt_new_8,
      </if>
      <if test="numCum308 != null">
        num_cum_30_8,
      </if>
      <if test="amtCum308 != null">
        amt_cum_30_8,
      </if>
      <if test="amtRate308 != null">
        amt_rate_30_8,
      </if>
      <if test="profitAmtCum308 != null">
        profit_amt_cum_30_8,
      </if>
      <if test="profitRate308 != null">
        profit_rate_30_8,
      </if>
      <if test="amtInvNew8 != null">
        amt_inv_new_8,
      </if>
      <if test="skuCntTotal9 != null">
        sku_cnt_total_9,
      </if>
      <if test="skuCnt9 != null">
        sku_cnt_9,
      </if>
      <if test="skuCntNew9 != null">
        sku_cnt_new_9,
      </if>
      <if test="numCum309 != null">
        num_cum_30_9,
      </if>
      <if test="amtCum309 != null">
        amt_cum_30_9,
      </if>
      <if test="amtRate309 != null">
        amt_rate_30_9,
      </if>
      <if test="profitAmtCum309 != null">
        profit_amt_cum_30_9,
      </if>
      <if test="profitRate309 != null">
        profit_rate_30_9,
      </if>
      <if test="amtInvNew9 != null">
        amt_inv_new_9,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="chainName != null">
        #{chainName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeGroup != null">
        #{storeGroup,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt != null">
        #{skuCnt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal2 != null">
        #{skuCntTotal2,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt2 != null">
        #{skuCnt2,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew2 != null">
        #{skuCntNew2,jdbcType=VARCHAR},
      </if>
      <if test="numCum302 != null">
        #{numCum302,jdbcType=VARCHAR},
      </if>
      <if test="amtCum302 != null">
        #{amtCum302,jdbcType=VARCHAR},
      </if>
      <if test="amtRate302 != null">
        #{amtRate302,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum302 != null">
        #{profitAmtCum302,jdbcType=VARCHAR},
      </if>
      <if test="profitRate302 != null">
        #{profitRate302,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew2 != null">
        #{amtInvNew2,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal3 != null">
        #{skuCntTotal3,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt3 != null">
        #{skuCnt3,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew3 != null">
        #{skuCntNew3,jdbcType=VARCHAR},
      </if>
      <if test="numCum303 != null">
        #{numCum303,jdbcType=VARCHAR},
      </if>
      <if test="amtCum303 != null">
        #{amtCum303,jdbcType=VARCHAR},
      </if>
      <if test="amtRate303 != null">
        #{amtRate303,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum303 != null">
        #{profitAmtCum303,jdbcType=VARCHAR},
      </if>
      <if test="profitRate303 != null">
        #{profitRate303,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew3 != null">
        #{amtInvNew3,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal4 != null">
        #{skuCntTotal4,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt4 != null">
        #{skuCnt4,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew4 != null">
        #{skuCntNew4,jdbcType=VARCHAR},
      </if>
      <if test="numCum304 != null">
        #{numCum304,jdbcType=VARCHAR},
      </if>
      <if test="amtCum304 != null">
        #{amtCum304,jdbcType=VARCHAR},
      </if>
      <if test="amtRate304 != null">
        #{amtRate304,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum304 != null">
        #{profitAmtCum304,jdbcType=VARCHAR},
      </if>
      <if test="profitRate304 != null">
        #{profitRate304,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew4 != null">
        #{amtInvNew4,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal5 != null">
        #{skuCntTotal5,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt5 != null">
        #{skuCnt5,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew5 != null">
        #{skuCntNew5,jdbcType=VARCHAR},
      </if>
      <if test="numCum305 != null">
        #{numCum305,jdbcType=VARCHAR},
      </if>
      <if test="amtCum305 != null">
        #{amtCum305,jdbcType=VARCHAR},
      </if>
      <if test="amtRate305 != null">
        #{amtRate305,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum305 != null">
        #{profitAmtCum305,jdbcType=VARCHAR},
      </if>
      <if test="profitRate305 != null">
        #{profitRate305,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew5 != null">
        #{amtInvNew5,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal6 != null">
        #{skuCntTotal6,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt6 != null">
        #{skuCnt6,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew6 != null">
        #{skuCntNew6,jdbcType=VARCHAR},
      </if>
      <if test="numCum306 != null">
        #{numCum306,jdbcType=VARCHAR},
      </if>
      <if test="amtCum306 != null">
        #{amtCum306,jdbcType=VARCHAR},
      </if>
      <if test="amtRate306 != null">
        #{amtRate306,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum306 != null">
        #{profitAmtCum306,jdbcType=VARCHAR},
      </if>
      <if test="profitRate306 != null">
        #{profitRate306,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew6 != null">
        #{amtInvNew6,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal7 != null">
        #{skuCntTotal7,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt7 != null">
        #{skuCnt7,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew7 != null">
        #{skuCntNew7,jdbcType=VARCHAR},
      </if>
      <if test="numCum307 != null">
        #{numCum307,jdbcType=VARCHAR},
      </if>
      <if test="amtCum307 != null">
        #{amtCum307,jdbcType=VARCHAR},
      </if>
      <if test="amtRate307 != null">
        #{amtRate307,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum307 != null">
        #{profitAmtCum307,jdbcType=VARCHAR},
      </if>
      <if test="profitRate307 != null">
        #{profitRate307,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew7 != null">
        #{amtInvNew7,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal8 != null">
        #{skuCntTotal8,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt8 != null">
        #{skuCnt8,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew8 != null">
        #{skuCntNew8,jdbcType=VARCHAR},
      </if>
      <if test="numCum308 != null">
        #{numCum308,jdbcType=VARCHAR},
      </if>
      <if test="amtCum308 != null">
        #{amtCum308,jdbcType=VARCHAR},
      </if>
      <if test="amtRate308 != null">
        #{amtRate308,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum308 != null">
        #{profitAmtCum308,jdbcType=VARCHAR},
      </if>
      <if test="profitRate308 != null">
        #{profitRate308,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew8 != null">
        #{amtInvNew8,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal9 != null">
        #{skuCntTotal9,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt9 != null">
        #{skuCnt9,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew9 != null">
        #{skuCntNew9,jdbcType=VARCHAR},
      </if>
      <if test="numCum309 != null">
        #{numCum309,jdbcType=VARCHAR},
      </if>
      <if test="amtCum309 != null">
        #{amtCum309,jdbcType=VARCHAR},
      </if>
      <if test="amtRate309 != null">
        #{amtRate309,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum309 != null">
        #{profitAmtCum309,jdbcType=VARCHAR},
      </if>
      <if test="profitRate309 != null">
        #{profitRate309,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew9 != null">
        #{amtInvNew9,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReviewExample" resultType="java.lang.Long">
    select count(*) from track_retult_composition_review
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_retult_composition_review
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.zoneNew != null">
        zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="record.chainName != null">
        chain_name = #{record.chainName,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGroup != null">
        store_group = #{record.storeGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.classoneName != null">
        classone_name = #{record.classoneName,jdbcType=VARCHAR},
      </if>
      <if test="record.classtwoName != null">
        classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="record.classthreeName != null">
        classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="record.classfourName != null">
        classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt != null">
        sku_cnt = #{record.skuCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal2 != null">
        sku_cnt_total_2 = #{record.skuCntTotal2,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt2 != null">
        sku_cnt_2 = #{record.skuCnt2,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew2 != null">
        sku_cnt_new_2 = #{record.skuCntNew2,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum302 != null">
        num_cum_30_2 = #{record.numCum302,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum302 != null">
        amt_cum_30_2 = #{record.amtCum302,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate302 != null">
        amt_rate_30_2 = #{record.amtRate302,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum302 != null">
        profit_amt_cum_30_2 = #{record.profitAmtCum302,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate302 != null">
        profit_rate_30_2 = #{record.profitRate302,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew2 != null">
        amt_inv_new_2 = #{record.amtInvNew2,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal3 != null">
        sku_cnt_total_3 = #{record.skuCntTotal3,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt3 != null">
        sku_cnt_3 = #{record.skuCnt3,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew3 != null">
        sku_cnt_new_3 = #{record.skuCntNew3,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum303 != null">
        num_cum_30_3 = #{record.numCum303,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum303 != null">
        amt_cum_30_3 = #{record.amtCum303,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate303 != null">
        amt_rate_30_3 = #{record.amtRate303,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum303 != null">
        profit_amt_cum_30_3 = #{record.profitAmtCum303,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate303 != null">
        profit_rate_30_3 = #{record.profitRate303,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew3 != null">
        amt_inv_new_3 = #{record.amtInvNew3,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal4 != null">
        sku_cnt_total_4 = #{record.skuCntTotal4,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt4 != null">
        sku_cnt_4 = #{record.skuCnt4,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew4 != null">
        sku_cnt_new_4 = #{record.skuCntNew4,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum304 != null">
        num_cum_30_4 = #{record.numCum304,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum304 != null">
        amt_cum_30_4 = #{record.amtCum304,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate304 != null">
        amt_rate_30_4 = #{record.amtRate304,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum304 != null">
        profit_amt_cum_30_4 = #{record.profitAmtCum304,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate304 != null">
        profit_rate_30_4 = #{record.profitRate304,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew4 != null">
        amt_inv_new_4 = #{record.amtInvNew4,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal5 != null">
        sku_cnt_total_5 = #{record.skuCntTotal5,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt5 != null">
        sku_cnt_5 = #{record.skuCnt5,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew5 != null">
        sku_cnt_new_5 = #{record.skuCntNew5,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum305 != null">
        num_cum_30_5 = #{record.numCum305,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum305 != null">
        amt_cum_30_5 = #{record.amtCum305,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate305 != null">
        amt_rate_30_5 = #{record.amtRate305,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum305 != null">
        profit_amt_cum_30_5 = #{record.profitAmtCum305,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate305 != null">
        profit_rate_30_5 = #{record.profitRate305,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew5 != null">
        amt_inv_new_5 = #{record.amtInvNew5,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal6 != null">
        sku_cnt_total_6 = #{record.skuCntTotal6,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt6 != null">
        sku_cnt_6 = #{record.skuCnt6,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew6 != null">
        sku_cnt_new_6 = #{record.skuCntNew6,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum306 != null">
        num_cum_30_6 = #{record.numCum306,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum306 != null">
        amt_cum_30_6 = #{record.amtCum306,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate306 != null">
        amt_rate_30_6 = #{record.amtRate306,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum306 != null">
        profit_amt_cum_30_6 = #{record.profitAmtCum306,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate306 != null">
        profit_rate_30_6 = #{record.profitRate306,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew6 != null">
        amt_inv_new_6 = #{record.amtInvNew6,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal7 != null">
        sku_cnt_total_7 = #{record.skuCntTotal7,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt7 != null">
        sku_cnt_7 = #{record.skuCnt7,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew7 != null">
        sku_cnt_new_7 = #{record.skuCntNew7,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum307 != null">
        num_cum_30_7 = #{record.numCum307,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum307 != null">
        amt_cum_30_7 = #{record.amtCum307,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate307 != null">
        amt_rate_30_7 = #{record.amtRate307,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum307 != null">
        profit_amt_cum_30_7 = #{record.profitAmtCum307,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate307 != null">
        profit_rate_30_7 = #{record.profitRate307,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew7 != null">
        amt_inv_new_7 = #{record.amtInvNew7,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal8 != null">
        sku_cnt_total_8 = #{record.skuCntTotal8,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt8 != null">
        sku_cnt_8 = #{record.skuCnt8,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew8 != null">
        sku_cnt_new_8 = #{record.skuCntNew8,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum308 != null">
        num_cum_30_8 = #{record.numCum308,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum308 != null">
        amt_cum_30_8 = #{record.amtCum308,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate308 != null">
        amt_rate_30_8 = #{record.amtRate308,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum308 != null">
        profit_amt_cum_30_8 = #{record.profitAmtCum308,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate308 != null">
        profit_rate_30_8 = #{record.profitRate308,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew8 != null">
        amt_inv_new_8 = #{record.amtInvNew8,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntTotal9 != null">
        sku_cnt_total_9 = #{record.skuCntTotal9,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCnt9 != null">
        sku_cnt_9 = #{record.skuCnt9,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCntNew9 != null">
        sku_cnt_new_9 = #{record.skuCntNew9,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum309 != null">
        num_cum_30_9 = #{record.numCum309,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum309 != null">
        amt_cum_30_9 = #{record.amtCum309,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRate309 != null">
        amt_rate_30_9 = #{record.amtRate309,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum309 != null">
        profit_amt_cum_30_9 = #{record.profitAmtCum309,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate309 != null">
        profit_rate_30_9 = #{record.profitRate309,jdbcType=VARCHAR},
      </if>
      <if test="record.amtInvNew9 != null">
        amt_inv_new_9 = #{record.amtInvNew9,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_retult_composition_review
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      chain_name = #{record.chainName,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      store_group = #{record.storeGroup,jdbcType=VARCHAR},
      classone_name = #{record.classoneName,jdbcType=VARCHAR},
      classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      sku_cnt = #{record.skuCnt,jdbcType=VARCHAR},
      sku_cnt_total_2 = #{record.skuCntTotal2,jdbcType=VARCHAR},
      sku_cnt_2 = #{record.skuCnt2,jdbcType=VARCHAR},
      sku_cnt_new_2 = #{record.skuCntNew2,jdbcType=VARCHAR},
      num_cum_30_2 = #{record.numCum302,jdbcType=VARCHAR},
      amt_cum_30_2 = #{record.amtCum302,jdbcType=VARCHAR},
      amt_rate_30_2 = #{record.amtRate302,jdbcType=VARCHAR},
      profit_amt_cum_30_2 = #{record.profitAmtCum302,jdbcType=VARCHAR},
      profit_rate_30_2 = #{record.profitRate302,jdbcType=VARCHAR},
      amt_inv_new_2 = #{record.amtInvNew2,jdbcType=VARCHAR},
      sku_cnt_total_3 = #{record.skuCntTotal3,jdbcType=VARCHAR},
      sku_cnt_3 = #{record.skuCnt3,jdbcType=VARCHAR},
      sku_cnt_new_3 = #{record.skuCntNew3,jdbcType=VARCHAR},
      num_cum_30_3 = #{record.numCum303,jdbcType=VARCHAR},
      amt_cum_30_3 = #{record.amtCum303,jdbcType=VARCHAR},
      amt_rate_30_3 = #{record.amtRate303,jdbcType=VARCHAR},
      profit_amt_cum_30_3 = #{record.profitAmtCum303,jdbcType=VARCHAR},
      profit_rate_30_3 = #{record.profitRate303,jdbcType=VARCHAR},
      amt_inv_new_3 = #{record.amtInvNew3,jdbcType=VARCHAR},
      sku_cnt_total_4 = #{record.skuCntTotal4,jdbcType=VARCHAR},
      sku_cnt_4 = #{record.skuCnt4,jdbcType=VARCHAR},
      sku_cnt_new_4 = #{record.skuCntNew4,jdbcType=VARCHAR},
      num_cum_30_4 = #{record.numCum304,jdbcType=VARCHAR},
      amt_cum_30_4 = #{record.amtCum304,jdbcType=VARCHAR},
      amt_rate_30_4 = #{record.amtRate304,jdbcType=VARCHAR},
      profit_amt_cum_30_4 = #{record.profitAmtCum304,jdbcType=VARCHAR},
      profit_rate_30_4 = #{record.profitRate304,jdbcType=VARCHAR},
      amt_inv_new_4 = #{record.amtInvNew4,jdbcType=VARCHAR},
      sku_cnt_total_5 = #{record.skuCntTotal5,jdbcType=VARCHAR},
      sku_cnt_5 = #{record.skuCnt5,jdbcType=VARCHAR},
      sku_cnt_new_5 = #{record.skuCntNew5,jdbcType=VARCHAR},
      num_cum_30_5 = #{record.numCum305,jdbcType=VARCHAR},
      amt_cum_30_5 = #{record.amtCum305,jdbcType=VARCHAR},
      amt_rate_30_5 = #{record.amtRate305,jdbcType=VARCHAR},
      profit_amt_cum_30_5 = #{record.profitAmtCum305,jdbcType=VARCHAR},
      profit_rate_30_5 = #{record.profitRate305,jdbcType=VARCHAR},
      amt_inv_new_5 = #{record.amtInvNew5,jdbcType=VARCHAR},
      sku_cnt_total_6 = #{record.skuCntTotal6,jdbcType=VARCHAR},
      sku_cnt_6 = #{record.skuCnt6,jdbcType=VARCHAR},
      sku_cnt_new_6 = #{record.skuCntNew6,jdbcType=VARCHAR},
      num_cum_30_6 = #{record.numCum306,jdbcType=VARCHAR},
      amt_cum_30_6 = #{record.amtCum306,jdbcType=VARCHAR},
      amt_rate_30_6 = #{record.amtRate306,jdbcType=VARCHAR},
      profit_amt_cum_30_6 = #{record.profitAmtCum306,jdbcType=VARCHAR},
      profit_rate_30_6 = #{record.profitRate306,jdbcType=VARCHAR},
      amt_inv_new_6 = #{record.amtInvNew6,jdbcType=VARCHAR},
      sku_cnt_total_7 = #{record.skuCntTotal7,jdbcType=VARCHAR},
      sku_cnt_7 = #{record.skuCnt7,jdbcType=VARCHAR},
      sku_cnt_new_7 = #{record.skuCntNew7,jdbcType=VARCHAR},
      num_cum_30_7 = #{record.numCum307,jdbcType=VARCHAR},
      amt_cum_30_7 = #{record.amtCum307,jdbcType=VARCHAR},
      amt_rate_30_7 = #{record.amtRate307,jdbcType=VARCHAR},
      profit_amt_cum_30_7 = #{record.profitAmtCum307,jdbcType=VARCHAR},
      profit_rate_30_7 = #{record.profitRate307,jdbcType=VARCHAR},
      amt_inv_new_7 = #{record.amtInvNew7,jdbcType=VARCHAR},
      sku_cnt_total_8 = #{record.skuCntTotal8,jdbcType=VARCHAR},
      sku_cnt_8 = #{record.skuCnt8,jdbcType=VARCHAR},
      sku_cnt_new_8 = #{record.skuCntNew8,jdbcType=VARCHAR},
      num_cum_30_8 = #{record.numCum308,jdbcType=VARCHAR},
      amt_cum_30_8 = #{record.amtCum308,jdbcType=VARCHAR},
      amt_rate_30_8 = #{record.amtRate308,jdbcType=VARCHAR},
      profit_amt_cum_30_8 = #{record.profitAmtCum308,jdbcType=VARCHAR},
      profit_rate_30_8 = #{record.profitRate308,jdbcType=VARCHAR},
      amt_inv_new_8 = #{record.amtInvNew8,jdbcType=VARCHAR},
      sku_cnt_total_9 = #{record.skuCntTotal9,jdbcType=VARCHAR},
      sku_cnt_9 = #{record.skuCnt9,jdbcType=VARCHAR},
      sku_cnt_new_9 = #{record.skuCntNew9,jdbcType=VARCHAR},
      num_cum_30_9 = #{record.numCum309,jdbcType=VARCHAR},
      amt_cum_30_9 = #{record.amtCum309,jdbcType=VARCHAR},
      amt_rate_30_9 = #{record.amtRate309,jdbcType=VARCHAR},
      profit_amt_cum_30_9 = #{record.profitAmtCum309,jdbcType=VARCHAR},
      profit_rate_30_9 = #{record.profitRate309,jdbcType=VARCHAR},
      amt_inv_new_9 = #{record.amtInvNew9,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReview">
    update track_retult_composition_review
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        zone_new = #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="chainName != null">
        chain_name = #{chainName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="storeGroup != null">
        store_group = #{storeGroup,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        classone_name = #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        classthree_name = #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        classfour_name = #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt != null">
        sku_cnt = #{skuCnt,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal2 != null">
        sku_cnt_total_2 = #{skuCntTotal2,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt2 != null">
        sku_cnt_2 = #{skuCnt2,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew2 != null">
        sku_cnt_new_2 = #{skuCntNew2,jdbcType=VARCHAR},
      </if>
      <if test="numCum302 != null">
        num_cum_30_2 = #{numCum302,jdbcType=VARCHAR},
      </if>
      <if test="amtCum302 != null">
        amt_cum_30_2 = #{amtCum302,jdbcType=VARCHAR},
      </if>
      <if test="amtRate302 != null">
        amt_rate_30_2 = #{amtRate302,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum302 != null">
        profit_amt_cum_30_2 = #{profitAmtCum302,jdbcType=VARCHAR},
      </if>
      <if test="profitRate302 != null">
        profit_rate_30_2 = #{profitRate302,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew2 != null">
        amt_inv_new_2 = #{amtInvNew2,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal3 != null">
        sku_cnt_total_3 = #{skuCntTotal3,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt3 != null">
        sku_cnt_3 = #{skuCnt3,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew3 != null">
        sku_cnt_new_3 = #{skuCntNew3,jdbcType=VARCHAR},
      </if>
      <if test="numCum303 != null">
        num_cum_30_3 = #{numCum303,jdbcType=VARCHAR},
      </if>
      <if test="amtCum303 != null">
        amt_cum_30_3 = #{amtCum303,jdbcType=VARCHAR},
      </if>
      <if test="amtRate303 != null">
        amt_rate_30_3 = #{amtRate303,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum303 != null">
        profit_amt_cum_30_3 = #{profitAmtCum303,jdbcType=VARCHAR},
      </if>
      <if test="profitRate303 != null">
        profit_rate_30_3 = #{profitRate303,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew3 != null">
        amt_inv_new_3 = #{amtInvNew3,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal4 != null">
        sku_cnt_total_4 = #{skuCntTotal4,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt4 != null">
        sku_cnt_4 = #{skuCnt4,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew4 != null">
        sku_cnt_new_4 = #{skuCntNew4,jdbcType=VARCHAR},
      </if>
      <if test="numCum304 != null">
        num_cum_30_4 = #{numCum304,jdbcType=VARCHAR},
      </if>
      <if test="amtCum304 != null">
        amt_cum_30_4 = #{amtCum304,jdbcType=VARCHAR},
      </if>
      <if test="amtRate304 != null">
        amt_rate_30_4 = #{amtRate304,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum304 != null">
        profit_amt_cum_30_4 = #{profitAmtCum304,jdbcType=VARCHAR},
      </if>
      <if test="profitRate304 != null">
        profit_rate_30_4 = #{profitRate304,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew4 != null">
        amt_inv_new_4 = #{amtInvNew4,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal5 != null">
        sku_cnt_total_5 = #{skuCntTotal5,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt5 != null">
        sku_cnt_5 = #{skuCnt5,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew5 != null">
        sku_cnt_new_5 = #{skuCntNew5,jdbcType=VARCHAR},
      </if>
      <if test="numCum305 != null">
        num_cum_30_5 = #{numCum305,jdbcType=VARCHAR},
      </if>
      <if test="amtCum305 != null">
        amt_cum_30_5 = #{amtCum305,jdbcType=VARCHAR},
      </if>
      <if test="amtRate305 != null">
        amt_rate_30_5 = #{amtRate305,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum305 != null">
        profit_amt_cum_30_5 = #{profitAmtCum305,jdbcType=VARCHAR},
      </if>
      <if test="profitRate305 != null">
        profit_rate_30_5 = #{profitRate305,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew5 != null">
        amt_inv_new_5 = #{amtInvNew5,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal6 != null">
        sku_cnt_total_6 = #{skuCntTotal6,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt6 != null">
        sku_cnt_6 = #{skuCnt6,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew6 != null">
        sku_cnt_new_6 = #{skuCntNew6,jdbcType=VARCHAR},
      </if>
      <if test="numCum306 != null">
        num_cum_30_6 = #{numCum306,jdbcType=VARCHAR},
      </if>
      <if test="amtCum306 != null">
        amt_cum_30_6 = #{amtCum306,jdbcType=VARCHAR},
      </if>
      <if test="amtRate306 != null">
        amt_rate_30_6 = #{amtRate306,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum306 != null">
        profit_amt_cum_30_6 = #{profitAmtCum306,jdbcType=VARCHAR},
      </if>
      <if test="profitRate306 != null">
        profit_rate_30_6 = #{profitRate306,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew6 != null">
        amt_inv_new_6 = #{amtInvNew6,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal7 != null">
        sku_cnt_total_7 = #{skuCntTotal7,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt7 != null">
        sku_cnt_7 = #{skuCnt7,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew7 != null">
        sku_cnt_new_7 = #{skuCntNew7,jdbcType=VARCHAR},
      </if>
      <if test="numCum307 != null">
        num_cum_30_7 = #{numCum307,jdbcType=VARCHAR},
      </if>
      <if test="amtCum307 != null">
        amt_cum_30_7 = #{amtCum307,jdbcType=VARCHAR},
      </if>
      <if test="amtRate307 != null">
        amt_rate_30_7 = #{amtRate307,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum307 != null">
        profit_amt_cum_30_7 = #{profitAmtCum307,jdbcType=VARCHAR},
      </if>
      <if test="profitRate307 != null">
        profit_rate_30_7 = #{profitRate307,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew7 != null">
        amt_inv_new_7 = #{amtInvNew7,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal8 != null">
        sku_cnt_total_8 = #{skuCntTotal8,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt8 != null">
        sku_cnt_8 = #{skuCnt8,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew8 != null">
        sku_cnt_new_8 = #{skuCntNew8,jdbcType=VARCHAR},
      </if>
      <if test="numCum308 != null">
        num_cum_30_8 = #{numCum308,jdbcType=VARCHAR},
      </if>
      <if test="amtCum308 != null">
        amt_cum_30_8 = #{amtCum308,jdbcType=VARCHAR},
      </if>
      <if test="amtRate308 != null">
        amt_rate_30_8 = #{amtRate308,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum308 != null">
        profit_amt_cum_30_8 = #{profitAmtCum308,jdbcType=VARCHAR},
      </if>
      <if test="profitRate308 != null">
        profit_rate_30_8 = #{profitRate308,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew8 != null">
        amt_inv_new_8 = #{amtInvNew8,jdbcType=VARCHAR},
      </if>
      <if test="skuCntTotal9 != null">
        sku_cnt_total_9 = #{skuCntTotal9,jdbcType=VARCHAR},
      </if>
      <if test="skuCnt9 != null">
        sku_cnt_9 = #{skuCnt9,jdbcType=VARCHAR},
      </if>
      <if test="skuCntNew9 != null">
        sku_cnt_new_9 = #{skuCntNew9,jdbcType=VARCHAR},
      </if>
      <if test="numCum309 != null">
        num_cum_30_9 = #{numCum309,jdbcType=VARCHAR},
      </if>
      <if test="amtCum309 != null">
        amt_cum_30_9 = #{amtCum309,jdbcType=VARCHAR},
      </if>
      <if test="amtRate309 != null">
        amt_rate_30_9 = #{amtRate309,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum309 != null">
        profit_amt_cum_30_9 = #{profitAmtCum309,jdbcType=VARCHAR},
      </if>
      <if test="profitRate309 != null">
        profit_rate_30_9 = #{profitRate309,jdbcType=VARCHAR},
      </if>
      <if test="amtInvNew9 != null">
        amt_inv_new_9 = #{amtInvNew9,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackRetultCompositionReview">
    update track_retult_composition_review
    set task_id = #{taskId,jdbcType=BIGINT},
      zone_new = #{zoneNew,jdbcType=VARCHAR},
      chain_name = #{chainName,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      store_group = #{storeGroup,jdbcType=VARCHAR},
      classone_name = #{classoneName,jdbcType=VARCHAR},
      classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      classthree_name = #{classthreeName,jdbcType=VARCHAR},
      classfour_name = #{classfourName,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      sku_cnt = #{skuCnt,jdbcType=VARCHAR},
      sku_cnt_total_2 = #{skuCntTotal2,jdbcType=VARCHAR},
      sku_cnt_2 = #{skuCnt2,jdbcType=VARCHAR},
      sku_cnt_new_2 = #{skuCntNew2,jdbcType=VARCHAR},
      num_cum_30_2 = #{numCum302,jdbcType=VARCHAR},
      amt_cum_30_2 = #{amtCum302,jdbcType=VARCHAR},
      amt_rate_30_2 = #{amtRate302,jdbcType=VARCHAR},
      profit_amt_cum_30_2 = #{profitAmtCum302,jdbcType=VARCHAR},
      profit_rate_30_2 = #{profitRate302,jdbcType=VARCHAR},
      amt_inv_new_2 = #{amtInvNew2,jdbcType=VARCHAR},
      sku_cnt_total_3 = #{skuCntTotal3,jdbcType=VARCHAR},
      sku_cnt_3 = #{skuCnt3,jdbcType=VARCHAR},
      sku_cnt_new_3 = #{skuCntNew3,jdbcType=VARCHAR},
      num_cum_30_3 = #{numCum303,jdbcType=VARCHAR},
      amt_cum_30_3 = #{amtCum303,jdbcType=VARCHAR},
      amt_rate_30_3 = #{amtRate303,jdbcType=VARCHAR},
      profit_amt_cum_30_3 = #{profitAmtCum303,jdbcType=VARCHAR},
      profit_rate_30_3 = #{profitRate303,jdbcType=VARCHAR},
      amt_inv_new_3 = #{amtInvNew3,jdbcType=VARCHAR},
      sku_cnt_total_4 = #{skuCntTotal4,jdbcType=VARCHAR},
      sku_cnt_4 = #{skuCnt4,jdbcType=VARCHAR},
      sku_cnt_new_4 = #{skuCntNew4,jdbcType=VARCHAR},
      num_cum_30_4 = #{numCum304,jdbcType=VARCHAR},
      amt_cum_30_4 = #{amtCum304,jdbcType=VARCHAR},
      amt_rate_30_4 = #{amtRate304,jdbcType=VARCHAR},
      profit_amt_cum_30_4 = #{profitAmtCum304,jdbcType=VARCHAR},
      profit_rate_30_4 = #{profitRate304,jdbcType=VARCHAR},
      amt_inv_new_4 = #{amtInvNew4,jdbcType=VARCHAR},
      sku_cnt_total_5 = #{skuCntTotal5,jdbcType=VARCHAR},
      sku_cnt_5 = #{skuCnt5,jdbcType=VARCHAR},
      sku_cnt_new_5 = #{skuCntNew5,jdbcType=VARCHAR},
      num_cum_30_5 = #{numCum305,jdbcType=VARCHAR},
      amt_cum_30_5 = #{amtCum305,jdbcType=VARCHAR},
      amt_rate_30_5 = #{amtRate305,jdbcType=VARCHAR},
      profit_amt_cum_30_5 = #{profitAmtCum305,jdbcType=VARCHAR},
      profit_rate_30_5 = #{profitRate305,jdbcType=VARCHAR},
      amt_inv_new_5 = #{amtInvNew5,jdbcType=VARCHAR},
      sku_cnt_total_6 = #{skuCntTotal6,jdbcType=VARCHAR},
      sku_cnt_6 = #{skuCnt6,jdbcType=VARCHAR},
      sku_cnt_new_6 = #{skuCntNew6,jdbcType=VARCHAR},
      num_cum_30_6 = #{numCum306,jdbcType=VARCHAR},
      amt_cum_30_6 = #{amtCum306,jdbcType=VARCHAR},
      amt_rate_30_6 = #{amtRate306,jdbcType=VARCHAR},
      profit_amt_cum_30_6 = #{profitAmtCum306,jdbcType=VARCHAR},
      profit_rate_30_6 = #{profitRate306,jdbcType=VARCHAR},
      amt_inv_new_6 = #{amtInvNew6,jdbcType=VARCHAR},
      sku_cnt_total_7 = #{skuCntTotal7,jdbcType=VARCHAR},
      sku_cnt_7 = #{skuCnt7,jdbcType=VARCHAR},
      sku_cnt_new_7 = #{skuCntNew7,jdbcType=VARCHAR},
      num_cum_30_7 = #{numCum307,jdbcType=VARCHAR},
      amt_cum_30_7 = #{amtCum307,jdbcType=VARCHAR},
      amt_rate_30_7 = #{amtRate307,jdbcType=VARCHAR},
      profit_amt_cum_30_7 = #{profitAmtCum307,jdbcType=VARCHAR},
      profit_rate_30_7 = #{profitRate307,jdbcType=VARCHAR},
      amt_inv_new_7 = #{amtInvNew7,jdbcType=VARCHAR},
      sku_cnt_total_8 = #{skuCntTotal8,jdbcType=VARCHAR},
      sku_cnt_8 = #{skuCnt8,jdbcType=VARCHAR},
      sku_cnt_new_8 = #{skuCntNew8,jdbcType=VARCHAR},
      num_cum_30_8 = #{numCum308,jdbcType=VARCHAR},
      amt_cum_30_8 = #{amtCum308,jdbcType=VARCHAR},
      amt_rate_30_8 = #{amtRate308,jdbcType=VARCHAR},
      profit_amt_cum_30_8 = #{profitAmtCum308,jdbcType=VARCHAR},
      profit_rate_30_8 = #{profitRate308,jdbcType=VARCHAR},
      amt_inv_new_8 = #{amtInvNew8,jdbcType=VARCHAR},
      sku_cnt_total_9 = #{skuCntTotal9,jdbcType=VARCHAR},
      sku_cnt_9 = #{skuCnt9,jdbcType=VARCHAR},
      sku_cnt_new_9 = #{skuCntNew9,jdbcType=VARCHAR},
      num_cum_30_9 = #{numCum309,jdbcType=VARCHAR},
      amt_cum_30_9 = #{amtCum309,jdbcType=VARCHAR},
      amt_rate_30_9 = #{amtRate309,jdbcType=VARCHAR},
      profit_amt_cum_30_9 = #{profitAmtCum309,jdbcType=VARCHAR},
      profit_rate_30_9 = #{profitRate309,jdbcType=VARCHAR},
      amt_inv_new_9 = #{amtInvNew9,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>