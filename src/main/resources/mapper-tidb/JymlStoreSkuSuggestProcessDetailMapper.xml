<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.JymlStoreSkuSuggestProcessDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="upper_limit" jdbcType="INTEGER" property="upperLimit" />
    <result column="sku_lower_limit" jdbcType="INTEGER" property="skuLowerLimit" />
    <result column="sku_max_limit" jdbcType="INTEGER" property="skuMaxLimit" />
    <result column="sku_count" jdbcType="INTEGER" property="skuCount" />
    <result column="confirmed" jdbcType="INTEGER" property="confirmed" />
    <result column="confirmed_by" jdbcType="BIGINT" property="confirmedBy" />
    <result column="confirmed_name" jdbcType="VARCHAR" property="confirmedName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_org_id, store_id, store_code, category, category_name, rx_otc, middle_category,
    middle_category_name, small_category, small_category_name, sub_category, sub_category_name,
    upper_limit, sku_lower_limit, sku_max_limit, sku_count, confirmed, confirmed_by,
    confirmed_name, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_suggest_process_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jyml_store_sku_suggest_process_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_suggest_process_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetailExample">
    delete from jyml_store_sku_suggest_process_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail">
    insert into jyml_store_sku_suggest_process_detail (id, business_org_id, store_id,
    store_code, category, category_name,
    rx_otc, middle_category, middle_category_name,
    small_category, small_category_name, sub_category,
    sub_category_name, upper_limit, sku_lower_limit,
    sku_max_limit, sku_count, confirmed,
    confirmed_by, confirmed_name, `status`,
    gmt_create, gmt_update, extend,
    version, created_by, created_name,
    updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{businessOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
    #{storeCode,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR},
    #{rxOtc,jdbcType=VARCHAR}, #{middleCategory,jdbcType=VARCHAR}, #{middleCategoryName,jdbcType=VARCHAR},
    #{smallCategory,jdbcType=VARCHAR}, #{smallCategoryName,jdbcType=VARCHAR}, #{subCategory,jdbcType=VARCHAR},
    #{subCategoryName,jdbcType=VARCHAR}, #{upperLimit,jdbcType=INTEGER}, #{skuLowerLimit,jdbcType=INTEGER},
    #{skuMaxLimit,jdbcType=INTEGER}, #{skuCount,jdbcType=INTEGER}, #{confirmed,jdbcType=INTEGER},
    #{confirmedBy,jdbcType=BIGINT}, #{confirmedName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
    #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR},
    #{version,jdbcType=BIGINT}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR},
    #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail">
    insert into jyml_store_sku_suggest_process_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="rxOtc != null">
        rx_otc,
      </if>
      <if test="middleCategory != null">
        middle_category,
      </if>
      <if test="middleCategoryName != null">
        middle_category_name,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="smallCategoryName != null">
        small_category_name,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="skuLowerLimit != null">
        sku_lower_limit,
      </if>
      <if test="skuMaxLimit != null">
        sku_max_limit,
      </if>
      <if test="skuCount != null">
        sku_count,
      </if>
      <if test="confirmed != null">
        confirmed,
      </if>
      <if test="confirmedBy != null">
        confirmed_by,
      </if>
      <if test="confirmedName != null">
        confirmed_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="rxOtc != null">
        #{rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=INTEGER},
      </if>
      <if test="skuLowerLimit != null">
        #{skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="skuMaxLimit != null">
        #{skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="skuCount != null">
        #{skuCount,jdbcType=INTEGER},
      </if>
      <if test="confirmed != null">
        #{confirmed,jdbcType=INTEGER},
      </if>
      <if test="confirmedBy != null">
        #{confirmedBy,jdbcType=BIGINT},
      </if>
      <if test="confirmedName != null">
        #{confirmedName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetailExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_suggest_process_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_suggest_process_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.rxOtc != null">
        rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategoryName != null">
        middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.upperLimit != null">
        upper_limit = #{record.upperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuLowerLimit != null">
        sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuMaxLimit != null">
        sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuCount != null">
        sku_count = #{record.skuCount,jdbcType=INTEGER},
      </if>
      <if test="record.confirmed != null">
        confirmed = #{record.confirmed,jdbcType=INTEGER},
      </if>
      <if test="record.confirmedBy != null">
        confirmed_by = #{record.confirmedBy,jdbcType=BIGINT},
      </if>
      <if test="record.confirmedName != null">
        confirmed_name = #{record.confirmedName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_suggest_process_detail
    set id = #{record.id,jdbcType=BIGINT},
    business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
    store_id = #{record.storeId,jdbcType=BIGINT},
    store_code = #{record.storeCode,jdbcType=VARCHAR},
    category = #{record.category,jdbcType=VARCHAR},
    category_name = #{record.categoryName,jdbcType=VARCHAR},
    rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
    middle_category = #{record.middleCategory,jdbcType=VARCHAR},
    middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
    small_category = #{record.smallCategory,jdbcType=VARCHAR},
    small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
    sub_category = #{record.subCategory,jdbcType=VARCHAR},
    sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
    upper_limit = #{record.upperLimit,jdbcType=INTEGER},
    sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
    sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
    sku_count = #{record.skuCount,jdbcType=INTEGER},
    confirmed = #{record.confirmed,jdbcType=INTEGER},
    confirmed_by = #{record.confirmedBy,jdbcType=BIGINT},
    confirmed_name = #{record.confirmedName,jdbcType=VARCHAR},
    `status` = #{record.status,jdbcType=TINYINT},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{record.extend,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=BIGINT},
    created_by = #{record.createdBy,jdbcType=BIGINT},
    created_name = #{record.createdName,jdbcType=VARCHAR},
    updated_by = #{record.updatedBy,jdbcType=BIGINT},
    updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail">
    update jyml_store_sku_suggest_process_detail
    <set>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="rxOtc != null">
        rx_otc = #{rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        middle_category = #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=INTEGER},
      </if>
      <if test="skuLowerLimit != null">
        sku_lower_limit = #{skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="skuMaxLimit != null">
        sku_max_limit = #{skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="skuCount != null">
        sku_count = #{skuCount,jdbcType=INTEGER},
      </if>
      <if test="confirmed != null">
        confirmed = #{confirmed,jdbcType=INTEGER},
      </if>
      <if test="confirmedBy != null">
        confirmed_by = #{confirmedBy,jdbcType=BIGINT},
      </if>
      <if test="confirmedName != null">
        confirmed_name = #{confirmedName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail">
    update jyml_store_sku_suggest_process_detail
    set business_org_id = #{businessOrgId,jdbcType=BIGINT},
    store_id = #{storeId,jdbcType=BIGINT},
    store_code = #{storeCode,jdbcType=VARCHAR},
    category = #{category,jdbcType=VARCHAR},
    category_name = #{categoryName,jdbcType=VARCHAR},
    rx_otc = #{rxOtc,jdbcType=VARCHAR},
    middle_category = #{middleCategory,jdbcType=VARCHAR},
    middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
    small_category = #{smallCategory,jdbcType=VARCHAR},
    small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
    sub_category = #{subCategory,jdbcType=VARCHAR},
    sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
    upper_limit = #{upperLimit,jdbcType=INTEGER},
    sku_lower_limit = #{skuLowerLimit,jdbcType=INTEGER},
    sku_max_limit = #{skuMaxLimit,jdbcType=INTEGER},
    sku_count = #{skuCount,jdbcType=INTEGER},
    confirmed = #{confirmed,jdbcType=INTEGER},
    confirmed_by = #{confirmedBy,jdbcType=BIGINT},
    confirmed_name = #{confirmedName,jdbcType=VARCHAR},
    `status` = #{status,jdbcType=TINYINT},
    gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{extend,jdbcType=VARCHAR},
    version = #{version,jdbcType=BIGINT},
    created_by = #{createdBy,jdbcType=BIGINT},
    created_name = #{createdName,jdbcType=VARCHAR},
    updated_by = #{updatedBy,jdbcType=BIGINT},
    updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsertSelective" parameterType="java.util.List">
    insert into jyml_store_sku_suggest_process_detail (
    business_org_id, store_id,
    store_code, category, category_name,
    rx_otc, middle_category, middle_category_name,
    small_category, small_category_name, sub_category,
    sub_category_name, upper_limit, sku_lower_limit,sku_max_limit,
    sku_count, confirmed,version,extend
    )
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.businessOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}, #{item.categoryName,jdbcType=VARCHAR},
      #{item.rxOtc,jdbcType=VARCHAR}, #{item.middleCategory,jdbcType=VARCHAR}, #{item.middleCategoryName,jdbcType=VARCHAR},
      #{item.smallCategory,jdbcType=VARCHAR}, #{item.smallCategoryName,jdbcType=VARCHAR}, #{item.subCategory,jdbcType=VARCHAR},
      #{item.subCategoryName,jdbcType=VARCHAR}, #{item.upperLimit,jdbcType=INTEGER}, #{item.skuLowerLimit,jdbcType=INTEGER}, #{item.skuMaxLimit,jdbcType=INTEGER},
      #{item.skuCount,jdbcType=INTEGER}, #{item.confirmed,jdbcType=INTEGER},#{item.version,jdbcType=BIGINT},#{item.extend,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>


</mapper>