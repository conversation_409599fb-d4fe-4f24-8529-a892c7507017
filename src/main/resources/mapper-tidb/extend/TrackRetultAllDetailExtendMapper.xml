<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackRetultAllDetailExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultAllDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
        <result column="plat_orgid" jdbcType="VARCHAR" property="platOrgid" />
        <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
        <result column="compid" jdbcType="VARCHAR" property="compid" />
        <result column="data_from_v2" jdbcType="VARCHAR" property="dataFromV2" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="org_no" jdbcType="VARCHAR" property="orgNo" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="revise_store_group" jdbcType="VARCHAR" property="reviseStoreGroup" />
        <result column="saleslevel" jdbcType="VARCHAR" property="saleslevel" />
        <result column="tradingarea" jdbcType="VARCHAR" property="tradingarea" />
        <result column="store_group_zy" jdbcType="VARCHAR" property="storeGroupZy" />
        <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
        <result column="goodsname" jdbcType="VARCHAR" property="goodsname" />
        <result column="level" jdbcType="VARCHAR" property="level" />
        <result column="bak" jdbcType="VARCHAR" property="bak" />
        <result column="sub_category_id" jdbcType="VARCHAR" property="subCategoryId" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="new_ind_bak" jdbcType="VARCHAR" property="newIndBak" />
        <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
        <result column="goodsspec" jdbcType="VARCHAR" property="goodsspec" />
        <result column="jx_cate1_name" jdbcType="VARCHAR" property="jxCate1Name" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
        <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
        <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
        <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
        <result column="component" jdbcType="VARCHAR" property="component" />
        <result column="is_otc" jdbcType="VARCHAR" property="isOtc" />
        <result column="flag_disease" jdbcType="VARCHAR" property="flagDisease" />
        <result column="grossprofit" jdbcType="VARCHAR" property="grossprofit" />
        <result column="taotai_type" jdbcType="VARCHAR" property="taotaiType" />
        <result column="stjb" jdbcType="VARCHAR" property="stjb" />
        <result column="specialattributes_type" jdbcType="VARCHAR" property="specialattributesType" />
        <result column="in_stock_rate" jdbcType="VARCHAR" property="inStockRate" />
        <result column="in_sales_rate" jdbcType="VARCHAR" property="inSalesRate" />
        <result column="sku_int" jdbcType="VARCHAR" property="skuInt" />
        <result column="cost_amt" jdbcType="VARCHAR" property="costAmt" />
        <result column="stall_no_num" jdbcType="VARCHAR" property="stallNoNum" />
        <result column="ph_org_bz_flag" jdbcType="VARCHAR" property="phOrgBzFlag" />
        <result column="store_type" jdbcType="VARCHAR" property="storeType" />
        <result column="num_cum_90" jdbcType="VARCHAR" property="numCum90" />
        <result column="amt_cum_90" jdbcType="VARCHAR" property="amtCum90" />
        <result column="profit_amt_cum_90" jdbcType="VARCHAR" property="profitAmtCum90" />
        <result column="profit_rate_90" jdbcType="VARCHAR" property="profitRate90" />
        <result column="amt_rate_org_90" jdbcType="VARCHAR" property="amtRateOrg90" />
        <result column="amt_rate_comp_90" jdbcType="VARCHAR" property="amtRateComp90" />
        <result column="num_cum_180" jdbcType="VARCHAR" property="numCum180" />
        <result column="amt_cum_180" jdbcType="VARCHAR" property="amtCum180" />
        <result column="profit_amt_cum_180" jdbcType="VARCHAR" property="profitAmtCum180" />
        <result column="profit_rate_180" jdbcType="VARCHAR" property="profitRate180" />
        <result column="amt_rate_org_180" jdbcType="VARCHAR" property="amtRateOrg180" />
        <result column="amt_rate_comp_180" jdbcType="VARCHAR" property="amtRateComp180" />
        <result column="revise_num_cum_90" jdbcType="VARCHAR" property="reviseNumCum90" />
        <result column="revise_amt_cum_90" jdbcType="VARCHAR" property="reviseAmtCum90" />
        <result column="revise_profit_amt_cum_90" jdbcType="VARCHAR" property="reviseProfitAmtCum90" />
        <result column="revise_profit_rate_90" jdbcType="VARCHAR" property="reviseProfitRate90" />
        <result column="revise_num_cum_180" jdbcType="VARCHAR" property="reviseNumCum180" />
        <result column="revise_amt_cum_180" jdbcType="VARCHAR" property="reviseAmtCum180" />
        <result column="revise_profit_amt_cum_180" jdbcType="VARCHAR" property="reviseProfitAmtCum180" />
        <result column="revise_profit_rate_180" jdbcType="VARCHAR" property="reviseProfitRate180" />
        <result column="retail_price" jdbcType="VARCHAR" property="retailPrice"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="status" jdbcType="TINYINT" property="status" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, zone_new, plat_orgid, data_from, compid, data_from_v2, city, org_no,
        store_name, revise_store_group, saleslevel, tradingarea, store_group_zy, goods_id,
        goodsname, `level`, bak, sub_category_id, remark, new_ind_bak, goodsunit, goodsspec,
        jx_cate1_name, manufacturer, classone_name, classtwo_name, classthree_name, classfour_name,
        component, is_otc, flag_disease, grossprofit, taotai_type, stjb, specialattributes_type,
        in_stock_rate, in_sales_rate, sku_int, cost_amt, stall_no_num, ph_org_bz_flag, store_type,
        num_cum_90, amt_cum_90, profit_amt_cum_90, profit_rate_90, amt_rate_org_90, amt_rate_comp_90,
        num_cum_180, amt_cum_180, profit_amt_cum_180, profit_rate_180, amt_rate_org_180,
        amt_rate_comp_180, retail_price, revise_num_cum_90, revise_amt_cum_90, revise_profit_amt_cum_90,
        revise_profit_rate_90, revise_num_cum_180, revise_amt_cum_180, revise_profit_amt_cum_180,
        revise_profit_rate_180, gmt_create, `status`
    </sql>
    <select id="countTrackRetultAllDetail" resultType="java.lang.Long">
        SELECT count(*)
        FROM track_retult_all_detail_${taskId}
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="compid !=null">
            and compid= #{compid,jdbcType=VARCHAR}
        </if>
        <if test="levelList !=null and levelList.size>0">
            and level in
            <foreach close=")" collection="levelList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectTrackRetultAllDetailByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_all_detail_${taskId}
        where `status` = 0
        <if test="taskId != null">
            and task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="compid !=null">
            and compid= #{compid,jdbcType=VARCHAR}
        </if>
        <if test="levelList !=null and levelList.size>0">
            and level in
            <foreach close=")" collection="levelList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="id != null">
            and id>#{id,jdbcType=BIGINT}
        </if>
        order by id
        <if test="perPage >= 0">
            limit ${perPage}
        </if>
    </select>


    <select id="selectTrackRetultCompid" resultType="com.cowell.scib.service.dto.TrackResultTaskCompidDTO">
        SELECT task_id,compid
        FROM track_retult_all_detail_${taskId}
        where task_id = #{taskId,jdbcType=BIGINT}
        <if test="levelList !=null and levelList.size>0">
            and level in
            <foreach close=")" collection="levelList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        group by compid
    </select>

    <select id="queryTrackRetultDetailByGoods" parameterType="com.cowell.scib.service.dto.TrackRetultDetailParam"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_all_detail_${taskId}
        where
        <if test="taskId != null">
            task_id = #{taskId ,jdbcType=BIGINT}
        </if>
        <if test="platOrgid != null">
            and plat_orgid = #{platOrgid ,jdbcType=VARCHAR}
        </if>
        <if test="compid != null">
            and compid = #{compid ,jdbcType=VARCHAR}
        </if>
        <if test="city != null">
            and city = #{city ,jdbcType=VARCHAR}
        </if>
        <if test="orgNoList != null and orgNoList.size>0">
            and org_no in
            <foreach close=")" collection="orgNoList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="reviseStoreGroup != null">
            and revise_store_group = #{reviseStoreGroup ,jdbcType=VARCHAR}
        </if>
        <if test="goodsId != null">
            and goods_id = #{goodsId ,jdbcType=VARCHAR}
        </if>
        <if test="levelList !=null and levelList.size>0">
            and level in
            <foreach close=")" collection="levelList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="status != null">
            and status = #{status ,jdbcType=TINYINT}
        </if>
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>
    </select>
    <update id="delTrackRetultAllDetailById">
        update track_retult_all_detail_${taskId} set status=-1
        where
        <if test="idList !=null and idList.size>0">
            id in
            <foreach close=")" collection="idList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateLevel">
        update track_retult_all_detail_${taskId} set level=#{level ,jdbcType=VARCHAR}
        where
        <if test="idList !=null and idList.size>0">
            id in
            <foreach close=")" collection="idList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into track_retult_all_detail_${taskId} (task_id, zone_new, plat_orgid, data_from, compid, data_from_v2,
        city, org_no,store_name,
        revise_store_group, goods_id,goodsname,`level`, sub_category_id, goodsunit, goodsspec, jx_cate1_name,
        component,gmt_create, `status`)
        values
        <if test="list != null and list.size() != 0">
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.taskId,jdbcType=BIGINT}, #{item.zoneNew,jdbcType=VARCHAR}, #{item.platOrgid,jdbcType=VARCHAR},
                #{item.dataFrom,jdbcType=VARCHAR}, #{item.compid,jdbcType=VARCHAR}, #{item.dataFromV2,jdbcType=VARCHAR},
                #{item.city,jdbcType=VARCHAR}, #{item.orgNo,jdbcType=VARCHAR},#{item.storeName,jdbcType=VARCHAR},#{item.reviseStoreGroup,jdbcType=VARCHAR},
                #{item.goodsId,jdbcType=VARCHAR}, #{item.goodsname,jdbcType=VARCHAR}, #{item.level,jdbcType=VARCHAR}, #{item.subCategoryId,jdbcType=VARCHAR},
                #{item.goodsunit,jdbcType=VARCHAR}, #{item.goodsspec,jdbcType=VARCHAR}, #{item.jxCate1Name,jdbcType=VARCHAR},
                #{item.component,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.status,jdbcType=TINYINT})
            </foreach>
        </if>
    </insert>

    <insert id="createTable" parameterType="java.lang.String">
        DROP TABLE IF EXISTS ${tableName};
        CREATE
        TABLE
        ${tableName} (
        id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
        task_id bigint(20) NOT NULL DEFAULT 0 COMMENT '任务ID',
        zone_new varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '平台',
        plat_orgid varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '平台orgid',
        data_from varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '企业名称',
        compid varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '企业orgid',
        data_from_v2 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '管理主体',
        city varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
        org_no varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '门店MDM编码',
        store_name varchar(100)  CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '门店名称',
        revise_store_group varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '店型',
        saleslevel varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '门店月销售额等级',
        tradingarea varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '门店选址商圈店型',
        store_group_zy varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '门店中参店型',
        goods_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'SKU',
        goodsname varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商品名称',
        level varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '必备层级',
        bak varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '引入原因',
        sub_category_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子类id',
        remark varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '该品种在该门店当前的经营状态',
        new_ind_bak varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '新/旧成分新品',
        goodsunit varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '单位',
        goodsspec varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '规格',
        jx_cate1_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '剂型',
        manufacturer varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '厂家名称',
        classone_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '大类',
        classtwo_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '中类',
        classthree_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '小类',
        classfour_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子类',
        component varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '成分',
        is_otc varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '处方类别',
        flag_disease varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '疾病种',
        grossprofit varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '采购属性',
        taotai_type varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '经营属性',
        stjb varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '销售属性',
        specialattributes_type varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '特殊销售属性',
        retail_price varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '参考零售价',
        in_stock_rate varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '店型在库率',
        in_sales_rate varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '店型90天动销率',
        sku_int varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '成分必备SKU(平台+企业)',
        cost_amt varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '库存增长额',
        stall_no_num varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '门店现有库存数',
        ph_org_bz_flag varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '挂网是否考核（只看非中参的统采品）',
        store_type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '门店类型',
        num_cum_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前90天销售数量',
        amt_cum_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前90天销售金额',
        profit_amt_cum_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前90天毛利额',
        profit_rate_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前90天毛利率',
        amt_rate_org_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前90天销售占比（单品/门店总销售）',
        amt_rate_comp_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前90天成分销售占比（单品/门店成分销售）',
        num_cum_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前180天销售数量',
        amt_cum_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前180天销售金额',
        profit_amt_cum_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前180天毛利额',
        profit_rate_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前180天毛利率',
        amt_rate_org_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前180天销售占比（单品/门店总销售）',
        amt_rate_comp_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '前180天成分销售占比（单品/门店成分销售）',
        revise_num_cum_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前90天销售数量',
        revise_amt_cum_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前90天销售金额',
        revise_profit_amt_cum_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前90天毛利额',
        revise_profit_rate_90 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前90天毛利率',
        revise_num_cum_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前180天销售数量',
        revise_amt_cum_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前180天销售金额',
        revise_profit_amt_cum_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前180天毛利额',
        revise_profit_rate_180 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '模型使用_前180天毛利率',
        gmt_create timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        status tinyint(3) NOT NULL DEFAULT 0 COMMENT '状态(-1删除，0正常)',
        PRIMARY KEY (id) USING BTREE,
        INDEX inx_taskid_level(task_id, level) USING BTREE,
        INDEX inx_taskid_zonenew_storegroup_goodsid_level(task_id, revise_store_group, goods_id, level) USING BTREE,
        INDEX inx_taskid_zonenew_datafrom_city_goodsid_level(task_id, compid, city, goods_id, level) USING BTREE,
        INDEX inx_taskid_zonenew_datafrom_city_storegroup_goodsid_level(task_id, compid, city, revise_store_group, goods_id, level) USING BTREE,
        INDEX inx_taskid_zonenew_datafrom_city_orgno_goodsid_level(task_id, compid, city, org_no, goods_id, level) USING BTREE,
        INDEX inx_task_goods_id_level_status (task_id,goods_id,level,status) USING BTREE,
        INDEX inx_taskid_orgno_goodsid_level_status (task_id,org_no,goods_id,level,status)  USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '必备目录全量表(一店一目级别)';
    </insert>
    <insert id="createNewStoreRecommendTable" parameterType="java.lang.String">
        DROP TABLE IF EXISTS ${tableName};
        CREATE
        TABLE
        ${tableName} (
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
        `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务ID',
        `org_no` varchar(20) NOT NULL DEFAULT '' COMMENT '门店MDM编码',
        `store_name` varchar(100) NOT NULL DEFAULT '' COMMENT '门店名称',
        `goods_id` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
        `goodsname` varchar(128) NOT NULL DEFAULT '' COMMENT '商品名称',
        `goodsspec` varchar(256) DEFAULT '' COMMENT '规格',
        `manufacturer` varchar(256) NOT NULL DEFAULT '' COMMENT '厂家名称',
        `jx_cate1_name` varchar(50) NOT NULL DEFAULT '' COMMENT '剂型',
        `goodsunit` varchar(32) NOT NULL DEFAULT '' COMMENT '单位',
        `level` varchar(100) NOT NULL DEFAULT '' COMMENT '推荐来源',
        `suggest_ph_qty` decimal(20,4) NOT NULL DEFAULT '0.0' COMMENT '建议首次备货数量',
        `ph_cost` decimal(20,4) NOT NULL DEFAULT '0.0' COMMENT '备货库存成本金额',
        `taotai_type` varchar(50) NOT NULL DEFAULT '' COMMENT '经营属性',
        `stjb` varchar(50) NOT NULL DEFAULT '' COMMENT '销售属性',
        `grossprofit` varchar(50) NOT NULL DEFAULT '' COMMENT '采购属性',
        `sub_category_id` varchar(50) NOT NULL DEFAULT '' COMMENT '子类id',
        `classone_name` varchar(50) NOT NULL DEFAULT '' COMMENT '大类',
        `classtwo_name` varchar(50) NOT NULL DEFAULT '' COMMENT '中类',
        `classthree_name` varchar(50) NOT NULL DEFAULT '' COMMENT '小类',
        `classfour_name` varchar(50) NOT NULL DEFAULT '' COMMENT '子类',
        `component` varchar(50) NOT NULL DEFAULT '' COMMENT '成分',
        `dtpgood` varchar(100) NOT NULL DEFAULT '' COMMENT 'DTP商品(D)',
        `flag_disease` varchar(32) NOT NULL DEFAULT '' COMMENT '疾病种',
        `department` varchar(32) NOT NULL DEFAULT '' COMMENT '归属部门',
        `distribind` varchar(32) NOT NULL DEFAULT '' COMMENT '禁止配送',
        `precious` varchar(32) NOT NULL DEFAULT '' COMMENT '是否贵重',
        `refretailprice` varchar(32) NOT NULL DEFAULT '0' COMMENT '参考零售价',
        `in_stock_rate_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-本企业本城市本店型',
        `in_sales_rate_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-本企业本城市本店型',
        `num_cum_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-本企业本城市本店型',
        `bill_cnts_cum_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-本企业本城市本店型',
        `amt_cum_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-本企业本城市本店型',
        `profit_rate_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-本企业本城市本店型',
        `in_stock_rate_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-本企业本城市',
        `in_sales_rate_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-本企业本城市',
        `num_cum_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-本企业本城市',
        `bill_cnts_cum_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-本企业本城市',
        `amt_cum_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-本企业本城市',
        `profit_rate_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-本企业本城市',
        `in_stock_rate_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-本企业',
        `in_sales_rate_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-本企业',
        `num_cum_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-本企业',
        `bill_cnts_cum_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-本企业',
        `amt_cum_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-本企业',
        `profit_rate_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-本企业',
        `in_stock_rate_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-相似门店',
        `in_sales_rate_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-相似门店',
        `num_cum_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-相似门店',
        `bill_cnts_cum_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-相似门店',
        `amt_cum_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-相似门店',
        `profit_rate_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-相似门店',
        `jy_able` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否经营 0 否 1是',
        `rx_otc` varchar(32) DEFAULT '' COMMENT 'rx/otc',
        `bak1` varchar(100) DEFAULT '',
        `bak2` varchar(100) DEFAULT '',
        `bak3` varchar(100) DEFAULT '',
        `bak4` varchar(100) DEFAULT '',
        `bak5` varchar(100) DEFAULT '',
        `extend` varchar(2000) DEFAULT '' COMMENT '扩展字段',
        `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态(-1删除，0正常)',
        PRIMARY KEY (`id`) ,
        KEY `inx_taskid_store` (`task_id`,`org_no`,`status`)
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT='新店推荐目录';
    </insert>
    <insert id="createNewStoreRecommendRankTable" parameterType="java.lang.String">
        DROP TABLE IF EXISTS ${tableName};
        CREATE
        TABLE
        ${tableName} (
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
        `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务ID',
        `rank` int(10) NOT NULL DEFAULT '0' COMMENT '排名',
        `org_no` varchar(20) NOT NULL DEFAULT '' COMMENT '门店MDM编码',
        `store_name` varchar(100) NOT NULL DEFAULT '' COMMENT '门店名称',
        `goods_id` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
        `goodsname` varchar(128) NOT NULL DEFAULT '' COMMENT '商品名称',
        `goodsspec` varchar(256) DEFAULT '' COMMENT '规格',
        `manufacturer` varchar(256) NOT NULL DEFAULT '' COMMENT '厂家名称',
        `jx_cate1_name` varchar(50) NOT NULL DEFAULT '' COMMENT '剂型',
        `goodsunit` varchar(32) NOT NULL DEFAULT '' COMMENT '单位',
        `level` varchar(100) NOT NULL DEFAULT '' COMMENT '推荐来源',
        `suggest_ph_qty` decimal(20,4) NOT NULL DEFAULT '0.0' COMMENT '建议首次备货数量',
        `ph_cost` decimal(20,4) NOT NULL DEFAULT '0.0' COMMENT '备货库存成本金额',
        `taotai_type` varchar(50) NOT NULL DEFAULT '' COMMENT '经营属性',
        `stjb` varchar(50) NOT NULL DEFAULT '' COMMENT '销售属性',
        `grossprofit` varchar(50) NOT NULL DEFAULT '' COMMENT '采购属性',
        `sub_category_id` varchar(50) NOT NULL DEFAULT '' COMMENT '子类id',
        `classone_name` varchar(50) NOT NULL DEFAULT '' COMMENT '大类',
        `classtwo_name` varchar(50) NOT NULL DEFAULT '' COMMENT '中类',
        `classthree_name` varchar(50) NOT NULL DEFAULT '' COMMENT '小类',
        `classfour_name` varchar(50) NOT NULL DEFAULT '' COMMENT '子类',
        `component` varchar(50) NOT NULL DEFAULT '' COMMENT '成分',
        `dtpgood` varchar(100) NOT NULL DEFAULT '' COMMENT 'DTP商品(D)',
        `flag_disease` varchar(32) NOT NULL DEFAULT '' COMMENT '疾病种',
        `department` varchar(32) NOT NULL DEFAULT '' COMMENT '归属部门',
        `distribind` varchar(32) NOT NULL DEFAULT '' COMMENT '禁止配送',
        `precious` varchar(32) NOT NULL DEFAULT '' COMMENT '是否贵重',
        `refretailprice` varchar(32) NOT NULL DEFAULT '0' COMMENT '参考零售价',
        `in_stock_rate_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-本企业本城市本店型',
        `in_sales_rate_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-本企业本城市本店型',
        `num_cum_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-本企业本城市本店型',
        `bill_cnts_cum_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-本企业本城市本店型',
        `amt_cum_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-本企业本城市本店型',
        `profit_rate_90_dx` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-本企业本城市本店型',
        `in_stock_rate_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-本企业本城市',
        `in_sales_rate_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-本企业本城市',
        `num_cum_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-本企业本城市',
        `bill_cnts_cum_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-本企业本城市',
        `amt_cum_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-本企业本城市',
        `profit_rate_90_city` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-本企业本城市',
        `in_stock_rate_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-本企业',
        `in_sales_rate_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-本企业',
        `num_cum_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-本企业',
        `bill_cnts_cum_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-本企业',
        `amt_cum_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-本企业',
        `profit_rate_90_qy` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-本企业',
        `in_stock_rate_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '集中度-相似门店',
        `in_sales_rate_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '动销率-相似门店',
        `num_cum_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售数量-相似门店',
        `bill_cnts_cum_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天客流量-相似门店',
        `amt_cum_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '近90天销售金额-相似门店',
        `profit_rate_90_md` varchar(32) NOT NULL DEFAULT '0' COMMENT '毛利率-相似门店',
        `jy_able` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否经营 0 否 1是',
        `rx_otc` varchar(32) DEFAULT '' COMMENT 'rx/otc',
        `bak1` varchar(100) DEFAULT '',
        `bak2` varchar(100) DEFAULT '',
        `bak3` varchar(100) DEFAULT '',
        `bak4` varchar(100) DEFAULT '',
        `bak5` varchar(100) DEFAULT '',
        `extend` varchar(2000) DEFAULT '' COMMENT '扩展字段',
        `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态(-1删除，0正常)',
        PRIMARY KEY (`id`),
        KEY `inx_taskid_store` (`task_id`,`org_no`,`status`)
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT='新店推荐目录排名';
    </insert>

    <delete id="batchDel">
        delete from track_retult_all_detail_${taskId} where
        <if test="ids != null and ids.size > 0">
            id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>
