<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackRetultTop4levelStoregroupExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
        <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
        <result column="data_from_v2" jdbcType="VARCHAR" property="dataFromV2" />
        <result column="orgId" jdbcType="VARCHAR" property="orgid" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="revise_store_group" jdbcType="VARCHAR" property="reviseStoreGroup" />
        <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
        <result column="goodsname" jdbcType="VARCHAR" property="goodsname" />
        <result column="level" jdbcType="VARCHAR" property="level" />
        <result column="bak" jdbcType="VARCHAR" property="bak" />
        <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
        <result column="goodsspec" jdbcType="VARCHAR" property="goodsspec" />
        <result column="jx_cate1_name" jdbcType="VARCHAR" property="jxCate1Name" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
        <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
        <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
        <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
        <result column="component" jdbcType="VARCHAR" property="component" />
        <result column="is_otc" jdbcType="VARCHAR" property="isOtc" />
        <result column="flag_disease" jdbcType="VARCHAR" property="flagDisease" />
        <result column="grossprofit" jdbcType="VARCHAR" property="grossprofit" />
        <result column="taotai_type" jdbcType="VARCHAR" property="taotaiType" />
        <result column="stjb" jdbcType="VARCHAR" property="stjb" />
        <result column="specialattributes_type" jdbcType="VARCHAR" property="specialattributesType" />
        <result column="in_stock_rate" jdbcType="VARCHAR" property="inStockRate" />
        <result column="in_sales_rate" jdbcType="VARCHAR" property="inSalesRate" />
        <result column="sku_int" jdbcType="VARCHAR" property="skuInt" />
        <result column="ph_org_bz_flag" jdbcType="VARCHAR" property="phOrgBzFlag" />
        <result column="num_cum_90" jdbcType="VARCHAR" property="numCum90" />
        <result column="amt_cum_90" jdbcType="VARCHAR" property="amtCum90" />
        <result column="profit_amt_cum_90" jdbcType="VARCHAR" property="profitAmtCum90" />
        <result column="profit_rate_90" jdbcType="VARCHAR" property="profitRate90" />
        <result column="amt_rate_org_90" jdbcType="VARCHAR" property="amtRateOrg90" />
        <result column="amt_rate_comp_90" jdbcType="VARCHAR" property="amtRateComp90" />
        <result column="num_cum_180" jdbcType="VARCHAR" property="numCum180" />
        <result column="amt_cum_180" jdbcType="VARCHAR" property="amtCum180" />
        <result column="profit_amt_cum_180" jdbcType="VARCHAR" property="profitAmtCum180" />
        <result column="profit_rate_180" jdbcType="VARCHAR" property="profitRate180" />
        <result column="amt_rate_org_180" jdbcType="VARCHAR" property="amtRateOrg180" />
        <result column="amt_rate_comp_180" jdbcType="VARCHAR" property="amtRateComp180" />
        <result column="store_num" jdbcType="VARCHAR" property="storeNum" />
        <result column="retail_price" jdbcType="VARCHAR" property="retailPrice" />
        <result column="revise_num_cum_90" jdbcType="VARCHAR" property="reviseNumCum90" />
        <result column="revise_amt_cum_90" jdbcType="VARCHAR" property="reviseAmtCum90" />
        <result column="revise_profit_amt_cum_90" jdbcType="VARCHAR" property="reviseProfitAmtCum90" />
        <result column="revise_profit_rate_90" jdbcType="VARCHAR" property="reviseProfitRate90" />
        <result column="revise_num_cum_180" jdbcType="VARCHAR" property="reviseNumCum180" />
        <result column="revise_amt_cum_180" jdbcType="VARCHAR" property="reviseAmtCum180" />
        <result column="revise_profit_amt_cum_180" jdbcType="VARCHAR" property="reviseProfitAmtCum180" />
        <result column="revise_profit_rate_180" jdbcType="VARCHAR" property="reviseProfitRate180" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, zone_new, data_from, data_from_v2, orgId, city, revise_store_group,
        goods_id, goodsname, `level`, bak, goodsunit, goodsspec, jx_cate1_name, manufacturer,
        classone_name, classtwo_name, classthree_name, classfour_name, component, is_otc,
        flag_disease, grossprofit, taotai_type, stjb, specialattributes_type, in_stock_rate,
        in_sales_rate, sku_int, ph_org_bz_flag, num_cum_90, amt_cum_90, profit_amt_cum_90,
        profit_rate_90, amt_rate_org_90, amt_rate_comp_90, num_cum_180, amt_cum_180, profit_amt_cum_180,
        profit_rate_180, amt_rate_org_180, amt_rate_comp_180, store_num, retail_price, revise_num_cum_90,
        revise_amt_cum_90, revise_profit_amt_cum_90, revise_profit_rate_90, revise_num_cum_180,
        revise_amt_cum_180, revise_profit_amt_cum_180, revise_profit_rate_180, gmt_create
    </sql>

    <select id="countTrackRetultTop4level" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT count(*)
        FROM track_retult_top4level_storegroup
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="orgId !=null">
            and orgId= #{orgId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectTrackRetultTop4levelByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_top4level_storegroup
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="orgId !=null">
            and orgId= #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="id != null">
            and id>#{id,jdbcType=BIGINT}
        </if>
        order by id
        <if test="perPage >= 0">
            limit ${perPage}
        </if>

    </select>


    <select id="selectTrackRetultCompidTop4level" resultType = "java.lang.String">
        SELECT orgId
        FROM track_retult_top4level_storegroup
        where task_id = #{taskId,jdbcType=BIGINT}
        group by orgId
    </select>

    <delete id="batchDel">
        delete from track_retult_top4level_storegroup where
        <if test="ids != null and ids.size > 0">
            id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>
