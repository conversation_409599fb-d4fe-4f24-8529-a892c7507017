<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackResultLevelNecessaryExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackResultLevelNecessary">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="level" jdbcType="INTEGER" property="level" />
        <result column="plat_orgid" jdbcType="VARCHAR" property="platOrgid" />
        <result column="compid" jdbcType="VARCHAR" property="compid" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="store_group" jdbcType="VARCHAR" property="storeGroup" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
        <result column="bak" jdbcType="VARCHAR" property="bak" />
        <result column="store_concentration" jdbcType="VARCHAR" property="storeConcentration" />
        <result column="store_sale_rate" jdbcType="VARCHAR" property="storeSaleRate" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    </resultMap>
    <sql id="Base_Column_List">
        id, task_id, `level`, plat_orgid, compid, city, store_group, store_code, goods_id,
        bak, store_concentration, store_sale_rate, gmt_create
    </sql>
    <select id="countTrackRetultLevelNecessary" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT count(*)
        FROM track_result_level_necessary
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectTrackRetultLevelNecessaryByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_result_level_necessary
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        order by id
        <if test="page >= 0">
            limit ${page} , ${perPage}
        </if>
    </select>

    <delete id="batchDel">
        delete from track_result_level_necessary where
        <if test="ids != null and ids.size > 0">
            id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

</mapper>