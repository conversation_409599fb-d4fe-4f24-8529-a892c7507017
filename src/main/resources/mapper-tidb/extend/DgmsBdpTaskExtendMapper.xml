<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.DgmsBdpTaskExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.DgmsBdpTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="busines_id" jdbcType="BIGINT" property="businesId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sub_category" jdbcType="BIGINT" property="subCategory" />
    <result column="necessary_tag" jdbcType="INTEGER" property="necessaryTag" />
    <result column="necessary_tag_name" jdbcType="VARCHAR" property="necessaryTagName" />
    <result column="manage_status" jdbcType="INTEGER" property="manageStatus" />
    <result column="suggest_manage_status" jdbcType="INTEGER" property="suggestManageStatus" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_distr" jdbcType="VARCHAR" property="forbidDistr" />
    <result column="forbid_sale" jdbcType="VARCHAR" property="forbidSale" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_return_allot" jdbcType="VARCHAR" property="forbidReturnAllot" />
    <result column="sensitive" jdbcType="VARCHAR" property="sensitive" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="apply_limit_upper" jdbcType="DECIMAL" property="applyLimitUpper" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_type, company_org_id, busines_id, company_name, store_org_id, store_id, store_code, 
    store_name, goods_no, sub_category, necessary_tag, necessary_tag_name, manage_status,
    suggest_manage_status, forbid_apply, forbid_distr, forbid_sale, forbid_return_warehouse,
    forbid_return_allot, `sensitive`, push_level, min_display_quantity, apply_limit_upper,
    `status`, gmt_create, gmt_update, create_by, update_by, extend, version
  </sql>
  <select id="selectStoreIdsByBizType" resultType="java.lang.Long" parameterType="java.lang.Integer">
    select store_id from dgms_bdp_task where biz_type = #{bizType}
    group by store_id
  </select>
  <select id="selectGoodsNosByStoreAndBizType" resultType="java.lang.String">
    select goods_no from dgms_bdp_task where biz_type = #{bizType} and store_id = #{storeId}
    group by goods_no
  </select>
  <select id="selectBizType" resultType="java.lang.Integer">
    select biz_type from dgms_bdp_task group by biz_type
  </select>
</mapper>
