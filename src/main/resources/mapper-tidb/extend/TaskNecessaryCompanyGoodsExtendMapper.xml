<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TaskNecessaryCompanyGoodsExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TaskNecessaryCompanyGoods">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
        <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
        <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
        <result column="businessId" jdbcType="BIGINT" property="businessid" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="category_id" jdbcType="BIGINT" property="categoryId" />
        <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
        <result column="middle_category_id" jdbcType="BIGINT" property="middleCategoryId" />
        <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
        <result column="small_category_id" jdbcType="BIGINT" property="smallCategoryId" />
        <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
        <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
        <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
        <result column="composition" jdbcType="VARCHAR" property="composition" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
        <result column="purchase_attr" jdbcType="VARCHAR" property="purchaseAttr" />
        <result column="choose_reason" jdbcType="VARCHAR" property="chooseReason" />
        <result column="store_focus_level" jdbcType="DECIMAL" property="storeFocusLevel" />
        <result column="store_sales_rate" jdbcType="DECIMAL" property="storeSalesRate" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, task_id, platform_org_id, platform_name, company_org_id, businessId, company_code,
        company_name, city, category_id, category_name, middle_category_id, middle_category_name,
        small_category_id, small_category_name, sub_category_id, sub_category_name, composition,
        goods_no, bar_code, goods_common_name, goods_name, goods_unit, description, specifications,
        dosage_form, manufacturer, approval_number, purchase_attr, choose_reason, store_focus_level,
        store_sales_rate, `status`, gmt_create, gmt_update, extend, version, created_by,
        created_name, updated_by, updated_name
    </sql>
    <select id="countNecessaryCompanyGoods" resultType="java.lang.Long">
        select
        count(*)
        from task_necessary_company_goods
        where status=0
        <if test="taskId != null">
            and task_id = #{taskId}
        </if>
    </select>

    <select id="queryNecessaryCompanyGoodsListByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task_necessary_company_goods
        where status=0
        <if test="taskId != null">
            and task_id = #{taskId}
        </if>
        order by id desc
        <if test="start != null and pageSize != null">
            limit #{start,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryNecessaryCompanyGoodsBusinessIdList" resultType="com.cowell.scib.service.dto.NecessaryBusinessIdDTO">
        SELECT task_id,businessId
        FROM task_necessary_company_goods
        where  status=0
        <if test="taskId != null">
            and task_id = #{taskId}
        </if>
        group by businessId
    </select>

    <delete id="batchDel">
        delete from task_necessary_company_goods where
        <if test="ids != null and ids.size > 0">
            id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>