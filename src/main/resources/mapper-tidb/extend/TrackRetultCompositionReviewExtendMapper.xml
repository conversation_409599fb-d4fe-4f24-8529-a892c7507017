<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackRetultCompositionReviewExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultCompositionReview">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
        <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="store_group" jdbcType="VARCHAR" property="storeGroup" />
        <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
        <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
        <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
        <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
        <result column="component" jdbcType="VARCHAR" property="component" />
        <result column="sku_cnt" jdbcType="VARCHAR" property="skuCnt" />
        <result column="sku_cnt_total_2" jdbcType="VARCHAR" property="skuCntTotal2" />
        <result column="sku_cnt_2" jdbcType="VARCHAR" property="skuCnt2" />
        <result column="sku_cnt_new_2" jdbcType="VARCHAR" property="skuCntNew2" />
        <result column="num_cum_30_2" jdbcType="VARCHAR" property="numCum302" />
        <result column="amt_cum_30_2" jdbcType="VARCHAR" property="amtCum302" />
        <result column="amt_rate_30_2" jdbcType="VARCHAR" property="amtRate302" />
        <result column="profit_amt_cum_30_2" jdbcType="VARCHAR" property="profitAmtCum302" />
        <result column="profit_rate_30_2" jdbcType="VARCHAR" property="profitRate302" />
        <result column="amt_inv_new_2" jdbcType="VARCHAR" property="amtInvNew2" />
        <result column="sku_cnt_total_3" jdbcType="VARCHAR" property="skuCntTotal3" />
        <result column="sku_cnt_3" jdbcType="VARCHAR" property="skuCnt3" />
        <result column="sku_cnt_new_3" jdbcType="VARCHAR" property="skuCntNew3" />
        <result column="num_cum_30_3" jdbcType="VARCHAR" property="numCum303" />
        <result column="amt_cum_30_3" jdbcType="VARCHAR" property="amtCum303" />
        <result column="amt_rate_30_3" jdbcType="VARCHAR" property="amtRate303" />
        <result column="profit_amt_cum_30_3" jdbcType="VARCHAR" property="profitAmtCum303" />
        <result column="profit_rate_30_3" jdbcType="VARCHAR" property="profitRate303" />
        <result column="amt_inv_new_3" jdbcType="VARCHAR" property="amtInvNew3" />
        <result column="sku_cnt_total_4" jdbcType="VARCHAR" property="skuCntTotal4" />
        <result column="sku_cnt_4" jdbcType="VARCHAR" property="skuCnt4" />
        <result column="sku_cnt_new_4" jdbcType="VARCHAR" property="skuCntNew4" />
        <result column="num_cum_30_4" jdbcType="VARCHAR" property="numCum304" />
        <result column="amt_cum_30_4" jdbcType="VARCHAR" property="amtCum304" />
        <result column="amt_rate_30_4" jdbcType="VARCHAR" property="amtRate304" />
        <result column="profit_amt_cum_30_4" jdbcType="VARCHAR" property="profitAmtCum304" />
        <result column="profit_rate_30_4" jdbcType="VARCHAR" property="profitRate304" />
        <result column="amt_inv_new_4" jdbcType="VARCHAR" property="amtInvNew4" />
        <result column="sku_cnt_total_5" jdbcType="VARCHAR" property="skuCntTotal5" />
        <result column="sku_cnt_5" jdbcType="VARCHAR" property="skuCnt5" />
        <result column="sku_cnt_new_5" jdbcType="VARCHAR" property="skuCntNew5" />
        <result column="num_cum_30_5" jdbcType="VARCHAR" property="numCum305" />
        <result column="amt_cum_30_5" jdbcType="VARCHAR" property="amtCum305" />
        <result column="amt_rate_30_5" jdbcType="VARCHAR" property="amtRate305" />
        <result column="profit_amt_cum_30_5" jdbcType="VARCHAR" property="profitAmtCum305" />
        <result column="profit_rate_30_5" jdbcType="VARCHAR" property="profitRate305" />
        <result column="amt_inv_new_5" jdbcType="VARCHAR" property="amtInvNew5" />
        <result column="sku_cnt_total_6" jdbcType="VARCHAR" property="skuCntTotal6" />
        <result column="sku_cnt_6" jdbcType="VARCHAR" property="skuCnt6" />
        <result column="sku_cnt_new_6" jdbcType="VARCHAR" property="skuCntNew6" />
        <result column="num_cum_30_6" jdbcType="VARCHAR" property="numCum306" />
        <result column="amt_cum_30_6" jdbcType="VARCHAR" property="amtCum306" />
        <result column="amt_rate_30_6" jdbcType="VARCHAR" property="amtRate306" />
        <result column="profit_amt_cum_30_6" jdbcType="VARCHAR" property="profitAmtCum306" />
        <result column="profit_rate_30_6" jdbcType="VARCHAR" property="profitRate306" />
        <result column="amt_inv_new_6" jdbcType="VARCHAR" property="amtInvNew6" />
        <result column="sku_cnt_total_7" jdbcType="VARCHAR" property="skuCntTotal7" />
        <result column="sku_cnt_7" jdbcType="VARCHAR" property="skuCnt7" />
        <result column="sku_cnt_new_7" jdbcType="VARCHAR" property="skuCntNew7" />
        <result column="num_cum_30_7" jdbcType="VARCHAR" property="numCum307" />
        <result column="amt_cum_30_7" jdbcType="VARCHAR" property="amtCum307" />
        <result column="amt_rate_30_7" jdbcType="VARCHAR" property="amtRate307" />
        <result column="profit_amt_cum_30_7" jdbcType="VARCHAR" property="profitAmtCum307" />
        <result column="profit_rate_30_7" jdbcType="VARCHAR" property="profitRate307" />
        <result column="amt_inv_new_7" jdbcType="VARCHAR" property="amtInvNew7" />
        <result column="sku_cnt_total_8" jdbcType="VARCHAR" property="skuCntTotal8" />
        <result column="sku_cnt_8" jdbcType="VARCHAR" property="skuCnt8" />
        <result column="sku_cnt_new_8" jdbcType="VARCHAR" property="skuCntNew8" />
        <result column="num_cum_30_8" jdbcType="VARCHAR" property="numCum308" />
        <result column="amt_cum_30_8" jdbcType="VARCHAR" property="amtCum308" />
        <result column="amt_rate_30_8" jdbcType="VARCHAR" property="amtRate308" />
        <result column="profit_amt_cum_30_8" jdbcType="VARCHAR" property="profitAmtCum308" />
        <result column="profit_rate_30_8" jdbcType="VARCHAR" property="profitRate308" />
        <result column="amt_inv_new_8" jdbcType="VARCHAR" property="amtInvNew8" />
        <result column="sku_cnt_total_9" jdbcType="VARCHAR" property="skuCntTotal9" />
        <result column="sku_cnt_9" jdbcType="VARCHAR" property="skuCnt9" />
        <result column="sku_cnt_new_9" jdbcType="VARCHAR" property="skuCntNew9" />
        <result column="num_cum_30_9" jdbcType="VARCHAR" property="numCum309" />
        <result column="amt_cum_30_9" jdbcType="VARCHAR" property="amtCum309" />
        <result column="amt_rate_30_9" jdbcType="VARCHAR" property="amtRate309" />
        <result column="profit_amt_cum_30_9" jdbcType="VARCHAR" property="profitAmtCum309" />
        <result column="profit_rate_30_9" jdbcType="VARCHAR" property="profitRate309" />
        <result column="amt_inv_new_9" jdbcType="VARCHAR" property="amtInvNew9" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    </resultMap>
    <sql id="Base_Column_List">
        id, task_id, zone_new, chain_name, city, store_group, classone_name, classtwo_name,
        classthree_name, classfour_name, component, sku_cnt, sku_cnt_total_2, sku_cnt_2,
        sku_cnt_new_2, num_cum_30_2, amt_cum_30_2, amt_rate_30_2, profit_amt_cum_30_2, profit_rate_30_2,
        amt_inv_new_2, sku_cnt_total_3, sku_cnt_3, sku_cnt_new_3, num_cum_30_3, amt_cum_30_3,
        amt_rate_30_3, profit_amt_cum_30_3, profit_rate_30_3, amt_inv_new_3, sku_cnt_total_4,
        sku_cnt_4, sku_cnt_new_4, num_cum_30_4, amt_cum_30_4, amt_rate_30_4, profit_amt_cum_30_4,
        profit_rate_30_4, amt_inv_new_4, sku_cnt_total_5, sku_cnt_5, sku_cnt_new_5, num_cum_30_5,
        amt_cum_30_5, amt_rate_30_5, profit_amt_cum_30_5, profit_rate_30_5, amt_inv_new_5,
        sku_cnt_total_6, sku_cnt_6, sku_cnt_new_6, num_cum_30_6, amt_cum_30_6, amt_rate_30_6,
        profit_amt_cum_30_6, profit_rate_30_6, amt_inv_new_6, sku_cnt_total_7, sku_cnt_7,
        sku_cnt_new_7, num_cum_30_7, amt_cum_30_7, amt_rate_30_7, profit_amt_cum_30_7, profit_rate_30_7,
        amt_inv_new_7, sku_cnt_total_8, sku_cnt_8, sku_cnt_new_8, num_cum_30_8, amt_cum_30_8,
        amt_rate_30_8, profit_amt_cum_30_8, profit_rate_30_8, amt_inv_new_8, sku_cnt_total_9,
        sku_cnt_9, sku_cnt_new_9, num_cum_30_9, amt_cum_30_9, amt_rate_30_9, profit_amt_cum_30_9,
        profit_rate_30_9, amt_inv_new_9, gmt_create
    </sql>
    <select id="countTrackRetultCompositionReview" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT count(*)
        FROM track_retult_composition_review
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectTrackRetultCompositionReviewByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_composition_review
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="page >= 0">
            limit ${page} , ${perPage}
        </if>
    </select>

    <delete id="batchDel">
        delete from track_retult_composition_review where
        <if test="ids != null and ids.size > 0">
            id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>