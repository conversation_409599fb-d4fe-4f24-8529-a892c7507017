<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackRetultNewStoreAllDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="org_no" jdbcType="VARCHAR" property="orgNo" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goodsname" jdbcType="VARCHAR" property="goodsname" />
    <result column="goodsspec" jdbcType="VARCHAR" property="goodsspec" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="jx_cate1_name" jdbcType="VARCHAR" property="jxCate1Name" />
    <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="suggest_ph_qty" jdbcType="DECIMAL" property="suggestPhQty" />
    <result column="ph_cost" jdbcType="DECIMAL" property="phCost" />
    <result column="taotai_type" jdbcType="VARCHAR" property="taotaiType" />
    <result column="stjb" jdbcType="VARCHAR" property="stjb" />
    <result column="grossprofit" jdbcType="VARCHAR" property="grossprofit" />
    <result column="sub_category_id" jdbcType="VARCHAR" property="subCategoryId" />
    <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
    <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
    <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
    <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="dtpgood" jdbcType="VARCHAR" property="dtpgood" />
    <result column="flag_disease" jdbcType="VARCHAR" property="flagDisease" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="distribind" jdbcType="VARCHAR" property="distribind" />
    <result column="precious" jdbcType="VARCHAR" property="precious" />
    <result column="refretailprice" jdbcType="VARCHAR" property="refretailprice" />
    <result column="in_stock_rate_dx" jdbcType="VARCHAR" property="inStockRateDx" />
    <result column="in_sales_rate_dx" jdbcType="VARCHAR" property="inSalesRateDx" />
    <result column="num_cum_90_dx" jdbcType="VARCHAR" property="numCum90Dx" />
    <result column="bill_cnts_cum_90_dx" jdbcType="VARCHAR" property="billCntsCum90Dx" />
    <result column="amt_cum_90_dx" jdbcType="VARCHAR" property="amtCum90Dx" />
    <result column="profit_rate_90_dx" jdbcType="VARCHAR" property="profitRate90Dx" />
    <result column="in_stock_rate_city" jdbcType="VARCHAR" property="inStockRateCity" />
    <result column="in_sales_rate_city" jdbcType="VARCHAR" property="inSalesRateCity" />
    <result column="num_cum_90_city" jdbcType="VARCHAR" property="numCum90City" />
    <result column="bill_cnts_cum_90_city" jdbcType="VARCHAR" property="billCntsCum90City" />
    <result column="amt_cum_90_city" jdbcType="VARCHAR" property="amtCum90City" />
    <result column="profit_rate_90_city" jdbcType="VARCHAR" property="profitRate90City" />
    <result column="in_stock_rate_qy" jdbcType="VARCHAR" property="inStockRateQy" />
    <result column="in_sales_rate_qy" jdbcType="VARCHAR" property="inSalesRateQy" />
    <result column="num_cum_90_qy" jdbcType="VARCHAR" property="numCum90Qy" />
    <result column="bill_cnts_cum_90_qy" jdbcType="VARCHAR" property="billCntsCum90Qy" />
    <result column="amt_cum_90_qy" jdbcType="VARCHAR" property="amtCum90Qy" />
    <result column="profit_rate_90_qy" jdbcType="VARCHAR" property="profitRate90Qy" />
    <result column="in_stock_rate_md" jdbcType="VARCHAR" property="inStockRateMd" />
    <result column="in_sales_rate_md" jdbcType="VARCHAR" property="inSalesRateMd" />
    <result column="num_cum_90_md" jdbcType="VARCHAR" property="numCum90Md" />
    <result column="bill_cnts_cum_90_md" jdbcType="VARCHAR" property="billCntsCum90Md" />
    <result column="amt_cum_90_md" jdbcType="VARCHAR" property="amtCum90Md" />
    <result column="profit_rate_90_md" jdbcType="VARCHAR" property="profitRate90Md" />
    <result column="jy_able" jdbcType="TINYINT" property="jyAble" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
    <result column="bak1" jdbcType="VARCHAR" property="bak1" />
    <result column="bak2" jdbcType="VARCHAR" property="bak2" />
    <result column="bak3" jdbcType="VARCHAR" property="bak3" />
    <result column="bak4" jdbcType="VARCHAR" property="bak4" />
    <result column="bak5" jdbcType="VARCHAR" property="bak5" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, org_no, store_name, goods_id, goodsname, goodsspec, manufacturer, jx_cate1_name, 
    goodsunit, `level`, suggest_ph_qty, ph_cost, taotai_type, stjb, grossprofit, sub_category_id, 
    classone_name, classtwo_name, classthree_name, classfour_name, component, dtpgood, 
    flag_disease, department, distribind, precious, refretailprice, in_stock_rate_dx, 
    in_sales_rate_dx, num_cum_90_dx, bill_cnts_cum_90_dx, amt_cum_90_dx, profit_rate_90_dx, 
    in_stock_rate_city, in_sales_rate_city, num_cum_90_city, bill_cnts_cum_90_city, amt_cum_90_city, 
    profit_rate_90_city, in_stock_rate_qy, in_sales_rate_qy, num_cum_90_qy, bill_cnts_cum_90_qy, 
    amt_cum_90_qy, profit_rate_90_qy, in_stock_rate_md, in_sales_rate_md, num_cum_90_md, 
    bill_cnts_cum_90_md, amt_cum_90_md, profit_rate_90_md, jy_able, rx_otc, bak1, bak2,
    bak3, bak4, bak5, extend, gmt_create, `status`
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail" useGeneratedKeys="true">
    insert into track_retult_new_store_all_detail_${taskId} (task_id, org_no, store_name,
      goods_id, goodsname, goodsspec, 
      manufacturer, jx_cate1_name, goodsunit, 
      `level`, suggest_ph_qty, ph_cost, 
      taotai_type, stjb, grossprofit, 
      sub_category_id, classone_name, classtwo_name, 
      classthree_name, classfour_name, component, 
      dtpgood,
      distribind, refretailprice
      )
    values
    <if test="list != null and list.size() != 0">
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.taskId,jdbcType=BIGINT}, #{item.orgNo,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR},
      #{item.goodsId,jdbcType=VARCHAR}, #{item.goodsname,jdbcType=VARCHAR}, #{item.goodsspec,jdbcType=VARCHAR},
      #{item.manufacturer,jdbcType=VARCHAR}, #{item.jxCate1Name,jdbcType=VARCHAR}, #{item.goodsunit,jdbcType=VARCHAR},
      #{item.level,jdbcType=VARCHAR}, #{item.suggestPhQty,jdbcType=DECIMAL}, #{item.phCost,jdbcType=DECIMAL},
      #{item.taotaiType,jdbcType=VARCHAR}, #{item.stjb,jdbcType=VARCHAR}, #{item.grossprofit,jdbcType=VARCHAR},
      #{item.subCategoryId,jdbcType=VARCHAR}, #{item.classoneName,jdbcType=VARCHAR}, #{item.classtwoName,jdbcType=VARCHAR},
      #{item.classthreeName,jdbcType=VARCHAR}, #{item.classfourName,jdbcType=VARCHAR}, #{item.component,jdbcType=VARCHAR},
      #{item.dtpgood,jdbcType=VARCHAR},
      #{item.distribind,jdbcType=VARCHAR}, #{item.refretailprice,jdbcType=VARCHAR}
      )
    </foreach>
    </if>
  </insert>
  <update id="batchUpdate">
    update track_retult_new_store_all_detail_${taskId}
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="jy_able=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.jyAble,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="suggest_ph_qty=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.suggestPhQty,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="ph_cost=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.phCost,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="count" resultType="java.lang.Long">
    select count(*) from track_retult_new_store_all_detail_${taskId}
  </select>
  <select id="selectByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from track_retult_new_store_all_detail_${taskId}
    where task_id = #{taskId,jdbcType=BIGINT}
    order by id asc
    limit ${start},${pageSize}
  </select>
  <select id="selectByTaskIdAndGoodsIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from track_retult_new_store_all_detail_${taskId}
    where task_id = #{taskId,jdbcType=BIGINT}
    and goods_id in
    <foreach collection="goodsNoList" index="index" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

</mapper>
