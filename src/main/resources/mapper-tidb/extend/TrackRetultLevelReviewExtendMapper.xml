<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackRetultLevelReviewExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultLevelReview">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
        <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="revise_store_group" jdbcType="VARCHAR" property="reviseStoreGroup" />
        <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
        <result column="org_cnt_v3" jdbcType="VARCHAR" property="orgCntV3" />
        <result column="sku_cnt" jdbcType="VARCHAR" property="skuCnt" />
        <result column="sku_cnt_jt" jdbcType="VARCHAR" property="skuCntJt" />
        <result column="sku_cnt_pt" jdbcType="VARCHAR" property="skuCntPt" />
        <result column="sku_cnt_qy" jdbcType="VARCHAR" property="skuCntQy" />
        <result column="sku_cnt_dxbb" jdbcType="VARCHAR" property="skuCntDxbb" />
        <result column="sku_cnt_dxxp" jdbcType="VARCHAR" property="skuCntDxxp" />
        <result column="sku_cnt_dd" jdbcType="VARCHAR" property="skuCntDd" />
        <result column="sku_cnt_jt_qy_dx" jdbcType="VARCHAR" property="skuCntJtQyDx" />
        <result column="sku_cnt_no_dd" jdbcType="VARCHAR" property="skuCntNoDd" />
        <result column="sku_cnt_xz" jdbcType="VARCHAR" property="skuCntXz" />
        <result column="sku_cnt_js" jdbcType="VARCHAR" property="skuCntJs" />
        <result column="sku_rate_top4" jdbcType="VARCHAR" property="skuRateTop4" />
        <result column="sku_rate_no_dd" jdbcType="VARCHAR" property="skuRateNoDd" />
        <result column="comp_cnt" jdbcType="VARCHAR" property="compCnt" />
        <result column="comp_cnt_jt" jdbcType="VARCHAR" property="compCntJt" />
        <result column="comp_cnt_pt" jdbcType="VARCHAR" property="compCntPt" />
        <result column="comp_cnt_qy" jdbcType="VARCHAR" property="compCntQy" />
        <result column="comp_cnt_dxbb" jdbcType="VARCHAR" property="compCntDxbb" />
        <result column="comp_cnt_dxxp" jdbcType="VARCHAR" property="compCntDxxp" />
        <result column="comp_cnt_dd" jdbcType="VARCHAR" property="compCntDd" />
        <result column="comp_cnt_jt_qy_dx" jdbcType="VARCHAR" property="compCntJtQyDx" />
        <result column="comp_cnt_no_dd" jdbcType="VARCHAR" property="compCntNoDd" />
        <result column="comp_cnt_xz" jdbcType="VARCHAR" property="compCntXz" />
        <result column="comp_cnt_js" jdbcType="VARCHAR" property="compCntJs" />
        <result column="comp_rate_top4" jdbcType="VARCHAR" property="compRateTop4" />
        <result column="comp_rate_no_dd" jdbcType="VARCHAR" property="compRateNoDd" />
        <result column="class_cnt" jdbcType="VARCHAR" property="classCnt" />
        <result column="class_cnt_jt" jdbcType="VARCHAR" property="classCntJt" />
        <result column="class_cnt_pt" jdbcType="VARCHAR" property="classCntPt" />
        <result column="class_cnt_qy" jdbcType="VARCHAR" property="classCntQy" />
        <result column="class_cnt_dxbb" jdbcType="VARCHAR" property="classCntDxbb" />
        <result column="class_cnt_dxxp" jdbcType="VARCHAR" property="classCntDxxp" />
        <result column="class_cnt_dd" jdbcType="VARCHAR" property="classCntDd" />
        <result column="class_cnt_jt_qy_dx" jdbcType="VARCHAR" property="classCntJtQyDx" />
        <result column="class_cnt_no_dd" jdbcType="VARCHAR" property="classCntNoDd" />
        <result column="class_cnt_xz" jdbcType="VARCHAR" property="classCntXz" />
        <result column="class_cnt_js" jdbcType="VARCHAR" property="classCntJs" />
        <result column="amt_cum_30" jdbcType="VARCHAR" property="amtCum30" />
        <result column="amt_cum_30_jt" jdbcType="VARCHAR" property="amtCum30Jt" />
        <result column="amt_cum_30_pt" jdbcType="VARCHAR" property="amtCum30Pt" />
        <result column="amt_cum_30_qy" jdbcType="VARCHAR" property="amtCum30Qy" />
        <result column="amt_cum_30_dxbb" jdbcType="VARCHAR" property="amtCum30Dxbb" />
        <result column="amt_cum_30_dxxp" jdbcType="VARCHAR" property="amtCum30Dxxp" />
        <result column="amt_cum_30_dd" jdbcType="VARCHAR" property="amtCum30Dd" />
        <result column="amt_cum_30_jt_qy_dx" jdbcType="VARCHAR" property="amtCum30JtQyDx" />
        <result column="amt_cum_30_no_dd" jdbcType="VARCHAR" property="amtCum30NoDd" />
        <result column="amt_cum_30_xz" jdbcType="VARCHAR" property="amtCum30Xz" />
        <result column="amt_cum_30_js" jdbcType="VARCHAR" property="amtCum30Js" />
        <result column="profit_cum_30" jdbcType="VARCHAR" property="profitCum30" />
        <result column="profit_cum_30_jt" jdbcType="VARCHAR" property="profitCum30Jt" />
        <result column="profit_cum_30_pt" jdbcType="VARCHAR" property="profitCum30Pt" />
        <result column="profit_cum_30_qy" jdbcType="VARCHAR" property="profitCum30Qy" />
        <result column="profit_cum_30_dxbb" jdbcType="VARCHAR" property="profitCum30Dxbb" />
        <result column="profit_cum_30_dxxp" jdbcType="VARCHAR" property="profitCum30Dxxp" />
        <result column="profit_cum_30_dd" jdbcType="VARCHAR" property="profitCum30Dd" />
        <result column="profit_cum_30_jt_qy_dx" jdbcType="VARCHAR" property="profitCum30JtQyDx" />
        <result column="profit_cum_30_no_dd" jdbcType="VARCHAR" property="profitCum30NoDd" />
        <result column="profit_cum_30_xz" jdbcType="VARCHAR" property="profitCum30Xz" />
        <result column="profit_cum_30_js" jdbcType="VARCHAR" property="profitCum30Js" />
        <result column="profit_cum_30_rate" jdbcType="VARCHAR" property="profitCum30Rate" />
        <result column="profit_cum_30_rate_jt" jdbcType="VARCHAR" property="profitCum30RateJt" />
        <result column="profit_cum_30_rate_pt" jdbcType="VARCHAR" property="profitCum30RatePt" />
        <result column="profit_cum_30_rate_qy" jdbcType="VARCHAR" property="profitCum30RateQy" />
        <result column="profit_cum_30_rate_dxbb" jdbcType="VARCHAR" property="profitCum30RateDxbb" />
        <result column="profit_cum_30_rate_dxxp" jdbcType="VARCHAR" property="profitCum30RateDxxp" />
        <result column="profit_cum_30_rate_dd" jdbcType="VARCHAR" property="profitCum30RateDd" />
        <result column="profit_cum_30_rate_jt_qy_dx" jdbcType="VARCHAR" property="profitCum30RateJtQyDx" />
        <result column="profit_cum_30_rate_no_dd" jdbcType="VARCHAR" property="profitCum30RateNoDd" />
        <result column="profit_cum_30_rate_xz" jdbcType="VARCHAR" property="profitCum30RateXz" />
        <result column="profit_cum_30_rate_js" jdbcType="VARCHAR" property="profitCum30RateJs" />
        <result column="amt_cum_30_month" jdbcType="VARCHAR" property="amtCum30Month" />
        <result column="amt_cum_30_month_nodtp" jdbcType="VARCHAR" property="amtCum30MonthNodtp" />
        <result column="amt_cum_30_month_nozy" jdbcType="VARCHAR" property="amtCum30MonthNozy" />
        <result column="amt_cum_30_rate" jdbcType="VARCHAR" property="amtCum30Rate" />
        <result column="amt_cum_30_rate_jt" jdbcType="VARCHAR" property="amtCum30RateJt" />
        <result column="amt_cum_30_rate_pt" jdbcType="VARCHAR" property="amtCum30RatePt" />
        <result column="amt_cum_30_rate_qt" jdbcType="VARCHAR" property="amtCum30RateQt" />
        <result column="amt_cum_30_rate_dxbb" jdbcType="VARCHAR" property="amtCum30RateDxbb" />
        <result column="amt_cum_30_rate_dxxp" jdbcType="VARCHAR" property="amtCum30RateDxxp" />
        <result column="amt_cum_30_rate_dd" jdbcType="VARCHAR" property="amtCum30RateDd" />
        <result column="amt_cum_30_rate_jt_qy_dx" jdbcType="VARCHAR" property="amtCum30RateJtQyDx" />
        <result column="amt_cum_30_rate_no_dd" jdbcType="VARCHAR" property="amtCum30RateNoDd" />
        <result column="amt_cum_30_rate_xz" jdbcType="VARCHAR" property="amtCum30RateXz" />
        <result column="amt_cum_30_rate_js" jdbcType="VARCHAR" property="amtCum30RateJs" />
        <result column="amt_cum_30_rate_nodtp" jdbcType="VARCHAR" property="amtCum30RateNodtp" />
        <result column="amt_cum_30_rate_jt_nodtp" jdbcType="VARCHAR" property="amtCum30RateJtNodtp" />
        <result column="amt_cum_30_rate_pt_nodtp" jdbcType="VARCHAR" property="amtCum30RatePtNodtp" />
        <result column="amt_cum_30_rate_qt_nodtp" jdbcType="VARCHAR" property="amtCum30RateQtNodtp" />
        <result column="amt_cum_30_rate_dxbb_nodtp" jdbcType="VARCHAR" property="amtCum30RateDxbbNodtp" />
        <result column="amt_cum_30_rate_dxxp_nodtp" jdbcType="VARCHAR" property="amtCum30RateDxxpNodtp" />
        <result column="amt_cum_30_rate_dd_nodtp" jdbcType="VARCHAR" property="amtCum30RateDdNodtp" />
        <result column="amt_cum_30_rate_jt_qy_dx_nodtp" jdbcType="VARCHAR" property="amtCum30RateJtQyDxNodtp" />
        <result column="amt_cum_30_rate_no_dd_nodtp" jdbcType="VARCHAR" property="amtCum30RateNoDdNodtp" />
        <result column="amt_cum_30_rate_xz_nodtp" jdbcType="VARCHAR" property="amtCum30RateXzNodtp" />
        <result column="amt_cum_30_rate_js_nodtp" jdbcType="VARCHAR" property="amtCum30RateJsNodtp" />
        <result column="amt_cum_30_rate_nozy" jdbcType="VARCHAR" property="amtCum30RateNozy" />
        <result column="amt_cum_30_rate_jt_nozy" jdbcType="VARCHAR" property="amtCum30RateJtNozy" />
        <result column="amt_cum_30_rate_pt_nozy" jdbcType="VARCHAR" property="amtCum30RatePtNozy" />
        <result column="amt_cum_30_rate_qt_nozy" jdbcType="VARCHAR" property="amtCum30RateQtNozy" />
        <result column="amt_cum_30_rate_dxbb_nozy" jdbcType="VARCHAR" property="amtCum30RateDxbbNozy" />
        <result column="amt_cum_30_rate_dxxp_nozy" jdbcType="VARCHAR" property="amtCum30RateDxxpNozy" />
        <result column="amt_cum_30_rate_dd_nozy" jdbcType="VARCHAR" property="amtCum30RateDdNozy" />
        <result column="amt_cum_30_rate_jt_qy_dx_nozy" jdbcType="VARCHAR" property="amtCum30RateJtQyDxNozy" />
        <result column="amt_cum_30_rate_no_dd_nozy" jdbcType="VARCHAR" property="amtCum30RateNoDdNozy" />
        <result column="amt_cum_30_rate_xz_nozy" jdbcType="VARCHAR" property="amtCum30RateXzNozy" />
        <result column="amt_cum_30_rate_js_nozy" jdbcType="VARCHAR" property="amtCum30RateJsNozy" />
        <result column="stock_cum_30" jdbcType="VARCHAR" property="stockCum30" />
        <result column="stock_cum_30_jt" jdbcType="VARCHAR" property="stockCum30Jt" />
        <result column="stock_cum_30_pt" jdbcType="VARCHAR" property="stockCum30Pt" />
        <result column="stock_cum_30_qy" jdbcType="VARCHAR" property="stockCum30Qy" />
        <result column="stock_cum_30_dxbb" jdbcType="VARCHAR" property="stockCum30Dxbb" />
        <result column="stock_cum_30_dxxp" jdbcType="VARCHAR" property="stockCum30Dxxp" />
        <result column="stock_cum_30_dd" jdbcType="VARCHAR" property="stockCum30Dd" />
        <result column="stock_cum_30_jt_qy_dx" jdbcType="VARCHAR" property="stockCum30JtQyDx" />
        <result column="stock_cum_30_no_dd" jdbcType="VARCHAR" property="stockCum30NoDd" />
        <result column="stock_cum_30_new" jdbcType="VARCHAR" property="stockCum30New" />
        <result column="stock_cum_30_js" jdbcType="VARCHAR" property="stockCum30Js" />
        <result column="avg_stock_cum_30_new" jdbcType="VARCHAR" property="avgStockCum30New" />
        <result column="avg_stock_cum_30_js" jdbcType="VARCHAR" property="avgStockCum30Js" />
        <result column="avg_sku_cnt_xz" jdbcType="VARCHAR" property="avgSkuCntXz" />
        <result column="avg_sku_cnt_js" jdbcType="VARCHAR" property="avgSkuCntJs" />
        <result column="avg_cnt" jdbcType="VARCHAR" property="avgCnt" />
        <result column="amt_cum_30_null" jdbcType="VARCHAR" property="amtCum30Null" />
        <result column="avg_amt_cum_30_null" jdbcType="VARCHAR" property="avgAmtCum30Null" />
        <result column="amt_cum_30_new" jdbcType="VARCHAR" property="amtCum30New" />
        <result column="avg_amt_cum_30_new" jdbcType="VARCHAR" property="avgAmtCum30New" />
        <result column="profit_cum_30_new" jdbcType="VARCHAR" property="profitCum30New" />
        <result column="profit_cum_30_rate_new" jdbcType="VARCHAR" property="profitCum30RateNew" />
        <result column="amt_cum_30_rate_new" jdbcType="VARCHAR" property="amtCum30RateNew" />
        <result column="amt_cum_30_rate_new_nodtp" jdbcType="VARCHAR" property="amtCum30RateNewNodtp" />
        <result column="amt_cum_30_rate_new_nozy" jdbcType="VARCHAR" property="amtCum30RateNewNozy" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    </resultMap>
    <sql id="Base_Column_List">
        id, task_id, zone_new, chain_name, city, revise_store_group, classone_name, org_cnt_v3,
        sku_cnt, sku_cnt_jt, sku_cnt_pt, sku_cnt_qy, sku_cnt_dxbb, sku_cnt_dxxp, sku_cnt_dd,
        sku_cnt_jt_qy_dx, sku_cnt_no_dd, sku_cnt_xz, sku_cnt_js, sku_rate_top4, sku_rate_no_dd,
        comp_cnt, comp_cnt_jt, comp_cnt_pt, comp_cnt_qy, comp_cnt_dxbb, comp_cnt_dxxp, comp_cnt_dd,
        comp_cnt_jt_qy_dx, comp_cnt_no_dd, comp_cnt_xz, comp_cnt_js, comp_rate_top4, comp_rate_no_dd,
        class_cnt, class_cnt_jt, class_cnt_pt, class_cnt_qy, class_cnt_dxbb, class_cnt_dxxp,
        class_cnt_dd, class_cnt_jt_qy_dx, class_cnt_no_dd, class_cnt_xz, class_cnt_js, amt_cum_30,
        amt_cum_30_jt, amt_cum_30_pt, amt_cum_30_qy, amt_cum_30_dxbb, amt_cum_30_dxxp, amt_cum_30_dd,
        amt_cum_30_jt_qy_dx, amt_cum_30_no_dd, amt_cum_30_xz, amt_cum_30_js, profit_cum_30,
        profit_cum_30_jt, profit_cum_30_pt, profit_cum_30_qy, profit_cum_30_dxbb, profit_cum_30_dxxp,
        profit_cum_30_dd, profit_cum_30_jt_qy_dx, profit_cum_30_no_dd, profit_cum_30_xz,
        profit_cum_30_js, profit_cum_30_rate, profit_cum_30_rate_jt, profit_cum_30_rate_pt,
        profit_cum_30_rate_qy, profit_cum_30_rate_dxbb, profit_cum_30_rate_dxxp, profit_cum_30_rate_dd,
        profit_cum_30_rate_jt_qy_dx, profit_cum_30_rate_no_dd, profit_cum_30_rate_xz, profit_cum_30_rate_js,
        amt_cum_30_month, amt_cum_30_month_nodtp, amt_cum_30_month_nozy, amt_cum_30_rate,
        amt_cum_30_rate_jt, amt_cum_30_rate_pt, amt_cum_30_rate_qt, amt_cum_30_rate_dxbb,
        amt_cum_30_rate_dxxp, amt_cum_30_rate_dd, amt_cum_30_rate_jt_qy_dx, amt_cum_30_rate_no_dd,
        amt_cum_30_rate_xz, amt_cum_30_rate_js, amt_cum_30_rate_nodtp, amt_cum_30_rate_jt_nodtp,
        amt_cum_30_rate_pt_nodtp, amt_cum_30_rate_qt_nodtp, amt_cum_30_rate_dxbb_nodtp, amt_cum_30_rate_dxxp_nodtp,
        amt_cum_30_rate_dd_nodtp, amt_cum_30_rate_jt_qy_dx_nodtp, amt_cum_30_rate_no_dd_nodtp,
        amt_cum_30_rate_xz_nodtp, amt_cum_30_rate_js_nodtp, amt_cum_30_rate_nozy, amt_cum_30_rate_jt_nozy,
        amt_cum_30_rate_pt_nozy, amt_cum_30_rate_qt_nozy, amt_cum_30_rate_dxbb_nozy, amt_cum_30_rate_dxxp_nozy,
        amt_cum_30_rate_dd_nozy, amt_cum_30_rate_jt_qy_dx_nozy, amt_cum_30_rate_no_dd_nozy,
        amt_cum_30_rate_xz_nozy, amt_cum_30_rate_js_nozy, stock_cum_30, stock_cum_30_jt,
        stock_cum_30_pt, stock_cum_30_qy, stock_cum_30_dxbb, stock_cum_30_dxxp, stock_cum_30_dd,
        stock_cum_30_jt_qy_dx, stock_cum_30_no_dd, stock_cum_30_new, stock_cum_30_js, avg_stock_cum_30_new,
        avg_stock_cum_30_js, avg_sku_cnt_xz, avg_sku_cnt_js, avg_cnt, amt_cum_30_null, avg_amt_cum_30_null,
        amt_cum_30_new, avg_amt_cum_30_new, profit_cum_30_new, profit_cum_30_rate_new, amt_cum_30_rate_new,
        amt_cum_30_rate_new_nodtp, amt_cum_30_rate_new_nozy, gmt_create
    </sql>

    <select id="countTrackRetultLevelReview" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT count(*)
        FROM track_retult_level_review
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectTrackRetultLevelReviewByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_level_review
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="page >= 0">
            limit ${page} , ${perPage}
        </if>
    </select>
    <select id="selectTrackRetultLevelReviewByChainName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_level_review
        where task_id = #{taskId,jdbcType=BIGINT}
        <if test="chainName !=null">
            and chain_name= #{chainName,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>