<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.JymlStoreSkuConfirmDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="process_detail_id" jdbcType="BIGINT" property="processDetailId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="category_path" jdbcType="VARCHAR" property="categoryPath" />
    <result column="system_suggest" jdbcType="INTEGER" property="systemSuggest" />
    <result column="previous_review_result" jdbcType="INTEGER" property="previousReviewResult" />
    <result column="my_confirm" jdbcType="INTEGER" property="myConfirm" />
    <result column="review_result" jdbcType="INTEGER" property="reviewResult" />
    <result column="submit_by" jdbcType="BIGINT" property="submitBy" />
    <result column="submit_name" jdbcType="VARCHAR" property="submitName" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="review_by" jdbcType="BIGINT" property="reviewBy" />
    <result column="review_name" jdbcType="VARCHAR" property="reviewName" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_org_id, store_id, store_code, process_detail_id, goods_no, category, 
    middle_category, small_category, sub_category, category_path, system_suggest, previous_review_result, 
    my_confirm, review_result, submit_by, submit_name, submit_time, review_by, review_name, 
    review_time, version, `status`, gmt_create, gmt_update, extend, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <update id="batchUpdateByPrimaryKeySelective" parameterType="java.util.List">
      update jyml_store_sku_confirm_detail
      <trim prefix="set" suffixOverrides=",">
        <trim prefix="business_org_id = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.businessOrgId != null">
              when id = #{item.id} then #{item.businessOrgId,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="store_id = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.storeId != null">
              when id = #{item.id} then #{item.storeId,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="store_code = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.storeCode != null">
              when id = #{item.id} then #{item.storeCode,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="process_detail_id = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.processDetailId != null">
              when id = #{item.id} then #{item.processDetailId,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="goods_no = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.goodsNo != null">
              when id = #{item.id} then #{item.goodsNo,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="category = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.category != null">
              when id = #{item.id} then #{item.category,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="middle_category = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.middleCategory != null">
              when id = #{item.id} then #{item.middleCategory,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="small_category = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.smallCategory != null">
              when id = #{item.id} then #{item.smallCategory,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="sub_category = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.subCategory != null">
              when id = #{item.id} then #{item.subCategory,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="category_path = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.categoryPath != null">
              when id = #{item.id} then #{item.categoryPath,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="system_suggest = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.systemSuggest != null">
              when id = #{item.id} then #{item.systemSuggest,jdbcType=INTEGER}
            </if>
          </foreach>
        </trim>
        <trim prefix="previous_review_result = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.previousReviewResult != null">
              when id = #{item.id} then #{item.previousReviewResult,jdbcType=INTEGER}
            </if>
          </foreach>
        </trim>
        <trim prefix="my_confirm = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.myConfirm != null">
              when id = #{item.id} then #{item.myConfirm,jdbcType=INTEGER}
            </if>
          </foreach>
        </trim>
        <trim prefix="review_result = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.reviewResult != null">
              when id = #{item.id} then #{item.reviewResult,jdbcType=INTEGER}
            </if>
          </foreach>
        </trim>
        <trim prefix="submit_by = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.submitBy != null">
              when id = #{item.id} then #{item.submitBy,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="submit_name = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.submitName != null">
              when id = #{item.id} then #{item.submitName,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="submit_time = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.submitTime != null">
              when id = #{item.id} then #{item.submitTime,jdbcType=TIMESTAMP}
            </if>
          </foreach>
        </trim>
        <trim prefix="review_by = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.reviewBy != null">
              when id = #{item.id} then #{item.reviewBy,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="review_name = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.reviewName != null">
              when id = #{item.id} then #{item.reviewName,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="review_time = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.reviewTime != null">
              when id = #{item.id} then #{item.reviewTime,jdbcType=TIMESTAMP}
            </if>
          </foreach>
        </trim>
        <trim prefix="version = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.version != null">
              when id = #{item.id} then #{item.version,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="status = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.status != null">
              when id = #{item.id} then #{item.status,jdbcType=TINYINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="gmt_create = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.gmtCreate != null">
              when id = #{item.id} then #{item.gmtCreate,jdbcType=TIMESTAMP}
            </if>
          </foreach>
        </trim>
        <trim prefix="gmt_update = case" suffix="end,">
          <foreach collection="list" item="item">
            when id = #{item.id} then now()
          </foreach>
        </trim>
        <trim prefix="extend = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.extend != null">
              when id = #{item.id} then #{item.extend,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="created_by = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.createdBy != null">
              when id = #{item.id} then #{item.createdBy,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="created_name = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.createdName != null">
              when id = #{item.id} then #{item.createdName,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
        <trim prefix="updated_by = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.updatedBy != null">
              when id = #{item.id} then #{item.updatedBy,jdbcType=BIGINT}
            </if>
          </foreach>
        </trim>
        <trim prefix="updated_name = case" suffix="end,">
          <foreach collection="list" item="item">
            <if test="item.updatedName != null">
              when id = #{item.id} then #{item.updatedName,jdbcType=VARCHAR}
            </if>
          </foreach>
        </trim>
      </trim>
      where id in
      <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item.id}
      </foreach>
  </update>

  <!-- 批量插入方法 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into jyml_store_sku_confirm_detail (
      business_org_id, store_id, store_code, process_detail_id, goods_no, category, middle_category,
      small_category, sub_category, category_path, system_suggest, previous_review_result, my_confirm,
      review_result, submit_by, submit_name, submit_time, review_by, review_name, review_time,
      version, `status`, extend, created_by, created_name, updated_by, updated_name
    ) values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.businessOrgId,jdbcType=BIGINT},
        #{item.storeId,jdbcType=BIGINT},
        #{item.storeCode,jdbcType=VARCHAR},
        #{item.processDetailId,jdbcType=BIGINT},
        #{item.goodsNo,jdbcType=VARCHAR},
        #{item.category,jdbcType=VARCHAR},
        #{item.middleCategory,jdbcType=VARCHAR},
        #{item.smallCategory,jdbcType=VARCHAR},
        #{item.subCategory,jdbcType=VARCHAR},
        #{item.categoryPath,jdbcType=VARCHAR},
        #{item.systemSuggest,jdbcType=INTEGER},
        #{item.previousReviewResult,jdbcType=INTEGER},
        #{item.myConfirm,jdbcType=INTEGER},
        #{item.reviewResult,jdbcType=INTEGER},
        #{item.submitBy,jdbcType=BIGINT},
        #{item.submitName,jdbcType=VARCHAR},
        #{item.submitTime,jdbcType=TIMESTAMP},
        #{item.reviewBy,jdbcType=BIGINT},
        #{item.reviewName,jdbcType=VARCHAR},
        #{item.reviewTime,jdbcType=TIMESTAMP},
        #{item.version,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.extend,jdbcType=VARCHAR},
        #{item.createdBy,jdbcType=BIGINT},
        #{item.createdName,jdbcType=VARCHAR},
        #{item.updatedBy,jdbcType=BIGINT},
        #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>
