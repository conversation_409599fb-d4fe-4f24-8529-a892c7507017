<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.TrackRetultEfficiencyAnalyseExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
        <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
        <result column="revise_types" jdbcType="VARCHAR" property="reviseTypes" />
        <result column="org_cnt_v2" jdbcType="VARCHAR" property="orgCntV2" />
        <result column="org_cnt_v3" jdbcType="VARCHAR" property="orgCntV3" />
        <result column="cnt_v3" jdbcType="VARCHAR" property="cntV3" />
        <result column="cnt_v3_v2" jdbcType="VARCHAR" property="cntV3V2" />
        <result column="cnt_xz" jdbcType="VARCHAR" property="cntXz" />
        <result column="cnt_tc" jdbcType="VARCHAR" property="cntTc" />
        <result column="avg_sku_cnt" jdbcType="VARCHAR" property="avgSkuCnt" />
        <result column="avg_sku_cnt_v3_v2" jdbcType="VARCHAR" property="avgSkuCntV3V2" />
        <result column="avg_sku_cnt_tc" jdbcType="VARCHAR" property="avgSkuCntTc" />
        <result column="avg_sku_cnt_jc" jdbcType="VARCHAR" property="avgSkuCntJc" />
        <result column="avg_sku_cnt_dc" jdbcType="VARCHAR" property="avgSkuCntDc" />
        <result column="avg_sku_cnt_aa" jdbcType="VARCHAR" property="avgSkuCntAa" />
        <result column="avg_sku_cnt_a1" jdbcType="VARCHAR" property="avgSkuCntA1" />
        <result column="amt_cum_30_total" jdbcType="VARCHAR" property="amtCum30Total" />
        <result column="amt_cum_30_rate_v3_total" jdbcType="VARCHAR" property="amtCum30RateV3Total" />
        <result column="amt_cum_30_rate_v3_v2_total" jdbcType="VARCHAR" property="amtCum30RateV3V2Total" />
        <result column="amt_cum_30_v3_v2_total" jdbcType="VARCHAR" property="amtCum30V3V2Total" />
        <result column="amt_cum_30_v3_v2_1_total" jdbcType="VARCHAR" property="amtCum30V3V21Total" />
        <result column="amt_cum_30_rate_v3_total_aa" jdbcType="VARCHAR" property="amtCum30RateV3TotalAa" />
        <result column="amt_cum_30_rate_v3_total_a1" jdbcType="VARCHAR" property="amtCum30RateV3TotalA1" />
        <result column="amt_cum_30_rate_v3_total_tc" jdbcType="VARCHAR" property="amtCum30RateV3TotalTc" />
        <result column="amt_cum_30_rate_v3_total_jc" jdbcType="VARCHAR" property="amtCum30RateV3TotalJc" />
        <result column="amt_cum_30_rate_v3_total_dc" jdbcType="VARCHAR" property="amtCum30RateV3TotalDc" />
        <result column="amt_cum_30_rate_v3_total_on" jdbcType="VARCHAR" property="amtCum30RateV3TotalOn" />
        <result column="amt_cum_30_rate_v3_total_off" jdbcType="VARCHAR" property="amtCum30RateV3TotalOff" />
        <result column="profit_cum_30_v3_total" jdbcType="VARCHAR" property="profitCum30V3Total" />
        <result column="profit_cum_30_v3_v2_total" jdbcType="VARCHAR" property="profitCum30V3V2Total" />
        <result column="profit_cum_30_v3_v2_1_total" jdbcType="VARCHAR" property="profitCum30V3V21Total" />
        <result column="profit_cum_30_rate_v3_total" jdbcType="VARCHAR" property="profitCum30RateV3Total" />
        <result column="amt_cum_30_rate_gx_v3_total" jdbcType="VARCHAR" property="amtCum30RateGxV3Total" />
        <result column="profit_cum_30_rate_v3_v2_total" jdbcType="VARCHAR" property="profitCum30RateV3V2Total" />
        <result column="profit_cum_30_rate_v3_total_aa" jdbcType="VARCHAR" property="profitCum30RateV3TotalAa" />
        <result column="profit_cum_30_rate_v3_total_a1" jdbcType="VARCHAR" property="profitCum30RateV3TotalA1" />
        <result column="profit_cum_30_rate_v3_total_tc" jdbcType="VARCHAR" property="profitCum30RateV3TotalTc" />
        <result column="profit_cum_30_rate_v3_total_jc" jdbcType="VARCHAR" property="profitCum30RateV3TotalJc" />
        <result column="profit_cum_30_rate_v3_total_dc" jdbcType="VARCHAR" property="profitCum30RateV3TotalDc" />
        <result column="profit_cum_30_rate_v3_total_on" jdbcType="VARCHAR" property="profitCum30RateV3TotalOn" />
        <result column="profit_cum_30_rate_v3_total_off" jdbcType="VARCHAR" property="profitCum30RateV3TotalOff" />
        <result column="avg_cnt" jdbcType="VARCHAR" property="avgCnt" />
        <result column="amt_cum_30_null" jdbcType="VARCHAR" property="amtCum30Null" />
        <result column="avg_amt_cum_30_null" jdbcType="VARCHAR" property="avgAmtCum30Null" />
        <result column="amt_cum_30_new" jdbcType="VARCHAR" property="amtCum30New" />
        <result column="avg_amt_cum_30_new" jdbcType="VARCHAR" property="avgAmtCum30New" />
        <result column="amt_cum_30_rate_new" jdbcType="VARCHAR" property="amtCum30RateNew" />
        <result column="profit_cum_30_new" jdbcType="VARCHAR" property="profitCum30New" />
        <result column="profit_cum_30_rate_new" jdbcType="VARCHAR" property="profitCum30RateNew" />
        <result column="amt_cum_30_v3" jdbcType="VARCHAR" property="amtCum30V3" />
        <result column="amt_cum_30_rate_v3" jdbcType="VARCHAR" property="amtCum30RateV3" />
        <result column="amt_cum_30_rate_v3_v2" jdbcType="VARCHAR" property="amtCum30RateV3V2" />
        <result column="amt_cum_30_v3_v2" jdbcType="VARCHAR" property="amtCum30V3V2" />
        <result column="amt_cum_30_v3_v2_1" jdbcType="VARCHAR" property="amtCum30V3V21" />
        <result column="amt_cum_30_rate_v3_aa" jdbcType="VARCHAR" property="amtCum30RateV3Aa" />
        <result column="amt_cum_30_rate_v3_a1" jdbcType="VARCHAR" property="amtCum30RateV3A1" />
        <result column="amt_cum_30_rate_v3_tc" jdbcType="VARCHAR" property="amtCum30RateV3Tc" />
        <result column="amt_cum_30_rate_v3_jc" jdbcType="VARCHAR" property="amtCum30RateV3Jc" />
        <result column="amt_cum_30_rate_v3_dc" jdbcType="VARCHAR" property="amtCum30RateV3Dc" />
        <result column="amt_cum_30_rate_v3_on" jdbcType="VARCHAR" property="amtCum30RateV3On" />
        <result column="amt_cum_30_rate_v3_off" jdbcType="VARCHAR" property="amtCum30RateV3Off" />
        <result column="profit_cum_30_v3" jdbcType="VARCHAR" property="profitCum30V3" />
        <result column="profit_cum_30_v3_v2" jdbcType="VARCHAR" property="profitCum30V3V2" />
        <result column="profit_cum_30_v3_v2_1" jdbcType="VARCHAR" property="profitCum30V3V21" />
        <result column="profit_cum_30_rate_v3" jdbcType="VARCHAR" property="profitCum30RateV3" />
        <result column="amt_cum_30_rate_gx_v3" jdbcType="VARCHAR" property="amtCum30RateGxV3" />
        <result column="profit_cum_30_rate_v3_v2" jdbcType="VARCHAR" property="profitCum30RateV3V2" />
        <result column="profit_cum_30_rate_v3_aa" jdbcType="VARCHAR" property="profitCum30RateV3Aa" />
        <result column="profit_cum_30_rate_v3_a1" jdbcType="VARCHAR" property="profitCum30RateV3A1" />
        <result column="profit_cum_30_rate_v3_tc" jdbcType="VARCHAR" property="profitCum30RateV3Tc" />
        <result column="profit_cum_30_rate_v3_jc" jdbcType="VARCHAR" property="profitCum30RateV3Jc" />
        <result column="profit_cum_30_rate_v3_dc" jdbcType="VARCHAR" property="profitCum30RateV3Dc" />
        <result column="profit_cum_30_rate_v3_on" jdbcType="VARCHAR" property="profitCum30RateV3On" />
        <result column="profit_cum_30_rate_v3_off" jdbcType="VARCHAR" property="profitCum30RateV3Off" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    </resultMap>
    <sql id="Base_Column_List">
        id, task_id, zone_new, chain_name, revise_types, org_cnt_v2, org_cnt_v3, cnt_v3,
        cnt_v3_v2, cnt_xz, cnt_tc, avg_sku_cnt, avg_sku_cnt_v3_v2, avg_sku_cnt_tc, avg_sku_cnt_jc,
        avg_sku_cnt_dc, avg_sku_cnt_aa, avg_sku_cnt_a1, amt_cum_30_total, amt_cum_30_rate_v3_total,
        amt_cum_30_rate_v3_v2_total, amt_cum_30_v3_v2_total, amt_cum_30_v3_v2_1_total, amt_cum_30_rate_v3_total_aa,
        amt_cum_30_rate_v3_total_a1, amt_cum_30_rate_v3_total_tc, amt_cum_30_rate_v3_total_jc,
        amt_cum_30_rate_v3_total_dc, amt_cum_30_rate_v3_total_on, amt_cum_30_rate_v3_total_off,
        profit_cum_30_v3_total, profit_cum_30_v3_v2_total, profit_cum_30_v3_v2_1_total, profit_cum_30_rate_v3_total,
        amt_cum_30_rate_gx_v3_total, profit_cum_30_rate_v3_v2_total, profit_cum_30_rate_v3_total_aa,
        profit_cum_30_rate_v3_total_a1, profit_cum_30_rate_v3_total_tc, profit_cum_30_rate_v3_total_jc,
        profit_cum_30_rate_v3_total_dc, profit_cum_30_rate_v3_total_on, profit_cum_30_rate_v3_total_off,
        avg_cnt, amt_cum_30_null, avg_amt_cum_30_null, amt_cum_30_new, avg_amt_cum_30_new,
        amt_cum_30_rate_new, profit_cum_30_new, profit_cum_30_rate_new, amt_cum_30_v3, amt_cum_30_rate_v3,
        amt_cum_30_rate_v3_v2, amt_cum_30_v3_v2, amt_cum_30_v3_v2_1, amt_cum_30_rate_v3_aa,
        amt_cum_30_rate_v3_a1, amt_cum_30_rate_v3_tc, amt_cum_30_rate_v3_jc, amt_cum_30_rate_v3_dc,
        amt_cum_30_rate_v3_on, amt_cum_30_rate_v3_off, profit_cum_30_v3, profit_cum_30_v3_v2,
        profit_cum_30_v3_v2_1, profit_cum_30_rate_v3, amt_cum_30_rate_gx_v3, profit_cum_30_rate_v3_v2,
        profit_cum_30_rate_v3_aa, profit_cum_30_rate_v3_a1, profit_cum_30_rate_v3_tc, profit_cum_30_rate_v3_jc,
        profit_cum_30_rate_v3_dc, profit_cum_30_rate_v3_on, profit_cum_30_rate_v3_off, gmt_create
    </sql>

    <select id="countTrackRetultEfficiencyAnalyse" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT count(*)
        FROM track_retult_efficiency_analyse
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectTrackRetultEfficiencyAnalyseByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_retult_efficiency_analyse
        <if test="taskId != null">
            where task_id = #{taskId,jdbcType=BIGINT}
        </if>
        <if test="page >= 0">
            limit ${page} , ${perPage}
        </if>
    </select>
</mapper>