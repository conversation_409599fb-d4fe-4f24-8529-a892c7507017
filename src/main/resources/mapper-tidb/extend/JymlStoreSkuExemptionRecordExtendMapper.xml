<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.JymlStoreSkuExemptionRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="jhiSpecification" jdbcType="VARCHAR" property="jhispecification" />
    <result column="factoryid" jdbcType="VARCHAR" property="factoryid" />
    <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `source`, business_org_id, store_id, store_code, goods_no, goods_name, small_category,
    small_category_name, sub_category, sub_category_name, jhiSpecification, factoryid,
    goodsunit, component, goods_contribute_rate, sale_amount_quarter, sale_amount_tow_quarter,
    sale_amount_three_quarter, `status`, gmt_create, gmt_update, extend, version, created_by,
    created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord" useGeneratedKeys="true">
    insert into jyml_store_sku_exemption_record (`source`, business_org_id, store_id,
           store_code, goods_no, goods_name,
           small_category, small_category_name, sub_category,
           sub_category_name, jhiSpecification, factoryid,
           goodsunit, component, goods_contribute_rate,
           sale_amount_quarter, sale_amount_tow_quarter,
           sale_amount_three_quarter,
           created_by, created_name,
           updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.source,jdbcType=TINYINT}, #{item.businessOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
      #{item.smallCategory,jdbcType=VARCHAR}, #{item.smallCategoryName,jdbcType=VARCHAR}, #{item.subCategory,jdbcType=VARCHAR},
      #{item.subCategoryName,jdbcType=VARCHAR}, #{item.jhispecification,jdbcType=VARCHAR}, #{item.factoryid,jdbcType=VARCHAR},
      #{item.goodsunit,jdbcType=VARCHAR}, #{item.component,jdbcType=VARCHAR}, #{item.goodsContributeRate,jdbcType=DECIMAL},
      #{item.saleAmountQuarter,jdbcType=DECIMAL}, #{item.saleAmountTowQuarter,jdbcType=DECIMAL},
      #{item.saleAmountThreeQuarter,jdbcType=DECIMAL},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="getAllStoreIds" resultType="java.lang.Long">
    select store_id from jyml_store_sku_exemption_record group by store_id
  </select>
  <select id="getGoodsNosByStoreId" resultType="java.lang.String">
    select goods_no from jyml_store_sku_exemption_record where store_id = #{storeId}
  </select>
  <select id="getGoodsCountByStore" resultType="com.cowell.scib.service.dto.customize.StoreGoodsCountDTO">
    select store_code as storeCode, count(*) as goodsCount from jyml_store_sku_exemption_record
    <if test="storeCodes != null and storeCodes.size > 0">
      where store_code in
      <foreach collection="storeCodes" item="storeCode" separator="," index="index" open="(" close=")">
        #{storeCode}
      </foreach>
    </if>
    group by store_code
  </select>
  <select id="getGoodsByStore" resultType="com.cowell.scib.service.dto.customize.StoreGoodsDTO">
    select store_code as storeCode, goods_no as goodsNo from jyml_store_sku_exemption_record
    <if test="storeCodes != null and storeCodes.size > 0">
      where store_code in
      <foreach collection="storeCodes" item="storeCode" separator="," index="index" open="(" close=")">
        #{storeCode}
      </foreach>
    </if>
    group by store_code,goods_no

  </select>
</mapper>
