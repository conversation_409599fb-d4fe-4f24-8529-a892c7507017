<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.JymlSkuMaxLimitConfigureExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="VARCHAR" property="storeTypeName" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="sku_max_limit" jdbcType="INTEGER" property="skuMaxLimit" />
    <result column="sku_suggested_limit" jdbcType="INTEGER" property="skuSuggestedLimit" />
    <result column="sku_lower_limit" jdbcType="INTEGER" property="skuLowerLimit" />
    <result column="sku_suggested_lower_limit" jdbcType="INTEGER" property="skuSuggestedLowerLimit" />
    <result column="ingredient_count" jdbcType="INTEGER" property="ingredientCount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_org_id, platform_name, business_org_id, business_name, city, store_type,
    store_type_name, category, category_name, middle_category, middle_category_name,
    small_category, small_category_name, sub_category, sub_category_name, sku_max_limit,
    sku_suggested_limit, sku_lower_limit, sku_suggested_lower_limit, ingredient_count,
    `status`, gmt_create, gmt_update, create_by, update_by, extend, version, env, create_by_id,
    update_by_id, rx_otc
  </sql>
  <select id="selectAllCategory" resultType="com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure">
    select category, category_name as categoryName, middle_category as middleCategory, middle_category_name as middleCategoryName,
           small_category as smallCategory, small_category_name as smallCategoryName, sub_category as subCategory, sub_category_name as subCategoryName
    from jyml_sku_max_limit_configure
    where `status` = 0
    <if test="version != null">
      and version = #{version}
    </if>
    group by category, middle_category, small_category, sub_category
    order by category, middle_category, small_category, sub_category
  </select>
  <select id="selectWithCtrlCategory" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from
        (select * ,CONCAT(category,middle_category,small_category,sub_category) as ctrlCategory, CONCAT(category_name,middle_category_name,small_category_name,sub_category_name) as ctrlCategoryName from jyml_sku_max_limit_configure
          where `status` = 0
            <if test="version != null">
              and version = #{version}
            </if>
          <if test="businessOrgIds != null and businessOrgIds.size > 0">
            and business_org_id in
            <foreach collection="businessOrgIds" item="businessOrgId" index="index" open="(" close=")" separator=",">
              #{businessOrgId}
            </foreach>
          </if>
          <if test="citys != null and citys.size > 0">
            and city in
            <foreach collection="citys" item="city" index="index" open="(" close=")" separator=",">
              #{city}
            </foreach>
          </if>
          <if test="storeTypes != null and storeTypes.size > 0">
            and store_type in
            <foreach collection="storeTypes" item="storeType" index="index" open="(" close=")" separator=",">
              #{storeType}
            </foreach>
          </if>
          <if test="storeTypeNames != null and storeTypeNames.size > 0">
            and store_type_name in
            <foreach collection="storeTypeNames" item="storeTypeName" index="index" open="(" close=")" separator=",">
              #{storeTypeName}
            </foreach>
          </if>
    ) as a
    where a.version = #{version} and `status` = 0
    <if test="businessOrgIds != null and businessOrgIds.size > 0">
      and a.business_org_id in
      <foreach collection="businessOrgIds" item="businessOrgId" index="index" open="(" close=")" separator=",">
        #{businessOrgId}
      </foreach>
    </if>
    <if test="citys != null and citys.size > 0">
      and a.city in
      <foreach collection="citys" item="city" index="index" open="(" close=")" separator=",">
        #{city}
      </foreach>
    </if>
    <if test="storeTypes != null and storeTypes.size > 0">
      and a.store_type in
      <foreach collection="storeTypes" item="storeType" index="index" open="(" close=")" separator=",">
        #{storeType}
      </foreach>
    </if>
    <if test="storeTypeNames != null and storeTypeNames.size > 0">
      and a.store_type_name in
      <foreach collection="storeTypeNames" item="storeTypeName" index="index" open="(" close=")" separator=",">
        #{storeTypeName}
      </foreach>
    </if>
    <if test="ctrlCategorys != null and ctrlCategorys.size > 0">
      and a.ctrlCategory in
      <foreach collection="ctrlCategorys" item="ctrlCategory" index="index" open="(" close=")" separator=",">
        #{ctrlCategory}
      </foreach>
    </if>
    <if test="ctrlCategoryNames != null and ctrlCategoryNames.size > 0">
      and a.ctrlCategoryName in
      <foreach collection="ctrlCategoryNames" item="ctrlCategoryName" index="index" open="(" close=")" separator=",">
        #{ctrlCategoryName}
      </foreach>
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_sku_max_limit_configure
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=VARCHAR},
      </if>
      <if test="record.storeTypeName != null">
        store_type_name = #{record.storeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategoryName != null">
        middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuMaxLimit != null">
        sku_max_limit = #{record.skuMaxLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuSuggestedLimit != null">
        sku_suggested_limit = #{record.skuSuggestedLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuLowerLimit != null">
        sku_lower_limit = #{record.skuLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.skuSuggestedLowerLimit != null">
        sku_suggested_lower_limit = #{record.skuSuggestedLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.ingredientCount != null">
        ingredient_count = #{record.ingredientCount,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.env != null">
        env = #{record.env,jdbcType=VARCHAR},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.rxOtc != null">
        rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    order by id
    <if test="limit != null">
      limit ${limit}
    </if>
  </update>

</mapper>
