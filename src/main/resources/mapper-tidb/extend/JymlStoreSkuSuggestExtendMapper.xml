<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.extend.JymlStoreSkuSuggestExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuSuggest">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="suggest_manage_status" jdbcType="VARCHAR" property="suggestManageStatus" />
    <result column="suggest_manage_status_name" jdbcType="VARCHAR" property="suggestManageStatusName" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="stock_quantity" jdbcType="VARCHAR" property="stockQuantity" />
    <result column="sales_last_30d" jdbcType="VARCHAR" property="salesLast30d" />
    <result column="sales_last_60d" jdbcType="VARCHAR" property="salesLast60d" />
    <result column="sales_last_90d" jdbcType="VARCHAR" property="salesLast90d" />
    <result column="sales_last_180d" jdbcType="VARCHAR" property="salesLast180d" />
    <result column="seasonal_component" jdbcType="VARCHAR" property="seasonalComponent" />
    <result column="sales_last_year_same_period_last_90d" jdbcType="VARCHAR" property="salesLastYearSamePeriodLast90d" />
    <result column="city_store_sales_rate_90d" jdbcType="VARCHAR" property="cityStoreSalesRate90d" />
    <result column="store_type_sales_rate_90d" jdbcType="VARCHAR" property="storeTypeSalesRate90d" />
    <result column="days_unsold" jdbcType="VARCHAR" property="daysUnsold" />
    <result column="city_product_contribution_rank" jdbcType="VARCHAR" property="cityProductContributionRank" />
    <result column="city_subcategory_contribution_rank" jdbcType="VARCHAR" property="citySubcategoryContributionRank" />
    <result column="price_lsj" jdbcType="VARCHAR" property="priceLsj" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_org_id, store_id, store_code, goods_no, goods_name, suggest_manage_status,
    suggest_manage_status_name, category, category_name, rx_otc, middle_category, middle_category_name,
    small_category, small_category_name, sub_category, sub_category_name, component,
    stock_quantity, sales_last_30d, sales_last_60d, sales_last_90d, sales_last_180d,
    seasonal_component, sales_last_year_same_period_last_90d, city_store_sales_rate_90d,
    store_type_sales_rate_90d, days_unsold, city_product_contribution_rank, city_subcategory_contribution_rank,
    price_lsj, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name
  </sql>
  <select id="selectSimpleByExample" resultType="com.cowell.scib.entityTidb.JymlStoreSkuSuggest"
          parameterType="com.cowell.scib.entityTidb.JymlStoreSkuSuggestExample">
    select
    <if test="distinct">
      distinct
    </if>
    goods_no as goodsNo, category,sub_category as subCategory
    from jyml_store_sku_suggest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

</mapper>
