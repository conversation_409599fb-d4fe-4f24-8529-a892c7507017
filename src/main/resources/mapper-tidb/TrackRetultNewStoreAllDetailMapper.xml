<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackRetultNewStoreAllDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="org_no" jdbcType="VARCHAR" property="orgNo" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goodsname" jdbcType="VARCHAR" property="goodsname" />
    <result column="goodsspec" jdbcType="VARCHAR" property="goodsspec" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="jx_cate1_name" jdbcType="VARCHAR" property="jxCate1Name" />
    <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="suggest_ph_qty" jdbcType="DECIMAL" property="suggestPhQty" />
    <result column="ph_cost" jdbcType="DECIMAL" property="phCost" />
    <result column="taotai_type" jdbcType="VARCHAR" property="taotaiType" />
    <result column="stjb" jdbcType="VARCHAR" property="stjb" />
    <result column="grossprofit" jdbcType="VARCHAR" property="grossprofit" />
    <result column="sub_category_id" jdbcType="VARCHAR" property="subCategoryId" />
    <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
    <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
    <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
    <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="dtpgood" jdbcType="VARCHAR" property="dtpgood" />
    <result column="flag_disease" jdbcType="VARCHAR" property="flagDisease" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="distribind" jdbcType="VARCHAR" property="distribind" />
    <result column="precious" jdbcType="VARCHAR" property="precious" />
    <result column="refretailprice" jdbcType="VARCHAR" property="refretailprice" />
    <result column="in_stock_rate_dx" jdbcType="VARCHAR" property="inStockRateDx" />
    <result column="in_sales_rate_dx" jdbcType="VARCHAR" property="inSalesRateDx" />
    <result column="num_cum_90_dx" jdbcType="VARCHAR" property="numCum90Dx" />
    <result column="bill_cnts_cum_90_dx" jdbcType="VARCHAR" property="billCntsCum90Dx" />
    <result column="amt_cum_90_dx" jdbcType="VARCHAR" property="amtCum90Dx" />
    <result column="profit_rate_90_dx" jdbcType="VARCHAR" property="profitRate90Dx" />
    <result column="in_stock_rate_city" jdbcType="VARCHAR" property="inStockRateCity" />
    <result column="in_sales_rate_city" jdbcType="VARCHAR" property="inSalesRateCity" />
    <result column="num_cum_90_city" jdbcType="VARCHAR" property="numCum90City" />
    <result column="bill_cnts_cum_90_city" jdbcType="VARCHAR" property="billCntsCum90City" />
    <result column="amt_cum_90_city" jdbcType="VARCHAR" property="amtCum90City" />
    <result column="profit_rate_90_city" jdbcType="VARCHAR" property="profitRate90City" />
    <result column="in_stock_rate_qy" jdbcType="VARCHAR" property="inStockRateQy" />
    <result column="in_sales_rate_qy" jdbcType="VARCHAR" property="inSalesRateQy" />
    <result column="num_cum_90_qy" jdbcType="VARCHAR" property="numCum90Qy" />
    <result column="bill_cnts_cum_90_qy" jdbcType="VARCHAR" property="billCntsCum90Qy" />
    <result column="amt_cum_90_qy" jdbcType="VARCHAR" property="amtCum90Qy" />
    <result column="profit_rate_90_qy" jdbcType="VARCHAR" property="profitRate90Qy" />
    <result column="in_stock_rate_md" jdbcType="VARCHAR" property="inStockRateMd" />
    <result column="in_sales_rate_md" jdbcType="VARCHAR" property="inSalesRateMd" />
    <result column="num_cum_90_md" jdbcType="VARCHAR" property="numCum90Md" />
    <result column="bill_cnts_cum_90_md" jdbcType="VARCHAR" property="billCntsCum90Md" />
    <result column="amt_cum_90_md" jdbcType="VARCHAR" property="amtCum90Md" />
    <result column="profit_rate_90_md" jdbcType="VARCHAR" property="profitRate90Md" />
    <result column="jy_able" jdbcType="TINYINT" property="jyAble" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
    <result column="bak1" jdbcType="VARCHAR" property="bak1" />
    <result column="bak2" jdbcType="VARCHAR" property="bak2" />
    <result column="bak3" jdbcType="VARCHAR" property="bak3" />
    <result column="bak4" jdbcType="VARCHAR" property="bak4" />
    <result column="bak5" jdbcType="VARCHAR" property="bak5" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, org_no, store_name, goods_id, goodsname, goodsspec, manufacturer, jx_cate1_name, 
    goodsunit, `level`, suggest_ph_qty, ph_cost, taotai_type, stjb, grossprofit, sub_category_id, 
    classone_name, classtwo_name, classthree_name, classfour_name, component, dtpgood, 
    flag_disease, department, distribind, precious, refretailprice, in_stock_rate_dx, 
    in_sales_rate_dx, num_cum_90_dx, bill_cnts_cum_90_dx, amt_cum_90_dx, profit_rate_90_dx, 
    in_stock_rate_city, in_sales_rate_city, num_cum_90_city, bill_cnts_cum_90_city, amt_cum_90_city, 
    profit_rate_90_city, in_stock_rate_qy, in_sales_rate_qy, num_cum_90_qy, bill_cnts_cum_90_qy, 
    amt_cum_90_qy, profit_rate_90_qy, in_stock_rate_md, in_sales_rate_md, num_cum_90_md, 
    bill_cnts_cum_90_md, amt_cum_90_md, profit_rate_90_md, jy_able, rx_otc, bak1, bak2, 
    bak3, bak4, bak5, extend, gmt_create, `status`
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_retult_new_store_all_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_retult_new_store_all_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_retult_new_store_all_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetailExample">
    delete from track_retult_new_store_all_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail" useGeneratedKeys="true">
    insert into track_retult_new_store_all_detail (task_id, org_no, store_name, 
      goods_id, goodsname, goodsspec, 
      manufacturer, jx_cate1_name, goodsunit, 
      `level`, suggest_ph_qty, ph_cost, 
      taotai_type, stjb, grossprofit, 
      sub_category_id, classone_name, classtwo_name, 
      classthree_name, classfour_name, component, 
      dtpgood, flag_disease, department, 
      distribind, precious, refretailprice, 
      in_stock_rate_dx, in_sales_rate_dx, num_cum_90_dx, 
      bill_cnts_cum_90_dx, amt_cum_90_dx, profit_rate_90_dx, 
      in_stock_rate_city, in_sales_rate_city, num_cum_90_city, 
      bill_cnts_cum_90_city, amt_cum_90_city, profit_rate_90_city, 
      in_stock_rate_qy, in_sales_rate_qy, num_cum_90_qy, 
      bill_cnts_cum_90_qy, amt_cum_90_qy, profit_rate_90_qy, 
      in_stock_rate_md, in_sales_rate_md, num_cum_90_md, 
      bill_cnts_cum_90_md, amt_cum_90_md, profit_rate_90_md, 
      jy_able, rx_otc, bak1, 
      bak2, bak3, bak4, bak5, 
      extend, gmt_create, `status`
      )
    values (#{taskId,jdbcType=BIGINT}, #{orgNo,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{goodsId,jdbcType=VARCHAR}, #{goodsname,jdbcType=VARCHAR}, #{goodsspec,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{jxCate1Name,jdbcType=VARCHAR}, #{goodsunit,jdbcType=VARCHAR}, 
      #{level,jdbcType=VARCHAR}, #{suggestPhQty,jdbcType=DECIMAL}, #{phCost,jdbcType=DECIMAL}, 
      #{taotaiType,jdbcType=VARCHAR}, #{stjb,jdbcType=VARCHAR}, #{grossprofit,jdbcType=VARCHAR}, 
      #{subCategoryId,jdbcType=VARCHAR}, #{classoneName,jdbcType=VARCHAR}, #{classtwoName,jdbcType=VARCHAR}, 
      #{classthreeName,jdbcType=VARCHAR}, #{classfourName,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, 
      #{dtpgood,jdbcType=VARCHAR}, #{flagDisease,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR}, 
      #{distribind,jdbcType=VARCHAR}, #{precious,jdbcType=VARCHAR}, #{refretailprice,jdbcType=VARCHAR}, 
      #{inStockRateDx,jdbcType=VARCHAR}, #{inSalesRateDx,jdbcType=VARCHAR}, #{numCum90Dx,jdbcType=VARCHAR}, 
      #{billCntsCum90Dx,jdbcType=VARCHAR}, #{amtCum90Dx,jdbcType=VARCHAR}, #{profitRate90Dx,jdbcType=VARCHAR}, 
      #{inStockRateCity,jdbcType=VARCHAR}, #{inSalesRateCity,jdbcType=VARCHAR}, #{numCum90City,jdbcType=VARCHAR}, 
      #{billCntsCum90City,jdbcType=VARCHAR}, #{amtCum90City,jdbcType=VARCHAR}, #{profitRate90City,jdbcType=VARCHAR}, 
      #{inStockRateQy,jdbcType=VARCHAR}, #{inSalesRateQy,jdbcType=VARCHAR}, #{numCum90Qy,jdbcType=VARCHAR}, 
      #{billCntsCum90Qy,jdbcType=VARCHAR}, #{amtCum90Qy,jdbcType=VARCHAR}, #{profitRate90Qy,jdbcType=VARCHAR}, 
      #{inStockRateMd,jdbcType=VARCHAR}, #{inSalesRateMd,jdbcType=VARCHAR}, #{numCum90Md,jdbcType=VARCHAR}, 
      #{billCntsCum90Md,jdbcType=VARCHAR}, #{amtCum90Md,jdbcType=VARCHAR}, #{profitRate90Md,jdbcType=VARCHAR}, 
      #{jyAble,jdbcType=TINYINT}, #{rxOtc,jdbcType=VARCHAR}, #{bak1,jdbcType=VARCHAR}, 
      #{bak2,jdbcType=VARCHAR}, #{bak3,jdbcType=VARCHAR}, #{bak4,jdbcType=VARCHAR}, #{bak5,jdbcType=VARCHAR}, 
      #{extend,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail" useGeneratedKeys="true">
    insert into track_retult_new_store_all_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="orgNo != null">
        org_no,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsname != null">
        goodsname,
      </if>
      <if test="goodsspec != null">
        goodsspec,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="jxCate1Name != null">
        jx_cate1_name,
      </if>
      <if test="goodsunit != null">
        goodsunit,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="suggestPhQty != null">
        suggest_ph_qty,
      </if>
      <if test="phCost != null">
        ph_cost,
      </if>
      <if test="taotaiType != null">
        taotai_type,
      </if>
      <if test="stjb != null">
        stjb,
      </if>
      <if test="grossprofit != null">
        grossprofit,
      </if>
      <if test="subCategoryId != null">
        sub_category_id,
      </if>
      <if test="classoneName != null">
        classone_name,
      </if>
      <if test="classtwoName != null">
        classtwo_name,
      </if>
      <if test="classthreeName != null">
        classthree_name,
      </if>
      <if test="classfourName != null">
        classfour_name,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="dtpgood != null">
        dtpgood,
      </if>
      <if test="flagDisease != null">
        flag_disease,
      </if>
      <if test="department != null">
        department,
      </if>
      <if test="distribind != null">
        distribind,
      </if>
      <if test="precious != null">
        precious,
      </if>
      <if test="refretailprice != null">
        refretailprice,
      </if>
      <if test="inStockRateDx != null">
        in_stock_rate_dx,
      </if>
      <if test="inSalesRateDx != null">
        in_sales_rate_dx,
      </if>
      <if test="numCum90Dx != null">
        num_cum_90_dx,
      </if>
      <if test="billCntsCum90Dx != null">
        bill_cnts_cum_90_dx,
      </if>
      <if test="amtCum90Dx != null">
        amt_cum_90_dx,
      </if>
      <if test="profitRate90Dx != null">
        profit_rate_90_dx,
      </if>
      <if test="inStockRateCity != null">
        in_stock_rate_city,
      </if>
      <if test="inSalesRateCity != null">
        in_sales_rate_city,
      </if>
      <if test="numCum90City != null">
        num_cum_90_city,
      </if>
      <if test="billCntsCum90City != null">
        bill_cnts_cum_90_city,
      </if>
      <if test="amtCum90City != null">
        amt_cum_90_city,
      </if>
      <if test="profitRate90City != null">
        profit_rate_90_city,
      </if>
      <if test="inStockRateQy != null">
        in_stock_rate_qy,
      </if>
      <if test="inSalesRateQy != null">
        in_sales_rate_qy,
      </if>
      <if test="numCum90Qy != null">
        num_cum_90_qy,
      </if>
      <if test="billCntsCum90Qy != null">
        bill_cnts_cum_90_qy,
      </if>
      <if test="amtCum90Qy != null">
        amt_cum_90_qy,
      </if>
      <if test="profitRate90Qy != null">
        profit_rate_90_qy,
      </if>
      <if test="inStockRateMd != null">
        in_stock_rate_md,
      </if>
      <if test="inSalesRateMd != null">
        in_sales_rate_md,
      </if>
      <if test="numCum90Md != null">
        num_cum_90_md,
      </if>
      <if test="billCntsCum90Md != null">
        bill_cnts_cum_90_md,
      </if>
      <if test="amtCum90Md != null">
        amt_cum_90_md,
      </if>
      <if test="profitRate90Md != null">
        profit_rate_90_md,
      </if>
      <if test="jyAble != null">
        jy_able,
      </if>
      <if test="rxOtc != null">
        rx_otc,
      </if>
      <if test="bak1 != null">
        bak1,
      </if>
      <if test="bak2 != null">
        bak2,
      </if>
      <if test="bak3 != null">
        bak3,
      </if>
      <if test="bak4 != null">
        bak4,
      </if>
      <if test="bak5 != null">
        bak5,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="orgNo != null">
        #{orgNo,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsname != null">
        #{goodsname,jdbcType=VARCHAR},
      </if>
      <if test="goodsspec != null">
        #{goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jxCate1Name != null">
        #{jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="suggestPhQty != null">
        #{suggestPhQty,jdbcType=DECIMAL},
      </if>
      <if test="phCost != null">
        #{phCost,jdbcType=DECIMAL},
      </if>
      <if test="taotaiType != null">
        #{taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="stjb != null">
        #{stjb,jdbcType=VARCHAR},
      </if>
      <if test="grossprofit != null">
        #{grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        #{subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="dtpgood != null">
        #{dtpgood,jdbcType=VARCHAR},
      </if>
      <if test="flagDisease != null">
        #{flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="distribind != null">
        #{distribind,jdbcType=VARCHAR},
      </if>
      <if test="precious != null">
        #{precious,jdbcType=VARCHAR},
      </if>
      <if test="refretailprice != null">
        #{refretailprice,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateDx != null">
        #{inStockRateDx,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateDx != null">
        #{inSalesRateDx,jdbcType=VARCHAR},
      </if>
      <if test="numCum90Dx != null">
        #{numCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90Dx != null">
        #{billCntsCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90Dx != null">
        #{amtCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90Dx != null">
        #{profitRate90Dx,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateCity != null">
        #{inStockRateCity,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateCity != null">
        #{inSalesRateCity,jdbcType=VARCHAR},
      </if>
      <if test="numCum90City != null">
        #{numCum90City,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90City != null">
        #{billCntsCum90City,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90City != null">
        #{amtCum90City,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90City != null">
        #{profitRate90City,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateQy != null">
        #{inStockRateQy,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateQy != null">
        #{inSalesRateQy,jdbcType=VARCHAR},
      </if>
      <if test="numCum90Qy != null">
        #{numCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90Qy != null">
        #{billCntsCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90Qy != null">
        #{amtCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90Qy != null">
        #{profitRate90Qy,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateMd != null">
        #{inStockRateMd,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateMd != null">
        #{inSalesRateMd,jdbcType=VARCHAR},
      </if>
      <if test="numCum90Md != null">
        #{numCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90Md != null">
        #{billCntsCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90Md != null">
        #{amtCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90Md != null">
        #{profitRate90Md,jdbcType=VARCHAR},
      </if>
      <if test="jyAble != null">
        #{jyAble,jdbcType=TINYINT},
      </if>
      <if test="rxOtc != null">
        #{rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="bak1 != null">
        #{bak1,jdbcType=VARCHAR},
      </if>
      <if test="bak2 != null">
        #{bak2,jdbcType=VARCHAR},
      </if>
      <if test="bak3 != null">
        #{bak3,jdbcType=VARCHAR},
      </if>
      <if test="bak4 != null">
        #{bak4,jdbcType=VARCHAR},
      </if>
      <if test="bak5 != null">
        #{bak5,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetailExample" resultType="java.lang.Long">
    select count(*) from track_retult_new_store_all_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_retult_new_store_all_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.orgNo != null">
        org_no = #{record.orgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsname != null">
        goodsname = #{record.goodsname,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsspec != null">
        goodsspec = #{record.goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.jxCate1Name != null">
        jx_cate1_name = #{record.jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsunit != null">
        goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestPhQty != null">
        suggest_ph_qty = #{record.suggestPhQty,jdbcType=DECIMAL},
      </if>
      <if test="record.phCost != null">
        ph_cost = #{record.phCost,jdbcType=DECIMAL},
      </if>
      <if test="record.taotaiType != null">
        taotai_type = #{record.taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="record.stjb != null">
        stjb = #{record.stjb,jdbcType=VARCHAR},
      </if>
      <if test="record.grossprofit != null">
        grossprofit = #{record.grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryId != null">
        sub_category_id = #{record.subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.classoneName != null">
        classone_name = #{record.classoneName,jdbcType=VARCHAR},
      </if>
      <if test="record.classtwoName != null">
        classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="record.classthreeName != null">
        classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="record.classfourName != null">
        classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.dtpgood != null">
        dtpgood = #{record.dtpgood,jdbcType=VARCHAR},
      </if>
      <if test="record.flagDisease != null">
        flag_disease = #{record.flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="record.department != null">
        department = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.distribind != null">
        distribind = #{record.distribind,jdbcType=VARCHAR},
      </if>
      <if test="record.precious != null">
        precious = #{record.precious,jdbcType=VARCHAR},
      </if>
      <if test="record.refretailprice != null">
        refretailprice = #{record.refretailprice,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockRateDx != null">
        in_stock_rate_dx = #{record.inStockRateDx,jdbcType=VARCHAR},
      </if>
      <if test="record.inSalesRateDx != null">
        in_sales_rate_dx = #{record.inSalesRateDx,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum90Dx != null">
        num_cum_90_dx = #{record.numCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="record.billCntsCum90Dx != null">
        bill_cnts_cum_90_dx = #{record.billCntsCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum90Dx != null">
        amt_cum_90_dx = #{record.amtCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate90Dx != null">
        profit_rate_90_dx = #{record.profitRate90Dx,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockRateCity != null">
        in_stock_rate_city = #{record.inStockRateCity,jdbcType=VARCHAR},
      </if>
      <if test="record.inSalesRateCity != null">
        in_sales_rate_city = #{record.inSalesRateCity,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum90City != null">
        num_cum_90_city = #{record.numCum90City,jdbcType=VARCHAR},
      </if>
      <if test="record.billCntsCum90City != null">
        bill_cnts_cum_90_city = #{record.billCntsCum90City,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum90City != null">
        amt_cum_90_city = #{record.amtCum90City,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate90City != null">
        profit_rate_90_city = #{record.profitRate90City,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockRateQy != null">
        in_stock_rate_qy = #{record.inStockRateQy,jdbcType=VARCHAR},
      </if>
      <if test="record.inSalesRateQy != null">
        in_sales_rate_qy = #{record.inSalesRateQy,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum90Qy != null">
        num_cum_90_qy = #{record.numCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.billCntsCum90Qy != null">
        bill_cnts_cum_90_qy = #{record.billCntsCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum90Qy != null">
        amt_cum_90_qy = #{record.amtCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate90Qy != null">
        profit_rate_90_qy = #{record.profitRate90Qy,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockRateMd != null">
        in_stock_rate_md = #{record.inStockRateMd,jdbcType=VARCHAR},
      </if>
      <if test="record.inSalesRateMd != null">
        in_sales_rate_md = #{record.inSalesRateMd,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum90Md != null">
        num_cum_90_md = #{record.numCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="record.billCntsCum90Md != null">
        bill_cnts_cum_90_md = #{record.billCntsCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum90Md != null">
        amt_cum_90_md = #{record.amtCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate90Md != null">
        profit_rate_90_md = #{record.profitRate90Md,jdbcType=VARCHAR},
      </if>
      <if test="record.jyAble != null">
        jy_able = #{record.jyAble,jdbcType=TINYINT},
      </if>
      <if test="record.rxOtc != null">
        rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="record.bak1 != null">
        bak1 = #{record.bak1,jdbcType=VARCHAR},
      </if>
      <if test="record.bak2 != null">
        bak2 = #{record.bak2,jdbcType=VARCHAR},
      </if>
      <if test="record.bak3 != null">
        bak3 = #{record.bak3,jdbcType=VARCHAR},
      </if>
      <if test="record.bak4 != null">
        bak4 = #{record.bak4,jdbcType=VARCHAR},
      </if>
      <if test="record.bak5 != null">
        bak5 = #{record.bak5,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_retult_new_store_all_detail
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      org_no = #{record.orgNo,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_id = #{record.goodsId,jdbcType=VARCHAR},
      goodsname = #{record.goodsname,jdbcType=VARCHAR},
      goodsspec = #{record.goodsspec,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      jx_cate1_name = #{record.jxCate1Name,jdbcType=VARCHAR},
      goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=VARCHAR},
      suggest_ph_qty = #{record.suggestPhQty,jdbcType=DECIMAL},
      ph_cost = #{record.phCost,jdbcType=DECIMAL},
      taotai_type = #{record.taotaiType,jdbcType=VARCHAR},
      stjb = #{record.stjb,jdbcType=VARCHAR},
      grossprofit = #{record.grossprofit,jdbcType=VARCHAR},
      sub_category_id = #{record.subCategoryId,jdbcType=VARCHAR},
      classone_name = #{record.classoneName,jdbcType=VARCHAR},
      classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      dtpgood = #{record.dtpgood,jdbcType=VARCHAR},
      flag_disease = #{record.flagDisease,jdbcType=VARCHAR},
      department = #{record.department,jdbcType=VARCHAR},
      distribind = #{record.distribind,jdbcType=VARCHAR},
      precious = #{record.precious,jdbcType=VARCHAR},
      refretailprice = #{record.refretailprice,jdbcType=VARCHAR},
      in_stock_rate_dx = #{record.inStockRateDx,jdbcType=VARCHAR},
      in_sales_rate_dx = #{record.inSalesRateDx,jdbcType=VARCHAR},
      num_cum_90_dx = #{record.numCum90Dx,jdbcType=VARCHAR},
      bill_cnts_cum_90_dx = #{record.billCntsCum90Dx,jdbcType=VARCHAR},
      amt_cum_90_dx = #{record.amtCum90Dx,jdbcType=VARCHAR},
      profit_rate_90_dx = #{record.profitRate90Dx,jdbcType=VARCHAR},
      in_stock_rate_city = #{record.inStockRateCity,jdbcType=VARCHAR},
      in_sales_rate_city = #{record.inSalesRateCity,jdbcType=VARCHAR},
      num_cum_90_city = #{record.numCum90City,jdbcType=VARCHAR},
      bill_cnts_cum_90_city = #{record.billCntsCum90City,jdbcType=VARCHAR},
      amt_cum_90_city = #{record.amtCum90City,jdbcType=VARCHAR},
      profit_rate_90_city = #{record.profitRate90City,jdbcType=VARCHAR},
      in_stock_rate_qy = #{record.inStockRateQy,jdbcType=VARCHAR},
      in_sales_rate_qy = #{record.inSalesRateQy,jdbcType=VARCHAR},
      num_cum_90_qy = #{record.numCum90Qy,jdbcType=VARCHAR},
      bill_cnts_cum_90_qy = #{record.billCntsCum90Qy,jdbcType=VARCHAR},
      amt_cum_90_qy = #{record.amtCum90Qy,jdbcType=VARCHAR},
      profit_rate_90_qy = #{record.profitRate90Qy,jdbcType=VARCHAR},
      in_stock_rate_md = #{record.inStockRateMd,jdbcType=VARCHAR},
      in_sales_rate_md = #{record.inSalesRateMd,jdbcType=VARCHAR},
      num_cum_90_md = #{record.numCum90Md,jdbcType=VARCHAR},
      bill_cnts_cum_90_md = #{record.billCntsCum90Md,jdbcType=VARCHAR},
      amt_cum_90_md = #{record.amtCum90Md,jdbcType=VARCHAR},
      profit_rate_90_md = #{record.profitRate90Md,jdbcType=VARCHAR},
      jy_able = #{record.jyAble,jdbcType=TINYINT},
      rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
      bak1 = #{record.bak1,jdbcType=VARCHAR},
      bak2 = #{record.bak2,jdbcType=VARCHAR},
      bak3 = #{record.bak3,jdbcType=VARCHAR},
      bak4 = #{record.bak4,jdbcType=VARCHAR},
      bak5 = #{record.bak5,jdbcType=VARCHAR},
      extend = #{record.extend,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail">
    update track_retult_new_store_all_detail
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="orgNo != null">
        org_no = #{orgNo,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsname != null">
        goodsname = #{goodsname,jdbcType=VARCHAR},
      </if>
      <if test="goodsspec != null">
        goodsspec = #{goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jxCate1Name != null">
        jx_cate1_name = #{jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        goodsunit = #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="suggestPhQty != null">
        suggest_ph_qty = #{suggestPhQty,jdbcType=DECIMAL},
      </if>
      <if test="phCost != null">
        ph_cost = #{phCost,jdbcType=DECIMAL},
      </if>
      <if test="taotaiType != null">
        taotai_type = #{taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="stjb != null">
        stjb = #{stjb,jdbcType=VARCHAR},
      </if>
      <if test="grossprofit != null">
        grossprofit = #{grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        sub_category_id = #{subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        classone_name = #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        classthree_name = #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        classfour_name = #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="dtpgood != null">
        dtpgood = #{dtpgood,jdbcType=VARCHAR},
      </if>
      <if test="flagDisease != null">
        flag_disease = #{flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        department = #{department,jdbcType=VARCHAR},
      </if>
      <if test="distribind != null">
        distribind = #{distribind,jdbcType=VARCHAR},
      </if>
      <if test="precious != null">
        precious = #{precious,jdbcType=VARCHAR},
      </if>
      <if test="refretailprice != null">
        refretailprice = #{refretailprice,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateDx != null">
        in_stock_rate_dx = #{inStockRateDx,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateDx != null">
        in_sales_rate_dx = #{inSalesRateDx,jdbcType=VARCHAR},
      </if>
      <if test="numCum90Dx != null">
        num_cum_90_dx = #{numCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90Dx != null">
        bill_cnts_cum_90_dx = #{billCntsCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90Dx != null">
        amt_cum_90_dx = #{amtCum90Dx,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90Dx != null">
        profit_rate_90_dx = #{profitRate90Dx,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateCity != null">
        in_stock_rate_city = #{inStockRateCity,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateCity != null">
        in_sales_rate_city = #{inSalesRateCity,jdbcType=VARCHAR},
      </if>
      <if test="numCum90City != null">
        num_cum_90_city = #{numCum90City,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90City != null">
        bill_cnts_cum_90_city = #{billCntsCum90City,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90City != null">
        amt_cum_90_city = #{amtCum90City,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90City != null">
        profit_rate_90_city = #{profitRate90City,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateQy != null">
        in_stock_rate_qy = #{inStockRateQy,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateQy != null">
        in_sales_rate_qy = #{inSalesRateQy,jdbcType=VARCHAR},
      </if>
      <if test="numCum90Qy != null">
        num_cum_90_qy = #{numCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90Qy != null">
        bill_cnts_cum_90_qy = #{billCntsCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90Qy != null">
        amt_cum_90_qy = #{amtCum90Qy,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90Qy != null">
        profit_rate_90_qy = #{profitRate90Qy,jdbcType=VARCHAR},
      </if>
      <if test="inStockRateMd != null">
        in_stock_rate_md = #{inStockRateMd,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRateMd != null">
        in_sales_rate_md = #{inSalesRateMd,jdbcType=VARCHAR},
      </if>
      <if test="numCum90Md != null">
        num_cum_90_md = #{numCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="billCntsCum90Md != null">
        bill_cnts_cum_90_md = #{billCntsCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90Md != null">
        amt_cum_90_md = #{amtCum90Md,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90Md != null">
        profit_rate_90_md = #{profitRate90Md,jdbcType=VARCHAR},
      </if>
      <if test="jyAble != null">
        jy_able = #{jyAble,jdbcType=TINYINT},
      </if>
      <if test="rxOtc != null">
        rx_otc = #{rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="bak1 != null">
        bak1 = #{bak1,jdbcType=VARCHAR},
      </if>
      <if test="bak2 != null">
        bak2 = #{bak2,jdbcType=VARCHAR},
      </if>
      <if test="bak3 != null">
        bak3 = #{bak3,jdbcType=VARCHAR},
      </if>
      <if test="bak4 != null">
        bak4 = #{bak4,jdbcType=VARCHAR},
      </if>
      <if test="bak5 != null">
        bak5 = #{bak5,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail">
    update track_retult_new_store_all_detail
    set task_id = #{taskId,jdbcType=BIGINT},
      org_no = #{orgNo,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=VARCHAR},
      goodsname = #{goodsname,jdbcType=VARCHAR},
      goodsspec = #{goodsspec,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      jx_cate1_name = #{jxCate1Name,jdbcType=VARCHAR},
      goodsunit = #{goodsunit,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      suggest_ph_qty = #{suggestPhQty,jdbcType=DECIMAL},
      ph_cost = #{phCost,jdbcType=DECIMAL},
      taotai_type = #{taotaiType,jdbcType=VARCHAR},
      stjb = #{stjb,jdbcType=VARCHAR},
      grossprofit = #{grossprofit,jdbcType=VARCHAR},
      sub_category_id = #{subCategoryId,jdbcType=VARCHAR},
      classone_name = #{classoneName,jdbcType=VARCHAR},
      classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      classthree_name = #{classthreeName,jdbcType=VARCHAR},
      classfour_name = #{classfourName,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      dtpgood = #{dtpgood,jdbcType=VARCHAR},
      flag_disease = #{flagDisease,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      distribind = #{distribind,jdbcType=VARCHAR},
      precious = #{precious,jdbcType=VARCHAR},
      refretailprice = #{refretailprice,jdbcType=VARCHAR},
      in_stock_rate_dx = #{inStockRateDx,jdbcType=VARCHAR},
      in_sales_rate_dx = #{inSalesRateDx,jdbcType=VARCHAR},
      num_cum_90_dx = #{numCum90Dx,jdbcType=VARCHAR},
      bill_cnts_cum_90_dx = #{billCntsCum90Dx,jdbcType=VARCHAR},
      amt_cum_90_dx = #{amtCum90Dx,jdbcType=VARCHAR},
      profit_rate_90_dx = #{profitRate90Dx,jdbcType=VARCHAR},
      in_stock_rate_city = #{inStockRateCity,jdbcType=VARCHAR},
      in_sales_rate_city = #{inSalesRateCity,jdbcType=VARCHAR},
      num_cum_90_city = #{numCum90City,jdbcType=VARCHAR},
      bill_cnts_cum_90_city = #{billCntsCum90City,jdbcType=VARCHAR},
      amt_cum_90_city = #{amtCum90City,jdbcType=VARCHAR},
      profit_rate_90_city = #{profitRate90City,jdbcType=VARCHAR},
      in_stock_rate_qy = #{inStockRateQy,jdbcType=VARCHAR},
      in_sales_rate_qy = #{inSalesRateQy,jdbcType=VARCHAR},
      num_cum_90_qy = #{numCum90Qy,jdbcType=VARCHAR},
      bill_cnts_cum_90_qy = #{billCntsCum90Qy,jdbcType=VARCHAR},
      amt_cum_90_qy = #{amtCum90Qy,jdbcType=VARCHAR},
      profit_rate_90_qy = #{profitRate90Qy,jdbcType=VARCHAR},
      in_stock_rate_md = #{inStockRateMd,jdbcType=VARCHAR},
      in_sales_rate_md = #{inSalesRateMd,jdbcType=VARCHAR},
      num_cum_90_md = #{numCum90Md,jdbcType=VARCHAR},
      bill_cnts_cum_90_md = #{billCntsCum90Md,jdbcType=VARCHAR},
      amt_cum_90_md = #{amtCum90Md,jdbcType=VARCHAR},
      profit_rate_90_md = #{profitRate90Md,jdbcType=VARCHAR},
      jy_able = #{jyAble,jdbcType=TINYINT},
      rx_otc = #{rxOtc,jdbcType=VARCHAR},
      bak1 = #{bak1,jdbcType=VARCHAR},
      bak2 = #{bak2,jdbcType=VARCHAR},
      bak3 = #{bak3,jdbcType=VARCHAR},
      bak4 = #{bak4,jdbcType=VARCHAR},
      bak5 = #{bak5,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>