<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.JymlStoreSkuExemptionRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="jhiSpecification" jdbcType="VARCHAR" property="jhispecification" />
    <result column="factoryid" jdbcType="VARCHAR" property="factoryid" />
    <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="goods_contribute_rate" jdbcType="DECIMAL" property="goodsContributeRate" />
    <result column="sale_amount_quarter" jdbcType="DECIMAL" property="saleAmountQuarter" />
    <result column="sale_amount_tow_quarter" jdbcType="DECIMAL" property="saleAmountTowQuarter" />
    <result column="sale_amount_three_quarter" jdbcType="DECIMAL" property="saleAmountThreeQuarter" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `source`, business_org_id, store_id, store_code, goods_no, goods_name, small_category, 
    small_category_name, sub_category, sub_category_name, jhiSpecification, factoryid, 
    goodsunit, component, goods_contribute_rate, sale_amount_quarter, sale_amount_tow_quarter, 
    sale_amount_three_quarter, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_exemption_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jyml_store_sku_exemption_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_exemption_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecordExample">
    delete from jyml_store_sku_exemption_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord" useGeneratedKeys="true">
    insert into jyml_store_sku_exemption_record (`source`, business_org_id, store_id, 
      store_code, goods_no, goods_name, 
      small_category, small_category_name, sub_category, 
      sub_category_name, jhiSpecification, factoryid, 
      goodsunit, component, goods_contribute_rate, 
      sale_amount_quarter, sale_amount_tow_quarter, 
      sale_amount_three_quarter, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{source,jdbcType=TINYINT}, #{businessOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{smallCategory,jdbcType=VARCHAR}, #{smallCategoryName,jdbcType=VARCHAR}, #{subCategory,jdbcType=VARCHAR}, 
      #{subCategoryName,jdbcType=VARCHAR}, #{jhispecification,jdbcType=VARCHAR}, #{factoryid,jdbcType=VARCHAR}, 
      #{goodsunit,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, #{goodsContributeRate,jdbcType=DECIMAL}, 
      #{saleAmountQuarter,jdbcType=DECIMAL}, #{saleAmountTowQuarter,jdbcType=DECIMAL}, 
      #{saleAmountThreeQuarter,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=BIGINT}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord" useGeneratedKeys="true">
    insert into jyml_store_sku_exemption_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="source != null">
        `source`,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="smallCategoryName != null">
        small_category_name,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="jhispecification != null">
        jhiSpecification,
      </if>
      <if test="factoryid != null">
        factoryid,
      </if>
      <if test="goodsunit != null">
        goodsunit,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="goodsContributeRate != null">
        goods_contribute_rate,
      </if>
      <if test="saleAmountQuarter != null">
        sale_amount_quarter,
      </if>
      <if test="saleAmountTowQuarter != null">
        sale_amount_tow_quarter,
      </if>
      <if test="saleAmountThreeQuarter != null">
        sale_amount_three_quarter,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="jhispecification != null">
        #{jhispecification,jdbcType=VARCHAR},
      </if>
      <if test="factoryid != null">
        #{factoryid,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="goodsContributeRate != null">
        #{goodsContributeRate,jdbcType=DECIMAL},
      </if>
      <if test="saleAmountQuarter != null">
        #{saleAmountQuarter,jdbcType=DECIMAL},
      </if>
      <if test="saleAmountTowQuarter != null">
        #{saleAmountTowQuarter,jdbcType=DECIMAL},
      </if>
      <if test="saleAmountThreeQuarter != null">
        #{saleAmountThreeQuarter,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecordExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_exemption_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_exemption_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.jhispecification != null">
        jhiSpecification = #{record.jhispecification,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryid != null">
        factoryid = #{record.factoryid,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsunit != null">
        goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsContributeRate != null">
        goods_contribute_rate = #{record.goodsContributeRate,jdbcType=DECIMAL},
      </if>
      <if test="record.saleAmountQuarter != null">
        sale_amount_quarter = #{record.saleAmountQuarter,jdbcType=DECIMAL},
      </if>
      <if test="record.saleAmountTowQuarter != null">
        sale_amount_tow_quarter = #{record.saleAmountTowQuarter,jdbcType=DECIMAL},
      </if>
      <if test="record.saleAmountThreeQuarter != null">
        sale_amount_three_quarter = #{record.saleAmountThreeQuarter,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_exemption_record
    set id = #{record.id,jdbcType=BIGINT},
      `source` = #{record.source,jdbcType=TINYINT},
      business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      small_category = #{record.smallCategory,jdbcType=VARCHAR},
      small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      sub_category = #{record.subCategory,jdbcType=VARCHAR},
      sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      jhiSpecification = #{record.jhispecification,jdbcType=VARCHAR},
      factoryid = #{record.factoryid,jdbcType=VARCHAR},
      goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      goods_contribute_rate = #{record.goodsContributeRate,jdbcType=DECIMAL},
      sale_amount_quarter = #{record.saleAmountQuarter,jdbcType=DECIMAL},
      sale_amount_tow_quarter = #{record.saleAmountTowQuarter,jdbcType=DECIMAL},
      sale_amount_three_quarter = #{record.saleAmountThreeQuarter,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=BIGINT},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord">
    update jyml_store_sku_exemption_record
    <set>
      <if test="source != null">
        `source` = #{source,jdbcType=TINYINT},
      </if>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="jhispecification != null">
        jhiSpecification = #{jhispecification,jdbcType=VARCHAR},
      </if>
      <if test="factoryid != null">
        factoryid = #{factoryid,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        goodsunit = #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="goodsContributeRate != null">
        goods_contribute_rate = #{goodsContributeRate,jdbcType=DECIMAL},
      </if>
      <if test="saleAmountQuarter != null">
        sale_amount_quarter = #{saleAmountQuarter,jdbcType=DECIMAL},
      </if>
      <if test="saleAmountTowQuarter != null">
        sale_amount_tow_quarter = #{saleAmountTowQuarter,jdbcType=DECIMAL},
      </if>
      <if test="saleAmountThreeQuarter != null">
        sale_amount_three_quarter = #{saleAmountThreeQuarter,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord">
    update jyml_store_sku_exemption_record
    set `source` = #{source,jdbcType=TINYINT},
      business_org_id = #{businessOrgId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      small_category = #{smallCategory,jdbcType=VARCHAR},
      small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      sub_category = #{subCategory,jdbcType=VARCHAR},
      sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      jhiSpecification = #{jhispecification,jdbcType=VARCHAR},
      factoryid = #{factoryid,jdbcType=VARCHAR},
      goodsunit = #{goodsunit,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      goods_contribute_rate = #{goodsContributeRate,jdbcType=DECIMAL},
      sale_amount_quarter = #{saleAmountQuarter,jdbcType=DECIMAL},
      sale_amount_tow_quarter = #{saleAmountTowQuarter,jdbcType=DECIMAL},
      sale_amount_three_quarter = #{saleAmountThreeQuarter,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
