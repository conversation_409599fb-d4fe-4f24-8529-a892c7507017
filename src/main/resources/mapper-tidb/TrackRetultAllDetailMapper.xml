<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.scib.mapperTidb.TrackRetultAllDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.scib.entityTidb.TrackRetultAllDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="zone_new" jdbcType="VARCHAR" property="zoneNew" />
    <result column="plat_orgid" jdbcType="VARCHAR" property="platOrgid" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="compid" jdbcType="VARCHAR" property="compid" />
    <result column="data_from_v2" jdbcType="VARCHAR" property="dataFromV2" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="org_no" jdbcType="VARCHAR" property="orgNo" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="revise_store_group" jdbcType="VARCHAR" property="reviseStoreGroup" />
    <result column="saleslevel" jdbcType="VARCHAR" property="saleslevel" />
    <result column="tradingarea" jdbcType="VARCHAR" property="tradingarea" />
    <result column="store_group_zy" jdbcType="VARCHAR" property="storeGroupZy" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goodsname" jdbcType="VARCHAR" property="goodsname" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="bak" jdbcType="VARCHAR" property="bak" />
    <result column="sub_category_id" jdbcType="VARCHAR" property="subCategoryId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="new_ind_bak" jdbcType="VARCHAR" property="newIndBak" />
    <result column="goodsunit" jdbcType="VARCHAR" property="goodsunit" />
    <result column="goodsspec" jdbcType="VARCHAR" property="goodsspec" />
    <result column="jx_cate1_name" jdbcType="VARCHAR" property="jxCate1Name" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="classone_name" jdbcType="VARCHAR" property="classoneName" />
    <result column="classtwo_name" jdbcType="VARCHAR" property="classtwoName" />
    <result column="classthree_name" jdbcType="VARCHAR" property="classthreeName" />
    <result column="classfour_name" jdbcType="VARCHAR" property="classfourName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="is_otc" jdbcType="VARCHAR" property="isOtc" />
    <result column="flag_disease" jdbcType="VARCHAR" property="flagDisease" />
    <result column="grossprofit" jdbcType="VARCHAR" property="grossprofit" />
    <result column="taotai_type" jdbcType="VARCHAR" property="taotaiType" />
    <result column="stjb" jdbcType="VARCHAR" property="stjb" />
    <result column="specialattributes_type" jdbcType="VARCHAR" property="specialattributesType" />
    <result column="in_stock_rate" jdbcType="VARCHAR" property="inStockRate" />
    <result column="in_sales_rate" jdbcType="VARCHAR" property="inSalesRate" />
    <result column="sku_int" jdbcType="VARCHAR" property="skuInt" />
    <result column="cost_amt" jdbcType="VARCHAR" property="costAmt" />
    <result column="stall_no_num" jdbcType="VARCHAR" property="stallNoNum" />
    <result column="ph_org_bz_flag" jdbcType="VARCHAR" property="phOrgBzFlag" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="num_cum_90" jdbcType="VARCHAR" property="numCum90" />
    <result column="amt_cum_90" jdbcType="VARCHAR" property="amtCum90" />
    <result column="profit_amt_cum_90" jdbcType="VARCHAR" property="profitAmtCum90" />
    <result column="profit_rate_90" jdbcType="VARCHAR" property="profitRate90" />
    <result column="amt_rate_org_90" jdbcType="VARCHAR" property="amtRateOrg90" />
    <result column="amt_rate_comp_90" jdbcType="VARCHAR" property="amtRateComp90" />
    <result column="num_cum_180" jdbcType="VARCHAR" property="numCum180" />
    <result column="amt_cum_180" jdbcType="VARCHAR" property="amtCum180" />
    <result column="profit_amt_cum_180" jdbcType="VARCHAR" property="profitAmtCum180" />
    <result column="profit_rate_180" jdbcType="VARCHAR" property="profitRate180" />
    <result column="amt_rate_org_180" jdbcType="VARCHAR" property="amtRateOrg180" />
    <result column="amt_rate_comp_180" jdbcType="VARCHAR" property="amtRateComp180" />
    <result column="revise_num_cum_90" jdbcType="VARCHAR" property="reviseNumCum90" />
    <result column="revise_amt_cum_90" jdbcType="VARCHAR" property="reviseAmtCum90" />
    <result column="revise_profit_amt_cum_90" jdbcType="VARCHAR" property="reviseProfitAmtCum90" />
    <result column="revise_profit_rate_90" jdbcType="VARCHAR" property="reviseProfitRate90" />
    <result column="revise_num_cum_180" jdbcType="VARCHAR" property="reviseNumCum180" />
    <result column="revise_amt_cum_180" jdbcType="VARCHAR" property="reviseAmtCum180" />
    <result column="revise_profit_amt_cum_180" jdbcType="VARCHAR" property="reviseProfitAmtCum180" />
    <result column="revise_profit_rate_180" jdbcType="VARCHAR" property="reviseProfitRate180" />
    <result column="retail_price" jdbcType="VARCHAR" property="retailPrice" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, zone_new, plat_orgid, data_from, compid, data_from_v2, city, org_no, 
    store_name, revise_store_group, saleslevel, tradingarea, store_group_zy, goods_id, 
    goodsname, `level`, bak, sub_category_id, remark, new_ind_bak, goodsunit, goodsspec, 
    jx_cate1_name, manufacturer, classone_name, classtwo_name, classthree_name, classfour_name, 
    component, is_otc, flag_disease, grossprofit, taotai_type, stjb, specialattributes_type, 
    in_stock_rate, in_sales_rate, sku_int, cost_amt, stall_no_num, ph_org_bz_flag, store_type, 
    num_cum_90, amt_cum_90, profit_amt_cum_90, profit_rate_90, amt_rate_org_90, amt_rate_comp_90, 
    num_cum_180, amt_cum_180, profit_amt_cum_180, profit_rate_180, amt_rate_org_180, 
    amt_rate_comp_180, revise_num_cum_90, revise_amt_cum_90, revise_profit_amt_cum_90, 
    revise_profit_rate_90, revise_num_cum_180, revise_amt_cum_180, revise_profit_amt_cum_180, 
    revise_profit_rate_180, retail_price, gmt_create, `status`
  </sql>
  <select id="selectByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from track_retult_all_detail_${taskId}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from track_retult_all_detail_${taskId}
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from track_retult_all_detail_${taskId}
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetailExample">
    delete from track_retult_all_detail_${taskId}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetail" useGeneratedKeys="true">
    insert into track_retult_all_detail (task_id, zone_new, plat_orgid, 
      data_from, compid, data_from_v2, 
      city, org_no, store_name, 
      revise_store_group, saleslevel, tradingarea, 
      store_group_zy, goods_id, goodsname, 
      `level`, bak, sub_category_id, 
      remark, new_ind_bak, goodsunit, 
      goodsspec, jx_cate1_name, manufacturer, 
      classone_name, classtwo_name, classthree_name, 
      classfour_name, component, is_otc, 
      flag_disease, grossprofit, taotai_type, 
      stjb, specialattributes_type, in_stock_rate, 
      in_sales_rate, sku_int, cost_amt, 
      stall_no_num, ph_org_bz_flag, store_type, 
      num_cum_90, amt_cum_90, profit_amt_cum_90, 
      profit_rate_90, amt_rate_org_90, amt_rate_comp_90, 
      num_cum_180, amt_cum_180, profit_amt_cum_180, 
      profit_rate_180, amt_rate_org_180, amt_rate_comp_180, 
      revise_num_cum_90, revise_amt_cum_90, revise_profit_amt_cum_90, 
      revise_profit_rate_90, revise_num_cum_180, revise_amt_cum_180, 
      revise_profit_amt_cum_180, revise_profit_rate_180, 
      retail_price, gmt_create, `status`
      )
    values (#{taskId,jdbcType=BIGINT}, #{zoneNew,jdbcType=VARCHAR}, #{platOrgid,jdbcType=VARCHAR}, 
      #{dataFrom,jdbcType=VARCHAR}, #{compid,jdbcType=VARCHAR}, #{dataFromV2,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{orgNo,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{reviseStoreGroup,jdbcType=VARCHAR}, #{saleslevel,jdbcType=VARCHAR}, #{tradingarea,jdbcType=VARCHAR}, 
      #{storeGroupZy,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR}, #{goodsname,jdbcType=VARCHAR}, 
      #{level,jdbcType=VARCHAR}, #{bak,jdbcType=VARCHAR}, #{subCategoryId,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{newIndBak,jdbcType=VARCHAR}, #{goodsunit,jdbcType=VARCHAR}, 
      #{goodsspec,jdbcType=VARCHAR}, #{jxCate1Name,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{classoneName,jdbcType=VARCHAR}, #{classtwoName,jdbcType=VARCHAR}, #{classthreeName,jdbcType=VARCHAR}, 
      #{classfourName,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, #{isOtc,jdbcType=VARCHAR}, 
      #{flagDisease,jdbcType=VARCHAR}, #{grossprofit,jdbcType=VARCHAR}, #{taotaiType,jdbcType=VARCHAR}, 
      #{stjb,jdbcType=VARCHAR}, #{specialattributesType,jdbcType=VARCHAR}, #{inStockRate,jdbcType=VARCHAR}, 
      #{inSalesRate,jdbcType=VARCHAR}, #{skuInt,jdbcType=VARCHAR}, #{costAmt,jdbcType=VARCHAR}, 
      #{stallNoNum,jdbcType=VARCHAR}, #{phOrgBzFlag,jdbcType=VARCHAR}, #{storeType,jdbcType=VARCHAR}, 
      #{numCum90,jdbcType=VARCHAR}, #{amtCum90,jdbcType=VARCHAR}, #{profitAmtCum90,jdbcType=VARCHAR}, 
      #{profitRate90,jdbcType=VARCHAR}, #{amtRateOrg90,jdbcType=VARCHAR}, #{amtRateComp90,jdbcType=VARCHAR}, 
      #{numCum180,jdbcType=VARCHAR}, #{amtCum180,jdbcType=VARCHAR}, #{profitAmtCum180,jdbcType=VARCHAR}, 
      #{profitRate180,jdbcType=VARCHAR}, #{amtRateOrg180,jdbcType=VARCHAR}, #{amtRateComp180,jdbcType=VARCHAR}, 
      #{reviseNumCum90,jdbcType=VARCHAR}, #{reviseAmtCum90,jdbcType=VARCHAR}, #{reviseProfitAmtCum90,jdbcType=VARCHAR}, 
      #{reviseProfitRate90,jdbcType=VARCHAR}, #{reviseNumCum180,jdbcType=VARCHAR}, #{reviseAmtCum180,jdbcType=VARCHAR}, 
      #{reviseProfitAmtCum180,jdbcType=VARCHAR}, #{reviseProfitRate180,jdbcType=VARCHAR}, 
      #{retailPrice,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetail" useGeneratedKeys="true">
    insert into track_retult_all_detail_${taskId}
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="zoneNew != null">
        zone_new,
      </if>
      <if test="platOrgid != null">
        plat_orgid,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="compid != null">
        compid,
      </if>
      <if test="dataFromV2 != null">
        data_from_v2,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="orgNo != null">
        org_no,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="reviseStoreGroup != null">
        revise_store_group,
      </if>
      <if test="saleslevel != null">
        saleslevel,
      </if>
      <if test="tradingarea != null">
        tradingarea,
      </if>
      <if test="storeGroupZy != null">
        store_group_zy,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsname != null">
        goodsname,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="bak != null">
        bak,
      </if>
      <if test="subCategoryId != null">
        sub_category_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="newIndBak != null">
        new_ind_bak,
      </if>
      <if test="goodsunit != null">
        goodsunit,
      </if>
      <if test="goodsspec != null">
        goodsspec,
      </if>
      <if test="jxCate1Name != null">
        jx_cate1_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="classoneName != null">
        classone_name,
      </if>
      <if test="classtwoName != null">
        classtwo_name,
      </if>
      <if test="classthreeName != null">
        classthree_name,
      </if>
      <if test="classfourName != null">
        classfour_name,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="isOtc != null">
        is_otc,
      </if>
      <if test="flagDisease != null">
        flag_disease,
      </if>
      <if test="grossprofit != null">
        grossprofit,
      </if>
      <if test="taotaiType != null">
        taotai_type,
      </if>
      <if test="stjb != null">
        stjb,
      </if>
      <if test="specialattributesType != null">
        specialattributes_type,
      </if>
      <if test="inStockRate != null">
        in_stock_rate,
      </if>
      <if test="inSalesRate != null">
        in_sales_rate,
      </if>
      <if test="skuInt != null">
        sku_int,
      </if>
      <if test="costAmt != null">
        cost_amt,
      </if>
      <if test="stallNoNum != null">
        stall_no_num,
      </if>
      <if test="phOrgBzFlag != null">
        ph_org_bz_flag,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
      <if test="numCum90 != null">
        num_cum_90,
      </if>
      <if test="amtCum90 != null">
        amt_cum_90,
      </if>
      <if test="profitAmtCum90 != null">
        profit_amt_cum_90,
      </if>
      <if test="profitRate90 != null">
        profit_rate_90,
      </if>
      <if test="amtRateOrg90 != null">
        amt_rate_org_90,
      </if>
      <if test="amtRateComp90 != null">
        amt_rate_comp_90,
      </if>
      <if test="numCum180 != null">
        num_cum_180,
      </if>
      <if test="amtCum180 != null">
        amt_cum_180,
      </if>
      <if test="profitAmtCum180 != null">
        profit_amt_cum_180,
      </if>
      <if test="profitRate180 != null">
        profit_rate_180,
      </if>
      <if test="amtRateOrg180 != null">
        amt_rate_org_180,
      </if>
      <if test="amtRateComp180 != null">
        amt_rate_comp_180,
      </if>
      <if test="reviseNumCum90 != null">
        revise_num_cum_90,
      </if>
      <if test="reviseAmtCum90 != null">
        revise_amt_cum_90,
      </if>
      <if test="reviseProfitAmtCum90 != null">
        revise_profit_amt_cum_90,
      </if>
      <if test="reviseProfitRate90 != null">
        revise_profit_rate_90,
      </if>
      <if test="reviseNumCum180 != null">
        revise_num_cum_180,
      </if>
      <if test="reviseAmtCum180 != null">
        revise_amt_cum_180,
      </if>
      <if test="reviseProfitAmtCum180 != null">
        revise_profit_amt_cum_180,
      </if>
      <if test="reviseProfitRate180 != null">
        revise_profit_rate_180,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="platOrgid != null">
        #{platOrgid,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="compid != null">
        #{compid,jdbcType=VARCHAR},
      </if>
      <if test="dataFromV2 != null">
        #{dataFromV2,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="orgNo != null">
        #{orgNo,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="reviseStoreGroup != null">
        #{reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="saleslevel != null">
        #{saleslevel,jdbcType=VARCHAR},
      </if>
      <if test="tradingarea != null">
        #{tradingarea,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupZy != null">
        #{storeGroupZy,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsname != null">
        #{goodsname,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="bak != null">
        #{bak,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        #{subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="newIndBak != null">
        #{newIndBak,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="goodsspec != null">
        #{goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="jxCate1Name != null">
        #{jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="isOtc != null">
        #{isOtc,jdbcType=VARCHAR},
      </if>
      <if test="flagDisease != null">
        #{flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="grossprofit != null">
        #{grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="taotaiType != null">
        #{taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="stjb != null">
        #{stjb,jdbcType=VARCHAR},
      </if>
      <if test="specialattributesType != null">
        #{specialattributesType,jdbcType=VARCHAR},
      </if>
      <if test="inStockRate != null">
        #{inStockRate,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRate != null">
        #{inSalesRate,jdbcType=VARCHAR},
      </if>
      <if test="skuInt != null">
        #{skuInt,jdbcType=VARCHAR},
      </if>
      <if test="costAmt != null">
        #{costAmt,jdbcType=VARCHAR},
      </if>
      <if test="stallNoNum != null">
        #{stallNoNum,jdbcType=VARCHAR},
      </if>
      <if test="phOrgBzFlag != null">
        #{phOrgBzFlag,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="numCum90 != null">
        #{numCum90,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90 != null">
        #{amtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum90 != null">
        #{profitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90 != null">
        #{profitRate90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg90 != null">
        #{amtRateOrg90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp90 != null">
        #{amtRateComp90,jdbcType=VARCHAR},
      </if>
      <if test="numCum180 != null">
        #{numCum180,jdbcType=VARCHAR},
      </if>
      <if test="amtCum180 != null">
        #{amtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum180 != null">
        #{profitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitRate180 != null">
        #{profitRate180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg180 != null">
        #{amtRateOrg180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp180 != null">
        #{amtRateComp180,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum90 != null">
        #{reviseNumCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum90 != null">
        #{reviseAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum90 != null">
        #{reviseProfitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate90 != null">
        #{reviseProfitRate90,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum180 != null">
        #{reviseNumCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum180 != null">
        #{reviseAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum180 != null">
        #{reviseProfitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate180 != null">
        #{reviseProfitRate180,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetailExample" resultType="java.lang.Long">
    select count(*) from track_retult_all_detail_${taskId}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update track_retult_all_detail_${taskId}
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.zoneNew != null">
        zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="record.platOrgid != null">
        plat_orgid = #{record.platOrgid,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.compid != null">
        compid = #{record.compid,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFromV2 != null">
        data_from_v2 = #{record.dataFromV2,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.orgNo != null">
        org_no = #{record.orgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseStoreGroup != null">
        revise_store_group = #{record.reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.saleslevel != null">
        saleslevel = #{record.saleslevel,jdbcType=VARCHAR},
      </if>
      <if test="record.tradingarea != null">
        tradingarea = #{record.tradingarea,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGroupZy != null">
        store_group_zy = #{record.storeGroupZy,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsname != null">
        goodsname = #{record.goodsname,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.bak != null">
        bak = #{record.bak,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryId != null">
        sub_category_id = #{record.subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.newIndBak != null">
        new_ind_bak = #{record.newIndBak,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsunit != null">
        goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsspec != null">
        goodsspec = #{record.goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="record.jxCate1Name != null">
        jx_cate1_name = #{record.jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.classoneName != null">
        classone_name = #{record.classoneName,jdbcType=VARCHAR},
      </if>
      <if test="record.classtwoName != null">
        classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="record.classthreeName != null">
        classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="record.classfourName != null">
        classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.isOtc != null">
        is_otc = #{record.isOtc,jdbcType=VARCHAR},
      </if>
      <if test="record.flagDisease != null">
        flag_disease = #{record.flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="record.grossprofit != null">
        grossprofit = #{record.grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="record.taotaiType != null">
        taotai_type = #{record.taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="record.stjb != null">
        stjb = #{record.stjb,jdbcType=VARCHAR},
      </if>
      <if test="record.specialattributesType != null">
        specialattributes_type = #{record.specialattributesType,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockRate != null">
        in_stock_rate = #{record.inStockRate,jdbcType=VARCHAR},
      </if>
      <if test="record.inSalesRate != null">
        in_sales_rate = #{record.inSalesRate,jdbcType=VARCHAR},
      </if>
      <if test="record.skuInt != null">
        sku_int = #{record.skuInt,jdbcType=VARCHAR},
      </if>
      <if test="record.costAmt != null">
        cost_amt = #{record.costAmt,jdbcType=VARCHAR},
      </if>
      <if test="record.stallNoNum != null">
        stall_no_num = #{record.stallNoNum,jdbcType=VARCHAR},
      </if>
      <if test="record.phOrgBzFlag != null">
        ph_org_bz_flag = #{record.phOrgBzFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum90 != null">
        num_cum_90 = #{record.numCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum90 != null">
        amt_cum_90 = #{record.amtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum90 != null">
        profit_amt_cum_90 = #{record.profitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate90 != null">
        profit_rate_90 = #{record.profitRate90,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateOrg90 != null">
        amt_rate_org_90 = #{record.amtRateOrg90,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateComp90 != null">
        amt_rate_comp_90 = #{record.amtRateComp90,jdbcType=VARCHAR},
      </if>
      <if test="record.numCum180 != null">
        num_cum_180 = #{record.numCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.amtCum180 != null">
        amt_cum_180 = #{record.amtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.profitAmtCum180 != null">
        profit_amt_cum_180 = #{record.profitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.profitRate180 != null">
        profit_rate_180 = #{record.profitRate180,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateOrg180 != null">
        amt_rate_org_180 = #{record.amtRateOrg180,jdbcType=VARCHAR},
      </if>
      <if test="record.amtRateComp180 != null">
        amt_rate_comp_180 = #{record.amtRateComp180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseNumCum90 != null">
        revise_num_cum_90 = #{record.reviseNumCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseAmtCum90 != null">
        revise_amt_cum_90 = #{record.reviseAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitAmtCum90 != null">
        revise_profit_amt_cum_90 = #{record.reviseProfitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitRate90 != null">
        revise_profit_rate_90 = #{record.reviseProfitRate90,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseNumCum180 != null">
        revise_num_cum_180 = #{record.reviseNumCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseAmtCum180 != null">
        revise_amt_cum_180 = #{record.reviseAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitAmtCum180 != null">
        revise_profit_amt_cum_180 = #{record.reviseProfitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseProfitRate180 != null">
        revise_profit_rate_180 = #{record.reviseProfitRate180,jdbcType=VARCHAR},
      </if>
      <if test="record.retailPrice != null">
        retail_price = #{record.retailPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update track_retult_all_detail_${taskId}
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      zone_new = #{record.zoneNew,jdbcType=VARCHAR},
      plat_orgid = #{record.platOrgid,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      compid = #{record.compid,jdbcType=VARCHAR},
      data_from_v2 = #{record.dataFromV2,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      org_no = #{record.orgNo,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      revise_store_group = #{record.reviseStoreGroup,jdbcType=VARCHAR},
      saleslevel = #{record.saleslevel,jdbcType=VARCHAR},
      tradingarea = #{record.tradingarea,jdbcType=VARCHAR},
      store_group_zy = #{record.storeGroupZy,jdbcType=VARCHAR},
      goods_id = #{record.goodsId,jdbcType=VARCHAR},
      goodsname = #{record.goodsname,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=VARCHAR},
      bak = #{record.bak,jdbcType=VARCHAR},
      sub_category_id = #{record.subCategoryId,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      new_ind_bak = #{record.newIndBak,jdbcType=VARCHAR},
      goodsunit = #{record.goodsunit,jdbcType=VARCHAR},
      goodsspec = #{record.goodsspec,jdbcType=VARCHAR},
      jx_cate1_name = #{record.jxCate1Name,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      classone_name = #{record.classoneName,jdbcType=VARCHAR},
      classtwo_name = #{record.classtwoName,jdbcType=VARCHAR},
      classthree_name = #{record.classthreeName,jdbcType=VARCHAR},
      classfour_name = #{record.classfourName,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      is_otc = #{record.isOtc,jdbcType=VARCHAR},
      flag_disease = #{record.flagDisease,jdbcType=VARCHAR},
      grossprofit = #{record.grossprofit,jdbcType=VARCHAR},
      taotai_type = #{record.taotaiType,jdbcType=VARCHAR},
      stjb = #{record.stjb,jdbcType=VARCHAR},
      specialattributes_type = #{record.specialattributesType,jdbcType=VARCHAR},
      in_stock_rate = #{record.inStockRate,jdbcType=VARCHAR},
      in_sales_rate = #{record.inSalesRate,jdbcType=VARCHAR},
      sku_int = #{record.skuInt,jdbcType=VARCHAR},
      cost_amt = #{record.costAmt,jdbcType=VARCHAR},
      stall_no_num = #{record.stallNoNum,jdbcType=VARCHAR},
      ph_org_bz_flag = #{record.phOrgBzFlag,jdbcType=VARCHAR},
      store_type = #{record.storeType,jdbcType=VARCHAR},
      num_cum_90 = #{record.numCum90,jdbcType=VARCHAR},
      amt_cum_90 = #{record.amtCum90,jdbcType=VARCHAR},
      profit_amt_cum_90 = #{record.profitAmtCum90,jdbcType=VARCHAR},
      profit_rate_90 = #{record.profitRate90,jdbcType=VARCHAR},
      amt_rate_org_90 = #{record.amtRateOrg90,jdbcType=VARCHAR},
      amt_rate_comp_90 = #{record.amtRateComp90,jdbcType=VARCHAR},
      num_cum_180 = #{record.numCum180,jdbcType=VARCHAR},
      amt_cum_180 = #{record.amtCum180,jdbcType=VARCHAR},
      profit_amt_cum_180 = #{record.profitAmtCum180,jdbcType=VARCHAR},
      profit_rate_180 = #{record.profitRate180,jdbcType=VARCHAR},
      amt_rate_org_180 = #{record.amtRateOrg180,jdbcType=VARCHAR},
      amt_rate_comp_180 = #{record.amtRateComp180,jdbcType=VARCHAR},
      revise_num_cum_90 = #{record.reviseNumCum90,jdbcType=VARCHAR},
      revise_amt_cum_90 = #{record.reviseAmtCum90,jdbcType=VARCHAR},
      revise_profit_amt_cum_90 = #{record.reviseProfitAmtCum90,jdbcType=VARCHAR},
      revise_profit_rate_90 = #{record.reviseProfitRate90,jdbcType=VARCHAR},
      revise_num_cum_180 = #{record.reviseNumCum180,jdbcType=VARCHAR},
      revise_amt_cum_180 = #{record.reviseAmtCum180,jdbcType=VARCHAR},
      revise_profit_amt_cum_180 = #{record.reviseProfitAmtCum180,jdbcType=VARCHAR},
      revise_profit_rate_180 = #{record.reviseProfitRate180,jdbcType=VARCHAR},
      retail_price = #{record.retailPrice,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetail">
    update track_retult_all_detail_${taskId}
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="zoneNew != null">
        zone_new = #{zoneNew,jdbcType=VARCHAR},
      </if>
      <if test="platOrgid != null">
        plat_orgid = #{platOrgid,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="compid != null">
        compid = #{compid,jdbcType=VARCHAR},
      </if>
      <if test="dataFromV2 != null">
        data_from_v2 = #{dataFromV2,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="orgNo != null">
        org_no = #{orgNo,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="reviseStoreGroup != null">
        revise_store_group = #{reviseStoreGroup,jdbcType=VARCHAR},
      </if>
      <if test="saleslevel != null">
        saleslevel = #{saleslevel,jdbcType=VARCHAR},
      </if>
      <if test="tradingarea != null">
        tradingarea = #{tradingarea,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupZy != null">
        store_group_zy = #{storeGroupZy,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsname != null">
        goodsname = #{goodsname,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="bak != null">
        bak = #{bak,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        sub_category_id = #{subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="newIndBak != null">
        new_ind_bak = #{newIndBak,jdbcType=VARCHAR},
      </if>
      <if test="goodsunit != null">
        goodsunit = #{goodsunit,jdbcType=VARCHAR},
      </if>
      <if test="goodsspec != null">
        goodsspec = #{goodsspec,jdbcType=VARCHAR},
      </if>
      <if test="jxCate1Name != null">
        jx_cate1_name = #{jxCate1Name,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="classoneName != null">
        classone_name = #{classoneName,jdbcType=VARCHAR},
      </if>
      <if test="classtwoName != null">
        classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      </if>
      <if test="classthreeName != null">
        classthree_name = #{classthreeName,jdbcType=VARCHAR},
      </if>
      <if test="classfourName != null">
        classfour_name = #{classfourName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="isOtc != null">
        is_otc = #{isOtc,jdbcType=VARCHAR},
      </if>
      <if test="flagDisease != null">
        flag_disease = #{flagDisease,jdbcType=VARCHAR},
      </if>
      <if test="grossprofit != null">
        grossprofit = #{grossprofit,jdbcType=VARCHAR},
      </if>
      <if test="taotaiType != null">
        taotai_type = #{taotaiType,jdbcType=VARCHAR},
      </if>
      <if test="stjb != null">
        stjb = #{stjb,jdbcType=VARCHAR},
      </if>
      <if test="specialattributesType != null">
        specialattributes_type = #{specialattributesType,jdbcType=VARCHAR},
      </if>
      <if test="inStockRate != null">
        in_stock_rate = #{inStockRate,jdbcType=VARCHAR},
      </if>
      <if test="inSalesRate != null">
        in_sales_rate = #{inSalesRate,jdbcType=VARCHAR},
      </if>
      <if test="skuInt != null">
        sku_int = #{skuInt,jdbcType=VARCHAR},
      </if>
      <if test="costAmt != null">
        cost_amt = #{costAmt,jdbcType=VARCHAR},
      </if>
      <if test="stallNoNum != null">
        stall_no_num = #{stallNoNum,jdbcType=VARCHAR},
      </if>
      <if test="phOrgBzFlag != null">
        ph_org_bz_flag = #{phOrgBzFlag,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="numCum90 != null">
        num_cum_90 = #{numCum90,jdbcType=VARCHAR},
      </if>
      <if test="amtCum90 != null">
        amt_cum_90 = #{amtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum90 != null">
        profit_amt_cum_90 = #{profitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="profitRate90 != null">
        profit_rate_90 = #{profitRate90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg90 != null">
        amt_rate_org_90 = #{amtRateOrg90,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp90 != null">
        amt_rate_comp_90 = #{amtRateComp90,jdbcType=VARCHAR},
      </if>
      <if test="numCum180 != null">
        num_cum_180 = #{numCum180,jdbcType=VARCHAR},
      </if>
      <if test="amtCum180 != null">
        amt_cum_180 = #{amtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitAmtCum180 != null">
        profit_amt_cum_180 = #{profitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="profitRate180 != null">
        profit_rate_180 = #{profitRate180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateOrg180 != null">
        amt_rate_org_180 = #{amtRateOrg180,jdbcType=VARCHAR},
      </if>
      <if test="amtRateComp180 != null">
        amt_rate_comp_180 = #{amtRateComp180,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum90 != null">
        revise_num_cum_90 = #{reviseNumCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum90 != null">
        revise_amt_cum_90 = #{reviseAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum90 != null">
        revise_profit_amt_cum_90 = #{reviseProfitAmtCum90,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate90 != null">
        revise_profit_rate_90 = #{reviseProfitRate90,jdbcType=VARCHAR},
      </if>
      <if test="reviseNumCum180 != null">
        revise_num_cum_180 = #{reviseNumCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseAmtCum180 != null">
        revise_amt_cum_180 = #{reviseAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitAmtCum180 != null">
        revise_profit_amt_cum_180 = #{reviseProfitAmtCum180,jdbcType=VARCHAR},
      </if>
      <if test="reviseProfitRate180 != null">
        revise_profit_rate_180 = #{reviseProfitRate180,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.scib.entityTidb.TrackRetultAllDetail">
    update track_retult_all_detail_${taskId}
    set task_id = #{taskId,jdbcType=BIGINT},
      zone_new = #{zoneNew,jdbcType=VARCHAR},
      plat_orgid = #{platOrgid,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      compid = #{compid,jdbcType=VARCHAR},
      data_from_v2 = #{dataFromV2,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      org_no = #{orgNo,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      revise_store_group = #{reviseStoreGroup,jdbcType=VARCHAR},
      saleslevel = #{saleslevel,jdbcType=VARCHAR},
      tradingarea = #{tradingarea,jdbcType=VARCHAR},
      store_group_zy = #{storeGroupZy,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=VARCHAR},
      goodsname = #{goodsname,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      bak = #{bak,jdbcType=VARCHAR},
      sub_category_id = #{subCategoryId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      new_ind_bak = #{newIndBak,jdbcType=VARCHAR},
      goodsunit = #{goodsunit,jdbcType=VARCHAR},
      goodsspec = #{goodsspec,jdbcType=VARCHAR},
      jx_cate1_name = #{jxCate1Name,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      classone_name = #{classoneName,jdbcType=VARCHAR},
      classtwo_name = #{classtwoName,jdbcType=VARCHAR},
      classthree_name = #{classthreeName,jdbcType=VARCHAR},
      classfour_name = #{classfourName,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      is_otc = #{isOtc,jdbcType=VARCHAR},
      flag_disease = #{flagDisease,jdbcType=VARCHAR},
      grossprofit = #{grossprofit,jdbcType=VARCHAR},
      taotai_type = #{taotaiType,jdbcType=VARCHAR},
      stjb = #{stjb,jdbcType=VARCHAR},
      specialattributes_type = #{specialattributesType,jdbcType=VARCHAR},
      in_stock_rate = #{inStockRate,jdbcType=VARCHAR},
      in_sales_rate = #{inSalesRate,jdbcType=VARCHAR},
      sku_int = #{skuInt,jdbcType=VARCHAR},
      cost_amt = #{costAmt,jdbcType=VARCHAR},
      stall_no_num = #{stallNoNum,jdbcType=VARCHAR},
      ph_org_bz_flag = #{phOrgBzFlag,jdbcType=VARCHAR},
      store_type = #{storeType,jdbcType=VARCHAR},
      num_cum_90 = #{numCum90,jdbcType=VARCHAR},
      amt_cum_90 = #{amtCum90,jdbcType=VARCHAR},
      profit_amt_cum_90 = #{profitAmtCum90,jdbcType=VARCHAR},
      profit_rate_90 = #{profitRate90,jdbcType=VARCHAR},
      amt_rate_org_90 = #{amtRateOrg90,jdbcType=VARCHAR},
      amt_rate_comp_90 = #{amtRateComp90,jdbcType=VARCHAR},
      num_cum_180 = #{numCum180,jdbcType=VARCHAR},
      amt_cum_180 = #{amtCum180,jdbcType=VARCHAR},
      profit_amt_cum_180 = #{profitAmtCum180,jdbcType=VARCHAR},
      profit_rate_180 = #{profitRate180,jdbcType=VARCHAR},
      amt_rate_org_180 = #{amtRateOrg180,jdbcType=VARCHAR},
      amt_rate_comp_180 = #{amtRateComp180,jdbcType=VARCHAR},
      revise_num_cum_90 = #{reviseNumCum90,jdbcType=VARCHAR},
      revise_amt_cum_90 = #{reviseAmtCum90,jdbcType=VARCHAR},
      revise_profit_amt_cum_90 = #{reviseProfitAmtCum90,jdbcType=VARCHAR},
      revise_profit_rate_90 = #{reviseProfitRate90,jdbcType=VARCHAR},
      revise_num_cum_180 = #{reviseNumCum180,jdbcType=VARCHAR},
      revise_amt_cum_180 = #{reviseAmtCum180,jdbcType=VARCHAR},
      revise_profit_amt_cum_180 = #{reviseProfitAmtCum180,jdbcType=VARCHAR},
      revise_profit_rate_180 = #{reviseProfitRate180,jdbcType=VARCHAR},
      retail_price = #{retailPrice,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
