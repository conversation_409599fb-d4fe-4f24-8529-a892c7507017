package com.cowell.scib.security;

import com.cowell.scib.config.oauth2.OAuth2JwtAccessTokenConverter;
import com.cowell.scib.service.dto.TokenUserDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Implementation of AuditorAware based on Spring Security.
 */
@Component
public class SpringSecurityAuditorAware implements AuditorAware<String> {

    private final Logger logger = LoggerFactory.getLogger(SpringSecurityAuditorAware.class);

    @Autowired
    private OAuth2JwtAccessTokenConverter converter;

    @Override
    public String getCurrentAuditor() {
        Long currentUserId = 0L;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication == null) {
            logger.debug("oauth2 authentication is null,use system default user id");
            return currentUserId.toString();
        }
        Object details = authentication.getDetails();
        if(!(details instanceof OAuth2AuthenticationDetails)){
            logger.debug("authentication detail is not auth2 detail,use system default user id");
            return currentUserId.toString();
        }

        String principal = ((OAuth2AuthenticationDetails)details).getTokenValue();
        logger.debug("current principal '{}'",principal);
        try {
            if (principal != null) {
                Map<String, Object> map = converter.decode(principal);
                TokenUserDTO tokenUserDTO = TokenUserDTO.toDTO(map);
                currentUserId = tokenUserDTO.getUserId();
            }
        }catch (Throwable e){
            logger.info("fail to get current user id with '{}',use default system id ",principal,e);
        }
        return currentUserId.toString();
    }

    /**
     * 获取当前用户ID lang 型
     * @return
     */
    public Long getCurrentAuditorLong(){

        String operatorStr = this.getCurrentAuditor();
        Long operatorLong = Long.parseLong(operatorStr);

        return operatorLong;
    }
}
