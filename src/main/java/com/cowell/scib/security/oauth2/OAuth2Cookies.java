package com.cowell.scib.security.oauth2;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * Holds the access token and refresh token cookies.
 */
public class OAuth2Cookies {
    private Cookie accessTokenCookie;
    private Cookie refreshTokenCookie;

    private Cookie grantTypeCookie;

    public Cookie getAccessTokenCookie() {
        return accessTokenCookie;
    }

    public Cookie getRefreshTokenCookie() {
        return refreshTokenCookie;
    }

    public void setCookies(Cookie accessTokenCookie, Cookie refreshTokenCookie) {
        this.accessTokenCookie = accessTokenCookie;
        this.refreshTokenCookie = refreshTokenCookie;
    }

    public Cookie getGrantTypeCookie() {
        return grantTypeCookie;
    }

    public void setGrantTypeCookie(Cookie grantTypeCookie) {
        this.grantTypeCookie = grantTypeCookie;
    }

    /**
     * Add the access token and refresh token as cookies to the response after successful authentication.
     *
     * @param response the response to add them to.
     */
    public void addCookiesTo(HttpServletResponse response) {
        response.addCookie(getAccessTokenCookie());
        response.addCookie(getRefreshTokenCookie());
        if (Objects.nonNull(getGrantTypeCookie())) {
            response.addCookie(getGrantTypeCookie());
        }
    }
}
