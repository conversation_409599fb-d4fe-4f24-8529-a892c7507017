package com.cowell.scib.security;

import com.cowell.scib.config.oauth2.OAuth2JwtAccessTokenConverter;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.utils.GetApplicationContextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;

import java.util.Map;
import java.util.Optional;

/**
 * Utility class for Spring Security.
 */
public final class SecurityUtils {
    private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);
    private SecurityUtils() {
    }

    /**
     * Get the login of the current user.
     *
     * @return the login of the current user
     */
    public static Optional<String> getCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
            .map(authentication -> {
                if (authentication.getPrincipal() instanceof UserDetails) {
                    UserDetails springSecurityUser = (UserDetails) authentication.getPrincipal();
                    return springSecurityUser.getUsername();
                } else if (authentication.getPrincipal() instanceof String) {
                    return (String) authentication.getPrincipal();
                }
                return null;
            });
    }

    public static TokenUserDTO getCurrentUserLoginInfo() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication == null) {
            log.debug("oauth2 authentication is null,use system default user id");
            return null;
        }
        Object details = authentication.getDetails();
        if(!(details instanceof OAuth2AuthenticationDetails)){
            log.debug("authentication detail is not auth2 detail,use system default user id");
            return null;
        }

        String principal = ((OAuth2AuthenticationDetails)details).getTokenValue();
        log.debug("current principal '{}'",principal);
        try {
            if (principal != null) {
                //TODO
//                Map<String, Object> map = converter.decode(principal);
                Map<String, Object> map = null;
                TokenUserDTO tokenUserDTO = TokenUserDTO.toDTO(map);
                return tokenUserDTO;
            }
        }catch (Throwable e){
            log.info("fail to get current user id with '{}',use default system id ",principal,e);
        }
        return null;
    }
    /**
     * Check if a user is authenticated.
     *
     * @return true if the user is authenticated, false otherwise
     */
    public static boolean isAuthenticated() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
            .map(authentication -> authentication.getAuthorities().stream()
                .noneMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(AuthoritiesConstants.ANONYMOUS)))
            .orElse(false);
    }

    /**
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the isUserInRole() method in the Servlet API
     *
     * @param authority the authority to check
     * @return true if the current user has the authority, false otherwise
     */
    public static boolean isCurrentUserInRole(String authority) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
            .map(authentication -> authentication.getAuthorities().stream()
                .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority)))
            .orElse(false);
    }

    public static TokenUserDTO getCurrentUserToken() {
        TokenUserDTO tokenUserDTO = new TokenUserDTO();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication == null) {
            log.debug("oauth2 authentication is null,use system default user id");
            return tokenUserDTO;
        }
        Object details = authentication.getDetails();
        if(!(details instanceof OAuth2AuthenticationDetails)){
            log.debug("authentication detail is not auth2 detail,use system default user id");
            return tokenUserDTO;
        }

        String principal = ((OAuth2AuthenticationDetails)details).getTokenValue();
        log.debug("current principal '{}'",principal);
        try {
            if (principal != null) {
                OAuth2JwtAccessTokenConverter converter= (OAuth2JwtAccessTokenConverter) GetApplicationContextUtils.getBean("OAuth2JwtAccessTokenConverter");
                Map<String, Object> map = converter.decode(principal);
                tokenUserDTO = TokenUserDTO.toDTO(map);
            }
        }catch (Throwable e){
            log.info("fail to get current user id with '{}',use default system id ",principal,e);
        }
        return tokenUserDTO;
    }
}
