package com.cowell.scib.service;

import com.cowell.permission.vo.ResourceTreeVO;
import com.cowell.scib.service.dto.rule.HotGoodsImportDTO;

import java.util.List;

public interface CheckService {

    /**
     * 是否有全部权限
     * @param orgId
     * @param userId
     */
    boolean fullPermByOrgId(Long orgId, Long userId);
    boolean fullPermByOrgIdWhitResourceId(Long orgId, Long userId, Long resourceId);

    /**
     * 获取用户权限树MAP集合
     * @param userId
     * @return
     */
    List<ResourceTreeVO>  getUserTreeMap(Long userId);

    List<ResourceTreeVO> getUserTreeMapWhitResourceId(Long userId, Long resourceId);
    /**
     * 判断菜单是否存在
     * @param action
     * @param userId
     * @return
     */
    boolean judgeActionExit(String action, Long userId);
    /**
     * 判断菜单是否存在
     * @param action
     * @param userId
     * @param resourceId
     * @return
     */

    boolean judgeActionExitWithResourceId(String action, Long userId, Long resourceId);

    /**
     * 判断角色存不存在
     * @param roleCode
     * @param userId
     * @return
     */
    boolean judgeUserRoleExit(String roleCode, Long userId);

    /**
     * 判断是否有该菜单权限
     * @param action
     * @param userId
     * @return
     */
    Boolean judgeEditAble(Long orgId, String action, Long userId);
    Boolean judgeEditAbleWithResource(Long orgId, String action, Long userId, Long resourceId);

    /**
     * 正整数校验
     * @param num
     * @return
     */
    Boolean isUpNumber(String num);

    /**
     * 操作权限返回值
     * @param orgId
     * @param action
     * @param userId
     * @return
     */
    int operatePerm(Long orgId, String action, Long userId);
    int operatePermWhitResource(Long orgId, String action, Long userId, Long resourceId);

    /**
     * 校验热销品参数
     * @param hotGoodsImportDTO
     * @return
     */
    String checkHotGoodsParam(HotGoodsImportDTO hotGoodsImportDTO);
}
