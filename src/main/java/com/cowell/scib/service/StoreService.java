package com.cowell.scib.service;

import com.cowell.scib.service.dto.MdmCompanyTransformDTO;
import com.cowell.scib.service.dto.MdmLicenseBaseDTO;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/14 14:39
 */
public interface StoreService {

    Map<String, MdmStoreBaseDTO> getBusinessAndStoreIdByStoreNos(List<String> storeNos);

    Map<Long, MdmStoreBaseDTO> findStoreByStoreIds(List<Long> storeIds);

    /**
     * [
     *   {
     *     "id": 17,
     *     "businessId": 512,
     *     "storeId": ***********,
     *     "leaseArea": "85.40",
     *     "oldErpNo": "022",
     *     "region": "西南平台",
     *     "orgPeopleNum": "5.00",
     *     "systemType": "602",
     *     "province": "四川",
     *     "comId": "30C0",
     *     "openDate": "20110902",
     *     "orderOrg": "3000",
     *     "fax": null,
     *     "storeStatus": "营业",
     *     "lastRenovationDate": null,
     *     "outerRentArea": null,
     *     "calendar": "高济",
     *     "closeDate": null,
     *     "customeRage": null,
     *     "dept": "00",
     *     "storeNo": "A116",
     *     "insuranceSort": "普通医保店",
     *     "operatingArea": "85.40",
     *     "tel": null,
     *     "storeShotName": "瑞康成都花径路药店",
     *     "city": "成都",
     *     "storeInnerNo": null,
     *     "storeName": "成都瑞康医药连锁有限公司成华区花径路药店",
     *     "address": "成都市成华区花径路115号附56号",
     *     "salesChannel": "10",
     *     "salesOrg": "3000",
     *     "refStoreNo": null,
     *     "status": 1,
     *     "annualRent": null,
     *     "opCode": null,
     *     "comName": null,
     *     "contact": null,
     *     "deliveryDate": null,
     *     "salesLevel": "D",
     *     "operationType": null,
     *     "tradingArea": "社区及商住店",
     *     "area": "成华区",
     *     "storeAttr": "直营-自建",
     *     "operationAreaSort": "标准门店",
     *     "businessTime": null,
     *     "orgName": null,
     *     "extend": null,
     *     "medicareStoreId": null,
     *     "dtp": null,
     *     "allocationTeam": null,
     *     "channel": null,
     *     "format": null,
     *     "specialType": null,
     *     "actualNo": "企业级医保店",
     *     "dataSign": null,
     *     "retrospectCode": null,
     *     "dtpType": null,
     *     "receiveType": null,
     *     "marPosition": null,
     *     "operarea": null,
     *     "stdshop": null
     *   }
     * ]
     * 获取mdm门店信息，通过storeCode集合
     * @param storeCodeList
     * @return
     */
    List<MdmStoreBaseDTO> findStoreByStoreNos(List<String> storeCodeList);

    /**
     * {
     *     "id": 17,
     *     "businessId": 512,
     *     "storeId": ***********,
     *     "leaseArea": "85.40",
     *     "oldErpNo": "022",
     *     "region": "西南平台",
     *     "orgPeopleNum": "5.00",
     *     "systemType": "602",
     *     "province": "四川",
     *     "comId": "30C0",
     *     "openDate": "20110902",
     *     "orderOrg": "3000",
     *     "fax": null,
     *     "storeStatus": "营业",
     *     "lastRenovationDate": null,
     *     "outerRentArea": null,
     *     "calendar": "高济",
     *     "closeDate": null,
     *     "customeRage": null,
     *     "dept": "00",
     *     "storeNo": "A116",
     *     "insuranceSort": "普通医保店",
     *     "operatingArea": "85.40",
     *     "tel": null,
     *     "storeShotName": "瑞康成都花径路药店",
     *     "city": "成都",
     *     "storeInnerNo": null,
     *     "storeName": "成都瑞康医药连锁有限公司成华区花径路药店",
     *     "address": "成都市成华区花径路115号附56号",
     *     "salesChannel": "10",
     *     "salesOrg": "3000",
     *     "refStoreNo": null,
     *     "status": 1,
     *     "annualRent": null,
     *     "opCode": null,
     *     "comName": null,
     *     "contact": null,
     *     "deliveryDate": null,
     *     "salesLevel": "D",
     *     "operationType": null,
     *     "tradingArea": "社区及商住店",
     *     "area": "成华区",
     *     "storeAttr": "直营-自建",
     *     "operationAreaSort": "标准门店",
     *     "businessTime": null,
     *     "orgName": null,
     *     "extend": "{\"zsShop\":\"中参店型\",\"psStore\":\"配方店型\",\"manageState\":\"经营状态\",\"handoverDate\":\"停止营业日期\",\"manSubject\":\"是\",\"b2cShop\":\"b2c门店\"}",
     *     "medicareStoreId": null,
     *     "dtp": "是",
     *     "allocationTeam": null,
     *     "channel": null,
     *     "format": null,
     *     "specialType": null,
     *     "actualNo": "企业级医保店",
     *     "dataSign": null,
     *     "retrospectCode": null,
     *     "dtpType": null,
     *     "receiveType": null,
     *     "marPosition": null,
     *     "operarea": null,
     *     "stdshop": null
     *   }
     * 带扩展信息
     * @param storeCodeList
     * @return
     */
    List<MdmStoreBaseDTO> findStoreByStoreNosAndExtend(List<String> storeCodeList);

    List<MdmLicenseBaseDTO> findStoreLicense(Long businessId, Long storeId);

    /**
     * {
     *   "businessIds": null,
     *   "comIds": [
     *     "3070"
     *   ],
     *   "transFormType": 2,
     *   "businessMappings": [
     *     {
     *       "businessId": 27336,
     *       "comId": "3070"
     *     }
     *   ]
     * }
     * 根据法人公司sapCode 获取连锁businessId
     */

    MdmCompanyTransformDTO transformBusiness(MdmCompanyTransformDTO mdmCompanyTransformDTO);

}
