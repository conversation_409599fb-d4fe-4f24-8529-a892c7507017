package com.cowell.scib.service.handle;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.TaskNecessarySingleStoreGoods;
import com.cowell.scib.enums.BudnlTaskDetailDicEnum;
import com.cowell.scib.enums.BundlBoolEnum;
import com.cowell.scib.enums.BundlTaskTypeEnum;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.NecessarySingleStoreGoodsMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.CommonEnumsExtendMapper;
import com.cowell.scib.mapperDgms.extend.NecessarySingleStoreGoodsExtendMapper;
import com.cowell.scib.service.SearchService;
import com.cowell.scib.service.dto.NecessaryGoodsDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class NecessarySingleStoreGoodsHandler{
    private final Logger log = LoggerFactory.getLogger(NecessaryChooseStoreTypeGoodsHandler.class);

    @Autowired
    private NecessarySingleStoreGoodsExtendMapper necessarySingleStoreGoodsExtendMapper;

    @Autowired
    private NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;


    public void check(NecessaryGoodsDTO necessaryGoodsDTO) {
        List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods = necessaryGoodsDTO.getTaskNecessarySingleStoreGoods();
        log.info("单店必备数据：{}",taskNecessarySingleStoreGoods.size());
        if (CollectionUtils.isEmpty(taskNecessarySingleStoreGoods)) {
            return;
        }

        try{

            BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskNecessarySingleStoreGoods.get(0).getTaskId());

            boolean b = taskNecessarySingleStoreGoods.get(0).getFinishFlag().equals(1);
            log.info("单店必备finishFlag：{}",b );
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskNecessarySingleStoreGoods.get(0).getTaskId());
            if (CollectionUtils.isEmpty(bundlingTaskStoreDetailList)){
                return;
            }

            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskNecessarySingleStoreGoods.get(0).getTaskId());
            List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
            List<Long> goodsTypeList=new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodCategoryList)){
                goodsTypeList = goodCategoryList.stream().map(v->Long.valueOf(v.getPerprotyValue())).collect(Collectors.toList());
            }
            //查重
            //todo 删除表加平台Id，门店,商品大类、删除  以商品大类为准   11，12，13   1201


            List<Long> collect1 = goodsTypeList.stream().filter(v -> Constants.ZS_STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zsType=false;
            if (CollectionUtils.isNotEmpty(collect1)){
                zsType = true;
            }

            List<Long> zhTypeList = goodsTypeList.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zhType=false;
            if (CollectionUtils.isNotEmpty(zhTypeList)){
                zhType=true;
            }
            if (zsType){

                NecessarySingleStoreGoodsExample example = new NecessarySingleStoreGoodsExample();
                NecessarySingleStoreGoodsExample.Criteria criteria = example.createCriteria();
                List<String> storeCodeList = getStoreCodeList(bundlingTaskInfo, bundlingTaskStoreDetailList);
                criteria.andPlatformOrgIdEqualTo(taskNecessarySingleStoreGoods.get(0).getPlatformOrgId()).andMiddleCategoryIdEqualTo(Constants.ZS_STORE_TYPE_CATEGORY.get(0)).andStoreCodeIn(storeCodeList).andVersionNotEqualTo(taskNecessarySingleStoreGoods.get(0).getTaskId());

                List<NecessarySingleStoreGoods> necessarySingleStoreGoods = necessarySingleStoreGoodsMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(necessarySingleStoreGoods)){
                    necessarySingleStoreGoodsExtendMapper.batchDel(necessarySingleStoreGoods.stream().map(NecessarySingleStoreGoods::getId).collect(Collectors.toList()));
                }
            }

              if (zhType){
                  NecessarySingleStoreGoodsExample example1 = new NecessarySingleStoreGoodsExample();
                  NecessarySingleStoreGoodsExample.Criteria criteria1 = example1.createCriteria();
                  List<String> storeCodeList = getStoreCodeList(bundlingTaskInfo, bundlingTaskStoreDetailList);
                  criteria1.andPlatformOrgIdEqualTo(taskNecessarySingleStoreGoods.get(0).getPlatformOrgId()).andStoreCodeIn(storeCodeList).andCategoryIdIn(goodsTypeList).andVersionNotEqualTo(taskNecessarySingleStoreGoods.get(0).getTaskId());
                  List<NecessarySingleStoreGoods> necessarySingleStoreGoods1 = necessarySingleStoreGoodsMapper.selectByExample(example1);
                  if (CollectionUtils.isNotEmpty(necessarySingleStoreGoods1)){
                      necessarySingleStoreGoodsExtendMapper.batchDel(necessarySingleStoreGoods1.stream().map(NecessarySingleStoreGoods::getId).collect(Collectors.toList()));
                  }
            }

            ArrayList<NecessarySingleStoreGoods> necessarySingleStoreGoodsArrayList = new ArrayList<>();
            for (TaskNecessarySingleStoreGoods taskNecessarySingleStoreGood : taskNecessarySingleStoreGoods) {
                NecessarySingleStoreGoods necessarySingleStoreGood = new NecessarySingleStoreGoods();
                BeanUtils.copyProperties(taskNecessarySingleStoreGood,necessarySingleStoreGood);
                necessarySingleStoreGood.setId(null);
                necessarySingleStoreGood.setGmtUpdate(new Date());
                necessarySingleStoreGood.setGmtCreate(new Date());
                necessarySingleStoreGood.setVersion(taskNecessarySingleStoreGood.getTaskId());
                necessarySingleStoreGoodsArrayList.add(necessarySingleStoreGood);
            }
            if (CollectionUtils.isNotEmpty(necessarySingleStoreGoodsArrayList)){
                necessarySingleStoreGoodsExtendMapper.batchInsert(necessarySingleStoreGoodsArrayList);
            }
        }catch (Exception e){
            log.error("处理单店数据报错",e);
        }

    }

    private List<String> getStoreCodeList(BundlingTaskInfo bundlingTaskInfo, List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList) {
        List<String> storeCodeList;
        if (BundlTaskTypeEnum.PLATE_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType()) || BundlTaskTypeEnum.BUSINESS_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())) {
            //todo 如果是全层级或者企业层级组货   删除表加平台Id，所有门店,商品大类、删除  以商品大类为准   11，12，13   1201
            storeCodeList = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getStoreCode).collect(Collectors.toList());
        } else {
            //todo 如果是店型级组货   删除表加平台Id，用户选中门店,商品大类、删除  以商品大类为准   11，12，13   1201
            storeCodeList = bundlingTaskStoreDetailList.stream().filter(v -> v.getBundlConfirmAble().equals(BundlBoolEnum.YES.getCode())).map(BundlingTaskStoreDetail::getStoreCode).collect(Collectors.toList());
        }
        return storeCodeList;
    }
}
