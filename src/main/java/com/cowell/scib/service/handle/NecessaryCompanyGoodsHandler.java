package com.cowell.scib.service.handle;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.TaskNecessaryCompanyGoods;
import com.cowell.scib.enums.BudnlTaskDetailDicEnum;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mapperDgms.NecessaryCompanyGoodsMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.MdmTaskDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.NecessaryCompanyGoodsExtendMapper;
import com.cowell.scib.service.SearchService;
import com.cowell.scib.service.dto.NecessaryGoodsDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class NecessaryCompanyGoodsHandler {

    private final Logger log = LoggerFactory.getLogger(NecessaryCompanyGoodsHandler.class);

    @Autowired
    private NecessaryCompanyGoodsExtendMapper necessaryCompanyGoodsExtendMapper;

    @Autowired
    private NecessaryCompanyGoodsMapper necessaryCompanyGoodsMapper;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    public void check(NecessaryGoodsDTO necessaryGoodsDTO) {
        List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = necessaryGoodsDTO.getTaskNecessaryCompanyGoods();
        log.info("企业必备数据为：{}",taskNecessaryCompanyGoods.size());
        if (CollectionUtils.isEmpty(taskNecessaryCompanyGoods)) {
            return;
        }
        try{
            boolean b = taskNecessaryCompanyGoods.get(0).getFinishFlag().equals(1);
            log.info("企业必备finishFlag：{}",b );
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskNecessaryCompanyGoods.get(0).getTaskId());
            if (CollectionUtils.isEmpty(bundlingTaskStoreDetailList)) {
                return;
            }


            List<Long> companyOrgIdList = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getCompanyOrgId).distinct().collect(Collectors.toList());
            List<String> cityList = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getCity).distinct().collect(Collectors.toList());
            //查重
            //todo 删除表加平台Id，企业Id,城市,商品大类、删除  以商品大类为准   11，12，13   1201
            //todo 直接插入

       /* List<String> goodsNoList = taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getGoodsNo).distinct().collect(Collectors.toList());
        Map<String, SpuListVo> spuMap = new HashMap<>();
        Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
*/
            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskNecessaryCompanyGoods.get(0).getTaskId());
            List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
            List<Long> goodsTypeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodCategoryList)) {
                goodsTypeList = goodCategoryList.stream().map(v -> Long.valueOf(v.getPerprotyValue())).collect(Collectors.toList());
            }
            List<Long> collect1 = goodsTypeList.stream().filter(v -> Constants.ZS_STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zsType=false;
            if (CollectionUtils.isNotEmpty(collect1)){
                zsType = true;
            }
            List<Long> zhTypeList = goodsTypeList.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zhType=false;
            if (CollectionUtils.isNotEmpty(zhTypeList)){
                zhType=true;
            }
            //查重
            if (zhType){
                NecessaryCompanyGoodsExample example1 = new NecessaryCompanyGoodsExample();
                NecessaryCompanyGoodsExample.Criteria criteria = example1.createCriteria();
                criteria.andPlatformOrgIdEqualTo(taskNecessaryCompanyGoods.get(0).getPlatformOrgId()).andCategoryIdIn(goodsTypeList).andCompanyOrgIdIn(companyOrgIdList).andCityIn(cityList).andVersionNotEqualTo(taskNecessaryCompanyGoods.get(0).getTaskId());
                List<NecessaryCompanyGoods> necessaryCompanyGoods1 = necessaryCompanyGoodsMapper.selectByExample(example1);
                if (CollectionUtils.isNotEmpty(necessaryCompanyGoods1)) {
                    necessaryCompanyGoodsExtendMapper.batchDel(necessaryCompanyGoods1.stream().map(NecessaryCompanyGoods::getId).collect(Collectors.toList()));
                }
            }


            if (zsType) {
                NecessaryCompanyGoodsExample example = new NecessaryCompanyGoodsExample();
                NecessaryCompanyGoodsExample.Criteria criteria1 = example.createCriteria();
                criteria1.andPlatformOrgIdEqualTo(taskNecessaryCompanyGoods.get(0).getPlatformOrgId()).andCompanyOrgIdIn(companyOrgIdList).andCityIn(cityList).andVersionNotEqualTo(taskNecessaryCompanyGoods.get(0).getTaskId()).andMiddleCategoryIdEqualTo(Constants.ZS_STORE_TYPE_CATEGORY.get(0));
                List<NecessaryCompanyGoods> necessaryCompanyGoods = necessaryCompanyGoodsMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(necessaryCompanyGoods)) {
                    necessaryCompanyGoodsExtendMapper.batchDel(necessaryCompanyGoods.stream().map(NecessaryCompanyGoods::getId).collect(Collectors.toList()));
                }
            }

            ArrayList<NecessaryCompanyGoods> necessaryCompanyGoodsArrayList = new ArrayList<>();
            for (TaskNecessaryCompanyGoods taskNecessaryCompanyGood : taskNecessaryCompanyGoods) {
            /*NecessaryCompanyGoodsExample example2 = new NecessaryCompanyGoodsExample();
            example2.createCriteria().andPlatformOrgIdEqualTo(taskNecessaryCompanyGood.getPlatformOrgId()).andCompanyOrgIdEqualTo(taskNecessaryCompanyGood.getCompanyOrgId()).andCityEqualTo(taskNecessaryCompanyGood.getCity()).andGoodsNoEqualTo(taskNecessaryCompanyGood.getGoodsNo());
            List<NecessaryCompanyGoods> necessaryCompanyGoods3 = necessaryCompanyGoodsMapper.selectByExample(example2);
            if (CollectionUtils.isEmpty(necessaryCompanyGoods3)){*/
                NecessaryCompanyGoods necessaryCompanyGoods2 = new NecessaryCompanyGoods();
                BeanUtils.copyProperties(taskNecessaryCompanyGood, necessaryCompanyGoods2);
                necessaryCompanyGoods2.setGmtCreate(new Date());
                necessaryCompanyGoods2.setGmtUpdate(new Date());
                necessaryCompanyGoods2.setId(null);
                necessaryCompanyGoods2.setVersion(taskNecessaryCompanyGood.getTaskId());
                necessaryCompanyGoodsArrayList.add(necessaryCompanyGoods2);
                // }
            }
            if (CollectionUtils.isNotEmpty(necessaryCompanyGoodsArrayList)) {
                necessaryCompanyGoodsExtendMapper.batchInsert(necessaryCompanyGoodsArrayList);
            }

        }catch (Exception e){
            log.error("处理企业必备数据报错",e);
        }

    }

}
