package com.cowell.scib.service.handle;

import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.TaskNecessaryChooseStoreTypeGoods;
import com.cowell.scib.enums.BudnlTaskDetailDicEnum;
import com.cowell.scib.enums.BundlBoolEnum;
import com.cowell.scib.enums.BundlTaskTypeEnum;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mapperDgms.NecessaryChooseStoreTypeGoodsMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.MdmTaskDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.NecessaryChooseStoreTypeGoodsExtendMapper;
import com.cowell.scib.service.dto.NecessaryGoodsDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class NecessaryChooseStoreTypeGoodsHandler{
    private final Logger log = LoggerFactory.getLogger(NecessaryChooseStoreTypeGoodsHandler.class);

    @Autowired
    private NecessaryChooseStoreTypeGoodsExtendMapper necessaryChooseStoreTypeGoodsExtendMapper;

    @Autowired
    private NecessaryChooseStoreTypeGoodsMapper necessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;


    public void check(NecessaryGoodsDTO necessaryGoodsDTO) {
        List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods = necessaryGoodsDTO.getTaskNecessaryChooseStoreTypeGoods();
        log.info("店型选配必备数据为：{}",taskNecessaryChooseStoreTypeGoods.size());
        if (CollectionUtils.isEmpty(taskNecessaryChooseStoreTypeGoods)) {
            return;
        }
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskNecessaryChooseStoreTypeGoods.get(0).getTaskId());

        try{
            boolean b = taskNecessaryChooseStoreTypeGoods.get(0).getFinishFlag().equals(1);
            log.info("店型选配finishFlag：{}",b );
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskNecessaryChooseStoreTypeGoods.get(0).getTaskId());
            if (CollectionUtils.isEmpty(bundlingTaskStoreDetailList)){
                return;
            }
            List<String> cityList = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getCity).distinct().collect(Collectors.toList());
            List<Long> companyOrgIdList = bundlingTaskStoreDetailList.stream().filter(v->v.getBundlAdviceAble().equals(BundlBoolEnum.YES.getCode())).map(BundlingTaskStoreDetail::getCompanyOrgId).distinct().collect(Collectors.toList());
            List<BundlingTaskStoreDetail> storeTypeLists = bundlingTaskStoreDetailList.stream().filter(v -> v.getBundlConfirmAble().equals(BundlBoolEnum.YES.getCode())).collect(Collectors.toList());

            //List<String> storeTypeList = taskNecessaryChooseStoreTypeGoods.stream().map(TaskNecessaryChooseStoreTypeGoods::getStoreType).distinct().collect(Collectors.toList());

            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskNecessaryChooseStoreTypeGoods.get(0).getTaskId());
            List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
            List<Long> goodsTypeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodCategoryList)) {
                goodsTypeList = goodCategoryList.stream().map(v -> Long.valueOf(v.getPerprotyValue())).collect(Collectors.toList());
            }
            // boolean zsType= goodsTypeList.contains(Constants.ZS_STORE_TYPE_CATEGORY);

            List<Long> collect1 = goodsTypeList.stream().filter(v -> Constants.ZS_STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zsType=false;
            if (CollectionUtils.isNotEmpty(collect1)){
                zsType = true;
            }
            List<Long> zhTypeList = goodsTypeList.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zhType=false;
            if (CollectionUtils.isNotEmpty(zhTypeList)){
                zhType=true;
            }
            log.info("zsType：{}", zsType);
            log.info("zhType：{}", zhType);
            List<String> storeTypeList = new ArrayList();
            List<String> storeTypeTemp = new ArrayList();
            List<String> bundlStoreTypeList = new ArrayList<>();

            if (zhType) {
                List<String> collect = storeTypeLists.stream().map(BundlingTaskStoreDetail::getStoreTypeCode).distinct().collect(Collectors.toList());
                storeTypeList.addAll(collect);
                storeTypeTemp.addAll(collect);
            }
            if (zsType) {
                List<String> collect = storeTypeLists.stream().map(BundlingTaskStoreDetail::getZsStoreTypeCode).distinct().collect(Collectors.toList());
                storeTypeList.addAll(collect);
                storeTypeTemp.addAll(collect);
            }

            if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())) {
                storeTypeList.clear();
                if (zsType) {
                    List<BundlingTaskDetail> zsStoreType = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(zsStoreType)) {
                        List<String> collect = zsStoreType.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
                        bundlStoreTypeList.addAll(collect);
                    }
                }
                if (zhType) {
                    List<BundlingTaskDetail> storeType = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.STORE.getCode())).collect(Collectors.toList());
                    List<String> collect = storeType.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
                    bundlStoreTypeList.addAll(collect);
                }
                if (CollectionUtils.isNotEmpty(bundlStoreTypeList)) {
                    List<String> collect = storeTypeTemp.stream().filter(v -> bundlStoreTypeList.contains(v)).collect(Collectors.toList());
                    storeTypeList.addAll(collect);
                }
            }


            if (CollectionUtils.isEmpty(storeTypeList)){
                return;
            }
            //todo 删除表加平台Id，企业Id,城市,店型,商品大类、删除  以商品大类为准   11，12，13   1201
             if (zhType){
                 NecessaryChooseStoreTypeGoodsExample necessaryChooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
                 NecessaryChooseStoreTypeGoodsExample.Criteria criteria = necessaryChooseStoreTypeGoodsExample.createCriteria();

                 buildChooseStoreTypeGoodsCondition(taskNecessaryChooseStoreTypeGoods, bundlingTaskInfo, cityList, companyOrgIdList, goodsTypeList, storeTypeList, criteria);
                 List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods1 = necessaryChooseStoreTypeGoodsMapper.selectByExample(necessaryChooseStoreTypeGoodsExample);
                 if (CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoods1)) {
                     necessaryChooseStoreTypeGoodsExtendMapper.batchDel(necessaryChooseStoreTypeGoods1.stream().map(NecessaryChooseStoreTypeGoods::getId).collect(Collectors.toList()));
                 }
             }
            if (zsType) {
                NecessaryChooseStoreTypeGoodsExample necessaryChooseStoreTypeGoodsExample1 = new NecessaryChooseStoreTypeGoodsExample();
                NecessaryChooseStoreTypeGoodsExample.Criteria criteria1 = necessaryChooseStoreTypeGoodsExample1.createCriteria();
                buildChooseStoreTypeGoodsCondition(taskNecessaryChooseStoreTypeGoods, bundlingTaskInfo, cityList, companyOrgIdList, goodsTypeList, storeTypeList, criteria1);
                criteria1.andPlatformOrgIdEqualTo(taskNecessaryChooseStoreTypeGoods.get(0).getPlatformOrgId()).andCompanyOrgIdIn(companyOrgIdList)
                        .andCityIn(cityList).andStoreTypeIn(storeTypeList).andVersionNotEqualTo(taskNecessaryChooseStoreTypeGoods.get(0).getTaskId()).andMiddleCategoryIdEqualTo(Constants.ZS_STORE_TYPE_CATEGORY.get(0));
                List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods = necessaryChooseStoreTypeGoodsMapper.selectByExample(necessaryChooseStoreTypeGoodsExample1);
                if (CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoods)){
                    necessaryChooseStoreTypeGoodsExtendMapper.batchDel(necessaryChooseStoreTypeGoods.stream().map(NecessaryChooseStoreTypeGoods::getId).collect(Collectors.toList()));
                }
            }

            ArrayList<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoodsArrayList = new ArrayList<>();
            for (TaskNecessaryChooseStoreTypeGoods taskNecessaryChooseStoreTypeGood : taskNecessaryChooseStoreTypeGoods) {
           /* NecessaryChooseStoreTypeGoodsExample example1 = new NecessaryChooseStoreTypeGoodsExample();
            example1.createCriteria().andPlatformOrgIdEqualTo(taskNecessaryChooseStoreTypeGood.getPlatformOrgId()).andCompanyOrgIdEqualTo(taskNecessaryChooseStoreTypeGood.getCompanyOrgId())
                    .andCityEqualTo(taskNecessaryChooseStoreTypeGood.getCity()).andStoreTypeEqualTo(taskNecessaryChooseStoreTypeGood.getStoreType()).andGoodsNoEqualTo(taskNecessaryChooseStoreTypeGood.getGoodsNo());
            List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods3 = necessaryChooseStoreTypeGoodsMapper.selectByExample(example1);
            if (CollectionUtils.isEmpty(necessaryChooseStoreTypeGoods3)){*/
                NecessaryChooseStoreTypeGoods necessaryChooseStoreTypeGoods2 = new NecessaryChooseStoreTypeGoods();
                BeanUtils.copyProperties(taskNecessaryChooseStoreTypeGood,necessaryChooseStoreTypeGoods2);
                necessaryChooseStoreTypeGoods2.setGmtCreate(new Date());
                necessaryChooseStoreTypeGoods2.setGmtUpdate(new Date());
                necessaryChooseStoreTypeGoods2.setVersion(taskNecessaryChooseStoreTypeGood.getTaskId());
                necessaryChooseStoreTypeGoods2.setId(null);
                necessaryChooseStoreTypeGoodsArrayList.add(necessaryChooseStoreTypeGoods2);
                // }
            }
            if (CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoodsArrayList)){
                necessaryChooseStoreTypeGoodsExtendMapper.batchInsert(necessaryChooseStoreTypeGoodsArrayList);
            }
        }catch (Exception e){
            log.error("处理店型必备数据出错",e);
        }

    }

    private void buildChooseStoreTypeGoodsCondition(List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods, BundlingTaskInfo bundlingTaskInfo, List<String> cityList, List<Long> companyOrgIdList, List<Long> goodsTypeList, List<String> storeTypeList, NecessaryChooseStoreTypeGoodsExample.Criteria criteria) {
        if (BundlTaskTypeEnum.PLATE_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType()) || BundlTaskTypeEnum.BUSINESS_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())) {
            //todo 如果是全层级组货或者是企业级组货  删除表平台Id，企业Id,城市,商品大类、删除  以商品大类为准 11，12，13   1201
            criteria.andPlatformOrgIdEqualTo(taskNecessaryChooseStoreTypeGoods.get(0).getPlatformOrgId()).andCompanyOrgIdIn(companyOrgIdList)
                    .andCityIn(cityList).andVersionNotEqualTo(taskNecessaryChooseStoreTypeGoods.get(0).getTaskId()).andCategoryIdIn(goodsTypeList);
        } else {
            //todo 如果是店型级组货   删除表加平台Id，企业Id,城市,店型,商品大类、删除  以商品大类为准   11，12，13   1201
            criteria.andPlatformOrgIdEqualTo(taskNecessaryChooseStoreTypeGoods.get(0).getPlatformOrgId()).andCompanyOrgIdIn(companyOrgIdList)
                    .andCityIn(cityList).andStoreTypeIn(storeTypeList).andVersionNotEqualTo(taskNecessaryChooseStoreTypeGoods.get(0).getTaskId()).andCategoryIdIn(goodsTypeList);
        }
    }

}
