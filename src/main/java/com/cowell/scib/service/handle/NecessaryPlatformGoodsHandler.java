package com.cowell.scib.service.handle;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.BundlingTaskDetail;
import com.cowell.scib.entityDgms.NecessaryPlatformGoods;
import com.cowell.scib.entityDgms.NecessaryPlatformGoodsExample;
import com.cowell.scib.entityTidb.TaskNecessaryPlatformGoods;
import com.cowell.scib.enums.BudnlTaskDetailDicEnum;
import com.cowell.scib.mapperDgms.NecessaryPlatformGoodsMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.NecessaryPlatformGoodsExtendMapper;
import com.cowell.scib.service.dto.NecessaryGoodsDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NecessaryPlatformGoodsHandler {
    private final Logger log = LoggerFactory.getLogger(NecessaryPlatformGoodsHandler.class);

    @Autowired
    private NecessaryPlatformGoodsMapper necessaryPlatformGoodsMapper;
    @Autowired
    private NecessaryPlatformGoodsExtendMapper necessaryPlatformGoodsExtendMapper;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;


    public void check(NecessaryGoodsDTO necessaryGoodsDTO) {
        List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = necessaryGoodsDTO.getTaskNecessaryPlatformGoods();
        log.info("平台必备数据为：{}",taskNecessaryPlatformGoods.size());
        if (CollectionUtils.isEmpty(taskNecessaryPlatformGoods)) {
            return;
        }
        try {
            boolean b = taskNecessaryPlatformGoods.get(0).getFinishFlag().equals(1);
            log.info("平台必备finishFlag：{}",b );

            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskNecessaryPlatformGoods.get(0).getTaskId());
            List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
            List<Long> goodsTypeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodCategoryList)) {
                goodsTypeList = goodCategoryList.stream().map(v -> Long.valueOf(v.getPerprotyValue())).collect(Collectors.toList());
            }

            List<Long> collect1 = goodsTypeList.stream().filter(v -> Constants.ZS_STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zsType=false;
            if (CollectionUtils.isNotEmpty(collect1)){
                zsType = true;
            }
            List<Long> zhTypeList = goodsTypeList.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zhType=false;
            if (CollectionUtils.isNotEmpty(zhTypeList)){
                zhType=true;
            }
            //查重
            //todo 删除表   查商品大类   加平台Id,商品大类、删除   11，12，13   1201
            if (zsType) {
                NecessaryPlatformGoodsExample necessaryPlatformGoodsExample = new NecessaryPlatformGoodsExample();
                NecessaryPlatformGoodsExample.Criteria criteria = necessaryPlatformGoodsExample.createCriteria();
                criteria.andPlatformOrgIdEqualTo(taskNecessaryPlatformGoods.get(0).getPlatformOrgId()).andVersionNotEqualTo(taskNecessaryPlatformGoods.get(0).getTaskId()).andMiddleCategoryIdEqualTo(Constants.ZS_STORE_TYPE_CATEGORY.get(0));
                List<NecessaryPlatformGoods> necessaryPlatformGoods1 = necessaryPlatformGoodsMapper.selectByExample(necessaryPlatformGoodsExample);
                if (CollectionUtils.isNotEmpty(necessaryPlatformGoods1)) {
                    necessaryPlatformGoodsExtendMapper.batchDel(necessaryPlatformGoods1.stream().map(NecessaryPlatformGoods::getId).collect(Collectors.toList()));

                }
            }
            if (zhType) {
                NecessaryPlatformGoodsExample necessaryPlatformGoodsExample1 = new NecessaryPlatformGoodsExample();
                NecessaryPlatformGoodsExample.Criteria criteria1 = necessaryPlatformGoodsExample1.createCriteria();
                criteria1.andPlatformOrgIdEqualTo(taskNecessaryPlatformGoods.get(0).getPlatformOrgId()).andVersionNotEqualTo(taskNecessaryPlatformGoods.get(0).getTaskId()).andCategoryIdIn(goodsTypeList);
                List<NecessaryPlatformGoods> necessaryPlatformGoods2 = necessaryPlatformGoodsMapper.selectByExample(necessaryPlatformGoodsExample1);
                if (CollectionUtils.isNotEmpty(necessaryPlatformGoods2)) {
                    necessaryPlatformGoodsExtendMapper.batchDel(necessaryPlatformGoods2.stream().map(NecessaryPlatformGoods::getId).collect(Collectors.toList()));

                }
            }

            ArrayList<NecessaryPlatformGoods> necessaryPlatformGoodsArrayList = new ArrayList<>();
            for (TaskNecessaryPlatformGoods taskNecessaryPlatformGood : taskNecessaryPlatformGoods) {
           /* NecessaryPlatformGoodsExample example1 = new NecessaryPlatformGoodsExample();
            example1.createCriteria().andPlatformOrgIdEqualTo(taskNecessaryPlatformGood.getPlatformOrgId()).andStoreTypeEqualTo(taskNecessaryPlatformGood.getGoodsNo());
            List<NecessaryPlatformGoods> necessaryPlatformGoods3 = necessaryPlatformGoodsMapper.selectByExample(example1);
            if (CollectionUtils.isEmpty(necessaryPlatformGoods3)){*/
                NecessaryPlatformGoods necessaryPlatformGoods = new NecessaryPlatformGoods();
                BeanUtils.copyProperties(taskNecessaryPlatformGood, necessaryPlatformGoods);
                necessaryPlatformGoods.setGmtCreate(new Date());
                necessaryPlatformGoods.setGmtUpdate(new Date());
                necessaryPlatformGoods.setId(null);
                necessaryPlatformGoods.setVersion(taskNecessaryPlatformGood.getTaskId());
                necessaryPlatformGoodsArrayList.add(necessaryPlatformGoods);
                // }
            }
            if (CollectionUtils.isNotEmpty(necessaryPlatformGoodsArrayList)) {
                necessaryPlatformGoodsExtendMapper.batchInsert(necessaryPlatformGoodsArrayList);
            }
        } catch (Exception e) {
            log.error("处理平台数据报错", e);
        }
    }
}
