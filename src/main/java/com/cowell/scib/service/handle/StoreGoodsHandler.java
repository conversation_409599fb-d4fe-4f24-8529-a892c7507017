package com.cowell.scib.service.handle;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.TrackRetultAllDetail;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mapperDgms.StoreGoodsInfoMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.MdmTaskDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.StoreGoodsInfoExtendMapper;
import com.cowell.scib.mapperTidb.extend.TrackRetultAllDetailExtendMapper;
import com.cowell.scib.service.NecessaryContentsService;
import com.cowell.scib.service.SearchService;
import com.cowell.scib.service.StoreService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.vo.SpuListVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class StoreGoodsHandler{
    private final Logger log = LoggerFactory.getLogger(StoreGoodsHandler.class);

    @Autowired
    private StoreGoodsInfoExtendMapper storeGoodsInfoExtendMapper;

    @Autowired
    private StoreGoodsInfoMapper storeGoodsInfoMapper;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SearchService searchService;

    @Autowired
    private NecessaryContentsService necessaryContentsService;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;

    @Autowired
    private TrackRetultAllDetailExtendMapper trackRetultAllDetailExtendMapper;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;

    @Autowired
    private StoreService storeService;

    public void check(NecessaryGoodsDTO necessaryGoodsDTO) {

        List<PushStoreMdmDTO> pushStoreMdmDTOList = necessaryGoodsDTO.getPushStoreMdmDTOList();
        log.info("一点一目必备修改数据：{}",pushStoreMdmDTOList.size());
        if (CollectionUtils.isEmpty(pushStoreMdmDTOList)) {
            return;
        }
        /**
         * 从表2中取出一行，用门店和商品在表1中查，查不到做新增，例如表2中的红色字体的3行属于这种情况				门店A，商品D2，店型选配，有效
         * 查得到，继续对比必备层级，如果必备层级相同且有效性一致（有为有效或者都为无效），不做操作，例如表2中的，商品B2和C1。
         * 如果必备层级相同，但是有效性不同（表1有效，表2无效；或者表1无效，表2有效），用表2的有效性更新表1。
         * 如果必备层级不相同，则更新表1=表2的必备层级，例如表2中的，商品B1，应该将表1改为企业必备。有效性跟随表2的值。

         * 在处理过程中，记录新增行，变更行（必备层级变更和有效性变更），删除行，作为下发MDM的taskDetail。 其中，有效性变更可以再分解为新增和删除：有效变无效为删除必备，无效变有效为新增必备。
         * 上述过程需要在内存中对比完成，将每行的处理结果记录好，再分批次发到MQ执行。
         */
        try {
            List<String> goodsNoList = pushStoreMdmDTOList.stream().map(PushStoreMdmDTO::getGoodsId).distinct().collect(Collectors.toList());
            Map<String, SpuListVo> spuMap = new HashMap<>();
            Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
            try {
                Thread.sleep(10L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            MdmTaskExample mdmTaskExample = new MdmTaskExample();
            mdmTaskExample.createCriteria().andBundlingTaskIdEqualTo(pushStoreMdmDTOList.get(0).getTaskId());
            List<MdmTask> mdmTasks = mdmTaskMapper.selectByExample(mdmTaskExample);
            MdmTask mdmTask = mdmTasks.get(0);
            ArrayList<StoreGoodsInfo> storeGoodsInfoList = new ArrayList<>();

            ArrayList<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();


            Optional<MdmStoreExDTO> storeExtInfoBySapCode = CacheVar.getStoreExtInfoBySapCode(pushStoreMdmDTOList.get(0).getOrgNo());
            MdmStoreExDTO mdmStoreExDTO = new MdmStoreExDTO();
            if (!storeExtInfoBySapCode.isPresent()) {
                List<MdmStoreBaseDTO> storeByStoreNos = storeService.findStoreByStoreNos(Arrays.asList(pushStoreMdmDTOList.get(0).getOrgNo()));
                if (CollectionUtils.isEmpty(storeByStoreNos)) {
                    log.error("查询不到:{}门店信息", pushStoreMdmDTOList.get(0).getOrgNo());
                    return;
                }
                BeanUtils.copyProperties(storeByStoreNos.get(0), mdmStoreExDTO);
            } else {
                mdmStoreExDTO = storeExtInfoBySapCode.get();
            }
            //查询出的一点一目数据带集团必备

            StoreGoodsInfoExample example = new StoreGoodsInfoExample();
            example.createCriteria().andStoreIdEqualTo(mdmStoreExDTO.getStoreId()).andGoodsNoIn(goodsNoList);
            List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoMapper.selectByExample(example);
            //已存在的门店商品层级信息 map
            Map<String, StoreGoodsInfo> storeGoodsInfoMap = storeGoodsInfos.stream().collect(Collectors.toMap(v -> v.getGoodsNo(), Function.identity(), (k1, k2) -> k1));
            //查重
            List<Long> storeGoodsIds = necessaryContentsService.getStoreGoodsIds(pushStoreMdmDTOList.size()+1);
            List<Long> storeGoodsIds1 = necessaryContentsService.getMdmTaskDetailIds(pushStoreMdmDTOList.size() + 1);

            List<StoreGoodNecessaryTagDTO> storeGoodNecessaryTagDTOS = new ArrayList<>();
            List<StoreGoodEffectStatusDTO> storeGoodEffectStatusDTOS = new ArrayList<>();
            Long storeId= mdmStoreExDTO.getStoreId();
            for (int i = 0; i < pushStoreMdmDTOList.size(); i++) {
                PushStoreMdmDTO pushStoreMdmDTO = pushStoreMdmDTOList.get(i);
                StoreGoodsInfo storeGoodsInfo = storeGoodsInfoMap.get(pushStoreMdmDTO.getGoodsId());
                if (Objects.nonNull(storeGoodsInfo)) {
                    //查得到，继续对比必备层级，如果必备层级相同且有效性一致（有为有效或者都为无效），不做操作，例如表2中的，商品B2和C1。
                    if (pushStoreMdmDTO.getLevel().equals(String.valueOf(storeGoodsInfo.getNecessaryTag())) && pushStoreMdmDTO.getEffectStatus().equals(storeGoodsInfo.getEffectStatus())) {
                       //log.info("跳过 门店={} 商品={}  necessaryTag={} EffectStatus={}",storeGoodsInfo.getStoreCode(),storeGoodsInfo.getGoodsNo(),pushStoreMdmDTO.getLevel(),pushStoreMdmDTO.getEffectStatus());
                        continue;
                    }else {
                        log.info("不一致 门店={} 商品={}  necessaryTag={} 对比 {}   EffectStatus={} 对比 {}",storeGoodsInfo.getStoreCode(),storeGoodsInfo.getGoodsNo(),pushStoreMdmDTO.getLevel(),storeGoodsInfo.getNecessaryTag(),pushStoreMdmDTO.getEffectStatus(),storeGoodsInfo.getEffectStatus());
                    }
                    //如果必备层级不相同，则更新表1=表2的必备层级，例如表2中的，商品B1，应该将表1改为企业必备。有效性跟随表2的值。
                    if (!pushStoreMdmDTO.getLevel().equals(String.valueOf(storeGoodsInfo.getNecessaryTag()))) {
                        StoreGoodNecessaryTagDTO storeGoodNecessaryTagDTO = new StoreGoodNecessaryTagDTO();
                        storeGoodNecessaryTagDTO.setNecessaryTag(Byte.valueOf(pushStoreMdmDTO.getLevel()));
                        storeGoodNecessaryTagDTO.setEffectStatus(pushStoreMdmDTO.getEffectStatus());
                        storeGoodNecessaryTagDTO.setUpdatedBy(pushStoreMdmDTO.getUserId());
                        storeGoodNecessaryTagDTO.setUpdatedName(pushStoreMdmDTO.getUserName());
                        storeGoodNecessaryTagDTO.setMinDisplayQuantity(BigDecimal.ONE);
                        storeGoodNecessaryTagDTO.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
                        storeGoodNecessaryTagDTO.setId(storeGoodsInfo.getId());
                        storeGoodNecessaryTagDTOS.add(storeGoodNecessaryTagDTO);

                    } else if (!pushStoreMdmDTO.getEffectStatus().equals(storeGoodsInfo.getEffectStatus())){
                        //有效性不同的时处理
                        StoreGoodEffectStatusDTO storeGoodEffectStatusDTO = new StoreGoodEffectStatusDTO();
                        storeGoodEffectStatusDTO.setId(storeGoodsInfo.getId());
                        storeGoodEffectStatusDTO.setEffectStatus(pushStoreMdmDTO.getEffectStatus());
                        storeGoodEffectStatusDTO.setUpdatedName(pushStoreMdmDTO.getUserName());
                        storeGoodEffectStatusDTO.setUpdatedBy(pushStoreMdmDTO.getUserId());
                        if (pushStoreMdmDTO.getEffectStatus().equals(StoreGoodsEffectStatusEnum.YES.getCode())){
                            storeGoodEffectStatusDTO.setMinDisplayQuantity(BigDecimal.ONE);
                        }else {
                            storeGoodEffectStatusDTO.setMinDisplayQuantity(BigDecimal.ZERO);
                        }
                        storeGoodEffectStatusDTO.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
                        storeGoodEffectStatusDTOS.add(storeGoodEffectStatusDTO);
                    }
                    MdmTaskDetail mdmTaskDetail = buildMdmTaskDetail(pushStoreMdmDTO, mdmStoreExDTO, mdmTask.getId(), spuMap,i,storeGoodsIds1);
                    //log.info("不一致 门店={} 商品={} add to mdmTaskDetail ={}",storeGoodsInfo.getStoreCode(),storeGoodsInfo.getGoodsNo(),mdmTaskDetail);
                    mdmTaskDetails.add(mdmTaskDetail);
                }else  {
                    //不存在就 build 一个新的 批量新增
                    storeGoodsInfo = getStoreGoodsInfo(pushStoreMdmDTO, mdmStoreExDTO,i,storeGoodsIds);
                    if (Objects.nonNull(storeGoodsInfo)){
                        storeGoodsInfoList.add(storeGoodsInfo);
                        MdmTaskDetail mdmTaskDetail = buildMdmTaskDetail(pushStoreMdmDTO, mdmStoreExDTO, mdmTask.getId(),spuMap,i,storeGoodsIds1);
                        mdmTaskDetails.add(mdmTaskDetail);
                    }
                }
            }
            /**
             * 处理完成后，查询在表1中但是没在表2中的门店和商品，删除表1这些行，例如商品A1，A2，A3。按商品大类和必备层级做过滤查马东表
             *  处理完成后形成新的表1，如右图。
             */
            if (CollectionUtils.isNotEmpty(storeGoodNecessaryTagDTOS)){
                storeGoodsInfoExtendMapper.batchUpdateNecessaryTag(storeGoodNecessaryTagDTOS,storeId);
            }
            if (CollectionUtils.isNotEmpty(storeGoodEffectStatusDTOS)){
                storeGoodsInfoExtendMapper.batchUpdateEffectStatus(storeGoodEffectStatusDTOS,storeId);
            }

            deleteExsistStoreGoodsInfoAndAddToTaskDetail(pushStoreMdmDTOList, spuMap, mdmTask, storeGoodsInfoList, mdmTaskDetails, mdmStoreExDTO);

            if(CollectionUtils.isNotEmpty(storeGoodsInfoList)){
                storeGoodsInfoExtendMapper.batchInsert(storeGoodsInfoList);
            }
            if (CollectionUtils.isNotEmpty(mdmTaskDetails)){
                mdmTaskDetailExtendMapper.batchInsert(mdmTaskDetails);
            }
            //necessaryContentsService.pushMdm(mdmTaskDetails);
        } catch (Exception e) {
            log.error("下发处理一店一目数据失败", e);
        }
    }

    /**
     * 按 taskId  + storeNo  删除原一店一目 已有 本次组货结果没有的 明细数据
     * @param pushStoreMdmDTOList
     * @param spuMap
     * @param mdmTask
     * @param storeGoodsInfoList
     * @param mdmTaskDetails
     * @param mdmStoreExDTO
     */
    private void deleteExsistStoreGoodsInfoAndAddToTaskDetail(List<PushStoreMdmDTO> pushStoreMdmDTOList, Map<String, SpuListVo> spuMap, MdmTask mdmTask, ArrayList<StoreGoodsInfo> storeGoodsInfoList, ArrayList<MdmTaskDetail> mdmTaskDetails, MdmStoreExDTO mdmStoreExDTO) {
        Long taskId = pushStoreMdmDTOList.get(0).getTaskId();
        String orgNo = pushStoreMdmDTOList.get(0).getOrgNo();
        String redisKey = RedisConstant.NECESSARY_TASK_STORE_DEL_EXIST_CACHE_KEY + taskId+"_"+orgNo;
        RAtomicLong atomicLong = redissonClient.getAtomicLong(redisKey);
        long count = atomicLong.incrementAndGet();
        if(count>1){
            return;
        }
        atomicLong.expire(24, TimeUnit.HOURS);
        log.info("deleteExsistStoreGoodsInfoAndAddToTaskDetail 首次执行 taskId{} orgNo={}", taskId,orgNo);
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(pushStoreMdmDTOList.get(0).getTaskId());
        List<Byte> levelList = new ArrayList<>();
        if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
            levelList = Arrays.asList(NecessaryTagEnum.PLATFORM_NECESSARY.getCode(), NecessaryTagEnum.COMPANY_NECESSARY.getCode(), NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode(),
                    NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode(), NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
        } else if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())) {
            levelList = Arrays.asList(NecessaryTagEnum.COMPANY_NECESSARY.getCode(), NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode(),
                    NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode(), NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
        } else {
            levelList = Arrays.asList(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode(), NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode(), NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
        }
        TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
        trackRetultDetailParam.setTaskId(taskId);
        //trackRetultDetailParam.setPlatOrgid(String.valueOf(pushStoreMdmDTOList.get(0).get()));
        trackRetultDetailParam.setOrgNoList(Arrays.asList(orgNo));
        trackRetultDetailParam.setLevelList(levelList.stream().map(v->String.valueOf(v)).collect(Collectors.toList()));
        trackRetultDetailParam.setStatus(Constants.NORMAL_STATUS);
        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
        List<String> bdpGoodsNoList = trackRetultAllDetails.stream().map(TrackRetultAllDetail::getGoodsId).distinct().collect(Collectors.toList());
        log.info("获取大数据推送的商品goodsNo：{}",bdpGoodsNoList);
        List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskId);
        List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
        List<Long> goodsTypeList = goodCategoryList.stream().map(v -> Long.valueOf(v.getPerprotyValue())).collect(Collectors.toList());
        List<String> goodsTypeLists = goodCategoryList.stream().map(v -> v.getPerprotyValue()).collect(Collectors.toList());

        List<Long> collect1 = goodsTypeList.stream().filter(v ->v.equals(Constants.ZS_STORE_TYPE_CATEGORY.get(0))).collect(Collectors.toList());

        boolean zsType=false;
        if (CollectionUtils.isNotEmpty(collect1)){
            zsType = true;
        }
        log.info("一店一目zsType：{}", zsType);
        List<Long> zhTypeList = goodsTypeList.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
        boolean zhType = false;
        if (CollectionUtils.isNotEmpty(zhTypeList)) {
            zhType = true;
        }
        log.info("一店一目zhType：{}", zhType);
        //查询大数据回传的一店一目数据（未删除，经营属性正常的） 不在一店一目表中的数据
        StoreGoodsInfoExample example1 = new StoreGoodsInfoExample();
        example1.createCriteria().andStoreIdEqualTo(mdmStoreExDTO.getStoreId()).andGoodsNoNotIn(bdpGoodsNoList).andNecessaryTagIn(levelList);
        List<StoreGoodsInfo> storeGoodsInfos1 = storeGoodsInfoMapper.selectByExample(example1);
        List<StoreGoodsInfo> storeGoodsInfosList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storeGoodsInfos1)){
            if (zhType){
                List<StoreGoodsInfo> zhStoreGoodsInfoList = storeGoodsInfos1.stream().filter(v -> goodsTypeLists.contains(v.getSubCategoryId().toString().substring(0, 2))).collect(Collectors.toList());

                storeGoodsInfosList.addAll(zhStoreGoodsInfoList);
            }
            if (zsType){
                List<StoreGoodsInfo> zsStoreGoodsInfoList = storeGoodsInfos1.stream().filter(v -> goodsTypeLists.contains(v.getSubCategoryId().toString().substring(0, 4))).collect(Collectors.toList());

                storeGoodsInfosList.addAll(zsStoreGoodsInfoList);
            }
            List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(storeGoodsInfosList.size() + 1);
            for (int i = 0; i < storeGoodsInfosList.size(); i++) {
                StoreGoodsInfo storeGoodsInfo = storeGoodsInfosList.get(i);
                PushStoreMdmDTO pushStoreMdmDTO = new PushStoreMdmDTO();
                pushStoreMdmDTO.setUserId(pushStoreMdmDTOList.get(0).getUserId());
                pushStoreMdmDTO.setUserName(pushStoreMdmDTOList.get(0).getUserName());
                pushStoreMdmDTO.setGoodsId(storeGoodsInfo.getGoodsNo());
                pushStoreMdmDTO.setEffectStatus(storeGoodsInfo.getEffectStatus());
                pushStoreMdmDTO.setLevel(String.valueOf(NecessaryTagEnum.NONE_NECESSARY.getCode()));
                pushStoreMdmDTO.setSubCategoryId(String.valueOf(storeGoodsInfo.getSubCategoryId()));
                pushStoreMdmDTO.setOrgNo(storeGoodsInfo.getStoreCode());
                pushStoreMdmDTO.setTaskId(pushStoreMdmDTOList.get(0).getTaskId());
                MdmTaskDetail mdmTaskDetail = buildMdmTaskDetail(pushStoreMdmDTO, mdmStoreExDTO, mdmTask.getId(), spuMap, i, mdmTaskDetailIds);
                mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
                mdmTaskDetails.add(mdmTaskDetail);
            }
            if (CollectionUtils.isNotEmpty(storeGoodsInfosList)){
                log.info("开始删除一店一目数据:{}",storeGoodsInfosList.size());
                List<List<StoreGoodsInfo>> partition = Lists.partition(storeGoodsInfosList, Constants.FEIGN_ONCE_QUERY_MAX);
                for (List<StoreGoodsInfo> storeGoodsInfosDel : partition) {
                    storeGoodsInfoExtendMapper.batchDel(storeGoodsInfosDel.stream().map(StoreGoodsInfo::getId).collect(Collectors.toList()), mdmStoreExDTO.getStoreId(),pushStoreMdmDTOList.get(0).getUserName());
                }
            }
        }

    }

    private StoreGoodsInfo getStoreGoodsInfo(PushStoreMdmDTO pushStoreMdmDTO, MdmStoreExDTO mdmStoreExDTO,int i, List<Long> storeGoodsIds) {
        StoreGoodsInfo storeGoodsInfo = new StoreGoodsInfo();
        Optional<OrgInfoBaseCache> storeByStoreId = CacheVar.getStoreBySapCode(mdmStoreExDTO.getStoreNo());
        if (!storeByStoreId.isPresent()){
            return null;
        }
        OrgInfoBaseCache orgInfoBaseCache = storeByStoreId.get();
        storeGoodsInfo.setStoreOrgId(orgInfoBaseCache.getId() == null ? 0: orgInfoBaseCache.getId());
        storeGoodsInfo.setId(storeGoodsIds.get(i));
        storeGoodsInfo.setStoreCode(mdmStoreExDTO.getStoreNo());
        storeGoodsInfo.setSubCategoryId(StringUtils.isNotBlank(pushStoreMdmDTO.getSubCategoryId())?Long.valueOf(pushStoreMdmDTO.getSubCategoryId()):0);
        storeGoodsInfo.setStoreId(mdmStoreExDTO.getStoreId());
        storeGoodsInfo.setGoodsNo(pushStoreMdmDTO.getGoodsId());
        storeGoodsInfo.setNecessaryTag(Byte.valueOf(pushStoreMdmDTO.getLevel()));
        storeGoodsInfo.setEffectStatus(pushStoreMdmDTO.getEffectStatus());
        storeGoodsInfo.setStatus(Constants.NO_PUSH_MDM_STATUS);
        storeGoodsInfo.setUpdatedBy(pushStoreMdmDTO.getUserId());
        storeGoodsInfo.setUpdatedName(pushStoreMdmDTO.getUserName());
        storeGoodsInfo.setGmtUpdate(new Date());
        storeGoodsInfo.setMinDisplayQuantity(new BigDecimal(1));
        return storeGoodsInfo;

    }

    private MdmTaskDetail buildMdmTaskDetail(PushStoreMdmDTO pushStoreMdmDTO, MdmStoreExDTO mdmStoreExDTO,Long mdmTaskId,Map<String, SpuListVo> spuMap,int i, List<Long> storeGoodsIds1) {
        MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
        mdmTaskDetail.setId(storeGoodsIds1.get(i));

        mdmTaskDetail.setTaskId(mdmTaskId);
        mdmTaskDetail.setGoodsNo(pushStoreMdmDTO.getGoodsId());
        if (MapUtils.isNotEmpty(spuMap) && Objects.nonNull(spuMap.get(pushStoreMdmDTO.getGoodsId()))){
            SpuListVo spuListVo = spuMap.get(pushStoreMdmDTO.getGoodsId());
            mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
            mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
            mdmTaskDetail.setDescription(spuListVo.getDescription());
            mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
            mdmTaskDetail.setBarCode(spuListVo.getBarCode());
            mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
            mdmTaskDetail.setGoodsName(spuListVo.getName());
            mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
            mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
        }
        mdmTaskDetail.setUpdatedBy(pushStoreMdmDTO.getUserId());
        mdmTaskDetail.setUpdatedName(pushStoreMdmDTO.getUserName());
        mdmTaskDetail.setStoreOrgId(mdmStoreExDTO.getStoreOrgId() == null ? 0 : mdmStoreExDTO.getStoreOrgId());
        mdmTaskDetail.setStoreId(mdmStoreExDTO.getStoreId());
        mdmTaskDetail.setStoreCode(mdmStoreExDTO.getStoreNo());
        mdmTaskDetail.setStoreName(mdmStoreExDTO.getStoreName());
        mdmTaskDetail.setNecessaryTag(Integer.valueOf(pushStoreMdmDTO.getLevel()));
        mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ONE);
        mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.NO_PUSH.getCode());
        mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
        mdmTaskDetail.setGmtUpdate(new Date());
        return mdmTaskDetail;
    }

}
