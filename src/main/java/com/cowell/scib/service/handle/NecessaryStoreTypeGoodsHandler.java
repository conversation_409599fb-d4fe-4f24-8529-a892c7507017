package com.cowell.scib.service.handle;

import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.TaskNecessaryStoreTypeGoods;
import com.cowell.scib.enums.BudnlTaskDetailDicEnum;
import com.cowell.scib.enums.BundlBoolEnum;
import com.cowell.scib.enums.BundlTaskTypeEnum;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.NecessaryStoreTypeGoodsMapper;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.service.dto.NecessaryGoodsDTO;
import com.cowell.scib.utils.JacksonUtil;
import com.google.common.collect.Lists;
import groovy.util.IFileNameFinder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class NecessaryStoreTypeGoodsHandler {

    private final Logger log = LoggerFactory.getLogger(NecessaryStoreTypeGoodsHandler.class);

    @Autowired
    private NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;

    @Autowired
    private NecessaryStoreTypeGoodsExtendMapper necessaryStoreTypeGoodsExtendMapper;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;


    public void check(NecessaryGoodsDTO necessaryGoodsDTO) {
        List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods = necessaryGoodsDTO.getTaskNecessaryStoreTypeGoods();
        log.info("店型必备数据：{}",taskNecessaryStoreTypeGoods.size());
        if (CollectionUtils.isEmpty(taskNecessaryStoreTypeGoods)) {
            return;
        }

        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskNecessaryStoreTypeGoods.get(0).getTaskId());


        try {
            boolean b = taskNecessaryStoreTypeGoods.get(0).getFinishFlag().equals(1);
            log.info("店型必备finishFlag：{}", b);
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskNecessaryStoreTypeGoods.get(0).getTaskId());
            if (CollectionUtils.isEmpty(bundlingTaskStoreDetailList)) {
                return;
            }
            //查重
            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskNecessaryStoreTypeGoods.get(0).getTaskId());
            List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());

            List<BundlingTaskStoreDetail> storeTypeLists = bundlingTaskStoreDetailList.stream().filter(v -> v.getBundlConfirmAble().equals(BundlBoolEnum.YES.getCode())).collect(Collectors.toList());


            List<Long> goodsTypeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodCategoryList)) {
                goodsTypeList = goodCategoryList.stream().map(v -> Long.valueOf(v.getPerprotyValue())).collect(Collectors.toList());
            }


            List<String> cityList = storeTypeLists.stream().map(BundlingTaskStoreDetail::getCity).distinct().collect(Collectors.toList());

            List<Long> companyOrgIdList = storeTypeLists.stream().map(BundlingTaskStoreDetail::getCompanyOrgId).distinct().collect(Collectors.toList());

            List<Long> collect1 = goodsTypeList.stream().filter(v ->v.equals(Constants.ZS_STORE_TYPE_CATEGORY.get(0))).collect(Collectors.toList());

            boolean zsType=false;
            if (CollectionUtils.isNotEmpty(collect1)){
                 zsType = true;
            }
            log.info("zsType：{}", zsType);
            List<Long> zhTypeList = goodsTypeList.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v)).collect(Collectors.toList());
            boolean zhType = false;
            if (CollectionUtils.isNotEmpty(zhTypeList)) {
                zhType = true;
            }

            log.info("zhType：{}", zhType);
            List<String> storeTypeList = new ArrayList();
            List<String> storeTypeTemp = new ArrayList();
            List<String> bundlStoreTypeList = new ArrayList<>();

            if (zhType) {
                List<String> collect = storeTypeLists.stream().map(BundlingTaskStoreDetail::getStoreTypeCode).distinct().collect(Collectors.toList());

                storeTypeList.addAll(collect);
                storeTypeTemp.addAll(collect);
            }
            if (zsType) {
                List<String> collect = storeTypeLists.stream().map(BundlingTaskStoreDetail::getZsStoreTypeCode).distinct().collect(Collectors.toList());

                storeTypeList.addAll(collect);
                storeTypeTemp.addAll(collect);
            }

            if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())) {
                storeTypeList.clear();
                if (zsType) {
                    List<BundlingTaskDetail> zsStoreType = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(zsStoreType)) {
                        List<String> collect = zsStoreType.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());

                        bundlStoreTypeList.addAll(collect);
                    }
                }
                if (zhType) {
                    List<BundlingTaskDetail> storeType = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.STORE.getCode())).collect(Collectors.toList());
                    List<String> collect = storeType.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());

                    bundlStoreTypeList.addAll(collect);
                }
                if (CollectionUtils.isNotEmpty(bundlStoreTypeList)) {
                    List<String> collect = storeTypeTemp.stream().filter(v -> bundlStoreTypeList.contains(v)).collect(Collectors.toList());

                    storeTypeList.addAll(collect);
                }
            }

            if (CollectionUtils.isEmpty(storeTypeList)) {
                return;
            }

            //todo 删除表加平台Id，企业Id,城市,店型,商品大类、删除  以商品大类为准   11，12，13   1201
            if (zhType) {
                NecessaryStoreTypeGoodsExample necessaryStoreTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
                NecessaryStoreTypeGoodsExample.Criteria criteria = necessaryStoreTypeGoodsExample.createCriteria();
                buildStoreTypeGoodsCondition(taskNecessaryStoreTypeGoods, bundlingTaskInfo, goodsTypeList, cityList, companyOrgIdList, storeTypeList, criteria);
                List<NecessaryStoreTypeGoods> necessaryStoreTypeGoods = necessaryStoreTypeGoodsMapper.selectByExample(necessaryStoreTypeGoodsExample);
                if (CollectionUtils.isNotEmpty(necessaryStoreTypeGoods)) {
                    necessaryStoreTypeGoodsExtendMapper.batchDel(necessaryStoreTypeGoods.stream().map(NecessaryStoreTypeGoods::getId).collect(Collectors.toList()));
                }
            }


            if (zsType) {
                NecessaryStoreTypeGoodsExample necessaryStoreTypeGoodsExample1 = new NecessaryStoreTypeGoodsExample();
                NecessaryStoreTypeGoodsExample.Criteria criteria1 = necessaryStoreTypeGoodsExample1.createCriteria();
                buildStoreTypeGoodsCondition(taskNecessaryStoreTypeGoods, bundlingTaskInfo, goodsTypeList, cityList, companyOrgIdList, storeTypeList, criteria1);
                List<NecessaryStoreTypeGoods> zsNecessaryStoreTypeGoods = necessaryStoreTypeGoodsMapper.selectByExample(necessaryStoreTypeGoodsExample1);
                if (CollectionUtils.isNotEmpty(zsNecessaryStoreTypeGoods)) {
                    necessaryStoreTypeGoodsExtendMapper.batchDel(zsNecessaryStoreTypeGoods.stream().map(NecessaryStoreTypeGoods::getId).collect(Collectors.toList()));
                }
            }

            List<NecessaryStoreTypeGoods> necessaryStoreTypeGoodsList = new ArrayList<>();
            for (TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGood : taskNecessaryStoreTypeGoods) {
                NecessaryStoreTypeGoods necessaryStoreTypeGoods1 = new NecessaryStoreTypeGoods();
                BeanUtils.copyProperties(taskNecessaryStoreTypeGood, necessaryStoreTypeGoods1);
                necessaryStoreTypeGoods1.setId(null);
                necessaryStoreTypeGoods1.setGmtUpdate(new Date());
                necessaryStoreTypeGoods1.setVersion(taskNecessaryStoreTypeGood.getTaskId());
                necessaryStoreTypeGoods1.setGmtCreate(new Date());
                necessaryStoreTypeGoodsList.add(necessaryStoreTypeGoods1);
            }
            if (CollectionUtils.isNotEmpty(necessaryStoreTypeGoodsList)) {
                necessaryStoreTypeGoodsExtendMapper.batchInsert(necessaryStoreTypeGoodsList);
            }


        } catch (Exception e) {
            log.error("处理店型必备出错", e);

        }
    }

    private void buildStoreTypeGoodsCondition(List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods, BundlingTaskInfo bundlingTaskInfo, List<Long> goodsTypeList, List<String> cityList, List<Long> companyOrgIdList, List<String> storeTypeList, NecessaryStoreTypeGoodsExample.Criteria criteria) {
        if (BundlTaskTypeEnum.PLATE_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType()) || BundlTaskTypeEnum.BUSINESS_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())) {
            //todo 如果是全层级组货或者是企业级组货  删除表平台Id，企业Id,城市,商品大类、删除  以商品大类为准 11，12，13   1201
            criteria.andPlatformOrgIdEqualTo(taskNecessaryStoreTypeGoods.get(0).getPlatformOrgId()).andCompanyOrgIdIn(companyOrgIdList)
                    .andCityIn(cityList).andCategoryIdIn(goodsTypeList).andVersionNotEqualTo(taskNecessaryStoreTypeGoods.get(0).getTaskId());
        } else {
            //todo 如果是店型级组货   删除表加平台Id，企业Id,城市,店型,商品大类、删除  以商品大类为准   11，12，13   1201
            criteria.andPlatformOrgIdEqualTo(taskNecessaryStoreTypeGoods.get(0).getPlatformOrgId()).andCompanyOrgIdIn(companyOrgIdList)
                    .andCityIn(cityList).andStoreTypeIn(storeTypeList).andCategoryIdIn(goodsTypeList).andVersionNotEqualTo(taskNecessaryStoreTypeGoods.get(0).getTaskId());
        }
    }
}
