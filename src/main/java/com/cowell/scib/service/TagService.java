package com.cowell.scib.service;

import com.cowell.scib.service.dto.StoreComponentQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/23 15:26
 */
public interface TagService {

    /**
     * 获取选择器门店列表
     * @param taskId
     * @return
     */
    List<Long> getSelectStoreIdList(Long taskId);

    /**
     * 获取选择器门店列表，通过店型入参
     * @param queryParam
     * @return
     */
    List<Long> getSelectStoreIdListByType(StoreComponentQueryParam queryParam);
}
