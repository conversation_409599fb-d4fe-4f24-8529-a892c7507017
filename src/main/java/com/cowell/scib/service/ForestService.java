package com.cowell.scib.service;

import com.cowell.scib.service.dto.AuctionSpuBaseInfo;
import com.cowell.scib.service.dto.CategoryDTO;

import java.util.List;
import java.util.Map;

public interface ForestService {
    List<AuctionSpuBaseInfo> batchFindSpuProperty(List<String> goodsNoList, Long businessId);

    List<CategoryDTO> queryCategoryListByIdsAndLevel(List<Long> categoryIds, String categoryLevel);

    Map<String, Map<String,String>> querySpuProperties(List<String> propertyNames, List<String> goodsCodes, Long businessId);

    Map<Long,String> getPathsByIds(List<Long> categoryIds);
}
