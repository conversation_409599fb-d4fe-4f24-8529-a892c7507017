package com.cowell.scib.service;

import com.cowell.scib.service.vo.amis.UrlResult;
import com.qcloud.cos.model.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface ITencentCOSService {
    String createBucket(String name);

    void deleteBucket(String name);

    boolean simpleUploadFile(String bucketName, String targetPath, String fileName, File localFile);

    String simpleUploadFileStream(String targetPath, String fileName, InputStream inputStream);

    String simpleUploadFileStream(String bucketName, String targetPath, String fileName, InputStream inputStream);

    String uploadFileStreamByContentLength(String targetPath, String fileName, InputStream inputStream, Long contentLength);

    boolean delSingleFile(String filePath, String fileName);

    boolean delSingleFile(String filePath);

    boolean delSingleFile(String bucketName, String filePath, String fileName);

    boolean batchDelFile(String bucketName, ArrayList<DeleteObjectsRequest.KeyVersion> keyList);

    List<Map<String, Object>> listObjects(String bucketName, String prefix);

    File downloadFile(String bucketName, String filePath, String fileName, String targetPath);

    InputStream downloadFileStream(String bucketName, String filePath, String fileName);

    InputStream downloadFileStream(String fileUrl);

    String getBaseUrl();

    /**
     * qshao
     * 2018年6月18日09:02:37
     * 判断文件是否已经生成
     *
     * @return
     */
    boolean isExsit(String bucketName, String fileName);

    /**
     * 获取预授权下载url
     *
     * @param key
     * @return
     */
    String getSign(String key);


    /**
     * 初始化分片上传
     *
     * @return
     * @Param
     */
    public InitiateMultipartUploadResult initiateMultipartUpload(String key);

    /**
     * 分片上传 第n片
     *
     * @param uploadId
     * @param partNumber
     * @param partSize
     * @param inputStream
     * @return
     */
    public UploadPartResult uploadPart(String key, String uploadId, int partNumber, long partSize, InputStream inputStream);

    /**
     * 完成分片上传
     *
     * @param key
     * @param uploadId
     * @param partETags
     * @return
     */
    public CompleteMultipartUploadResult completeMultipartUpload(String key, String uploadId, List<PartETag> partETags);

    /**
     * 中断分片上传
     *
     * @param key
     * @param uploadId
     */
    public void abortMultipartUpload(String key, String uploadId);

    /**
     * 获取文件元数据
     *
     * @param key
     * @return
     */
    public ObjectMetadata getObjectMetadata(String key);

    /**
     * 根据range进行下载
     *
     * @param key
     * @return
     */
    public InputStream rangeDownloadFileStream(String key, long start, long end);

    /**
     * 上传文件
     * @param file
     * @return
     */
    UrlResult uploadAndGetFile(String fileName, MultipartFile file);
}
