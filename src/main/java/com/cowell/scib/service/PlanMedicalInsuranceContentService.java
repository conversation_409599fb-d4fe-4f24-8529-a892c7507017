package com.cowell.scib.service;

import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.planContents.PlanContentParam;
import com.cowell.scib.service.dto.planContents.PlanMedicalInsuranceContentsDTO;
import com.cowell.scib.service.dto.rule.HotGoodsResponseDTO;
import com.cowell.scib.service.vo.amis.PageResult;
import org.springframework.web.multipart.MultipartFile;

public interface PlanMedicalInsuranceContentService {
    PageResult<PlanMedicalInsuranceContentsDTO> list(PlanContentParam param);

    void deleteBySelect(PlanContentParam param);

    HotGoodsResponseDTO importPlan(MultipartFile file, TokenUserDTO userDTO);
}
