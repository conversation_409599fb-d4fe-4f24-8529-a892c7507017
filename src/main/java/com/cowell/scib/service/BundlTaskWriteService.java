package com.cowell.scib.service;

import com.cowell.scib.enums.TaskStatusChangeEnum;
import com.cowell.scib.service.dto.BdpResponseDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.BundlStoreConfirmParam;
import com.cowell.scib.service.param.BundlTaskAddParam;
import com.cowell.scib.service.param.BundlUpdateStoreConfirmParam;

/**
 * 组货任务写操作服务
 * <AUTHOR>
 * @date 2023/3/13 15:29
 */
public interface BundlTaskWriteService {

    /**
     * 添加
     * @param bundlTaskAddParam
     * @param userDTO
     */
    Object add(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO);

    /**
     * 添加MDM门店信息
     * @param confirmParam
     * @param userDTO
     */
    void updateMdmStore(BundlStoreConfirmParam confirmParam, TokenUserDTO userDTO);

    /**
     * 添加任务明细
     * @param bundlTaskAddParam
     * @param userDTO
     */
    void addTaskDetail(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO);

    /**
     * 添加任务明细扩展
     * @param bundlTaskAddParam
     * @param userDTO
     */
    void addTaskDetailExtend(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO);

    /**
     * 第一步添加门店信息
     * @param bundlTaskAddParam
     * @param userDTO
     */
    void addMdmStore(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO);

    /**
     * 更新状态
     * @param taskId
     * @param targetTaskStatus
     */
    void updateTaskStatusByTaskId(Long taskId, Byte targetTaskStatus);

    /**
     * 更新组货业务确认状态
     * @param userDTO
     * @param confirmParam
     */
    void updateBundlConfirm(BundlUpdateStoreConfirmParam confirmParam, TokenUserDTO userDTO);

    /**
     * 提交任务到大数据
     * @param taskId
     */
    BdpResponseDTO commitTaskToBdp(Long taskId, TokenUserDTO userDTO);

    /**
     * 更新状态
     * @param taskId
     * @param changeEnum
     */
    void unifyUpdateStasktatus(Long taskId, TaskStatusChangeEnum changeEnum, TokenUserDTO userDTO);

    /**
     * 更新组货任务状态
     * @param taskId
     * @param taskStatus
     */
    void updateTaskStatus(Long taskId, Byte taskStatus, TokenUserDTO userDTO);

    /**
     * 更新组货任务状态通过编码
     * @param taskCode
     * @param taskStatus
     */
    void updateTaskStatusByCode(String taskCode, Byte taskStatus);
}
