package com.cowell.scib.service;


import com.cowell.scib.enums.AsyncExportActionEnum;
import com.cowell.scib.service.dto.ListToExcelMultiSheetDTO;
import com.cowell.scib.service.dto.TokenUserDTO;

import java.util.List;

/**
 * 异步上传文件处理接口
 */
public interface AsyncExportFileService {

    void asyncExportToCos(String fileName, AsyncExportActionEnum key, TokenUserDTO userInfobyToken, List<ListToExcelMultiSheetDTO> dataToExcelList);

    /**
     * 分页上传
     * @param fileName
     * @param key
     * @param userInfobyToken
     * @param handlerDataExportService
     */
    void asyncExportToCos(String fileName, AsyncExportActionEnum key, TokenUserDTO userInfobyToken, HandlerDataExportService handlerDataExportService);

}
