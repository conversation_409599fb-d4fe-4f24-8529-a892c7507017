package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.service.dto.AuctionSpuBaseInfo;
import com.cowell.scib.service.dto.CategoryDTO;
import com.cowell.scib.service.dto.CategoryQueryDTO;
import com.cowell.scib.service.dto.PropertyParamDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "forest")
public interface ForestFeignClient {

    @ApiOperation(value = "批量查询商品属性信息")
    @GetMapping(value = {"/api/internal/auctionSpu/batchFindSpuProperty"})
    @Timed
    ResponseEntity<List<AuctionSpuBaseInfo>> batchFindSpuProperty(@RequestParam("goodsNoList") List<String> goodsNoList, @RequestParam(value = "businessId", required = false) Long businessId);

    /**
     * 根据 分类id list和类目级别  查询分类信息
     * @param categoryQueryDTO
     * @return
     */
    @ApiOperation(value = "根据ids和类目级别批量查询分类基本信息")
    @PostMapping(value ={"/api/internal/category/queryCategoryListByIdsAndLevel"})
    @Timed
    ResponseEntity<List<CategoryDTO>> queryCategoryListByIdsAndLevel(@RequestBody CategoryQueryDTO categoryQueryDTO);

    @ApiOperation(value = "查询商品编码对应spu集合")
    @PostMapping({"/api/spu/property"})
    @Timed
    ResponseEntity<Map<String, Map<String,String>>> querySpuProperties(@RequestBody List<PropertyParamDTO> propertyParamDTOS);

    @ApiOperation(value = "获取指定类目路径集合", notes = "获取指定类目路径集合")
    @GetMapping("/api/category/getPathsByIds")
    @Timed
    Map<Long,String> getPathsByIds(@RequestParam("categoryIds") List<Long> categoryIds);
    }
