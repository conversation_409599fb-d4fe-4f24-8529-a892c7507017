package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.permission.dto.*;
import com.cowell.scib.service.dto.*;
import com.cowell.permission.vo.OrgVO;
import com.cowell.permission.vo.ResourceTreeVO;
import com.cowell.permission.vo.RoleInfoDetailsVO;
import com.cowell.scib.client.AuthorizedFeignClient;
import com.cowell.scib.config.FeignOauth2RequestInterceptor;
import com.cowell.scib.service.dto.necessaryContents.RoleDTO;
import com.cowell.scib.service.dto.permssion.EmployeeDetailWithWxDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/18 18:14
 */
@AuthorizedFeignClient(name = "permission",configuration = FeignOauth2RequestInterceptor.class)
public interface PermissionFeignClient {

    @ApiOperation(value = "获取当前登录用户的角色", notes = "获取当前登录用户的角色")
    @GetMapping("/api/role/getUserRole")
    @Timed
    ResponseEntity<List<RoleInfoDetailsVO>> getUserRole() throws URISyntaxException;

    @ApiOperation(value = "根据组织机构id获取平台信息", notes = "根据组织机构id获取平台信息")
    @GetMapping("/api/org/getParentOrgByIdAndType")
    @Timed
    ResponseEntity<List<OrgDTO>> getParentOrgByIdAndType(@RequestParam("orgId") Long orgId, @RequestParam("type") Integer type);

    @ApiOperation(value = "获取用户是否有权限访问指定orgId下的所有子级组织", notes = "获取用户是否有权限访问指定orgId下的所有子级组织")
    @GetMapping(value = "/api/datascope/getUserDataScopeChildOrgByOrgId")
    @Timed
    ResponseEntity<List<OrgSimpleDTO>> getUserDataScopeChildOrgByOrgId(@RequestParam(value = "userId") Long userId,
                                                                              @RequestParam("resourceId") Long resourceId,
                                                                              @RequestParam("orgIdList") List<Long> orgIdList,
                                                                              @RequestParam(value = "isRecursion",defaultValue = "0") Integer isRecursion);

    @ApiOperation(value = "根据系统权限ID获取系统的菜单树", notes = "根据系统权限ID获取系统的菜单树")
    @GetMapping("/api/role/getSubMenuTree")
    @Timed
    ResponseEntity<List<ResourceTreeVO>> getSubMenuTree(@RequestParam(value = "userId") Long userId, @RequestParam(value = "id") Long srmResourceId);

    @GetMapping("/api/role/getUserRoleByUserIds")
    @ApiOperation(value="获取用户的角色列表", notes="获取用户的角色列表")
    @Timed
    ResponseEntity<Map<Long,List<RoleInfoDTO>>> getUserRoleByUserIds(@RequestParam("userIds")List<Long> userIds);

    @ApiOperation(value="根据组织机构id获取门店信息", notes="根据组织机构id获取门店信息")
    @GetMapping(value = "api/org/listStoreByOrgIds")
    @Timed
    List<CrmStoreDTO> listStoreByOrgIds(@RequestParam("ids") List<Long> ids);

    /**
     * 根据组织机构ID和登记,获取所有下级组织,示例：
     * userId:29614499710000;
     * resourceId:5();
     * parentOrgId:16(西南平台);
     * orgType:500 (500连锁,800门店);
     * orgIdType:1（默认写1）;
     */
    @RequestMapping(value = "/api/datascope/listOrgTypeBelowOrgAssignedInScope", method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<OrgDTO>> listOrgTypeBelowOrgAssignedInScope(@RequestBody QueryOrgDTO queryDTO);

    @ApiOperation(value = "根据id获取组织机构信息", notes = "根据id获取组织机构信息")
    @GetMapping("/api/org/getOrgInfoById")
    @Timed
    ResponseEntity<OrgVO> getOrgInfoById(@RequestParam(value = "id", required = true) Long id) throws URISyntaxException;


    @ApiOperation(value = "根据指定组织类型与组织路径获取当前/子级组织机构", notes = "根据指定组织类型与组织路径获取当前/子级组织机构")
    @GetMapping(value = "api/org/listChildOrgByOrgTypeAndOrgPath")
    @Timed
    ResponseEntity<List<OrgDTO>> listChildOrgByOrgTypeAndOrgPath(@RequestParam(value = "orgType") Integer orgType,
                                                                 @RequestParam(value = "orgPath") String orgPath);
    /**
     * 根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表
     *
     * @param ids  上级组织机构id集合
     * @param type 类型
     * @return ResponseEntity<List<ChildOrgsDTO>> 如果获取成功则返回状态200和ChildOrgsDTO列表,否则返回http错误400和相应的错误码
     */
    @ApiOperation(value = "根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表", notes = "根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表")
    @GetMapping("/api/org/listChildOrgAssignedType")
    @Timed
    ResponseEntity<List<ChildOrgsDTO>> listChildOrgAssignedType(@RequestParam(value = "ids") List<Long> ids, @RequestParam(value = "type") Integer type) throws URISyntaxException;

    @ApiOperation(value = "获取指定组织下用户有数据权限的树并根据组织类型过滤", notes = "获取指定组织下用户有数据权限的树并根据组织类型过滤")
    @GetMapping(value = "/api/datascope/listUserDataScopeTreesByOrgIdAndTypes")
    @Timed
    ResponseEntity<List<OrgTreeSimpleDTO>> listUserDataScopeTreesByOrgIdAndTypes(@RequestParam(value = "userId") Long userId,
                                                                                        @RequestParam("resourceId") Long resourceId,
                                                                                        @RequestParam("orgId") Long orgId,
                                                                                        @RequestParam(value = "types",required = false) List<Integer> types);

    @ApiOperation(value = "根据用户empCodes和状态查询用户信息", notes = "根据用户empCodes和状态查询用户信息")
    @PostMapping("/api/employee/getListByEmpCodesAndStatus")
    @Timed
    ResponseEntity<List<EmployeeInfoVO>> getListByEmpCodesAndStatus(@RequestBody EmpCodeListAndStatusVO empCodeListAndStatusVO);


    @ApiOperation(value = "根据门店和角色查询员工列表", notes = "根据门店和角色查询员工列表")
    @PostMapping("/api/internal/employee/listEmployeesByOrgIdAndRole")
    @Timed
    ResponseEntity<List<EmployeeDetailWithWxDTO>> getEmployeesByOrgIdAndRole(@RequestBody QueryEmployeeDTO queryEmployeeDTO);

    @ApiOperation(value = "查询所有的角色类型", notes = "查询所有的角色类型")
    @GetMapping("/api/role/getAllRoles")
    @Timed
    ResponseEntity<List<RoleDTO>> getAllRoles();

    @RequestMapping(value = "/api/employee/internal/getUserNamesByEmpCodes", method = RequestMethod.GET)
    @Timed
    List<EmployeeDetailWithWxDTO> getUserNamesByEmpCodes(@RequestParam("empCodes") List<String> empCodes);

    /**
     * @Description 根据角色编码获取相关用户数据
     * @Param queryEmployeeByRoleDTO
     * @Return java.util.List<com.cowell.permission.dto.EmployeeDetailDTO>
     * <AUTHOR>
     * @Date 2025/5/516 18:09
     */
    @PostMapping("/api/internal/employee/listEmployeesByRole")
    @Timed
    List<EmployeeDetailDTO> getEmployeesByRole(@RequestBody QueryEmployeeByRoleDTO queryEmployeeByRoleDTO);

}
