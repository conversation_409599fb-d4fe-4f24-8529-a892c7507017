package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.service.dto.AlertContent;
import com.cowell.scib.service.dto.AlertContentForISCM;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023年03月24日14:08:21
 */
@FeignClient(name = "weblog")
public interface WebLogFeignService {

    @PostMapping("/api/intranet/alert")
    @Timed
    String alert(@RequestBody AlertContent content);


    @PostMapping("/api/intranet/alert/alertForQYYY")
    @Timed
    String alertIscm(@RequestBody AlertContentForISCM content);
}
