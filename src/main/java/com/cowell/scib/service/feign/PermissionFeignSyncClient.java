package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.permission.dto.*;
import com.cowell.permission.vo.OrgVO;
import com.cowell.permission.vo.ResourceTreeVO;
import com.cowell.permission.vo.RoleInfoDetailsVO;
import com.cowell.scib.client.AuthorizedFeignClient;
import com.cowell.scib.config.FeignOauth2RequestInterceptor;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.necessaryContents.RoleDTO;
import com.cowell.scib.service.dto.permssion.EmployeeDetailWithWxDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/18 18:14
 */
@AuthorizedFeignClient(name = "permission-sync",configuration = FeignOauth2RequestInterceptor.class)
public interface PermissionFeignSyncClient {

    /**
     * @Description 根据角色编码获取相关用户数据
     * @Param queryEmployeeByRoleDTO
     * @Return java.util.List<com.cowell.permission.dto.EmployeeDetailDTO>
     * <AUTHOR>
     * @Date 2025/5/516 18:09
     */
    @PostMapping("/api/internal/employee/listEmployeesByRole")
    @Timed
    List<EmployeeDetailDTO> getEmployeesByRole(@RequestBody QueryEmployeeByRoleDTO queryEmployeeByRoleDTO);

}
