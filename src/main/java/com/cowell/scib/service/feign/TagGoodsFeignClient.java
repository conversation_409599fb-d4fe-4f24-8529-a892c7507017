package com.cowell.scib.service.feign;

import com.cowell.scib.client.AuthorizedFeignClient;
import com.cowell.scib.config.FeignOauth2RequestInterceptor;
import com.cowell.scib.service.dto.EntityBase;
import com.cowell.scib.service.dto.StoreComponentQueryParam;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/3/23 14:02
 */
@AuthorizedFeignClient(name = "tag-goods",configuration = FeignOauth2RequestInterceptor.class)
public interface TagGoodsFeignClient {

    @ApiOperation("门店组件器查询门店")
    @PostMapping("/api/optimize/storeComponent/query")
    ResponseEntity<CommonResult<PageResult<EntityBase>>> query(@RequestBody StoreComponentQueryParam param);

}
