package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.client.AuthorizedFeignClient;
import com.cowell.scib.config.FeignOauth2RequestInterceptor;
import com.cowell.scib.service.dto.MdmCompanyTransformDTO;
import com.cowell.scib.service.dto.MdmLicenseBaseDTO;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @date 2023/3/8 18:14
 */
@AuthorizedFeignClient(name = "store",configuration = FeignOauth2RequestInterceptor.class)
public interface StoreFeignClient {
    /**
     * 获取门店信息根据storeno
     * @params storeId
     * @return
     */
    @RequestMapping(value = "/api/mdmStoreBase/findMdmStoreBaseListByStoreNoList",method = RequestMethod.GET)
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findMdmStoreBaseListByStoreNoList(@RequestParam(value = "storeNos") List<String> storeNos);

    /**
     * 获取门店信息根据storeID
     * @params storeId
     * @return
     */
    @RequestMapping(value = "/api/internal/mdmStoreBase/findStoreByStoreIds",method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findStoreByStoreIds(@RequestBody List<Long> storeIds);


    @ApiOperation(value = "获取MDM门店信息", notes = "获取MDM门店信息")
    @RequestMapping(value = "/api/mdmStoreBase/findStoreByStoreNos",method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findStoreByStoreNos(@RequestBody List<String> storeNos);

    @ApiOperation(value = "根据门店编码与扩展标识获取门店主数据信息", notes = "根据门店编码与扩展标识获取门店主数据信息")
    @RequestMapping(value = "/api/mdmStoreBase/findStoreByStoreNosAndExtend",method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findStoreByStoreNosAndExtend(@RequestParam(value = "storeNos") List<String> storeNos,
                                                                              @RequestParam(value = "extendFlag",defaultValue = "false") Boolean extendFlag);

    @GetMapping("/api/findStoreLicense")
    @Timed
    @ApiOperation(value = "通过连锁id和门店id码查经营范围", notes = "通过连锁id和门店id码查经营范围")
    ResponseEntity<List<MdmLicenseBaseDTO>> findStoreLicense(@RequestParam(value = "businessId") Long businessId, @RequestParam(value = "storeId") Long storeId);

    @RequestMapping(value = "/api/internal/file/uploadfileByByte", method = RequestMethod.POST)
    @Timed
    Map<String, Object> uploadFileByByte(@RequestParam(value = "fileName", required = false) String fileName,
                                         @RequestBody byte[] fileStream);

    @ApiOperation(value = "根据公司的法人sapCode获取businessId", notes = "根据公司的法人sapCode获取businessId")
    @RequestMapping(value = "/api/intranet/mdmBusinessBase/transformBusiness",method = RequestMethod.POST)
    @Timed
    ResponseEntity<MdmCompanyTransformDTO> transformBusiness(@RequestBody MdmCompanyTransformDTO mdmCompanyTransformDTO);

}
