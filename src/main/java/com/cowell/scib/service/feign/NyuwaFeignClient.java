package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.service.vo.StoreGoodsVo;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;




@FeignClient(name = "nyuwa-erp")
public interface NyuwaFeignClient {


    /**
     * 导出门店级的一店一目数据
     *
     * @param
     * @return
     */
    @ApiOperation(value = "根据ids和类目级别批量查询分类基本信息")
    @PostMapping(value = {"/api/intranet/router/218/e63c48f1ef1c4da4a92d38b1f8cadcd5.storeGoodsInfo/d7b69bb4e44d4f17be2aa532e2338398/export"})
    @Timed
    CommonResult export(@RequestBody StoreGoodsVo storeGoodsVo);

}
