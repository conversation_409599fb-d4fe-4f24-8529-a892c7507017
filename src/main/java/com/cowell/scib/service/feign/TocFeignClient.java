package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.client.AuthorizedFeignClient;
import com.cowell.scib.config.FeignOauth2RequestInterceptor;
import com.cowell.scib.service.vo.DistributedIDResultVO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/30 14:35
 */
@AuthorizedFeignClient(name = "toc",configuration = FeignOauth2RequestInterceptor.class)
public interface TocFeignClient {

    /**
     * 获取一个ID
     * @param biz
     * @return
     */
    @GetMapping("/api/internal/getId")
    @Timed
    ResponseEntity<DistributedIDResultVO> getNextId(@RequestParam("biz") String biz);

    /**
     * 批量获取ID列表，count最大数量支持200，超过后将抛异常
     * @param biz
     * @param count
     * @return
     */
    @GetMapping("/api/internal/getIdBatch")
    @Timed
    ResponseEntity<List<Long>> getNextIdBatch(@RequestParam("biz") String biz, @RequestParam("count") Integer count);

}
