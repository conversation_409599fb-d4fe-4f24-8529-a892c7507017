package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.service.dto.FileDownloadTaskDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "scrm")
public interface ScrmFeignClient {
    @RequestMapping(value = "/api/noauth/fileDownloadTask", method = RequestMethod.POST)
    @Timed
    @ApiOperation(value = "新建文件下载任务")
    FileDownloadTaskDTO createFileDownloadTask(@RequestBody FileDownloadTaskDTO fileDownloadTaskDTO);

    @RequestMapping(value = "/api/noauth/fileDownloadTask", method = RequestMethod.PUT)
    @Timed
    @ApiOperation(value = "更新文件下载任务 ")
    FileDownloadTaskDTO updateFileDownloadTask(@RequestBody FileDownloadTaskDTO fileDownloadTaskDTO);

}
