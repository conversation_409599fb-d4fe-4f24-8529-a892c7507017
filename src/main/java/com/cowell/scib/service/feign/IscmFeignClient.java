package com.cowell.scib.service.feign;

import com.cowell.scib.service.dto.IscmGoodsCategoryDTO;
import com.cowell.scib.service.dto.SensitiveWarnSaveDTO;
import com.cowell.scib.service.dto.iscm.DailySalesParam;
import com.cowell.scib.service.dto.iscm.DailySalesResponse;
import com.cowell.scib.service.dto.iscm.GoodsInfoParams;
import com.cowell.scib.service.dto.iscm.GoodsInfoResponse;
import com.cowell.scib.service.vo.CommonResponse;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "iscm")
public interface IscmFeignClient {

    @ApiOperation(value = "根据Id查分类信息", notes = "根据Id查分类信息")
    @PostMapping("/api/goods/category/getIscmGoodsCategoryListByIds")
    ResponseEntity<List<IscmGoodsCategoryDTO>> getIscmGoodsCategoryListByIds(@RequestBody List<Long> categoryIds);

    @ApiOperation(value = "获取敏感品参数", notes = "获取敏感品参数")
    @GetMapping({ "/api/internal/sensitivewarn/config/get"})
    ResponseEntity<SensitiveWarnSaveDTO> getSensitivewarnConfig(@RequestParam(value = "paramUniqueMark") String paramUniqueMark);


    @ApiOperation(value = "根据参数唯一编码+业务域获取组织参数类型为商品的商品信息", notes = "根据参数唯一编码+业务域获取组织参数类型为商品的商品信息")
    @PostMapping("/api/configcenter/getParamGoodsInfos")
    ResponseEntity<CommonResponse<GoodsInfoResponse>> getParamGoodsInfos(@RequestBody GoodsInfoParams goodsInfoParams);

    @ApiOperation(value = "根据门店与商品列表获取日销列表", notes = "根据门店与商品列表获取日销列表")
    @PostMapping("/api/intranet/avg/sales/getList")
    ResponseEntity<List<DailySalesResponse>> getAvgSalesList(@RequestBody DailySalesParam dailySalesParam);



}
