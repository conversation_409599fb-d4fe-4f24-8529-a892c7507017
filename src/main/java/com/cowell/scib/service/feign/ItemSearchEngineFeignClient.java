package com.cowell.scib.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.client.AuthorizedFeignClient;
import com.cowell.scib.config.FeignOauth2RequestInterceptor;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;



@AuthorizedFeignClient(name = "item-search-engine",configuration = FeignOauth2RequestInterceptor.class)
public interface ItemSearchEngineFeignClient {

    /**
     * 按连锁获取商品属性
     * @param
     * @return
     */
    @GetMapping(value = "/api/intranet/general/305/23c58280d2744f8291939a04a2bf71d1")
    @Timed
    CommonResult<PageResult<SpuNewVo>> getNewSpuList4Post(@RequestParam("businessId") String businessId,
                                                          @RequestParam("goodsNo") String goodsNo,
                                                          @RequestHeader("bizCode") String bizCode,
                                                          @RequestParam("page") int page,
                                                          @RequestParam("perPage") int perPage);


    /**
     * 获取集团级商品信息
     * 参考:
     * https://api-internal.gaojihealth.cn/nyuwa/api/intranet/general/305/06cfe3d53653402c9a219be8ba55a84a?goodsNo=1000025
     * @param
     * @return
     */
    @ApiOperation(value = "获取集团级商品信息", notes = "获取集团级商品信息")
    @GetMapping(value = "/api/intranet/general/305/06cfe3d53653402c9a219be8ba55a84a")
    @Timed
    CommonResult<PageResult<SpuListVo>> getSupAllList(@RequestParam("goodsNo") String goodsNo,
                                                     @RequestHeader("bizCode") String bizCode,
                                                     @RequestParam("page") int page,
                                                     @RequestParam("perPage") int perPage);
}
