package com.cowell.scib.service;

import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.param.BundlStoreConfirmParam;
import com.cowell.scib.service.param.BundlTaskListParam;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.param.TaskDictParam;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.SelectorResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/10 16:50
 */
public interface BundlTaskService {

    /**
     * 组货任务列表
     * @param listParam
     * @param userDTO
     * @return
     */
    PageResult<BundlTaskListDTO> listBundlTask(BundlTaskListParam listParam, TokenUserDTO userDTO);

    /**
     * 组货公司列表
     * @param plateOrgId
     * @param userDTO
     * @return
     */
    SelectorResult listBundlCompany(Long plateOrgId, TokenUserDTO userDTO);

    /**
     * 组货任务类型
     * @param plateOrgId
     * @param userDTO
     * @return
     */
    SelectorResult listBundlType(Long plateOrgId, TokenUserDTO userDTO);

    /**
     * 组货商品类型列表
     * @param plateOrgId
     * @param userDTO
     * @return
     */
    SelectorResult listBundlGoods(Long plateOrgId, TokenUserDTO userDTO);

    /**
     * 搜索条件列表
     * @param searchType
     * @param userDTO
     * @return
     */
    SelectorResult listSearch(Integer searchType, TokenUserDTO userDTO);

    /**
     * 确认组货门店范围列表
     * @param confirmParam
     * @param userDTO
     * @return
     */
    PageResult<BundlStoreConfirmDTO> listConfirmBundlStore(BundlStoreConfirmParam confirmParam, TokenUserDTO userDTO);

    /**
     * 获取任务规则接口
     * @param taskDictParam
     * @param userDTO
     * @return
     */
    Map<String, BundlTaskDetailDTO> getTaskRuleList(TaskDictParam taskDictParam, TokenUserDTO userDTO);

    /**
     * 查询任务组货商品  BB_2
     * @param taskDictParam
     * @param userDTO
     * @return
     */
    PageResult<CommonGoodsDTO> queryTaskGroupBlackGoods(TaskDictParam taskDictParam, TokenUserDTO userDTO);

    /**
     * 获取门店信息通过店型
     * @param plateOrgId
     * @param userId
     * @param companyOrgIdList
     * @param dictMap
     * @return
     */
    List<MdmStoreBaseDTO> selectMdmStoreFilterStoreType(Long plateOrgId, Long userId, List<Long> companyOrgIdList, Map<String, List<String>> dictMap);

    /**
     * 获取门店信息通过门店选择器
     * @param plateOrgId
     * @param userId
     * @param companyOrgIdList
     * @return
     */
    List<Long> selectMdmStoreIdFilterSelector(Long plateOrgId, Long userId, List<Long> companyOrgIdList);

    /**
     * 获取门店信息通过任务和店型
     * @param taskId
     * @param platStoreTypeCodeList
     * @param storeTypeCodeList
     * @param zsStoreTypeCodeList
     * @return
     */
    List<MdmStoreExDTO> selectMdmStoreByTaskId(Long taskId, Long companyOrgId, String city, List<String> platStoreTypeCodeList, List<String> storeTypeCodeList, List<String> zsStoreTypeCodeList);

    /**
     * 判断是否有selecId
     * @param taskId
     * @param userDTO
     * @return
     */
    Long judgeSelectIdByTask(Long taskId, TokenUserDTO userDTO);

    /**
     * 选择器参数
     * @param ruleParam
     * @param userDTO
     * @return
     */
    Map<String, List<String>> buildSelectParam(RuleParam ruleParam, TokenUserDTO userDTO);

    /**
     * 任务信息
     * @param taskId
     * @param userDTO
     * @return
     */
    BundlTaskInfoDTO getTaskInfo(Long taskId, TokenUserDTO userDTO);

    /**
     * 查询已经存在计算任务中的门店 或 计算中的任务编码
     * @param orgId
     * @param storeSapCode 指定门店sap编码 返回任务编码  不指定 返回门店编码列表
     * @return
     */
    List<String> queryAlreadyBundlStoreListAndTaskType(Long orgId,List<Byte> taskTypeList,String storeSapCode);

    /**
     * 返回枚举map
     * @param dicCodeList
     * @return
     */
    Map<String, String> dicValueMap(List<String> dicCodeList);


    /**
     * 查询任务详情信息
     * @param taskId
     * @param userDTO
     */
    BundlTaskInfoDTO getTaskDetailInfo(Long taskId, TokenUserDTO userDTO);

    /**
     * 导出组货任务全部门店
     * @param taskId
     * @param response
     * @param userDTO
     */
    void exportStores(Long taskId, HttpServletResponse response, TokenUserDTO userDTO) throws Exception;

    /**
     * 查询任务结果-推荐商品
     * @param taskDictParam
     * @param userDTO
     * @return
     */
    PageResult<?> queryTaskResult(TaskDictParam taskDictParam, TokenUserDTO userDTO);

}
