package com.cowell.scib.service;

import com.cowell.scib.rest.errors.BusinessErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ImportDataFactory {
    @Autowired
    private List<IImportDataService> policyList;
    @Autowired
    private static Map<String, IImportDataService> policyMap = new HashMap<>();

    public IImportDataService getPolicy(String code, MultipartFile file){
        IImportDataService iPolicy = policyMap.get(code);
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new BusinessErrorException("导入文件大于" + (file.getSize() / (1024 * 1024)) + "M");
        }
        String originalFilename = file.getOriginalFilename();
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!".xls".equals(fileType) && !".xlsx".equals(fileType)) {
            throw new BusinessErrorException("导入的文件类型有误");
        }

        if(iPolicy == null){
            return null;
        }
        return iPolicy;
    }

    @PostConstruct
    private void init(){policyList.forEach(iPolicy -> policyMap.put(iPolicy.getCode(), iPolicy));}

}
