package com.cowell.scib.service;

import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:47
 */
@Component
public class RuleAddFactory {

    @Autowired
    private List<IRuleAddService> policyList;
    @Autowired
    private static Map<String, IRuleAddService> policyMap = new HashMap<>();

    public IRuleAddService getPolicy(String code){
        IRuleAddService iPolicy = policyMap.get(code);
        if(iPolicy == null){
            return null;
        }
        return iPolicy;
    }

    @PostConstruct
    private void init(){policyList.forEach(iPolicy -> policyMap.put(iPolicy.getCode(), iPolicy));}


}
