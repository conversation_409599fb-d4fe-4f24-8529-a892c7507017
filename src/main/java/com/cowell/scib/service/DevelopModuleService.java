package com.cowell.scib.service;

import com.cowell.scib.entity.DevelopModule;
import com.cowell.scib.service.dto.DevelopModuleDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopAddParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:24
 */
public interface DevelopModuleService {

    /**
     * 查询功能模块集合
     * @return
     */
    List<DevelopModuleDTO> listDevelopModuleByCondition(List<String> moduleCodeList, Byte useStatus);

    /**
     * 通过名称精确查找记录
     * @param moduleName
     * @return
     */
    long countDevelopModuleByName(String moduleName);

    /**
     * 通过编码精确查找记录
     * @param moduleCode
     * @return
     */
    long countDevelopModuleByCode(String moduleCode);

    /**
     * 添加模块
     * @param developAddParam
     */
    void addModule(DevelopAddParam developAddParam, TokenUserDTO userDTO);

    /**
     * 编辑模块
     * @param developAddParam
     */
    void editModule(DevelopAddParam developAddParam, TokenUserDTO userDTO);

    /**
     * 通过编码精确查找记录
     * @param moduleCode
     * @return
     */
    public long countModuleByCode(String moduleCode);
}
