package com.cowell.scib.service;

import com.cowell.scib.service.dto.DropBoxParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.ScibCommonResultDTO;
import com.cowell.scib.service.vo.amis.OptionDto;

import java.util.List;

public interface CommonService {
    List<OptionDto> dropBox(DropBoxParam param, TokenUserDTO userDTO);

    /**
     * 获取门店id列表通过选择器和配置
     * @param orgId
     * @param scopeCode
     * @param configType
     * @return
     */
    ScibCommonResultDTO getStoreIdListByStoreSelectorAndConfig(Long orgId, String scopeCode, String configType);
}
