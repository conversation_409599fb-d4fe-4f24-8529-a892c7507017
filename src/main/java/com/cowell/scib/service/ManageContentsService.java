package com.cowell.scib.service;

import com.cowell.scib.entityDgms.JymlSkuLimit;
import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.customize.JymlSkyExemptionParam;
import com.cowell.scib.service.dto.customize.JymlStoreIncreaseLimitConfigureDTO;
import com.cowell.scib.service.dto.customize.JymlStoreSkuExemptionRecordDTO;
import com.cowell.scib.service.dto.manageContents.*;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ManageContentsService {

    /**
     * 经营目录管理下拉框
     * @param userDTO
     * @return
     */
    CommonResponse<OptionDto> manageContentsDropBox(TokenUserDTO userDTO, ManageQueryParam param);


    /**
     * 经营目录bdp推送完成回调
     * @return
     */
    CommonResult bdpManageSuggestResultNotify(Long version,String businessIds);


    /**
     *  集团sku上限维护,bdp推送完成回调
     * @return
     */
    CommonResult bdpManageSkuLimitResultNotify(Long version, String businessOrgIds);



    /**
     * 经营目录管理列表
     * @param userDTO
     * @return
     */
    CommonResponse<ManageCommonDTO> manageContentsList(TokenUserDTO userDTO, ManageQueryParam param);

    /**
     * 经营目录管理树
     * @param userDTO
     * @param param
     * @return
     */
    CommonResponse<ManageCategoryTreeDTO> manageContentsTree(TokenUserDTO userDTO, ManageQueryParam param);

    /**
     * 经营目录管理推送按门店处理
     * @param suggestMqDTO
     * @return
     */
    boolean handleSuggest(ManageSuggestMqDTO suggestMqDTO);

    /**
     * 经营目录管理批量确认
     * @param userDTO
     * @param manageBatchUpdateDTO
     * @return
     */
    CommonResult batchUpdate(TokenUserDTO userDTO, ManageBatchUpdateParam manageBatchUpdateDTO);

    /**
     * 经营目录 门店确认管理进度数据汇总
     * @param userDTO
     * @param param
     * @return
     */
    CommonResult summarizeMdProcessData(TokenUserDTO userDTO, ManageQueryParam param);

    /**
     * 经营目录 分类上限查询
     * @param userDTO
     * @param param
     * @return
     */
    CommonResult manageCategoryLimit(TokenUserDTO userDTO, ManageQueryParam param);

    /**
     * sku上限校验 通过则更新
     * @param limitParam
     * @return
     */
    CommonResult skuLimitUpdate(ManageJymlSkuMaxLimitParam limitParam, TokenUserDTO userDTO);


   Boolean selectMdmStoreIdFilterSelector(OrgInfoBaseCache storeInfo);

    /**
     * 升级版本  经营目录管理推送按门店/增加按连锁处理能力 给前端工具使用
     * @param suggestMqDTO
     * @return
     */
    Boolean handleSuggestByStoreOrBusiness(ManageSuggestMqDTO suggestMqDTO);

    /**
     * 查询豁免商品
     * @param limitParam
     * @param userDTO
     * @return
     */
    CommonResponse<JymlStoreSkuExemptionRecord> getNonJyGoods(ManageJymlNonJyGoodsParam limitParam, TokenUserDTO userDTO);
    /**
     * 获取已添加的豁免商品列表
     * @param record
     * @param userDTO
     * @return
     */
    CommonResponse<JymlStoreSkuExemptionRecordDTO> nonJyGoodsPage(JymlStoreSkuExemptionRecord record, TokenUserDTO userDTO);
    /**
     * 添加或者删除豁免商品 (有id删除,无id添加)
     * @param record
     * @param userDTO
     * @return
     */
    CommonResult nonJyGoodsUpdate(JymlStoreSkuExemptionRecord record, TokenUserDTO userDTO);


    /**
     * 更新 建议经营状态=3，经营状态=4的，更新成3； 建议经营状态=4和5，经营状态=3的，更新成4
     * @param storeCodes
     */
    void updateDiffManageStatus(List<String> storeCodes);

    /**
     * 导入修改经营状态
     * @param file
     */
    void importUpdateManageStatus(MultipartFile file);

    /**
     * 分页查询SKU数配置管理
     * @param queryParam
     * @param userDTO
     * @return
     */
    PageResult<JymlSkuMaxLimitConfigureDTO> getSkuLimitList(ManageJymlSkuMaxLimitQueryParam queryParam, TokenUserDTO userDTO);

    /**
     * 导出SKU数配置管理
     * @param userDTO
     * @param param
     */
    void exportSkuLimitList(TokenUserDTO userDTO, ManageJymlSkuMaxLimitQueryParam param);

    PageResult<JymlSkuLimit> getJtJymlLimitList(JtQueryParam queryParam, TokenUserDTO userDTO);

    ImportResult importNonJyGoods(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    PageResult<JymlStoreIncreaseLimitConfigureDTO> getMaintenanceNonJyGoods(JymlSkyExemptionParam param, TokenUserDTO userDTO, List<Long> scopeCompanyOrgIds);

    void exportMaintenanceNonJyGoods(TokenUserDTO userDTO, JymlSkyExemptionParam param);
}
