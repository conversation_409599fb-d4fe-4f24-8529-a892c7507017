package com.cowell.scib.service.param.rule;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:55
 */
@Data
public class RuleAddParam implements Serializable {
    private static final long serialVersionUID = -290858796264669045L;

    @ApiModelProperty("组织机构Id")
    private Long orgId;

    @ApiModelProperty("区域编码")
    private String scopeCode;

    @ApiModelProperty("配置类型 1.推荐/2.必备/3.淘汰规则")
    private String configType;

    /**
     * 规则集合
     */
    @ApiModelProperty("配置信息")
    private Map<String, RuleDetailDTO> detailMap;

    @ApiModelProperty("商品编码集合")
    private List<String> goodsNoList;
    @ApiModelProperty("resourceId")
    private Long resourceId;

    // 为了兼容老接口 默认为组货商品黑名单
    @ApiModelProperty("商品dictCode")
    private String goodsDictCode = Constants.GOODS_BLACK_LIST;

}
