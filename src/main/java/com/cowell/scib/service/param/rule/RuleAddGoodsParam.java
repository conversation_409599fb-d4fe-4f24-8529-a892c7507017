package com.cowell.scib.service.param.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RuleAddGoodsParam implements Serializable {


    @ApiModelProperty("组织机构Id")
    private Long orgId;

    @ApiModelProperty("区域编码")
    private String scopeCode;

    @ApiModelProperty("配置类型 1.推荐/2.必备/3.淘汰规则")
    private String configType;

    @ApiModelProperty("商品编码集合")
    private List<String> goodsNoList;

}
