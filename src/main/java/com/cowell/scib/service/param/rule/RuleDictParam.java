package com.cowell.scib.service.param.rule;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:27
 */
@Data
public class RuleDictParam implements Serializable {
    private static final long serialVersionUID = 4858710038871025548L;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 域编码
     */
    private String scopeCode;

    /**
     * 域描述
     */
    private String scopeDesc;

    /**
     * 是否可编辑 0：否 1：是
     */
    private Byte editAble;

}
