package com.cowell.scib.service.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/30 11:48
 */
@Data
public class DevelopAddParam implements Serializable {
    private static final long serialVersionUID = -5151896013395024133L;

    @ApiModelProperty("发版模块编码，唯一，不传代表添加")
    @NotNull(message = "功能模块编码不能为空")
    private String moduleCode;

    /**
     * 模块名称
     */
    @ApiModelProperty("模块名称")
    @NotNull(message = "请填写名称")
    private String moduleName;

    /**
     * 启用状态  0-停用 1-启用
     */
    @ApiModelProperty("启用状态  0-停用 1-启用")
    @NotNull(message = "启用状态不能为空")
    private Byte useStatus;

    @ApiModelProperty("新增标识")
    private Integer createMark;
}
