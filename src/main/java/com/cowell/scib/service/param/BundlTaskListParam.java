package com.cowell.scib.service.param;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/9 18:27
 */
@Data
public class BundlTaskListParam extends AmisPageParam implements Serializable {
    private static final long serialVersionUID = 243441481478507558L;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务编号")
    private String taskCode;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货  @see BundlTaskTypeEnum
     */
    @ApiModelProperty(value = "组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货")
    private Byte taskType;

    @JsonIgnore
    private List<Byte> taskTypeList;
    /**
     * 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货  @see BundlTaskTypeEnum
     */
    @ApiModelProperty(value = "组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货")
    private String taskTypeStr;
    /**
     * 组货商品类型（大类），多选
     */
    @ApiModelProperty(value = "组货商品类型（大类），多选")
    private String bundlGoodsBigKindsStr;

    /**
     * 组货商品类型（大类），多选
     */
    @ApiModelProperty(value = "组货商品类型（大类），多选")
    private String bundlGoodsBigKindsList;

    @JsonIgnore
    private List<String> bundlGoodsBigKinds;

    /**
     * 组货区域平台ID集合
     */
    @ApiModelProperty(value = "组货区域平台ID")
    private Long plateOrgId;

    /**
     * 组货公司连锁ID集合
     */
    @ApiModelProperty(value = "组货公司连锁ID集合")
    private String commpanyOrgIdList;
    @JsonIgnore
    private List<String> commpanyOrgIds;

    /**
     * 组货店型，多选
     */
    @ApiModelProperty(value = "组货店型，多选")
    private String bundlStoreList;
    @JsonIgnore
    private List<String> bundlStores;

    /**
     * 1 暂存 2 计算中 3 计算完成 4已作废 5已更新
     */
    @ApiModelProperty(value = "组货任务状态  1 暂存 2 计算中 3 计算完成 4已作废 5已更新")
    private String taskStatusList;
    @JsonIgnore
    private List<Byte> taskStatus;

    @ApiModelProperty(value = "中参店型，多选")
    private String zsStoreList;
    @JsonIgnore
    private List<String> zsStores;



    @ApiModelProperty(value = "配方店型，多选,逗号分割")
    private String disposeStoreList;
    @JsonIgnore
    private List<String> disposeStores;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "任务创建人")
    private String createdName;
    @ApiModelProperty(value = "任务创建开始时间和结束时间，逗号分割")
    private String gmtCreateStartEnd;
    @JsonIgnore
    private String gmtCreateStart;
    @JsonIgnore
    private String gmtCreateEnd;

    /**
     * 提交人
     */
    @ApiModelProperty(value = "任务提交人")
    private String commitName;
    @ApiModelProperty(value = "任务提交开始时间和结束时间，逗号分割")
    private String gmtCommitStartEnd;
    @JsonIgnore
    private String gmtCommitStart;
    @JsonIgnore
    private String gmtCommitEnd;

    /**
     * 下发人
     */
    @ApiModelProperty(value = "任务下发人")
    private String issuedName;
    @ApiModelProperty(value = "任务下发开始时间和结束时间，逗号分割")
    private String gmtIssuedStartEnd;
    @JsonIgnore
    private String gmtIssuedStart;
    @JsonIgnore
    private String gmtIssuedEnd;

    /**
     * 创建人userId
     */
    @JsonIgnore
    private Long createBy;

    /**
     * 任务ID集合
     */
    @JsonIgnore
    private List<Long> taskIdList;

    /**
     * 明细显示字段
     */
    @JsonIgnore
    private Map<String, String> taskDetailMap;

    @ApiModelProperty(value = "门店编码/预选址编集合")
    private String storeIdList;

    @JsonIgnore
    private List<Long> storeIds;

    @ApiModelProperty(value = "预选址名称")
    private String storeName;

    @ApiModelProperty(value = "门店属性集合")
    private String storeAttrList;
    @JsonIgnore
    private List<String> storeAttrs;


}

