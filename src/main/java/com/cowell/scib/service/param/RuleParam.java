package com.cowell.scib.service.param;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RuleParam extends AmisPageParam implements Serializable {

    @ApiModelProperty("组织机构Id")
    private Long orgId;

    @ApiModelProperty("区域编码")
    private String scopeCode;

    @ApiModelProperty("配置类型 1.推荐/2.必备/3.淘汰规则")
    private String configType;

    @ApiModelProperty("resourceId")
    private Long resourceId;

    // 为了兼容老接口 默认为组货商品黑名单
    @ApiModelProperty("商品dictCode")
    private String goodsDictCode = Constants.GOODS_BLACK_LIST;

}
