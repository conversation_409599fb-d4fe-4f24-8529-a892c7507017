package com.cowell.scib.service.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TaskNecessaryPlatformParam implements Serializable {
    /**
     * 任务Id
     */
    private Long taskId;

    /**
     * 平台orgid
     */
    private Long platformOrgId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 店型
     */
    private List<String> storeTypeList;

    /**
     * 商品编码
     */
    private String goodsNo;
}
