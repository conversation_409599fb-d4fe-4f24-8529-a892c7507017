package com.cowell.scib.service.param;

import com.cowell.scib.service.dto.BundlTaskDetailDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/13 16:28
 */
@Data
public class BundlTaskAddParam implements Serializable {
    private static final long serialVersionUID = 6311039529747409820L;

    @ApiModelProperty("任务Id")
    private Long taskId;

    @ApiModelProperty("任务编码")
    private String taskCode;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务创建步骤 1-第一步 2-第二步 3-第三步")
    private Byte taskStep;

    @ApiModelProperty("是否暂存  true - 是")
    private Boolean saveAble;

    /**
     * 组货区域平台ID
     */
    @ApiModelProperty(value = "组货区域平台ID")
    private Long plateOrgId;

    @ApiModelProperty("备注")
    private String memo;

    @ApiModelProperty("组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货 5: 新店目录推荐任务")
    private Byte taskType;

    /**
     * 字典集合
     */
    @ApiModelProperty("配置字典信息")
    private Map<String, BundlTaskDetailDTO> detailMap;

    @ApiModelProperty("商品编码集合")
    private List<String> goodsNoList;

    /****** 5: 新店目录推荐任务  后加参数 start ******/

    @ApiModelProperty("新店公司")
    private Long businessOrgId;

    @ApiModelProperty("门店来源")
    private int selectStoreSource=0;

    @ApiModelProperty("新店StoreId")
    private Long selectStoreId;

    @ApiModelProperty("门店编码")
    private String selectSapCode;

    @ApiModelProperty("门店名")
    private String selectStoreName;

    @ApiModelProperty("相似门店storeIdList")
    private List<Long> similarStoreIdList;

    @ApiModelProperty("新店经营商品最高成本单价")
    private BigDecimal goodMaxCostPrice;

    @ApiModelProperty("预计开业时间")
    private String predictOpenTime;

    @ApiModelProperty("新店黑名单查询参数")
    private String blackListParamUniqueMark;
    /****** 5: 新店目录推荐任务  后加参数 end******/


    @JsonIgnore
    private TokenUserDTO userDTO;
}
