package com.cowell.scib.service.param;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 第一步：域编码：TaskCreate
 * 第二步：域编码：B_1   域编码：BB_2
 * 第三步：组货通用规则域编码：BB_3
 *        企业必备中西成药域编码：BB_4_1
 *        企业必备中药参茸-养生中药域编码：BB_4_2
 *        企业必备保健食品域编码：BB_4_3
 *        企业必备医疗器械域编码：BB_4_4
 *
 *        店型必备中西成药域编码：BB_5_1
*         店型必备中药参茸-养生中药域编码：BB_5_2
*         店型必备保健食品域编码：BB_5_3
*         店型必备医疗器械域编码：BB_5_4
 *        店型必备通用域编码：BB_5_5
 *        店型必备中药参茸-配方中药域编码：BB_5_6
 *
 * <AUTHOR>
 * @date 2023/3/16 10:16
 */
@Data
public class TaskDictParam extends AmisPageParam implements Serializable {
    private static final long serialVersionUID = 4183292562821487970L;

    @ApiModelProperty("任务Id")
    private Long taskId;

    @ApiModelProperty("平台组织机构Id")
    private Long orgId;

    @ApiModelProperty("区域编码")
    private String scopeCode;

    @ApiModelProperty(value = "组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货")
    private Byte taskType;
}
