package com.cowell.scib.service.param;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13 10:28
 */
@Data
public class BundlStoreConfirmParam extends AmisPageParam implements Serializable {
    private static final long serialVersionUID = 4127906590201852103L;

    /**
     * 平台组织ID
     */
    private Long plateOrgId;

    /**
     * 组货公司组织ID集合
     */
    private List<Long> companyOrgIdList;

    /**
     * 门店编码，逗号分割，多选
     */
    private String storeCode;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 选择器ID
     */
    private Long selectId;

    /**
     * 选择的组货门店id集合
     */
    private List<Long> bundlStoreIdList;

    /**
     * 多个门店编码集合
     */
    @JsonIgnore
    private List<String> storeCodeList;
}
