package com.cowell.scib.service.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 发版记录更新和编辑入参
 * <AUTHOR>
 * @date 2022/8/30 15:06
 */
@Data
public class DevelopRecordAddParam implements Serializable {
    private static final long serialVersionUID = -1713427467303669771L;

    /**
     * 主键
     */
    @ApiModelProperty("发版记录主键ID")
    private Integer recordId;

    /**
     * 模块编码
     */
    @ApiModelProperty("功能模块编码")
    @NotNull(message = "请选择功能模块")
    private String moduleCode;

    /**
     * 发版记录版本号
     */
    @ApiModelProperty("发版记录版本号")
    @NotNull(message = "请填写版本号")
    private String developVersion;

    /**
     * 发版类型  1-BUG修复  2-新功能  3-功能优化
     */
    @ApiModelProperty("发版类型  1-BUG修复  2-新功能  3-功能优化")
    @NotNull(message = "请选择发版类型")
    private Byte developType;

    /**
     * 发版记录标题
     */
    @ApiModelProperty("发版记录标题")
    @NotNull(message = "请填写标题")
    private String developTitle;

    /**
     * 发版记录内容
     */
    @ApiModelProperty("发版记录内容")
    @NotNull(message = "请填写发版内容")
    private String developContent;

    /**
     * 发版状态  0-未发布 1-已发布
     */
    @ApiModelProperty("发版状态  0-未发布 1-已发布")
    private Byte developStatus;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    @NotNull(message = "请填写发布时间")
    private String developTime;

    /**
     * 文件列表
     */
    @ApiModelProperty("文件URL集合，逗号分割（amis要求）")
    private String fileVOList;

    @ApiModelProperty("图片地址，逗号分割（amis要求）")
    private String imageUrlList;

    @ApiModelProperty("触达通道集合，逗号分割（amis要求）")
    private String reachChannelList;

    @ApiModelProperty("触达组id集合，逗号分割（amis要求）")
    private String reachGroupIdList;

    @ApiModelProperty("触达url")
    private String developUrl;
}
