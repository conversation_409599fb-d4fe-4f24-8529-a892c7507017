package com.cowell.scib.service;

import com.cowell.scib.service.dto.BdpResponseDTO;
import com.cowell.scib.service.dto.BundlTaskBdpDTO;
import com.cowell.scib.service.dto.rule.RuleDataBaseDTO;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 * @date 2023/3/20 18:18
 */
public interface SendService {

    /**
     * 发送BDP，返回值暂定
     * @param bundlTaskBdpDTO
     */
    ResponseEntity<BdpResponseDTO> sendBdp(BundlTaskBdpDTO bundlTaskBdpDTO);

    ResponseEntity<BdpResponseDTO> sendAdjustBdp(BundlTaskBdpDTO bundlTaskBdpDTO);

    /**
     * 不良库存
     * @param ruleDataBaseDTO
     * @return
     */
    ResponseEntity<BdpResponseDTO> sendDefectiveBdp(RuleDataBaseDTO ruleDataBaseDTO);

}
