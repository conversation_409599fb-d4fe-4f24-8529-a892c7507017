package com.cowell.scib.service;

import com.cowell.scib.entityDgms.MdmTask;
import com.cowell.scib.entityDgms.NecessaryLevelConfig;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.necessaryComtentsV2.NecessaryAddParam;
import com.cowell.scib.service.dto.necessaryComtentsV2.NecessaryContentsDTO;
import com.cowell.scib.service.dto.necessaryComtentsV2.NecessaryQueryParam;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreGoodsContentDTO;
import com.cowell.scib.service.dto.necessaryContents.*;
import com.cowell.scib.service.vo.amis.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface NecessaryContentsV2Service {

    String BATCH_IMPORT_COPY_STORE = "SCIB_BATCH-IMPORT-COPY-STORE-";
    /**
     * 判断添加是否完成key
     */
    String COPY_STORE_INFO = "SCIB_COPY-STORE-INFO-";

    PageResult<NecessaryContentsDTO> getContents(TokenUserDTO userDTO, NecessaryQueryParam param);

    String editNecessaryGoods(TokenUserDTO userDTO, NecessaryAddParam param, MdmTask task);

    String modify(TokenUserDTO userDTO, List<Long> ids, Byte status, String invalidReason, MdmTask task, Map<String, String> goodsLineMap);

    void exportContents(TokenUserDTO userDTO, NecessaryQueryParam param);

    ImportResult importContent(MultipartFile file, Integer bizType, TokenUserDTO userDTO) throws Exception;
    ImportResult importDelContent(MultipartFile file, Integer bizType, TokenUserDTO userDTO) throws Exception;

    void deleteStoreGoods(List<Long> orgIds, TokenUserDTO userDTO, List<OrgInfoBaseCache> orgInfoBaseCaches);

    String importCopyStore(MultipartFile file, TokenUserDTO userDTO);

    CommonProcessDTO<List<ImportStoreDTO>> getImportProcess(TokenUserDTO userDTO);

    PageResult<StoreGoodsContentDTO> getSourceStoreInfos(StoreGoodsQueryParam param, TokenUserDTO userDTO);

    String copyStoreGoods(TokenUserDTO userDTO, NecessaryCopyParam param);

    void updateCopyStoreGoodsStatus(Long sourceStoreId, String goodsNo, Byte effectStatus, TokenUserDTO userDTO);

    NecessaryLevelConfig getConfigLevelByNecessaryTag(Integer necessaryTag);

    Boolean checkHasEditPerm(TokenUserDTO userDTO);

    // 张瑜测试用
    List<Long> selectMdmStoreIdFilterSelector(Long platformOrgId, TokenUserDTO userDTO);

    void importUpdateManageStauts(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    void bdpTaskCallback();
    void bdpTaskCallback(String storeCode, Integer bizType);
    void bdpTaskCallback(Integer bizType) throws Exception;

    void importUpdateCategory(MultipartFile file, TokenUserDTO userDTO) throws Exception;
}
