package com.cowell.scib.service;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.nyuwa.cos.util.CosService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.mapperTidb.*;
import com.cowell.scib.mapperTidb.extend.*;
import com.cowell.scib.mq.producer.SixLevelNecessaryCheckProducer;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.*;
import com.cowell.scib.service.dto.necessaryContents.NecessaryTaskGoodsCommonDTO;
import com.cowell.scib.service.param.TaskNecessaryPlatformParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.TracerBean;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@EnableAsync
@Configuration
public class AsyncImportTrackResultGoodsConfig {

    private static Logger logger = LoggerFactory.getLogger(AsyncImportTrackResultGoodsConfig.class);

    @Autowired
    private TrackRetultAllDetailExtendMapper trackRetultAllDetailExtendMapper;

    @Autowired
    private TaskNecessaryPlatformGoodsMapper taskNecessaryPlatformGoodsMapper;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;

    @Autowired
    private TrackRetultAllDetailMapper trackRetultAllDetailMapper;

    @Autowired
    private TaskNecessaryPlatformGoodsExtendMapper taskNecessaryPlatformGoodsExtendMapper;

    @Autowired
    private TaskNecessaryStoreTypeGoodsExtendMapper taskNecessaryStoreTypeGoodsExtendMapper;

    @Autowired
    private TaskNecessaryStoreTypeGoodsMapper taskNecessaryStoreTypeGoodsMapper;

    @Autowired
    private TaskNecessaryCompanyGoodsMapper taskNecessaryCompanyGoodsMapper;

    @Autowired
    private TaskNecessarySingleStoreGoodsMapper taskNecessarySingleStoreGoodsMapper;

    @Autowired
    private TaskNecessaryChooseStoreTypeGoodsMapper taskNecessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private TaskNecessaryChooseStoreTypeGoodsExtendMapper taskNecessaryChooseStoreTypeGoodsExtendMapper;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;
    @Autowired
    private CommonEnumsMapper commonEnumsMapper;

    @Autowired
    private NecessaryPlatformGoodsExtendMapper necessaryPlatformGoodsExtendMapper;


    @Autowired
    private NecessaryGroupGoodsExtendMapper necessaryGroupGoodsExtendMapper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private ForestService forestService;

    @Autowired
    private BundlTaskService bundlTaskService;

    @Autowired
    private NecessaryStoreTypeGoodsExtendMapper necessaryStoreTypeGoodsExtendMapper;

    @Autowired
    private NecessaryCompanyGoodsExtendMapper necessaryCompanyGoodsExtendMapper;

    @Autowired
    private NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;

    @Autowired
    private TrackResultChooseStoreTypeGoodsMapper trackResultChooseStoreTypeGoodsMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;

    @Autowired
    private ConfigOrgDetailExMapper configOrgDetailExMapper;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private TaskNecessaryCompanyGoodsExtendMapper taskNecessaryCompanyGoodsExtendMapper;

    @Autowired
    private StoreService storeService;

    @Autowired
    private SixLevelNecessaryCheckProducer sixLevelNecessaryCheckProducer;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private CosService cosService;

    @Autowired
    private IscmService iscmService;

    @Autowired
    private TracerBean tracerBean;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    @Autowired
    private NecessaryCompanyGoodsMapper necessaryCompanyGoodsMapper;

    @Autowired
    private TaskNecessarySingleStoreGoodsExtendMapper taskNecessarySingleStoreGoodsExtendMapper;

    @Autowired
    private TrackRetultNewStoreAllDetailExtendMapper trackRetultNewStoreAllDetailExtendMapper;

    // 中参店型名字映射关系
    public static final Map<String, String> zsStoreTypeNameMapping = new HashMap<>();

    static {
        zsStoreTypeNameMapping.put("ZS01", "中参大店");
        zsStoreTypeNameMapping.put("ZS02", "中参中店");
        zsStoreTypeNameMapping.put("ZS03", "中参小店");
        zsStoreTypeNameMapping.put("ZS04", "中参微店");
    }

    public List<String> storeTypeByPlat(String platStoreTypeCode) {
        Map<String, List<String>> storeTypePlatTypeMapping = new HashMap<>();
        storeTypePlatTypeMapping.put("PT01", Lists.newArrayList("14", "24", "34"));
        storeTypePlatTypeMapping.put("PT05", Lists.newArrayList("10", "20", "30"));
        storeTypePlatTypeMapping.put("PT02", Lists.newArrayList("11", "21", "31"));
        storeTypePlatTypeMapping.put("PT03", Lists.newArrayList("12", "22", "32"));
        storeTypePlatTypeMapping.put("PT04", Lists.newArrayList("13", "23", "33"));
        return storeTypePlatTypeMapping.get(platStoreTypeCode);
    }
    @Async("trackResultFileUploadExecutor")
    public void delSingleStoreGoods(List<SingleStoreNecessaryData> dataList, Long taskId, String key, RBucket<ImportResult> rBucket, ImportResult result) {
        try{
            ArrayList<SingleStoreNecessaryData> errorData = new ArrayList<>();
            ArrayList<SingleStoreNecessaryData> repeatDataList = new ArrayList<>();
            ArrayList<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoodsList = new ArrayList<>();
            ArrayList<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoodsList = new ArrayList<>();
            ArrayList<Long> trackRetultAllDetailIdList = new ArrayList<>();
            for (SingleStoreNecessaryData singleStoreNecessaryData : dataList) {
                if (StringUtils.isEmpty(singleStoreNecessaryData.getGoodsNo()) || StringUtils.isEmpty(singleStoreNecessaryData.getOrgNo())) {
                    singleStoreNecessaryData.setErrorReason("商品编码、门店编码不能为空");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                Optional<OrgInfoBaseCache> storeByOrgId = CacheVar.getStoreBySapCode(singleStoreNecessaryData.getOrgNo());
                if (!storeByOrgId.isPresent()) {
                    singleStoreNecessaryData.setErrorReason("门店编码在组织下查询不存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                if (repeatDataList.contains(singleStoreNecessaryData)) {
                    singleStoreNecessaryData.setErrorReason("表格中数据重复");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }

                TrackRetultDetailParam param = new TrackRetultDetailParam();
                List<String> levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()), String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode()));
                param.setTaskId(taskId);
                param.setOrgNoList(Arrays.asList(singleStoreNecessaryData.getOrgNo()));
                param.setGoodsId(singleStoreNecessaryData.getGoodsNo());
                param.setLevelList(levelList);
                param.setStatus(Constants.NORMAL_STATUS);
                List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);
                if (CollectionUtils.isEmpty(trackRetultAllDetails)) {
                    singleStoreNecessaryData.setErrorReason("该行在一店一目表中没有对应的必备明细,不需要删除。");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                Map<String, List<TrackRetultAllDetail>> levelMap = trackRetultAllDetails.stream().collect(Collectors.groupingBy(TrackRetultAllDetail::getLevel));
                if (Objects.nonNull(levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode())))) {
                    //删除表7-4
                    TaskNecessarySingleStoreGoodsExample example = new TaskNecessarySingleStoreGoodsExample();
                    example.createCriteria().andStoreCodeEqualTo(singleStoreNecessaryData.getOrgNo()).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                    List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods = taskNecessarySingleStoreGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessarySingleStoreGoods)){
                        taskNecessarySingleStoreGoodsList.addAll(taskNecessarySingleStoreGoods);
                    }
                    trackRetultAllDetailIdList.addAll(trackRetultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()));
                }
                if (Objects.nonNull(levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode())))) {
                    List<TrackRetultAllDetail> retultAllDetails = levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()));
                    MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(singleStoreNecessaryData.getOrgNo());
                    if (Objects.isNull(mdmStoreExDTO)) {
                        singleStoreNecessaryData.setErrorReason("门店信息查询为空");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }
                    trackRetultAllDetailExtendMapper.delTrackRetultAllDetailById(taskId, retultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()));
                    List<String> platformStoreType = new ArrayList<>();
                    List<String> zsStoreType = new ArrayList<>();
                    for (TrackRetultAllDetail retultAllDetail : retultAllDetails) {
                        if (Constants.GOOD_FIRST.contains(retultAllDetail.getSubCategoryId().substring(0, 2))) {
                            platformStoreType.add(retultAllDetail.getOrgNo());
                        }
                        if (Constants.GOOD_SECOND.equals(retultAllDetail.getSubCategoryId().substring(0, 4))) {
                            zsStoreType.add(retultAllDetail.getOrgNo());
                        }
                    }
                    logger.info("platformStoreType:{}", JSONObject.toJSONString(platformStoreType));
                    logger.info("zsStoreType:{}", JSONObject.toJSONString(zsStoreType));
                    List<String> StoreTypeCodeList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(platformStoreType)) {
                        for (String s : platformStoreType) {
                            StoreTypeCodeList.add(mdmStoreExDTO.getStoreTypeCode());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(zsStoreType)) {
                        for (String s : zsStoreType) {
                            StoreTypeCodeList.add(mdmStoreExDTO.getZsStoreTypeCode());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(StoreTypeCodeList)) {
                        StoreTypeCodeList = StoreTypeCodeList.stream().distinct().collect(Collectors.toList());
                        logger.info("店型CodeList{}", JSONObject.toJSONString(StoreTypeCodeList));
                    }
                    Optional<OrgInfoBaseCache> businessBySapCode = CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId());
                    logger.info("连锁信息：{}", JSONObject.toJSONString(businessBySapCode));
                    if (!businessBySapCode.isPresent()){
                        singleStoreNecessaryData.setErrorReason("门店编码错误");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }
                    List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode(),BudnlTaskDetailDicEnum.ZS.getCode()),StoreTypeCodeList);

                    TrackRetultDetailParam param2 = new TrackRetultDetailParam();
                    param2.setTaskId(taskId);
                    param2.setCity(mdmStoreExDTO.getCity());
                    param2.setCompid(String.valueOf(businessBySapCode.get().getBusinessOrgId()));
                    param2.setGoodsId(singleStoreNecessaryData.getGoodsNo());
                    param2.setReviseStoreGroup(commonEnums.get(0).getEnumName());
                    param2.setLevelList(Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode())));
                    param2.setStatus(Constants.NORMAL_STATUS);
                    List<TrackRetultAllDetail> trackRetultAllDetails1 = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param2);
                    if (CollectionUtils.isEmpty(trackRetultAllDetails1)) {
                        //删除店型选配表
                        TaskNecessaryChooseStoreTypeGoodsExample example = new TaskNecessaryChooseStoreTypeGoodsExample();
                        example.createCriteria().andTaskIdEqualTo(taskId).andStoreTypeEqualTo(commonEnums.get(0).getEnumValue()).andCompanyOrgIdEqualTo(businessBySapCode.get().getBusinessOrgId()).andCityEqualTo(mdmStoreExDTO.getCity()).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                        List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods = taskNecessaryChooseStoreTypeGoodsMapper.selectByExample(example);
                        if (CollectionUtils.isNotEmpty(taskNecessaryChooseStoreTypeGoods)) {
                            taskNecessaryChooseStoreTypeGoodsList.addAll(taskNecessaryChooseStoreTypeGoods);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(taskNecessarySingleStoreGoodsList)){
                List<List<TaskNecessarySingleStoreGoods>> partition = Lists.partition(taskNecessarySingleStoreGoodsList, Constants.DELETE_MAX_SIZE);
                for (List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods : partition) {
                    taskNecessarySingleStoreGoodsExtendMapper.batchDel(taskNecessarySingleStoreGoods.stream().map(TaskNecessarySingleStoreGoods::getId).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.isNotEmpty(taskNecessaryChooseStoreTypeGoodsList)){
                List<List<TaskNecessaryChooseStoreTypeGoods>> partition = Lists.partition(taskNecessaryChooseStoreTypeGoodsList, Constants.DELETE_MAX_SIZE);
                for (List<TaskNecessaryChooseStoreTypeGoods> taskecessaryChooseStoreTypeGoods : partition) {
                    taskNecessaryChooseStoreTypeGoodsExtendMapper.batchDel(taskecessaryChooseStoreTypeGoods.stream().map(TaskNecessaryChooseStoreTypeGoods::getId).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.isNotEmpty(trackRetultAllDetailIdList)){
                List<List<Long>> partition = Lists.partition(trackRetultAllDetailIdList, Constants.DELETE_MAX_SIZE);
                for (List<Long> trackRetultAllDetailId : partition) {
                    trackRetultAllDetailExtendMapper.delTrackRetultAllDetailById(taskId, trackRetultAllDetailId);
                }
            }
            repeatDataList.clear();
            String fileName = taskId + "_单店级必备明细_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, SingleStoreNecessaryData.class).sheet("单店级必备明细").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("单店级必备明细错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }

            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        }catch (Exception e){
            logger.error("数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }


    @Async("trackResultFileUploadExecutor")
    public void delStoreTypeGoods(List<StoreTypeNecessaryData> dataList, Long taskId, BundlingTaskInfo bundlingTaskInfo, ImportResult result, String key, RBucket<ImportResult> rBucket) {
        try{
            ArrayList<StoreTypeNecessaryData> errorData = new ArrayList<>();
            ArrayList<StoreTypeNecessaryData> repeatDataList = new ArrayList<>();
            if (dataList.size() <= 0) {
                throw new BusinessErrorException("导入文件数据为空");
            }
            List<StoreTypeNecessaryData> necessaryData = dataList.stream().collect(Collectors.toList());
            List<String> goodsNoList = dataList.stream().map(StoreTypeNecessaryData::getGoodsNo).distinct().collect(Collectors.toList());
            List<String> commonStoreTypeList = dataList.stream().map(StoreTypeNecessaryData::getStoreType).distinct().collect(Collectors.toList());
            Map<String, SpuListVo> spuMap = new HashMap<>();
            Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));

            CommonEnumsExample commonEnumsExample1 = new CommonEnumsExample();
            commonEnumsExample1.createCriteria().andEnumNameIn(commonStoreTypeList);
            List<CommonEnums> commonEnumsList = commonEnumsMapper.selectByExample(commonEnumsExample1);

            ArrayList<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoodsArrayList = new ArrayList<>();
            for (StoreTypeNecessaryData storeTypeNecessaryData : necessaryData) {
                if (StringUtils.isEmpty(storeTypeNecessaryData.getCity()) || StringUtils.isEmpty(storeTypeNecessaryData.getStoreType()) || StringUtils.isEmpty(storeTypeNecessaryData.getGoodsNo())) {
                    storeTypeNecessaryData.setErrorReason("城市、店型、商品不能为空");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }
                if (Objects.isNull(spuMap.get(storeTypeNecessaryData.getGoodsNo()))){
                    storeTypeNecessaryData.setErrorReason("商品信息不存在");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }

                if (null == storeTypeNecessaryData.getCompanyOrgId()) {
                    storeTypeNecessaryData.setErrorReason("公司Id不能为空");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }

                if (repeatDataList.contains(storeTypeNecessaryData)) {
                    storeTypeNecessaryData.setErrorReason("表格中数据重复");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }
                Optional<OrgInfoBaseCache> businessByOrgId = CacheVar.getBusinessByOrgId(storeTypeNecessaryData.getCompanyOrgId());
                if (!businessByOrgId.isPresent()) {
                    storeTypeNecessaryData.setErrorReason("公司Id在组织机构下不存在");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }
                repeatDataList.add(storeTypeNecessaryData);

                //根据传入的编码获取店型判断是否存在
                if (!commonEnumsList.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()).contains(storeTypeNecessaryData.getStoreType())) {
                    storeTypeNecessaryData.setErrorReason("店型不存在");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }

                List<String> levelList = new ArrayList<>();
                if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
                    levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()), String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                } else if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())) {
                    levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                } else {
                    levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                }
                List<TrackRetultAllDetail> trackRetultAllDetails1 = selectAllDetail(bundlingTaskInfo, storeTypeNecessaryData.getCompanyOrgId().toString(), storeTypeNecessaryData.getStoreType(), storeTypeNecessaryData.getGoodsNo(), storeTypeNecessaryData.getCity(), levelList);
                logger.info("查询表四结果：{}", trackRetultAllDetails1.size());
                //先删除表④中对应的行
                if (CollectionUtils.isEmpty(trackRetultAllDetails1)) {
                    storeTypeNecessaryData.setErrorReason("该行在一店一目表中没有对应的必备明细,不需要删除。");
                    errorData.add(storeTypeNecessaryData);
                    continue;
                }
                Map<String, List<TrackRetultAllDetail>> levelMap = trackRetultAllDetails1.stream().collect(Collectors.groupingBy(TrackRetultAllDetail::getLevel));
                //根据任务id 查询任务类型 根据类型就知道是平台组货 还是企业组货  或者是店型组货 现在将类型传入 不需要查询
                //根据文件条件 查询全层级数据  如果是企业组货 查出来的数据是平台必备则不删除
                if (Objects.nonNull(levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()))) && bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
                    List<TrackRetultAllDetail> retultAllDetails = levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()));
                    retultAllDetails.stream().forEach(v -> {
                        v.setStatus(Constants.DEL_STATUS);
                    });
                    trackRetultAllDetailExtendMapper.delTrackRetultAllDetailById(taskId, retultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()));
                    //查询删除后sku的有效行
                    TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
                    trackRetultDetailParam.setGoodsId(storeTypeNecessaryData.getGoodsNo());
                    trackRetultDetailParam.setTaskId(taskId);
                    trackRetultDetailParam.setLevelList(Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode())));
                    trackRetultDetailParam.setStatus(Constants.NORMAL_STATUS);
                    List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
                    logger.info("查询表四有效行结果：{}", trackRetultAllDetails.size());
                    List<String> platformStoreType = new ArrayList<>();
                    List<String> zsStoreType = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(trackRetultAllDetails)) {
                        for (TrackRetultAllDetail trackRetultAllDetail : trackRetultAllDetails) {
                            if (Constants.GOOD_FIRST.contains(trackRetultAllDetail.getSubCategoryId().substring(0, 2))) {
                                platformStoreType.add(trackRetultAllDetail.getOrgNo());
                            }
                            if (Constants.GOOD_SECOND.equals(trackRetultAllDetail.getSubCategoryId().substring(0, 4))) {
                                zsStoreType.add(trackRetultAllDetail.getOrgNo());
                            }
                        }
                    }

                    List<String> StoreTypeCodeList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(platformStoreType)) {
                        for (String s : platformStoreType) {
                            MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(s);
                            if (null == mdmStoreExDTO) {
                                logger.error("门店:{}没有查询到缓存", s);
                                continue;
                            }
                            StoreTypeCodeList.add(mdmStoreExDTO.getPlatStoreTypeCode());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(zsStoreType)) {
                        for (String s : zsStoreType) {
                            MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(s);
                            if (null == mdmStoreExDTO) {
                                logger.error("门店:{}没有查询到缓存", s);
                                continue;
                            }
                            StoreTypeCodeList.add(mdmStoreExDTO.getZsStoreTypeCode());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(StoreTypeCodeList)) {
                        StoreTypeCodeList = StoreTypeCodeList.stream().distinct().collect(Collectors.toList());
                    }


                    TaskNecessaryPlatformParam taskNecessaryPlatformParam = new TaskNecessaryPlatformParam();
                    taskNecessaryPlatformParam.setGoodsNo(storeTypeNecessaryData.getGoodsNo());
                    taskNecessaryPlatformParam.setPlatformOrgId(bundlingTaskInfo.getOrgId());
                    taskNecessaryPlatformParam.setTaskId(taskId);
                    List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsExtendMapper.queryNecessaryPlatformGoodList(taskNecessaryPlatformParam);
                    List<String> finalStoreTypeCodeList = StoreTypeCodeList;
                    List<TaskNecessaryPlatformGoods> platformGoodsList = taskNecessaryPlatformGoods.stream().filter(v -> !finalStoreTypeCodeList.contains(v.getStoreType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(platformGoodsList)){
                        taskNecessaryPlatformGoodsExtendMapper.batchDel(platformGoodsList.stream().map(TaskNecessaryPlatformGoods::getId).collect(Collectors.toList()));
                    }
                    /*for (TaskNecessaryPlatformGoods taskNecessaryPlatformGood : taskNecessaryPlatformGoods) {
                        if (!StoreTypeCodeList.contains(taskNecessaryPlatformGood.getStoreType())) {
                            taskNecessaryPlatformGoodsMapper.deleteByPrimaryKey(taskNecessaryPlatformGood.getId());
                        }
                    }*/
                }
                if (Objects.nonNull(levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode())))) {
                    List<TrackRetultAllDetail> retultAllDetails = levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()));
                    /*TaskNecessaryPlatformGoodsExample example = new TaskNecessaryPlatformGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(taskId).andGoodsNoEqualTo(storeTypeNecessaryData.getGoodsNo()).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId());
                    List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsMapper.selectByExample(example);
                    List<String> ptStoreType=new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(taskNecessaryPlatformGoods)){
                        ptStoreType = taskNecessaryPlatformGoods.stream().map(TaskNecessaryPlatformGoods::getStoreType).collect(Collectors.toList());
                    }
                    logger.info("ptStoreType:{}",ptStoreType);
                    List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoodList = new ArrayList<>();
                    List<CommonEnums> commonEnums1 = commonEnumsExtendMapper.selectByDicCode(commonEnums.get(0).getPropertyCode());
                    List<CommonEnums> otherCommonEnum = commonEnums1.stream().filter(v -> !v.getEnumValue().equals(commonEnums.get(0).getEnumValue())).collect(Collectors.toList());
                    for (CommonEnums enums : otherCommonEnum) {
                        if (enums.getPropertyCode().equals(BudnlTaskDetailDicEnum.STORE.getCode()) && CollectionUtils.isNotEmpty(ptStoreType)) {
                            //平台必备中存在 就要排除平台必备中得店型(包含中参和组货店型)
                            for (String platStoreTypeCode : ptStoreType) {
                                List list = storeTypeByPlat(platStoreTypeCode);
                                if (CollectionUtils.isEmpty(list)) {
                                    TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods = new TaskNecessaryStoreTypeGoods();
                                    buildTaskNecessaryStoreTypeDTO(storeTypeNecessaryData, taskId, enums, taskNecessaryStoreTypeGoods, retultAllDetails.get(0));
                                    taskNecessaryStoreTypeGoodList.add(taskNecessaryStoreTypeGoods);
                                }
                            }

                        } else if (enums.getPropertyCode().equals(BudnlTaskDetailDicEnum.ZS.getCode()) && CollectionUtils.isNotEmpty(ptStoreType)) {
                            if (!ptStoreType.contains(enums.getEnumValue())) {
                                TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods = new TaskNecessaryStoreTypeGoods();
                                buildTaskNecessaryStoreTypeDTO(storeTypeNecessaryData, taskId, enums, taskNecessaryStoreTypeGoods, retultAllDetails.get(0));
                                taskNecessaryStoreTypeGoodList.add(taskNecessaryStoreTypeGoods);
                            }
                        } else {
                            TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods = new TaskNecessaryStoreTypeGoods();
                            buildTaskNecessaryStoreTypeDTO(storeTypeNecessaryData, taskId, enums, taskNecessaryStoreTypeGoods, retultAllDetails.get(0));
                            taskNecessaryStoreTypeGoodList.add(taskNecessaryStoreTypeGoods);
                        }
                    }*/
                    //删除表4
                    retultAllDetails.stream().forEach(v -> {
                        v.setStatus(Constants.DEL_STATUS);
                    });
                    trackRetultAllDetailExtendMapper.delTrackRetultAllDetailById(taskId, retultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()));
                    //删除企业必备表对应行，在店型必备表中新增其他店型的行，表④一店一目表配置类型改成店型必备
                    //删除表7-3数据
                    TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                    taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andCompanyOrgIdEqualTo(storeTypeNecessaryData.getCompanyOrgId()).andCityEqualTo(storeTypeNecessaryData.getCity()).andGoodsNoEqualTo(storeTypeNecessaryData.getGoodsNo());
                    List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
                    if (CollectionUtils.isNotEmpty(taskNecessaryCompanyGoods)) {
                        taskNecessaryCompanyGoodsExtendMapper.batchDel(taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getId).collect(Collectors.toList()));
                    }
                    //数据插入表7-4
                    levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()));
                    storeTypeNecessaryData.setStoreType(null);
                    List<TrackRetultAllDetail> trackRetultAllDetails = selectAllDetail(bundlingTaskInfo, storeTypeNecessaryData.getCompanyOrgId().toString(), storeTypeNecessaryData.getStoreType(), storeTypeNecessaryData.getGoodsNo(), storeTypeNecessaryData.getCity(), levelList);

                    //List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
                    List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoodList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(trackRetultAllDetails)){
                        trackRetultAllDetailExtendMapper.updateLevel(taskId,trackRetultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                        //一店一目中的店型List
                        List<String> storeTypeList = trackRetultAllDetails.stream().map(TrackRetultAllDetail::getReviseStoreGroup).distinct().collect(Collectors.toList());
                        //查询到枚举中的店型对应的数字
                        List<CommonEnums> commonEnums1 = commonEnumsList.stream().filter(v -> storeTypeList.contains(v.getEnumName())).collect(Collectors.toList());
                        logger.info("查询到枚举中的店型对应的数字数据为：{}",JSONObject.toJSONString(commonEnums1));
                        for (CommonEnums enums : commonEnums1) {
                            TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods = new TaskNecessaryStoreTypeGoods();
                            buildTaskNecessaryStoreTypeDTO(storeTypeNecessaryData, taskId, enums, taskNecessaryStoreTypeGoods, trackRetultAllDetails.get(0),spuMap);
                            taskNecessaryStoreTypeGoodList.add(taskNecessaryStoreTypeGoods);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(taskNecessaryStoreTypeGoodList)){
                        taskNecessaryStoreTypeGoodsExtendMapper.batchInsert(taskNecessaryStoreTypeGoodList);
                    }
                }
                if (Objects.nonNull(levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode())))) {
                    List<TrackRetultAllDetail> retultAllDetails = levelMap.get(String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                    retultAllDetails.stream().forEach(v -> {
                        v.setStatus(Constants.DEL_STATUS);
                    });
                    trackRetultAllDetailExtendMapper.delTrackRetultAllDetailById(taskId, retultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()));
                    Optional<CommonEnums> commonEnum = commonEnumsList.stream().filter(v -> v.getEnumName().equals(storeTypeNecessaryData.getStoreType())).findFirst();
                    if (!commonEnum.isPresent()){
                        continue;
                    }
                    TaskNecessaryStoreTypeGoodsExample taskNecessaryStoreTypeGoodsExample = new TaskNecessaryStoreTypeGoodsExample();
                    taskNecessaryStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andCompanyOrgIdEqualTo(storeTypeNecessaryData.getCompanyOrgId()).andCityEqualTo(storeTypeNecessaryData.getCity()).andStoreTypeEqualTo(commonEnum.get().getEnumValue()).andGoodsNoEqualTo(storeTypeNecessaryData.getGoodsNo());
                    List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods = taskNecessaryStoreTypeGoodsMapper.selectByExample(taskNecessaryStoreTypeGoodsExample);
                    if (CollectionUtils.isNotEmpty(taskNecessaryStoreTypeGoods)){
                        taskNecessaryStoreTypeGoodsArrayList.addAll(taskNecessaryStoreTypeGoods);
                        //taskNecessaryStoreTypeGoodsExtendMapper.batchDel(taskNecessaryStoreTypeGoods.stream().map(TaskNecessaryStoreTypeGoods::getId).collect(Collectors.toList()));
                    }
                }
            }
            repeatDataList.clear();
            if (CollectionUtils.isNotEmpty(taskNecessaryStoreTypeGoodsArrayList)){
                List<List<TaskNecessaryStoreTypeGoods>> partition = Lists.partition(taskNecessaryStoreTypeGoodsArrayList, Constants.DELETE_MAX_SIZE);
                for (List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods : partition) {
                    taskNecessaryStoreTypeGoodsExtendMapper.batchDel(taskNecessaryStoreTypeGoods.stream().map(TaskNecessaryStoreTypeGoods::getId).collect(Collectors.toList()));
                }
            }
            String fileName = taskId + "_店型级必备明细_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, StoreTypeNecessaryData.class).sheet("店型级必备明细").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("店型级必备明细错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        }catch (Exception e){
            logger.error("数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }

    }

    @Async("trackResultFileUploadExecutor")
    public void platformGoods(List<PlatformGoodsData> dataList, Long taskId, ImportResult result, String key, RBucket<ImportResult> rBucket) {
        try{
            ArrayList<PlatformGoodsData> errorData = new ArrayList<>();
            ArrayList<PlatformGoodsData> repeatDataList = new ArrayList<>();
            BundlingTaskInfo bundlingTaskInfo = getBundlingTaskInfo(taskId);
            List<String> goodsNoList = dataList.stream().map(PlatformGoodsData::getGoodsNo).distinct().collect(Collectors.toList());

            Map<String, SpuListVo> spuMap = new HashMap<>();
            Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
            //集团必备查重
            List<String> existsGoods = necessaryGroupGoodsExtendMapper.selectGroupExistsGoods(bundlingTaskInfo.getOrgId(), goodsNoList, NecessaryTagEnum.GROUP_NECESSARY.getCode());
            //校验商品是不是组或商品类型
            List<String> taskGoodTypeList = getTaskGoodTypeList(taskId);

            for (PlatformGoodsData platformGoodsData : dataList) {
                if (StringUtils.isEmpty(platformGoodsData.getGoodsNo()) || StringUtils.isEmpty(platformGoodsData.getStoreType())) {
                    platformGoodsData.setErrorReason("商品编码、店型不能为空");
                    errorData.add(platformGoodsData);
                    continue;
                }
                if (repeatDataList.contains(platformGoodsData)) {
                    platformGoodsData.setErrorReason("表格中数据重复");
                    errorData.add(platformGoodsData);
                    continue;
                }
                repeatDataList.add(platformGoodsData);
                if (CollectionUtils.isNotEmpty(existsGoods) && existsGoods.contains(platformGoodsData.getGoodsNo())) {
                    platformGoodsData.setErrorReason("该行商品在集团必备中存在，数据无效");
                    errorData.add(platformGoodsData);
                    //导入数据无效   集团必备有数据
                    continue;
                }
                List<CommonEnums> commonEnums1 = commonEnumsExtendMapper.selectByDicName(platformGoodsData.getStoreType());
                if (CollectionUtils.isEmpty(commonEnums1)) {
                    platformGoodsData.setErrorReason("该行店型不存在，数据无效");
                    errorData.add(platformGoodsData);
                    //导入数据无效   集团必备有数据
                    continue;
                }

                List<String> stringList = storeTypeByPlat(commonEnums1.get(0).getEnumValue());

                SpuListVo spuListVo = spuMap.get(platformGoodsData.getGoodsNo());
                //校验商品是否在商品组货类型的范围下
                if (StringUtils.isNotEmpty(checkTaskGoodsType(taskGoodTypeList, spuListVo))) {
                    platformGoodsData.setErrorReason(checkTaskGoodsType(taskGoodTypeList, spuListVo));
                    errorData.add(platformGoodsData);
                    continue;
                }
                //平台必备查重
                TaskNecessaryPlatformGoodsExample example = new TaskNecessaryPlatformGoodsExample();
                //中心大店  和  11_社商_大店
                example.createCriteria().andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andTaskIdEqualTo(taskId).andGoodsNoEqualTo(platformGoodsData.getGoodsNo()).andStoreTypeIn(Arrays.asList(commonEnums1.get(0).getEnumValue()));
                List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(taskNecessaryPlatformGoods)) {
                    //导入数据无效
                    platformGoodsData.setErrorReason("该行商品在平台必备中存在，数据无效");
                    errorData.add(platformGoodsData);
                    continue;
                }

                //不重复，允许添加
                if (Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2))) {
                    //是平台组货店型
                    List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCode("PlatStoreGroup");
                    if (CollectionUtils.isEmpty(commonEnums)) {
                        continue;
                    }
                    if (!commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()).contains(platformGoodsData.getStoreType())) {
                        platformGoodsData.setErrorReason("商品和店型不符，数据无效");
                        errorData.add(platformGoodsData);
                        continue;
                    }

                    List<CommonEnums> commonEnumsTemp = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode()),stringList);
                    platformData(taskId, bundlingTaskInfo, platformGoodsData, spuListVo, commonEnums,commonEnumsTemp);
                } else if (Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4))) {
                    //是中参店型
                    List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCode("ZsStoreGroup");
                    if (CollectionUtils.isEmpty(commonEnums)) {
                        continue;
                    }
                    if (!commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()).contains(platformGoodsData.getStoreType())) {
                        platformGoodsData.setErrorReason("商品和店型不符，数据无效");
                        errorData.add(platformGoodsData);
                        continue;
                    }
                    platformData(taskId, bundlingTaskInfo, platformGoodsData, spuListVo, commonEnums,commonEnums1);
                } else {
                    continue;
                }

            }
            repeatDataList.clear();
            String fileName = taskId + "_平台必备商品导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, PlatformGoodsData.class).sheet("平台必备商品导入").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("平台必备商品导入错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        }catch (Exception e){
            logger.error("平台必备导入数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("平台必备导入数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }

    }

    private String checkTaskGoodsType(List<String> taskGoodTypeList, SpuListVo spuListVo) {
        if (Objects.nonNull(spuListVo) && StringUtils.isNotEmpty(spuListVo.getCategoryId())) {
            Map<Long, CommonCategoryDTO> categoryBySubIds = getCategoryBySubIds(Arrays.asList(Long.valueOf(spuListVo.getCategoryId())));
            if (MapUtils.isNotEmpty(categoryBySubIds)) {
                if (MapUtils.isNotEmpty(categoryBySubIds) && Objects.nonNull(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())))) {
                    if (taskGoodTypeList.contains(String.valueOf(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryId())) ||
                            taskGoodTypeList.contains(String.valueOf(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryId()))) {
                        return null;
                    } else {
                        return "该行商品不在商品组货范围内，数据无效";
                    }
                }
            } else {
                return "该行商品查询不到大中小子类信息，数据无效";
            }
        }
        return "该行商品查询不到商品信息，数据无效";
    }

    private List<String> getTaskGoodTypeList(Long taskId) {
        List<String> collect = new ArrayList<>();
        List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskId);
        List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodCategoryList)) {
            new ArrayList<>();
        }
        collect = goodCategoryList.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
        return collect;
    }

    private List<String> getTaskStoreTypeList(Long taskId) {
        List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskId);
        List<String> collect = new ArrayList<>();
        List<BundlingTaskDetail> zsStoreList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(zsStoreList)) {
            List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(null, zsStoreList.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList()));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(commonEnums)) {
                collect.addAll(commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()));
            }
        }
        List<BundlingTaskDetail> zhStoreList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.STORE.getCode())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(zhStoreList)) {
            List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode()), zhStoreList.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList()));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(commonEnums)) {
                collect.addAll(commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()));
            }
        }
        return collect;
    }

    @Async("trackResultFileUploadExecutor")
    public void companyGoods(List<CompanyGoodsData> dataList, Long taskId, BundlingTaskInfo bundlingTaskInfo, ImportResult result, String key, RBucket<ImportResult> rBucket) {
        try{
            ArrayList<CompanyGoodsData> errorData = new ArrayList<>();
            ArrayList<CompanyGoodsData> repeatDataList = new ArrayList<>();

            List<String> goodsNoList = dataList.stream().map(CompanyGoodsData::getGoodsNo).collect(Collectors.toList());
            Map<String, SpuListVo> spuMap = new HashMap<>();
            List<String> taskGoodTypeList = getTaskGoodTypeList(taskId);
            Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);

            //集团必备查重
            List<String> existsGoods = necessaryGroupGoodsExtendMapper.selectGroupExistsGoods(bundlingTaskInfo.getOrgId(), goodsNoList, NecessaryTagEnum.GROUP_NECESSARY.getCode());

            for (CompanyGoodsData companyGoodsData : dataList) {
                if (StringUtils.isBlank(companyGoodsData.getGoodsNo())||StringUtils.isBlank(companyGoodsData.getCity())||null==companyGoodsData.getOrgId()){
                    companyGoodsData.setErrorReason("表格中商品编码，城市，组织机构信息不能为空");
                    errorData.add(companyGoodsData);
                    continue;
                }
                SpuListVo spuListVo = spuMap.get(companyGoodsData.getGoodsNo());
                if (Objects.isNull(spuListVo)) {
                    companyGoodsData.setErrorReason("商品信息不存在");
                    errorData.add(companyGoodsData);
                    continue;
                }
                //校验商品是否在商品组货类型的范围下
                if (StringUtils.isNotEmpty(checkTaskGoodsType(taskGoodTypeList, spuListVo))) {
                    companyGoodsData.setErrorReason(checkTaskGoodsType(taskGoodTypeList, spuListVo));
                    errorData.add(companyGoodsData);
                    continue;
                }
                if (existsGoods.contains(companyGoodsData.getGoodsNo())) {
                    companyGoodsData.setErrorReason("商品在集团必备中存在，不能新增为企业必备");
                    errorData.add(companyGoodsData);
                    continue;
                }
                //todo 企业Id查任务门店表 不存在报错
                //todo  校验表格中的企业Id  是不是在任务中的企业Id范围内   根据任务类型去判断马东的表还是大数据下发的表   店型在不在范围内   商品范围所有新增
                if (StringUtils.isNotEmpty(checkCompanyAndCity(bundlingTaskStoreDetailList, companyGoodsData.getOrgId(), companyGoodsData.getCity()))) {
                    companyGoodsData.setErrorReason(checkCompanyAndCity(bundlingTaskStoreDetailList, companyGoodsData.getOrgId(), companyGoodsData.getCity()));
                    errorData.add(companyGoodsData);
                    continue;
                }

                boolean ptStoreType = Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2));
                boolean zsStoreType = Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4));
                if (!ptStoreType && !zsStoreType) {
                    companyGoodsData.setErrorReason("商品不是平台组货店型，也不是中参店型");
                    errorData.add(companyGoodsData);
                    continue;
                }
                if (repeatDataList.contains(companyGoodsData)) {
                    companyGoodsData.setErrorReason("表格中数据重复");
                    errorData.add(companyGoodsData);
                    continue;
                }
                List<CommonEnums> platStoreGroup = commonEnumsExtendMapper.selectByDicCode("PlatStoreGroup");
                List<CommonEnums> zsStoreGroup = commonEnumsExtendMapper.selectByDicCode("ZsStoreGroup");
                if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
                    //平台必备查重
                    TaskNecessaryPlatformGoodsExample taskNecessaryPlatformGoodsExample = new TaskNecessaryPlatformGoodsExample();
                    List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods=new ArrayList<>();
                    if (ptStoreType){
                        taskNecessaryPlatformGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andStoreTypeIn( platStoreGroup.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList())).andGoodsNoEqualTo(companyGoodsData.getGoodsNo());
                        taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsMapper.selectByExample(taskNecessaryPlatformGoodsExample);
                        if (CollectionUtils.isNotEmpty(taskNecessaryPlatformGoods) && taskNecessaryPlatformGoods.size() == platStoreGroup.size()) {
                            companyGoodsData.setErrorReason("商品在平台必备中存在，不能新增为企业必备");
                            errorData.add(companyGoodsData);
                            continue;
                        }
                    }
                    if (zsStoreType){
                        taskNecessaryPlatformGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andStoreTypeIn( zsStoreGroup.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList())).andGoodsNoEqualTo(companyGoodsData.getGoodsNo());
                        taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsMapper.selectByExample(taskNecessaryPlatformGoodsExample);
                        if (CollectionUtils.isNotEmpty(taskNecessaryPlatformGoods) && taskNecessaryPlatformGoods.size()== zsStoreGroup.size()) {
                            companyGoodsData.setErrorReason("商品在平台必备中存在，不能新增为企业必备");
                            errorData.add(companyGoodsData);
                            continue;
                        }
                    }

                } else if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())) {
                    //平台必备查重
                    List<NecessaryTaskGoodsCommonDTO> necessaryTaskGoodsCommonDTOS=new ArrayList<>();
                    if (ptStoreType){
                        necessaryTaskGoodsCommonDTOS = necessaryPlatformGoodsExtendMapper.selectExistsGoodsAndStoreType(bundlingTaskInfo.getOrgId(), platStoreGroup.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()), Arrays.asList(companyGoodsData.getGoodsNo()));
                        if (CollectionUtils.isNotEmpty(necessaryTaskGoodsCommonDTOS) && necessaryTaskGoodsCommonDTOS.size() == platStoreGroup.size()) {
                            companyGoodsData.setErrorReason("商品在平台必备中存在，不能新增为企业必备");
                            errorData.add(companyGoodsData);
                            continue;
                        }
                    }
                    if (zsStoreType){
                        necessaryTaskGoodsCommonDTOS = necessaryPlatformGoodsExtendMapper.selectExistsGoodsAndStoreType(bundlingTaskInfo.getOrgId(), zsStoreGroup.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()), Arrays.asList(companyGoodsData.getGoodsNo()));
                        if (CollectionUtils.isNotEmpty(necessaryTaskGoodsCommonDTOS) && necessaryTaskGoodsCommonDTOS.size() == zsStoreGroup.size()) {
                            companyGoodsData.setErrorReason("商品在平台必备中存在，不能新增为企业必备");
                            errorData.add(companyGoodsData);
                            continue;
                        }
                    }
                }

                repeatDataList.add(companyGoodsData);

                //企业必备查重
                TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(companyGoodsData.getOrgId()).andGoodsNoEqualTo(companyGoodsData.getGoodsNo()).andCityEqualTo(companyGoodsData.getCity());
                List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
                if (CollectionUtils.isNotEmpty(taskNecessaryCompanyGoods)) {
                    //导入数据无效
                    companyGoodsData.setErrorReason("商品在企业必备中存在，导入数据无效");
                    errorData.add(companyGoodsData);
                    continue;
                }
                Optional<OrgInfoBaseCache> storeByOrgId = CacheVar.getBusinessByOrgId(companyGoodsData.getOrgId());
                if (!storeByOrgId.isPresent()) {
                    companyGoodsData.setErrorReason("获取门店信息为空，门店信息不存在");
                    errorData.add(companyGoodsData);
                    continue;
                }
                Map<Long, CommonCategoryDTO> categoryBySubIds = getCategoryBySubIds(Arrays.asList(Long.valueOf(spuListVo.getCategoryId())));
                TaskNecessaryCompanyGoods taskNecessaryCompanyGoods1 = new TaskNecessaryCompanyGoods();
                buildTaskNecessaryCompanyGoods(bundlingTaskInfo, companyGoodsData, spuListVo, storeByOrgId, categoryBySubIds, taskNecessaryCompanyGoods1);
                taskNecessaryCompanyGoodsMapper.insertSelective(taskNecessaryCompanyGoods1);
                //查询门店List
                List<MdmStoreExDTO> mdmStoreExDTOS = new ArrayList<>();
                if (ptStoreType){
                    List<CommonEnums> commonEnums1  = commonEnumsExtendMapper.selectByDicCode(BudnlTaskDetailDicEnum.STORE.getCode());
                    mdmStoreExDTOS = bundlTaskService.selectMdmStoreByTaskId(taskId, companyGoodsData.getOrgId(), companyGoodsData.getCity(), null, commonEnums1.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()), null);
                }
                if (zsStoreType){
                    List<CommonEnums> commonEnums1 = commonEnumsExtendMapper.selectByDicCode(BudnlTaskDetailDicEnum.ZS.getCode());
                    mdmStoreExDTOS = bundlTaskService.selectMdmStoreByTaskId(taskId, companyGoodsData.getOrgId(), companyGoodsData.getCity(), null, null, commonEnums1.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()));
                }
                if (CollectionUtils.isEmpty(mdmStoreExDTOS)){
                    companyGoodsData.setErrorReason("城市下没有门店");
                    errorData.add(companyGoodsData);
                    continue;
                }
               //门店list*SKU在在一店一目表查询，存在&配置类型!=平台必备，则更新，不存在则新增，配置类型=企业必备、最小陈列量=1。存在&配置类型=平台必备，不做调整。

                TrackRetultDetailParam param = new TrackRetultDetailParam();
                param.setTaskId(bundlingTaskInfo.getId());
                param.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
                param.setGoodsId(companyGoodsData.getGoodsNo());
                param.setOrgNoList(mdmStoreExDTOS.stream().map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList()));
                param.setStatus(Constants.NORMAL_STATUS);
                List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);

                List<String> StoreNoList = mdmStoreExDTOS.stream().map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList());

                List<String> tempStoreNos = trackRetultAllDetails.stream().map(TrackRetultAllDetail::getOrgNo).collect(Collectors.toList());

                List<String> insertStoreNos = StoreNoList.stream().filter(v -> !tempStoreNos.contains(v)).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(trackRetultAllDetails)) {
                    List<TrackRetultAllDetail> otherLevelGoods = trackRetultAllDetails.stream().filter(v -> !Byte.valueOf(v.getLevel()).equals(NecessaryGoodsLevelEnum.SECEND.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(otherLevelGoods)) {
                        trackRetultAllDetailExtendMapper.updateLevel(taskId,otherLevelGoods.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()),String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()));
                    }
                }
                if (CollectionUtils.isNotEmpty(insertStoreNos)){
                    //新增
                    ArrayList<TrackRetultAllDetail> trackRetultAllDetailArrayList = new ArrayList<>();
                    for (String insertStoreNo : insertStoreNos) {

                        TrackRetultAllDetail trackRetultAllDetail = new TrackRetultAllDetail();
                        trackRetultAllDetail.setLevel(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()));
                        trackRetultAllDetail.setGoodsId(companyGoodsData.getGoodsNo());
                        trackRetultAllDetail.setTaskId(taskId);
                        trackRetultAllDetail.setZoneNew(bundlingTaskInfo.getOrgName());
                        trackRetultAllDetail.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
                        trackRetultAllDetail.setOrgNo(insertStoreNo);
                        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(insertStoreNo);
                        if (Objects.nonNull(mdmStoreExDTO)){
                            trackRetultAllDetail.setCity(mdmStoreExDTO.getCity());
                            Optional<OrgInfoBaseCache> businessOrgByBusinessId = CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId());
                            if (businessOrgByBusinessId.isPresent()){
                                trackRetultAllDetail.setCompid(String.valueOf(businessOrgByBusinessId.get().getBusinessOrgId()));
                                trackRetultAllDetail.setDataFrom(businessOrgByBusinessId.get().getBusinessShortName());
                                trackRetultAllDetail.setDataFromV2(businessOrgByBusinessId.get().getBusinessShortName());
                                trackRetultAllDetail.setStoreName(businessOrgByBusinessId.get().getShortName());
                            }
                            if (Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2))) {
                                trackRetultAllDetail.setReviseStoreGroup(mdmStoreExDTO.getStoreType());
                            }else if (Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4))) {
                                trackRetultAllDetail.setReviseStoreGroup(zsStoreTypeNameMapping.get(mdmStoreExDTO.getZsStoreTypeCode()));
                            }
                        }
                        setGoodsProperty(spuListVo, trackRetultAllDetail);
                        trackRetultAllDetail.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId().toString());
                        trackRetultAllDetail.setGmtCreate(new Date());
                        trackRetultAllDetail.setStatus(Constants.NORMAL_STATUS);
                        trackRetultAllDetailArrayList.add(trackRetultAllDetail);
                    }
                    if (CollectionUtils.isNotEmpty(trackRetultAllDetailArrayList)){
                        trackRetultAllDetailExtendMapper.batchInsert(taskId,trackRetultAllDetailArrayList);
                    }
                }
                List<BundlingTaskStoreDetail> cityTaskStoreDetailList = bundlingTaskStoreDetailList.stream().filter(v -> v.getCity().equals(companyGoodsData.getCity())).collect(Collectors.toList());
                if (Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2))) {
                    //是平台组货店型
                    List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCode("StoreGroup");
                    if (CollectionUtils.isEmpty(commonEnums)) {
                        continue;
                    }
                    List<String> storeTypeCode = cityTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getStoreTypeCode).distinct().collect(Collectors.toList());
                    //寻找城市下的店型
                    delCompanyGoods(bundlingTaskInfo, companyGoodsData, mdmStoreExDTOS,storeTypeCode);
                } else if (Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4))) {
                    //是中参店型
                    List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCode("ZsStoreGroup");
                    if (CollectionUtils.isEmpty(commonEnums)) {
                        continue;
                    }
                    List<String> ZsStoreTypeCode = cityTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getZsStoreTypeCode).distinct().collect(Collectors.toList());
                    delCompanyGoods(bundlingTaskInfo, companyGoodsData, mdmStoreExDTOS,ZsStoreTypeCode);
                } else {
                    continue;
                }
            }
            repeatDataList.clear();
            String fileName = taskId + "_企业必备商品导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, CompanyGoodsData.class).sheet("企业必备商品导入").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("企业必备商品导入错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        }catch (Exception e){
            logger.error("企业必备导入数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("企业必备导入数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }

    }

//    @Async("trackResultFileUploadExecutor")
    public void newStoreGoods(List<NewStoreGoodsData> dataList, Long taskId, BundlingTaskInfo bundlingTaskInfo, BundlingTaskStoreDetail detail, List<String> storeLicense, List<String> goodslineList,ImportResult result, String key, RBucket<ImportResult> rBucket) {
        try{
            ArrayList<NewStoreGoodsData> errorData = new ArrayList<>();

            List<String> goodsNoList = dataList.stream().map(NewStoreGoodsData::getGoodsNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            Map<String, SpuNewVo> spuMap = new HashMap<>();
            SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
            spuNewParamVo.setGoodsNoList(goodsNoList);
            spuNewParamVo.setBusinessId(detail.getBusinessId());
            Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getNewSpuMap(spuNewParamVo)));
            Map<String, AuctionSpuBaseInfo> auctionSpuMap = forestService.batchFindSpuProperty(goodsNoList, detail.getBusinessId()).stream().collect(Collectors.toMap(AuctionSpuBaseInfo::getGoodsNo, Function.identity(), (k1,k2) -> k1));
            // 存在的商品
            Map<String, TrackRetultNewStoreAllDetail> existsMap = trackRetultNewStoreAllDetailExtendMapper.selectByTaskIdAndGoodsIds(taskId, goodsNoList).stream().collect(Collectors.toMap(TrackRetultNewStoreAllDetail::getGoodsId, Function.identity(), (k1,k2) -> k1));
            List<TrackRetultNewStoreAllDetail> updates = new ArrayList<>();
            List<TrackRetultNewStoreAllDetail> inserts = new ArrayList<>();
            Iterator<NewStoreGoodsData> iterator = dataList.iterator();
            while (iterator.hasNext()) {
                NewStoreGoodsData goodsData = iterator.next();
                if (StringUtils.isBlank(goodsData.getGoodsNo())){
                    goodsData.setErrorReason("表格中商品编码不能为空");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                if (!(BundlBoolEnum.YES.getMessage().equals(goodsData.getOperateAble()) || BundlBoolEnum.NO.getMessage().equals(goodsData.getOperateAble()))) {
                    goodsData.setErrorReason("是否经营只能填写：是、否");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                BigDecimal fistrStockUpQty = null;
                try {
                    fistrStockUpQty = new BigDecimal(goodsData.getStockUpQty()).setScale(4, RoundingMode.HALF_UP);
                } catch (Exception e) {
                    goodsData.setErrorReason("首次备货数量只能填写数字");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                if (BundlBoolEnum.NO.getMessage().equals(goodsData.getOperateAble()) && fistrStockUpQty.compareTo(BigDecimal.ZERO) != 0) {
                    goodsData.setErrorReason("当是否经营=否时,首次备货数量需填写0");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                TrackRetultNewStoreAllDetail newStore = existsMap.get(goodsData.getGoodsNo());
                if (null != newStore) {
                    //是否经营=是） then 更新推荐清单该商品行的 建议首次备货数量=excel中的值， 备货库存成本金额 = 原值/原数量*新数量
                    if (BundlBoolEnum.YES.getMessage().equals(goodsData.getOperateAble())) {
                        newStore.setJyAble(BundlBoolEnum.YES.getCode());
                        newStore.setPhCost(newStore.getSuggestPhQty().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : newStore.getPhCost().divide(newStore.getSuggestPhQty(), 2, BigDecimal.ROUND_HALF_UP).multiply(fistrStockUpQty).setScale(4, RoundingMode.HALF_UP));
                        newStore.setSuggestPhQty(fistrStockUpQty);
                    } else {
                        newStore.setJyAble(BundlBoolEnum.NO.getCode());
                        newStore.setSuggestPhQty(fistrStockUpQty);
                        newStore.setPhCost(BigDecimal.ZERO);
                    }
                    updates.add(newStore);
                    iterator.remove();
                    continue;
                }
                SpuNewVo spuListVo = spuMap.get(goodsData.getGoodsNo());
                if (Objects.isNull(spuListVo)) {
                    goodsData.setErrorReason("本公司未经营该商品");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                AuctionSpuBaseInfo auctionSpuBaseInfo = auctionSpuMap.get(goodsData.getGoodsNo());
                if (CollectionUtils.isEmpty(storeLicense) || null == auctionSpuBaseInfo || !storeLicense.contains(auctionSpuBaseInfo.getBusiscopetag())) {
                    goodsData.setErrorReason("本门店无本商品的经营范围");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                if (Objects.nonNull(auctionSpuBaseInfo) && !goodslineList.contains(auctionSpuBaseInfo.getGoodsline())) {
                    goodsData.setErrorReason("该商品已淘汰作废或者是订购商品");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                if (BundlBoolEnum.NO.getMessage().equals(goodsData.getOperateAble())) {
                    goodsData.setErrorReason("该商品是否经营=否");
                    errorData.add(goodsData);
                    iterator.remove();
                    continue;
                }
                TrackRetultNewStoreAllDetail insert = new TrackRetultNewStoreAllDetail();
                insert.setTaskId(taskId);
                insert.setOrgNo(detail.getStoreCode());
                insert.setStoreName(detail.getStoreName());
                insert.setGoodsId(auctionSpuBaseInfo.getGoodsNo());
                insert.setGoodsname(spuListVo.getName());
                insert.setGoodsspec(spuListVo.getJhiSpecification());
                insert.setManufacturer(spuListVo.getFactoryid());
                insert.setJxCate1Name(spuListVo.getDosageformsid());
                insert.setGoodsunit(spuListVo.getGoodsunit());
                insert.setLevel("99手工添加");
                insert.setSuggestPhQty(fistrStockUpQty);
                insert.setPhCost(BigDecimal.ZERO);
                insert.setTaotaiType(StringUtils.isNotBlank(auctionSpuBaseInfo.getGoodsline()) ? auctionSpuBaseInfo.getGoodsline() : "");
                insert.setStjb(StringUtils.isNotBlank(auctionSpuBaseInfo.getPushlevel()) ? auctionSpuBaseInfo.getPushlevel() : "");
                insert.setGrossprofit(StringUtils.isNotBlank(auctionSpuBaseInfo.getPurchchannel()) ? auctionSpuBaseInfo.getPurchchannel() : "");
                insert.setSubCategoryId(spuListVo.getCategoryId().toString());
                insert.setComponent(StringUtils.isNotBlank(auctionSpuBaseInfo.getComponent()) ? auctionSpuBaseInfo.getComponent() : "");
                insert.setDtpgood(StringUtils.isNotBlank(auctionSpuBaseInfo.getDtpGoods()) ? auctionSpuBaseInfo.getDtpGoods() : "");
                insert.setDistribind(null == auctionSpuBaseInfo.getOtherProperty() ? "" : Optional.ofNullable(auctionSpuBaseInfo.getOtherProperty().getString("distribind")).orElse(""));
                insert.setRefretailprice(null == auctionSpuBaseInfo.getOtherProperty() ? "" : Optional.ofNullable(auctionSpuBaseInfo.getSpuOtherProperty().getString("refretailprice")).orElse(""));
                inserts.add(insert);
            }
            if (CollectionUtils.isNotEmpty(inserts)) {
                Map<Long, CommonCategoryDTO> categoryMap = getCategoryBySubIds(inserts.stream().map(v -> Long.valueOf(v.getSubCategoryId())).distinct().collect(Collectors.toList()));
                inserts.forEach(v -> {
                    CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(v.getSubCategoryId()));
                    v.setClassoneName(null == commonCategoryDTO ? "" : commonCategoryDTO.getCategoryName());
                    v.setClasstwoName(null == commonCategoryDTO ? "" : commonCategoryDTO.getMiddleCategoryName());
                    v.setClassthreeName(null == commonCategoryDTO ? "" : commonCategoryDTO.getSmallCategoryName());
                    v.setClassfourName(null == commonCategoryDTO ? "" : commonCategoryDTO.getSubCategoryName());
                });
                Lists.partition(inserts, Constants.INSERT_MAX_SIZE).forEach(v -> trackRetultNewStoreAllDetailExtendMapper.batchInsert(taskId, v));
            }
            if (CollectionUtils.isNotEmpty(updates)) {
                Lists.partition(updates, Constants.INSERT_MAX_SIZE).forEach(v -> trackRetultNewStoreAllDetailExtendMapper.batchUpdate(taskId, v));
            }
            String fileName = taskId + "_新店商品目录导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, NewStoreGoodsData.class).sheet("新店商品目录导入").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("新店商品目录导入错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        }catch (Exception e){
            logger.error("新店商品目录导入数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("新店商品目录导入数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }

    }

    private void setGoodsProperty(SpuListVo spuListVo, TrackRetultAllDetail trackRetultAllDetail) {
        trackRetultAllDetail.setGoodsunit(Objects.nonNull(spuListVo) ? spuListVo.getGoodsunit() : "");
        trackRetultAllDetail.setGoodsname(Objects.nonNull(spuListVo) ? spuListVo.getName() : "");
        trackRetultAllDetail.setComponent(Objects.nonNull(spuListVo) ? (StringUtils.isBlank(spuListVo.getComponent()) ? "" : spuListVo.getComponent()) : "");
        trackRetultAllDetail.setJxCate1Name(Objects.nonNull(spuListVo) ? (StringUtils.isBlank(spuListVo.getDosageformsid()) ? "" : spuListVo.getDosageformsid())  : "");
        trackRetultAllDetail.setGoodsspec(Objects.nonNull(spuListVo) ? spuListVo.getJhiSpecification() : "");
    }

    private String checkCompanyAndCity(List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList, Long companyOrgId, String city) {
        if (CollectionUtils.isNotEmpty(bundlingTaskStoreDetailList)) {
            List<Long> companyOrgIdList = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getCompanyOrgId).collect(Collectors.toList());
            if (!companyOrgIdList.contains(companyOrgId)) {
                return "该行的企业Id不在组货范围内";
            }
            List<String> cityList = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getCity).collect(Collectors.toList());
            if (!cityList.contains(city)) {
                return "该行的城市不在组货范围内";
            }
            return null;
        }
        return "组货任务企业为空";
    }
    private String checkStoreType(BundlingTaskInfo bundlingTaskInfo,List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList,List<BundlingTaskDetail> bundlingTaskDetails,boolean zsType, boolean zhType,String storeType) {

        List<BundlingTaskStoreDetail> collect = bundlingTaskStoreDetailList.stream().filter(v -> v.getBundlConfirmAble().equals(BundlBoolEnum.YES.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            if (zsType){
                List<String> storeZsStoreType = collect.stream().map(BundlingTaskStoreDetail::getZsStoreTypeCode).distinct().collect(Collectors.toList());
                List<BundlingTaskDetail> bundlingTaskDetails1 = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())).collect(Collectors.toList());
                List<String> BundTaskZsStoreType = bundlingTaskDetails1.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
                List<String> collect1 = storeZsStoreType.stream().filter(item -> BundTaskZsStoreType.contains(item)).collect(Collectors.toList());
                List<CommonEnums> commonEnums = new ArrayList<>();
                if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())){
                    commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.ZS.getCode()), collect1);
                }else {
                    commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.ZS.getCode()), collect1);
                }
                if (CollectionUtils.isNotEmpty(commonEnums)&&commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()).contains(storeType)){
                    return null;
                }else {
                    return "组货任务不包含"+storeType+"店型";
                }
            }
            if (zhType){
                List<String> storeStoreType = collect.stream().map(BundlingTaskStoreDetail::getStoreTypeCode).distinct().collect(Collectors.toList());
                List<BundlingTaskDetail> bundlingTaskDetails1 = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.STORE.getCode())).collect(Collectors.toList());
                List<String> BundTaskZsStoreType = bundlingTaskDetails1.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
                List<String> collect1 = storeStoreType.stream().filter(item -> BundTaskZsStoreType.contains(item)).collect(Collectors.toList());
                List<CommonEnums> commonEnums = new ArrayList<>();
                if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())){
                    commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode()), collect1);
                }else {
                    commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode()), collect1);
                }
                if (CollectionUtils.isNotEmpty(commonEnums)&&commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()).contains(storeType)){
                    return null;
                }else {
                    return "组货任务不包含"+storeType+"店型";
                }
            }
            return null;
        }
        return "组货任务店型为空";
    }

    @Async("trackResultFileUploadExecutor")
    public void storeTypeGoods(List<StoreTypeGoodsData> dataList, Long taskId, BundlingTaskInfo bundlingTaskInfo, ImportResult result, String key, RBucket<ImportResult> rBucket) {

       try {
           ArrayList<StoreTypeGoodsData> errorData = new ArrayList<>();
           ArrayList<StoreTypeGoodsData> repeatDataList = new ArrayList<>();
           List<String> goodsNoList = dataList.stream().map(StoreTypeGoodsData::getGoodsNo).collect(Collectors.toList());
           List<String> taskGoodTypeList = getTaskGoodTypeList(taskId);
           List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskId);
           Map<String, SpuListVo> spuMap = new HashMap<>();
           Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
           List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);
           //集团必备查重 平台、SKU
           //todo 企业Id\店型  查任务门店信息  查不到报错
           List<String> existsGoods = necessaryGroupGoodsExtendMapper.selectGroupExistsGoods(bundlingTaskInfo.getOrgId(), goodsNoList, NecessaryTagEnum.GROUP_NECESSARY.getCode());
           for (StoreTypeGoodsData storeTypeGoodsData : dataList) {
               if (storeTypeGoodsData.getOrgId() == null || StringUtils.isBlank(storeTypeGoodsData.getGoodsNo()) || StringUtils.isBlank(storeTypeGoodsData.getCity()) || StringUtils.isBlank(storeTypeGoodsData.getStoreType())) {
                   storeTypeGoodsData.setErrorReason("表格中组织ID、商品编码、城市店型不能为空");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               SpuListVo spuListVo = spuMap.get(storeTypeGoodsData.getGoodsNo());
               if (Objects.isNull(spuListVo)) {
                   storeTypeGoodsData.setErrorReason("商品查询不到");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }

               //校验商品是否在商品组货类型的范围下
               if (StringUtils.isNotEmpty(checkTaskGoodsType(taskGoodTypeList, spuListVo))) {
                   storeTypeGoodsData.setErrorReason(checkTaskGoodsType(taskGoodTypeList, spuListVo));
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               //校验城市和企业
               if (StringUtils.isNotEmpty(checkCompanyAndCity(bundlingTaskStoreDetailList, storeTypeGoodsData.getOrgId(), storeTypeGoodsData.getCity()))) {
                   storeTypeGoodsData.setErrorReason(checkCompanyAndCity(bundlingTaskStoreDetailList, storeTypeGoodsData.getOrgId(), storeTypeGoodsData.getCity()));
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               boolean ptStoreType = Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2));
               boolean zsStoreType = Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4));

               if (StringUtils.isNotEmpty( checkStoreType(bundlingTaskInfo,bundlingTaskStoreDetailList, bundlingTaskDetails,zsStoreType, ptStoreType, storeTypeGoodsData.getStoreType()))){
                   storeTypeGoodsData.setErrorReason( checkStoreType(bundlingTaskInfo,bundlingTaskStoreDetailList, bundlingTaskDetails,zsStoreType, ptStoreType, storeTypeGoodsData.getStoreType()));
                   errorData.add(storeTypeGoodsData);
                   continue;
               }

               if (existsGoods.contains(storeTypeGoodsData.getGoodsNo())) {
                   storeTypeGoodsData.setErrorReason("商品在集团必备中存在");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               if (repeatDataList.contains(storeTypeGoodsData)) {
                   storeTypeGoodsData.setErrorReason("数据在表格中重复");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               repeatDataList.add(storeTypeGoodsData);
               List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicName(storeTypeGoodsData.getStoreType());
               if (CollectionUtils.isEmpty(commonEnums)) {
                   storeTypeGoodsData.setErrorReason("店型在枚举种查询不到");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }

               Optional<OrgInfoBaseCache> businessByOrgId = CacheVar.getBusinessByOrgId(storeTypeGoodsData.getOrgId());
               if (!businessByOrgId.isPresent()) {
                   storeTypeGoodsData.setErrorReason("获取企业信息为空，企业信息不存在");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               List<String> platStoreTypeCodeList = new ArrayList<>();
               List<String> storeTypeCodeList = new ArrayList<>();
               List<String> zsStoreTypeCodeList = new ArrayList<>();
               List<String> storeAndZsTypeCodeList = new ArrayList<>();
               CommonEnums commonEnums1 = commonEnums.get(0);
               if (ptStoreType) {
                   String ptStoreTypes = CacheVar.storeTypeConvertPtStoreType(commonEnums1.getEnumValue());
                   platStoreTypeCodeList.add(ptStoreTypes);
                   storeAndZsTypeCodeList.add(ptStoreTypes);
                   storeTypeCodeList.add(commonEnums1.getEnumValue());
               } else if (zsStoreType && commonEnums1.getPropertyCode().equals("ZsStoreGroup")) {
                   zsStoreTypeCodeList.add(commonEnums1.getEnumValue());
                   storeAndZsTypeCodeList.add(commonEnums1.getEnumValue());
               } else {
                   storeTypeGoodsData.setErrorReason("店型不符合");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }

               //todo  查重最后再写  重要
               //全层级必备  查重自己的平台必备  、查重自己的企业必备 查重自己的店型必备
               if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
                   //平台必备查重
                   TaskNecessaryPlatformGoodsExample taskNecessaryPlatformGoodsExample = new TaskNecessaryPlatformGoodsExample();
                   taskNecessaryPlatformGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andStoreTypeIn(storeAndZsTypeCodeList).andGoodsNoIn(goodsNoList);
                   List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsMapper.selectByExample(taskNecessaryPlatformGoodsExample);
                   if (taskNecessaryPlatformGoods.stream().map(TaskNecessaryPlatformGoods::getGoodsNo).collect(Collectors.toList()).contains(storeTypeGoodsData.getGoodsNo())) {
                       storeTypeGoodsData.setErrorReason("商品在平台必备中存在，不能新增为店型必备");
                       errorData.add(storeTypeGoodsData);
                       continue;
                   }
                   TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                   taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(storeTypeGoodsData.getOrgId()).andCityEqualTo(storeTypeGoodsData.getCity()).andGoodsNoEqualTo(storeTypeGoodsData.getGoodsNo());
                   List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
                   if (taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getGoodsNo).collect(Collectors.toList()).contains(storeTypeGoodsData.getGoodsNo())) {
                       storeTypeGoodsData.setErrorReason("商品在企业必备中存在，不能新增为店型必备");
                       errorData.add(storeTypeGoodsData);
                       continue;
                   }
               } else if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())) {
                   //平台必备查重
                   List<String> existsGoodsNos = necessaryPlatformGoodsExtendMapper.selectExistsGoods(bundlingTaskInfo.getOrgId(), Lists.newArrayList(storeAndZsTypeCodeList.get(0)), Arrays.asList(storeTypeGoodsData.getGoodsNo()));
                   if (CollectionUtils.isNotEmpty(existsGoodsNos)) {
                       storeTypeGoodsData.setErrorReason("商品在平台必备中存在,不能新增为店型必备");
                       errorData.add(storeTypeGoodsData);
                       continue;
                   }
                   // 企业必备表查重：平台、管理主体、城市、SKU
                   TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                   taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(storeTypeGoodsData.getOrgId()).andCityEqualTo(storeTypeGoodsData.getCity()).andGoodsNoEqualTo(storeTypeGoodsData.getGoodsNo());
                   List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
                   if (taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getGoodsNo).collect(Collectors.toList()).contains(storeTypeGoodsData.getGoodsNo())) {
                       storeTypeGoodsData.setErrorReason("商品在企业必备中存在,不能新增为店型必备");
                       errorData.add(storeTypeGoodsData);
                       continue;
                   }
               } else {
                   //平台必备查重
                   List<String> existsGoodsNos = necessaryPlatformGoodsExtendMapper.selectExistsGoods(bundlingTaskInfo.getOrgId(), Lists.newArrayList(storeAndZsTypeCodeList.get(0)), Arrays.asList(storeTypeGoodsData.getGoodsNo()));
                   if (CollectionUtils.isNotEmpty(existsGoodsNos)) {
                       storeTypeGoodsData.setErrorReason("商品在平台必备中存在,不能新增为店型必备");
                       errorData.add(storeTypeGoodsData);
                       continue;
                   }
                   // 企业必备表查重：平台、管理主体、城市、SKU
                   List<String> companyExists = necessaryCompanyGoodsExtendMapper.selectExistsGoods(bundlingTaskInfo.getOrgId(), storeTypeGoodsData.getOrgId(), Lists.newArrayList(storeTypeGoodsData.getCity()), Arrays.asList(storeTypeGoodsData.getGoodsNo()));
                   if (CollectionUtils.isNotEmpty(companyExists)) {
                       storeTypeGoodsData.setErrorReason("商品在企业必备中存在,不能新增为店型必备");
                       errorData.add(storeTypeGoodsData);
                       continue;
                   }
               }

               Map<Long, CommonCategoryDTO> categoryBySubIds = getCategoryBySubIds(Arrays.asList(Long.valueOf(spuListVo.getCategoryId())));

               // 店型必备表查重：平台、管理主体、城市、店型、SKU
               TaskNecessaryStoreTypeGoodsExample taskNecessaryStoreTypeGoodsExample = new TaskNecessaryStoreTypeGoodsExample();
               taskNecessaryStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andStoreTypeEqualTo(commonEnums.get(0).getEnumValue()).andGoodsNoEqualTo(storeTypeGoodsData.getGoodsNo()).andCompanyOrgIdEqualTo(storeTypeGoodsData.getOrgId()).andCityEqualTo(storeTypeGoodsData.getCity());
               List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods = taskNecessaryStoreTypeGoodsMapper.selectByExample(taskNecessaryStoreTypeGoodsExample);
               if (CollectionUtils.isNotEmpty(taskNecessaryStoreTypeGoods)) {
                   storeTypeGoodsData.setErrorReason("数据在店型必备中存在,无法重复添加");
                   errorData.add(storeTypeGoodsData);
                   continue;
               } else {
                   TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods1 = new TaskNecessaryStoreTypeGoods();
                   buildTaskNecessaryStoreTypeGoods(taskId,commonEnums, bundlingTaskInfo, storeTypeGoodsData, spuListVo, businessByOrgId, categoryBySubIds, taskNecessaryStoreTypeGoods1);
                   taskNecessaryStoreTypeGoodsMapper.insertSelective(taskNecessaryStoreTypeGoods1);
               }
               List<MdmStoreExDTO> mdmStoreExDTOList=new ArrayList<>();
               List<String> storeList=new ArrayList<>();
               if (ptStoreType){
                   mdmStoreExDTOList = bundlTaskService.selectMdmStoreByTaskId(taskId, storeTypeGoodsData.getOrgId(), storeTypeGoodsData.getCity(), null, commonEnums.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()), null);
                   storeList=mdmStoreExDTOList.stream().filter(v->v.getStoreTypeCode().equals(commonEnums1.getEnumValue())).map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList());
               }
               if (zsStoreType){
                   mdmStoreExDTOList = bundlTaskService.selectMdmStoreByTaskId(taskId, storeTypeGoodsData.getOrgId(), storeTypeGoodsData.getCity(), null, null,  commonEnums.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()));
                   storeList=mdmStoreExDTOList.stream().filter(v->v.getZsStoreTypeCode().equals(commonEnums1.getEnumValue())).map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList());
               }

               if (CollectionUtils.isEmpty(storeList)){
                   storeTypeGoodsData.setErrorReason("店型下没有门店");
                   errorData.add(storeTypeGoodsData);
                   continue;
               }
               //通过平台*管理公司*城市*店型*规则找到门店list，门店list*SKU在在一店一目表查询，存在则更新，不存在则新增，配置类型=店型必备、最小陈列量=1。
               List<MdmStoreExDTO> mdmStoreExDTOS = bundlTaskService.selectMdmStoreByTaskId(taskId, storeTypeGoodsData.getOrgId(), storeTypeGoodsData.getCity(), null, storeTypeCodeList, zsStoreTypeCodeList);
               TrackRetultDetailParam param = new TrackRetultDetailParam();
               param.setTaskId(bundlingTaskInfo.getId());
               param.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
               param.setCompid(String.valueOf(storeTypeGoodsData.getOrgId()));
               param.setOrgNoList(storeList);
               param.setCity(storeTypeGoodsData.getCity());
               param.setGoodsId(storeTypeGoodsData.getGoodsNo());
               param.setStatus(Constants.NORMAL_STATUS);
               List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);
               List<String> StoreNoList = mdmStoreExDTOS.stream().map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList());

               List<String> tempStoreNos = trackRetultAllDetails.stream().map(TrackRetultAllDetail::getOrgNo).collect(Collectors.toList());

               List<String> insertStoreNos = StoreNoList.stream().filter(v -> !tempStoreNos.contains(v)).collect(Collectors.toList());

               if (CollectionUtils.isNotEmpty(trackRetultAllDetails)) {
                   List<TrackRetultAllDetail> otherLevelGoods = trackRetultAllDetails.stream().filter(v -> !Byte.valueOf(v.getLevel()).equals(NecessaryGoodsLevelEnum.FOURTH.getCode())).collect(Collectors.toList());
                   if (CollectionUtils.isNotEmpty(otherLevelGoods)) {
                       trackRetultAllDetailExtendMapper.updateLevel(taskId,otherLevelGoods.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()),String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                   }
               }
               if (CollectionUtils.isNotEmpty(insertStoreNos)){
                   //新增
                   ArrayList<TrackRetultAllDetail> trackRetultAllDetailArrayList = new ArrayList<>();
                   for (String insertStoreNo : insertStoreNos) {
                       TrackRetultAllDetail trackRetultAllDetail = new TrackRetultAllDetail();
                       trackRetultAllDetail.setLevel(String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()));
                       trackRetultAllDetail.setGoodsId(storeTypeGoodsData.getGoodsNo());
                       trackRetultAllDetail.setTaskId(taskId);
                       trackRetultAllDetail.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
                       trackRetultAllDetail.setZoneNew(bundlingTaskInfo.getOrgName());
                       trackRetultAllDetail.setOrgNo(insertStoreNo);
                       trackRetultAllDetail.setReviseStoreGroup(storeTypeGoodsData.getStoreType());
                       MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(insertStoreNo);
                       if (Objects.nonNull(mdmStoreExDTO)) {
                           trackRetultAllDetail.setCity(mdmStoreExDTO.getCity());
                       }
                       Optional<OrgInfoBaseCache> storeBySapCode = CacheVar.getStoreBySapCode(insertStoreNo);
                       if (storeBySapCode.isPresent()) {
                           trackRetultAllDetail.setDataFrom(storeBySapCode.get().getBusinessShortName());
                           trackRetultAllDetail.setDataFromV2(storeBySapCode.get().getBusinessShortName());
                           Optional<OrgInfoBaseCache> businessOrgByBusinessId = CacheVar.getBusinessOrgByBusinessId(storeBySapCode.get().getBusinessId());
                           if (businessOrgByBusinessId.isPresent()){
                               trackRetultAllDetail.setCompid(String.valueOf(businessOrgByBusinessId.get().getBusinessOrgId()));
                               trackRetultAllDetail.setStoreName(businessOrgByBusinessId.get().getShortName());
                           }
                       }

                       SpuListVo spuListVo1 = spuMap.get(storeTypeGoodsData.getGoodsNo());
                       setGoodsProperty(spuListVo1, trackRetultAllDetail);
                       trackRetultAllDetail.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId().toString());
                       trackRetultAllDetail.setGmtCreate(new Date());
                       trackRetultAllDetail.setStatus(Constants.NORMAL_STATUS);
                       trackRetultAllDetailArrayList.add(trackRetultAllDetail);
                   }
                   if (CollectionUtils.isNotEmpty(trackRetultAllDetailArrayList)){
                       trackRetultAllDetailExtendMapper.batchInsert(taskId,trackRetultAllDetailArrayList);
                   }
               }

               //        ⑤⑤查店型选配表：平台、管理主体、城市、店型、SKU ，存在则删除N行。
               TaskNecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new TaskNecessaryChooseStoreTypeGoodsExample();
               chooseStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCityEqualTo(storeTypeGoodsData.getCity()).andCompanyOrgIdEqualTo(storeTypeGoodsData.getOrgId()).andStoreTypeIn(commonEnums.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList())).andGoodsNoEqualTo(storeTypeGoodsData.getGoodsNo());
               taskNecessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);

               //       平台、管理主体、城市、店型下的门店list、SKU，存在则删除N行

               TaskNecessarySingleStoreGoodsExample singleStoreGoodsExample = new TaskNecessarySingleStoreGoodsExample();
               singleStoreGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(storeTypeGoodsData.getOrgId()).andCityEqualTo(storeTypeGoodsData.getCity()).andStoreCodeIn(storeList).andGoodsNoEqualTo(storeTypeGoodsData.getGoodsNo());
               taskNecessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);
           }
           repeatDataList.clear();
           String fileName = taskId + "_店型必备商品导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
           String filePath = "/tmp/" + fileName;
           String fileFileUrl = "";
           if (CollectionUtils.isNotEmpty(errorData)) {
               try {
                   EasyExcel.write(filePath, StoreTypeGoodsData.class).sheet("店型必备商品导入").doWrite(errorData);
                   fileFileUrl = uplodaErrorData(fileName, filePath);
               } catch (Exception e) {
                   logger.error("店型必备商品导入错误文件生成文件失败,删除文件", e);
                   throw e;
               } finally {
                   delTempFile(filePath);
               }
           }
           toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
       }catch (Exception e){
           logger.error("店型必备导入数据处理失败",e);
           result.setCode("1");
           result.setResult(key);
           result.setMessage("企业必备导入数据处理失败!");
           result.setFailFileUrl(null);
           rBucket.set(result, 12, TimeUnit.HOURS);
       }

    }

    @Async("trackResultFileUploadExecutor")
    public void singleStoreGoods(List<SingleStoreNecessaryData> dataList, Long taskId, BundlingTaskInfo bundlingTaskInfo, ImportResult result, String key, RBucket<ImportResult> rBucket) {
        try{
            ArrayList<SingleStoreNecessaryData> errorData = new ArrayList<>();
            ArrayList<SingleStoreNecessaryData> repeatDataList = new ArrayList<>();


            List<String> goodsNoList = dataList.stream().map(SingleStoreNecessaryData::getGoodsNo).collect(Collectors.toList());

            Map<String, SpuListVo> spuMap = new HashMap<>();
            Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
            List<String> taskGoodTypeList = getTaskGoodTypeList(taskId);

            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskId);
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);

            //集团必备查重 平台、SKU
            //todo 企业Id、店型  查任务门店信息  查不到报错
            List<String> existsGoods = necessaryGroupGoodsExtendMapper.selectGroupExistsGoods(bundlingTaskInfo.getOrgId(), goodsNoList, NecessaryTagEnum.GROUP_NECESSARY.getCode());
            ArrayList<TrackRetultAllDetail> trackRetultAllDetailArrayList = new ArrayList<>();
            for (SingleStoreNecessaryData singleStoreNecessaryData : dataList) {
                if (StringUtils.isBlank(singleStoreNecessaryData.getOrgNo()) || StringUtils.isBlank(singleStoreNecessaryData.getGoodsNo())) {
                    singleStoreNecessaryData.setErrorReason("机构或商品信息为空");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                SpuListVo spuListVo = spuMap.get(singleStoreNecessaryData.getGoodsNo());
                if (Objects.isNull(spuListVo)) {
                    singleStoreNecessaryData.setErrorReason("商品信息不存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }

                //校验商品是否在商品组货类型的范围下
                if (StringUtils.isNotEmpty(checkTaskGoodsType(taskGoodTypeList, spuListVo))) {
                    singleStoreNecessaryData.setErrorReason(checkTaskGoodsType(taskGoodTypeList, spuListVo));
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }

                if (!bundlingTaskStoreDetailList.stream().filter(v->v.getBundlConfirmAble().equals(BundlBoolEnum.YES.getCode())).map(BundlingTaskStoreDetail::getStoreCode).collect(Collectors.toList()).contains(singleStoreNecessaryData.getOrgNo())) {
                    singleStoreNecessaryData.setErrorReason("门店不在组货范围内");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }

                MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(singleStoreNecessaryData.getOrgNo());
                if (Objects.isNull(mdmStoreExDTO)) {
                    singleStoreNecessaryData.setErrorReason("门店信息不存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                boolean ptStoreType = Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2));
                boolean zsStoreType = Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4));
                if (!ptStoreType && !zsStoreType) {
                    singleStoreNecessaryData.setErrorReason("组货店型不存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                List<CommonEnums> storeGroupList = new ArrayList<>();
                String ptStoreTypeCode=null;
                //门店所属【平台组货店型】
                if (Objects.nonNull(mdmStoreExDTO)){
                    if (ptStoreType) {
                        ptStoreTypeCode=mdmStoreExDTO.getPlatStoreTypeCode();
                        if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())){
                            List<BundlingTaskDetail> bundlingTaskDetails1 = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.STORE.getCode())).collect(Collectors.toList());
                            List<String> bundTaskStoreType = bundlingTaskDetails1.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
                            if (!bundTaskStoreType.contains(mdmStoreExDTO.getStoreTypeCode())){
                                singleStoreNecessaryData.setErrorReason("门店所属店型不在组货范围内");
                                errorData.add(singleStoreNecessaryData);
                                continue;
                            }
                        }
                    } else if (zsStoreType) {
                        ptStoreTypeCode=mdmStoreExDTO.getZsStoreTypeCode();
                        if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())){
                            List<BundlingTaskDetail> bundlingTaskDetails1 = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())).collect(Collectors.toList());
                            List<String> bundTaskStoreType = bundlingTaskDetails1.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList());
                            if (!bundTaskStoreType.contains(ptStoreTypeCode)){
                                singleStoreNecessaryData.setErrorReason("门店所属店型不在组货范围内");
                                errorData.add(singleStoreNecessaryData);
                                continue;
                            }
                        }
                    } else {
                        continue;
                    }
                }
                if (StringUtils.isEmpty(ptStoreTypeCode)){
                    singleStoreNecessaryData.setErrorReason("找不到店型");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                Optional<OrgInfoBaseCache> businessOrgByBusinessId = CacheVar.getStoreBySapCode(singleStoreNecessaryData.getOrgNo());
                if (!businessOrgByBusinessId.isPresent()){
                    singleStoreNecessaryData.setErrorReason("门店信息查询企业信息不存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }

                if (existsGoods.contains(singleStoreNecessaryData.getGoodsNo())) {
                    singleStoreNecessaryData.setErrorReason("商品在集团必备中存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                //平台必备查重
                if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
                    //平台必备查重
                    TaskNecessaryPlatformGoodsExample taskNecessaryPlatformGoodsExample = new TaskNecessaryPlatformGoodsExample();
                    taskNecessaryPlatformGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andStoreTypeEqualTo(ptStoreTypeCode).andGoodsNoIn(goodsNoList);
                    List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsMapper.selectByExample(taskNecessaryPlatformGoodsExample);

                    if (taskNecessaryPlatformGoods.stream().map(TaskNecessaryPlatformGoods::getGoodsNo).collect(Collectors.toList()).contains(singleStoreNecessaryData.getGoodsNo())) {
                        singleStoreNecessaryData.setErrorReason("商品在平台必备中存在，不能新增为单店必备");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }
                    TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                    taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(businessOrgByBusinessId.get().getBusinessOrgId()).andCityEqualTo(mdmStoreExDTO.getCity()).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                    List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
                    if (taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getGoodsNo).collect(Collectors.toList()).contains(singleStoreNecessaryData.getGoodsNo())) {
                        singleStoreNecessaryData.setErrorReason("商品在企业必备中存在，不能新增为单店必备");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }

                } else if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())) {
                    //平台必备查重
                    List<String> existsGoodsNos = necessaryPlatformGoodsExtendMapper.selectExistsGoods(bundlingTaskInfo.getOrgId(), Lists.newArrayList(ptStoreTypeCode), Arrays.asList(singleStoreNecessaryData.getGoodsNo()));
                    if (CollectionUtils.isNotEmpty(existsGoodsNos)) {
                        singleStoreNecessaryData.setErrorReason("商品在平台必备中存在");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }
                    // 企业必备表查重：平台、管理主体、城市、SKU
                    TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                    taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(businessOrgByBusinessId.get().getBusinessOrgId()).andCityEqualTo(mdmStoreExDTO.getCity()).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                    List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
                    if (taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getGoodsNo).collect(Collectors.toList()).contains(singleStoreNecessaryData.getGoodsNo())) {
                        singleStoreNecessaryData.setErrorReason("商品在企业必备中存在，不能新增为单店必备");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }

                } else {
                    //平台必备查重
                    List<String> existsGoodsNos = necessaryPlatformGoodsExtendMapper.selectExistsGoods(bundlingTaskInfo.getOrgId(), Lists.newArrayList(ptStoreTypeCode), Arrays.asList(singleStoreNecessaryData.getGoodsNo()));
                    if (CollectionUtils.isNotEmpty(existsGoodsNos)) {
                        singleStoreNecessaryData.setErrorReason("商品在平台必备中存在");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }
                    // 企业必备表查重：平台、管理主体、城市、SKU
                    List<String> companyExists = necessaryCompanyGoodsExtendMapper.selectExistsGoods(bundlingTaskInfo.getOrgId(),businessOrgByBusinessId.get().getBusinessOrgId(), Lists.newArrayList(mdmStoreExDTO.getCity()), Arrays.asList(singleStoreNecessaryData.getGoodsNo()));
                    if (CollectionUtils.isNotEmpty(companyExists)) {
                        singleStoreNecessaryData.setErrorReason("商品在企业必备中存在");
                        errorData.add(singleStoreNecessaryData);
                        continue;
                    }
                }
                Map<Long, CommonCategoryDTO> categoryBySubIds = getCategoryBySubIds(Arrays.asList(Long.valueOf(spuListVo.getCategoryId())));

                //④店型必备表查重：平台、门店所属管理主体、门店所属城市、门店所属【组货店型】或者【中参店型】-根据SKU的商品类型确定去哪类店型、SKU
                if (ptStoreType){
                    ptStoreTypeCode = mdmStoreExDTO.getStoreTypeCode();
                }
                TaskNecessaryStoreTypeGoodsExample example = new TaskNecessaryStoreTypeGoodsExample();
                example.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(businessOrgByBusinessId.get().getBusinessOrgId()).
                        andStoreTypeEqualTo(ptStoreTypeCode).andCityEqualTo(mdmStoreExDTO.getCity()).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods = taskNecessaryStoreTypeGoodsMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(taskNecessaryStoreTypeGoods)) {
                    singleStoreNecessaryData.setErrorReason("商品在店型必备中存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }

                //⑤店型选配表查重：平台、门店所属管理主体、门店所属城市、门店所属【组货店型】或者【中参店型】、部分店清单包含本门店、SKU
                TaskNecessaryChooseStoreTypeGoodsExample example1 = new TaskNecessaryChooseStoreTypeGoodsExample();
                example1.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(businessOrgByBusinessId.get().getBusinessOrgId()).
                        andCityEqualTo(mdmStoreExDTO.getCity()).andStoreTypeEqualTo(ptStoreTypeCode).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods = taskNecessaryChooseStoreTypeGoodsMapper.selectByExample(example1);
                if (CollectionUtils.isNotEmpty(taskNecessaryChooseStoreTypeGoods)) {
                    singleStoreNecessaryData.setErrorReason("商品在店型选配中存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                }
                //⑥单店必备表查重：平台、门店、SKU
                TaskNecessarySingleStoreGoodsExample taskNecessarySingleStoreGoodsExample = new TaskNecessarySingleStoreGoodsExample();
                taskNecessarySingleStoreGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andStoreCodeEqualTo(singleStoreNecessaryData.getOrgNo()).andGoodsNoEqualTo(singleStoreNecessaryData.getGoodsNo());
                List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods1 = taskNecessarySingleStoreGoodsMapper.selectByExample(taskNecessarySingleStoreGoodsExample);
                if (CollectionUtils.isNotEmpty(taskNecessarySingleStoreGoods1)) {
                    singleStoreNecessaryData.setErrorReason("商品在单店必备中存在");
                    errorData.add(singleStoreNecessaryData);
                    continue;
                } else {
                    TaskNecessarySingleStoreGoods taskNecessarySingleStoreGoods = new TaskNecessarySingleStoreGoods();
                    buildTaskNecessarySingleStoreGoods(bundlingTaskInfo, singleStoreNecessaryData, spuListVo, mdmStoreExDTO, businessOrgByBusinessId, categoryBySubIds, taskNecessarySingleStoreGoods);
                    taskNecessarySingleStoreGoodsMapper.insertSelective(taskNecessarySingleStoreGoods);

                    TrackRetultDetailParam param = new TrackRetultDetailParam();
                    param.setTaskId(bundlingTaskInfo.getId());
                    param.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
                    param.setOrgNoList(Arrays.asList(singleStoreNecessaryData.getOrgNo()));
                    param.setLevelList(Arrays.asList(String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode())));
                    param.setGoodsId(singleStoreNecessaryData.getGoodsNo());
                    param.setStatus(Constants.NORMAL_STATUS);
                    List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);
                    if (CollectionUtils.isEmpty(trackRetultAllDetails)){
                        TrackRetultAllDetail trackRetultAllDetail = new TrackRetultAllDetail();
                        trackRetultAllDetail.setLevel(String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode()));
                        trackRetultAllDetail.setGoodsId(singleStoreNecessaryData.getGoodsNo());
                        trackRetultAllDetail.setTaskId(taskId);
                        trackRetultAllDetail.setOrgNo(singleStoreNecessaryData.getOrgNo());
                        trackRetultAllDetail.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
                        trackRetultAllDetail.setZoneNew(bundlingTaskInfo.getOrgName());
                        trackRetultAllDetail.setCompid(businessOrgByBusinessId.get().getBusinessOrgId() != null ? String.valueOf(businessOrgByBusinessId.get().getBusinessOrgId()) : "");
                        trackRetultAllDetail.setDataFrom(businessOrgByBusinessId.get().getBusinessShortName());
                        trackRetultAllDetail.setDataFromV2(businessOrgByBusinessId.get().getBusinessShortName());
                        trackRetultAllDetail.setStoreName(businessOrgByBusinessId.get().getShortName());
                        trackRetultAllDetail.setCity(mdmStoreExDTO.getCity());
                        SpuListVo spuListVo1 = spuMap.get(singleStoreNecessaryData.getOrgNo());
                        setGoodsProperty(spuListVo1, trackRetultAllDetail);
                        if (ptStoreType){
                            trackRetultAllDetail.setReviseStoreGroup(mdmStoreExDTO.getStoreType());
                        }
                        if (zsStoreType){
                            trackRetultAllDetail.setReviseStoreGroup(zsStoreTypeNameMapping.get(mdmStoreExDTO.getZsStoreTypeCode()));
                        }
                        trackRetultAllDetail.setGmtCreate(new Date());
                        trackRetultAllDetail.setStatus(Constants.NORMAL_STATUS);
                        trackRetultAllDetail.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId().toString());
                        trackRetultAllDetailArrayList.add(trackRetultAllDetail);
                    }
                }

            }
            if (CollectionUtils.isNotEmpty(trackRetultAllDetailArrayList)){
                List<List<TrackRetultAllDetail>> partition = Lists.partition(trackRetultAllDetailArrayList, Constants.INSERT_MAX_SIZE);
                for (List<TrackRetultAllDetail> trackRetultAllDetails : partition) {
                    trackRetultAllDetailExtendMapper.batchInsert(taskId,trackRetultAllDetails);
                }
            }
            repeatDataList.clear();
            String fileName = taskId + "_单店必备商品导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {

                    EasyExcel.write(filePath, SingleStoreNecessaryData.class).sheet("单店必备商品导入").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("单店必备商品导入错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        }catch (Exception e){
            logger.error("单店必备导入数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("单店必备导入数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }

    }


    private void buildTaskNecessarySingleStoreGoods(BundlingTaskInfo bundlingTaskInfo, SingleStoreNecessaryData singleStoreNecessaryData, SpuListVo spuListVo, MdmStoreExDTO mdmStoreExDTO, Optional<OrgInfoBaseCache> storeBySapCode, Map<Long, CommonCategoryDTO> categoryBySubIds, TaskNecessarySingleStoreGoods taskNecessarySingleStoreGoods) {
        taskNecessarySingleStoreGoods.setTaskId(bundlingTaskInfo.getId());
        taskNecessarySingleStoreGoods.setPlatformOrgId(bundlingTaskInfo.getOrgId());
        taskNecessarySingleStoreGoods.setPlatformName(bundlingTaskInfo.getOrgName());
        taskNecessarySingleStoreGoods.setCompanyOrgId(storeBySapCode.get().getBusinessOrgId());
        taskNecessarySingleStoreGoods.setCompanyCode(storeBySapCode.get().getBusinessSapCode());
        taskNecessarySingleStoreGoods.setCompanyName(storeBySapCode.get().getBusinessShortName());
        taskNecessarySingleStoreGoods.setBusinessid(storeBySapCode.get().getBusinessId());
        taskNecessarySingleStoreGoods.setStoreId(mdmStoreExDTO.getStoreId());
        taskNecessarySingleStoreGoods.setStoreCode(mdmStoreExDTO.getStoreNo());
        taskNecessarySingleStoreGoods.setStoreName(mdmStoreExDTO.getStoreName());
        taskNecessarySingleStoreGoods.setCity(mdmStoreExDTO.getCity());
        taskNecessarySingleStoreGoods.setGoodsNo(singleStoreNecessaryData.getGoodsNo());
        if (MapUtils.isNotEmpty(categoryBySubIds) && Objects.nonNull(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())))) {
            taskNecessarySingleStoreGoods.setCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryId());
            taskNecessarySingleStoreGoods.setCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryName());
            taskNecessarySingleStoreGoods.setMiddleCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryId());
            taskNecessarySingleStoreGoods.setMiddleCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryName());
            taskNecessarySingleStoreGoods.setSmallCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryName());
            taskNecessarySingleStoreGoods.setSmallCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryId());
            taskNecessarySingleStoreGoods.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId());
            taskNecessarySingleStoreGoods.setSubCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryName());
        }
        taskNecessarySingleStoreGoods.setComposition(spuListVo.getComponent());
        taskNecessarySingleStoreGoods.setBarCode(spuListVo.getBarCode());
        taskNecessarySingleStoreGoods.setGoodsCommonName(spuListVo.getCurName());
        taskNecessarySingleStoreGoods.setGoodsName(spuListVo.getName());
        taskNecessarySingleStoreGoods.setGoodsUnit(spuListVo.getGoodsunit());
        taskNecessarySingleStoreGoods.setDescription(spuListVo.getDescription());
        taskNecessarySingleStoreGoods.setSpecifications(spuListVo.getJhiSpecification());
        taskNecessarySingleStoreGoods.setDosageForm(spuListVo.getDosageformsid());
        taskNecessarySingleStoreGoods.setManufacturer(spuListVo.getFactoryid());
        taskNecessarySingleStoreGoods.setApprovalNumber(spuListVo.getApprdocno());
        taskNecessarySingleStoreGoods.setGmtCreate(new Date());
        taskNecessarySingleStoreGoods.setGmtUpdate(new Date());
    }


    private void buildTaskNecessaryStoreTypeGoods(Long taskId, List<CommonEnums> commonEnums,BundlingTaskInfo bundlingTaskInfo, StoreTypeGoodsData storeTypeGoodsData, SpuListVo spuListVo, Optional<OrgInfoBaseCache> storeByOrgId, Map<Long, CommonCategoryDTO> categoryBySubIds, TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods1) {
        taskNecessaryStoreTypeGoods1.setTaskId(taskId);
        taskNecessaryStoreTypeGoods1.setPlatformOrgId(bundlingTaskInfo.getOrgId());
        taskNecessaryStoreTypeGoods1.setPlatformName(bundlingTaskInfo.getOrgName());
        taskNecessaryStoreTypeGoods1.setCompanyOrgId(storeTypeGoodsData.getOrgId());
        taskNecessaryStoreTypeGoods1.setBusinessid(storeByOrgId.get().getBusinessId());
        taskNecessaryStoreTypeGoods1.setCompanyName(storeByOrgId.get().getName());
        taskNecessaryStoreTypeGoods1.setCompanyCode(storeByOrgId.get().getSapCode());
        taskNecessaryStoreTypeGoods1.setBusinessid(storeByOrgId.get().getBusinessId());
        taskNecessaryStoreTypeGoods1.setCity(storeTypeGoodsData.getCity());
        taskNecessaryStoreTypeGoods1.setGoodsNo(storeTypeGoodsData.getGoodsNo());
        taskNecessaryStoreTypeGoods1.setStoreType(commonEnums.get(0).getEnumValue());

        if (MapUtils.isNotEmpty(categoryBySubIds) && Objects.nonNull(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())))) {
            taskNecessaryStoreTypeGoods1.setCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryId());
            taskNecessaryStoreTypeGoods1.setCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryName());
            taskNecessaryStoreTypeGoods1.setMiddleCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryId());
            taskNecessaryStoreTypeGoods1.setMiddleCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryName());
            taskNecessaryStoreTypeGoods1.setSmallCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryName());
            taskNecessaryStoreTypeGoods1.setSmallCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryId());
            taskNecessaryStoreTypeGoods1.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId());
            taskNecessaryStoreTypeGoods1.setSubCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryName());
        }
        taskNecessaryStoreTypeGoods1.setComposition(spuListVo.getComponent());
        taskNecessaryStoreTypeGoods1.setBarCode(spuListVo.getBarCode());
        taskNecessaryStoreTypeGoods1.setGoodsCommonName(spuListVo.getCurName());
        taskNecessaryStoreTypeGoods1.setGoodsName(spuListVo.getName());
        taskNecessaryStoreTypeGoods1.setGoodsUnit(spuListVo.getGoodsunit());
        taskNecessaryStoreTypeGoods1.setDescription(spuListVo.getDescription());
        taskNecessaryStoreTypeGoods1.setSpecifications(spuListVo.getJhiSpecification());
        taskNecessaryStoreTypeGoods1.setDosageForm(spuListVo.getDosageformsid());
        taskNecessaryStoreTypeGoods1.setManufacturer(spuListVo.getFactoryid());
        taskNecessaryStoreTypeGoods1.setApprovalNumber(spuListVo.getApprdocno());

        taskNecessaryStoreTypeGoods1.setGmtCreate(new Date());
        taskNecessaryStoreTypeGoods1.setGmtUpdate(new Date());
    }

    private void buildTaskNecessaryCompanyGoods(BundlingTaskInfo bundlingTaskInfo, CompanyGoodsData companyGoodsData, SpuListVo spuListVo, Optional<OrgInfoBaseCache> storeByOrgId, Map<Long, CommonCategoryDTO> categoryBySubIds, TaskNecessaryCompanyGoods taskNecessaryCompanyGoods1) {
        taskNecessaryCompanyGoods1.setTaskId(bundlingTaskInfo.getId());
        taskNecessaryCompanyGoods1.setPlatformOrgId(bundlingTaskInfo.getOrgId());
        taskNecessaryCompanyGoods1.setPlatformName(bundlingTaskInfo.getOrgName());
        taskNecessaryCompanyGoods1.setCompanyOrgId(companyGoodsData.getOrgId());
        if (storeByOrgId.isPresent()) {
            OrgInfoBaseCache orgInfoBaseCache = storeByOrgId.get();
            taskNecessaryCompanyGoods1.setBusinessid(orgInfoBaseCache.getBusinessId());
            taskNecessaryCompanyGoods1.setCompanyCode(orgInfoBaseCache.getSapCode());
            taskNecessaryCompanyGoods1.setCompanyName(orgInfoBaseCache.getName());
        }

        taskNecessaryCompanyGoods1.setCity(companyGoodsData.getCity());
        taskNecessaryCompanyGoods1.setGoodsNo(companyGoodsData.getGoodsNo());
        if (MapUtils.isNotEmpty(categoryBySubIds) && Objects.nonNull(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())))) {
            taskNecessaryCompanyGoods1.setCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryId());
            taskNecessaryCompanyGoods1.setCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryName());
            taskNecessaryCompanyGoods1.setMiddleCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryId());
            taskNecessaryCompanyGoods1.setMiddleCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryName());
            taskNecessaryCompanyGoods1.setSmallCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryName());
            taskNecessaryCompanyGoods1.setSmallCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryId());
            taskNecessaryCompanyGoods1.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId());
            taskNecessaryCompanyGoods1.setSubCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryName());
        }
        taskNecessaryCompanyGoods1.setBarCode(spuListVo.getBarCode());
        taskNecessaryCompanyGoods1.setComposition(spuListVo.getComponent());
        taskNecessaryCompanyGoods1.setGoodsCommonName(spuListVo.getCurName());
        taskNecessaryCompanyGoods1.setGoodsName(spuListVo.getName());
        taskNecessaryCompanyGoods1.setGoodsUnit(spuListVo.getGoodsunit());
        taskNecessaryCompanyGoods1.setDescription(spuListVo.getDescription());
        taskNecessaryCompanyGoods1.setSpecifications(spuListVo.getJhiSpecification());
        taskNecessaryCompanyGoods1.setDosageForm(spuListVo.getDosageformsid());
        taskNecessaryCompanyGoods1.setManufacturer(spuListVo.getFactoryid());
        taskNecessaryCompanyGoods1.setApprovalNumber(spuListVo.getApprdocno());
        taskNecessaryCompanyGoods1.setGmtCreate(new Date());
        taskNecessaryCompanyGoods1.setGmtUpdate(new Date());
    }

    private void delCompanyGoods(BundlingTaskInfo bundlingTaskInfo, CompanyGoodsData companyGoodsData, List<MdmStoreExDTO> mdmStoreExDTOS,List<String> storeTypeCode) {
        //          ④查店型必备表：平台、管理主体、城市下店型（根据SKU商品大类确定取哪类店型）、SKU，存在则删除N行。
        TaskNecessaryStoreTypeGoodsExample taskNecessaryStoreTypeGoodsExample = new TaskNecessaryStoreTypeGoodsExample();
        taskNecessaryStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId()).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(companyGoodsData.getOrgId()).andCityEqualTo(companyGoodsData.getCity()).andStoreTypeIn(storeTypeCode).andGoodsNoEqualTo(companyGoodsData.getGoodsNo());
        taskNecessaryStoreTypeGoodsMapper.deleteByExample(taskNecessaryStoreTypeGoodsExample);
        //        ⑤⑤查店型选配表：平台、管理主体、城市下店型（根据SKU商品大类确定取哪类店型）、SKU ，存在则删除N行。
        TaskNecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new TaskNecessaryChooseStoreTypeGoodsExample();
        chooseStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId()).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(companyGoodsData.getOrgId()).andCityEqualTo(companyGoodsData.getCity()).andStoreTypeIn(storeTypeCode).andGoodsNoEqualTo(companyGoodsData.getGoodsNo());
        taskNecessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);

        //        ⑥查单店必备表：平台、管理主体、城市下的门店list、SKU，存在则删除N行。
        TaskNecessarySingleStoreGoodsExample singleStoreGoodsExample = new TaskNecessarySingleStoreGoodsExample();
        singleStoreGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId()).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdEqualTo(companyGoodsData.getOrgId()).andCityEqualTo(companyGoodsData.getCity()).andStoreCodeIn(mdmStoreExDTOS.stream().map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList())).andGoodsNoEqualTo(companyGoodsData.getGoodsNo());
        taskNecessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);
    }


    private List<TrackRetultAllDetail> selectAllDetail(BundlingTaskInfo bundlingTaskInfo, String compid, String storeType, String goodsNo, String city, List<String> levelList) {
        TrackRetultDetailParam param = new TrackRetultDetailParam();
        param.setTaskId(bundlingTaskInfo.getId());
        param.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
        param.setCompid(compid);
        param.setCity(city);
        param.setReviseStoreGroup(storeType);
        param.setGoodsId(goodsNo);
        param.setLevelList(levelList);
        param.setStatus(Constants.NORMAL_STATUS);
        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);
        return trackRetultAllDetails;
    }

    private BundlingTaskInfo getBundlingTaskInfo(Long taskId) {
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(bundlingTaskInfo)) {
            throw new BusinessErrorException(ErrorCodeEnum.TASK_ID_NOTEXIT.getMsg());
        }
        return bundlingTaskInfo;
    }

    private void platformData(Long taskId, BundlingTaskInfo bundlingTaskInfo, PlatformGoodsData platformGoodsData, SpuListVo spuListVo, List<CommonEnums> commonEnums,List<CommonEnums> commonEnumsTemp) {
        TaskNecessaryPlatformGoods taskNecessaryPlatformGoods1 = new TaskNecessaryPlatformGoods();
        taskNecessaryPlatformGoods1.setTaskId(bundlingTaskInfo.getId());
        taskNecessaryPlatformGoods1.setPlatformOrgId(bundlingTaskInfo.getOrgId());
        taskNecessaryPlatformGoods1.setPlatformName(bundlingTaskInfo.getOrgName());
        List<CommonEnums> collect = commonEnums.stream().filter(v -> v.getEnumName().equals(platformGoodsData.getStoreType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)){
            taskNecessaryPlatformGoods1.setStoreType(collect.get(0).getEnumValue());
        }
        taskNecessaryPlatformGoods1.setGoodsNo(platformGoodsData.getGoodsNo());

        Map<Long, CommonCategoryDTO> categoryBySubIds = getCategoryBySubIds(Arrays.asList(Long.valueOf(spuListVo.getCategoryId())));
        if (MapUtils.isNotEmpty(categoryBySubIds) && Objects.nonNull(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())))) {
            taskNecessaryPlatformGoods1.setCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryId());
            taskNecessaryPlatformGoods1.setCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getCategoryName());
            taskNecessaryPlatformGoods1.setMiddleCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryId());
            taskNecessaryPlatformGoods1.setMiddleCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getMiddleCategoryName());
            taskNecessaryPlatformGoods1.setSmallCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryName());
            taskNecessaryPlatformGoods1.setSmallCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSmallCategoryId());
            taskNecessaryPlatformGoods1.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId());
            taskNecessaryPlatformGoods1.setSubCategoryName(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryName());
        }
        taskNecessaryPlatformGoods1.setBarCode(spuListVo.getBarCode());
        taskNecessaryPlatformGoods1.setComposition(spuListVo.getComponent());
        taskNecessaryPlatformGoods1.setGoodsCommonName(spuListVo.getCurName());
        taskNecessaryPlatformGoods1.setGoodsName(spuListVo.getName());
        taskNecessaryPlatformGoods1.setGoodsUnit(spuListVo.getGoodsunit());
        taskNecessaryPlatformGoods1.setDescription(spuListVo.getDescription());
        taskNecessaryPlatformGoods1.setSpecifications(spuListVo.getJhiSpecification());
        taskNecessaryPlatformGoods1.setDosageForm(spuListVo.getDosageformsid());
        taskNecessaryPlatformGoods1.setManufacturer(spuListVo.getFactoryid());
        taskNecessaryPlatformGoods1.setApprovalNumber(spuListVo.getApprdocno());
        taskNecessaryPlatformGoods1.setGmtCreate(new Date());
        taskNecessaryPlatformGoods1.setGmtUpdate(new Date());
        taskNecessaryPlatformGoodsMapper.insertSelective(taskNecessaryPlatformGoods1);
        //通过平台*店型  找到门店list
        // PT02  PT03  PT04
        List<CommonEnums> collect1 = commonEnums.stream().filter(v -> v.getEnumName().equals(platformGoodsData.getStoreType())).collect(Collectors.toList());
        List<MdmStoreExDTO> mdmStoreExDTOS = bundlTaskService.selectMdmStoreByTaskId(taskId, null, null, Arrays.asList(collect1.get(0).getEnumValue()), null, null);
        if (CollectionUtils.isNotEmpty(collect1)&&collect1.get(0).getPropertyCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())){
            mdmStoreExDTOS = bundlTaskService.selectMdmStoreByTaskId(taskId, null, null,null, null,  Arrays.asList(collect1.get(0).getEnumValue()));
        }

        TrackRetultDetailParam param = new TrackRetultDetailParam();
        param.setTaskId(bundlingTaskInfo.getId());
        param.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
        param.setGoodsId(platformGoodsData.getGoodsNo());
        param.setOrgNoList(mdmStoreExDTOS.stream().map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList()));
        param.setStatus(Constants.NORMAL_STATUS);
        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);

        List<String> StoreNoList = mdmStoreExDTOS.stream().map(MdmStoreExDTO::getStoreNo).collect(Collectors.toList());

        List<String> tempStoreNos = trackRetultAllDetails.stream().map(TrackRetultAllDetail::getOrgNo).collect(Collectors.toList());

        List<String> insertStoreNos = StoreNoList.stream().filter(v -> !tempStoreNos.contains(v)).collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(trackRetultAllDetails)) {
            List<TrackRetultAllDetail> otherLevelGoods = trackRetultAllDetails.stream().filter(v -> !Byte.valueOf(v.getLevel()).equals(NecessaryGoodsLevelEnum.SECEND.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherLevelGoods)) {
                trackRetultAllDetailExtendMapper.updateLevel(taskId,otherLevelGoods.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()),String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()) );

            }
        }
        if (CollectionUtils.isNotEmpty(insertStoreNos)){
            //新增
            List<TrackRetultAllDetail> trackRetultAllDetails1 = new ArrayList<>();
            for (String insertStoreNo : insertStoreNos) {
                TrackRetultAllDetail trackRetultAllDetail = new TrackRetultAllDetail();
                trackRetultAllDetail.setLevel(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()));
                trackRetultAllDetail.setGoodsId(platformGoodsData.getGoodsNo());
                trackRetultAllDetail.setTaskId(taskId);
                trackRetultAllDetail.setOrgNo(insertStoreNo);
                trackRetultAllDetail.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
                trackRetultAllDetail.setZoneNew(bundlingTaskInfo.getOrgName());

                MdmStoreExDTO mdmStoreExDTO1 = CacheVar.storeExMap.get(insertStoreNo);
                if (Objects.nonNull(mdmStoreExDTO1)) {
                    trackRetultAllDetail.setCity(mdmStoreExDTO1.getCity());
                    if (Constants.GOOD_FIRST.contains(spuListVo.getCategoryId().substring(0, 2))) {
                        if (Objects.nonNull(mdmStoreExDTO1)){
                            trackRetultAllDetail.setReviseStoreGroup(mdmStoreExDTO1.getStoreType());
                        }
                    }else if (Constants.GOOD_SECOND.contains(spuListVo.getCategoryId().substring(0, 4))) {
                        if (Objects.nonNull(mdmStoreExDTO1)){
                            trackRetultAllDetail.setReviseStoreGroup(zsStoreTypeNameMapping.get(mdmStoreExDTO1.getZsStoreTypeCode()));
                        }
                    }
                    Optional<OrgInfoBaseCache> businessOrgByBusinessId = CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO1.getBusinessId());
                    if (businessOrgByBusinessId.isPresent()){
                        trackRetultAllDetail.setCompid(String.valueOf(businessOrgByBusinessId.get().getBusinessOrgId()));
                        trackRetultAllDetail.setDataFromV2(businessOrgByBusinessId.get().getBusinessShortName());
                        trackRetultAllDetail.setDataFrom(businessOrgByBusinessId.get().getBusinessShortName());
                        trackRetultAllDetail.setStoreName(businessOrgByBusinessId.get().getShortName());
                    }
                }
                setGoodsProperty(spuListVo, trackRetultAllDetail);
                trackRetultAllDetail.setGmtCreate(new Date());
                trackRetultAllDetail.setStatus(Constants.NORMAL_STATUS);
                trackRetultAllDetail.setSubCategoryId(categoryBySubIds.get(Long.valueOf(spuListVo.getCategoryId())).getSubCategoryId().toString());
                trackRetultAllDetails1.add(trackRetultAllDetail);
            }
            trackRetultAllDetailExtendMapper.batchInsert(taskId,trackRetultAllDetails1);
        }
        List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);
        List<Long> companyOrgId = bundlingTaskStoreDetailList.stream().map(BundlingTaskStoreDetail::getCompanyOrgId).distinct().collect(Collectors.toList());
        // ③查企业必备表：平台下管理公司、SUK，存在，继续查最新一店一目表中这个管理公司下的门店、SKU对应的配置类型，如果全部都是平台必备，则删除对应的企业必备表；如果有企业必备，则不删除企业必备表。
        TaskNecessaryCompanyGoodsExample taskNecessaryCompanyGoodsExample = new TaskNecessaryCompanyGoodsExample();
        taskNecessaryCompanyGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdIn(companyOrgId).andGoodsNoEqualTo(platformGoodsData.getGoodsNo());
        List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(taskNecessaryCompanyGoodsExample);
        if (CollectionUtils.isNotEmpty(taskNecessaryCompanyGoods)){
            List<String> storeCodeList = bundlingTaskStoreDetailList.stream().filter(v->v.getBundlConfirmAble().equals(BundlBoolEnum.YES.getCode())).map(BundlingTaskStoreDetail::getStoreCode).distinct().collect(Collectors.toList());
            TrackRetultDetailParam retultDetailParam = new TrackRetultDetailParam();
            retultDetailParam.setTaskId(bundlingTaskInfo.getId());
            retultDetailParam.setPlatOrgid(bundlingTaskInfo.getOrgId().toString());
            retultDetailParam.setGoodsId(platformGoodsData.getGoodsNo());
            retultDetailParam.setOrgNoList(storeCodeList);
            List<TrackRetultAllDetail> retultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(retultDetailParam);
            if (CollectionUtils.isNotEmpty(retultAllDetails)){
                List<String> levelList = retultAllDetails.stream().map(TrackRetultAllDetail::getLevel).distinct().collect(Collectors.toList());
                if (levelList.size() == 1 && levelList.contains(String.valueOf(NecessaryTagEnum.PLATFORM_NECESSARY.getCode()))) {
                    TaskNecessaryCompanyGoodsExample example = new TaskNecessaryCompanyGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andCompanyOrgIdIn(companyOrgId).andGoodsNoEqualTo(platformGoodsData.getGoodsNo());
                    List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods1 = taskNecessaryCompanyGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessaryCompanyGoods1)) {
                        taskNecessaryCompanyGoodsExtendMapper.batchDel(taskNecessaryCompanyGoods1.stream().map(TaskNecessaryCompanyGoods::getId).collect(Collectors.toList()));
                    }
                }
            }
        }
        //          ④查店型必备表：平台、管理主体、城市下店型（根据SKU商品大类确定取哪类店型）、SKU，存在则删除N行。
        TaskNecessaryStoreTypeGoodsExample taskNecessaryStoreTypeGoodsExample = new TaskNecessaryStoreTypeGoodsExample();
        taskNecessaryStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andGoodsNoEqualTo(platformGoodsData.getGoodsNo()).andStoreTypeIn(commonEnumsTemp.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()));
        taskNecessaryStoreTypeGoodsMapper.deleteByExample(taskNecessaryStoreTypeGoodsExample);
        //        ⑤查店型选配表：平台、管理主体、城市下店型（根据SKU商品大类确定取哪类店型）、SKU ，存在则删除N行。
        TaskNecessaryChooseStoreTypeGoodsExample taskNecessaryChooseStoreTypeGoodsExample = new TaskNecessaryChooseStoreTypeGoodsExample();
        taskNecessaryChooseStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andGoodsNoEqualTo(platformGoodsData.getGoodsNo()).andStoreTypeIn(commonEnumsTemp.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()));
        taskNecessaryChooseStoreTypeGoodsMapper.deleteByExample(taskNecessaryChooseStoreTypeGoodsExample);

        //        ⑥查单店必备表：平台、管理主体、城市下的门店list、SKU，存在则删除N行。
        TaskNecessarySingleStoreGoodsExample taskNecessarySingleStoreGoodsExample = new TaskNecessarySingleStoreGoodsExample();
        taskNecessarySingleStoreGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andPlatformOrgIdEqualTo(bundlingTaskInfo.getOrgId()).andGoodsNoEqualTo(platformGoodsData.getGoodsNo()).andStoreCodeIn(StoreNoList);
        taskNecessarySingleStoreGoodsMapper.deleteByExample(taskNecessarySingleStoreGoodsExample);
    }

    /**
     * 根据子类id获取四级类目信息
     *
     * @param subCategoryIds
     * @return
     */
    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        logger.info("subCategoryIds:{}", JSON.toJSONString(subCategoryIds));
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0, 2)));
            set.add(Long.valueOf(cateStr.substring(0, 4)));
            set.add(Long.valueOf(cateStr.substring(0, 6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1, k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k, v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        return resultMap;
    }


    private void buildTaskNecessaryStoreTypeDTO(StoreTypeNecessaryData storeTypeNecessaryData, Long taskId, CommonEnums enums, TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods, TrackRetultAllDetail trackRetultAllDetail,Map<String, SpuListVo> spuMap) {

        taskNecessaryStoreTypeGoods.setTaskId(taskId);
        taskNecessaryStoreTypeGoods.setCompanyOrgId(storeTypeNecessaryData.getCompanyOrgId());
        OrgInfoBaseCache orgInfoBaseCache = CacheVar.getBusinessByOrgId(storeTypeNecessaryData.getCompanyOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到企业信息"));
        taskNecessaryStoreTypeGoods.setBusinessid(orgInfoBaseCache.getBusinessId());
        taskNecessaryStoreTypeGoods.setCompanyCode(orgInfoBaseCache.getSapCode());
        taskNecessaryStoreTypeGoods.setCompanyName(orgInfoBaseCache.getShortName());
        taskNecessaryStoreTypeGoods.setPlatformOrgId(orgInfoBaseCache.getPlatformOrgId());

        taskNecessaryStoreTypeGoods.setCity(storeTypeNecessaryData.getCity());
        taskNecessaryStoreTypeGoods.setPlatformName(trackRetultAllDetail.getZoneNew());
        taskNecessaryStoreTypeGoods.setStoreType(enums.getEnumValue());
        if (MapUtils.isNotEmpty(spuMap)) {
            SpuListVo spuListVo = spuMap.get(trackRetultAllDetail.getGoodsId());
            String categoryId = spuListVo.getCategoryId();
            taskNecessaryStoreTypeGoods.setCategoryId(StringUtils.isNotBlank(categoryId) ? Long.valueOf(categoryId.substring(0, 2)) : null);
            taskNecessaryStoreTypeGoods.setMiddleCategoryId(StringUtils.isNotBlank(categoryId) ? Long.valueOf(categoryId.substring(0, 4)) : null);
            taskNecessaryStoreTypeGoods.setSmallCategoryId(StringUtils.isNotBlank(categoryId) ? Long.valueOf(categoryId.substring(0, 6)) : null);
            taskNecessaryStoreTypeGoods.setSubCategoryId(StringUtils.isNotBlank(categoryId) ? Long.valueOf(categoryId) : null);
            taskNecessaryStoreTypeGoods.setBarCode(spuListVo.getBarCode());
            taskNecessaryStoreTypeGoods.setGoodsCommonName(spuListVo.getCurName());
            taskNecessaryStoreTypeGoods.setDescription(spuListVo.getDescription());
            taskNecessaryStoreTypeGoods.setApprovalNumber(spuListVo.getApprdocno());
        }
        taskNecessaryStoreTypeGoods.setCategoryName(trackRetultAllDetail.getClassoneName());
        taskNecessaryStoreTypeGoods.setMiddleCategoryName(trackRetultAllDetail.getClasstwoName());
        taskNecessaryStoreTypeGoods.setSmallCategoryName(trackRetultAllDetail.getClassthreeName());
        taskNecessaryStoreTypeGoods.setSubCategoryName(trackRetultAllDetail.getClassfourName());
        taskNecessaryStoreTypeGoods.setComposition(trackRetultAllDetail.getComponent());
        taskNecessaryStoreTypeGoods.setGoodsNo(trackRetultAllDetail.getGoodsId());
        taskNecessaryStoreTypeGoods.setGoodsName(trackRetultAllDetail.getGoodsname());
        taskNecessaryStoreTypeGoods.setGoodsUnit(trackRetultAllDetail.getGoodsunit());
        taskNecessaryStoreTypeGoods.setSpecifications(trackRetultAllDetail.getGoodsspec());
        taskNecessaryStoreTypeGoods.setDosageForm(trackRetultAllDetail.getJxCate1Name());
        taskNecessaryStoreTypeGoods.setManufacturer(trackRetultAllDetail.getManufacturer());
        taskNecessaryStoreTypeGoods.setPurchaseAttr(trackRetultAllDetail.getGrossprofit());
        taskNecessaryStoreTypeGoods.setChooseReason(trackRetultAllDetail.getBak());
        taskNecessaryStoreTypeGoods.setStoreSalesRate(new BigDecimal(trackRetultAllDetail.getInSalesRate()));
        taskNecessaryStoreTypeGoods.setStatus(Constants.NORMAL_STATUS);
        taskNecessaryStoreTypeGoods.setExtend("");
        taskNecessaryStoreTypeGoods.setVersion(Constants.DEF_VERSION);
        taskNecessaryStoreTypeGoods.setCreatedBy(null);
        taskNecessaryStoreTypeGoods.setGmtCreate(new Date());
        taskNecessaryStoreTypeGoods.setGmtUpdate(new Date());
        taskNecessaryStoreTypeGoods.setCreatedName("");
        taskNecessaryStoreTypeGoods.setUpdatedBy(null);
        taskNecessaryStoreTypeGoods.setUpdatedName("");
    }

    private String uplodaErrorData(String fileName, String filePath) {
        File errorFile = new File(filePath);
        String key = fileName;
        String presignatureUrl = "";
        try {
            String url = cosService.multipartUploadFile(key, errorFile);
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, +7);
            presignatureUrl = cosService.getPresignatureUrl(key, calendar.getTime());
        } catch (Exception e) {
            logger.error("分片上传文件失败", e);
        }
        return presignatureUrl;
    }

    private void delTempFile(String filePath) {
        Path path = Paths.get(filePath);
        try {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
                    Files.delete(file);
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException ioException) {
            logger.error("删除文件异常", ioException);
            ioException.printStackTrace();
        }
    }

    private void toRedis(RBucket<ImportResult> rBucket, int size,
                         ImportResult result, String key, String fileFileUrl) {
        if (size > 0) {
            result.setCode("1");
            result.setMessage("上传数据已处理完成，存在" + size + "行无效数据，请下载查看。");
            result.setResult(key);
            result.setFailFileUrl(fileFileUrl);
            rBucket.set(result, 12, TimeUnit.HOURS);
        } else {
            result.setCode("0");
            result.setMessage("上传数据全部处理成功。");
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }


}
