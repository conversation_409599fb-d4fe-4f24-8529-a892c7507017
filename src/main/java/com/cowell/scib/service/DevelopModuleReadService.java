package com.cowell.scib.service;

import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopSelectParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.vo.DevelopModuleRecordVO;
import com.cowell.scib.service.vo.DevelopModuleVO;
import com.cowell.scib.service.vo.amis.TabResult;

import java.util.List;

/**
 * 发版读操作服务
 * <AUTHOR>
 * @date 2022/8/29 15:08
 */
public interface DevelopModuleReadService {

    /**
     * 模块集合
     * @return
     */
    List<TabResult> listModuleVO(Integer useStatus);

    /**
     * 记录分页
     * @param developListParam
     * @return
     */
    PageResult<DevelopModuleRecordVO> listPageModuleRecordVO(DevelopListParam developListParam);

    /**
     * 模块详情接口
     * @param developListParam
     * @return
     */
    DevelopModuleVO moduleDetail(DevelopListParam developListParam);

    /**
     * 记录详情
     * @param developListParam
     * @return
     */
    DevelopModuleRecordVO recordDetail(DevelopListParam developListParam);

    /**
     * 下拉框接口
     * @param param
     * @return
     */
    List<OptionDto> developSelectUnifyList(DevelopSelectParam param);

    /**
     * 取最近三天的数据，提醒
     * @param moduleCode
     * @return
     */
    DevelopModuleRecordVO recordDetailReminder(String moduleCode, TokenUserDTO userDTO);

    /**
     * 是否有编辑角色
     * @return
     */
    Boolean editAblePermission();

    /**
     * 删除缓存key
     * @param redisKey
     */
    void delReminderKey(String redisKey);
}
