package com.cowell.scib.service;

import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.skuadjust.*;
import com.cowell.scib.service.vo.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SkuConfigureAdjustService {

    /**
     * 获取全集团四级分类树
     */
    List<JymlSkuMaxLimitConfigureTreeDTO> getSkuConfigureTree();
    List<JymlSkuMaxLimitConfigureTreeDTO> getSkuConfigureTreeByStore(Long storeOrgId);

    List<JymlSkuMaxLimitConfigure> getSkuConfigureById(Long id, Integer level, Long storeOrgId);

    /**
     * 根据门店+管控类别获取老管控标准
     * @param param
     * @return
     */
    List<JymlSkuMaxLimitConfigureTreeDTO> getOldConfigure(AdjustQueryParam param);
    /**
     * 获取审核明细
     * @param adjustId
     * @return
     */
    JymlStoreSkuLimitAdjustDTO getAdjustDetails(Long adjustId);

    /**
     * 创建审核单
     * @param param
     * @param userDTO
     */
    void createAdjust(SkuAdjustAddParam param, TokenUserDTO userDTO);

    /**
     * 提交审核
     * @param ids
     * @param userDTO
     */
    void sumbitApprove(List<Long> ids, TokenUserDTO userDTO);

    /**
     * 获取审核单列表
     * @param param
     * @param userDTO
     * @return
     */
    PageResponse<List<JymlStoreSkuLimitAdjustDTO>> getAdjustList(SkuAdjustQueryParam param, TokenUserDTO userDTO);

    /**
     * 导出审核单
     * @param userDTO
     * @param param
     */
    void exportAdjustList(TokenUserDTO userDTO, SkuAdjustQueryParam param);

    /**
     * 获取生效明细
     * @param param
     * @param userDTO
     * @return
     */
    PageResponse<List<JymlStoreSkuLimitAdjustEffectDTO>> getAdjustEffectList(SkuAdjustQueryParam param, TokenUserDTO userDTO);

    /**
     * 导出审核生效明细
     * @param userDTO
     * @param param
     */
    void exportAdjustEffectList(TokenUserDTO userDTO, SkuAdjustQueryParam param);

    /**
     * 导入
     * @param file
     * @param userDTO
     * @return
     */
    ImportResult importAdjust(MultipartFile file, TokenUserDTO userDTO);

    /**
     * 根据门店获取门店详情
     * @param storeOrgId
     * @return
     */
    MdmStoreExDTO getStoreInfoByStoreOrdIds(Long storeOrgId);

}
