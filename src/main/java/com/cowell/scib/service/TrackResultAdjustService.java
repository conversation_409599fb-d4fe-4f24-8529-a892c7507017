package com.cowell.scib.service;


import com.cowell.scib.service.dto.BdpResponseDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import org.springframework.web.multipart.MultipartFile;


public interface TrackResultAdjustService {

    ImportResult importStoreTypeDelGoods(MultipartFile file, Long taskId, TokenUserDTO userDTO) throws Exception;


    ImportResult importSingleStoreDelGoods(MultipartFile file,Long taskId, TokenUserDTO userDTO) throws Exception;

    ImportResult importPlatformGoods(MultipartFile file,Long taskId,Byte taskType, TokenUserDTO userDTO) throws Exception;


    ImportResult importCompanyGoods(MultipartFile file,Long taskId,Byte taskType, TokenUserDTO userDTO) throws Exception;

    ImportResult importStoreTypeGoods(MultipartFile file,Long taskId, TokenUserDTO userDTO) throws Exception;

    ImportResult importSingleStoreGoods(MultipartFile file,Long taskId, TokenUserDTO userDTO) throws Exception;

    ImportResult importAdjustNewStoreSuggest(MultipartFile file, Long taskId, Byte taskType, TokenUserDTO userDTO) throws Exception;

    void updateMDMData(Long taskId,Byte taskType,Integer version,TokenUserDTO userDTO);

    void updateOneStoreOneData(Long taskId,String orgNo,TokenUserDTO userDTO);

    Boolean uploadOperationalPermission(Long taskId, Integer version, TokenUserDTO userDTO);

    //List<String> getRemoveBusGoods (BundlingTaskInfo bundlingTaskInfo, Long businessId, List<String> goodNos);

    String getRedisKeyByTaskId (Long taskId,TokenUserDTO userDTO);

    String updateRedisKeyByTaskId (Long taskId,TokenUserDTO userDTO);

    void deleteFiveData (Long taskId,Integer type);

    void cleanGroupData(String date);

    BdpResponseDTO adjustBdpTrackResult(Long taskId, Integer version);
}
