package com.cowell.scib.service;

import com.cowell.scib.service.dto.CommonGoodsDTO;
import com.cowell.scib.service.vo.PageResponse;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;

import java.util.List;
import java.util.Map;

public interface SearchService {


    /**
     * 根据商品编码列表,查询商品基础信息Map, 商品列表的个数不能大于50
     * @param goodsNoList
     * @return
     */
    Map<String, SpuListVo> getSpuVOMap(List<String> goodsNoList);

    PageResponse<List<SpuNewVo>> getNewSpuList(SpuNewParamVo spuNewParamVo);

    Map<String, SpuNewVo> getNewSpuMap(SpuNewParamVo spuNewParamVo);

    CommonGoodsDTO getCommonGoods(SpuListVo spuListVo);

}
