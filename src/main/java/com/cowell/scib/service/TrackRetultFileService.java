package com.cowell.scib.service;

import com.cowell.scib.entityTidb.TrackResultFile;
import com.cowell.scib.service.dto.BdpResponseDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface TrackRetultFileService {
    /**
     * 企业店型级复盘表文件
     */
    void createFileLevelReviewFile(Long taskId, Integer version);
    /**
     * 生成成分复盘文件
     */
    void createTrackRetultCompositionReviewFile(Long taskId, Integer version);

    /**
     * 生成组货效率分析文件
     */
    void createTrackRetultEfficiencyAnalyseFile(Long taskId, Integer version);

    /**
     * 生成必备目录全量表(一店一目级别)文件
     */
    void createTrackRetultAllDetailFile(Long taskId, Integer version);
    /**
     * 生成组货结果明细生成
     */
    void createGroupGoodsResultsFile(Long taskId, Integer version);

    /**
     * 前四级必备全量（店型级）
     */
    void createTrackRetultTop4levelStoreGroupFile(Long taskId, Integer version);

    /**
     * 单店级必备明细（后2级）
     */
    void createTrackRetult2levelSingleFile(Long taskId, Integer version);

    /**
     * 查询六级必备目录信息
     * @param taskId
     */
    void selectTrackRetultLevelNecessary(Long taskId);

     void selectTrackRetultLevelNecessaryByPage(Long taskId, int page, int perPage);

    /**
     * 企业店型级复盘表文件
     */
    Map<Integer, List<TrackResultFile>> queryLevelNecessaryFile(Long taskId, Integer version);

    void createFileNewStoreFile(Long taskId, HttpServletResponse response) throws Exception;

}
