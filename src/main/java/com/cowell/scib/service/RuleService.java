package com.cowell.scib.service;

import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.rule.*;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.param.rule.RuleAddParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:21
 */
public interface RuleService {
    /**
     * 查询规则列表
     * @param ruleParam
     * @param userDTO
     */
    Map<String, RuleDetailDTO> getRuleList(RuleParam ruleParam, TokenUserDTO userDTO);

    /**
     * 任务配置信息
     * @param orgId
     * @param scopeCodeList
     * @param configType
     * @return
     */
    Map<String, RuleDetailDTO> getConfigRuleList(Long orgId, List<String> scopeCodeList, Byte configType);

    /**
     * 初始化
     * @param orgId
     * @param userDTO
     */
    void initConfigRuleInfo(Long orgId, TokenUserDTO userDTO, Integer version);

    /**
     * 添加
     * @param ruleAddParam
     * @param userDTO
     */
    void add(RuleAddParam ruleAddParam, TokenUserDTO userDTO);

    /**
     * 枚举列表
     * @param ruleParam
     * @param userDTO
     * @return
     */
    Map<String, List<OptionDto>> getRuleEnumList(RuleParam ruleParam, TokenUserDTO userDTO);
    /**
     * 修改规则
     * @param ruleAddParam
     * @param userDTO
     * @return
     */
    void updateRule(RuleAddParam ruleAddParam, TokenUserDTO userDTO);

    /**
     *  @param file
     * @param orgId
     * @param scopeCode
     * @param configType
     * @param userDTO
     */
    List<ErrorGoodsDTO> importGoods(MultipartFile file, Long orgId, String scopeCode, String configType, String dictCode, TokenUserDTO userDTO) throws Exception;

    /**
     * 添加组货商品黑名单
     * @param ruleAddParam
     * @param userDTO
     * @return
     */
    void addGroupBlackGoods(RuleAddParam ruleAddParam,TokenUserDTO userDTO);

    /**
     * 查询组货商品黑名单
     * @param ruleParam
     * @param userDTO
     * @return
     */
    PageResult<CommonGoodsDTO> queryGroupBlackGoods(RuleParam ruleParam, TokenUserDTO userDTO);
    /**
     * 删除组货商品黑名单
     * @param ruleAddParam
     * @param userDTO
     * @return
     */
    void delGroupBlackGoods(RuleAddParam ruleAddParam,TokenUserDTO userDTO);
    /**
     * 线上热销品导入
     * @param file
     * @param userDTO
     */
    HotGoodsResponseDTO importHotGoods(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 导出商品
     * @param orgId
     * @param dictCode
     * @param configType
     * @param response
     * @param userDTO
     * @throws Exception
     */
    void exportGoods(Long orgId, String dictCode, String configType, HttpServletResponse response, TokenUserDTO userDTO) throws Exception;


    /**
     * 批量删除热销品
     * @param hotGoodsImportDTO
     */
    void batchDelHotGoods(HotGoodsImportDTO hotGoodsImportDTO);

    /**
     * 删除
     * @param orgId
     * @param userDTO
     * @param version
     */
    void delConfigRuleInfo(Long orgId, TokenUserDTO userDTO, Integer version);

    /**
     * 导入不经营目录
     * @param file
     * @param orgId
     * @param scopeCode
     * @param configType
     * @param userDTO
     * @return
     */
    List<ErrorUnmanageDTO> importUnmanageGoods(MultipartFile file, Long orgId, String scopeCode, String configType, TokenUserDTO userDTO) throws Exception;

    /**
     * 检查传入的门店和分类, 是否存在于不经营目录
     * @param storeIds 门店ID集合
     * @param categoryIds 分类ID集合(传入4级分类)
     * @return  Map<storeID, List<categoryId>>
     */
    Map<Long, List<String>> checkExistsUnmanage(List<Long> storeIds, List<String> categoryIds);

    BdpResponseDTO pushConfigToBdp(Long orgId, Long taskId, Integer month, Byte configType, String dataVersion) throws Exception ;
}
