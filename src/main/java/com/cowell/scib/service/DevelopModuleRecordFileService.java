package com.cowell.scib.service;

import com.cowell.scib.entity.DevelopModuleRecordFile;
import com.cowell.scib.service.dto.DevelopModuleRecordFileDTO;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.service.vo.DevelopModuleRecordFileVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:25
 */
public interface DevelopModuleRecordFileService {

    /**
     * 按照顺序返回文件列表
     * @param codeVersionKey
     * @return
     */
    List<DevelopModuleRecordFile> listFileSort(String codeVersionKey);

    /**
     * 按照顺序返回文件列表 map
     * @param codeVersionKeyList
     * @return
     */
    Map<String, List<DevelopModuleRecordFile>> mapFileSort(List<String> codeVersionKeyList);

    /**
     * 添加或编辑文件
     * @param fileVOList
     * @param isCreate
     * @param developRecordAddParam
     */
    void addOrEditFile(String fileVOList, DevelopRecordAddParam developRecordAddParam, boolean isCreate);

    /**
     * 删除文件
     * @param id
     */
    void deleteFileById(Integer id);

    /**
     * 通过ID查询文件
     * @param id
     * @return
     */
    DevelopModuleRecordFile recordFileById(Integer id);

    /**
     * 转换String
     * @param fileList
     * @return
     */
    String fileUrlListToString(List<DevelopModuleRecordFileDTO> fileList);

}
