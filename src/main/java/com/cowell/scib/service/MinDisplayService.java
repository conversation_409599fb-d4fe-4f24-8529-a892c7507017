package com.cowell.scib.service;

import com.cowell.scib.service.dto.SeasonalGoodsPushDTO;
import com.cowell.scib.service.dto.SensitiveUpdateParam;

import javax.servlet.http.HttpServletResponse;

public interface MinDisplayService {

    void getMinDisplayFromMdm(SeasonalGoodsPushDTO seasonalGoodsDTO);

    void updateSingleNecessary(SensitiveUpdateParam param);

    void exportMdmTask(Long taskId, HttpServletResponse response) throws Exception;
}
