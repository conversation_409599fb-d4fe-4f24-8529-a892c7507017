package com.cowell.scib.service;

import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelRoleConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryRoleConfigUpdateParam;
import com.cowell.scib.service.dto.config.NecessaryUpdateParam;
import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.cowell.scib.service.vo.amis.CommonResponse;

import java.util.List;

public interface NecessaryLevelConfigService {
    CommonResponse<NecessaryLevelConfigDTO> list(AmisPageParam param);

    void edit(NecessaryUpdateParam param, TokenUserDTO userDTO);

    CommonResponse<NecessaryLevelRoleConfigDTO> roleList(Long orgId);

    void roleEdit(NecessaryRoleConfigUpdateParam param, TokenUserDTO userDTO);

    List<NecessaryLevelConfigDTO> getNecessaryTagByRole(TokenUserDTO userDTO);
}
