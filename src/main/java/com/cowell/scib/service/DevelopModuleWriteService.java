package com.cowell.scib.service;

import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;

/**
 * 发版写操作服务
 * <AUTHOR>
 * @date 2022/8/29 15:08
 */
public interface DevelopModuleWriteService {

    /**
     * 保存或编辑
     * @param developAddParam
     */
    void moduleAddOrEdit(DevelopAddParam developAddParam, TokenUserDTO userDTO);

    /**
     * 发版记录保存或编辑
     * @param developRecordAddParam
     */
    void recordAddOrEdit(DevelopRecordAddParam developRecordAddParam, TokenUserDTO userDTO);

    /**
     * 删除文件  数据库和文件服务器
     * @param id
     */
    void deleteFile(Integer id);
}
