package com.cowell.scib.service.vo.amis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@ApiModel("动态列，字段及名称信息")
public class ColumnVO implements Serializable {

    private static final long serialVersionUID = -7447997713176322438L;

    @ApiModelProperty("字段名")
    private String name;

    @ApiModelProperty("表头名")
    private String label;

    @ApiModelProperty("合并表头名")
    private String groupName;

    @ApiModelProperty("是否可以编辑")
    private AmisQuickEditVO quickEdit;

    @ApiModelProperty("最小位数")
    private Integer min;

    @ApiModelProperty("精度")
    private Integer precision;

    @ApiModelProperty("宽度px")
    private Integer width;

    public static ColumnVO getTableColumn(String name ,String label) {
       return getTableColumnWithGroup(name, label, null);
    }

    public static ColumnVO getTableColumnWithGroup(String name ,String label, String groupName) {
        ColumnVO columnVO = new ColumnVO();
        columnVO.setName(name);
        columnVO.setLabel(label);
        columnVO.setGroupName(groupName);
        return columnVO;
    }

    public ColumnVO obtainCommonTableColumnWithGroup() {
        ColumnVO newColumnVO = new ColumnVO();
        newColumnVO.setName(this.getName());
        newColumnVO.setLabel(this.getLabel());
        newColumnVO.setGroupName(this.groupName);
        return newColumnVO;
    }

    public static ColumnVO getTableEditColumn(String name ,String label, boolean saveImmediately) {
        ColumnVO columnVO = new ColumnVO();
        columnVO.setName(name);
        columnVO.setLabel(label);
        columnVO.setQuickEdit(AmisQuickEditVO.getQuickEditVO(saveImmediately));
        return columnVO;
    }

    public static ColumnVO getTableInputNumberColumn(String name ,String label, Integer min, Integer precision, Integer width, boolean saveImmediately) {
        ColumnVO columnVO = new ColumnVO();
        columnVO.setName(name);
        columnVO.setLabel(label);
        columnVO.setMin(min);
        columnVO.setPrecision(precision);
        columnVO.setWidth(width);
        columnVO.setQuickEdit(AmisTypeQuickEditVO.getNumberQuickEditVO(saveImmediately));
        return columnVO;
    }

    public static ColumnVO getTableInputNumberColumn(String name ,String label, Integer min,  Integer precision, boolean saveImmediately) {
        return getTableInputNumberColumn(name, label, min, precision, null, saveImmediately);
    }

}

