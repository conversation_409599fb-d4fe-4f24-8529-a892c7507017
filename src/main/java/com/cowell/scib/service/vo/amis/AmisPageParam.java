package com.cowell.scib.service.vo.amis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: scib
 * @description: Amis的分页参数
 * @author: jmlu
 * @create: 2022-04-06 20:12
 **/
@Data
public class AmisPageParam {

    @ApiModelProperty("页码")
    private int page = 1;

    @ApiModelProperty("每页条数")
    private int perPage = 10;

    private long offset = 0;

    private boolean toPage = true;
}
