package com.cowell.scib.service.vo.amis;

import com.cowell.scib.enums.AmisComponentTypeEnum;
import com.cowell.scib.service.AmisDataInInterface;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 集合返回值，不分页
 * <AUTHOR>
 * @date 2022/8/29 17:09
 */
@Data
public class ListResult<T> implements Serializable, AmisDataInInterface {

    private String type = AmisComponentTypeEnum.TABS.getCode();

    private List<T> tabs;

    public ListResult(List<T> options) {
        this.tabs = options == null ? Collections.emptyList() : options;
    }
}
