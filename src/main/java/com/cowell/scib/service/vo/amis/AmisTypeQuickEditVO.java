package com.cowell.scib.service.vo.amis;

import com.cowell.scib.enums.AmisComponentTypeEnum;
import lombok.Data;

/**
 * @program: scib
 * @description: 带type属性的编辑AMisVO
 * @author: jmlu
 * @create: 2022-05-07 21:03
 **/

@Data
public class AmisTypeQuickEditVO extends AmisQuickEditVO {

    private String type;

    public static AmisTypeQuickEditVO getNumberQuickEditVO( boolean saveImmediately) {
        AmisTypeQuickEditVO typeQuickEditVO = new AmisTypeQuickEditVO();
        typeQuickEditVO.setType(AmisComponentTypeEnum.INPUT_NUMBER.getCode());
        typeQuickEditVO.setSaveImmediately(saveImmediately);
        return typeQuickEditVO;
    }

}
