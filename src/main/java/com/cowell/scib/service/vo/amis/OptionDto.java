package com.cowell.scib.service.vo.amis;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

import java.util.Comparator;
import java.util.Objects;

/**
 * @ClassName OptionDto
 * @Description 下拉框统一返回提
 * <AUTHOR>
 * @Date 2020/9/17 14:13
 **/
@ApiModel("下拉框返回类")
@ToString
public class OptionDto implements AmisDataInInterface, Comparator<OptionDto> {

    @ApiModelProperty("文本")
    private final String label ;

    @ApiModelProperty("值")
    private final String value ;

    public OptionDto(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    @Override
    public int compare(OptionDto o1, OptionDto o2) {
        return o1.label.compareTo(o2.label);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof OptionDto)) return false;
        OptionDto dto = (OptionDto) o;
        return Objects.equals(getLabel(), dto.getLabel()) &&
                Objects.equals(getValue(), dto.getValue());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getLabel(), getValue());
    }
}
