package com.cowell.scib.service.vo;


/**
 * Created by j<PERSON><PERSON> on 2018/5/8.
 */

public class PageResponse<T> extends CommonResponse<T> {
    private int pageSize;
    private int page;
    private long totalSize;
    private String scrollId;
    private long totalPage;
    /**
     * 数据是否都传完
     */
    private Boolean finished = false;

    /**
     * 分页数据是否准备好
     */
    private boolean pagePrepared = false;

    public PageResponse() {
    }

    public PageResponse(T result) {
        super(result);
    }

    public PageResponse(int code, String message, int pageSize, int page) {
        super(code, message);
        this.pageSize = pageSize;
        this.page = page;
    }
    public PageResponse(int code, String message, String scrollId) {
        super(code, message);
        this.scrollId = scrollId;
    }
    public PageResponse(int code, String message) {
        super(code, message);
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public String getScrollId() {
        return scrollId;
    }

    public void setScrollId(String scrollId) {
        this.scrollId = scrollId;
    }

    public Boolean getFinished() {
        return finished;
    }

    public void setFinished(Boolean finished) {
        this.finished = finished;
    }

    public boolean isPagePrepared() {
        return pagePrepared;
    }

    public void setPagePrepared(boolean pagePrepared) {
        this.pagePrepared = pagePrepared;
    }

    public long getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(long totalPage) {
        this.totalPage = totalPage;
    }
}

