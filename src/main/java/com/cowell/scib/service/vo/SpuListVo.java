package com.cowell.scib.service.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 集团标品
 */
@Data
public class SpuListVo implements Serializable {
    private Long id;
    /**
     *通用名名称
     */
    private String curName;
    private String name;
    /**
     * 规格
     */
    private String jhiSpecification;
    /**
     * 生产厂商
     */
    private String factoryid;

    /**
     * 剂型
     */
    private String dosageformsid;
    /**
     * 图片
     */
    private String picUrl;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 商品助记码
     */
    private String opCode;
    /**
     * 高济商品唯一编码
     */
    private String goodsNo;

    /**
     * 批准文号
     */
    private String apprdocno;

    /**
     * 商品大类
     */
    private String groupno;

    /**
     * 商品中类
     */
    private String subclass;

    /**
     * 类推等级
     */
    private String pushlevel;
    /**
     * 描述
     */
    private String description;

    /**
     * 状态(-1删除，0正常)
     */
    private Integer status;

    private String comPv;
    /**
     * 单位
     */
    private String goodsunit;

    private String categoryId;

    /**
     * 产地
     */
    private String prodarea;

    /**
     * 成分(comPv:13)
     */
    private String component;

    private String pathName;
    private String level1;
    private String level2;
    private String level3;
    private String level4;
}
