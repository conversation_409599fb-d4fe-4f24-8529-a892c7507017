package com.cowell.scib.service.vo;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/29 13:44
 */
@Data
public class DevelopModuleVO implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = 991310737749565236L;

    @ApiModelProperty("发版模块编码，唯一")
    private String moduleCode;

    @ApiModelProperty("发版模块名称")
    private String moduleName;

    /**
     * 启用状态  0-停用 1-启用
     */
    @ApiModelProperty("启用状态  0-停用 1-启用")
    private Byte useStatus;
}
