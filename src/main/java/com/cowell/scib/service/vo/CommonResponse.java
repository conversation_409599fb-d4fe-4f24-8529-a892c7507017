package com.cowell.scib.service.vo;

import com.cowell.scib.enums.ErrorCodeEnum;

public class CommonResponse<T>{
    private int     code;
    private String  message;
    private T result;

    public CommonResponse() {
        this.code = 0 ;
    }

    public CommonResponse(T result) {
        this.result = result;
        this.code = ErrorCodeEnum.SUCCESS.getCode() ;
        this.message = ErrorCodeEnum.SUCCESS.getMsg();
    }


    public CommonResponse(int code, String message, T result) {
        this.code = code;
        this.message = message;
        this.result = result;
    }


    public CommonResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public CommonResponse(ErrorCodeEnum returnCodeEnum) {
        this.code = returnCodeEnum.getCode();
        this.message = returnCodeEnum.getMsg();
    }

    public CommonResponse(ErrorCodeEnum returnCodeEnum, T result) {
        this.code = returnCodeEnum.getCode();
        this.message = returnCodeEnum.getMsg();
        this.result = result;
    }

    /**
     * Getter method for <tt>code</tt>.
     *
     * @return value of code
     */
    public int getCode() {
        return code;
    }

    /**
     * Setter method for <tt>code</tt>.
     *
     * @param code value to be assigned to code
     */
    public void setCode(int code) {
        this.code = code;
    }

    /**
     * Getter method for <tt>message</tt>.
     *
     * @return value of message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Setter method for <tt>message</tt>.
     *
     * @param message value to be assigned to message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * Getter method for <tt>result</tt>.
     *
     * @return value of result
     */
    public T getResult() {
        return result;
    }

    /**
     * Setter method for <tt>result</tt>.
     *
     * @param result value to be assigned to result
     */
    public void setResult(T result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "CommonResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
