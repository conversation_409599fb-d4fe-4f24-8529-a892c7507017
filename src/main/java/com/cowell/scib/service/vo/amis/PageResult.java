package com.cowell.scib.service.vo.amis;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@ApiModel("分页返回类")
@Data
public class PageResult<T> implements AmisDataInInterface {

    /**
     * 总页数
     */
    @ApiModelProperty("总数")
    private Long total;

    @ApiModelProperty("返回行")
    private List<T> rows;

    @ApiModelProperty("动态列信息")
    private List<ColumnVO> columns;

    public PageResult(){}

    public PageResult(Long total, List<T> rows) {
        this.total = total == null ? 0L : total;
        this.rows = rows == null ? Collections.emptyList() : rows;
    }

    public PageResult(Long total, List<ColumnVO> columns, List<T> rows) {
        this.total = total == null ? 0L : total;
        this.rows = rows == null ? Collections.emptyList() : rows;
        this.columns = columns == null ? Collections.emptyList() : columns;
    }

    public Long getTotal() {
        return total;
    }

    public List<T> getRows() {
        return rows;
    }
}
