package com.cowell.scib.service.vo;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/29 13:46
 */
@Data
public class DevelopModuleRecordVO implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = 4413711462689621928L;

    /**
     * 主键
     */
    @ApiModelProperty("发版记录主键ID")
    private Integer recordId;

    /**
     * 功能模块编码
     */
    @ApiModelProperty("模块编码")
    private String moduleCode;

    /**
     * 发版记录版本号
     */
    @ApiModelProperty("发版记录版本号")
    private String developVersion;

    /**
     * 发版类型  1-BUG修复  2-新功能  3-功能优化
     */
    @ApiModelProperty("发版类型  1-BUG修复  2-新功能  3-功能优化")
    private Byte developType;

    /**
     * 发版类型  1-BUG修复  2-新功能  3-功能优化
     */
    @ApiModelProperty("发版类型  1-BUG修复  2-新功能  3-功能优化")
    private String developTypeDesc;

    /**
     * 发版记录标题
     */
    @ApiModelProperty("发版记录标题")
    private String developTitle;

    /**
     * 发版记录内容
     */
    @ApiModelProperty("发版记录内容")
    private String developContent;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    private String developTime;


    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String updatedName;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private String gmtUpdate;

    /**
     * 文件url，逗号分割
     */
    private String fileVOList;

    @ApiModelProperty("图片地址，逗号分割（amis要求）")
    private String imageUrlList;

    @ApiModelProperty("触达通道集合，逗号分割（amis要求）")
    private String reachChannelList;

    @ApiModelProperty("触达组id集合，逗号分割（amis要求）")
    private String reachGroupIdList;

    @ApiModelProperty("触达url")
    private String developUrl;

    @ApiModelProperty("1：按角色、2：指定人员")
    private Integer selectMethod;
}
