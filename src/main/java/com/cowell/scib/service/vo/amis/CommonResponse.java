package com.cowell.scib.service.vo.amis;

import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@ApiModel("AMIS统一返回对象list")
@Getter
@ToString
@EqualsAndHashCode
public class CommonResponse<T extends AmisDataInInterface> {
    @ApiModelProperty("状态")
    private Integer status ;

    @ApiModelProperty("消息")
    private String msg ;

    @ApiModelProperty("数据")
    private List<T> data ;
    
    public CommonResponse() {
    }

    public CommonResponse(Integer status, String msg, List<T> data) {
        this.status = status == null ? 0 : status;
        this.msg = msg == null ? "" : msg;
        this.data = data == null ? new ArrayList<T>() : data;
    }

    public CommonResponse(Integer status, String msg) {
        this(status, msg, null);
    }

    public CommonResponse(ErrorCodeEnum returnCodeEnum, List<T> data) {
        this(returnCodeEnum.getCode(), returnCodeEnum.getMsg(), data);
    }

    public CommonResponse(ErrorCodeEnum returnCodeEnum) {
        this(returnCodeEnum, null);
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> ok(){
        return new CommonResponse<T>(ErrorCodeEnum.SUCCESS);
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> ok(String msg){
        return new CommonResponse<T>(ErrorCodeEnum.SUCCESS.getCode(), msg);
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> ok(List<T> data){
        return new CommonResponse<T>(ErrorCodeEnum.SUCCESS, data);
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> ok(String msg, List<T> data){
        return new CommonResponse<T>(ErrorCodeEnum.SUCCESS.getCode(), msg, data);
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> ok(Integer status, String msg){
        return new CommonResponse<T>(status, msg);
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> error(ErrorCodeEnum returnCodeEnum){
        return new CommonResponse<T>(returnCodeEnum) ;
    }

    public static<T extends AmisDataInInterface> CommonResponse<T> error(Integer status, String msg){
        return new CommonResponse<T>(status, msg);
    }
}
