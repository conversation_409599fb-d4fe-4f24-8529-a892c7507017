package com.cowell.scib.service.vo.amis;

import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ApiModel("AMIS统一返回对象")
@Getter
@ToString
@EqualsAndHashCode
public class CommonResult<T extends AmisDataInInterface> {
    @ApiModelProperty("状态")
    private Integer status ;

    @ApiModelProperty("消息")
    private String msg ;

    @ApiModelProperty("数据")
    private T data ;

    public CommonResult() {
    }

    public CommonResult(Integer status, String msg, T data) {
        this.status = status == null ? 0 : status;
        this.msg = msg == null ? "" : msg;
        this.data = data == null ? (T)new AmisMap<>() : data;
    }

    public CommonResult(Integer status, String msg) {
        this(status, msg, null);
    }

    public CommonResult(ErrorCodeEnum returnCodeEnum, T data) {
        this(returnCodeEnum.getCode(), returnCodeEnum.getMsg(), data);
    }

    public CommonResult(ErrorCodeEnum returnCodeEnum) {
        this(returnCodeEnum, null);
    }

    public static<T extends AmisDataInInterface> CommonResult<T> ok(){
        return new CommonResult<T>(ErrorCodeEnum.SUCCESS);
    }

    public static<T extends AmisDataInInterface> CommonResult<T> ok(String msg){
        return new CommonResult<T>(ErrorCodeEnum.SUCCESS.getCode(), msg);
    }

    public static<T extends AmisDataInInterface> CommonResult<T> ok(T data){
        return new CommonResult<T>(ErrorCodeEnum.SUCCESS, data);
    }

    public static<T extends AmisDataInInterface> CommonResult<T> ok(String msg, T data){
        return new CommonResult<T>(ErrorCodeEnum.SUCCESS.getCode(), msg, data);
    }

    public static<T extends AmisDataInInterface> CommonResult<T> ok(Integer status,String msg){
        return new CommonResult<T>(status, msg);
    }


    public static<T extends AmisDataInInterface> CommonResult<PageResult<T>> ok(PageResult<T> pageResult){
        return new CommonResult<>(ErrorCodeEnum.SUCCESS, pageResult);
    }

    public static<T extends AmisDataInInterface> CommonResult<T> error(ErrorCodeEnum returnCodeEnum){
        return new CommonResult<T>(returnCodeEnum) ;
    }

    public static<T extends AmisDataInInterface> CommonResult<T> error(Integer status, String msg){
        return new CommonResult<T>(status, msg);
    }
}
