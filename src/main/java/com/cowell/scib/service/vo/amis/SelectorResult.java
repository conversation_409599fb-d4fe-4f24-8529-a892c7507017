package com.cowell.scib.service.vo.amis;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName SelectorResult
 * @Description 下拉框
 * <AUTHOR>
 * @Date 2020/10/12 18:01
 **/
@ApiModel("下拉框")
@ToString
public class SelectorResult implements AmisDataInInterface {

    @ApiModelProperty("操作项")
    private final List<OptionDto> options ;

    public SelectorResult(List<OptionDto> options) {
        this.options = options == null ? Collections.emptyList() : options;
    }

    public List<OptionDto> getOptions() {
        return options;
    }

    public static SelectorResult createSelectorResult(List<OptionDto> options) {
        return new SelectorResult(options);
    }

}
