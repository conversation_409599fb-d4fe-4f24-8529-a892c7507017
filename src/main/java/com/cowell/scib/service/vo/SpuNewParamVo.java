package com.cowell.scib.service.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class SpuNewParamVo implements Serializable {
    private static final long serialVersionUID = 6115578744909410647L;

    @ApiModelProperty("连锁店ID")
    private Long businessId;
    @ApiModelProperty("关键词：支持商品编码/商品名称/通用名搜索")
    private String keyWord;
    @ApiModelProperty("商家编码")
    private List<String> goodsNoList;
    @ApiModelProperty("大类目ID")
    private Long bigCategoryId;
    @ApiModelProperty("类目ID")
    private Long categoryId;
    @ApiModelProperty("促销分类ID")
    private Long cateMarketId;
    @ApiModelProperty("促销分类名称")
    private String cateMarketName;
    @ApiModelProperty("当前页数：默认1")
    private Integer page = 1;
    @ApiModelProperty("显示数据条数：默认10")
    private Integer pageSize = 10;
    @ApiModelProperty("定制化需求：如果传1，就返回data")
    private Integer typeData ;
    @ApiModelProperty("定制化需求：海波")
    private List<Long> goodsNos;
    @ApiModelProperty("定制化需求：如果传1，in  2， not in 海波")
    private Integer goodsType;
    @ApiModelProperty("数据来源 0 ：mdm下发数据，1：自建商品，2：导入 废弃  3：互联网医院 废弃 4：套餐商品(如需多个用逗号隔开，'0,1,2')")
    private String source = "0";

    @ApiModelProperty("通用名称")
    private List<String> names;

    @ApiModelProperty("厂商名称")
    private List<String> factoryIds;
    @ApiModelProperty("连锁店IDList")
    private List<Long> businessIdList;

    public List<Long> getBusinessIdList() {
        return businessIdList;
    }

    public void setBusinessIdList(List<Long> businessIdList) {
        this.businessIdList = businessIdList;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public List<String> getGoodsNoList() {
        return goodsNoList;
    }

    public void setGoodsNoList(List<String> goodsNoList) {
        this.goodsNoList = goodsNoList;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getBigCategoryId() {
        return bigCategoryId;
    }

    public void setBigCategoryId(Long bigCategoryId) {
        this.bigCategoryId = bigCategoryId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getCateMarketId() {
        return cateMarketId;
    }

    public void setCateMarketId(Long cateMarketId) {
        this.cateMarketId = cateMarketId;
    }

    public String getCateMarketName() {
        return cateMarketName;
    }

    public void setCateMarketName(String cateMarketName) {
        this.cateMarketName = cateMarketName;
    }

    public Integer getTypeData() {
        return typeData;
    }

    public void setTypeData(Integer typeData) {
        this.typeData = typeData;
    }

    public List<Long> getGoodsNos() {
        return goodsNos;
    }

    public void setGoodsNos(List<Long> goodsNos) {
        this.goodsNos = goodsNos;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<String> getNames() {
        return names;
    }

    public void setNames(List<String> names) {
        this.names = names;
    }

    public List<String> getFactoryIds() {
        return factoryIds;
    }

    public void setFactoryIds(List<String> factoryIds) {
        this.factoryIds = factoryIds;
    }

    @Override
    public String toString() {
        return "SpuNewParamVo{" +
                "businessId=" + businessId +
                ", keyWord='" + keyWord + '\'' +
                ", goodsNoList=" + goodsNoList +
                ", bigCategoryId=" + bigCategoryId +
                ", categoryId=" + categoryId +
                ", cateMarketId=" + cateMarketId +
                ", cateMarketName='" + cateMarketName + '\'' +
                ", page=" + page +
                ", pageSize=" + pageSize +
                ", typeData=" + typeData +
                ", goodsNos=" + goodsNos +
                ", goodsType=" + goodsType +
                ", source=" + source +
                '}';
    }
}
