package com.cowell.scib.service.vo.amis;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/31 11:16
 */
@Data
public class TabResult implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = -7994189714848829961L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("标题")
    private  String title ;

    @ApiModelProperty("文本")
    private  String content ;

    @ApiModelProperty("启用状态  0-停用 1-启用")
    private String useStatus;

    @ApiModelProperty("更新人名字")
    private String updatedName;

    @ApiModelProperty("更新时间（操作时间）")
    private String gmtUpdate;
}
