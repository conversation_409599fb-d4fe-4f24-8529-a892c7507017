package com.cowell.scib.service.vo.amis;

import com.cowell.scib.service.AmisDataInInterface;
import lombok.ToString;

@ToString
public class IdLongData implements AmisDataInInterface {

    private Long id;

    private IdLongData(Long id) {
        this.id = id == null ? 0L : id;
    }

    public static IdLongData getInstance(Long id) {
        return new IdLongData(id);
    }

    public Long getId() {
        return id;
    }

}
