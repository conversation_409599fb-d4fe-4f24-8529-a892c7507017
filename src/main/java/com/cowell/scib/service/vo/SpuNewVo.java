package com.cowell.scib.service.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 连锁商品属性
 */
public class SpuNewVo implements Serializable {
    private static final long serialVersionUID = 8632639979978903558L;
    private Long id;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 产品名称(名称)
     */
    private String name;

    /**
     * 市场价
     */
    private Long marketPrice;

    /**
     * 上市时间
     */
    private Date marketTime;

    /**
     * 包装清单
     */
    private String packingList;

    /**
     * 状态 0:未发布(保存，在仓中)、1:已发布 2:已删除
     */
    private Integer status;

    /**
     * 产品图片
     */
    private String picUrl;

    /**
     * 搜索扩展字段
     */
    private String queryExtend;

    /**
     * 关键属性pv
     */
    private String keyPv;

    /**
     * 普通属性pv
     */
    private String comPv;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtUpdate;

    /**
     * 版本号
     */
    private Integer version;





    /**
     * 高济商品唯一编码
     */
    private String goodsNo;


    /**
     * 状态 0:未发布(保存，在仓中)、1:已发布
     */
    private Integer publishStatus;

    /**
     * 更新人ID
     */
    private String updatedBy;

    /**
     * 品牌id
     * 默认：-1
     */
    private Long brandId;

    /**
     * 商品名助记码
     */
    private String opCode;

    /**
     * 通用名
     */
    private String curName;

    /**
     * 通用名助记码
     */
    private String curOpCode;

    /**
     * 产品描述
     */
    private String description;
    /**
     * 标签
     */
    private String tag;
    /**
     * 其他信息
     */
    private String extraInfo;
    /**
     * spu特征
     */
    private String features;
    /**
     *扩展属性json串
     */
    private String attrExtend;

    /**
     * 扩展字段
     */
    private String extend;
    /**
     * 条形码
     */
    private Long spuExtendId;
    /**
     * 条形码
     */
    private String barCode;

    /**
     * 规格
     */
    private String jhiSpecification;

    /**
     * 连锁id
     */
    private Long businessId;
//============ 解析数据 ==================================
    /**
     * 生产厂家
     */
    private String factoryid;

    /**
     * 生产厂家简称
     */
    private String shortFactoryid;

    /**
     * 是否特管药品  1 是 2 否
     */
    private String specialctrl;

    /**
     * 是否可拆零  1 是 2 否
     */
    private String pieceind;

    /**
     * 渠道属性
     */
    private String purchchannel;


    /**
     * 养护类型
     */
    private String mainten_type;


    /**
     * 储存条件
     */
    private String storagecond;


    /**
     * 养护方法 4181：外观质量检查法 4180 ： 晾晒法
     * 4179对抗养护法德国 4176清洁养护法 4177 密封养护法
     * 4178  低温养护法
     */
    private String mainten_method;

    /**
     * 养护周期
     */
    private String mainten_period;


    /**
     * 制剂规格
     */
    private String prepspec;


    /**
     * 标识是否冷链存储 1 是 2 否
     */
    private String coldchainind;


    /**
     * 冷链储存温度下限
     */
    private String tempdown;

    /**
     * 冷链储存温度上限
     */
    private String tempup;

    /**
     * 冷链运输时限
     */
    private String transtimelmt;

    /**
     * 产地
     */
    private String prodarea;

    /**
     * 剂型
     */
    private String dosageformsid;

    /**
     * 基本计量单位
     */
    private String goodsunit;

    /**
     * 拆零单位
     */
    private String pieceunit;

    /**
     * 效期单位
     */
    private String periodunit;

    /**
     * 拆零比例
     */
    private String piecerate;

    /**
     * 保质期
     */
    private String validperiod;

    /**
     * 是否批号管理-标识商品是否需要管理批号 1 是 2 否
     */
    private String lotind;

    //推类等级
    private String pushlevel;
    //批准文号
    private String apprdocno;
    //批准文号有效期
    private String apprdocdate;

    /**
        * 是否凭处方销售
     */
    private String rxsalesind;

    /**
     * 处方类别
     */
    private String rxtype;

    /**
     * 商标
     */
    private String trademark;

    @ApiModelProperty("功能主治")
    private String  treatment;

    private String price;

    private String itemId;

    //所属季节
    private String belong_season;

    // 是否包含 季节属性 是/否
    private String season;

    private String goodsline;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(Long marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Date getMarketTime() {
        return marketTime;
    }

    public void setMarketTime(Date marketTime) {
        this.marketTime = marketTime;
    }

    public String getPackingList() {
        return packingList;
    }

    public void setPackingList(String packingList) {
        this.packingList = packingList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getQueryExtend() {
        return queryExtend;
    }

    public void setQueryExtend(String queryExtend) {
        this.queryExtend = queryExtend;
    }

    public String getKeyPv() {
        return keyPv;
    }

    public void setKeyPv(String keyPv) {
        this.keyPv = keyPv;
    }

    public String getComPv() {
        return comPv;
    }

    public void setComPv(String comPv) {
        this.comPv = comPv;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public String getCurName() {
        return curName;
    }

    public void setCurName(String curName) {
        this.curName = curName;
    }

    public String getCurOpCode() {
        return curOpCode;
    }

    public void setCurOpCode(String curOpCode) {
        this.curOpCode = curOpCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getFeatures() {
        return features;
    }

    public void setFeatures(String features) {
        this.features = features;
    }

    public String getAttrExtend() {
        return attrExtend;
    }

    public void setAttrExtend(String attrExtend) {
        this.attrExtend = attrExtend;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Long getSpuExtendId() {
        return spuExtendId;
    }

    public void setSpuExtendId(Long spuExtendId) {
        this.spuExtendId = spuExtendId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getJhiSpecification() {
        return jhiSpecification;
    }

    public void setJhiSpecification(String jhiSpecification) {
        this.jhiSpecification = jhiSpecification;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getFactoryid() {
        if (StringUtils.isEmpty(factoryid) || "null".equals(factoryid)) {
            return null;
        }
        return factoryid;
    }

    public String getShortFactoryid() {
        return shortFactoryid;
    }

    public void setShortFactoryid(String shortFactoryid) {
        this.shortFactoryid = shortFactoryid;
    }

    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    public String getSpecialctrl() {
        return specialctrl;
    }

    public void setSpecialctrl(String specialctrl) {
        this.specialctrl = specialctrl;
    }

    public String getPieceind() {
        return pieceind;
    }

    public void setPieceind(String pieceind) {
        this.pieceind = pieceind;
    }

    public String getPurchchannel() {
        return purchchannel;
    }

    public void setPurchchannel(String purchchannel) {
        this.purchchannel = purchchannel;
    }

    public String getMainten_type() {
        return mainten_type;
    }

    public void setMainten_type(String mainten_type) {
        this.mainten_type = mainten_type;
    }

    public String getStoragecond() {
        return storagecond;
    }

    public void setStoragecond(String storagecond) {
        this.storagecond = storagecond;
    }

    public String getMainten_method() {
        return mainten_method;
    }

    public void setMainten_method(String mainten_method) {
        this.mainten_method = mainten_method;
    }

    public String getMainten_period() {
        return mainten_period;
    }

    public void setMainten_period(String mainten_period) {
        this.mainten_period = mainten_period;
    }

    public String getPrepspec() {
        return prepspec;
    }

    public void setPrepspec(String prepspec) {
        this.prepspec = prepspec;
    }

    public String getColdchainind() {
        return coldchainind;
    }

    public void setColdchainind(String coldchainind) {
        this.coldchainind = coldchainind;
    }

    public String getTempdown() {
        return tempdown;
    }

    public void setTempdown(String tempdown) {
        this.tempdown = tempdown;
    }

    public String getTempup() {
        return tempup;
    }

    public void setTempup(String tempup) {
        this.tempup = tempup;
    }

    public String getTranstimelmt() {
        return transtimelmt;
    }

    public void setTranstimelmt(String transtimelmt) {
        this.transtimelmt = transtimelmt;
    }

    public String getProdarea() {
        return prodarea;
    }

    public void setProdarea(String prodarea) {
        this.prodarea = prodarea;
    }

    public String getDosageformsid() {
        return dosageformsid;
    }

    public void setDosageformsid(String dosageformsid) {
        this.dosageformsid = dosageformsid;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getPieceunit() {
        return pieceunit;
    }

    public void setPieceunit(String pieceunit) {
        this.pieceunit = pieceunit;
    }

    public String getPeriodunit() {
        return periodunit;
    }

    public void setPeriodunit(String periodunit) {
        this.periodunit = periodunit;
    }

    public String getPiecerate() {
        return piecerate;
    }

    public void setPiecerate(String piecerate) {
        this.piecerate = piecerate;
    }

    public String getValidperiod() {
        return validperiod;
    }

    public void setValidperiod(String validperiod) {
        this.validperiod = validperiod;
    }

    public String getLotind() {
        return lotind;
    }

    public void setLotind(String lotind) {
        this.lotind = lotind;
    }

    public String getPushlevel() {
        return pushlevel;
    }

    public void setPushlevel(String pushlevel) {
        this.pushlevel = pushlevel;
    }

    public String getApprdocno() {
        return apprdocno;
    }

    public void setApprdocno(String apprdocno) {
        this.apprdocno = apprdocno;
    }

    public String getApprdocdate() {
        return apprdocdate;
    }

    public void setApprdocdate(String apprdocdate) {
        this.apprdocdate = apprdocdate;
    }

    public String getRxsalesind() {
        return rxsalesind;
    }

    public void setRxsalesind(String rxsalesind) {
        this.rxsalesind = rxsalesind;
    }

    public String getRxtype() {
        return rxtype;
    }

    public void setRxtype(String rxtype) {
        this.rxtype = rxtype;
    }

    public String getTrademark() {
        return trademark;
    }

    public void setTrademark(String trademark) {
        this.trademark = trademark;
    }

    public String getTreatment() {
        return treatment;
    }

    public void setTreatment(String treatment) {
        this.treatment = treatment;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getBelong_season() {
        return belong_season;
    }

    public void setBelong_season(String belong_season) {
        this.belong_season = belong_season;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    @Override
    public String toString() {
        return "SpuNewVo{" +
                "id=" + id +
                ", categoryId=" + categoryId +
                ", categoryName='" + categoryName + '\'' +
                ", name='" + name + '\'' +
                ", marketPrice=" + marketPrice +
                ", marketTime=" + marketTime +
                ", packingList='" + packingList + '\'' +
                ", status=" + status +
                ", picUrl='" + picUrl + '\'' +
                ", queryExtend='" + queryExtend + '\'' +
                ", keyPv='" + keyPv + '\'' +
                ", comPv='" + comPv + '\'' +
                ", source=" + source +
                ", createdBy='" + createdBy + '\'' +
                ", gmtCreate=" + gmtCreate +
                ", gmtUpdate=" + gmtUpdate +
                ", version=" + version +
                ", goodsNo='" + goodsNo + '\'' +
                ", publishStatus=" + publishStatus +
                ", updatedBy='" + updatedBy + '\'' +
                ", brandId=" + brandId +
                ", opCode='" + opCode + '\'' +
                ", curName='" + curName + '\'' +
                ", curOpCode='" + curOpCode + '\'' +
                ", description='" + description + '\'' +
                ", tag='" + tag + '\'' +
                ", extraInfo='" + extraInfo + '\'' +
                ", features='" + features + '\'' +
                ", attrExtend='" + attrExtend + '\'' +
                ", extend='" + extend + '\'' +
                ", spuExtendId=" + spuExtendId +
                ", barCode='" + barCode + '\'' +
                ", jhiSpecification='" + jhiSpecification + '\'' +
                ", businessId=" + businessId +
                ", factoryid='" + factoryid + '\'' +
                ", shortFactoryid='" + shortFactoryid + '\'' +
                ", specialctrl='" + specialctrl + '\'' +
                ", pieceind='" + pieceind + '\'' +
                ", purchchannel='" + purchchannel + '\'' +
                ", mainten_type='" + mainten_type + '\'' +
                ", storagecond='" + storagecond + '\'' +
                ", mainten_method='" + mainten_method + '\'' +
                ", mainten_period='" + mainten_period + '\'' +
                ", prepspec='" + prepspec + '\'' +
                ", coldchainind='" + coldchainind + '\'' +
                ", tempdown='" + tempdown + '\'' +
                ", tempup='" + tempup + '\'' +
                ", transtimelmt='" + transtimelmt + '\'' +
                ", prodarea='" + prodarea + '\'' +
                ", dosageformsid='" + dosageformsid + '\'' +
                ", goodsunit='" + goodsunit + '\'' +
                ", pieceunit='" + pieceunit + '\'' +
                ", periodunit='" + periodunit + '\'' +
                ", piecerate='" + piecerate + '\'' +
                ", validperiod='" + validperiod + '\'' +
                ", lotind='" + lotind + '\'' +
                ", pushlevel='" + pushlevel + '\'' +
                ", apprdocno='" + apprdocno + '\'' +
                ", apprdocdate='" + apprdocdate + '\'' +
                ", rxsalesind='" + rxsalesind + '\'' +
                ", rxtype='" + rxtype + '\'' +
                ", trademark='" + trademark + '\'' +
                ", treatment='" + treatment + '\'' +
                ", price='" + price + '\'' +
                ", itemId='" + itemId + '\'' +
                ", belong_season='" + belong_season + '\'' +
                ", season='" + season + '\'' +
                ", goodsline='" + goodsline + '\'' +
                '}';
    }
}
