package com.cowell.scib.service;

import com.cowell.scib.service.dto.DevelopModuleRecordDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.service.vo.DevelopModuleRecordVO;
import com.cowell.scib.service.vo.amis.PageResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:25
 */
public interface DevelopModuleRecordService {

    /**
     * 通过功能模块编码查询发版记录
     * @param moudleCode
     * @return
     */
    List<DevelopModuleRecordDTO> listDevelopModuleRecordByCode(String moudleCode);

    /**
     * 通过ID查询发版记录详情
     * @param recordId
     * @return
     */
    DevelopModuleRecordDTO detailDevelopModuleRecordById(Integer recordId);

    /**
     * 分页查询模块发版记录
     * @param developListParam
     * @return
     */
    PageResult<DevelopModuleRecordVO> listPageDevelopModuleRecordByCode(DevelopListParam developListParam);

    /**
     * 通过功能模块编码查询发版记录 map
     * @param moudleCodeList
     * @return
     */
    Map<String, List<DevelopModuleRecordDTO>> mapDevelopModuleRecordByCode(List<String> moudleCodeList);

    /**
     * 创建记录
     * @param developRecordAddParam
     */
    void addModuleRecord(DevelopRecordAddParam developRecordAddParam, TokenUserDTO userDTO);

    /**
     * 编辑记录
     * @param developRecordAddParam
     */
    void editModuleRecord(DevelopRecordAddParam developRecordAddParam, TokenUserDTO userDTO);

    /**
     * 功能模块编码
     * @param moduleCode
     * @return
     */
    DevelopModuleRecordVO recentlyRecord(String moduleCode);

    /**
     * 发送触达信息
     * @param developRecordAddParam
     */
    void sendMessage(DevelopRecordAddParam developRecordAddParam);

}
