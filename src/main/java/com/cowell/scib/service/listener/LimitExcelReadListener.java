package com.cowell.scib.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cowell.scib.rest.errors.BusinessErrorException;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 功    能：EasyExcel导入限制大小
 * 作    者：王代军
 * 时    间：2023-03-14
 */
public class LimitExcelReadListener extends AnalysisEventListener<Object> {

    private Integer limitRowSize;
    private AtomicInteger atomicCount= new AtomicInteger(0);
    /**
     * 带参构造函数，直接赋值限制行列
     */
    public LimitExcelReadListener(Integer limitRowSize) {
        this.limitRowSize = Objects.isNull(limitRowSize)?Integer.MAX_VALUE:limitRowSize;
    }
    @Override
    public void invoke(Object object, AnalysisContext contex) {
        int count = atomicCount.incrementAndGet();
        // 判断行数已达到限制行数，抛出ExcelAnalysisException
        if (count > this.limitRowSize) {
            throw new BusinessErrorException("数据超过:"+this.limitRowSize + "行");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }
}
