package com.cowell.scib.service;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.ReachModuleQueryParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.necessaryComtentsV2.NecessaryContentsDTO;
import com.cowell.scib.service.dto.necessaryComtentsV2.NecessaryQueryParam;
import com.cowell.scib.service.dto.necessaryContents.ReachModuleDTO;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * reach模块服务
 */
public interface IReachModuleService {

    /**
     * 触达组管理列表
     * @param userDTO
     * @param param
     * @return
     */
    PageResult<ReachModuleDTO> getReachModuleList(TokenUserDTO userDTO, ReachModuleQueryParam param);

    /**
     * 保存、编辑、删除
     * @param reachModuleDTO
     * @param userDTO
     */
    void reachManage(ReachModuleDTO reachModuleDTO, TokenUserDTO userDTO);

}
