package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.MdmTaskDetailMapper;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.StoreGoodsProcessMapper;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.AuctionSpuBaseInfo;
import com.cowell.scib.service.dto.MdmLicenseBaseDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class StoreContentsNecessaryAssemble extends StoreContentsAssemble{
    private final Logger logger = LoggerFactory.getLogger(StoreContentsNecessaryAssemble.class);
    @Resource
    private ConfigOrgExtendMapper configOrgExtendMapper;
    @Resource
    private ConfigOrgDetailExMapper configOrgDetailExMapper;
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;
    @Resource
    private StoreGoodsProcessMapper storeGoodsProcessMapper;
    @Resource
    private StoreGoodsProcessExtendMapper storeGoodsProcessExtendMapper;
    @Resource
    private MdmTaskMapper mdmTaskMapper;
    @Resource
    private MdmTaskDetailMapper mdmTaskDetailMapper;
    @Resource
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;
    @Resource
    private RuleService ruleService;
    @Resource
    private TagService tagService;
    @Resource
    private StoreService storeService;
    @Resource
    private SearchService searchService;
    @Resource
    private ForestService forestService;
    private final static String DGMS_MDM_TASK_COUNT = "DGMS-MDM-TASK-COUNT-";

    @Override
    protected List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        try {
            if (Boolean.TRUE.equals(storeGoodsContentDTOS.get(0).getDelContent())) {
                logger.info("删除一店一目");
                return storeGoodsContentDTOS;
            }
            Map<Long, List<String>> platGoodslineMap = new HashMap<>();
            Map<Long, List<String>> blackCateIdMap = ruleService.checkExistsUnmanage(storeGoodsContentDTOS.stream().map(StoreGoodsContentDTO::getStoreId).distinct().collect(Collectors.toList()), storeGoodsContentDTOS.stream().map(v -> v.getSubCategoryId().toString()).distinct().collect(Collectors.toList()));
            storeGoodsContentDTOS.stream().map(StoreGoodsContentDTO::getPlatformOrgId).distinct().forEach(v -> {
                // 取平台的经营属性
                RuleParam ruleParam = new RuleParam();
                ruleParam.setScopeCode("BB_2");
                Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
                Map<String, String> goodslineMap = ruleEnum.get("goodsline").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
                // 平台必备店型
                ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(v, ConfigTypeEnum.BB.getType(), null);
                if (Objects.nonNull(configOrg)) {
                    List<String> goodslineList = Optional.ofNullable(platGoodslineMap.get(v)).orElse(new ArrayList<>());
                    goodslineList.addAll(configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Lists.newArrayList("goodsline")).stream().map(o -> goodslineMap.get(o.getPerprotyValue())).distinct().collect(Collectors.toList()));
                    platGoodslineMap.put(v, goodslineList);
                }
            });
            Map<Long, List<StoreGoodsContentDTO>> storeGoodsMap = storeGoodsContentDTOS.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getBusinessId));
            List<StoreGoodsContentDTO> processes = new ArrayList<>();
            storeGoodsMap.forEach((businessId, v) -> {
                List<String> goodsNos = v.stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList());
                //①商品经营属性合法性校验：
                //查商品在门店所属法人公司（businessid），商品表企业级属性：经营属性=（核心必备、一般、新品）-》从参数BB_2/goodsline中取值，说明经营属性合法，可以打入一店一目。
//                Map<String, AuctionSpuBaseInfo> goodsBizScope = forestService.batchFindSpuProperty(goodsNos, businessId).stream().collect(Collectors.toMap(AuctionSpuBaseInfo::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
                spuNewParamVo.setBusinessId(businessId);
                spuNewParamVo.setGoodsNoList(goodsNos);
                Map<String, SpuNewVo> goodsMap = new HashMap<>();
                try {
                    goodsMap.putAll(searchService.getNewSpuMap(spuNewParamVo));
                } catch (Exception e) {
                    logger.error("调用searchapi按连锁获取商品信息失败", e);
                }
                List<String> goodslineList = platGoodslineMap.get(v.get(0).getPlatformOrgId());
                for (StoreGoodsContentDTO goo : v) {
                    StoreGoodsContentDTO goodsInfo = new StoreGoodsContentDTO();
                    BeanUtils.copyProperties(goo, goodsInfo);
                    //②商品是否完成首营：
                    //查商品在门店所属的法人公司（businessid）下是否存在，存着则认为完成首营，可以打入一店一目。（集团、平台添加商品调用标品，剩下4级的调用企业级别商品）
                    SpuNewVo spuNewVo = goodsMap.get(goo.getGoodsNo());
                    if (Objects.isNull(spuNewVo)) {
                        logger.info("门店id:{}商品:{}没有查询到企业商品信息", goo.getStoreId(), goo.getGoodsNo());
                        continue;
                    }
//                    AuctionSpuBaseInfo auctionSpuBaseInfo = goodsBizScope.get(goo.getGoodsNo());
                    logger.info("门店id:{},商品:{},goodsLine:{}, bizScope:{}", goo.getStoreId(), goo.getGoodsNo(), JSON.toJSONString(goodslineList), spuNewVo.getGoodsline());
                    if (StringUtils.isBlank(spuNewVo.getGoodsline()) || CollectionUtils.isEmpty(goodslineList) ||!goodslineList.contains(spuNewVo.getGoodsline())) {
                        logger.info("门店id:{}商品:{}经营属性对不上", goo.getStoreId(), goo.getGoodsNo());
                        continue;
                    }
                    //③门店经营范围与商品经营范围相匹配检查：
                    //商品经营范围获取：forest接口查商品信息，属性：comPv下78的属性（78:100100）
                    //门店经营范围获取：store接口查api/internal/findStoreLicense，返回List<MdmLicenseBaseDTO>，DTO的属性businessScope
                    //校验商品的经营范围在门店的经营范围list中，即满足经营范围，可以打入一店一目。
                    List<String> goodsLicense = getStoreLicense(goo.getBusinessId(), goo.getStoreId());
                    logger.info("门店id:{},商品:{},busiscopetag:{},goodsLicense:{}", goo.getStoreId(), goo.getGoodsNo(), goo.getScopeName(), JSON.toJSONString(goodsLicense));
                    //todo 临时注掉
//                    if (StringUtils.isBlank(goo.getScopeName())){
//                        logger.info("商品:{}的经营范围不在门店:{}的经营范围", goo.getGoodsNo(), goo.getStoreId());
//                        continue;
//                    }
//                    if (!goodsLicense.contains(goo.getScopeName())) {
//                        logger.info("商品:{}的经营范围不在门店:{}的经营范围", goo.getGoodsNo(), goo.getStoreId());
//                        continue;
//                    }
                    // 商品分类黑名单
                    if (blackCateIdMap.containsKey(goo.getStoreId())) {
                        logger.info("商品:{}的大类在门店:{}的类型黑名单范围", goo.getGoodsNo(), goo.getStoreId());
                        continue;
                    }
                    goodsInfo.setManageStatus(ManageStatusEnum.MANAGE_NECESARY.getCode());
                    goodsInfo.setSuggestManageStatus(SuggestManageStatusEnum.MANAGE_NECESARY.getCode());
                    processes.add(goodsInfo);
                }
            });
            return processes;
        } catch (Exception e) {
            logger.error("必备目录组装一店一目数据异常:", e);
            dealCache(storeGoodsContentDTOS);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    protected void dealAndPush(List<StoreGoodsContentDTO> processes) {
        List<Long> processIds = new ArrayList<>();
        List<Long> storeGoodsIds = new ArrayList<>();
        if (!Boolean.TRUE.equals(processes.get(0).getDelContent())) {
            int size = processes.size();
            int loopCount = (size / Constants.INSERT_MAX_VALUE) + 1;
            logger.info("size:{}, loopCount:{}", size, loopCount);
            for (int i = 0; i < loopCount; i++) {
                processIds.addAll(getStoreGoodsProcessIds(Constants.INSERT_MAX_VALUE));
                storeGoodsIds.addAll(getStoreGoodsIds(Constants.INSERT_MAX_VALUE));
            }
        }
        Long mdmTaskId = processes.get(0).getMdmTaskId();
        MdmTask task = mdmTaskMapper.selectByPrimaryKey(mdmTaskId);
        logger.info("dealAndPush.task:{}", JSON.toJSONString(task));
        Map<Long, List<StoreGoodsContentDTO>> processMap = processes.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId));
        List<MdmTaskDetail> taskDetails = new ArrayList<>();
        for (Map.Entry<Long, List<StoreGoodsContentDTO>> entry : processMap.entrySet()) {
            if (Boolean.TRUE.equals(processes.get(0).getDelContent())) {
                logger.info("删除门店:{}一店一目", entry.getKey());
                StoreGoodsProcessExample processExample = new StoreGoodsProcessExample();
                processExample.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).collect(Collectors.toList())).andNecessaryTagEqualTo(entry.getValue().get(0).getNecessaryTag());
                storeGoodsProcessMapper.deleteByExample(processExample);
                StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                example.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).collect(Collectors.toList())).andNecessaryTagEqualTo(entry.getValue().get(0).getNecessaryTag());
                List<StoreGoodsContents> olds = storeGoodsContentsMapper.selectByExample(example);
                Map<String, StoreGoodsProcess> goodsTagMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(olds)) {
                    Map<Integer, List<StoreGoodsContents>> oldMap = olds.stream().collect(Collectors.groupingBy(StoreGoodsContents::getNecessaryTag));
                    oldMap.forEach((k, v) -> {
                        goodsTagMap.putAll(storeGoodsProcessExtendMapper.selectGoodsNecessaryTag(v.get(0).getStoreId(), v.stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList())).stream().collect(Collectors.toMap(StoreGoodsProcess::getGoodsNo, Function.identity(), (k1,k2) -> k1)));
                    });
                }
                List<StoreGoodsContents> updateList = olds.stream().map(goo -> {
                    StoreGoodsProcess necessaryTag = goodsTagMap.get(goo.getGoodsNo());
                    if (null == necessaryTag) {
                        goo.setNecessaryTag(0);
                        goo.setNecessaryTagName("");
                        if (ManageStatusEnum.MANAGE_NECESARY.getCode().equals(goo.getManageStatus())) {
                            goo.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                            goo.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                        }
                        ManageStatusEnum manageStatusEnum = ManageStatusEnum.getEnumByMessage(goodsline);
                        if (null != manageStatusEnum) {
                            goo.setManageStatus(manageStatusEnum.getCode());
                            goo.setSuggestManageStatus(SuggestManageStatusEnum.NONE.getCode());
                        }
                    } else {
                        goo.setNecessaryTag(necessaryTag.getNecessaryTag());
                        goo.setNecessaryTagName(necessaryTag.getNecessaryTagName());
                        goo.setMinDisplayQuantity(BigDecimal.ONE);
                        goo.setForbidApply("否");
                    }
                    return goo;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(updateList)) {
                    logger.info("门店:{} 商品:{} 没有需要修改的数据", entry.getKey(), JSON.toJSONString(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).collect(Collectors.toList())));
                    continue;
                }
                storeGoodsContentsExtendMapper.batchUpdate(updateList, entry.getKey());
                taskDetails.addAll(updateList.stream().map(v -> genTaskDetail(v, task)).collect(Collectors.toList()));
            } else {
                logger.info("添加");
                List<StoreGoodsContents> insert = new ArrayList<>();
                List<StoreGoodsProcess> processInsert = new ArrayList<>();
                // 先查过程表
                StoreGoodsProcessExample processExample = new StoreGoodsProcessExample();
                processExample.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).collect(Collectors.toList()));
                Map<String, List<StoreGoodsProcess>> map = storeGoodsProcessMapper.selectByExample(processExample).stream().collect(Collectors.groupingBy(v -> v.getGoodsNo()));
                entry.getValue().forEach(goo -> {
                    Optional<OrgInfoBaseCache> storeCache = CacheVar.getStoreByStoreId(goo.getStoreId());
                    List<StoreGoodsProcess> processList = map.get(goo.getGoodsNo());
                    if (CollectionUtils.isEmpty(processList)) {
                        StoreGoodsContents contents = new StoreGoodsContents();
                        BeanUtils.copyProperties(goo, contents);
                        contents.setStoreOrgId(storeCache.get().getId());
                        contents.setStoreCode(storeCache.get().getSapCode());
                        contents.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                        contents.setMinDisplayQuantity(BigDecimal.ONE);
                        contents.setForbidApply("");
                        insert.add(contents);
                        StoreGoodsProcess process = new StoreGoodsProcess();
                        BeanUtils.copyProperties(contents, process);
                        processInsert.add(process);
                    } else {
                        processList = processList.stream().sorted(Comparator.comparing(StoreGoodsProcess::getNecessaryTag)).collect(Collectors.toList());
                        Optional<StoreGoodsProcess> any = processList.stream().filter(v -> v.getNecessaryTag().equals(goo.getNecessaryTag())).findAny();
                        if (!any.isPresent()) {
                            // 存在不更新一店一目,不存在更新
                            if (goo.getNecessaryTag() < processList.get(0).getNecessaryTag()) {
                                // 只更新小的
                                StoreGoodsContents contents = new StoreGoodsContents();
                                BeanUtils.copyProperties(goo, contents);
                                contents.setStoreOrgId(storeCache.get().getId());
                                contents.setStoreCode(storeCache.get().getSapCode());
                                contents.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                                contents.setMinDisplayQuantity(BigDecimal.ONE);
                                contents.setForbidApply("否");
                                insert.add(contents);
                            }
                            StoreGoodsProcess process = new StoreGoodsProcess();
                            BeanUtils.copyProperties(goo, process);
                            process.setStoreOrgId(storeCache.get().getId());
                            process.setStoreCode(storeCache.get().getSapCode());
                            process.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                            process.setMinDisplayQuantity(BigDecimal.ONE);
                            processInsert.add(process);
                        }
                    }
                });
                if (CollectionUtils.isNotEmpty(processInsert)) {
                    processInsert.forEach(v -> v.setId(processIds.remove(0)));
                    StoreGoodsProcessExample processDelExample = new StoreGoodsProcessExample();
                    processDelExample.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(processInsert.stream().map(StoreGoodsProcess::getGoodsNo).collect(Collectors.toList())).andNecessaryTagEqualTo(processInsert.get(0).getNecessaryTag());
                    storeGoodsProcessMapper.deleteByExample(processDelExample);
                    storeGoodsProcessExtendMapper.batchInsert(processInsert);
                }
                if (CollectionUtils.isNotEmpty(insert)) {
                    insert.forEach(v -> v.setId(storeGoodsIds.remove(0)));
                    StoreGoodsContentsExample contentsDelExample = new StoreGoodsContentsExample();
                    contentsDelExample.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(insert.stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList()));
                    storeGoodsContentsMapper.deleteByExample(contentsDelExample);
                    storeGoodsContentsExtendMapper.batchInsert(insert);
                    taskDetails.addAll(insert.stream().map(v -> genTaskDetail(v, task)).collect(Collectors.toList()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(taskDetails)) {
            RAtomicLong atomicLong = redissonClient.getAtomicLong(DGMS_MDM_TASK_COUNT + task.getId());
            long count = atomicLong.addAndGet(taskDetails.size());
            task.setDetailCount((int)count);
            task.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
            mdmTaskMapper.updateByPrimaryKeySelective(task);
            List<Long> mdmTaskDetailIds = new ArrayList<>(taskDetails.size());
            int size = taskDetails.size();
            int loopCount = (size / Constants.INSERT_MAX_VALUE) + 1;
            for (int i = 0; i < loopCount; i++) {
                mdmTaskDetailIds.addAll(getMdmTaskDetailIds(Constants.INSERT_MAX_VALUE));
            }
            taskDetails.forEach(v -> v.setId(mdmTaskDetailIds.remove(0)));
            pushMdmTask(taskDetails);
        }
    }

    private MdmTaskDetail genTaskDetail(StoreGoodsContents contents, MdmTask task){
        MdmTaskDetail taskDetail = new MdmTaskDetail();
        BeanUtils.copyProperties(contents, taskDetail);
        taskDetail.setTaskId(task.getId());
        taskDetail.setStoreName(CacheVar.getStoreByStoreId(contents.getStoreId()).orElse(new OrgInfoBaseCache()).getShortName());
        taskDetail.setMinDisplayQuantity(contents.getNecessaryTag().equals(0) ? BigDecimal.ZERO : BigDecimal.ONE);
        taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        return taskDetail;
    }

    private List<String> getStoreLicense(Long businessId, Long storeId) {
        try {
            RBucket<List<String>> rBucket = redissonClient.getBucket(BUSINESS_SCOPE_CACHE + storeId.toString());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(rBucket.get())) {
                List<MdmLicenseBaseDTO> storeLicense = storeService.findStoreLicense(businessId, storeId);
                rBucket.set(storeLicense.stream().map(MdmLicenseBaseDTO::getScopeName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()), 30L, TimeUnit.MINUTES);
            }
            return rBucket.get();
        } catch (Exception e) {
            try {
                logger.warn("查询storeService.findStoreLicense报错 businessId:{}, storeId:{}", businessId, storeId);
                return storeService.findStoreLicense(businessId, storeId).stream().map(MdmLicenseBaseDTO::getBusinessScope).distinct().collect(Collectors.toList());
            } catch (Exception e1) {
                logger.warn("查询storeService.findStoreLicense报错返回空 businessId:{}, storeId:{}", businessId, storeId);
                return new ArrayList<>();
            }
        }
    }

}
