package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.StringJoiner;

/**
 * 通用商品DTO
 * <AUTHOR>
public class ImportStoreDTO implements Serializable {

    /**
     * 行号
     */
    @ApiModelProperty(value = "行号")
    private Integer lineNum;

    /**
     * 来源门店编码
     */
    @ApiModelProperty(value = "来源门店编码")
    private String sourceStoreCode;

    /**
     * 目标门店编码
     */
    @ApiModelProperty(value = "目标门店编码")
    private String targetStoreCode;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    public Integer getLineNum() {
        return lineNum;
    }

    public void setLineNum(Integer lineNum) {
        this.lineNum = lineNum;
    }

    public String getSourceStoreCode() {
        return sourceStoreCode;
    }

    public void setSourceStoreCode(String sourceStoreCode) {
        this.sourceStoreCode = sourceStoreCode;
    }

    public String getTargetStoreCode() {
        return targetStoreCode;
    }

    public void setTargetStoreCode(String targetStoreCode) {
        this.targetStoreCode = targetStoreCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ImportStoreDTO.class.getSimpleName() + "[", "]")
                .add("lineNum=" + lineNum)
                .add("sourceStoreCode='" + sourceStoreCode + "'")
                .add("targetStoreCode='" + targetStoreCode + "'")
                .add("errorMsg='" + errorMsg + "'")
                .toString();
    }
}
