package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ManageDropOptionDTO extends AmisPageParam implements Serializable {

    @ApiModelProperty(value = "查询类型(1:版本列表 2:城市列表 3:分类列表)")
    private Integer queryType;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 大类id
     */
    @ApiModelProperty(value = "大类id")
    private String category;

    /**
     * 大类名称
     */
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 中类id
     */
    @ApiModelProperty(value = "中类id")
    private String middleCategory;

    /**
     * 中类名称
     */
    @ApiModelProperty(value = "中类名称")
    private String middleCategoryName;

    /**
     * 小类编码
     */
    @ApiModelProperty(value = "小类编码")
    private String smallCategory;

    /**
     * 小类名称
     */
    @ApiModelProperty(value = "小类名称")
    private String smallCategoryName;

    /**
     * 子类编码
     */
    @ApiModelProperty(value = "子类编码")
    private String subCategory;

    /**
     * 子类名称
     */
    @ApiModelProperty(value = "子类名称")
    private String subCategoryName;

}
