package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NecessaryContentsImportData {

    @ExcelProperty(value = "必备标签",index = 0)
    private String necessaryTagName;

    @ExcelProperty(value = "集团ID",index = 1)
    private Long groupOrgId;

    @ExcelProperty(value = "集团",index = 2)
    private String groupName;

    @ExcelProperty(value = "区域平台ID",index = 3)
    private Long platformOrgId;

    @ExcelProperty(value = "区域平台",index = 4)
    private String platformName;

    @ExcelProperty(value = "省份",index = 5)
    private String province;

    @ExcelProperty(value = "项目公司ID",index = 6)
    private Long companyOrgId;

    @ExcelProperty(value = "项目公司",index = 7)
    private String companyName;

    @ExcelProperty(value = "城市",index = 8)
    private String city;

    @ExcelProperty(value = "区县",index = 9)
    private String area;

    @ExcelProperty(value = "门店编码",index = 10)
    private String storeCode;

    @ExcelProperty(value = "生效店型",index = 11)
    private String storeType;

    @ExcelProperty(value = "商品编码",index = 12)
    private String goodsNo;

    @ExcelProperty(value = "入选原因",index = 13)
    private String chooseReason;
    @ExcelProperty(value = "错误原因",index = 14)
    private String errorReason;

}
