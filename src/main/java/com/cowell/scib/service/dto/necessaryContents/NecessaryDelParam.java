package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class NecessaryDelParam {

    @ApiModelProperty(value = "待删除的ids")
    private List<Long> necessaryIds;

    @ApiModelProperty(value = "平台orgId")
    private Long platformOrgId;

    @ApiModelProperty(value = "必备标识(1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)")
    private Byte necessaryTag;
}
