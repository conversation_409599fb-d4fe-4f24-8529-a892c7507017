package com.cowell.scib.service.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 查询 scib 机构缓存请求参数
 * <AUTHOR>
public class CommonOrgCacheDTO implements Serializable {

    private Long id;
    private String name;
    private String shortName;
    private Integer type;
    private Long outId;
    private String sapCode;
    private String orgPath;
    private Long businessOrgId;
    private Long businessId;
    private String businessSapCode;
    private String businessShortName;
    private Long platformOrgId;
    private String platformShortName;
    private List<Long> orgIdList;
    private List<Long> storeIdList;
    private List<Long> businessIdList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getOutId() {
        return outId;
    }

    public void setOutId(Long outId) {
        this.outId = outId;
    }

    public String getSapCode() {
        return sapCode;
    }

    public void setSapCode(String sapCode) {
        this.sapCode = sapCode;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public Long getBusinessOrgId() {
        return businessOrgId;
    }

    public void setBusinessOrgId(Long businessOrgId) {
        this.businessOrgId = businessOrgId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessSapCode() {
        return businessSapCode;
    }

    public void setBusinessSapCode(String businessSapCode) {
        this.businessSapCode = businessSapCode;
    }

    public String getBusinessShortName() {
        return businessShortName;
    }

    public void setBusinessShortName(String businessShortName) {
        this.businessShortName = businessShortName;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformShortName() {
        return platformShortName;
    }

    public void setPlatformShortName(String platformShortName) {
        this.platformShortName = platformShortName;
    }

    public List<Long> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<Long> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public List<Long> getStoreIdList() {
        return storeIdList;
    }

    public void setStoreIdList(List<Long> storeIdList) {
        this.storeIdList = storeIdList;
    }

    public List<Long> getBusinessIdList() {
        return businessIdList;
    }

    public void setBusinessIdList(List<Long> businessIdList) {
        this.businessIdList = businessIdList;
    }

    @Override
    public String toString() {
        return "CommonOrgCacheDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", shortName='" + shortName + '\'' +
                ", type=" + type +
                ", outId=" + outId +
                ", sapCode='" + sapCode + '\'' +
                ", orgPath='" + orgPath + '\'' +
                ", businessOrgId=" + businessOrgId +
                ", businessId=" + businessId +
                ", businessSapCode='" + businessSapCode + '\'' +
                ", businessShortName='" + businessShortName + '\'' +
                ", platformOrgId=" + platformOrgId +
                ", platformShortName='" + platformShortName + '\'' +
                ", orgIdList=" + orgIdList +
                ", storeIdList=" + storeIdList +
                ", businessIdList=" + businessIdList +
                '}';
    }
}
