package com.cowell.scib.service.dto.newStore;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR> 新店推荐目录
 */
@Data
public class TrackRetultNewStoreAllDetailDTO implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 任务ID
     */
    @ExcelIgnore
    private Long taskId;

    @ExcelProperty(value = {"序号", "序号"}, index = 1)
    private Integer index;

    /**
     * 门店MDM编码
     */
    @ExcelIgnore
    private String orgNo;

    /**
     * 门店名称
     */
    @ExcelIgnore
    private String storeName;

    /**
     * 商品编码
     */
    @ExcelProperty(value = {"商品编码", "商品编码"}, index = 2)
    private String goodsId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = {"商品名称", "商品名称"}, index = 3)
    private String goodsname;

    /**
     * 规格
     */
    @ExcelProperty(value = {"规格", "规格"}, index = 4)
    private String goodsspec;

    /**
     * 厂家名称
     */
    @ExcelProperty(value = {"厂家", "厂家"}, index = 5)
    private String manufacturer;

    /**
     * 剂型
     */
    @ExcelProperty(value = {"剂型", "剂型"}, index = 6)
    private String jxCate1Name;

    /**
     * 单位
     */
    @ExcelProperty(value = {"单位", "单位"}, index = 7)
    private String goodsunit;

    /**
     * 推荐来源
     */
    @ExcelProperty(value = {"推荐来源", "推荐来源"}, index = 8)
    private String level;

    /**
     * 建议首次备货数量
     */
    @ExcelProperty(value = {"建议首次备货数量", "建议首次备货数量"}, index = 9)
    private String suggestPhQty;

    /**
     * 备货库存成本金额
     */
    @ExcelProperty(value = {"备货库存成本金额", "备货库存成本金额"}, index = 10)
    private String phCost;

    /**
     * 经营属性
     */
    @ExcelProperty(value = {"经营属性", "经营属性"}, index = 11)
    private String taotaiType;

    /**
     * 销售属性
     */
    @ExcelProperty(value = {"销售属性", "销售属性"}, index = 12)
    private String stjb;

    /**
     * 采购属性
     */
    @ExcelProperty(value = {"采购属性", "采购属性"}, index = 13)
    private String grossprofit;

    /**
     * 子类id
     */
    @ExcelIgnore
    private String subCategoryId;

    /**
     * 大类
     */
    @ExcelProperty(value = {"大类", "大类"}, index = 14)
    private String classoneName;

    /**
     * 中类
     */
    @ExcelProperty(value = {"中类", "中类"}, index = 15)
    private String classtwoName;

    /**
     * 小类
     */
    @ExcelProperty(value = {"小类", "小类"}, index = 16)
    private String classthreeName;

    /**
     * 子类
     */
    @ExcelProperty(value = {"子类", "子类"}, index = 17)
    private String classfourName;

    /**
     * 成分
     */
    @ExcelProperty(value = {"成分", "成分"}, index = 18)
    private String component;

    /**
     * RX/OTC
     */
    @ExcelProperty(value = {"RX/OTC", "RX/OTC"}, index = 19)
    private String rxOtc;

    /**
     * DTP商品(D)
     */
    @ExcelProperty(value = {"DTP商品(D)", "DTP商品(D)"}, index = 20)
    private String dtpgood;

    /**
     * 疾病种
     */
    @ExcelProperty(value = {"疾病种", "疾病种"}, index = 21)
    private String flagDisease;

    /**
     * 归属部门
     */
    @ExcelProperty(value = {"归属部门", "归属部门"}, index = 22)
    private String department;

    /**
     * 禁止采购
     */
    @ExcelProperty(value = {"禁止配送", "禁止配送"}, index = 23)
    private String distribind;

    /**
     * 是否贵重
     */
    @ExcelProperty(value = {"是否贵重", "是否贵重"}, index = 24)
    private String precious;

    /**
     * 参考零售价
     */
    @ExcelProperty(value = {"参考零售价", "参考零售价"}, index = 25)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String refretailprice;

    /**
     * 集中度-本企业本城市本店型
     */
    @ExcelProperty(value = {"本企业本城市本店型", "集中度"}, index = 26)
    private String inStockRateDx;

    /**
     * 动销率-本企业本城市本店型
     */
    @ExcelProperty(value = {"本企业本城市本店型", "动销率"}, index = 27)
    private String inSalesRateDx;

    /**
     * 近90天销售数量-本企业本城市本店型
     */
    @ExcelProperty(value = {"本企业本城市本店型", "近90天销售数量"}, index = 28)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String numCum90Dx;

    /**
     * 近90天客流量-本企业本城市本店型
     */
    @ExcelProperty(value = {"本企业本城市本店型", "近90天客流量"}, index = 29)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String billCntsCum90Dx;

    /**
     * 近90天销售金额-本企业本城市本店型
     */
    @ExcelProperty(value = {"本企业本城市本店型", "近90天销售金额"}, index = 30)
    private String amtCum90Dx;

    /**
     * 毛利率-本企业本城市本店型
     */
    @ExcelProperty(value = {"本企业本城市本店型", "毛利率"}, index = 31)
    private String profitRate90Dx;

    /**
     * 集中度-本企业本城市
     */
    @ExcelProperty(value = {"本企业本城市", "集中度"}, index = 32)
    private String inStockRateCity;

    /**
     * 动销率-本企业本城市
     */
    @ExcelProperty(value = {"本企业本城市", "动销率"}, index = 33)
    private String inSalesRateCity;

    /**
     * 近90天销售数量-本企业本城市
     */
    @ExcelProperty(value = {"本企业本城市", "近90天销售数量"}, index = 34)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String numCum90City;

    /**
     * 近90天客流量-本企业本城市
     */
    @ExcelProperty(value = {"本企业本城市", "近90天客流量"}, index = 35)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String billCntsCum90City;

    /**
     * 近90天销售金额-本企业本城市
     */
    @ExcelProperty(value = {"本企业本城市", "近90天销售金额"}, index = 36)
    private String amtCum90City;

    /**
     * 毛利率-本企业本城市
     */
    @ExcelProperty(value = {"本企业本城市", "毛利率"}, index = 37)
    private String profitRate90City;

    /**
     * 集中度-本企业
     */
    @ExcelProperty(value = {"本企业", "集中度"}, index = 38)
    private String inStockRateQy;

    /**
     * 动销率-本企业
     */
    @ExcelProperty(value = {"本企业", "动销率"}, index = 39)
    private String inSalesRateQy;

    /**
     * 近90天销售数量-本企业
     */
    @ExcelProperty(value = {"本企业", "近90天销售数量"}, index = 40)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String numCum90Qy;

    /**
     * 近90天客流量-本企业
     */
    @ExcelProperty(value = {"本企业", "近90天客流量"}, index = 41)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String billCntsCum90Qy;

    /**
     * 近90天销售金额-本企业
     */
    @ExcelProperty(value = {"本企业", "近90天销售金额"}, index = 42)
    private String amtCum90Qy;

    /**
     * 毛利率-本企业
     */
    @ExcelProperty(value = {"本企业", "毛利率"}, index = 43)
    private String profitRate90Qy;

    /**
     * 集中度-相似门店
     */
    @ExcelProperty(value = {"相似门店", "集中度"}, index = 44)
    private String inStockRateMd;

    /**
     * 动销率-相似门店
     */
    @ExcelProperty(value = {"相似门店", "动销率"}, index = 45)
    private String inSalesRateMd;

    /**
     * 近90天销售数量-相似门店
     */
    @ExcelProperty(value = {"相似门店", "近90天销售数量"}, index = 46)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String numCum90Md;

    /**
     * 近90天客流量-相似门店
     */
    @ExcelProperty(value = {"相似门店", "近90天客流量"}, index = 47)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String billCntsCum90Md;

    /**
     * 近90天销售金额-相似门店
     */
    @ExcelProperty(value = {"相似门店", "近90天销售金额"}, index = 48)
    private String amtCum90Md;

    /**
     * 毛利率-相似门店
     */
    @ExcelProperty(value = {"相似门店", "毛利率"}, index = 49)
    private String profitRate90Md;

    /**
     * 是否经营 0 否 1是
     */
    @ExcelIgnore
    private Byte jyAble;

    /**
     * 是否经营 0 否 1是
     */
    @ExcelProperty(value = {"是否经营", "是否经营"}, index = 0)
    private String jyAbleDesc;

    /**
     * 扩展字段
     */
    @ExcelIgnore
    private String extend;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date gmtCreate;

    /**
     * 状态(-1删除，0正常)
     */
    @ExcelIgnore
    private Byte status;


}
