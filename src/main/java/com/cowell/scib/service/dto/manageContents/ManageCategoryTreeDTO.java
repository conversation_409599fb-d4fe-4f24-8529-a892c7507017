package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static com.cowell.scib.enums.ScibCommonEnums.ManageConfirmStatusEnum.*;

/**
 * <AUTHOR>
 * 经营目录commonDto
 */
@Data
public class ManageCategoryTreeDTO implements AmisDataInInterface, Serializable {

    /**
     * 大类id
     */
    @ApiModelProperty(value = "大类id")
    private String category;

    /**
     * 大类名称
     */
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private Integer level;

    /**
     * 是否超上限
     */
    @ApiModelProperty(value = "是否超上限")
    private Boolean upLimit;

    /**
     * 分类处理进度
     */
    @ApiModelProperty(value = "分类处理进度 0:待确认 -1:已提交待确认 1:已确认")
    private Integer processStatus;


    @ApiModelProperty(value = "图标")
    private String icon;

    /**
     * 子节点
     */
   private List<ManageCategoryTreeDTO> children;

    // 添加无参构造函数
    public ManageCategoryTreeDTO() {
    }

    public ManageCategoryTreeDTO(String category, String categoryName) {
        this.category = category;
        this.categoryName = categoryName;
        this.level = 1;
        this.upLimit = false;
        this.children = new ArrayList<>();
        this.processStatus=0;
        this.icon = "fa fa-exclamation-circle-x";
    }
    public ManageCategoryTreeDTO(String category, String categoryName, Integer level, Boolean upLimit, Integer processStatus) {
        this.category = category;
        this.categoryName = categoryName;
        this.level = level;
        this.upLimit = upLimit;
        this.children = new ArrayList<>();
        this.processStatus= processStatus;
        if(Boolean.TRUE.equals(upLimit)){
            if (level !=1 && JYML_PROCESS_SUBMIT_REVIEW.getCode().equals(processStatus)){
                this.icon = "fa fa-exclamation-triangle";
            }else if (level !=1 && JYML_PROCESS_NEED_CONFIRM.getCode().equals(processStatus)){
                this.icon = "fa fa-exclamation-circle";
            }else if (level !=1 && JYML_PROCESS_REVIEW_CONFIRM.getCode().equals(processStatus)){
                this.icon = "fa fa-check-circle-o";
            }else {
                this.icon = "fa fa-exclamation-circle-x";
            }
        }else {
            //不超上限不给进度图标
            this.icon = "fa fa-exclamation-circle-x";
        }
    }
    public void addChild(ManageCategoryTreeDTO child) {
        this.children.add(child);
    }

}
