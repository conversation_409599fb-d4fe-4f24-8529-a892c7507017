package com.cowell.scib.service.dto;

public class MdmStore {
    /**
     * ⻔店编码
     */
    private String storeno;
    /**
     * 参考⻔店编号
     */
    private String refstoreno;
    /**
     * ⻔店属性
     */
    private String storeattr;
    /**
     * ⻔店内码
     */
    private String storeinnerno;
    /**
     * ⻔店简称
     */
    private String storeshotname;
    /**
     * 公司名称
     */
    private String mdm_company_code;
    /**
     * 公司名称
     */
    private String storename;
    /**
     * 原系统⻔店编码
     */
    private String olderpno;
    /**
     * ⻔店状态
     */
    private String storestatus;
    /**
     * 经营状态
     */
    private String managstate;

    /**
     *  -1 删除， 0 新增， 1 修改， 2不变
     */
    private Integer mdm_data_operation_status;

    public String getStoreno() {
        return storeno;
    }

    public void setStoreno(String storeno) {
        this.storeno = storeno;
    }

    public String getRefstoreno() {
        return refstoreno;
    }

    public void setRefstoreno(String refstoreno) {
        this.refstoreno = refstoreno;
    }

    public String getStoreattr() {
        return storeattr;
    }

    public void setStoreattr(String storeattr) {
        this.storeattr = storeattr;
    }

    public String getStoreinnerno() {
        return storeinnerno;
    }

    public void setStoreinnerno(String storeinnerno) {
        this.storeinnerno = storeinnerno;
    }

    public String getStoreshotname() {
        return storeshotname;
    }

    public void setStoreshotname(String storeshotname) {
        this.storeshotname = storeshotname;
    }

    public String getMdm_company_code() {
        return mdm_company_code;
    }

    public void setMdm_company_code(String mdm_company_code) {
        this.mdm_company_code = mdm_company_code;
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }

    public String getOlderpno() {
        return olderpno;
    }

    public void setOlderpno(String olderpno) {
        this.olderpno = olderpno;
    }

    public String getStorestatus() {
        return storestatus;
    }

    public void setStorestatus(String storestatus) {
        this.storestatus = storestatus;
    }

    public String getManagstate() {
        return managstate;
    }

    public void setManagstate(String managstate) {
        this.managstate = managstate;
    }

    public Integer getMdm_data_operation_status() {
        return mdm_data_operation_status;
    }

    public void setMdm_data_operation_status(Integer mdm_data_operation_status) {
        this.mdm_data_operation_status = mdm_data_operation_status;
    }

    @Override
    public String toString() {
        return "MdmStore{" +
                "storeno=" + storeno +
                ", refstoreno=" + refstoreno +
                ", storeattr='" + storeattr + '\'' +
                ", storeinnerno='" + storeinnerno + '\'' +
                ", storeshotname='" + storeshotname + '\'' +
                ", mdm_company_code='" + mdm_company_code + '\'' +
                ", storename='" + storename + '\'' +
                ", olderpno='" + olderpno + '\'' +
                ", storestatus='" + storestatus + '\'' +
                ", managstate='" + managstate + '\'' +
                '}';
    }
}
