package com.cowell.scib.service.dto.necessaryContents;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class NecessaryTaskMbDTO {
    private String storeCode;
    private String goodsNo;
    private String necessaryTag;
    private BigDecimal minDisplayQuantity;
    private String updateTime;
    private Long taskId;
    private Long taskDetailId;
    private String forbidApply;
    private String forbidDistr;
    private Boolean tagModifyFlag = true;
}
