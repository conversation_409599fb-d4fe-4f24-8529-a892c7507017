package com.cowell.scib.service.dto.necessaryContents;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.NecessaryStoreTypeGoodsMapper;
import com.cowell.scib.mapperDgms.extend.NecessaryStoreTypeGoodsExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NecessaryStoreTypeAssemble extends NecessaryAssemble {

    private final Logger logger = LoggerFactory.getLogger(NecessaryStoreTypeAssemble.class);
    @Autowired
    private NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;
    @Autowired
    private NecessaryStoreTypeGoodsExtendMapper necessaryStoreTypeGoodsExtendMapper;

    @Override
    public String deleteNecessary() throws Exception{
        if (!necessaryContentsService.checkModifyAble(delParam.getPlatformOrgId())){
            throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
        }
//        1、店型必备表删除该企业*城市*店型*SKU行。
//        2、用企业*城市*店型取门店列表，清空一店一目表这些门店*SKU的配置类型、最小陈列量
        NecessaryStoreTypeGoodsExample example = new NecessaryStoreTypeGoodsExample();
        example.createCriteria().andPlatformOrgIdEqualTo(delParam.getPlatformOrgId()).andIdIn(delParam.getNecessaryIds());
        List<NecessaryStoreTypeGoods> necessaryGoodsList = necessaryStoreTypeGoodsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryGoodsList)) {
            throw new AmisBadRequestException("所选单据已全部被删除");
        }
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        List<Long> companyOrgIds = necessaryGoodsList.stream().map(NecessaryStoreTypeGoods::getCompanyOrgId).distinct().collect(Collectors.toList());
        // set 平台信息
        NecessaryAddParam param = new NecessaryAddParam();
        param.setNecessaryTag(delParam.getNecessaryTag());
        setProp(param, userDTO, CacheVar.getPlatformByOrgId(delParam.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(companyOrgIds, userDTO.getUserId());
        // 按照 企业, 城市, 店型分组  并过滤没有权限的企业
        Map<String, List<NecessaryStoreTypeGoods>> necessaryGoodsMap = necessaryGoodsList.stream().filter(v -> orgDataScopeList.contains(v.getCompanyOrgId())).collect(Collectors.groupingBy(v -> v.getCompanyOrgId() + "-" + v.getCity() + "-" + v.getStoreType()));
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        companyOrgIds.forEach(c -> {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(c).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        necessaryGoodsMap.forEach((key,v) -> {
            String[] split = StringUtils.split(key, "-");
            List<OrgInfoBaseCache> childOrgsDTOS = CacheVar.getStoreListByBusinessOrgId(Long.valueOf(split[0]));
            if (CollectionUtils.isNotEmpty(childOrgsDTOS)) {
                List<Long> storeIds = childOrgsDTOS.stream().filter(store -> null != store.getOutId()).map(store -> CacheVar.getStoreExtInfoByStoreId(store.getOutId()).orElse(null)).filter(store ->
                    null != store && split[1].equals(store.getCity()) && (split[2].equals(store.getZsStoreTypeCode()) || split[2].equals(store.getStoreTypeCode()) || split[2].equals(store.getPfStoreTypeCode()))).map(MdmStoreExDTO::getStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(storeIds)) {
                    v.forEach(goo -> {
                        NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
                        BeanUtils.copyProperties(goo, goodsDTO);
                        List<Long> storeIdList = Optional.ofNullable(delGoodsStoreMap.get(goodsDTO)).orElse(new ArrayList<>());
                        storeIdList.addAll(storeIds);
                        delGoodsStoreMap.put(goodsDTO, storeIdList);
                    });
                }
            }
        });
        necessaryStoreTypeGoodsMapper.deleteByExample(example);
        if (MapUtils.isEmpty(delGoodsStoreMap)) {
            return "无符合条件的门店，仅删除必备目录，不创建MDM任务。";
        }
        logger.info("delGoodsStoreMap:{}", JSON.toJSONString(delGoodsStoreMap));
        return delStroeGoods(delGoodsStoreMap);
    }

    @Override
    public NecessaryAssemble checkFullScope() {
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(Lists.newArrayList(param.getCompanyOrgId()), userDTO.getUserId());
        if (CollectionUtils.isEmpty(orgDataScopeList)) {
            throw new AmisBadRequestException("没有当前公司权限");
        }
        return this;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NecessaryAssemble assemble() throws Exception{
        OrgInfoBaseCache company = CacheVar.getBusinessByOrgId(param.getCompanyOrgId()).orElseThrow(() -> new AmisBadRequestException("没有查询到企业信息,请联系管理员"));
        setPropCompany(company);
        List<MdmStoreExDTO> mdmStroeExDTOS = getMdmStroeExDTOS(true);
        List<NecessaryCommonGoodsDTO> list = assembleCommon();
        List<NecessaryStoreTypeGoods> storeTypeGoodsList = new ArrayList<>();
        List<NecessaryCommonGoodsDTO> addList = new ArrayList<>();
        // 是否是组货店型
        List<Long> categoryIds = getCategoryByStoreTypes(mdmStroeExDTOS.get(0));
        // 错误的店型商品
        List<String> errorStoreTypeGoodsNos = new ArrayList<>();
        for (NecessaryCommonGoodsDTO goodsDTO : list) {
            if (!categoryIds.contains(goodsDTO.getCategoryId()) && !categoryIds.contains(goodsDTO.getMiddleCategoryId())) {
                errorStoreTypeGoodsNos.add(goodsDTO.getGoodsNo());
                continue;
            }
            for (String storeType : param.getStoreTypes()) {
                for (String city : param.getCitys()) {
                    NecessaryStoreTypeGoods storeTypeGoods = new NecessaryStoreTypeGoods();
                    BeanUtils.copyProperties(goodsDTO, storeTypeGoods);
                    storeTypeGoods.setCompanyOrgId(company.getBusinessOrgId());
                    storeTypeGoods.setCompanyCode(company.getBusinessSapCode());
                    storeTypeGoods.setCompanyName(company.getBusinessShortName());
                    storeTypeGoods.setBusinessid(company.getBusinessId());
                    storeTypeGoods.setCity(city);
                    storeTypeGoods.setStoreType(storeType);
                    storeTypeGoodsList.add(storeTypeGoods);
                }
            }
            addList.add(goodsDTO);
        }
        if (CollectionUtils.isNotEmpty(errorStoreTypeGoodsNos)) {
            throw new AmisBadRequestException("商品:" + errorStoreTypeGoodsNos.stream().collect(Collectors.joining(",")) + "与店型冲突,无法加入,请修改");
        }

        List<String> goodsNos = storeTypeGoodsList.stream().map(NecessaryStoreTypeGoods::getGoodsNo).collect(Collectors.toList());
        List<Long> storeIds = mdmStroeExDTOS.stream().distinct().map(MdmStoreExDTO::getStoreId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new AmisBadRequestException("无符合条件的门店，不可新增必备。");
        }
//        ⑤查店型选配表：平台、管理主体、城市、店型、SKU ，存在则删除该行。
        NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
        chooseStoreTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getId()).andCompanyOrgIdEqualTo(company.getId()).andCityIn(param.getCitys()).andStoreTypeIn(param.getStoreTypes()).andGoodsNoIn(goodsNos);
        necessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);

//        ⑥查单店必备表：平台、管理主体、城市、店型下的门店list、SKU，存在则删除N行。
        NecessarySingleStoreGoodsExample singleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
        singleStoreGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getId()).andCompanyOrgIdEqualTo(company.getId()).andCityIn(param.getCitys()).andStoreIdIn(storeIds).andGoodsNoIn(goodsNos);
        necessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);

        // 添加
        necessaryStoreTypeGoodsExtendMapper.batchInsert(storeTypeGoodsList);
        message = addStoreGoods(storeIds, addList.stream().collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
        return this;

    }

    @Override
    public List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add) {
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        if (add) {
            storeInfos.addAll(necessaryContentsService.getPlatformStoreInfo(param.getPlatformOrgId(), Lists.newArrayList(companyOrg.getBusinessOrgId()), userDTO.getUserId()));
        } else {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(companyOrg.getId()).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("没有获取到店型:" + param.getStoreTypeNames() + "下的门店");
        }
        if (!add) {
            return storeInfos;
        }
        List<ChildOrgsDTO> childOrgsDTOS = permissionService.listChildOrgAssignedType(Lists.newArrayList(param.getCompanyOrgId()), OrgTypeEnum.STORE.getCode());
        if (CollectionUtils.isEmpty(childOrgsDTOS)) {
            throw new AmisBadRequestException("没有获取到企业:" + companyOrg.getShortName() + "下的门店");
        }
        List<Long> children = childOrgsDTOS.get(0).getChildren().stream().map(OrgDTO::getOutId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            throw new AmisBadRequestException("没有获取到企业:" + companyOrg.getShortName() + "下的门店");
        }
        storeInfos = storeInfos.stream().filter(v -> children.contains(v.getStoreId()) && param.getCitys().contains(v.getCity())
                && (param.getStoreTypes().contains(v.getZsStoreTypeCode()) || param.getStoreTypes().contains(v.getStoreTypeCode()) || param.getStoreTypes().contains(v.getPfStoreTypeCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("没有获取到企业:" + companyOrg.getShortName() + "与店型:" + param.getStoreTypeNames() + "下的门店");
        }
        return storeInfos;

    }

    @Override
    public String check() {
        if (null == param.getCompanyOrgId()) {
            throw new AmisBadRequestException("请选择企业");
        }
        if (CollectionUtils.isEmpty(param.getCitys())) {
            throw new AmisBadRequestException("请选择城市");
        }
        if (CollectionUtils.isEmpty(param.getStoreTypes())) {
            throw new AmisBadRequestException("请选择店型");
        }
        // 组货店型==0或==传入的店型size,否则说明店型不都属于组货或者中参
        RuleParam ruleParam = new RuleParam();
        // 写死  查店型用
        ruleParam.setScopeCode("TaskCreate");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
        // 中参必备店型
        Map<String, String> zsStoreGroup = ruleEnum.get("ZsStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 配方必备店型
        Map<String, String> pfStoreGroup = ruleEnum.get("PfStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 组货必备店型
        Map<String, String> storeGroup = ruleEnum.get("StoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));

        int storeTypeSize = param.getStoreTypes().stream().filter(v -> storeGroup.keySet().contains(v)).collect(Collectors.toList()).size();
        int pfStoreTypeSize = param.getStoreTypes().stream().filter(v -> pfStoreGroup.keySet().contains(v)).collect(Collectors.toList()).size();
        int zsStoreTypeSize = param.getStoreTypes().stream().filter(v -> zsStoreGroup.keySet().contains(v)).collect(Collectors.toList()).size();
        if (!(
                (storeTypeSize == 0 || storeTypeSize == param.getStoreTypes().size())
                && (pfStoreTypeSize == 0 || pfStoreTypeSize == param.getStoreTypes().size())
                && (zsStoreTypeSize == 0 || zsStoreTypeSize == param.getStoreTypes().size())
        )) {
            throw new AmisBadRequestException("店型类型不匹配:只能全为中参店型或组货店型或配方店型");
        }

        String checkMsg = super.check();
        // ②平台、店型所属【平台组货店型】、SKU。
        Set<String> platStoreTypes = new HashSet<>();
        param.getStoreTypes().forEach(s -> platStoreTypes.add(platStoreType(s)));
        List<String> platformExistsGoods = necessaryPlatformGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), CollectionUtils.isEmpty(platStoreTypes.stream().filter(Objects::nonNull).collect(Collectors.toSet())) && storeTypeSize == 0 ? param.getStoreTypes() : Lists.newArrayList(platStoreTypes), param.getGoodsNos());
        StringBuilder message = new StringBuilder();
        if (StringUtils.isNotBlank(checkMsg)) {
            message.append(checkMsg);
        }
        if (CollectionUtils.isNotEmpty(platformExistsGoods)) {
            message.append(platformExistsGoods.stream().distinct().collect(Collectors.joining("、")) + "，已在平台必备中存在，不可添加。");
        }
        // ③企业必备表查重：平台、管理主体、城市、SKU
        List<String> companyGoodsExists = necessaryCompanyGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), param.getCompanyOrgId(), param.getCitys(), param.getGoodsNos());
        if (CollectionUtils.isNotEmpty(companyGoodsExists)) {
            message.append(companyGoodsExists.stream().distinct().collect(Collectors.joining("、")) + "，已在企业必备中存在，不可添加。");
        }
        //④店型必备表查重：平台、管理主体、城市、店型、SKU
        List<String> storeTypeGoodsExists = necessaryStoreTypeGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), param.getCompanyOrgId(), param.getCitys(), param.getStoreTypes(), param.getGoodsNos());
        if (CollectionUtils.isNotEmpty(storeTypeGoodsExists)) {
            message.append(storeTypeGoodsExists.stream().distinct().collect(Collectors.joining("、")) + "，已在店型必备中存在，不可添加。");
        }
        if (message.length() > 0) {
            if (!param.getExistsIgnore()) {
                message.append("是否排除掉重复商品，继续添加？");
                return message.toString();
            } else {
                storeTypeGoodsExists.addAll(platformExistsGoods);
                storeTypeGoodsExists.addAll(companyGoodsExists);
                param.setGoodsNos(param.getGoodsNos().stream().filter(v -> !storeTypeGoodsExists.contains(v)).distinct().collect(Collectors.toList()));
                if (org.apache.commons.collections.CollectionUtils.isEmpty(param.getGoodsNos())) {
                    throw new AmisBadRequestException("排除重复后没有数据了");
                }
            }
        }
        return "";
    }
}
