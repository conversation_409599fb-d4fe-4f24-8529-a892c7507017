package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.StringJoiner;

@Data
public class MdmTaskQueryParam extends AmisPageParam {

    @ApiModelProperty(value = "任务状态")
    private Byte taskStatus;
    @ApiModelProperty(value = "任务来源")
    private Byte taskSource;
    @ApiModelProperty(value = "创建人")
    private String userName;
    @ApiModelProperty(value = "创建日期")
    private String createDate;
    @ApiModelProperty(value = "备注")
    private String remarks;
    @ApiModelProperty(value = "任务唯一值")
    private String paramUniqueMark;
}
