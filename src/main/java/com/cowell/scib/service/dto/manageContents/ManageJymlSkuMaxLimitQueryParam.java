package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 经营目录-sku数配置上线管理
 */
@Data
public class ManageJymlSkuMaxLimitQueryParam extends AmisPageParam implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("platform_org_id")
    private Long platformOrgId;
    @JsonProperty("business_org_id")
    private Long businessOrgId;
    @JsonProperty("version")
    private Long version;
    @JsonProperty("city")
    private String city;
    @JsonProperty("category")
    private String category;
    @JsonProperty("middle_category")
    private String middleCategory;
    @JsonProperty("small_category")
    private String smallCategory;
    @JsonProperty("sub_category")
    private String subCategory;
    @JsonProperty("store_type")
    private String storeType;
}
