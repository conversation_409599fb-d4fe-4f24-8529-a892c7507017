package com.cowell.scib.service.dto.necessaryComtentsV2;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 必备目录管理表
 */
@Data
public class StoreGoodsContentDTO implements Serializable {
    // 业务类型 必填
    private Integer bizType;

    /**
     * 必备标签 没有填0
     */
    private Integer necessaryTag = 0;
    /**
     * 经营状态
     */
    private Integer manageStatus;
    /**
     * 建议经营状态
     */
    private Integer suggestManageStatus;

    /**
     * 必备标签名称
     */
    private String necessaryTagName = "";

    /**
     * 平台orgid
     */
    private Long platformOrgId;

    /**
     * 连锁id
     */
    private Long businessId;

    /**
     * 门店ID 必填
     */
    private Long storeId;

    /**
     * 子类编码 必填
     */
    private Long subCategoryId;
    // 商品信息必填
    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品通用名
     */
    private String goodsCommonName;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 采购属性
     */
    private String purchaseAttr;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private Boolean delContent;
    private String platformName;
    private String companyName;
    private BigDecimal minDisplayQuantity;
    private Byte effectStatus;
    private Long mdmTaskId;
    private String scopeName;

}
