package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> 一店一目表
 */
@Data
public class StoreGoodsInfoDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 门店orgID
     */
    @ApiModelProperty(value = "门店orgID")
    private Long storeOrgId;

    /**
     * 门店ID
     */
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    /**
     * 门店MDM编码
     */
    @ApiModelProperty(value = "门店MDM编码")
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 平台
     */
    @ApiModelProperty(value = "平台")
    private String platformName;

    /**
     * 企业
     */
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specifications;

    /**
     * 厂家
     */
    @ApiModelProperty(value = "厂家")
    private String manufacturer;

    /**
     * 子类id
     */
    @ApiModelProperty(value = "子类id")
    private Long subCategoryId;

    /**
     * 必备标识(0非必备 1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)
     */
    @ApiModelProperty(value = "必备标识(0非必备 1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备")
    private Byte necessaryTag;

    /**
     * 必备标识
     */
    @ApiModelProperty(value = "必备标识")
    private String necessaryTagDesc;

    /**
     * 是否有效(0 否 1 是)
     */
    @ApiModelProperty(value = "是否有效(0 否 1 是)")
    private Byte effectStatus;

    /**
     * 最小陈列量
     */
    @ApiModelProperty(value = "最小陈列量")
    private BigDecimal minDisplayQuantity;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdName;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updatedBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedName;

    public static LinkedHashMap<String, String> getFieldMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("platformName", "平台");
        map.put("companyName", "企业");
        map.put("storeName", "门店名称");
        map.put("goodsNo", "商品编码");
        map.put("necessaryTagDesc", "配置类型");
        map.put("minDisplayQuantity", "最小陈列量");
        return map;
    }
}
