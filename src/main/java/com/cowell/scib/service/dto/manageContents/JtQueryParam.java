package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 经营目录-sku数配置上线管理
 */
@Data
public class JtQueryParam extends AmisPageParam implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("platform_org_id")
    private Long platformOrgId;
    @JsonProperty("company_org_id")
    private Long companyOrgId;
    @JsonProperty("city")
    private String city;
}
