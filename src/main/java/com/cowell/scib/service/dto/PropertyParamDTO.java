package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class PropertyParamDTO implements Serializable {

    private static final long serialVersionUID = -1317431903053697352L;
    /**
     * 连锁ID
     */
    @ApiModelProperty(value = "连锁ID")
    private Long businessId;
    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;
    /**
     * 属性集合
     */
    private List<PropertyParamRankDto> paramRankDtos;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public List<PropertyParamRankDto> getParamRankDtos() {
        return paramRankDtos;
    }

    public void setParamRankDtos(List<PropertyParamRankDto> paramRankDtos) {
        this.paramRankDtos = paramRankDtos;
    }
}
