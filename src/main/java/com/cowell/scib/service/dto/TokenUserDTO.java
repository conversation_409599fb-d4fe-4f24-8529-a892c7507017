package com.cowell.scib.service.dto;

import com.alibaba.fastjson.JSON;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class TokenUserDTO {


	private Long userId;

    private Long businessId;

    private String openId;

    private String name;

    private String userName;

    private Set<String> roles;

    /**
     * 系统类型
     * SRM系统=1
     */
    private Integer systemType;

    /**
     * 放置Token的值
     */
    private String bindingValue;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Long businessId) {
		this.businessId = businessId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Set<String> getRoles() {
		return roles;
	}

	public void setRoles(Set<String> roles) {
		this.roles = roles;
	}

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public String getBindingValue() {
        return bindingValue;
    }

    public void setBindingValue(String bindingValue) {
        this.bindingValue = bindingValue;
    }

    public static TokenUserDTO toDTO(Map<String,Object> map) {
    	if(map==null) {
    		return null;
    	}
//    	String tokenUserDTOStr=String.valueOf(map.get("username"));
//    	TokenUserDTO tokenUserDTO=JSON.parseObject(tokenUserDTOStr, TokenUserDTO.class);
    	TokenUserDTO tokenUserDTO=new TokenUserDTO();
    	if(map.get("businessId")!=null) {
        	String businessId=(String) map.get("businessId");
        	tokenUserDTO.setBusinessId(Long.valueOf(businessId));
    	}
    	if(map.get("userId")!=null) {
        	String userId=(String) map.get("userId");
        	tokenUserDTO.setUserId(Long.valueOf(userId));
    	}
    	if(map.get("name")!=null) {
        	tokenUserDTO.setName(String.valueOf(map.get("name")));
    	}
    	if(map.get("loginName")!=null) {
        	tokenUserDTO.setUserName(String.valueOf(map.get("loginName")));
    	}
    	if(map.get("openId")!=null) {
        	tokenUserDTO.setOpenId((String.valueOf(map.get("openId"))));
    	}
    	if(map.get("systemType")!=null) {
            String systemType = String.valueOf(map.get("systemType"));
        	tokenUserDTO.setSystemType(Integer.valueOf(systemType));
    	}
        if(map.get("bindingValue")!=null) {
            tokenUserDTO.setBindingValue((String.valueOf(map.get("bindingValue"))));
        }
        if(map.get("roles")!=null) {
        	String roles=String.valueOf(map.get("roles"));
        	List<String> list=JSON.parseArray(roles, String.class);
        	Set<String> set=new HashSet<String>(list);
        	tokenUserDTO.setRoles(set);
    	}
    	return tokenUserDTO;
    }

    @Override
    public String toString() {
        return "TokenUserDTO{" +
            "userId=" + userId +
            ", businessId=" + businessId +
            ", openId='" + openId + '\'' +
            ", name='" + name + '\'' +
            ", userName='" + userName + '\'' +
            ", systemType='" + systemType + '\'' +
            ", bindingValue='" + bindingValue + '\'' +
            ", roles=" + roles +
            '}';
    }
}
