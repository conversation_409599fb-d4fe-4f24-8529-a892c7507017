package com.cowell.scib.service.dto.TrackResult;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class StoreTypeGoodsData {

    @ExcelProperty(value = "企业ID",index = 0)
    private Long orgId;

    @ExcelProperty(value = "城市",index = 1)
    private String city;

    @ExcelProperty(value = "店型",index = 2)
    private String storeType;

    @ExcelProperty(value = "商品编码",index = 3)
    private String goodsNo;

    @ExcelProperty(value = "错误原因",index = 4)
    private String errorReason;

}
