package com.cowell.scib.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class MbCommonDTO<T> {

    @JSONField(name = "bdatetime")
    private String bdatetime;

    @JSONField(name = "bstatus")
    private Integer bstatus;

    @JSONField(name = "btype")
    private Integer btype;

    @JSONField(name = "bdestination")
    private Integer bdestination;

    @JSONField(name = "bguid")
    private String bguid;

    @JSONField(name = "bsource")
    private Integer bsource;

    @JSONField(name = "bversion")
    private String bversion;

    @JSONField(name = "bdata")
    private T bdata;

}
