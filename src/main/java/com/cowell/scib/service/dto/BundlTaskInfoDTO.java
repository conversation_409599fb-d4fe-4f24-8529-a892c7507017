package com.cowell.scib.service.dto;

import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.enums.BundlTaskTypeEnum;
import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:15
 */
@Data
public class BundlTaskInfoDTO implements Serializable, AmisDataInInterface  {

    /**
     * 主键
     */
    @ApiModelProperty(value = "任务主键")
    private Long taskId;

    /**
     * 任务编码
     */
    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货  枚举 BundlTaskTypeEnum
     */
    @ApiModelProperty(value = "组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货")
    private Byte taskType;

    /**
     * orgId
     */
    @ApiModelProperty(value = "平台组织ID")
    private Long orgId;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "平台组织名称")
    private String orgName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货 枚举 BundlTaskTypeEnum
     */
    @ApiModelProperty(value = "组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货")
    private String taskTypeName;
    /**
     * 商品类别
     */
    @ApiModelProperty(value = "商品类别")
    private List<String> goodCategory = new ArrayList<>();

    /**
     * 组货公司
     */
    @ApiModelProperty(value = "组货公司")
    private List<String> businessName = new ArrayList<>();

    /**
     * 组货公司
     */
    @ApiModelProperty(value = "组货店型")
    private List<String> groupStoreType = new ArrayList<>();
    /**
     * 组货公司
     */
    @ApiModelProperty(value = "中参店型")
    private List<String> ZSStoreType = new ArrayList<>();


    /****** 5: 新店目录推荐任务  后加参数 start ******/

    @ApiModelProperty("新店公司")
    private Long businessOrgId;

    @ApiModelProperty("新店公司名称")
    private String selectBusinessName;

    @ApiModelProperty("门店来源")
    private int selectStoreSource=0;

    @ApiModelProperty("新店StoreId")
    private Long selectStoreId;

    @ApiModelProperty("门店编码")
    private String selectSapCode;

    @ApiModelProperty("门店名")
    private String selectStoreName;

    @ApiModelProperty("门店属性")
    private String selectStoreAttr;

    @ApiModelProperty("相似门店storeIdList")
    private List<Long> similarStoreIdList;

    @ApiModelProperty("新店经营商品最高成本单价")
    private BigDecimal goodMaxCostPrice;

    @ApiModelProperty("预计开业时间")
    private String predictOpenTime;

    @ApiModelProperty("新店黑名单查询参数")
    private String blackListParamUniqueMark;


    //统计结果
    private boolean existResult;
    private long skuCount;
    private long zhongXiChengYaoSkuCount;
    private long rxCount;
    private long otcCount;
    private long zhongYaoCangRongCount;
    private long healthFoodSkuCount;
    private long medicalDeviceSkuCount;
    private long otherSkuCount;
    private int zhongXiChengYaoComponentCount;
    private int zhongYaoCangRongComponentCount;
    private int healthFoodSubcategoryCount;
    private int medicalDeviceSubcategoryCount;
    private int otherSubcategoryCount;
    private BigDecimal totalSuggestPhQty;
    private BigDecimal totalPhCost;
    /****** 5: 新店目录推荐任务  后加参数 end******/


    public void initXDMLParams(JSONObject jsonObject){
        // 新店目录推荐任务 新店目录推荐任务 后加参数
        if(!BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(getTaskType()) ||  Objects.isNull(jsonObject)){
            return;
        }
        this.businessOrgId = jsonObject.containsKey("businessOrgId")?jsonObject.getLong("businessOrgId"):0L;
        this.selectBusinessName = getBusinessOrgId() > 0L && CacheVar.getBusinessByOrgId(getBusinessOrgId()).isPresent() ? CacheVar.getBusinessByOrgId(getBusinessOrgId()).get().getBusinessShortName() : "";
        this.selectStoreId = jsonObject.containsKey("selectStoreId")? jsonObject.getLong("selectStoreId"):0L;
        this.selectStoreSource= jsonObject.containsKey("selectStoreSource")? jsonObject.getInteger("selectStoreSource"):0;
        this.selectSapCode = jsonObject.containsKey("selectSapCode")?jsonObject.getString("selectSapCode"):"";
        this.selectStoreAttr= CacheVar.getStoreExtInfoBySapCode(getSelectSapCode()).isPresent()? CacheVar.getStoreExtInfoBySapCode(getSelectSapCode()).get().getStoreAttr():"";
        this.selectStoreName = jsonObject.containsKey("selectStoreName")?jsonObject.getString("selectStoreName"):"";
        this.similarStoreIdList = jsonObject.containsKey("similarStoreIdList")?jsonObject.getJSONArray("similarStoreIdList").toJavaList(Long.class) : new ArrayList<>();
        this.goodMaxCostPrice = jsonObject.containsKey("goodMaxCostPrice")? jsonObject.getBigDecimal("goodMaxCostPrice"):null;
        this.predictOpenTime = jsonObject.containsKey("predictOpenTime")?jsonObject.getString("predictOpenTime"):null;
        this.blackListParamUniqueMark =jsonObject.containsKey("blackListParamUniqueMark")? jsonObject.getString("blackListParamUniqueMark"):null;
    }

    @ApiModelProperty(value = "组货结果当前所属版本")
    private Integer version;
}
