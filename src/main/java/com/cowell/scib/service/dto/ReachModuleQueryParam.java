package com.cowell.scib.service.dto;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReachModuleQueryParam extends AmisPageParam implements Serializable {
    @ApiModelProperty("触达模块id")
    private Long reachModuleId;
    @ApiModelProperty(value = "触达组名称")
    private String reachGroupName;
    @ApiModelProperty(value = "选择方式")
    private Integer selectMethod;
    @ApiModelProperty(value = "触达人")
    private String reachPersonName;
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

}
