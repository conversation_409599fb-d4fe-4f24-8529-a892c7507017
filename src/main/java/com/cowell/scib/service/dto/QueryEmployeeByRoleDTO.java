package com.cowell.scib.service.dto;

import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

public class QueryEmployeeByRoleDTO implements Serializable {

    /**
     * 组织机构id
     */
    private List<Long> orgIds;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色编码集合
     */
    private String[] roleCodes;

    /**
     * 组织机构id或门店id
     */
    private Integer idType;

    /**
     * 员工手机号/姓名
     */
    private String keyWord;

    /**
     * 员工id
     */
    private String empUserId;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 分页大小
     */
    private Integer size;

    public List<Long> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Long> orgIds) {
        this.orgIds = orgIds;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String[] getRoleCodes() {
        return roleCodes;
    }

    public void setRoleCodes(String[] roleCodes) {
        this.roleCodes = roleCodes;
    }

    public Integer getIdType() {
        return idType;
    }

    public void setIdType(Integer idType) {
        this.idType = idType;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getEmpUserId() {
        return empUserId;
    }

    public void setEmpUserId(String empUserId) {
        this.empUserId = empUserId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    @Override
    public String toString() {
        return "QueryEmployeeByRoleDTO{" +
            "orgIds=" + orgIds +
            ", roleCode='" + roleCode + '\'' +
            ", roleCodes=" + Arrays.toString(roleCodes) +
            ", idType=" + idType +
            ", keyWord='" + keyWord + '\'' +
            ", empUserId='" + empUserId + '\'' +
            ", name='" + name + '\'' +
            ", page=" + page +
            ", size=" + size +
            '}';
    }

    public String getRedisKey(String keyPrefix){
        StringBuilder redisKey = new StringBuilder(keyPrefix);
        if(CollectionUtils.isNotEmpty(orgIds)){
            for (Long orgId : orgIds) {
                redisKey.append("_").append(orgId);
            }
        }
        if(roleCodes != null){
            for (String roleCode : roleCodes) {
                redisKey.append("_").append(roleCode);
            }
        }
        redisKey.append("_").append(idType);
        return redisKey.toString();
    }
}
