package com.cowell.scib.service.dto.skuadjust;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdjustConfigrueIdMapping {
    @ApiModelProperty(value = "门店老标准Id")
    private Long oldConfigureId;
    @ApiModelProperty(value = "门店新标准Id")
    private Long configureId;
//    @ApiModelProperty(value = "商品分类")
//    private String category;
//    @ApiModelProperty(value = "分类层级")
//    private Integer level;
}
