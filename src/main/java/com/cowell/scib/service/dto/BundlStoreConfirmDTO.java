package com.cowell.scib.service.dto;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2023/3/13 10:33
 */
@Data
public class BundlStoreConfirmDTO extends MdmStoreBaseExtendDTO implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = -9108790889646827631L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 连锁企业MDM编码
     */
    @ApiModelProperty(value = "组货公司编码")
    private String companyCode;

    /**
     * 门店编码：SAP编码，编码段A000-ZZZZ
     */
    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组货公司名称")
    private String companyName;

    /**
     * 门店简称: 连锁企业+地级市/县级市+核心地名+店
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 月销售额等级
     */
    @ApiModelProperty(value = "月销售额等级")
    private String salesLevel;

    /**
     * 组货对应店型，大店/中店/小店
     */
    @ApiModelProperty(value = "组货对应店型")
    private String bundlStore;

    /**
     * 选址商圈店型
     */
    @ApiModelProperty(value = "选址商圈业态")
    private String tradingArea;

    /**
     * 组货对应商圈，院边店/乡镇店/社商店
     */
    @ApiModelProperty(value = "组货对应商圈")
    private String bundlMall;

    /**
     * 省: 门店所在省份
     */
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 城市: 门店所在城市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    private String area;

    /**
     * 详细地址: 详细地址到门牌号，乡镇店
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 门店状态: 营业、永久性闭店、暂时性闭店
     */
    @ApiModelProperty(value = "门店状态")
    private String storeStatus;

    /**
     * 开业日期: 正式营业的开始日期，对于收购门店，以收购店实际开业日期为准；如原系统没有记录或不能确认收购门店的开业日期，则以交割日期为准
     */
    @ApiModelProperty(value = "营业日期")
    private String openDate;

    /**
     * 关店日期: 如果门店状态为闭店，此字段必输
     */
    @ApiModelProperty(value = "永久闭店日期")
    private String closeDate;

    /**
     * 门店属性: 直营-自建、加盟、托管、直营-并购
     */
    @ApiModelProperty(value = "门店属性")
    private String storeAttr;

    /**
     * 业态 商超/批发
     */
    @ApiModelProperty(value = "业态")
    private String format;

    /**
     * 特殊业态店型
     */
    @ApiModelProperty(value = "特殊业态店型")
    private String operationType;

    /**
     * 特殊业务类型 DTP/慢病
     */
    @ApiModelProperty(value = "特殊业务类型")
    private String specialType;

    /**
     * dtp门店
     */
    @ApiModelProperty(value = "DTP药房")
    private String dtp;

    /**
     * 是否参与组货系统建议 否 是
     */
    @ApiModelProperty(value = "是否参与组货系统建议")
    private String bundlAdviceAble;

    /**
     * 是否参与组货业务确认 0-否 1-是
     */
    @ApiModelProperty(value = "是否参与组货业务确认")
    private Boolean bundlConfirmAble;

    /**
     * 是否参与组货业务确认 0-否 1-是
     */
    @ApiModelProperty(value = "是否参与组货业务确认")
    private String bundlConfirmAbleDesc;

    /*********缺少字段**********/
    //中参店型
    //配方店型
    //经营状态
    //停止营业日期
    //是否电商门店
    //B2C门店


    public static LinkedHashMap<String, String> getCommonStoresExportMap() {
        LinkedHashMap map = new LinkedHashMap();
        map.put("companyName", "组货公司");
        map.put("storeCode", "门店编码");
        map.put("storeName", "门店名称");
        map.put("salesLevel", "月销售等级");
        map.put("tradingArea", "选址商圈业态");
        map.put("bundlStore", "组货店型");
        map.put("zsShop", "中参店型");
        map.put("psStore", "配方店型");
        map.put("province", "省");
        map.put("city", "城市");
        map.put("area", "区/县");
        map.put("address", "详细地址");
        map.put("storeStatus", "门店状态");
        map.put("manageState", "经营状态");
        map.put("openDate", "营业日期");
        map.put("handoverDate", "停止营业日期");
        map.put("closeDate", "永久性闭店日期");
        map.put("storeAttr", "门店属性");
        map.put("format", "业态");
        map.put("operationType", "特殊业态店型");
        map.put("specialType", "特殊业务类型");
        map.put("dtp", "DTP药房");
        map.put("manSubject", "是否电商门店");
        map.put("b2cShop", "B2C门店");
        map.put("bundlAdviceAble", "是否参与组货-系统建议");
        map.put("bundlConfirmAbleDesc", "是否参与组货-业务确认");
        return map;
    }














}
