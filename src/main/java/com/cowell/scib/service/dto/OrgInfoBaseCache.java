package com.cowell.scib.service.dto;

import java.io.Serializable;

public class OrgInfoBaseCache implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String shortName;
    private Integer type;
    private Long outId;
    private String sapCode;
    private String orgPath;
    private Long businessOrgId;
    private Long businessId;
    private String businessSapCode;
    private String businessShortName;
    private Long platformOrgId;
    private String platformShortName;
    /**
     * 非核心属性,拓展属性放到 extend中
     */
    private String extend;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getOutId() {
        return outId;
    }

    public void setOutId(Long outId) {
        this.outId = outId;
    }

    public String getSapCode() {
        return sapCode;
    }

    public void setSapCode(String sapCode) {
        this.sapCode = sapCode;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public Long getBusinessOrgId() {
        return businessOrgId;
    }

    public void setBusinessOrgId(Long businessOrgId) {
        this.businessOrgId = businessOrgId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessSapCode() {
        return businessSapCode;
    }

    public void setBusinessSapCode(String businessSapCode) {
        this.businessSapCode = businessSapCode;
    }

    public String getBusinessShortName() {
        return businessShortName;
    }

    public void setBusinessShortName(String businessShortName) {
        this.businessShortName = businessShortName;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformShortName() {
        return platformShortName;
    }

    public void setPlatformShortName(String platformShortName) {
        this.platformShortName = platformShortName;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    @Override
    public String toString() {
        return "OrgInfoBaseCache{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", shortName='" + shortName + '\'' +
                ", type=" + type +
                ", outId=" + outId +
                ", sapCode='" + sapCode + '\'' +
                ", orgPath='" + orgPath + '\'' +
                ", businessOrgId=" + businessOrgId +
                ", businessId=" + businessId +
                ", businessSapCode='" + businessSapCode + '\'' +
                ", businessShortName='" + businessShortName + '\'' +
                ", platformOrgId=" + platformOrgId +
                ", platformShortName='" + platformShortName + '\'' +
                ", extend='" + extend + '\'' +
                '}';
    }
}
