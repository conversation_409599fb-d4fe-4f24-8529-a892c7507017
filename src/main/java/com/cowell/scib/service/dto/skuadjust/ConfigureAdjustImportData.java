package com.cowell.scib.service.dto.skuadjust;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ConfigureAdjustImportData {

    @ExcelProperty(value = "门店编码",index = 0)
    private String storeCode;

    @ExcelProperty(value = "商品管控类别",index = 1)
    private String ctrlCategory;

    @ExcelProperty(value = "新标准（店型）",index = 2)
    private String storeType;

    @ExcelProperty(value = "错误原因",index = 3)
    private String errorReason;

}
