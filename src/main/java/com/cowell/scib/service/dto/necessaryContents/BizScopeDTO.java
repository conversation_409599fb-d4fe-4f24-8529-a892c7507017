package com.cowell.scib.service.dto.necessaryContents;


import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR> 店型必备商品
 */
public class BizScopeDTO implements Serializable {

    /**
     * 经营属性
     */
    @ApiModelProperty(value = "经营属性")
    private String goodsline;

    @ApiModelProperty(value = "经营范围-标识")
    private String busiscopetag;

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    public String getBusiscopetag() {
        return busiscopetag;
    }

    public void setBusiscopetag(String busiscopetag) {
        this.busiscopetag = busiscopetag;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BizScopeDTO.class.getSimpleName() + "[", "]")
                .add("goodsline='" + goodsline + "'")
                .add("busiscopetag='" + busiscopetag + "'")
                .toString();
    }
}
