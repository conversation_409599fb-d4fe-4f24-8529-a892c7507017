package com.cowell.scib.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class StoreGoodEffectStatusDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 门店orgID
     */
    private Long storeOrgId;

    /**
     * 是否有效(0 否 1 是)
     */
    private Byte effectStatus;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 更新人
     */
    private String updatedName;


    /**
     * 更新人ID
     */
    private Long updatedBy;
}
