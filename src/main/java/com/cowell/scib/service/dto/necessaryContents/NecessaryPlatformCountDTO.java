package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 店型必备商品
 */
@Data
public class NecessaryPlatformCountDTO implements Serializable {
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    /**
     * 条码
     */
    @ApiModelProperty(value = "条数")
    private Integer count;

    /**
     * 大类id
     */
    @ApiModelProperty(value = "大类id")
    private Long categoryId;

}
