package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NecessaryDirectionsParam implements Serializable {

    @ApiModelProperty(value = "区域平台ID")
    private Long platformId;

    @ApiModelProperty(value = "企业ID")
    private List<Long> companyId;

    @ApiModelProperty(value = "商品编码(必填)")
    private String goodsNo;
}
