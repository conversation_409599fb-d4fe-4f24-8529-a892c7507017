package com.cowell.scib.service.dto.skuadjust;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SkuAdjustAddParam {
    @ApiModelProperty(value = "门店orgId")
    private Long storeOrgId;
    @ApiModelProperty(value = "调整单ID")
    private Long adjustId;
    @ApiModelProperty(value = "是否审批")
    private Boolean approveAble;
    @ApiModelProperty(value = "是否跳过权限校验")
    private Boolean skipCheckPerm;
    @ApiModelProperty(value = "调整信息")
    private List<AdjustConfigrueIdMapping> mappings;
}
