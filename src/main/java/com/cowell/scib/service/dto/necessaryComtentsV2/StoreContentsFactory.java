package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.enums.StoreContentBizTypeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class StoreContentsFactory {

    @Resource
    private StoreContentsNecessaryAssemble storeContentsNecessaryAssemble;
    @Resource
    private BdpMonthJyContentsAssemble bdpMonthJyContentsAssemble;
    @Resource
    private BdpDailyNewGoodsContentsAssemble bdpDailyNewGoodsContentsAssemble;
    @Resource
    private BdpDailyGoodsExchangeContentsAssemble bdpDailyGoodsExchangeContentsAssemble;
    @Resource
    private ManageContents2NonAssemble manageContents2NonAssemble;
    @Resource
    private ManageContents2ChooseAssemble manageContents2ChooseAssemble;
    @Resource
    private MdmGoodsLineChangeAssemble mdmGoodsLineChangeAssemble;

    public StoreContentsAssemble getAssemble(Integer bizType) {
        StoreContentBizTypeEnum bizTypeEnum = StoreContentBizTypeEnum.getEnumByCode(bizType);
        switch (bizTypeEnum) {
            case NECESSARY: return storeContentsNecessaryAssemble;
            case MONTH_MANAGE: return bdpMonthJyContentsAssemble;
            case DAILY_NEW_GOODS: return bdpDailyNewGoodsContentsAssemble;
            case DAILY_GOODS_EXCHANGE: return bdpDailyGoodsExchangeContentsAssemble;
            case MANAGE_CHOOSE: return manageContents2ChooseAssemble;
            case NON_MANAGE: return manageContents2NonAssemble;
            case MDM_PUSH: return mdmGoodsLineChangeAssemble;
            default:throw new AmisBadRequestException("未知的业务类型");
        }
    }
}
