package com.cowell.scib.service.dto;

import java.util.List;

public class MdmCompanyTransformDTO {

    private List<Long> businessIds;

    private List<BusinessComIdTransform> businessMappings;

    private List<String> comIds;

    private Integer transFormType;


    public List<Long> getBusinessIds() {
        return businessIds;
    }

    public void setBusinessIds(List<Long> businessIds) {
        this.businessIds = businessIds;
    }

    public List<BusinessComIdTransform> getBusinessMappings() {
        return businessMappings;
    }

    public void setBusinessMappings(List<BusinessComIdTransform> businessMappings) {
        this.businessMappings = businessMappings;
    }

    public List<String> getComIds() {
        return comIds;
    }

    public void setComIds(List<String> comIds) {
        this.comIds = comIds;
    }

    public Integer getTransFormType() {
        return transFormType;
    }

    public void setTransFormType(Integer transFormType) {
        this.transFormType = transFormType;
    }

    @Override
    public String toString() {
        return "mdmCompanyTransformDTO{" +
                "businessIds=" + businessIds +
                ", businessMappings=" + businessMappings +
                ", comIds=" + comIds +
                ", transFormType=" + transFormType +
                '}';
    }
}
