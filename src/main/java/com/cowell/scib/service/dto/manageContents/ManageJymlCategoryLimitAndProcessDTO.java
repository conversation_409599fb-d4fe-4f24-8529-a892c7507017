package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 店型组货经营目录分类的上下限和处理进度
 */
@Data
public class ManageJymlCategoryLimitAndProcessDTO extends ManageProcessDTO  implements AmisDataInInterface, Serializable {


    @ApiModelProperty(value = "提交按钮是否可用")
    private Boolean submitBtnAble;

    @ApiModelProperty(value = "审核按钮是否可用")
    private Boolean auditBtnAble;

    @ApiModelProperty(value = "选中分类处理进度")
    private Integer processStatus;

    @ApiModelProperty(value = "选中分类处理进度描述")
    private String processStatusStr;

    @ApiModelProperty(value = "只显示差异行")
    private Boolean diffRowsOnly;

    @ApiModelProperty(value = "分类经营sku数")
    private Integer  skuCount;

    @ApiModelProperty(value = "分类上限值")
    private Integer  skuLimit;

    @ApiModelProperty(value = "分类下限值")
    private Integer  skuLowerLimit;

}
