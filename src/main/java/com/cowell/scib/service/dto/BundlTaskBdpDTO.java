package com.cowell.scib.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/15 13:33
 */
@Data
public class BundlTaskBdpDTO implements Serializable {
    private static final long serialVersionUID = 1981648481781549811L;

    /**
     * 任务主信息
     */
    private TaskHeadDTO head;

    /**
     * 组货门店明细
     */
    private List<TaskStoreDTO> storeDetail;

    /**
     * 组货商品信息
     */
    private TaskGoodsDTO goodsDetail;

    /**
     * 组货规则信息
     */
    private List<TaskRuleDTO> ruleDetail;

    /**
     * 必备商品明细
     */
    private TaskNecessaryGooodsDTO necessaryGooodsDetail;

    /**
     * 必备商品明细
     */
    private TaskNewStoreRecommendDTO newStoreRecommendDTO;
}
