package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * entity_tag_relation
 * <AUTHOR>
@Data
@ApiModel("门店组件查询参数")
public class StoreComponentQueryParam implements Serializable {
    @ApiModelProperty("组件id")
    private Long id;
    @ApiModelProperty("业务身份：ISCM（供应链系统）")
    private String bizCode;
    @ApiModelProperty("业务类型：1001(数字化商品),1002(供应链-不请货门店)")
    private String bizType;
    @ApiModelProperty("查询应用范围:支持平台/连锁组织树orgId)")
    private String scopeCode;
    @ApiModelProperty("经营状态")
    private List<String> managstate;
    @ApiModelProperty("门店状态")
    private List<String> storestatus;
    @ApiModelProperty("业态")
    private List<String> format;
    @ApiModelProperty("权限白名单")
    private List<String> orgIds;
    @ApiModelProperty("门店类型")
    private List<String> storetype;
    @ApiModelProperty("特殊业态店型")
    private List<String> operationtype;
    @ApiModelProperty("特殊业务类型")
    private List<String> specialtype;
    @ApiModelProperty("门店属性  直营-自建 加盟 直营-收购 加盟事业部-分支结构 加盟事业部-个人独资 加盟事业部-特许加盟 加盟-单体-药证含加盟 加盟-单体-药证不含济加盟 加盟-连锁-药证含加盟 加盟-连锁-药证不含济加盟")
    private List<String> storeattr;
    @ApiModelProperty("权限黑名单")
    private List<String> excludeOrgIds;
    @ApiModelProperty("页码")
    private Long page = 1L;
    @ApiModelProperty("行数")
    private Long pageSize = 10L;
}