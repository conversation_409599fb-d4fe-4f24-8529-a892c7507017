package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/15 13:34
 */
@Data
public class TaskStoreDTO implements Serializable {
    private static final long serialVersionUID = -6065170499257843930L;

    /**
     * 公司组织ID
     */
    @ApiModelProperty(value = "公司组织ID")
    private Long businessOrgId=0L;

    /**
     * 门店编码：SAP编码，编码段A000-ZZZZ
     */
    @ApiModelProperty(value = "门店编码")
    private String storeCode="";

    /**
     * 月销售额等级
     */
    @ApiModelProperty(value = "月销售额等级")
    private String salesLevel="";

    /**
     * 组货对应店型，大店/中店/小店
     */
    @ApiModelProperty(value = "组货对应店型")
    private String bundlStore="";

    /**
     * 选址商圈店型
     */
    @ApiModelProperty(value = "选址商圈业态")
    private String tradingArea="";

    /**
     * 省: 门店所在省份
     */
    @ApiModelProperty(value = "省")
    private String province="";

    /**
     * 城市: 门店所在城市
     */
    @ApiModelProperty(value = "市")
    private String city="";

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    private String area="";

    @ApiModelProperty(value = "中参店型")
    private String zsShop="";

    @ApiModelProperty(value = "配方店型")
    private String psStore="";

    @ApiModelProperty(value = "平台店型")
    private String plateStore="";
}
