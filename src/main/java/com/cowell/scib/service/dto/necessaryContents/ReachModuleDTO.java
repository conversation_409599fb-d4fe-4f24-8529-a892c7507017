package com.cowell.scib.service.dto.necessaryContents;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReachModuleDTO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 触达模块id
     */
    private Long reachModule;

    /**
     * 触达模块名称
     */
    private String reachModuleName;

    /**
     * 触达组名称
     */
    private String reachGroupName;

    /**
     * 选择方式 1：按角色、2：指定人员
     */
    private Integer selectMethod;

    /**
     * 选择方式名称
     */
    private String selectMethodName;

    /**
     * 触达人。select_method等于1时存角色id。等于2时存人员工号
     */
    private List<Long> reachPersonList;

    /**
     * 触达人名称。select_method等于1时存角色名称。等于2时存人员名称
     */
    private List<String> reachPersonNameList;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 启用状态  0-停用 1-启用
     */
    private Integer status;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 操作类型 1：保存、2：编辑、3：删除
     */
    private Integer operateType;
}
