package com.cowell.scib.service.dto;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public class OrgTreeSimpleDTO implements Serializable {

    private static final long serialVersionUID = 132693748594176187L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组织机构名
     */
    @Size(max = 100)
    private String name;

    /**
     * 组织机构简称
     */
    @Size(max = 32)
    private String shortName;

    /**
     * 组织机构路径
     */
    private String orgPath;

    /**
     * 组织机构名称路径
     */
//    private String orgNamePath;

    /**
     * 组织机构类型
     */
    private Integer type;

    /**
     * 上级组织机构id
     */
    private Long parentId;

    /**
     * 组织机构级别
     */
//    private Integer level;

    /**
     * 子节点
     */
    private List<OrgTreeSimpleDTO> children;

    /**
     * 映射的z组织id,组织为门店则为storeid 是连锁为businessid
     */
    private Long outId;

    /**
     * 唯一健
     */
//    private String guidkey;

    /**
     * HR组织编码
     */
//    private String codeitemid;

    /**
     * SAP组织编码
     */
    private String sapcode;

    /**
     * 是否同步MDM数据，1开，-1关
     */
//    private Integer syncMdm;

    /**
     * 是否同步MDM数据--员工，1开，-1关
     */
//    private Integer syncMdmEmp;

    /**
     * 1内部企业,2消费医疗入驻企业
     */
//    private Integer companyType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    /*public String getOrgNamePath() {
        return orgNamePath;
    }

    public void setOrgNamePath(String orgNamePath) {
        this.orgNamePath = orgNamePath;
    }*/

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /*public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }*/

    public List<OrgTreeSimpleDTO> getChildren() {
        return children;
    }

    public void setChildren(List<OrgTreeSimpleDTO> children) {
        this.children = children;
    }

    public Long getOutId() {
        return outId;
    }

    public void setOutId(Long outId) {
        this.outId = outId;
    }

    /*public String getGuidkey() {
        return guidkey;
    }

    public void setGuidkey(String guidkey) {
        this.guidkey = guidkey;
    }

    public String getCodeitemid() {
        return codeitemid;
    }

    public void setCodeitemid(String codeitemid) {
        this.codeitemid = codeitemid;
    }*/

    public String getSapcode() {
        return sapcode;
    }

    public void setSapcode(String sapcode) {
        this.sapcode = sapcode;
    }

    /*public Integer getSyncMdm() {
        return syncMdm;
    }

    public void setSyncMdm(Integer syncMdm) {
        this.syncMdm = syncMdm;
    }

    public Integer getSyncMdmEmp() {
        return syncMdmEmp;
    }

    public void setSyncMdmEmp(Integer syncMdmEmp) {
        this.syncMdmEmp = syncMdmEmp;
    }

    public Integer getCompanyType() {
        return companyType;
    }

    public void setCompanyType(Integer companyType) {
        this.companyType = companyType;
    }*/

    @Override
    public String toString() {
        return "OrgTreeSimpleDTO{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", shortName='" + shortName + '\'' +
            ", orgPath='" + orgPath + '\'' +
            ", type=" + type +
            ", parentId=" + parentId +
            ", children=" + children +
            ", outId=" + outId +
            ", sapcode='" + sapcode + '\'' +
            '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrgTreeSimpleDTO that = (OrgTreeSimpleDTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
