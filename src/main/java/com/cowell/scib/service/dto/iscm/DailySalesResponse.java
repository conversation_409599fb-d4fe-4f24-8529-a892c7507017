package com.cowell.scib.service.dto.iscm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 根据门店与商品列表获取日销列表,请求参数
 */
@Data
public class DailySalesResponse implements Serializable {
        private static final long serialVersionUID = 7892983707110492382L;
        /**
         * 商品编码
         */
        private String  goodsNo;
        /**
         * 门店storeid
         */
        private Long storeId;

        /**
         * 是否特管0:否 1:是
         */
        private Byte specialCtrl;

        /**
         * 禁止配送0:否 1:是
         */
        private Byte distrForbid;

        /**
         * 禁止请货0:否 1:是
         */
        private Byte applyForbid;
        /**
         * 是否特管0:否 1:是
         */
        private String specialCtrlDesc;

        /**
         * 禁止配送0:否 1:是
         */
        private String distrForbidDesc;

        /**
         * 禁止请货0:否 1:是
         */
        private String applyForbidDesc;
}
