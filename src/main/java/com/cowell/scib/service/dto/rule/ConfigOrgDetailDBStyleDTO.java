package com.cowell.scib.service.dto.rule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 参数组织机构配置明细表
 */
@Data
public class ConfigOrgDetailDBStyleDTO implements Serializable {
    /**
     * 主键
     */
    @JSONField(name = "id")
    private Long id;

    /**
     * 机构配置Id
     */
    @JSONField(name = "config_id")
    private Long config_id;

    /**
     * 属性类型 1.obj/2collection
     */
    @JSONField(name = "perproty_type")
    private Byte perproty_type;

    /**
     * 字典编码
     */
    @JSONField(name = "dict_code")
    private String dict_code;

    /**
     * 参数值
     */
    @JSONField(name = "perproty_value")
    private String perproty_value;

    /**
     * 状态(-1删除，0正常)
     */
    @JSONField(name = "status")
    private Byte status;

    /**
     * 创建时间
     */
    @JSONField(name = "gmt_create")
    private Date gmt_create;

    /**
     * 更新时间
     */
    @JSONField(name = "gmt_update")
    private Date gmt_update;

    /**
     * 扩展字段
     */
    @JSONField(name = "extend")
    private String extend;

    /**
     * 版本号
     */
    @JSONField(name = "version")
    private Integer version;

    /**
     * 创建人ID
     */
    @JSONField(name = "created_by")
    private Long created_by;

    /**
     * 创建人
     */
    @JSONField(name = "created_name")
    private String created_name;

    /**
     * 更新人ID
     */
    @JSONField(name = "updated_by")
    private Long updated_by;

    /**
     * 更新人
     */
    @JSONField(name = "updated_name")
    private String updated_name;

}
