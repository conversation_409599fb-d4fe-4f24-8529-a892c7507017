package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class StoreGoodsQueryParam extends AmisPageParam implements Serializable {

    @ApiModelProperty(value = "门店Id")
    private Long storeId;
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

}
