package com.cowell.scib.service.dto;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023-03-24 11:06:37
 * @desc 技术中统一报警组件
 */
public class AlertContent {
    public static final String QYWX_GROUP="QYWX_GROUP";
    /**
     * 类型 EMAIL：邮箱，QYWX：企业微信，QYWX_GROUP：企业微信内部群
     */
    private String type;
    private String toUsers;
    private String subject;
    private String message;
//    private String businessType;
    private Boolean flag;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getToUsers() {
        return toUsers;
    }

    public void setToUsers(String toUsers) {
        this.toUsers = toUsers;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

//    public String getBusinessType() {
//        return businessType;
//    }
//
//    public void setBusinessType(String businessType) {
//        this.businessType = businessType;
//    }

    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", AlertContent.class.getSimpleName() + "[", "]")
                .add("type='" + type + "'")
//                .add("businessType='" + businessType + "'")
                .add("flag='" + flag + "'")
                .add("toUsers='" + toUsers + "'")
                .add("subject='" + subject + "'")
                .add("message='" + message + "'")
                .toString();
    }
}
