package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ManageStatusImportData {

    @ExcelProperty(value = "门店ID",index = 0)
    private Long storeId;

    @ExcelProperty(value = "商品编码",index = 1)
    private String goodsNo;

    @ExcelProperty(value = "建议经营状态",index = 2)
    private Integer suggestManageStatus;

    @ExcelProperty(value = "经营状态",index = 3)
    private Integer manageStatus;

}
