package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;

import java.util.LinkedHashMap;
import java.util.StringJoiner;

public class InitImportNecessaryDTO {
    @ApiModelProperty(value = "行号")
    private Integer lineNum;

    @ApiModelProperty(value = "平台orgid")
    private Long platformOrgId;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    // 平台 店型 选配
    @ApiModelProperty(value = "店型")
    private String storeType;

    @ApiModelProperty(value = "企业orgid")
    private Long companyOrgId;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    @ApiModelProperty(value = "采购属性")
    private String purchaseAttr;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    public static LinkedHashMap<String, String> initNecessaryFieldMap(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("平台orgid", "platformOrgId");
        map.put("商品编码", "goodsNo");
        map.put("店型", "storeType");
        map.put("企业orgid", "companyOrgId");
        map.put("城市", "city");
        map.put("门店编码", "storeCode");
        map.put("采购属性", "purchaseAttr");
        return map;
    }

    public Integer getLineNum() {
        return lineNum;
    }

    public void setLineNum(Integer lineNum) {
        this.lineNum = lineNum;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getPurchaseAttr() {
        return purchaseAttr;
    }

    public void setPurchaseAttr(String purchaseAttr) {
        this.purchaseAttr = purchaseAttr;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", InitImportNecessaryDTO.class.getSimpleName() + "[", "]")
                .add("lineNum=" + lineNum)
                .add("platformOrgId=" + platformOrgId)
                .add("goodsNo='" + goodsNo + "'")
                .add("companyOrgId=" + companyOrgId)
                .add("storeType='" + storeType + "'")
                .add("city='" + city + "'")
                .add("storeCode='" + storeCode + "'")
                .add("purchaseAttr='" + purchaseAttr + "'")
                .add("errorMsg='" + errorMsg + "'")
                .toString();
    }
}
