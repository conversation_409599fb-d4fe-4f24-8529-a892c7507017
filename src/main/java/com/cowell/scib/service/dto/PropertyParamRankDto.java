package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class PropertyParamRankDto implements Serializable {

    private static final long serialVersionUID = 7455506050084510828L;
    /**
     * 集团属性
     */
    @ApiModelProperty(value = "集团属性,1集团,2连锁",allowableValues = "[1,2]")
    private Integer rank;
    /**
     * 属性str
     */
    @ApiModelProperty(value = "属性字符串")
    private String property;

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }
}
