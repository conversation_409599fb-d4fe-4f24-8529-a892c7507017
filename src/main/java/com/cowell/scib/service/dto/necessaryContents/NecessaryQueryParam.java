package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NecessaryQueryParam extends AmisPageParam implements Serializable {

    @ApiModelProperty(value = "平台orgId")
    private List<Long> platformOrgId;

    @ApiModelProperty(value = "公司orgId")
    private List<Long> companyOrgId;

    @ApiModelProperty(value = "门店orgId")
    private List<Long> storeOrgId;

    @ApiModelProperty(value = "查询类型(1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)")
    private Byte queryTag;

    @ApiModelProperty(value = "必备标识(0非必备 1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)")
    private Byte necessaryTag;

    @ApiModelProperty(value = "mdm更新状态 0 待更新 1已更新")
    private Byte mdmUpdateStatus;

    @ApiModelProperty(value = "大类")
    private Long categoryId;

    @ApiModelProperty(value = "中类")
    private Long middleCategoryId;

    @ApiModelProperty(value = "小类")
    private Long smallCategoryId;

    @ApiModelProperty(value = "子类")
    private Long subCategoryId;

    @ApiModelProperty(value = "商品编码多个,分割")
    private String goodsNos;

    private List<String> goodsNoList;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "成分")
    private String composition;

    @ApiModelProperty(value = "店型")
    private List<String> storeType;

    //查询用-当前用户有权限的平台id集合
    private List<Long> queryPlatformOrgIdList;

}
