package com.cowell.scib.service.dto.TrackResult;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ImportResult implements Serializable {


    @ApiModelProperty(value = "状态码: 9999:处理中,0:成功 1:有失败文件")
    private String code;
    @ApiModelProperty(value = "描述")
    private String message;

    @ApiModelProperty(value = "uuid作为下载失败文件的key,如果有则可下载失败文件")
    private String result;

    private String time;

    private String failFileUrl;

    private Object data;

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this);
    }

    public static ImportResult buildDefaultImportResult(String result){
        ImportResult importResult = new ImportResult();
        importResult.setResult(result);
        importResult.setCode("0");
        importResult.setMessage("成功");
        return importResult;
    }
}
