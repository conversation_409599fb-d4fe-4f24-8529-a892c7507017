package com.cowell.scib.service.dto.permssion;

import com.cowell.permission.vo.EmployeeOrgAndTitleVO;

import java.util.Date;
import java.util.List;

public class EmployeeDetailWithWxDTO {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String loginName;
    private String password;
    private String name;
    private String email;
    private String phone;
    private String qrCodeUrl;
    private String area;
    private String empCode;
    private String erpCode;
    private Long userId;
    private String outId;
    private String headUrl;
    private Date birthday;
    private Byte sex;
    private List<EmployeeOrgAndTitleVO> employeeOrgAndTitleVOS;
    private Byte status;
    private String extend;
    private Integer version;
    private Integer sort;
    private String wxId;
    private String wxConfigId;
    private String wxQrCode;

    public EmployeeDetailWithWxDTO() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoginName() {
        return this.loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getArea() {
        return this.area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getEmpCode() {
        return this.empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }

    public String getErpCode() {
        return this.erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getQrCodeUrl() {
        return this.qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOutId() {
        return this.outId;
    }

    public void setOutId(String outId) {
        this.outId = outId;
    }

    public String getHeadUrl() {
        return this.headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public Date getBirthday() {
        return this.birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Byte getSex() {
        return this.sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public List<EmployeeOrgAndTitleVO> getEmployeeOrgAndTitleVOS() {
        return this.employeeOrgAndTitleVOS;
    }

    public void setEmployeeOrgAndTitleVOS(List<EmployeeOrgAndTitleVO> employeeOrgAndTitleVOS) {
        this.employeeOrgAndTitleVOS = employeeOrgAndTitleVOS;
    }

    public Byte getStatus() {
        return this.status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getExtend() {
        return this.extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getWxConfigId() {
        return wxConfigId;
    }

    public void setWxConfigId(String wxConfigId) {
        this.wxConfigId = wxConfigId;
    }

    public String getWxId() {
        return wxId;
    }

    public void setWxId(String wxId) {
        this.wxId = wxId;
    }

    public String getWxQrCode() {
        return wxQrCode;
    }

    public void setWxQrCode(String wxQrCode) {
        this.wxQrCode = wxQrCode;
    }

    @Override
    public String toString() {
        return "EmployeeDetailWithWxDTO{" +
                "area='" + area + '\'' +
                ", id=" + id +
                ", loginName='" + loginName + '\'' +
                ", password='" + password + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", qrCodeUrl='" + qrCodeUrl + '\'' +
                ", empCode='" + empCode + '\'' +
                ", erpCode='" + erpCode + '\'' +
                ", userId=" + userId +
                ", outId='" + outId + '\'' +
                ", headUrl='" + headUrl + '\'' +
                ", birthday=" + birthday +
                ", sex=" + sex +
                ", employeeOrgAndTitleVOS=" + employeeOrgAndTitleVOS +
                ", status=" + status +
                ", extend='" + extend + '\'' +
                ", version=" + version +
                ", sort=" + sort +
                ", wxId='" + wxId + '\'' +
                ", wxConfigId='" + wxConfigId + '\'' +
                ", wxQrCode='" + wxQrCode + '\'' +
                '}';
    }
}
