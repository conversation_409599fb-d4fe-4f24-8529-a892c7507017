package com.cowell.scib.service.dto.skuadjust;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> 经营目录-门店SKU数配置调整生效表
 */
@Data
public class JymlStoreSkuLimitAdjustEffectDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 主键
     */
    private Long adjustId;

    /**
     * 区域平台id
     */
    @ApiModelProperty(value = "区域平台id")
    private Long platformOrgId;

    /**
     * 区域平台
     */
    @ApiModelProperty(value = "区域平台")
    private String platformName;

    /**
     * 项目公司id
     */
    @ApiModelProperty(value = "项目公司id")
    private Long companyOrgId;

    /**
     * businessId
     */
    @ApiModelProperty(value = "businessId")
    private Long businessId;

    /**
     * 项目公司code
     */
    @ApiModelProperty(value = "项目公司code")
    private String companyCode;

    /**
     * 项目公司
     */
    @ApiModelProperty(value = "项目公司")
    private String companyName;

    /**
     * 项目公司
     */
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 门店orgid
     */
    @ApiModelProperty(value = "门店orgid")
    private Long storeOrgId;

    /**
     * storeId
     */
    @ApiModelProperty(value = "storeId")
    private Long storeId;

    /**
     * 门店code
     */
    @ApiModelProperty(value = "门店code")
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 商品大类id
     */
    @ApiModelProperty(value = "商品大类id")
    private String category;

    /**
     * 商品大类
     */
    @ApiModelProperty(value = "商品大类")
    private String categoryName;

    /**
     * 商品中类id
     */
    @ApiModelProperty(value = "商品中类id")
    private String middleCategory;

    /**
     * 商品中类
     */
    @ApiModelProperty(value = "商品中类")
    private String middleCategoryName;

    /**
     * 商品小类id
     */
    @ApiModelProperty(value = "商品小类id")
    private String smallCategory;

    /**
     * 商品小类
     */
    @ApiModelProperty(value = "商品小类")
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    @ApiModelProperty(value = "商品子类id")
    private String subCategory;

    /**
     * 商品子类
     */
    @ApiModelProperty(value = "商品子类")
    private String subCategoryName;

    @ApiModelProperty(value = "商品管控类别")
    private String ctrlCategoryName;
    /**
     * 变更前店型编码
     */
    @ApiModelProperty(value = "变更前店型编码")
    private String oldStoreType;

    /**
     * 变更前组货店型
     */
    @ApiModelProperty(value = "变更前组货店型")
    private String oldStoreTypeName;

    /**
     * 店型编码
     */
    @ApiModelProperty(value = "店型编码")
    private String storeType;

    /**
     * 组货店型
     */
    @ApiModelProperty(value = "组货店型")
    private String storeTypeName;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdName;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updatedBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String gmtUpdate;

    /**
     * 变更前SKU配置数上限
     */
    @ApiModelProperty(value = "变更前SKU配置数上限")
    private Integer oldSkuMaxLimit;

    /**
     * 变更前SKU配置数下限
     */
    @ApiModelProperty(value = "变更前SKU配置数下限")
    private Integer oldSkuLowerLimit;

    @ApiModelProperty(value = "原标准上下限")
    private String oldSkuLimitDesc;

    /**
     * 变更后SKU配置数上限
     */
    @ApiModelProperty(value = "变更后SKU配置数上限")
    private Integer skuMaxLimit;

    /**
     * 变更后SKU配置数下限
     */
    @ApiModelProperty(value = "变更后SKU配置数下限")
    private Integer skuLowerLimit;

    @ApiModelProperty(value = "新标准上下限")
    private String skuLimitDesc;

    public static LinkedHashMap<String, String> getExportMap() {
        LinkedHashMap<String, String> fieldMap = Maps.newLinkedHashMap();
        fieldMap.put("storeCode", "门店编码");
        fieldMap.put("storeName", "门店名称");
        fieldMap.put("ctrlCategoryName", "商品管控类别");
        fieldMap.put("oldStoreTypeName", "原标准（店型）");
        fieldMap.put("oldSkuLimitDesc", "原标准上下限");
        fieldMap.put("storeTypeName", "新标准（店型）");
        fieldMap.put("skuLimitDesc", "新标准上下限");
        fieldMap.put("createdName", "提出人");
        fieldMap.put("gmtCreate", "生效时间");
        fieldMap.put("platformName", "区域平台");
        fieldMap.put("province", "省份");
        fieldMap.put("companyName", "项目公司");
        fieldMap.put("city", "城市");
        return fieldMap;
    }

}
