package com.cowell.scib.service.dto.rule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 参数组织机构配置表
 */
@Data
public class ConfigOrgDBStyleDTO implements Serializable {
    /**
     * 主键
     */
    @JSONField(name = "id")
    private Long id;

    /**
     * 配置类型 1.推荐/2.必备/3.淘汰规则
     */
    @JSONField(name = "config_type")
    private Byte config_type;

    /**
     * orgId
     */
    @JSONField(name = "org_id")
    private Long org_id;

    /**
     * 机构名称
     */
    @JSONField(name = "org_name")
    private String org_name;

    /**
     * outId
     */
    @JSONField(name = "out_id")
    private Long out_id;

    /**
     * 公司MDM编码
     */
    @JSONField(name = "sap_code")
    private String sap_code;

    /**
     * 状态(-1删除，0正常)
     */
    @JSONField(name = "status")
    private Byte status;

    /**
     * 创建时间
     */
    @JSONField(name = "gmt_create")
    private Date gmt_create;

    /**
     * 更新时间
     */
    @JSONField(name = "gmt_update")
    private Date gmt_update;

    /**
     * 扩展字段
     */
    @JSONField(name = "extend")
    private String extend;

    /**
     * 版本号
     */
    @JSONField(name = "version")
    private Integer version;

    /**
     * 创建人ID
     */
    @JSONField(name = "created_by")
    private Long created_by;

    /**
     * 创建人
     */
    @JSONField(name = "created_name")
    private String created_name;

    /**
     * 更新人ID
     */
    @JSONField(name = "updated_by")
    private Long updated_by;

    /**
     * 更新人
     */
    @JSONField(name = "updated_name")
    private String updated_name;

}
