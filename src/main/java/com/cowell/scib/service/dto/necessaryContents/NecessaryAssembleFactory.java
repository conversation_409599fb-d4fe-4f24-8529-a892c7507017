package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.enums.NecessaryTagEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.TokenUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class NecessaryAssembleFactory {

    @Autowired
    private NecessaryGroupAssemble necessaryGroupAssemble;
    @Autowired
    private NecessaryPlatformAssemble necessaryPlatformAssemble;
    @Autowired
    private NecessaryCompanyAssemble necessaryCompanyAssemble;
    @Autowired
    private NecessaryStoreTypeAssemble necessaryStoreTypeAssemble;
    @Autowired
    private NecessaryStoreChooseTypeAssemble necessaryStoreChooseTypeAssemble;
    @Autowired
    private NecessarySingleStoreAssemble necessarySingleStoreAssemble;

    public NecessaryAssemble getNecessaryAssemble(NecessaryAddParam param, TokenUserDTO userDTO, OrgInfoBaseCache platformOrg){
        NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(param.getNecessaryTag())).orElseThrow(() -> new AmisBadRequestException("未知的必备目录类型"));
        switch (tagEnum) {
            case GROUP_NECESSARY: return necessaryGroupAssemble.setProp(param, userDTO, platformOrg);
            case PLATFORM_NECESSARY: return necessaryPlatformAssemble.setProp(param, userDTO, platformOrg);
            case COMPANY_NECESSARY: return necessaryCompanyAssemble.setProp(param, userDTO, platformOrg);
            case STORE_TYPE_NECESSARY: return necessaryStoreTypeAssemble.setProp(param, userDTO, platformOrg);
            case SINGLE_STORE_NECESSARY: return necessarySingleStoreAssemble.setProp(param, userDTO, platformOrg);
            default: throw new AmisBadRequestException("非法的必备目录类型");
        }
    }

    public NecessaryAssemble getDelNecessaryAssemble(NecessaryDelParam param, TokenUserDTO userDTO){
        NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(param.getNecessaryTag())).orElseThrow(() -> new AmisBadRequestException("未知的必备目录类型"));
        switch (tagEnum) {
            case GROUP_NECESSARY: return necessaryGroupAssemble.setDelProp(param, userDTO);
            case PLATFORM_NECESSARY: return necessaryPlatformAssemble.setDelProp(param, userDTO);
            case COMPANY_NECESSARY: return necessaryCompanyAssemble.setDelProp(param, userDTO);
            case STORE_TYPE_NECESSARY: return necessaryStoreTypeAssemble.setDelProp(param, userDTO);
            case STORE_CHOOSE_NECESSARY: return necessaryStoreChooseTypeAssemble.setDelProp(param, userDTO);
            case SINGLE_STORE_NECESSARY: return necessarySingleStoreAssemble.setDelProp(param, userDTO);
            default: throw new AmisBadRequestException("非法的必备目录类型");
        }
    }

}
