package com.cowell.scib.service.dto.manageContents;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> 经营目录-sku数配置上线管理
 */
@Data
public class ManageJymlSkuMaxLimitParam implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * SKU配置数上限(为空不管控 0是能有 >0:是管控数量)
     */
    private Integer skuMaxLimit;
    /**
     * SKU配置数下限(为空不管控 0是能有 >0:是管控数量)
     */
    private Integer skuLowerLimit;
    /**
     * 版本
     */
    private Long version;


    private static final long serialVersionUID = 1L;
}
