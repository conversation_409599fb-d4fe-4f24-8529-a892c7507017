package com.cowell.scib.service.dto.necessaryContents;

import com.google.common.collect.Maps;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.StringJoiner;

/**
 * <AUTHOR> mdm任务明细表
 */
public class MdmTaskDetailExportDTO extends MdmTaskDetailDTO implements Serializable {

    private Integer seq;

    private String taskSourceDesc;

    private String platformName;

    private String taskStatusDesc;

    private String necessaryTagDesc;

    private String pushStatusDesc;

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getTaskSourceDesc() {
        return taskSourceDesc;
    }

    public void setTaskSourceDesc(String taskSourceDesc) {
        this.taskSourceDesc = taskSourceDesc;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getTaskStatusDesc() {
        return taskStatusDesc;
    }

    public void setTaskStatusDesc(String taskStatusDesc) {
        this.taskStatusDesc = taskStatusDesc;
    }

    public String getNecessaryTagDesc() {
        return necessaryTagDesc;
    }

    public void setNecessaryTagDesc(String necessaryTagDesc) {
        this.necessaryTagDesc = necessaryTagDesc;
    }

    public String getPushStatusDesc() {
        return pushStatusDesc;
    }

    public void setPushStatusDesc(String pushStatusDesc) {
        this.pushStatusDesc = pushStatusDesc;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MdmTaskDetailExportDTO.class.getSimpleName() + "[", "]")
                .add("seq=" + seq)
                .add("taskSourceDesc='" + taskSourceDesc + "'")
                .add("platformName='" + platformName + "'")
                .add("taskStatusDesc='" + taskStatusDesc + "'")
                .add("necessaryTagDesc='" + necessaryTagDesc + "'")
                .add("pushStatusDesc='" + pushStatusDesc + "'")
                .toString();
    }

    public static LinkedHashMap<String, String> getFieldMap() {
        LinkedHashMap<String, String> fieldMap = Maps.newLinkedHashMap();
        fieldMap.put("seq", "序号");
        fieldMap.put("taskId", "更新任务ID");
        fieldMap.put("taskSourceDesc", "更新任务来源");
        fieldMap.put("platformName", "平台");
        fieldMap.put("gmtUpdate", "更新时间");
        fieldMap.put("taskStatusDesc", "任务状态");
        fieldMap.put("id", "行序号");
        fieldMap.put("companyName", "公司");
        fieldMap.put("storeCode", "门店编码");
        fieldMap.put("storeName", "门店名称");
        fieldMap.put("goodsNo", "商品编码");
        fieldMap.put("goodsName", "商品名称");
        fieldMap.put("specifications", "商品规格");
        fieldMap.put("manufacturer", "厂家");
        fieldMap.put("necessaryTagDesc", "配置类型");
        fieldMap.put("minDisplayQuantity", "最小陈列量");
        fieldMap.put("pushStatusDesc", "行更新状态");
        return fieldMap;
    }
}
