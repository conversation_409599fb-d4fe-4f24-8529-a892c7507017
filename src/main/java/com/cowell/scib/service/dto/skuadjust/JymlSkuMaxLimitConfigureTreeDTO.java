package com.cowell.scib.service.dto.skuadjust;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 经营目录-sku数配置上线管理
 */
@Data
public class JymlSkuMaxLimitConfigureTreeDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商品类别id
     */
    @ApiModelProperty(value = "商品类别id")
    private String category;

    /**
     * 商品类别
     */
    @ApiModelProperty(value = "商品类别")
    private String categoryName;

    @ApiModelProperty(value = "商品类别-包含父级")
    private String categoryComplete;

    @ApiModelProperty(value = "商品类别名称-包含父级")
    private String categoryCompleteName;

    /**
     * SKU配置数上限
     */
    @ApiModelProperty(value = "SKU配置数上限")
    private Integer skuMaxLimit;

    /**
     * SKU配置数下限
     */
    @ApiModelProperty(value = "SKU配置数下限")
    private Integer skuLowerLimit;

    @ApiModelProperty(value = "层级")
    private Integer level;

    /**
     * 店型编码
     */
    @ApiModelProperty(value = "店型编码")
    private String storeType;

    /**
     * 组货店型
     */
    @ApiModelProperty(value = "组货店型")
    private String storeTypeName;

    private List<JymlSkuMaxLimitConfigureTreeDTO> children = new ArrayList<>();
    public void addChild(JymlSkuMaxLimitConfigureTreeDTO child) {
        this.children.add(child);
    }
}
