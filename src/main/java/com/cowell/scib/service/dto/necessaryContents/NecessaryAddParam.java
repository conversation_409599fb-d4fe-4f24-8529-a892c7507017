package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.StringJoiner;

public class NecessaryAddParam {

    @ApiModelProperty(value = "平台orgId")
    private Long platformOrgId;

    @ApiModelProperty(value = "商品编码")
    private List<String> goodsNos;

    @ApiModelProperty(value = "是否忽略重复商品")
    private Boolean existsIgnore = false;

    @ApiModelProperty(value = "必备标识(1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)")
    private Byte necessaryTag;

    @ApiModelProperty(value = "店型")
    private List<String> storeTypes;

    @ApiModelProperty(value = "店型名称")
    private List<String> storeTypeNames;

    @ApiModelProperty(value = "公司orgId")
    private Long companyOrgId;

    @ApiModelProperty(value = "城市")
    private List<String> citys;

    @ApiModelProperty(value = "门店OrgId")
    private List<Long> storeOrgIds;

    @ApiModelProperty(value = "启用/停用id列表-集团必备启用停用使用")
    private List<Long> necessaryIds;

    @ApiModelProperty(value = "启用/停用标识-集团必备启用停用使用")
    private Boolean useMark;

    @ApiModelProperty(value = "后端使用-是否加商品锁")
    private Boolean goodsBlock;

    @ApiModelProperty(value = "后端使用-是否校验商品数量")
    private Boolean checkGoodsQty;

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public List<String> getGoodsNos() {
        return goodsNos;
    }

    public void setGoodsNos(List<String> goodsNos) {
        this.goodsNos = goodsNos;
    }

    public Boolean getExistsIgnore() {
        return existsIgnore;
    }

    public void setExistsIgnore(Boolean existsIgnore) {
        this.existsIgnore = existsIgnore;
    }

    public Byte getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(Byte necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public List<String> getStoreTypes() {
        return storeTypes;
    }

    public void setStoreTypes(List<String> storeTypes) {
        this.storeTypes = storeTypes;
    }

    public List<String> getStoreTypeNames() {
        return storeTypeNames;
    }

    public void setStoreTypeNames(List<String> storeTypeNames) {
        this.storeTypeNames = storeTypeNames;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public List<String> getCitys() {
        return citys;
    }

    public void setCitys(List<String> citys) {
        this.citys = citys;
    }

    public List<Long> getStoreOrgIds() {
        return storeOrgIds;
    }

    public void setStoreOrgIds(List<Long> storeOrgIds) {
        this.storeOrgIds = storeOrgIds;
    }

    public List<Long> getNecessaryIds() {
        return necessaryIds;
    }

    public void setNecessaryIds(List<Long> necessaryIds) {
        this.necessaryIds = necessaryIds;
    }

    public Boolean getUseMark() {
        return useMark;
    }

    public void setUseMark(Boolean useMark) {
        this.useMark = useMark;
    }

    public Boolean getGoodsBlock() {
        return goodsBlock;
    }

    public void setGoodsBlock(Boolean goodsBlock) {
        this.goodsBlock = goodsBlock;
    }

    public Boolean getCheckGoodsQty() {
        return checkGoodsQty;
    }

    public void setCheckGoodsQty(Boolean checkGoodsQty) {
        this.checkGoodsQty = checkGoodsQty;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", NecessaryAddParam.class.getSimpleName() + "[", "]")
                .add("platformOrgId=" + platformOrgId)
                .add("goodsNos=" + goodsNos)
                .add("existsIgnore=" + existsIgnore)
                .add("necessaryTag=" + necessaryTag)
                .add("storeTypes=" + storeTypes)
                .add("storeTypeNames='" + storeTypeNames + "'")
                .add("companyOrgId=" + companyOrgId)
                .add("citys=" + citys)
                .add("storeOrgIds=" + storeOrgIds)
                .add("necessaryIds=" + necessaryIds)
                .add("useMark=" + useMark)
                .add("goodsBlock=" + goodsBlock)
                .add("checkGoodsQty=" + checkGoodsQty)
                .toString();
    }
}
