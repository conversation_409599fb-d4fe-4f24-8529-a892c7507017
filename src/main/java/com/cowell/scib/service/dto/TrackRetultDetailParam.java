package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TrackRetultDetailParam {
    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "平台orgId")
    private String platOrgid;

    @ApiModelProperty(value = "企业orgid")
    private String compid;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "店型")
    private String reviseStoreGroup;

    @ApiModelProperty(value = "SKU")
    private String goodsId;

    @ApiModelProperty(value = "必备层级")
    private List<String> levelList;

    @ApiModelProperty(value = "门店MDM编码集合")
    private List<String> orgNoList;

    @ApiModelProperty(value = "状态")
    private Byte status;

    @ApiModelProperty(value = "条数")
    private Integer limit;

}
