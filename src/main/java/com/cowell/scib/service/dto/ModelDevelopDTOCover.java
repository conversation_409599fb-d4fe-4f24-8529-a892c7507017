package com.cowell.scib.service.dto;

import com.cowell.scib.entity.DevelopModule;
import com.cowell.scib.entity.DevelopModuleRecord;
import com.cowell.scib.entity.DevelopModuleRecordFile;
import com.cowell.scib.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/29 14:16
 */
public class ModelDevelopDTOCover {

    /**
     *
     * @param module
     * @return
     */
    public static DevelopModuleDTO moduleToDTO(DevelopModule module){
        DevelopModuleDTO moduleDTO = new DevelopModuleDTO();
        BeanUtils.copyProperties(module, moduleDTO);
        return moduleDTO;
    }


    /**
     * 发版记录明细对象转换
     * @param record
     * @return
     */
    public static DevelopModuleRecordDTO recordToDTO(DevelopModuleRecord record, List<DevelopModuleRecordFile> recordFileList){
        DevelopModuleRecordDTO recordDTO = new DevelopModuleRecordDTO();
        BeanUtils.copyProperties(record, recordDTO);
        recordDTO.setDevelopTime(DateUtils.conventDateStrByDate(record.getDevelopTime(), DateUtils.DATE_MINUTE_PATTERN));
        recordDTO.setGmtUpdate(DateUtils.conventDateStrByDate(record.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
        recordDTO.setImageUrlList(record.getImageUrls());
        recordDTO.setReachChannelList(record.getReachChannels());
        recordDTO.setReachGroupIdList(record.getReachGroupids());
        //文件
        if(CollectionUtils.isNotEmpty(recordFileList)){
            List<DevelopModuleRecordFileDTO> fileDTOList = recordFileList.stream().sorted(Comparator.comparing(DevelopModuleRecordFile::getGmtUpdate)).map(v->{
                return recordFileToDTO(v);
            }).collect(Collectors.toList());
            recordDTO.setFileList(fileDTOList);
        }
        return recordDTO;
    }

    /**
     * 记录文件对象转换
     * @param recordFile
     * @return
     */
    public static DevelopModuleRecordFileDTO recordFileToDTO(DevelopModuleRecordFile recordFile){
        DevelopModuleRecordFileDTO recordFileDTO = new DevelopModuleRecordFileDTO();
        BeanUtils.copyProperties(recordFile, recordFileDTO);
        return recordFileDTO;
    }

}
