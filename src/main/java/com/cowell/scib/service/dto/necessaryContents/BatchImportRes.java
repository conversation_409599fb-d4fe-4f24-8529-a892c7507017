package com.cowell.scib.service.dto.necessaryContents;

import java.io.Serializable;
import java.util.StringJoiner;

public class BatchImportRes implements Serializable {
    private static final long serialVersionUID = 2715026921398367192L;
    private String msg;

    public BatchImportRes() {
    }

    public BatchImportRes (String msg) {
        this.msg = msg;
    }
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BatchImportRes.class.getSimpleName() + "[", "]")
                .add("msg='" + msg + "'")
                .toString();
    }
}
