package com.cowell.scib.service.dto;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.StringJoiner;

/**
 * 通用商品DTO
 * <AUTHOR>
public class CommonUserDTO implements Serializable {

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    public CommonUserDTO() {
    }

    public CommonUserDTO(TokenUserDTO userDTO) {
        this.createdBy = userDTO.getUserId();
        this.createdName = userDTO.getName();
        this.updatedBy = userDTO.getUserId();
        this.updatedName = userDTO.getName();
    }

    public CommonUserDTO(Long userId, String name) {
        this.createdBy = userId;
        this.createdName = name;
        this.updatedBy = userId;
        this.updatedName = name;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CommonUserDTO.class.getSimpleName() + "[", "]")
                .add("createdBy=" + createdBy)
                .add("createdName='" + createdName + "'")
                .add("updatedBy=" + updatedBy)
                .add("updatedName='" + updatedName + "'")
                .toString();
    }
}
