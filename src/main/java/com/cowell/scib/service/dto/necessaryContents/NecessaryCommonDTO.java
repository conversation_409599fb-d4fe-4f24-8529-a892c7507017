package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.enums.NecessaryTagEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> 店型必备商品
 */
@Data
public class NecessaryCommonDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "集团必备标识 0非必备 1集团必备")
    private byte necessaryTag;

    @ApiModelProperty(value = "集团必备标识 0非必备 1集团必备")
    private String necessaryTagDesc;

    /**
     * 平台orgid
     */
    @ApiModelProperty(value = "平台orgid")
    private Long platformOrgId;

    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称")
    private String platformName;

    /**
     * 公司orgId
     */
    @ApiModelProperty(value = "公司orgId")
    private Long companyOrgId;

    /**
     * 连锁id
     */
    @ApiModelProperty(value = "连锁id")
    private Long businessid;

    /**
     * 公司MDM编码
     */
    @ApiModelProperty(value = "公司MDM编码")
    private String companyCode;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 门店orgId
     */
    @ApiModelProperty(value = "门店orgId")
    private Long storeOrgId;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    /**
     * 门店MDM编码
     */
    @ApiModelProperty(value = "门店MDM编码")
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 店型
     */
    @ApiModelProperty(value = "店型")
    private String storeType;

    /**
     * 店型描述
     */
    @ApiModelProperty(value = "店型描述")
    private String storeTypeDesc;

    /**
     * 大类id
     */
    @ApiModelProperty(value = "大类id")
    private Long categoryId;

    /**
     * 大类名称
     */
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 中类id
     */
    @ApiModelProperty(value = "中类id")
    private Long middleCategoryId;

    /**
     * 中类名称
     */
    @ApiModelProperty(value = "中类名称")
    private String middleCategoryName;

    /**
     * 小类编码
     */
    @ApiModelProperty(value = "小类编码")
    private Long smallCategoryId;

    /**
     * 小类名称
     */
    @ApiModelProperty(value = "小类名称")
    private String smallCategoryName;

    /**
     * 子类编码
     */
    @ApiModelProperty(value = "子类编码")
    private Long subCategoryId;

    /**
     * 子类名称
     */
    @ApiModelProperty(value = "子类名称")
    private String subCategoryName;

    /**
     * 成分
     */
    @ApiModelProperty(value = "成分")
    private String composition;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    /**
     * 条码
     */
    @ApiModelProperty(value = "条码")
    private String barCode;

    /**
     * 商品通用名
     */
    @ApiModelProperty(value = "商品通用名")
    private String goodsCommonName;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String goodsName;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String goodsUnit;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String description;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specifications;

    /**
     * 剂型
     */
    @ApiModelProperty(value = "剂型")
    private String dosageForm;

    /**
     * 生产厂家
     */
    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    /**
     * 批准文号
     */
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    /**
     * 采购属性
     */
    @ApiModelProperty(value = "采购属性")
    private String purchaseAttr;

    /**
     * 入选原因
     */
    @ApiModelProperty(value = "入选原因")
    private String chooseReason;

    @ApiModelProperty(value = "门店集中度")
    private BigDecimal storeFocusLevel;

    @ApiModelProperty(value = "门店动销率")
    private BigDecimal storeSalesRate;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date gmtUpdate;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreateStr;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String gmtUpdateStr;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdName;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updatedBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedName;


    public static LinkedHashMap<String, String> getCommonGoodsExportMap(NecessaryTagEnum tagEnum) {
        LinkedHashMap map = new LinkedHashMap();
        map.put("platformName", "区域平台");
        if (tagEnum.ordinal() >= NecessaryTagEnum.COMPANY_NECESSARY.ordinal()) {
            map.put("companyName", "企业");
            map.put("companyOrgId", "企业ID");
            map.put("city", "城市");
        }
        if (NecessaryTagEnum.PLATFORM_NECESSARY.equals(tagEnum)) {
            map.put("storeType", "平台必备店型");
            map.put("storeTypeDesc", "店型描述");
        }
        if (tagEnum.ordinal() > NecessaryTagEnum.COMPANY_NECESSARY.ordinal() && tagEnum.ordinal() < NecessaryTagEnum.SINGLE_STORE_NECESSARY.ordinal()) {
            map.put("storeType", "店型");
            map.put("storeTypeDesc", "店型描述");
        }
        if (NecessaryTagEnum.SINGLE_STORE_NECESSARY.equals(tagEnum)) {
            map.put("storeCode", "门店编码");
            map.put("companyOrgId", "企业ID");
//            map.put("storeTypeDesc", "店型描述");
            map.put("storeName", "门店名称");
        }
        map.put("goodsNo", "商品编码");
        map.put("goodsName", "商品名称");
        map.put("specifications", "规格");
        map.put("goodsUnit", "单位");
        map.put("manufacturer", "厂家简称");
        map.put("categoryName", "大类");
        map.put("middleCategoryName", "中类");
        map.put("smallCategoryName", "小类");
        map.put("subCategoryName", "子类");
        map.put("composition", "成分");
        if (NecessaryTagEnum.GROUP_NECESSARY.equals(tagEnum)) {
            map.put("purchaseAttr", "采购属性");
            map.put("necessaryTagDesc", "必备标识状态");
        } else {
            map.put("chooseReason", "入选原因");
        }
        if (tagEnum.ordinal() >= NecessaryTagEnum.COMPANY_NECESSARY.ordinal() && tagEnum.ordinal() < NecessaryTagEnum.SINGLE_STORE_NECESSARY.ordinal()) {
            map.put("storeFocusLevel", "门店集中度");
        }
        if (!NecessaryTagEnum.GROUP_NECESSARY.equals(tagEnum) && tagEnum.ordinal() < NecessaryTagEnum.SINGLE_STORE_NECESSARY.ordinal()) {
            map.put("storeSalesRate", "门店动销率");
        }
        map.put("updatedName", "最后操作人");
        map.put("gmtUpdateStr", "最后操作时间");
        return map;
    }
}
