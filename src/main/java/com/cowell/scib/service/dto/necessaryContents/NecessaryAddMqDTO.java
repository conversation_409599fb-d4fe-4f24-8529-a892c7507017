package com.cowell.scib.service.dto.necessaryContents;


import com.cowell.scib.service.dto.AuctionSpuBaseInfo;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.vo.SpuNewVo;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR> 店型必备商品
 */
public class NecessaryAddMqDTO implements Serializable {

    /**
     * 平台
     */
    private OrgInfoBaseCache platformOrg;

    /**
     * 必备级别
     */
    private Byte necessaryTag;

    /**
     * 商品
     */
    private NecessaryCommonGoodsDTO goods;

    /**
     * 门店id集合
     */
    private List<Long> storeIds;

    /**
     * 连锁商品
     */
    private Map<Long, SpuNewVo> bizGoodsMap;

    /**
     * 经营属性
     */
    private List<String> goodslineList;

    /**
     * 连锁商品的经营范围
     */
    private Map<Long, AuctionSpuBaseInfo> bizScopeMap;

    /**
     * 下发mdm任务Id
     */
    private Long mdmTaskId;
    /**
     * 用户信息
     */
    private TokenUserDTO userDTO;

    private String finishedKey;

    /**
     * 店型-平台必备用
     */
    private List<String> storeTypes;

    @ApiModelProperty(value = "后端使用-是否加商品锁")
    private Boolean goodsBlock;

    public OrgInfoBaseCache getPlatformOrg() {
        return platformOrg;
    }

    public void setPlatformOrg(OrgInfoBaseCache platformOrg) {
        this.platformOrg = platformOrg;
    }

    public Byte getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(Byte necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public NecessaryCommonGoodsDTO getGoods() {
        return goods;
    }

    public void setGoods(NecessaryCommonGoodsDTO goods) {
        this.goods = goods;
    }

    public List<Long> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<Long> storeIds) {
        this.storeIds = storeIds;
    }

    public Map<Long, SpuNewVo> getBizGoodsMap() {
        return bizGoodsMap;
    }

    public void setBizGoodsMap(Map<Long, SpuNewVo> bizGoodsMap) {
        this.bizGoodsMap = bizGoodsMap;
    }

    public List<String> getGoodslineList() {
        return goodslineList;
    }

    public void setGoodslineList(List<String> goodslineList) {
        this.goodslineList = goodslineList;
    }

    public Map<Long, AuctionSpuBaseInfo> getBizScopeMap() {
        return bizScopeMap;
    }

    public void setBizScopeMap(Map<Long, AuctionSpuBaseInfo> bizScopeMap) {
        this.bizScopeMap = bizScopeMap;
    }

    public Long getMdmTaskId() {
        return mdmTaskId;
    }

    public void setMdmTaskId(Long mdmTaskId) {
        this.mdmTaskId = mdmTaskId;
    }

    public TokenUserDTO getUserDTO() {
        return userDTO;
    }

    public void setUserDTO(TokenUserDTO userDTO) {
        this.userDTO = userDTO;
    }

    public String getFinishedKey() {
        return finishedKey;
    }

    public void setFinishedKey(String finishedKey) {
        this.finishedKey = finishedKey;
    }

    public List<String> getStoreTypes() {
        return storeTypes;
    }

    public void setStoreTypes(List<String> storeTypes) {
        this.storeTypes = storeTypes;
    }

    public Boolean getGoodsBlock() {
        return goodsBlock;
    }

    public void setGoodsBlock(Boolean goodsBlock) {
        this.goodsBlock = goodsBlock;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", NecessaryAddMqDTO.class.getSimpleName() + "[", "]")
                .add("platformOrg=" + platformOrg)
                .add("necessaryTag=" + necessaryTag)
                .add("goods='" + goods + "'")
                .add("storeIds=" + storeIds)
                .add("bizGoodsMap=" + bizGoodsMap)
                .add("goodslineList=" + goodslineList)
                .add("bizScopeMap=" + bizScopeMap)
                .add("mdmTaskId=" + mdmTaskId)
                .add("userDTO=" + userDTO)
                .add("finishedKey=" + finishedKey)
                .add("storeTypes=" + storeTypes)
                .add("goodsBlock=" + goodsBlock)
                .toString();
    }
}
