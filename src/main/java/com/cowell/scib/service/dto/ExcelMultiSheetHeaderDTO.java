package com.cowell.scib.service.dto;

import java.util.LinkedHashMap;

public class ExcelMultiSheetHeaderDTO {

    public ExcelMultiSheetHeaderDTO() {
    }

    public ExcelMultiSheetHeaderDTO(LinkedHashMap<String, String> fieldMap, Integer pageSize) {
        this.fieldMap = fieldMap;
        this.pageSize = pageSize;
    }

    /**
     * 表头映射
     */
    private LinkedHashMap<String, String> fieldMap;

    private Integer pageSize;

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public LinkedHashMap<String, String> getFieldMap() {
        return fieldMap;
    }

    public void setFieldMap(LinkedHashMap<String, String> fieldMap) {
        this.fieldMap = fieldMap;
    }

    @Override
    public String toString() {
        return "ExcelMultiSheetHeaderDTO{" +
            "fieldMap=" + fieldMap +
            ", pageSize=" + pageSize +
            '}';
    }
}
