package com.cowell.scib.service.dto.necessaryContents;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.framework.utils.IdUtils;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.mq.producer.NecessaryGoodsAddProducer;
import com.cowell.scib.rest.NecessaryContentsResource;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.impl.NecessaryContentsServiceImpl;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.utils.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public abstract class NecessaryAssemble<T> {

    final Logger logger = LoggerFactory.getLogger(NecessaryAssemble.class);

    @Autowired
    protected SearchService searchService;
    @Autowired
    protected ForestService forestService;
    @Autowired
    protected PermissionService permissionService;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    protected RuleService ruleService;
    @Autowired
    protected IscmService iscmService;
    @Autowired
    protected ConfigOrgExtendMapper configOrgExtendMapper;
    @Autowired
    protected ConfigOrgDetailExMapper configOrgDetailExMapper;
    @Autowired
    protected MdmTaskMapper mdmTaskMapper;
    @Autowired
    protected MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;
    @Autowired
    protected NecessaryGroupGoodsMapper necessaryGroupGoodsMapper;
    @Autowired
    protected NecessaryGroupGoodsExtendMapper necessaryGroupGoodsExtendMapper;
    @Autowired
    protected NecessaryPlatformGoodsMapper necessaryPlatformGoodsMapper;
    @Autowired
    protected NecessaryPlatformGoodsExtendMapper necessaryPlatformGoodsExtendMapper;
    @Autowired
    protected NecessaryCompanyGoodsMapper necessaryCompanyGoodsMapper;
    @Autowired
    protected NecessaryCompanyGoodsExtendMapper necessaryCompanyGoodsExtendMapper;
    @Autowired
    protected NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;
    @Autowired
    protected NecessaryStoreTypeGoodsExtendMapper necessaryStoreTypeGoodsExtendMapper;
    @Autowired
    protected NecessaryChooseStoreTypeGoodsMapper necessaryChooseStoreTypeGoodsMapper;
    @Autowired
    protected NecessaryChooseStoreTypeGoodsExtendMapper necessaryChooseStoreTypeGoodsExtendMapper;
    @Autowired
    protected NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;
    @Autowired
    protected NecessarySingleStoreGoodsExtendMapper necessarySingleStoreGoodsExtendMapper;
    @Autowired
    protected NecessaryContentsService necessaryContentsService;
    @Autowired
    protected StoreGoodsInfoMapper storeGoodsInfoMapper;
    @Autowired
    protected StoreGoodsInfoExtendMapper storeGoodsInfoExtendMapper;
    @Autowired
    @Qualifier("necessaryTaskExecutor")
    private AsyncTaskExecutor executor;
    @Autowired
    private NecessaryGoodsAddProducer producer;

    protected TokenUserDTO userDTO;

    protected NecessaryAddParam param;

    protected NecessaryDelParam delParam;

    protected OrgInfoBaseCache platformOrg;

    protected OrgInfoBaseCache companyOrg;

    protected NecessaryTagEnum tagEnum;

    protected Boolean goodsBlock;

    protected String message = "";

    public NecessaryAssemble setProp(NecessaryAddParam param, TokenUserDTO userDTO, OrgInfoBaseCache platformOrg) {
        this.param = param;
        this.userDTO = userDTO;
        this.platformOrg = platformOrg;
        this.goodsBlock = Boolean.TRUE.equals(param.getGoodsBlock());
        this.tagEnum = NecessaryTagEnum.getEnumByCode(param.getNecessaryTag());
        return this;
    }

    public NecessaryAssemble setDelProp(NecessaryDelParam delParam, TokenUserDTO userDTO) {
        this.delParam = delParam;
        this.userDTO = userDTO;
        return this;
    }

    public NecessaryAssemble setPropCompany(OrgInfoBaseCache companyOrg) {
        this.companyOrg = companyOrg;
        return this;
    }

    public String getMessage() {
        return message;
    }

    // 删除
    public abstract String deleteNecessary() throws Exception;

    // 校验权限
    public abstract NecessaryAssemble checkFullScope();

    // 组装自己特殊的地方
    public abstract NecessaryAssemble assemble() throws Exception;

    /**
     * 获取门店列表
     * @Param add : 是否新增
      */
    public abstract List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add);

    // 组装通用部分
    protected List<NecessaryCommonGoodsDTO> assembleCommon() throws Exception {
        Map<String, SpuListVo> spuMap = new HashMap<>();
        Map<Long, CommonCategoryDTO> categoryMap = new HashMap<>();
        Map<String, Map<String, String>> componentMap = new HashMap<>();
        List<Long> businessIds = permissionService.listChildOrgAssignedType(Lists.newArrayList(platformOrg.getId()), OrgTypeEnum.BUSINESS.getCode())
                .stream().findFirst().orElseThrow(() -> new AmisBadRequestException("查询权限异常,没有查询到平台下企业数据")).getChildren().stream().map(OrgDTO::getOutId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> grossprofitMap = new HashMap<>();
        Lists.partition(param.getGoodsNos(), Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
            spuMap.putAll(searchService.getSpuVOMap(v));
            categoryMap.putAll(getCategoryBySubIds(spuMap.values().stream().map(goo -> Long.valueOf(goo.getCategoryId())).distinct().collect(Collectors.toList())));
            componentMap.putAll(forestService.querySpuProperties(Lists.newArrayList("component"), v, null));
            businessIds.forEach(businessId -> {
                Map<String, Map<String, String>> grossprofit = forestService.querySpuProperties(Lists.newArrayList("grossprofit"), v, businessId);
                for (Map.Entry<String, Map<String, String>> entry : grossprofit.entrySet()) {
                    Map<String, String> value = entry.getValue();
                    if (MapUtils.isEmpty(value) || !value.containsKey("grossprofit")) {
                        continue;
                    }
                    PurchaseAttrEnum newEnum = PurchaseAttrEnum.getEnumByCode(value.get("grossprofit"));
                    if (Objects.isNull(newEnum)) {
                        continue;
                    }
                    if (!grossprofitMap.containsKey(entry.getKey())) {
                        grossprofitMap.put(entry.getKey(), newEnum.getCode());
                    } else {
                        PurchaseAttrEnum oldEnum = PurchaseAttrEnum.getEnumByCode(grossprofitMap.get(entry.getKey()));
                        if (oldEnum.ordinal() > newEnum.ordinal()) {
                            grossprofitMap.put(entry.getKey(), newEnum.getCode());
                        }
                    }
                }
            });
        });
        if (MapUtils.isEmpty(spuMap)) {
            throw new AmisBadRequestException("商品:" + spuMap.keySet().stream().collect(Collectors.joining("、")) + "信息有误,查不到对应商品");
        }
        List<String> failGoods = new ArrayList<>();
        List<NecessaryCommonGoodsDTO> commonGoodsList = new ArrayList<>();
        for(Map.Entry<String, SpuListVo> entry : spuMap.entrySet()){
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            CommonGoodsDTO commonGoodsDTO = searchService.getCommonGoods(entry.getValue());
            BeanUtils.copyProperties(commonGoodsDTO, goodsDTO);
            CommonUserDTO commonUserDTO = new CommonUserDTO(userDTO);
            BeanUtils.copyProperties(commonUserDTO, goodsDTO);
            CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(entry.getValue().getCategoryId()));
            if (Objects.isNull(commonCategoryDTO)) {
                logger.info("商品:{}没有查询到四级类目信息", entry.getKey());
                failGoods.add(entry.getKey());
                continue;
            }
            BeanUtils.copyProperties(commonCategoryDTO, goodsDTO);
            goodsDTO.setPlatformOrgId(platformOrg.getPlatformOrgId());
            goodsDTO.setPlatformName(platformOrg.getPlatformShortName());
            // 成分 标品属性 component
            Map<String, String> component = componentMap.get(entry.getKey());
            if (MapUtils.isNotEmpty(component)) {
                goodsDTO.setComposition(component.get("component"));
            }
            //采购属性 企业级 grossprofit
            goodsDTO.setPurchaseAttr(grossprofitMap.get(entry.getKey()));
            goodsDTO.setNecessaryTag(param.getNecessaryTag());
            commonGoodsList.add(goodsDTO);
        }
        if (CollectionUtils.isEmpty(commonGoodsList)) {
            throw new AmisBadRequestException("商品:" + failGoods.stream().collect(Collectors.joining("、")) + "没有查询到四级类目信息");
        }
        return commonGoodsList;
    }

    // 校验
    public String check() {
        List<String> existsGoodsNos = necessaryGroupGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), param.getGoodsNos(), param.getNecessaryTag());
        if (CollectionUtils.isNotEmpty(existsGoodsNos)) {
            if (!param.getExistsIgnore()) {
                return  existsGoodsNos.stream().collect(Collectors.joining("、")) + "，已在集团必备中存在，不可添加。";
            } else {
                param.setGoodsNos(param.getGoodsNos().stream().filter(v -> !existsGoodsNos.contains(v)).distinct().collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                    throw new AmisBadRequestException("排除重复后没有数据了");
                }
            }
        }
        return "";
    }

    /**
     * 添加一店一目逻辑
     * @param storeIds
     * @param goodsMap
     */
    public String addStoreGoods(List<Long> storeIds, Map<String, NecessaryCommonGoodsDTO> goodsMap){
        List<String> sucessGoodsNos = new ArrayList<>();
        String redisKey = NecessaryContentsService.NECESSARY_ADD_GOODS_CACHE_KEY + platformOrg.getId().toString() + "-";
        String finishedKey = NecessaryContentsService.NECESSARY_FINISHED_GOODS_CACHE_KEY + platformOrg.getId().toString() + DateUtils.conventDateStrByPattern(new Date(), DateUtils.FULL_TIME);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(finishedKey);
        List<String> hasProcessGoods = new ArrayList<>();
        MdmTask mdmTask = new MdmTask();
        BeanUtils.copyProperties(new CommonUserDTO(userDTO), mdmTask);
        mdmTask.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
        mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
        mdmTask.setDetailCount(0);
        mdmTaskMapper.insertSelective(mdmTask);
        try {
            List<String> goodsNos = Lists.newArrayList(goodsMap.keySet());
            Iterator<String> iterator = goodsNos.iterator();
            // 按商品锁
            if (goodsBlock) {
                while (iterator.hasNext()) {
                    String next = iterator.next();
                    RBucket<String> rBucket = redissonClient.getBucket(redisKey + next);
                    if (rBucket.isExists()) {
                        hasProcessGoods.add(next);
                        goodsMap.remove(next);
                        iterator.remove();
                    }
                    rBucket.set(next, 1L, TimeUnit.DAYS);
                }
            }
            if (CollectionUtils.isEmpty(goodsNos)) {
                throw new AmisBadRequestException("无符合条件的商品，不可新增必备。");
            }
            List<Long> businessIds = storeIds.stream().map(v -> CacheVar.getStoreByStoreId(v)).filter(Optional::isPresent).map(v -> v.get().getBusinessId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            // 企业级商品信息 key goodsNo value (key businessId)
            Map<String, Map<Long, SpuNewVo>> spuBusinessMap = new HashMap<>(businessIds.size());
            // 商品经营属性 key goodsNo value (key businessId)
            Map<String, Map<Long, AuctionSpuBaseInfo>> goodsBizScope = new HashMap<>(businessIds.size());
            // 查连锁级的商品
//        商品经营属性合法性校验：
//        查商品在门店所属法人公司（businessid），商品表企业级属性：经营属性=（核心必备、一般、新品）-》从参数BB_2/goodsline中取值，说明经营属性合法，可以打入一店一目
            businessIds.parallelStream().forEach(b -> {
                forestService.batchFindSpuProperty(goodsNos, b).forEach(goo -> {
                    Map<Long, AuctionSpuBaseInfo> goodsScope = Optional.ofNullable(goodsBizScope.get(goo.getGoodsNo())).orElse(new HashMap<>());
                    goodsScope.put(b, goo);
                    goodsBizScope.put(goo.getGoodsNo(), goodsScope);
                });
                SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
                spuNewParamVo.setBusinessId(b);
                spuNewParamVo.setGoodsNoList(goodsNos);
                searchService.getNewSpuMap(spuNewParamVo).forEach((goo, v) -> {
                    Map<Long, SpuNewVo> map = Optional.ofNullable(spuBusinessMap.get(goo)).orElse(new HashMap<>());
                    map.put(b, v);
                    spuBusinessMap.put(goo, map);
                });
            });
            // 取平台的经营属性
            RuleParam ruleParam = new RuleParam();
            ruleParam.setScopeCode("BB_2");
            Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
            Map<String, String> goodslineMap = ruleEnum.get("goodsline").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
            // 平台必备店型
            ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(platformOrg.getId(), ConfigTypeEnum.BB.getType(), null);
            List<String> goodslineList = new ArrayList<>();
            if (Objects.nonNull(configOrg)) {
                goodslineList.addAll(configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Lists.newArrayList("goodsline")).stream().map(v -> goodslineMap.get(v.getPerprotyValue())).distinct().collect(Collectors.toList()));
            }
            atomicLong.set(Long.valueOf(goodsNos.size()));
            atomicLong.expire(1L, TimeUnit.DAYS);
            goodsNos.parallelStream().forEach(goodsNo -> {
                NecessaryAddMqDTO addMqDTO = new NecessaryAddMqDTO();
                addMqDTO.setPlatformOrg(platformOrg);
                addMqDTO.setGoods(goodsMap.get(goodsNo));
                addMqDTO.setStoreIds(storeIds);
                addMqDTO.setBizGoodsMap(MapUtils.isEmpty(spuBusinessMap.get(goodsNo)) ? new HashMap<>() : spuBusinessMap.get(goodsNo));
                addMqDTO.setBizScopeMap(MapUtils.isEmpty(goodsBizScope.get(goodsNo)) ? new HashMap<>() : goodsBizScope.get(goodsNo));
                addMqDTO.setGoodslineList(CollectionUtils.isEmpty(goodslineList) ? Lists.newArrayList() : goodslineList);
                addMqDTO.setNecessaryTag(param.getNecessaryTag());
                addMqDTO.setMdmTaskId(mdmTask.getId());
                addMqDTO.setUserDTO(userDTO);
                addMqDTO.setFinishedKey(finishedKey);
                addMqDTO.setStoreTypes(param.getStoreTypes());
                addMqDTO.setGoodsBlock(goodsBlock);
//                try {//本地测试用
//                    necessaryContentsService.consumerAddNecessary(addMqDTO);
//                } catch (Exception e) {
//                    RBucket<String> bucket = redissonClient.getBucket(redisKey + goodsNo);
//                    atomicLong.decrementAndGet();
//                    bucket.delete();                }
                boolean send = producer.send(addMqDTO);
                if (!send) {
                    atomicLong.decrementAndGet();
                    if (goodsBlock) {
                        RBucket<String> bucket = redissonClient.getBucket(redisKey + goodsNo);
                        bucket.delete();
                    }
                } else {
                    sucessGoodsNos.add(goodsNo);
                }
            });
            return CollectionUtils.isNotEmpty(hasProcessGoods) ? "更新MDM任务创建成功，任务号：" + mdmTask.getId() + "，更新结果可去更新MDM任务管理模块查看(其中商品:" + hasProcessGoods.stream().collect(Collectors.joining(",")) + "在其他任务中处理,本次任务不处理这些商品)" : "更新MDM任务创建成功，任务号：" + mdmTask.getId() + "，更新结果可去更新MDM任务管理模块查看";
        } catch (Exception e) {
            logger.error("下发一店一目逻辑失败:", e);
                for (String goodsNo : goodsMap.keySet()) {
                    if (!sucessGoodsNos.contains(goodsNo)) {
                        atomicLong.decrementAndGet();
                        if (goodsBlock) {
                            RBucket rBucket = redissonClient.getBucket(redisKey + goodsNo);
                            rBucket.delete();
                        }
                    }
                }
                throw e;
        }
    }

    public String delStroeGoods(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap) throws Exception{
        List<String> sucessGoodsNos = new ArrayList<>();
        String redisKey;
        if (!Objects.isNull(platformOrg)) {
            redisKey = NecessaryContentsService.NECESSARY_ADD_GOODS_CACHE_KEY + platformOrg.getId().toString() + "-";
        }else {

            redisKey = NecessaryContentsService.NECESSARY_ADD_GOODS_CACHE_KEY + "-";
        }
        try {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
            if (Objects.isNull(userDTO)) {
                TokenUserDTO userDTO = new TokenUserDTO();
                userDTO.setUserId(Constants.SYS_USER_ID);
                userDTO.setName(Constants.SYS_USER_NAME);
                task.setTaskSource(MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                List<String> goodsList = delGoodsStoreMap.keySet().stream().map(NecessaryCommonGoodsDTO::getGoodsNo).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(goodsList)){
                    task.setRemarks(JSONObject.toJSONString(goodsList));
                }
            }else {
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            }
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(0);
            mdmTaskMapper.insertSelective(task);
            executor.execute(() -> {
                List<MdmTaskDetail> taskDetails = new ArrayList<>();
                for (Map.Entry<NecessaryCommonGoodsDTO, List<Long>> entry : delGoodsStoreMap.entrySet()) {
                    NecessaryCommonGoodsDTO dto = entry.getKey();
                    RBucket<String> rBucket = redissonClient.getBucket(redisKey + dto.getGoodsNo());
                    if (!rBucket.isExists()) {
                        rBucket.set(dto.getGoodsNo(), 1L, TimeUnit.DAYS);
                        Map<Integer, List<Long>> suffixMap = new HashMap<>();
                        entry.getValue().forEach(storeId -> {
                            int suffix = NecessaryContentsServiceImpl.getSuffixByStoreId(storeId);
                            List<Long> storeIds = Optional.ofNullable(suffixMap.get(suffix)).orElse(new ArrayList<>());
                            storeIds.add(storeId);
                            suffixMap.put(suffix, storeIds);
                        });
                        List<StoreGoodsInfo> storeGoodsInfos = new ArrayList<>();
                        suffixMap.forEach((k,v) -> {
                            StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                            if (Objects.isNull(delParam)){
                                example.createCriteria().andStoreIdIn(v).andGoodsNoEqualTo(dto.getGoodsNo()).andNecessaryTagEqualTo(dto.getNecessaryTag());
                            }else {
                                example.createCriteria().andStoreIdIn(v).andGoodsNoEqualTo(dto.getGoodsNo()).andNecessaryTagEqualTo(delParam.getNecessaryTag());
                            }
                            storeGoodsInfos.addAll(storeGoodsInfoMapper.selectByExample(example));
                        });
                        if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
                            List<Long> ids = necessaryContentsService.getStoreGoodsIds(storeGoodsInfos.size()  + 1);
                            List<Long> taskIds = necessaryContentsService.getMdmTaskDetailIds(storeGoodsInfos.size()  + 1);
                            // 先删后增
                            for (int i = 0; i < storeGoodsInfos.size(); i++) {
                                StoreGoodsInfo storeGoodsInfo = storeGoodsInfos.get(i);
                                Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(storeGoodsInfo.getStoreId());
                                if (Objects.isNull(userDTO)) {
                                    TokenUserDTO userDTO = new TokenUserDTO();
                                    userDTO.setUserId(Constants.SYS_USER_ID);
                                    userDTO.setName(Constants.SYS_USER_NAME);
                                    BeanUtils.copyProperties(new CommonUserDTO(userDTO), storeGoodsInfo);
                                } else {
                                    BeanUtils.copyProperties(new CommonUserDTO(userDTO), storeGoodsInfo);
                                }
                                storeGoodsInfo.setId(ids.get(i));
                                storeGoodsInfo.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
                                storeGoodsInfo.setMinDisplayQuantity(BigDecimal.ZERO);
                                MdmTaskDetail taskDetail = new MdmTaskDetail();
                                BeanUtils.copyProperties(dto, taskDetail);
                                taskDetail.setId(taskIds.get(i));
                                taskDetail.setNecessaryTag(Integer.valueOf(NecessaryTagEnum.NONE_NECESSARY.getCode()));
                                taskDetail.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
                                taskDetail.setStoreName(optional.isPresent() ? optional.get().getShortName() : "");
                                taskDetail.setStoreCode(storeGoodsInfo.getStoreCode());
                                taskDetail.setStoreId(storeGoodsInfo.getStoreId());
                                taskDetail.setStoreName(CacheVar.getStoreByStoreId(storeGoodsInfo.getStoreId()).orElse(new OrgInfoBaseCache()).getShortName());
                                taskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
                                taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
                                taskDetails.add(taskDetail);
                            }
                            Map<Integer, List<StoreGoodsInfo>> map = new HashMap<>();

                            storeGoodsInfos.forEach(v -> {
                                int suffix = NecessaryContentsServiceImpl.getSuffixByStoreId(v.getStoreId());
                                List<StoreGoodsInfo> list = Optional.ofNullable(map.get(suffix)).orElse(new ArrayList<>());
                                list.add(v);
                                map.put(suffix, list);
                            });
                            map.forEach((k,v) -> {
                                StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                                if (Objects.isNull(delParam)){
                                    example.createCriteria().andStoreIdIn(v.stream().map(StoreGoodsInfo::getStoreId).collect(Collectors.toList())).andGoodsNoEqualTo(dto.getGoodsNo()).andNecessaryTagEqualTo(dto.getNecessaryTag());
                                }else {
                                    example.createCriteria().andStoreIdIn(v.stream().map(StoreGoodsInfo::getStoreId).collect(Collectors.toList())).andGoodsNoEqualTo(dto.getGoodsNo()).andNecessaryTagEqualTo(delParam.getNecessaryTag());
                                }
                                storeGoodsInfoMapper.deleteByExample(example);
                                storeGoodsInfoExtendMapper.batchInsert(v);
                            });
//                        storeGoodsInfoMapper.deleteByExample(example);
//                        storeGoodsInfoExtendMapper.batchInsert(storeGoodsInfos);
                        }
                        rBucket.delete();
                    }
                    sucessGoodsNos.add(dto.getGoodsNo());
                }
                if (CollectionUtils.isNotEmpty(taskDetails)) {
                    task.setDetailCount(taskDetails.size());
                    task.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                    mdmTaskMapper.updateByPrimaryKeySelective(task);
                    taskDetails.forEach(v -> v.setTaskId(task.getId()));
                    List<List<MdmTaskDetail>> partition = Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE);
                    for(List<MdmTaskDetail> v : partition) {
                        mdmTaskDetailExtendMapper.batchInsert(v);
                        try {
                            necessaryContentsService.pushMdm(v);
                        } catch (Exception e) {
                            logger.warn("推送mdm失败", e);
                            v.forEach(taskDetail -> {
                                taskDetail.setPushStatus(MdmTaskPushStatusEnum.FAIL.getCode());
                                taskDetail.setExtend("推送mdm失败");
                            });
                            mdmTaskDetailExtendMapper.deleteByTaskId(task.getId());
                            mdmTaskDetailExtendMapper.batchInsert(v);
                        }
                    }
                }
            });
            return "更新MDM任务创建成功，任务号：" + task.getId() + "，更新结果可去更新MDM任务管理模块查看";
        } catch (Exception e) {
            logger.error("删除一店一目逻辑失败:", e);
            for (NecessaryCommonGoodsDTO goodsDTO : delGoodsStoreMap.keySet()) {
                if (!sucessGoodsNos.contains(goodsDTO.getGoodsNo())) {
                    RBucket<String> rBucket = redissonClient.getBucket(redisKey + goodsDTO.getGoodsNo());
                    rBucket.delete();
                }
            }
            throw e;
        }
    }

    /**
     * 根据子类id获取四级类目信息
     * @param subCategoryIds
     * @return
     */
    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        logger.info("subCategoryIds:{}", JSON.toJSONString(subCategoryIds));
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1,k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        return resultMap;
    }

    protected String platStoreType(String storeTypeCode) {
        return storeTypePlatTypeMapping().get(storeTypeCode);
    }
    protected List<String> storeTypeByPlat(String platStoreTypeCode) {
        return platTypeStoreTypeMapping().get(platStoreTypeCode);
    }

    protected Map<String, List<String>> platTypeStoreTypeMapping() {
        Map<String, List<String>> storeTypePlatTypeMapping = new HashMap<>();
        storeTypePlatTypeMapping.put("PT01", Lists.newArrayList("14", "24", "34"));
        storeTypePlatTypeMapping.put("PT05", Lists.newArrayList("10", "20", "30"));
        storeTypePlatTypeMapping.put("PT02", Lists.newArrayList("11", "21", "31"));
        storeTypePlatTypeMapping.put("PT03", Lists.newArrayList("12", "22", "32"));
        storeTypePlatTypeMapping.put("PT04", Lists.newArrayList("13", "23", "33"));
        return storeTypePlatTypeMapping;
    }

    protected Map<String, String> storeTypePlatTypeMapping(){
        Map<String, String> storeTypePlatTypeMapping = new HashMap<>();
        storeTypePlatTypeMapping.put("14", "PT01");
        storeTypePlatTypeMapping.put("24", "PT01");
        storeTypePlatTypeMapping.put("34", "PT01");
        storeTypePlatTypeMapping.put("10", "PT05");
        storeTypePlatTypeMapping.put("20", "PT05");
        storeTypePlatTypeMapping.put("30", "PT05");
        storeTypePlatTypeMapping.put("11", "PT02");
        storeTypePlatTypeMapping.put("21", "PT02");
        storeTypePlatTypeMapping.put("31", "PT02");
        storeTypePlatTypeMapping.put("12", "PT03");
        storeTypePlatTypeMapping.put("22", "PT03");
        storeTypePlatTypeMapping.put("32", "PT03");
        storeTypePlatTypeMapping.put("13", "PT04");
        storeTypePlatTypeMapping.put("23", "PT04");
        storeTypePlatTypeMapping.put("33", "PT04");
        return storeTypePlatTypeMapping;
    }

    List<Long> getCategoryByStoreTypes(MdmStoreExDTO storeEx){
        if (param.getStoreTypes().contains(storeEx.getStoreTypeCode()) || param.getStoreTypes().contains(storeEx.getPlatStoreTypeCode())) {
            return Constants.STORE_TYPE_CATEGORY;
        } else if (param.getStoreTypes().contains(storeEx.getZsStoreTypeCode())) {
            return Constants.ZS_STORE_TYPE_CATEGORY;
        } else if (param.getStoreTypes().contains(storeEx.getPfStoreTypeCode())) {
            return Constants.PF_STORE_TYPE_CATEGORY;
        } else {
            throw new AmisBadRequestException("没有找到店型对应的商品类型,请联系管理员");
        }
    }
}
