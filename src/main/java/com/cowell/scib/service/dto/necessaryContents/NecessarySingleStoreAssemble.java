package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.NecessarySingleStoreGoods;
import com.cowell.scib.entityDgms.NecessarySingleStoreGoodsExample;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.NecessarySingleStoreGoodsMapper;
import com.cowell.scib.mapperDgms.extend.NecessarySingleStoreGoodsExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NecessarySingleStoreAssemble extends NecessaryAssemble {

    @Autowired
    private NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;
    @Autowired
    private NecessarySingleStoreGoodsExtendMapper necessarySingleStoreGoodsExtendMapper;
    @Value("${scib.single.store.add.max:10}")
    private Integer addMax;

    @Override
    public String deleteNecessary() throws Exception{
        if (!necessaryContentsService.checkModifyAble(delParam.getPlatformOrgId())){
            throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
        }
//        1、单店必备表删除该门店*SKU行。
//        2、清空一店一目表这个门店*SKU的配置类型、最小陈列量
        NecessarySingleStoreGoodsExample example = new NecessarySingleStoreGoodsExample();
        example.createCriteria().andPlatformOrgIdEqualTo(delParam.getPlatformOrgId()).andIdIn(delParam.getNecessaryIds());
        List<NecessarySingleStoreGoods> necessaryGoodsList = necessarySingleStoreGoodsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryGoodsList)) {
            throw new AmisBadRequestException("所选单据已全部被删除");
        }
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        List<Long> companyOrgIds = necessaryGoodsList.stream().map(NecessarySingleStoreGoods::getCompanyOrgId).distinct().collect(Collectors.toList());
        // set 平台信息
        NecessaryAddParam param = new NecessaryAddParam();
        param.setNecessaryTag(delParam.getNecessaryTag());
        setProp(param, userDTO, CacheVar.getPlatformByOrgId(delParam.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
        checkFullScope();
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        companyOrgIds.forEach(c -> {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(c).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        Map<Long, MdmStoreExDTO> storeInfoMap = storeInfos.stream().collect(Collectors.toMap(MdmStoreExDTO::getStoreId, Function.identity(), (k1,k2) -> k1));
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        necessaryGoodsList.forEach((goo) -> {
            MdmStoreExDTO exDTO = storeInfoMap.get(goo.getStoreId());
            if (null != exDTO) {
                NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
                BeanUtils.copyProperties(goo, goodsDTO);
                List<Long> storeIdList = Optional.ofNullable(delGoodsStoreMap.get(goodsDTO)).orElse(new ArrayList<>());
                storeIdList.add(exDTO.getStoreId());
                delGoodsStoreMap.put(goodsDTO, storeIdList);
            }
        });
        necessarySingleStoreGoodsMapper.deleteByExample(example);
        return delStroeGoods(delGoodsStoreMap);
    }

    @Override
    public List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add) {
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        if (add) {
            storeInfos.addAll(necessaryContentsService.getPlatformStoreInfo(param.getPlatformOrgId(), Lists.newArrayList(companyOrg.getBusinessOrgId()), userDTO.getUserId()));
        } else {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(companyOrg.getId()).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("没有获取到企业:" + companyOrg.getShortName() + "下的门店");
        }
        if (!add) {
            return storeInfos;
        }
        List<ChildOrgsDTO> childOrgsDTOS = permissionService.listChildOrgAssignedType(Lists.newArrayList(param.getCompanyOrgId()), OrgTypeEnum.STORE.getCode());
        if (CollectionUtils.isEmpty(childOrgsDTOS)) {
            throw new AmisBadRequestException("没有获取到企业:" + companyOrg.getShortName() + "下的门店");
        }
        List<Long> children = childOrgsDTOS.get(0).getChildren().stream().filter(v -> param.getStoreOrgIds().contains(v.getId())).map(OrgDTO::getOutId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            throw new AmisBadRequestException("没有获取到企业:" + companyOrg.getShortName() + "下的门店");
        }
        return storeInfos.stream().filter(v -> children.contains(v.getStoreId())).collect(Collectors.toList());

    }

    @Override
    public NecessaryAssemble checkFullScope() {
        return this;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NecessaryAssemble assemble() throws Exception {
        OrgInfoBaseCache company = CacheVar.getBusinessByOrgId(param.getCompanyOrgId()).orElseThrow(() -> new AmisBadRequestException("没有查询到企业信息,请联系管理员"));
        setPropCompany(company);
        List<MdmStoreExDTO> mdmStroeExDTOS = getMdmStroeExDTOS(true);
        List<NecessaryCommonGoodsDTO> list = assembleCommon();
        List<NecessarySingleStoreGoods> singleStoreGoodsList = new ArrayList<>();
        List<OrgInfoBaseCache> stores = CacheVar.getStoreByStoreOrgIdList(param.getStoreOrgIds()).orElseThrow(() -> new AmisBadRequestException("没有查询到门店信息,请联系管理员"));
        List<NecessaryCommonGoodsDTO> addList = new ArrayList<>();
        for (NecessaryCommonGoodsDTO goodsDTO : list) {
            for (OrgInfoBaseCache store : stores) {
                Optional<MdmStoreExDTO> storeExt = CacheVar.getStoreExtInfoByStoreId(store.getOutId());
                if (!storeExt.isPresent()) {
                    continue;
                }
                NecessarySingleStoreGoods singleStoreGoods = new NecessarySingleStoreGoods();
                BeanUtils.copyProperties(goodsDTO, singleStoreGoods);
                singleStoreGoods.setCompanyOrgId(company.getBusinessOrgId());
                singleStoreGoods.setCompanyCode(company.getBusinessSapCode());
                singleStoreGoods.setCompanyName(company.getBusinessShortName());
                singleStoreGoods.setBusinessid(company.getBusinessId());
                singleStoreGoods.setCity(storeExt.get().getCity());
                singleStoreGoods.setStoreType("");
                singleStoreGoods.setStoreOrgId(store.getId());
                singleStoreGoods.setStoreId(store.getOutId());
                singleStoreGoods.setStoreCode(store.getSapCode());
                singleStoreGoods.setStoreName(store.getShortName());
                singleStoreGoodsList.add(singleStoreGoods);
            }
            addList.add(goodsDTO);
        }

        List<Long> storeIds = mdmStroeExDTOS.stream().map(MdmStoreExDTO::getStoreId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new AmisBadRequestException("无符合条件的门店，不可新增必备。");
        }
        // 添加
        necessarySingleStoreGoodsExtendMapper.batchInsert(singleStoreGoodsList);
        message = addStoreGoods(storeIds, addList.stream().collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
        return this;

    }

    @Override
    public String check() {
        if (null == param.getCompanyOrgId()) {
            throw new AmisBadRequestException("请选择企业");
        }
        if (CollectionUtils.isEmpty(param.getStoreOrgIds())) {
            throw new AmisBadRequestException("请选择门店");
        }
        if (param.getStoreOrgIds().size() > addMax) {
            throw new AmisBadRequestException("门店数不能大于:"+addMax);
        }
        Optional<List<OrgInfoBaseCache>> optional = CacheVar.getStoreByStoreOrgIdList(param.getStoreOrgIds());
        if (!optional.isPresent()) {
            throw new AmisBadRequestException("选择的门店不存在");
        }
        String checkMsg = super.check();
        StringBuilder message = new StringBuilder();
        if (StringUtils.isNotBlank(checkMsg)) {
            message.append(checkMsg);
        }
        //单店必备表查重：平台、门店、SKU
        List<String> singleGoodsExists = necessarySingleStoreGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), param.getStoreOrgIds(), param.getGoodsNos());
        if (CollectionUtils.isNotEmpty(singleGoodsExists)) {
            message.append(singleGoodsExists.stream().distinct().collect(Collectors.joining("、")) + "，已在单店必备中存在，不可添加。");
        }

        // 单店查重 只需判断一店一目里是否存在
        List<String> existsGoods = storeGoodsInfoExtendMapper.selectExistsGoods(optional.get().stream().map(OrgInfoBaseCache::getOutId).filter(Objects::nonNull).collect(Collectors.toList()), param.getGoodsNos());
        if (CollectionUtils.isNotEmpty(existsGoods)) {
            message.append(existsGoods.stream().distinct().collect(Collectors.joining("、")) + "，已存在，不可添加。");
        }
        if (message.length() > 0) {
            if (!param.getExistsIgnore()) {
                message.append("是否排除掉重复商品，继续添加？");
                return message.toString();
            } else {
                existsGoods.addAll(singleGoodsExists);
                param.setGoodsNos(param.getGoodsNos().stream().filter(v -> !existsGoods.contains(v)).distinct().collect(Collectors.toList()));
                if (org.apache.commons.collections.CollectionUtils.isEmpty(param.getGoodsNos())) {
                    throw new AmisBadRequestException("排除重复后没有数据了");
                }
            }
        }
        return "";
    }
}
