package com.cowell.scib.service.dto;


import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @Description; 经营范围数据
 * @date 2018-10-13 15:03
 */
public class MdmStoreLicenseData {

    private String code;


    /**
     * 实体编码 BP或门店的编码
     */
    private String entityno;

    /**
     * 档案编号
     */
    private String archiveno;

    private Long update_time;

    /**
     * 实体类型 01 BP;02 门店
     */
    private String entitytype;

    private String mdm_data_operation_status;

    /**
     * 原实体编码 原系统BP或门店编码
     */
    private String olderentityno;

    /**
     * 实体名称 BP名称或门店名称
     */
    private String entityname;


    private String mdm_company_code;

    /**
     * 从《经营范围列表》选择
     */
    private List<License> busiscopes;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEntityno() {
        return entityno;
    }

    public void setEntityno(String entityno) {
        this.entityno = entityno;
    }

    public String getArchiveno() {
        return archiveno;
    }

    public void setArchiveno(String archiveno) {
        this.archiveno = archiveno;
    }

    public Long getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Long update_time) {
        this.update_time = update_time;
    }

    public String getEntitytype() {
        return entitytype;
    }

    public void setEntitytype(String entitytype) {
        this.entitytype = entitytype;
    }

    public String getMdm_data_operation_status() {
        return mdm_data_operation_status;
    }

    public void setMdm_data_operation_status(String mdm_data_operation_status) {
        this.mdm_data_operation_status = mdm_data_operation_status;
    }

    public String getOlderentityno() {
        return olderentityno;
    }

    public void setOlderentityno(String olderentityno) {
        this.olderentityno = olderentityno;
    }

    public String getEntityname() {
        return entityname;
    }

    public void setEntityname(String entityname) {
        this.entityname = entityname;
    }

    public String getMdm_company_code() {
        return mdm_company_code;
    }

    public void setMdm_company_code(String mdm_company_code) {
        this.mdm_company_code = mdm_company_code;
    }

    public List<License> getBusiscopes() {
        return busiscopes;
    }

    public void setBusiscopes(List<License> busiscopes) {
        this.busiscopes = busiscopes;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static class License {

        private String busiscope;

        public String getBusiscope() {
            return busiscope;
        }

        public void setBusiscope(String busiscope) {
            this.busiscope = busiscope;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }
}
