package com.cowell.scib.service.dto.singleStoreSuggest;

import lombok.Data;

import java.util.List;

/**
 * 功    能：提成规则详情
 * 作    者：郜玉皓
 * 时    间：2023-03-06
 */
@Data
public class AwardInfoDTO {

    /**
     * 商品类型：200（单品）201（多品）
     */
    private Integer promotionType;

    /**
     * 门店系数
     */
    private String coefficients;

    /**
     * 规则ID
     */
    private Long promotionId;

    /**
     * 规则名称
     */
    private String promotionName;


    /**
     * 规则门店系数名称
     */
    private String storeCoefficientName;

    /**
     * 奖励方式 按金额；按比例
     */
    private String promotionWay;

    /**
     * 奖励：金额/积分
     */
    private List<AwardRuleDTO> awardRuleList;

}
