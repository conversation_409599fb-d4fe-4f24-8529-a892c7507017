package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.enums.ScibCommonEnums;
import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 经营目录commonDto
 */
@Data
public class ManageBatchUpdateParam implements AmisDataInInterface, Serializable {

    @ApiModelProperty(value = "indexes")
    private  List<String> indexes;

    @ApiModelProperty(value = "rowsDiff")
    private List<ManageCommonDTO> rowsDiff;
    @ApiModelProperty(value = "rows")
    private List<ManageCommonDTO> rows;

    @ApiModelProperty(value = "unModifiedItems")
    private List<ManageCommonDTO> unModifiedItems;

    private boolean autoConfirm = false;

    /**
     * @see com.cowell.scib.enums.ScibCommonEnums.ManageBatchUpdateActionEnum
     */
    @ApiModelProperty(value = "操作类型")
    private Integer action = ScibCommonEnums.ManageBatchUpdateActionEnum.JYML_BATCH_UPDATE_CONFIRM_SUBMIT.getCode();

}
