package com.cowell.scib.service.dto;

import com.cowell.scib.entityDgms.ConfigOrgDetailExtend;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13 16:39
 */
@Data
public class BundlTaskDetailDTO implements Serializable {
    private static final long serialVersionUID = -339027083040789685L;

    @ApiModelProperty("任务Id")
    private Long taskId;

    @ApiModelProperty("任务编码")
    private Long taskCode;

    @ApiModelProperty("任务明细Id")
    private Long taskDetailId;

    @ApiModelProperty("属性类型 1.obj/2.checkBox/3.collection")
    private Byte perprotyType;

    @ApiModelProperty("字典编码")
    private String dictCode;

    @ApiModelProperty("字典名称")
    private String dictName;

    @ApiModelProperty("参数值")
    private String perprotyValue;

    @ApiModelProperty("是否可编辑 true：否 false：是")
    private Boolean disAble;

    @ApiModelProperty("返回复选框")
    private List<String> perprotyValueList=new ArrayList<>();

    @ApiModelProperty("返回黑白名单门店")
    private List<Long> orgIdList=new ArrayList<>();

    @ApiModelProperty("组货商品黑名单")
    private List<ConfigOrgDetailExtend> goodsCodeList=new ArrayList<>();

    @ApiModelProperty("组货公司")
    private List<Long> companyOrgIdList=new ArrayList<>();
}
