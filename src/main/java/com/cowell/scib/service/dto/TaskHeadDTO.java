package com.cowell.scib.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/15 13:34
 */
@Data
public class TaskHeadDTO implements Serializable {
    private static final long serialVersionUID = 2977338296244763598L;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 组货类型
     */
    private String taskType;

    /**
     * 平台ID
     */
    private Long plateOrgId;

    /**
     * 组货公司
     */
    private String bundlComapny="";

    /**
     * 组货商品类型
     */
    private String bundlGoodsType="";

    /**
     * 组货门店类型
     */
    private String bundlStoreType="";

    /**
     * 中参店型
     */
    private String zsStoreType="";

    /**
     * 环境标识 300-测试  600-预发  800-线上
     */
    private Integer environment;

    /**
     * 版本号
     */
    private String version;
}
