package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.ManageStatusEnum;
import com.cowell.scib.enums.MdmStatusTaskEnum;
import com.cowell.scib.enums.MdmTaskPushStatusEnum;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.extend.StoreGoodsContentsExtendMapper;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
// 页面门店经营目录管理 - 选配改不经营
@Component
public class ManageContents2NonAssemble extends StoreContentsAssemble{
    private final Logger logger = LoggerFactory.getLogger(ManageContents2NonAssemble.class);
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    // 启用/停用
    @Override
    protected List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        logger.info("组装页面门店经营目录管理 - 选配改不经营");
        return storeGoodsContentDTOS;
    }

    protected void dealAndPush(List<StoreGoodsContentDTO> processes) {
        Map<Long, List<StoreGoodsContentDTO>> processMap = processes.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId));
        for (Map.Entry<Long, List<StoreGoodsContentDTO>> entry : processMap.entrySet()) {
            StoreGoodsContentsExample example = new StoreGoodsContentsExample();
            example.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList()));
            Map<String, StoreGoodsContents> existsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(StoreGoodsContents::getGoodsNo, Function.identity(), (k1, k2) -> k1));
            List<StoreGoodsContents> update = new ArrayList<>();
            for (StoreGoodsContentDTO contents : entry.getValue()) {
                StoreGoodsContents old = existsMap.get(contents.getGoodsNo());
                if (null != old) {
                    // （原始状态=经营-选配），改为不经营
//                    if (ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(old.getManageStatus())) {
//                        old.setManageStatus(ManageStatusEnum.NON_MANAGE.getCode());
//                    }
                    old.setManageStatus(ManageStatusEnum.NON_MANAGE.getCode());
                    old.setForbidApply("是");
                    update.add(old);
                }
            }
            if (CollectionUtils.isNotEmpty(update)) {
                Long taskId = entry.getValue().get(0).getMdmTaskId();
                MdmTask task = mdmTaskMapper.selectByPrimaryKey(taskId);
                List<MdmTaskDetail> taskDetails = new ArrayList<>();
                StoreGoodsContentsExample updateExample = new StoreGoodsContentsExample();
                updateExample.createCriteria().andStoreIdEqualTo(update.get(0).getStoreId()).andIdIn(update.stream().map(StoreGoodsContents::getId).collect(Collectors.toList()));
                StoreGoodsContents contents = new StoreGoodsContents();
                contents.setManageStatus(ManageStatusEnum.NON_MANAGE.getCode());
                storeGoodsContentsExtendMapper.batchUpdate(update, entry.getKey());
                task.setDetailCount(update.size());
                task.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                mdmTaskMapper.updateByPrimaryKeySelective(task);
                taskDetails.addAll(update.stream().map(v -> genTaskDetail(v, task)).collect(Collectors.toList()));
                List<Long> taskDetailIds = getMdmTaskDetailIds(taskDetails.size());
                taskDetails.forEach(v -> v.setId(taskDetailIds.remove(0)));
                if (CollectionUtils.isNotEmpty(taskDetails)) {
                    pushMdmTask(taskDetails);
                }
            }
        }
    }

    private MdmTaskDetail genTaskDetail(StoreGoodsContents contents, MdmTask task){
        MdmTaskDetail taskDetail = new MdmTaskDetail();
        BeanUtils.copyProperties(contents, taskDetail);
        taskDetail.setTaskId(task.getId());
        taskDetail.setStoreName(CacheVar.getStoreByStoreId(contents.getStoreId()).orElse(new OrgInfoBaseCache()).getShortName());
        taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        return taskDetail;
    }

}
