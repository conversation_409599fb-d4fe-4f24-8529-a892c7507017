package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.StringJoiner;

public class FileDownloadTaskDTO {
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "平台类型 1-商家中心 2-智能管理平台")
    private Integer platformType;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件位置 1-cos服务器 2-本地共享盘")
    private Integer filePlace;

    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "状态 -1-删除 0-待处理 1-下载中 2-成功 3-失败")
    private Integer status;

    @ApiModelProperty(value = "下载请求url")
    private String requestUrl;

    @ApiModelProperty(value = "下载请求参数json")
    private String requestParam;

    @ApiModelProperty(value = "下载请求类型 1-get 2-post")
    private Integer requestType;

    @ApiModelProperty(value = "导出内容开始时间")
    private String startTime;

    @ApiModelProperty(value = "导出内容结束时间")
    private String endTime;

    @ApiModelProperty(value = "创建人")
    private Long createdBy;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPlatformType() {
        return platformType;
    }

    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getFilePlace() {
        return filePlace;
    }

    public void setFilePlace(Integer filePlace) {
        this.filePlace = filePlace;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestParam() {
        return requestParam;
    }

    public void setRequestParam(String requestParam) {
        this.requestParam = requestParam;
    }

    public Integer getRequestType() {
        return requestType;
    }

    public void setRequestType(Integer requestType) {
        this.requestType = requestType;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", FileDownloadTaskDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("name='" + name + "'")
                .add("platformType=" + platformType)
                .add("fileName='" + fileName + "'")
                .add("filePlace=" + filePlace)
                .add("fileUrl='" + fileUrl + "'")
                .add("reason='" + reason + "'")
                .add("status=" + status)
                .add("requestUrl='" + requestUrl + "'")
                .add("requestParam='" + requestParam + "'")
                .add("requestType=" + requestType)
                .add("startTime='" + startTime + "'")
                .add("endTime='" + endTime + "'")
                .add("createdBy=" + createdBy)
                .toString();
    }
}
