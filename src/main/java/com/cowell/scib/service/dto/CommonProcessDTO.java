package com.cowell.scib.service.dto;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

public class CommonProcessDTO<T> implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = -2630705432805765710L;
    private T t;
    /**
     * 是否处理完成
     */
    @ApiModelProperty(value = "是否处理完成")
    private Boolean processFinished = false;

    /**
     * 已处理条数
     */
    @ApiModelProperty(value = "总处理条数")
    private Integer processCount = 0;

    /**
     * 成功条数
     */
    @ApiModelProperty(value = "成功条数")
    private Integer passCount = 0;

    /**
     * 失败条数
     */
    @ApiModelProperty(value = "失败条数")
    private Integer errorCount = 0;

    public T getT() {
        return t;
    }

    public void setT(T t) {
        this.t = t;
    }

    public Boolean getProcessFinished() {
        return processFinished;
    }

    public void setProcessFinished(Boolean processFinished) {
        this.processFinished = processFinished;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public void setProcessCount(Integer processCount) {
        this.processCount = processCount;
    }

    public Integer getPassCount() {
        return passCount;
    }

    public void setPassCount(Integer passCount) {
        this.passCount = passCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CommonProcessDTO.class.getSimpleName() + "[", "]")
                .add("obj=" + t)
                .add("processFinished=" + processFinished)
                .add("processCount=" + processCount)
                .add("passCount=" + passCount)
                .add("errorCount=" + errorCount)
                .toString();
    }
}
