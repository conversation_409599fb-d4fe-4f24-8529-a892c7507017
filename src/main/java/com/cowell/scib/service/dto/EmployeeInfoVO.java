package com.cowell.scib.service.dto;

//import com.cowell.framework.utils.sensitive.annotation.Sensitive;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * create EmployeeVO
 *
 * <AUTHOR>
 * @date 2018/6/11 11:56
 * */
public class EmployeeInfoVO implements Serializable {

    private static final long serialVersionUID = -4745087935039584987L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号码
     */
//    @Sensitive(preHold = 3, afterHold = 4)
    private String phone;

    private String encryptPhone;

    /**
     * 员工所在组织机构名称
     */
    private String beLongOrgNames;

    /**
     * 员工所在组织机构名称
     */
    private String ids;

    /**
     * 用户userID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 职位名称
     */
    private String titles;

    /**
     * 头像链接
     */
    private String headUrl;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 性别
     */
    private Byte sex;

    /**
     * 员工编号
     */
    private String empCode;

    private String extend;
    /**
     * 状态(1删除，0-正常)
     */
    private Byte status;

    /**
     * 用户拥有的角色
     */
    private String roles;

    /**
     * 用户拥有管理员权限类型 0-没有管理员权限，1-超管，2-层级管理员
     */
    private Integer adminType;

    private String erpCode;

    /**
     * 唯一健
     */
    private String guidkey;
    /**
     * 身份证号
     */
    private String idcard;

    /**
     * 加密身份证号
     */
//    @Sensitive(afterHold = 4)
    private String encryptIdCard;
    /**
     * 是否在职在岗状态
     */
    private Integer ondutyStatus;
    /**
     * 工作地点
     */
    private String location;
    /**
     * 直接上级
     */
    private String directLeader;
    /**
     * 职位等级
     */
    private String jobLevel;
    /**
     * 入职时间
     */
    private Date entryDate;
    /**
     * 岗位编码
     */
    private String jobTitleCode;
    /**
     * mdm更新数据时间
     */
    private Long mdmUpdateTime;
    /**
     * SRM 员工的类型 1供应商2生产厂家
     */
    private Integer srmUserType;

    /**
     * SRM 员工的公司id列表 逗号分隔
     */
    private String srmUserCompany;

    private Long srmUserOrgId;
    /**
     * 员工企微id
     */
    private String wxId;
    /**
     * 企业微信联系我配置id
     */
    private String wxConfigId;

    /**
     * 企业微信联系我二维码
     */
    private String wxQrCode;
    private Integer wxCodeUpdateCount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl == null ? null : headUrl.trim();
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getBeLongOrgNames() {
        return beLongOrgNames;
    }

    public void setBeLongOrgNames(String beLongOrgNames) {
        this.beLongOrgNames = beLongOrgNames;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTitles() {
        return titles;
    }

    public void setTitles(String titles) {
        this.titles = titles;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }

    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getEmpCode() {
        return empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWxId() {
        return wxId;
    }

    public void setWxId(String wxId) {
        this.wxId = wxId;
    }

    public String getEncryptPhone() {
        return encryptPhone;
    }

    public void setEncryptPhone(String encryptPhone) {
        this.encryptPhone = encryptPhone;
    }

    public String getGuidkey() {
        return guidkey;
    }

    public void setGuidkey(String guidkey) {
        this.guidkey = guidkey;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getEncryptIdCard() {
        return encryptIdCard;
    }

    public void setEncryptIdCard(String encryptIdCard) {
        this.encryptIdCard = encryptIdCard;
    }

    public Integer getOndutyStatus() {
        return ondutyStatus;
    }

    public void setOndutyStatus(Integer ondutyStatus) {
        this.ondutyStatus = ondutyStatus;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDirectLeader() {
        return directLeader;
    }

    public void setDirectLeader(String directLeader) {
        this.directLeader = directLeader;
    }

    public String getJobLevel() {
        return jobLevel;
    }

    public void setJobLevel(String jobLevel) {
        this.jobLevel = jobLevel;
    }

    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    public String getJobTitleCode() {
        return jobTitleCode;
    }

    public void setJobTitleCode(String jobTitleCode) {
        this.jobTitleCode = jobTitleCode;
    }

    public Long getMdmUpdateTime() {
        return mdmUpdateTime;
    }

    public void setMdmUpdateTime(Long mdmUpdateTime) {
        this.mdmUpdateTime = mdmUpdateTime;
    }

    public Integer getSrmUserType() {
        return srmUserType;
    }

    public void setSrmUserType(Integer srmUserType) {
        this.srmUserType = srmUserType;
    }

    public String getSrmUserCompany() {
        return srmUserCompany;
    }

    public void setSrmUserCompany(String srmUserCompany) {
        this.srmUserCompany = srmUserCompany;
    }

    public Long getSrmUserOrgId() {
        return srmUserOrgId;
    }

    public void setSrmUserOrgId(Long srmUserOrgId) {
        this.srmUserOrgId = srmUserOrgId;
    }

    public String getWxConfigId() {
        return wxConfigId;
    }

    public void setWxConfigId(String wxConfigId) {
        this.wxConfigId = wxConfigId;
    }

    public String getWxQrCode() {
        return wxQrCode;
    }

    public void setWxQrCode(String wxQrCode) {
        this.wxQrCode = wxQrCode;
    }

    public Integer getWxCodeUpdateCount() {
        return wxCodeUpdateCount;
    }

    public void setWxCodeUpdateCount(Integer wxCodeUpdateCount) {
        this.wxCodeUpdateCount = wxCodeUpdateCount;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
