package com.cowell.scib.service.dto;

import java.io.Serializable;
import java.util.List;

public class MdmInnerRequestDTO implements Serializable {

    /**
     * 当前数据版本是否激活
     */
    private boolean active;

    /**
     * 激活描述
     */
    private String activedesc;

    /**
     * ⼤数据版本
     */
    private String dataVersion;

    /**
     * 数据总条数
     */
    private Integer totalNum;

    private List<MdmGoodsUpdateDTO> mdmGoodsUpdateDTOList;

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getActivedesc() {
        return activedesc;
    }

    public void setActivedesc(String activedesc) {
        this.activedesc = activedesc;
    }

    public String getDataVersion() {
        return dataVersion;
    }

    public void setDataVersion(String dataVersion) {
        this.dataVersion = dataVersion;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public List<MdmGoodsUpdateDTO> getMdmGoodsUpdateDTOList() {
        return mdmGoodsUpdateDTOList;
    }

    public void setMdmGoodsUpdateDTOList(List<MdmGoodsUpdateDTO> mdmGoodsUpdateDTOList) {
        this.mdmGoodsUpdateDTOList = mdmGoodsUpdateDTOList;
    }

    @Override
    public String toString() {
        return "MdmInnerRequestDTO{" +
                "active=" + active +
                ", activedesc='" + activedesc + '\'' +
                ", dataVersion='" + dataVersion + '\'' +
                ", totalNum=" + totalNum +
                ", mdmGoodsUpdateDTOList=" + mdmGoodsUpdateDTOList +
                '}';
    }
}
