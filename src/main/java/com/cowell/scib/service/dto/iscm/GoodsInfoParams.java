package com.cowell.scib.service.dto.iscm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询iscm配置返回商品请求参数
 * <AUTHOR>
 * @date 2024-07-25 19:05:46
 */
@Data
public class GoodsInfoParams implements Serializable {
    private static final long serialVersionUID = 7892983707110492382L;

    @ApiModelProperty(value = "分页")
    private int page;

    @ApiModelProperty(value = "页大小")
    private Long pageSize;

    @ApiModelProperty(value = "组织orgId 连锁级别")
    private Long orgId;

    @ApiModelProperty(value = "参数唯一值")
    private String paramUniqueMark;

    @ApiModelProperty(value = "参数范围 7 ")
    private int paramScope;


}
