package com.cowell.scib.service.dto.necessaryContents;


import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 定位必备层级
 */
public class NecessaryDirectionsDTO implements Serializable, AmisDataInInterface {

    @ApiModelProperty(value = "集团必备数量")
    private Long groupCount;

    @ApiModelProperty(value = "平台必备数量")
    private Long platformCount;

    @ApiModelProperty(value = "企业必备数量")
    private Long companyCount;

    @ApiModelProperty(value = "店型必备数量")
    private Long storeTypeCount;

    @ApiModelProperty(value = "店型选配数量")
    private Long storeChooseCount;

    @ApiModelProperty(value = "单店必备数量")
    private Long storeCount;

    public Long getGroupCount() {
        return groupCount;
    }

    public void setGroupCount(Long groupCount) {
        this.groupCount = groupCount;
    }

    public Long getPlatformCount() {
        return platformCount;
    }

    public void setPlatformCount(Long platformCount) {
        this.platformCount = platformCount;
    }

    public Long getCompanyCount() {
        return companyCount;
    }

    public void setCompanyCount(Long companyCount) {
        this.companyCount = companyCount;
    }

    public Long getStoreTypeCount() {
        return storeTypeCount;
    }

    public void setStoreTypeCount(Long storeTypeCount) {
        this.storeTypeCount = storeTypeCount;
    }

    public Long getStoreChooseCount() {
        return storeChooseCount;
    }

    public void setStoreChooseCount(Long storeChooseCount) {
        this.storeChooseCount = storeChooseCount;
    }

    public Long getStoreCount() {
        return storeCount;
    }

    public void setStoreCount(Long storeCount) {
        this.storeCount = storeCount;
    }

    @Override
    public String toString() {
        return "NecessaryDirectionsDTO{" +
                ", groupCount='" + groupCount + '\'' +
                ", platformCount='" + platformCount + '\'' +
                ", companyCount='" + companyCount + '\'' +
                ", storeTypeCount='" + storeTypeCount + '\'' +
                ", storeChooseCount='" + storeChooseCount + '\'' +
                ", storeCount='" + storeCount + '\'' +
                '}';
    }
}
