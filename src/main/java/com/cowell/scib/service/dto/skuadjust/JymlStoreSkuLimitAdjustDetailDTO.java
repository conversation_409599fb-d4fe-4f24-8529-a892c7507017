package com.cowell.scib.service.dto.skuadjust;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 经营目录-门店SKU数配置调整明细
 */
@Data
public class JymlStoreSkuLimitAdjustDetailDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 调整单ID
     */
    @ApiModelProperty(value = "调整单ID")
    private Long adjustId;

    /**
     * 调整单号
     */
    @ApiModelProperty(value = "调整单号")
    private String adjustCode;

    /**
     * 商品大类id
     */
    @ApiModelProperty(value = "商品大类id")
    private String category;

    /**
     * 商品大类
     */
    @ApiModelProperty(value = "商品大类")
    private String categoryName;

    /**
     * 商品中类id
     */
    @ApiModelProperty(value = "商品中类id")
    private String middleCategory;

    /**
     * 商品中类
     */
    @ApiModelProperty(value = "商品中类")
    private String middleCategoryName;

    /**
     * 商品小类id
     */
    @ApiModelProperty(value = "商品小类id")
    private String smallCategory;

    /**
     * 商品小类
     */
    @ApiModelProperty(value = "商品小类")
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    @ApiModelProperty(value = "商品子类id")
    private String subCategory;

    /**
     * 商品子类
     */
    @ApiModelProperty(value = "商品子类")
    private String subCategoryName;

    /**
     * 变更前店型编码
     */
    @ApiModelProperty(value = "变更前店型编码")
    private String oldStoreType;

    /**
     * 变更前组货店型
     */
    @ApiModelProperty(value = "变更前组货店型")
    private String oldStoreTypeName;

    /**
     * 变更前SKU配置数上限
     */
    @ApiModelProperty(value = "变更前SKU配置数上限")
    private Integer oldSkuMaxLimit;

    /**
     * 变更前SKU配置数下限
     */
    @ApiModelProperty(value = "变更前SKU配置数下限")
    private Integer oldSkuLowerLimit;

    /**
     * 店型编码
     */
    @ApiModelProperty(value = "店型编码")
    private String storeType;

    /**
     * 组货店型
     */
    @ApiModelProperty(value = "组货店型")
    private String storeTypeName;

    /**
     * SKU配置数上限
     */
    @ApiModelProperty(value = "SKU配置数上限")
    private Integer skuMaxLimit;

    /**
     * SKU配置数下限
     */
    @ApiModelProperty(value = "SKU配置数下限")
    private Integer skuLowerLimit;

    /**
     * 变更前sku数配置id
     */
    @ApiModelProperty(value = "变更前sku数配置id")
    private Long oldConfigureId;

    /**
     * sku数配置id
     */
    @ApiModelProperty(value = "sku数配置id")
    private Long configureId;

    @ApiModelProperty(value = "管控类别名称")
    private String ctrlCategoryName;

    @ApiModelProperty(value = "level")
    private Integer level;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtUpdate;

}
