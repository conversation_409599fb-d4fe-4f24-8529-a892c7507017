package com.cowell.scib.service.dto.manageContents;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> 经营目录-sku数配置上线管理
 */
@Data
public class JymlSkuMaxLimitConfigureDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 区域平台id
     */
    @JsonProperty("platform_org_id")
    private Long platformOrgId;

    /**
     * 区域平台
     */
    @JsonProperty("platform_name")
    private String platformName;

    /**
     * 项目公司id
     */
    @JsonProperty("business_org_id")
    private Long businessOrgId;

    /**
     * 项目公司
     */
    @JsonProperty("business_name")
    private String businessName;

    /**
     * 城市
     */
    private String city;

    /**
     * 店型编码
     */
    @JsonProperty("store_type")
    private String storeType;

    /**
     * 组货店型
     */
    @JsonProperty("store_type_name")
    private String storeTypeName;

    /**
     * 商品大类id
     */
    private String category;

    /**
     * 商品大类
     */
    @JsonProperty("category_name")
    private String categoryName;

    /**
     * 商品中类id
     */
    @JsonProperty("middle_category")
    private String middleCategory;

    /**
     * 商品中类
     */
    @JsonProperty("middle_category_name")
    private String middleCategoryName;

    /**
     * 商品小类id
     */
    @JsonProperty("small_category")
    private String smallCategory;

    /**
     * 商品小类
     */
    @JsonProperty("small_category_name")
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    @JsonProperty("sub_category")
    private String subCategory;

    /**
     * 商品子类
     */
    @JsonProperty("sub_category_name")
    private String subCategoryName;

    /**
     * SKU配置数上限
     */
    @JsonProperty("sku_max_limit")
    private Integer skuMaxLimit;

    /**
     * SKU配置数上限建议
     */
    @JsonProperty("sku_suggested_limit")
    private Integer skuSuggestedLimit;

    /**
     * SKU配置数下限
     */
    @JsonProperty("sku_lower_limit")
    private Integer skuLowerLimit;

    /**
     * SKU配置数下限建议
     */
    @JsonProperty("sku_suggested_lower_limit")
    private Integer skuSuggestedLowerLimit;

    /**
     * 涉及子类/成分个数
     */
    @JsonProperty("ingredient_count")
    private Integer ingredientCount;

    /**
     * 生效状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    @JsonProperty("gmt_create")
    private String gmtCreate;

    /**
     * 修改时间
     */
    @JsonProperty("gmt_update")
    private String gmtUpdate;

    /**
     * 创建人
     */
    @JsonProperty("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @JsonProperty("update_by")
    private String updateBy;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本
     */
    private Long version;
    private String versionStr;

    /**
     * 环境变量
     */
    private String env;

    /**
     * 创建人ID
     */
    @JsonProperty("create_by_id")
    private Long createById;

    /**
     * 更新人ID
     */
    @JsonProperty("update_by_id")
    private Long updateById;

    /**
     * 商品大类是rx/otc
     */
    @JsonProperty("rx_otc")
    private String rxOtc;

    private static final long serialVersionUID = 1L;

    public static LinkedHashMap<String, String> getExportMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap();
        map.put("id", "主键");
        map.put("platformOrgId", "区域平台id");
        map.put("platformName", "区域平台");
        map.put("businessOrgId", "项目公司id");
        map.put("businessName", "项目公司");
        map.put("city", "城市");
        map.put("storeType", "店型编码");
        map.put("storeTypeName", "组货店型");
        map.put("category", "商品大类id");
        map.put("categoryName", "商品大类");
        map.put("middleCategory", "商品中类id");
        map.put("middleCategoryName", "商品中类");
        map.put("smallCategory", "商品小类id");
        map.put("smallCategoryName", "商品小类");
        map.put("subCategory", "商品子类id");
        map.put("subCategoryName", "商品子类");
        map.put("skuMaxLimit", "SKU配置数上限");
        map.put("skuSuggestedLimit", "SKU配置数上限建议");
        map.put("skuLowerLimit", "SKU配置数下限");
        map.put("skuSuggestedLowerLimit", "SKU配置数下限建议");
        map.put("ingredientCount", "涉及子类/成分个数");
        map.put("status", "生效状态");
        map.put("gmtCreate", "创建时间");
        map.put("gmtUpdate", "修改时间");
        map.put("createBy", "创建人");
        map.put("updateBy", "更新人");
        map.put("extend", "扩展字段");
        map.put("versionStr", "版本");
        return map;

    }

}
