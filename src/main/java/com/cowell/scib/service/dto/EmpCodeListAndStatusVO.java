package com.cowell.scib.service.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * 用户userId集合VO
 *
 * <AUTHOR>
 * @date 2018/6/20 11:14
 * */
public class EmpCodeListAndStatusVO implements Serializable {

    private List<Integer> statusList;

    private List<String> empCodes;

    public List<Integer> getStatusList() {
        return statusList;
    }

    private List<String> phoneList;

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<String> getEmpCodes() {
        return empCodes;
    }

    public void setEmpCodes(List<String> empCodes) {
        this.empCodes = empCodes;
    }

    public List<String> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<String> phoneList) {
        this.phoneList = phoneList;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
