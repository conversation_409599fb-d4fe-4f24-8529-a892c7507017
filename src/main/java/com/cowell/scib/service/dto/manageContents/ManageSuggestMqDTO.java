package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManageSuggestMqDTO implements Serializable {

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "门店MDM编码")
    private String storeNo;

    @ApiModelProperty(value = "连锁Ids")
    private String businessIds;

}
