package com.cowell.scib.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class StoreGoodNecessaryTagDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 门店orgID
     */
    private Long storeOrgId;

    /**
     * 必备标识(0非必备 1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)
     */
    private Byte necessaryTag;

    /**
     * 是否有效(0 否 1 是)
     */
    private Byte effectStatus;
    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 更新人
     */
    private String updatedName;


    /**
     * 更新人ID
     */
    private Long updatedBy;
}
