package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail;
import com.cowell.scib.enums.ScibCommonEnums;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.cowell.scib.constant.Constants.INTEGER_ONE;
import static com.cowell.scib.enums.ScibCommonEnums.ManageConfirmStatusEnum.JYML_PROCESS_NEED_CONFIRM;

// 排序方法所在的类
public class ManageDataHelper {
    private static final Logger logger = LoggerFactory.getLogger(ManageDataHelper.class);
    public static List<ManageCommonDTO>  sortOptionDtoList(List<ManageCommonDTO> optionDtoList, ManageQueryParam param) {
        //logger.info("sortOptionDtoList optionDtoList={}",optionDtoList);
        if (CollectionUtils.isEmpty(optionDtoList)) {
            return Lists.newArrayList();
        }
        if (param == null || StringUtils.isBlank(param.getOrderBy())) {
            //logger.info("sortOptionDtoList before={}",optionDtoList);
            // 默认排序为:小类、子类、成分、系统建议(店级新品>经营-必备>建议经营-选配>建议不经营>必须不经营)
            Comparator<ManageCommonDTO> defaultComparator = Comparator
                .comparing(ManageCommonDTO::getSmallCategory, Comparator.nullsLast(String::compareTo))
                .thenComparing(ManageCommonDTO::getSubCategory, Comparator.nullsLast(String::compareTo))
                .thenComparing(ManageCommonDTO::getComponent, Comparator.nullsLast(String::compareTo))
                .thenComparing(ManageDataHelper::compareSuggestManageStatus);

            optionDtoList.sort(defaultComparator);
            //logger.info("sortOptionDtoList after={}",optionDtoList);
            return optionDtoList;
        }
        ScibCommonEnums.ManageOrderByEnum orderBy = ScibCommonEnums.ManageOrderByEnum.fromField(param.getOrderBy());
        if (orderBy != null) {
            Comparator<ManageCommonDTO> comparator = Comparator.comparing(orderBy.getGetter());
            if (StringUtils.isNotBlank(param.getOrderDir()) && "desc".equalsIgnoreCase(param.getOrderDir())) {
                comparator = comparator.reversed();
            }
            //logger.info("sortOptionDtoList before={}",optionDtoList);
            optionDtoList.sort(comparator);
            //logger.info("sortOptionDtoList after={}",optionDtoList);
        }
        return optionDtoList;
    }

    /**
     * 自定义系统建议状态比较器
     * 排序优先级: 店级新品 > 经营-必备 > 建议经营-选配 > 建议不经营 > 必须不经营
     */
    private static int compareSuggestManageStatus(ManageCommonDTO dto1, ManageCommonDTO dto2) {
        if (dto1.getSuggestManageStatus() == null && dto2.getSuggestManageStatus() == null) {
            return 0;
        }
        if (dto1.getSuggestManageStatus() == null) {
            return 1;
        }
        if (dto2.getSuggestManageStatus() == null) {
            return -1;
        }

        // 获取枚举值的优先级
        int priority1 = getSuggestManageStatusPriority(dto1.getSuggestManageStatus());
        int priority2 = getSuggestManageStatusPriority(dto2.getSuggestManageStatus());

        // 优先级数字越小，排序越靠前
        return Integer.compare(priority1, priority2);
    }

    /**
     * 获取系统建议状态的优先级
     * 店级新品(1) > 经营-必备(2) > 建议经营-选配(3) > 建议不经营(4) > 必须不经营(5)
     */
    private static int getSuggestManageStatusPriority(String status) {
        if (status == null) {
            return Integer.MAX_VALUE;
        }

        try {
            // 尝试将状态转换为整数
            int statusCode = Integer.parseInt(status);

            // 根据SuggestManageStatusEnum中的定义返回优先级
            switch (statusCode) {
                case 1: // 店级新品
                    return 1;
                case 2: // 经营-必备
                    return 2;
                case 3: // 建议经营-选配
                    return 3;
                case 4: // 建议不经营
                    return 4;
                case 5: // 必须不经营
                    return 5;
                case 99: // 无
                    return 99;
                default:
                    return Integer.MAX_VALUE;
            }
        } catch (NumberFormatException e) {
            // 如果状态不是数字，则根据状态名称判断
            if ("店级新品".equals(status)) {
                return 1;
            } else if ("经营-必备".equals(status)) {
                return 2;
            } else if ("建议经营-选配".equals(status)) {
                return 3;
            } else if ("建议不经营".equals(status)) {
                return 4;
            } else if ("必须不经营".equals(status)) {
                return 5;
            } else if ("无".equals(status)) {
                return 99;
            } else {
                return Integer.MAX_VALUE;
            }
        }
    }

    public static List<ManageCommonDTO> filterAndSortOptionDtoList(List<ManageCommonDTO> optionDtoList, ManageQueryParam param) {
        Predicate<ManageCommonDTO> predicate = dto -> true;

        if (StringUtils.isNotBlank(param.getGoodsNo())) {
            predicate = predicate.and(dto -> dto.getGoodsNo().contains(param.getGoodsNo()));
        }

        if (StringUtils.isNotBlank(param.getGoodsName())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getGoodsName()) &&
                            dto.getGoodsName().toLowerCase().contains(param.getGoodsName().trim().toLowerCase())
            );
        }

        if (StringUtils.isNotBlank(param.getSuggestManageStatusName())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getSuggestManageStatusName()) &&
                            dto.getSuggestManageStatusName().contains(param.getSuggestManageStatusName())
            );
        }

        if (StringUtils.isNotBlank(param.getManageStatusName())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getManageStatusName()) &&
                            dto.getManageStatusName().contains(param.getManageStatusName())
            );
        }

        if (StringUtils.isNotBlank(param.getMyConfirmName())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getMyConfirmName()) &&
                            dto.getMyConfirmName().contains(param.getMyConfirmName())
            );
        }

        if (StringUtils.isNotBlank(param.getReviewResultName())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getReviewResultName()) &&
                            dto.getReviewResultName().contains(param.getReviewResultName())
            );
        }

        if (Boolean.TRUE.equals(param.getDiffRowsOnly())) {
            predicate = predicate.and(dto ->
                    !Objects.equals(dto.getSuggestManageStatus(),String.valueOf(dto.getMyConfirm()))
            );
        }

        if (StringUtils.isNotBlank(param.getJhiSpecification())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getJhiSpecification()) &&
                            dto.getJhiSpecification().toLowerCase().contains(param.getJhiSpecification().trim().toLowerCase())
            );
        }

        if (StringUtils.isNotBlank(param.getFactoryid())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getFactoryid()) &&
                            dto.getFactoryid().toLowerCase().contains(param.getFactoryid().trim().toLowerCase())
            );
        }

        if (StringUtils.isNotBlank(param.getComponent())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getComponent()) &&
                            dto.getComponent().toLowerCase().contains(param.getComponent().trim().toLowerCase())
            );
        }

        if (StringUtils.isNotBlank(param.getSmallCategory())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getSmallCategoryName()) &&
                            dto.getSmallCategoryName().toLowerCase().contains(param.getSmallCategory().trim().toLowerCase())
            );
        }

        if (StringUtils.isNotBlank(param.getSubCategory())) {
            predicate = predicate.and(dto ->
                    Objects.nonNull(dto.getSubCategoryName()) &&
                            dto.getSubCategoryName().toLowerCase().contains(param.getSubCategory().trim().toLowerCase())
            );
        }
        //logger.info("filterAndSortOptionDtoList before={}",optionDtoList);
        optionDtoList = optionDtoList.stream()
                .filter(predicate)
                .collect(Collectors.toList());
        //logger.info("filterAndSortOptionDtoList after={}",optionDtoList);
        return sortOptionDtoList(optionDtoList, param);
    }

    public static List<ManageCategoryTreeDTO> convertToTree(List<JymlStoreSkuSuggestProcessDetail> details, ManageQueryParam param) {
        // 存储每个层级的节点，使用组合键（大类id + 中类id 等）避免冲突
        Map<String, ManageCategoryTreeDTO> categoryMap =  new TreeMap<>();
        Map<String, ManageCategoryTreeDTO> middleCategoryMap = new TreeMap<>();
        Map<String, ManageCategoryTreeDTO> smallCategoryMap = new TreeMap<>();
        Map<String, ManageCategoryTreeDTO> subCategoryMap = new TreeMap<>();
        // 存储根节点
        Map<String, ManageCategoryTreeDTO> rootMap = new TreeMap<>();
        //是否过滤 待确认
        boolean uperLimitedFilter = false;
        if (Objects.nonNull(param.getQuickType())){
            uperLimitedFilter = true;
        }
        for (JymlStoreSkuSuggestProcessDetail detail : details) {
            // 处理大类
            String categoryId = detail.getCategory();
            String categoryName = detail.getCategoryName();
            ManageCategoryTreeDTO categoryNode = categoryMap.computeIfAbsent(categoryId, k -> {
                ManageCategoryTreeDTO node = new ManageCategoryTreeDTO(categoryId, categoryName);
                rootMap.put(categoryId, node);
                return node;
            });
            // 处理中类
            if(StringUtils.isNotBlank(detail.getMiddleCategory())){
                String middleCategoryId = detail.getMiddleCategory();
                String middleCategoryName = detail.getMiddleCategoryName();
                String middleCategoryKey = categoryId + "_" + middleCategoryId;
                ManageCategoryTreeDTO middleCategoryNode = middleCategoryMap.computeIfAbsent(middleCategoryKey, k -> {
                    ManageCategoryTreeDTO node = new ManageCategoryTreeDTO(middleCategoryId, middleCategoryName, 2, INTEGER_ONE.equals(detail.getUpperLimit()),detail.getConfirmed());
                    categoryNode.addChild(node);
                    categoryNode.setUpLimit(false);
                    categoryNode.setIcon("fa fa-exclamation-circle-x");
                    return node;
                });
                // 处理小类
                if(StringUtils.isNotBlank(detail.getSmallCategory())){
                    String smallCategoryId = detail.getSmallCategory();
                    String smallCategoryName = detail.getSmallCategoryName();
                    String smallCategoryKey = middleCategoryKey + "_" + smallCategoryId;
                    ManageCategoryTreeDTO smallCategoryNode = smallCategoryMap.computeIfAbsent(smallCategoryKey, k -> {
                        ManageCategoryTreeDTO node = new ManageCategoryTreeDTO(smallCategoryId, smallCategoryName, 3, INTEGER_ONE.equals(detail.getUpperLimit()),detail.getConfirmed());
                        middleCategoryNode.addChild(node);
                        middleCategoryNode.setUpLimit(false);
                        middleCategoryNode.setIcon("fa fa-exclamation-circle-x");
                        return node;
                    });
                    // 处理子类
                    if(StringUtils.isNotBlank(detail.getSubCategory())){
                        String subCategoryId = detail.getSubCategory();
                        String subCategoryName = detail.getSubCategoryName();
                        String subCategoryKey = smallCategoryKey + "_" + subCategoryId;
                        subCategoryMap.computeIfAbsent(subCategoryKey, k -> {
                            ManageCategoryTreeDTO node = new ManageCategoryTreeDTO(subCategoryId, subCategoryName, 4, INTEGER_ONE.equals(detail.getUpperLimit()),detail.getConfirmed());
                            smallCategoryNode.addChild(node);
                            smallCategoryNode.setUpLimit(false);
                            smallCategoryNode.setIcon("fa fa-exclamation-circle-x");
                            return node;
                        });
                    }
                }
            }
        }
        if (uperLimitedFilter){
            return new ArrayList<>(filterTree(rootMap,param).values());
        }
        return new ArrayList<>(rootMap.values());
    }

    public static Map<String, ManageCategoryTreeDTO> filterTree(Map<String, ManageCategoryTreeDTO> rootMap,ManageQueryParam param) {
        Integer processStatus;
        if (ScibCommonEnums.ManageTreeQuickTypeEnum.MANAGE_TREE_QUICK_TYPE_NEED_CONFIRMED.getCode().equals(param.getQuickType())){
            processStatus = 0;
        }else if (ScibCommonEnums.ManageTreeQuickTypeEnum.MANAGE_TREE_QUICK_TYPE_CONFIRMED.getCode().equals(param.getQuickType())){
            processStatus=1;
        }else if (ScibCommonEnums.ManageTreeQuickTypeEnum.MANAGE_TREE_QUICK_TYPE_SUBMIT_FOR_REVIEW.getCode().equals(param.getQuickType())){
            processStatus=-1;
        }else if (ScibCommonEnums.ManageTreeQuickTypeEnum.MANAGE_TREE_QUICK_TYPE_GOODS_FILTER.getCode().equals(param.getQuickType())){
            processStatus = 0;
        }else {
            processStatus = 0;
        }
        rootMap.entrySet().removeIf(entry -> {
            ManageCategoryTreeDTO filteredNode = filterNode(entry.getValue(),processStatus);
            if (filteredNode != null && CollectionUtils.isNotEmpty(filteredNode.getChildren())) {
                entry.setValue(filteredNode);
                return false;
            }
            return true;
        });
        logger.debug("filterTree rootMap={} ", rootMap);
        return rootMap;
    }

    private static ManageCategoryTreeDTO filterNode(ManageCategoryTreeDTO node, Integer processStatus) {
        if (node == null) {
            return null;
        }
        List<ManageCategoryTreeDTO> children = node.getChildren();
        if (CollectionUtils.isEmpty(children) && node.getUpLimit() && processStatus.equals(node.getProcessStatus())) {
            logger.debug("需要确认的节点  node={} ", node);
            return node;
        }
        List<ManageCategoryTreeDTO> filteredChildren = new ArrayList<>();
        for (ManageCategoryTreeDTO child : children) {
            ManageCategoryTreeDTO filteredChild = filterNode(child,processStatus);
            if (filteredChild != null) {
                filteredChildren.add(filteredChild);
            }
        }
        // 更新当前节点的子节点列表
        node.setChildren(filteredChildren);
        // 如果所有子节点都被过滤，并且当前节点也不需要确认，则移除该节点
        if (filteredChildren.isEmpty()) {
            logger.debug("filterNode remove node={} ", node);
            return null;
        }
        return node;
    }

    public static String getCategoryKeyByConfig(JymlSkuMaxLimitConfigure config) {
        return getCategoryKey(config.getCategory(), config.getMiddleCategory(), config.getSmallCategory(), config.getSubCategory());
    }

    public static String getCategoryKey(String category, String middleCategory, String smallCategory, String subCategory) {
        if (StringUtils.isNotBlank(subCategory)) {
            return category + "-" + middleCategory + "-" + smallCategory + "-" + subCategory;
        } else if (StringUtils.isNotBlank(smallCategory)) {
            return category + "-" + middleCategory + "-" + smallCategory;
        } else if (StringUtils.isNotBlank(middleCategory)) {
            return category + "-" + middleCategory;
        } else {
            return category;
        }
    }

    public static LocalDate findNextValidStartDate(String datesStr, LocalDate firstOpenDate, Integer days) {
        if(StringUtils.isBlank(datesStr)){
            datesStr="";
        }
        List<LocalDate> dates = Arrays.stream(datesStr.split(","))
                .filter(dateStr -> !dateStr.isEmpty())
                .map(LocalDate::parse)
                .collect(Collectors.toList());
        dates.add(firstOpenDate);
        LocalDate currentDate = LocalDate.now();
        for (LocalDate startDate : dates) {
            LocalDate endDate = startDate.plusDays(days);
            if (currentDate.isBefore(endDate) && currentDate.isAfter(startDate.minusDays(1))) {
                return startDate;
            }
        }
        List<LocalDate> futureDates = new ArrayList<>();
        for (LocalDate startDate : dates) {
            if (startDate.isAfter(currentDate)) {
                futureDates.add(startDate);
            }
        }

        if (!futureDates.isEmpty()) {
            futureDates.sort(LocalDate::compareTo);
            return futureDates.get(0);
        }
        return null;
    }
}