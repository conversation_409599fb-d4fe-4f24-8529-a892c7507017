package com.cowell.scib.service.dto;


import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

public class MdmRequestBodyDTO implements Serializable {
    /**
     * 消息发送时间
     * 格式：YYYY-MM-DD HH:mm:ss
     */
    private String bdatetime;


    /**
     * 业务双方确定的业务数据JSON结构
     * JSON格式。发送时传业务数据，接收时传处理结果
     */
    @NotNull
    private String bdata;
    /**
     * 数据类型
     */
    private Integer btype;
    /**
     * 发送数据的系统编号
     */
    private Integer bsource;
    /**
     * 接收数据的系统编号
     */
    private Integer bdestination;
    /**
     * 1- 已发送， 2 - 接受成功，3 - 发送失败， 4 -处理
     * 中， 5 - 处理成功， 6 - 处理失败
     */
    private Integer bstatus;

    /**
     * 请求唯一标识
     */
    private String bguid;

    /**
     * 业务查询使用的关键字段
     * JSON格式
     */
    private String bkeys;


    /**
     * 批量数据
     */
    private List<String> bdatas;

    /**
     * 批量数据
     */
    private List<String> bkeyss;

    /**
     * 内部请求参数
     */
    private MdmInnerRequestDTO mdmInnerRequestDTO;


    public String getBdata() {
        return bdata;
    }

    public void setBdata(String bdata) {
        this.bdata = bdata;
    }

    public String getBguid() {
        return bguid;
    }

    public void setBguid(String bguid) {
        this.bguid = bguid;
    }

    public List<String> getBdatas() {
        return bdatas;
    }

    public void setBdatas(List<String> bdatas) {
        this.bdatas = bdatas;
    }

    public List<String> getBkeyss() {
        return bkeyss;
    }

    public void setBkeyss(List<String> bkeyss) {
        this.bkeyss = bkeyss;
    }

    public String getBkeys() {
        return bkeys;
    }

    public void setBkeys(String bkeys) {
        this.bkeys = bkeys;
    }

    public String getBdatetime() {
        return bdatetime;
    }

    public void setBdatetime(String bdatetime) {
        this.bdatetime = bdatetime;
    }

    public Integer getBtype() {
        return btype;
    }

    public void setBtype(Integer btype) {
        this.btype = btype;
    }

    public Integer getBsource() {
        return bsource;
    }

    public void setBsource(Integer bsource) {
        this.bsource = bsource;
    }

    public Integer getBdestination() {
        return bdestination;
    }

    public void setBdestination(Integer bdestination) {
        this.bdestination = bdestination;
    }

    public Integer getBstatus() {
        return bstatus;
    }

    public void setBstatus(Integer bstatus) {
        this.bstatus = bstatus;
    }

    public MdmInnerRequestDTO getMdmInnerRequestDTO() {
        return mdmInnerRequestDTO;
    }

    public void setMdmInnerRequestDTO(MdmInnerRequestDTO mdmInnerRequestDTO) {
        this.mdmInnerRequestDTO = mdmInnerRequestDTO;
    }

    @Override
    public String toString() {
        return "MdmRequestBodyDTO{" +
                "bdatetime='" + bdatetime + '\'' +
                ", bdata='" + bdata + '\'' +
                ", btype=" + btype +
                ", bsource=" + bsource +
                ", bdestination=" + bdestination +
                ", bstatus=" + bstatus +
                ", bguid='" + bguid + '\'' +
                ", bkeys='" + bkeys + '\'' +
                ", bdatas=" + bdatas +
                ", bkeyss=" + bkeyss +
                ", mdmInnerRequestDTO=" + mdmInnerRequestDTO +
                '}';
    }
}