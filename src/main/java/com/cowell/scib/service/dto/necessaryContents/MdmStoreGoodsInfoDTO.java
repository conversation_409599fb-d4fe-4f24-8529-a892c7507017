package com.cowell.scib.service.dto.necessaryContents;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.StringJoiner;

public class MdmStoreGoodsInfoDTO {
    /**
     * 公司名称
     */
    @JSONField(name = "mdm_company_code")
    private String companyCode;

    /**
     * 门店编码
     */
    @JSONField(name = "storeno")
    private String storeCode;

    /**
     * 商品编码
     */
    @JSONField(name = "goodsno")
    private String goodsNo;

    /**
     * 数据ID
     */
    @JSONField(name = "auto_code")
    private String autoCode;

    /**
     * 最小陈列量
     */
    @JSONField(name = "minimumdisplay")
    private String minDisplayQuantity;

    /**
     * 配置类型
     */
    @JSO<PERSON><PERSON>(name = "configtype")
    private String necessaryTagDesc;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getAutoCode() {
        return autoCode;
    }

    public void setAutoCode(String autoCode) {
        this.autoCode = autoCode;
    }

    public String getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(String minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public String getNecessaryTagDesc() {
        return necessaryTagDesc;
    }

    public void setNecessaryTagDesc(String necessaryTagDesc) {
        this.necessaryTagDesc = necessaryTagDesc;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MdmStoreGoodsInfoDTO.class.getSimpleName() + "[", "]")
                .add("companyCode='" + companyCode + "'")
                .add("storeCode='" + storeCode + "'")
                .add("goodsNo='" + goodsNo + "'")
                .add("autoCode='" + autoCode + "'")
                .add("minDisplayQuantity='" + minDisplayQuantity + "'")
                .add("necessaryTagDesc='" + necessaryTagDesc + "'")
                .toString();
    }
}
