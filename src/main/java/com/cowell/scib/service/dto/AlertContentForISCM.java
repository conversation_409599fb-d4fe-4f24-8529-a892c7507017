package com.cowell.scib.service.dto;

import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023-03-24 11:06:37
 * @desc 技术中统一报警组件
 */
public class AlertContentForISCM {
    /**
     * 类型 EMAIL：邮箱，QYWX：企业微信，QYWX_GROUP：企业微信内部群
     */
    private String type;
    private String toUsers;
    private String subject;
    private String message;
    private Long id;
    private Long alramId;
    private String companyCode;
    private String errorStoreCodes;
    private String applyDate;
    private Date gmtCreate;
    private Date gmtUpdate;
    private Integer dealStatus;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getToUsers() {
        return toUsers;
    }

    public void setToUsers(String toUsers) {
        this.toUsers = toUsers;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAlramId() {
        return alramId;
    }

    public void setAlramId(Long alramId) {
        this.alramId = alramId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getErrorStoreCodes() {
        return errorStoreCodes;
    }

    public void setErrorStoreCodes(String errorStoreCodes) {
        this.errorStoreCodes = errorStoreCodes;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Integer getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(Integer dealStatus) {
        this.dealStatus = dealStatus;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", AlertContentForISCM.class.getSimpleName() + "[", "]")
                .add("type='" + type + "'")
                .add("toUsers='" + toUsers + "'")
                .add("subject='" + subject + "'")
                .add("message='" + message + "'")
                .add("id=" + id)
                .add("alramId=" + alramId)
                .add("companyCode='" + companyCode + "'")
                .add("errorStoreCodes='" + errorStoreCodes + "'")
                .add("applyDate='" + applyDate + "'")
                .add("gmtCreate=" + gmtCreate)
                .add("gmtUpdate=" + gmtUpdate)
                .add("dealStatus=" + dealStatus)
                .toString();
    }
}
