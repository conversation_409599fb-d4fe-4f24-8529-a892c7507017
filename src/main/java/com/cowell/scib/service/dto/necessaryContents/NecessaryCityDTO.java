package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.service.AmisDataInInterface;
import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NecessaryCityDTO implements AmisDataInInterface {

    @ApiModelProperty(value = "城市列表")
    private List<String> citys;

}
