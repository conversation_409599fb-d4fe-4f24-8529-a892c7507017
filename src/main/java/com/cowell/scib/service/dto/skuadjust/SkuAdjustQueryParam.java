package com.cowell.scib.service.dto.skuadjust;

import com.cowell.common.db.PageDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SkuAdjustQueryParam {
    @ApiModelProperty(value = "区域平台Id")
    private Long platformOrgId;
    @ApiModelProperty(value = "项目公司orgId")
    private Long companyOrgId;
    @ApiModelProperty(value = "门店orgId")
    private List<Long> storeOrgIds;
    @ApiModelProperty(value = "状态")
    private Integer adjustStatus;
    @ApiModelProperty(value = "管控类别")
    private String categoryId;
    @ApiModelProperty(value = "管控类别层级")
    private Integer level;
    @ApiModelProperty(value = "是否导出--前端不用传")
    private Boolean exportAble;
    @ApiModelProperty(value = "导出类型--1:调整记录 2:生效记录")
    private Integer exportType;
    private Integer page;
    private Integer pageSize;
}
