package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.entityDgms.MdmTask;
import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.entityDgms.StoreGoodsContentsExample;
import com.cowell.scib.enums.ManageStatusEnum;
import com.cowell.scib.enums.SuggestManageStatusEnum;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.extend.StoreGoodsContentsExtendMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BdpDailyGoodsExchangeContentsAssemble extends StoreContentsAssemble{
    private final Logger logger = LoggerFactory.getLogger(BdpDailyGoodsExchangeContentsAssemble.class);
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    // 启用/停用
    @Override
    protected List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        logger.info("组装大数据推送 - 月度经营目录建议处理到一店一目");
        return storeGoodsContentDTOS;
    }

    protected void dealAndPush(List<StoreGoodsContentDTO> processes) {
        Map<Long, List<StoreGoodsContentDTO>> processMap = processes.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId));
        for (Map.Entry<Long, List<StoreGoodsContentDTO>> entry : processMap.entrySet()) {
            StoreGoodsContentsExample example = new StoreGoodsContentsExample();
            example.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList()));
            Map<String, StoreGoodsContents> existsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(StoreGoodsContents::getGoodsNo, Function.identity(), (k1, k2) -> k1));
            List<StoreGoodsContents> update = new ArrayList<>();
            for (StoreGoodsContentDTO contents : entry.getValue()) {
                StoreGoodsContents old = existsMap.get(contents.getGoodsNo());
                Integer beforeSugggest = old.getSuggestManageStatus();
                Integer beforeManage = old.getManageStatus();
                // 新品到期转选配：大数据给门店、商品
                //dgms查门店商品，存在并且经营状态=店级新品，经营状态改为经营-选配；else 舍弃
                if (null != old && ManageStatusEnum.STORE_NEW_GOODS.getCode().equals(old.getManageStatus())) {
                    old.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                    old.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                    update.add(old);
                }
            }
            if (CollectionUtils.isNotEmpty(update)) {
                storeGoodsContentsExtendMapper.batchUpdate(update, entry.getKey());
            }
        }
    }

}
