package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component
public class NecessaryCompanyAssemble extends NecessaryAssemble {

    @Override
    public String deleteNecessary() throws Exception {
        if (!necessaryContentsService.checkModifyAble(delParam.getPlatformOrgId())){
            throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
        }
//        1、企业表删除该企业*城市*SKU行。
//        2、用企业*城市取门店列表，清空一店一目表这些门店*SKU的配置类型、最小陈列量
        NecessaryCompanyGoodsExample example = new NecessaryCompanyGoodsExample();
        example.createCriteria().andPlatformOrgIdEqualTo(delParam.getPlatformOrgId()).andIdIn(delParam.getNecessaryIds());
        List<NecessaryCompanyGoods> necessaryGoodsList = necessaryCompanyGoodsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryGoodsList)) {
            throw new AmisBadRequestException("所选单据已全部被删除");
        }
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        List<Long> companyOrgIds = necessaryGoodsList.stream().map(NecessaryCompanyGoods::getCompanyOrgId).distinct().collect(Collectors.toList());
        // set 平台信息
        NecessaryAddParam param = new NecessaryAddParam();
        param.setNecessaryTag(delParam.getNecessaryTag());
        setProp(param, userDTO, CacheVar.getPlatformByOrgId(delParam.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(companyOrgIds, userDTO.getUserId());
        // 按照 企业, 城市分组 并过滤没有权限的企业
        Map<String, List<NecessaryCompanyGoods>> necessaryGoodsMap = necessaryGoodsList.stream().filter(v -> orgDataScopeList.contains(v.getCompanyOrgId())).collect(Collectors.groupingBy(v -> v.getCompanyOrgId() + "-" + v.getCity()));
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        companyOrgIds.forEach(c -> {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(c).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        necessaryGoodsMap.forEach((key,v) -> {
            String[] split = StringUtils.split(key, "-");
            List<OrgInfoBaseCache> childOrgsDTOS = CacheVar.getStoreListByBusinessOrgId(Long.valueOf(split[0]));
            if (CollectionUtils.isNotEmpty(childOrgsDTOS)) {
                List<Long> storeIds = childOrgsDTOS.stream().filter(store -> null != store.getOutId()).map(store -> CacheVar.getStoreExtInfoByStoreId(store.getOutId()).orElse(null)).filter(store -> null != store && split[1].equals(store.getCity())).map(MdmStoreExDTO::getStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(storeIds)) {
                    v.forEach(goo -> {
                        NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
                        BeanUtils.copyProperties(goo, goodsDTO);
                        List<Long> storeIdList = Optional.ofNullable(delGoodsStoreMap.get(goodsDTO)).orElse(new ArrayList<>());
                        storeIdList.addAll(storeIds);
                        delGoodsStoreMap.put(goodsDTO, storeIdList);
                    });
                }
            }
        });
        necessaryCompanyGoodsMapper.deleteByExample(example);
        if (MapUtils.isEmpty(delGoodsStoreMap)) {
            return "无符合条件的门店，仅删除必备目录，不创建MDM任务。";
        }
        return delStroeGoods(delGoodsStoreMap);
    }

    @Override
    public NecessaryAssemble checkFullScope() {
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(Lists.newArrayList(param.getCompanyOrgId()), userDTO.getUserId());
        if (CollectionUtils.isEmpty(orgDataScopeList)) {
            throw new AmisBadRequestException("没有当前公司权限");
        }
        return this;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NecessaryAssemble assemble() throws Exception {
        OrgInfoBaseCache company = CacheVar.getBusinessByOrgId(param.getCompanyOrgId()).orElseThrow(() -> new AmisBadRequestException("没有查询到企业信息,请联系管理员"));
        setPropCompany(company);
        List<MdmStoreExDTO> mdmStroeExDTOS = getMdmStroeExDTOS(true);
        List<NecessaryCommonGoodsDTO> list = assembleCommon();
        List<NecessaryCompanyGoods> companyGoodsList = new ArrayList<>();
        List<NecessaryCommonGoodsDTO> addList = new ArrayList<>();
        boolean zsStoreTypeAble = false;
        boolean storeTypeAble = false;
        boolean pfStoreTypeAble = false;
        for (NecessaryCommonGoodsDTO goodsDTO : list) {
            for (String city : param.getCitys()) {
                NecessaryCompanyGoods companyGoods = new NecessaryCompanyGoods();
                BeanUtils.copyProperties(goodsDTO, companyGoods);
                companyGoods.setCompanyOrgId(company.getBusinessOrgId());
                companyGoods.setCompanyCode(company.getBusinessSapCode());
                companyGoods.setCompanyName(company.getBusinessShortName());
                companyGoods.setBusinessid(company.getBusinessId());
                companyGoods.setCity(city);
                if (Constants.ZS_STORE_TYPE_CATEGORY.contains(companyGoods.getMiddleCategoryId())) {
                    zsStoreTypeAble = true;
                }
                if (Constants.STORE_TYPE_CATEGORY.contains(companyGoods.getCategoryId())) {
                    storeTypeAble = true;
                }
                if (Constants.PF_STORE_TYPE_CATEGORY.contains(companyGoods.getMiddleCategoryId())) {
                    pfStoreTypeAble = true;
                }
                companyGoodsList.add(companyGoods);
            }
            addList.add(goodsDTO);
        }

        List<String> goodsNos = companyGoodsList.stream().map(NecessaryCompanyGoods::getGoodsNo).collect(Collectors.toList());
        List<Long> storeIds = mdmStroeExDTOS.stream().map(MdmStoreExDTO::getStoreId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new AmisBadRequestException("无符合条件的门店，不可新增必备。");
        }
        // 店型集合
        Set<String> goodsTypes = new HashSet<>();
        for (MdmStoreExDTO store :  mdmStroeExDTOS){
            if (zsStoreTypeAble && StringUtils.isNotBlank(store.getZsStoreTypeCode())) {
                goodsTypes.add(store.getZsStoreTypeCode());
            }
            if (storeTypeAble && StringUtils.isNotBlank(store.getStoreTypeCode())) {
                goodsTypes.add(store.getStoreTypeCode());
            }
            if (pfStoreTypeAble && StringUtils.isNotBlank(store.getPfStoreTypeCode())) {
                goodsTypes.add(store.getPfStoreTypeCode());
            }
        }
        log.debug("assemble|goodsTypes:{}.", goodsTypes);
//          ④查店型必备表：平台、管理主体、城市下店型（根据SKU商品大类确定取哪类店型）、SKU，存在则删除N行。
        NecessaryStoreTypeGoodsExample storeTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
        NecessaryStoreTypeGoodsExample.Criteria storeTypeCriteria = storeTypeGoodsExample.createCriteria();
        storeTypeCriteria.andPlatformOrgIdEqualTo(platformOrg.getId()).andCompanyOrgIdEqualTo(company.getId()).andGoodsNoIn(goodsNos);
        if(CollectionUtils.isNotEmpty(goodsTypes)){
            storeTypeCriteria.andStoreTypeIn(Lists.newArrayList(goodsTypes));
        }
        necessaryStoreTypeGoodsMapper.deleteByExample(storeTypeGoodsExample);
//        ⑤⑤查店型选配表：平台、管理主体、城市下店型（根据SKU商品大类确定取哪类店型）、SKU ，存在则删除N行。
        NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
        NecessaryChooseStoreTypeGoodsExample.Criteria chooseStoreTypeCriteria = chooseStoreTypeGoodsExample.createCriteria();
        chooseStoreTypeCriteria.andPlatformOrgIdEqualTo(platformOrg.getId()).andCompanyOrgIdEqualTo(company.getId()).andGoodsNoIn(goodsNos);
        if(CollectionUtils.isNotEmpty(goodsTypes)){
            chooseStoreTypeCriteria.andStoreTypeIn(Lists.newArrayList(goodsTypes));
        }
        necessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);

//        ⑥查单店必备表：平台、管理主体、城市下的门店list、SKU，存在则删除N行。
        NecessarySingleStoreGoodsExample singleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
        singleStoreGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getId()).andCompanyOrgIdEqualTo(company.getId()).andStoreIdIn(storeIds).andGoodsNoIn(goodsNos);
        necessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);

        // 添加
        necessaryCompanyGoodsExtendMapper.batchInsert(companyGoodsList);
        message = addStoreGoods(storeIds, addList.stream().collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
        return this;

    }

    @Override
    public List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add) {
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        if (add) {
            storeInfos.addAll(necessaryContentsService.getPlatformStoreInfo(param.getPlatformOrgId(), Lists.newArrayList(companyOrg.getBusinessOrgId()), userDTO.getUserId()));
        } else {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(companyOrg.getId()).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("没有获取到平台下配置的门店");
        }
        if (!add) {
            return storeInfos;
        }
        List<ChildOrgsDTO> childOrgsDTOS = permissionService.listChildOrgAssignedType(Lists.newArrayList(param.getCompanyOrgId()), OrgTypeEnum.STORE.getCode());
        if (CollectionUtils.isEmpty(childOrgsDTOS)) {
            throw new AmisBadRequestException("所选企业下没有查询到门店信息");
        }
        List<Long> children = childOrgsDTOS.get(0).getChildren().stream().map(OrgDTO::getOutId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            throw new AmisBadRequestException("所选企业下没有查询到门店信息");
        }
        storeInfos = storeInfos.stream().filter(v -> children.contains(v.getStoreId()) && param.getCitys().contains(v.getCity())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("所选城市下没有门店");
        }
        return storeInfos;
    }

    @Override
    public String check() {
        if (null == param.getCompanyOrgId()) {
            throw new AmisBadRequestException("请选择企业");
        }
        if (CollectionUtils.isEmpty(param.getCitys())) {
            throw new AmisBadRequestException("请选择城市");
        }
        RuleParam ruleParam = new RuleParam();
        // 写死  查店型用
        ruleParam.setScopeCode("TaskCreate");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
        // 平台必备店型
        Map<String, String> platStoreGroup = ruleEnum.get("PlatStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 中参必备店型
        Map<String, String> zsStoreGroup = ruleEnum.get("ZsStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));

        String checkMsg = super.check();
        // ②平台必备表查重：平台、SKU查到4条记录（全店型）则算重复。
        List<NecessaryPlatformCountDTO> countDTOS = necessaryPlatformGoodsExtendMapper.selectCompanyNecessaryExistsGoods(param.getPlatformOrgId(), param.getGoodsNos());
        List<String> platformExistsGoods = countDTOS.stream().filter(v -> Constants.STORE_TYPE_CATEGORY.contains(v.getCategoryId()) ? v.getCount().equals(platStoreGroup.size()) : v.getCount().equals(zsStoreGroup.size())).map(NecessaryPlatformCountDTO::getGoodsNo).collect(Collectors.toList());
        StringBuilder message = new StringBuilder();
        if (StringUtils.isNotBlank(checkMsg)) {
            message.append(checkMsg);
        }
        if (CollectionUtils.isNotEmpty(platformExistsGoods)) {
            message.append(platformExistsGoods.stream().distinct().collect(Collectors.joining("、")) + "，已在平台必备中存在，不可添加。");
        }
        // 企业必备表查重：平台、管理主体、城市、SKU
        List<String> companyGoodsExists = necessaryCompanyGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), param.getCompanyOrgId(), param.getCitys(), param.getGoodsNos());
        if (CollectionUtils.isNotEmpty(companyGoodsExists)) {
            message.append(companyGoodsExists.stream().distinct().collect(Collectors.joining("、")) + "，已在企业必备中存在，不可添加。");
        }
        if (message.length() > 0) {
            if (!param.getExistsIgnore()) {
                message.append("是否排除掉重复商品，继续添加？");
                return message.toString();
            } else {
                platformExistsGoods.addAll(companyGoodsExists);
                param.setGoodsNos(param.getGoodsNos().stream().filter(v -> !platformExistsGoods.contains(v)).distinct().collect(Collectors.toList()));
                if (org.apache.commons.collections.CollectionUtils.isEmpty(param.getGoodsNos())) {
                    throw new AmisBadRequestException("排除重复后没有数据了");
                }
            }
        }
        return "";
    }

}
