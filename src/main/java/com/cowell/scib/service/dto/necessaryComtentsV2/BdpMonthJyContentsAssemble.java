package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.ManageStatusEnum;
import com.cowell.scib.enums.StoreGoodsEffectStatusEnum;
import com.cowell.scib.enums.SuggestManageStatusEnum;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BdpMonthJyContentsAssemble extends StoreContentsAssemble{
    private final Logger logger = LoggerFactory.getLogger(BdpMonthJyContentsAssemble.class);
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    // 启用/停用
    @Override
    protected List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        logger.info("组装大数据推送 - 月度经营目录建议处理到一店一目 size:{}", storeGoodsContentDTOS.size());
        return storeGoodsContentDTOS;
    }

    protected void dealAndPush(List<StoreGoodsContentDTO> processes) {
        Map<Long, List<StoreGoodsContentDTO>> processMap = processes.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId));
        for (Map.Entry<Long, List<StoreGoodsContentDTO>> entry : processMap.entrySet()) {
            for (List<StoreGoodsContentDTO> part : Lists.partition(entry.getValue(), Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE)){
            StoreGoodsContentsExample example = new StoreGoodsContentsExample();
            example.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(part.stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList()));
            Map<String, StoreGoodsContents> existsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(StoreGoodsContents::getGoodsNo, Function.identity(), (k1, k2) -> k1));
            List<StoreGoodsContents> insert = new ArrayList<>();
            List<StoreGoodsContents> update = new ArrayList<>();
            for (StoreGoodsContentDTO contents : part) {
                StoreGoodsContents old = existsMap.get(contents.getGoodsNo());
                if (null == old) {
                    StoreGoodsContents newContent = new StoreGoodsContents();
                    BeanUtils.copyProperties(contents, newContent);
                    // 在一店一目中不存在 建议经营状态=大数据给的值(生效必备标签=非必备，如果大数据给的值是经营-必备，则改为建议经营-选配)
                    // 经营状态:如果建议经营状态=建议经营-选配 和 建议不经营设置为空(不更新);其他值，则=建议经营状态。
                    if (SuggestManageStatusEnum.MANAGE_NECESARY.getCode().equals(newContent.getSuggestManageStatus())) {
                        newContent.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                    }
                    if (SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode().equals(newContent.getSuggestManageStatus())
                            || SuggestManageStatusEnum.SUGGEST_NON_MANAGE.getCode().equals(newContent.getSuggestManageStatus())) {
                        newContent.setManageStatus(ManageStatusEnum.NONE.getCode());
                    } else {
                        newContent.setManageStatus(ManageStatusEnum.getEnumBySuggestCode(contents.getSuggestManageStatus()).getCode());
                    }
                    newContent.setNecessaryTag(0);
                    newContent.setNecessaryTagName("");
                    newContent.setMinDisplayQuantity(BigDecimal.ZERO);
                    newContent.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                    Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(contents.getStoreId());
                    if (optional.isPresent()) {
                        newContent.setStoreOrgId(optional.get().getId());
                        newContent.setStoreCode(optional.get().getSapCode());
                    }
                    insert.add(newContent);
                    continue;
                }
                // if在一店一目中已存在该行 & 经营状态为空建议经营状态=大数据给的值(如果与生效必备标签不一致，以生效必备标签为准):
                // 1.大数据≠经营-必备，生效必备标签=必备，建议经营状态=经营-必备
                // 2.大数据= 经营-必备，生效必备标签=非必备， 建议经营状态=建议经营-选配
                Integer beforeSugggest = old.getSuggestManageStatus();
                Integer beforeManage = old.getManageStatus();
                if (ManageStatusEnum.NONE.getCode().equals(beforeManage)) {
                    if (!SuggestManageStatusEnum.MANAGE_NECESARY.getCode().equals(contents.getSuggestManageStatus()) && old.getNecessaryTag() != 0) {
                        old.setSuggestManageStatus(SuggestManageStatusEnum.MANAGE_NECESARY.getCode());
                    } else if (SuggestManageStatusEnum.MANAGE_NECESARY.getCode().equals(contents.getSuggestManageStatus()) && old.getNecessaryTag() == 0) {
                        old.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                    } else {
                        old.setSuggestManageStatus(contents.getSuggestManageStatus());
                    }
                    // 经营状态:如果建议经营状态,=建议经营-选配 和 建议不经营，设置为空(不更新):其他值，则=建议经营状态。
                    old.setManageStatus(
                            SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode().equals(old.getSuggestManageStatus()) || SuggestManageStatusEnum.SUGGEST_NON_MANAGE.getCode().equals(old.getSuggestManageStatus())
                                    ? ManageStatusEnum.NONE.getCode() : ManageStatusEnum.getEnumBySuggestCode(old.getSuggestManageStatus()).getCode());
                } else {
                    // 在一店一目中存在该行 & 经营状态有值 建议经营状态=大数据给的值(如果与生效必备标签不一致，以生效必备标签为准)
                    // 经营状态: 如果建议经营状态=建议-选配 和 建议不经营，则不变更;其他值 则=建议经营状态
                    if (!SuggestManageStatusEnum.MANAGE_NECESARY.getCode().equals(contents.getSuggestManageStatus()) && old.getNecessaryTag() != 0) {
                        old.setSuggestManageStatus(SuggestManageStatusEnum.MANAGE_NECESARY.getCode());
                    } else if (SuggestManageStatusEnum.MANAGE_NECESARY.getCode().equals(contents.getSuggestManageStatus()) && old.getNecessaryTag() == 0) {
                        old.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                    } else {
                        old.setSuggestManageStatus(contents.getSuggestManageStatus());
                    }
                    old.setManageStatus(
                            SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode().equals(old.getSuggestManageStatus()) || SuggestManageStatusEnum.SUGGEST_NON_MANAGE.getCode().equals(old.getSuggestManageStatus())
                                    ? old.getManageStatus() : ManageStatusEnum.getEnumBySuggestCode(old.getSuggestManageStatus()).getCode());
                }
                if (!old.getSuggestManageStatus().equals(beforeSugggest) || !old.getManageStatus().equals(beforeManage)) {
                    update.add(old);
                }
            }
            if (CollectionUtils.isNotEmpty(insert)) {
                List<Long> storeGoodsIds = getStoreGoodsIds(insert.size());
                insert.forEach(v -> v.setId(storeGoodsIds.remove(0)));
                Lists.partition(insert, Constants.INSERT_MAX_VALUE).forEach(v -> storeGoodsContentsExtendMapper.batchInsert(v));
            }
            if (CollectionUtils.isNotEmpty(update)) {
                storeGoodsContentsExtendMapper.batchUpdate(update, entry.getKey());
            }
        }
        }
    }
}
