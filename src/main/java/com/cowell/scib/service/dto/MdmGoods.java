package com.cowell.scib.service.dto;

public class MdmGoods {

    /**
     * 商品编码
     */
    private String goodsno;
    /**
     * 旧系统编码
     */
    private String oldgoodsno;
    /**
     * 商品描述
     */
    private String goodsdesc;
    /**
     * 商品名称
     */
    private String goodsname;
    /**
     * 商品名称助记码
     */
    private String opcode;

    /**
     * 通⽤名
     */
    private String curname;
    /**
     * 规格
     */
    private String goodsspec;
    /**
     * 商品名称助记码
     */
    private String goodsunit;
    /**
     * 批准⽂号
     */
    private String apprdocno;
    /**
     * 注册证号
     */
    private String regdocno;
    /**
     * 国际条形码
     */
    private String barcode;
    /**
     * 内部条形码
     */
    private String interbarcode;
    /**
     * 经营属性
     */
    private String goodsline;
    /**
     * 商品状态
     */
    private String goodstatus;
    /**
     * 集团数据版本号
     */
    private String update_time;
    /**
     * 公司编码
     */
    private String mdm_company_code;

    /**
     *  -1 删除， 0 新增， 1 修改， 2不变
     */
    private Integer mdm_data_operation_status;


    public String getGoodsno() {
        return goodsno;
    }

    public void setGoodsno(String goodsno) {
        this.goodsno = goodsno;
    }

    public String getOldgoodsno() {
        return oldgoodsno;
    }

    public void setOldgoodsno(String oldgoodsno) {
        this.oldgoodsno = oldgoodsno;
    }

    public String getGoodsdesc() {
        return goodsdesc;
    }

    public void setGoodsdesc(String goodsdesc) {
        this.goodsdesc = goodsdesc;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getOpcode() {
        return opcode;
    }

    public void setOpcode(String opcode) {
        this.opcode = opcode;
    }

    public String getCurname() {
        return curname;
    }

    public void setCurname(String curname) {
        this.curname = curname;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getApprdocno() {
        return apprdocno;
    }

    public void setApprdocno(String apprdocno) {
        this.apprdocno = apprdocno;
    }

    public String getRegdocno() {
        return regdocno;
    }

    public void setRegdocno(String regdocno) {
        this.regdocno = regdocno;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getInterbarcode() {
        return interbarcode;
    }

    public void setInterbarcode(String interbarcode) {
        this.interbarcode = interbarcode;
    }

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    public String getGoodstatus() {
        return goodstatus;
    }

    public void setGoodstatus(String goodstatus) {
        this.goodstatus = goodstatus;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getMdm_company_code() {
        return mdm_company_code;
    }

    public void setMdm_company_code(String mdm_company_code) {
        this.mdm_company_code = mdm_company_code;
    }

    public Integer getMdm_data_operation_status() {
        return mdm_data_operation_status;
    }

    public void setMdm_data_operation_status(Integer mdm_data_operation_status) {
        this.mdm_data_operation_status = mdm_data_operation_status;
    }

    @Override
    public String toString() {
        return "MdmGoods{" +
                "goodsno='" + goodsno + '\'' +
                ", oldgoodsno='" + oldgoodsno + '\'' +
                ", goodsdesc='" + goodsdesc + '\'' +
                ", goodsname='" + goodsname + '\'' +
                ", opcode='" + opcode + '\'' +
                ", curname='" + curname + '\'' +
                ", goodsspec='" + goodsspec + '\'' +
                ", goodsunit='" + goodsunit + '\'' +
                ", apprdocno='" + apprdocno + '\'' +
                ", regdocno='" + regdocno + '\'' +
                ", barcode='" + barcode + '\'' +
                ", interbarcode='" + interbarcode + '\'' +
                ", goodsline='" + goodsline + '\'' +
                ", goodstatus='" + goodstatus + '\'' +
                ", update_time='" + update_time + '\'' +
                ", mdm_company_code='" + mdm_company_code + '\'' +
                ", mdm_data_operation_status=" + mdm_data_operation_status +
                '}';
    }
}
