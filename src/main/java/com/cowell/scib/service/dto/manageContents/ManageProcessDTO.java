package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 经营目录commonDto
 */
@Data
public class ManageProcessDTO implements AmisDataInInterface, Serializable {


    @ApiModelProperty(value = "门店MDM编码")
    private String storeCode;

    @ApiModelProperty(value = "店长处理通知")
    private String dzNotice;

    @ApiModelProperty(value = "门店是否需要二次确认")
    private Boolean needConfirm;

    @ApiModelProperty(value = "是否再调整期")
    private Boolean editAble;

    @ApiModelProperty(value = "进度值")
    private BigDecimal processPercent;

    @ApiModelProperty(value = "进度描述")
    private String processDesc;

    @ApiModelProperty(value = "门店豁免数量")
    private Integer  increaseLimit;
    @ApiModelProperty(value = "门店豁免数量下限")
    private Integer  increaseLow;


}
