package com.cowell.scib.service.dto;

import com.cowell.scib.entityTidb.*;
import lombok.Data;

import java.util.List;
@Data
public class NecessaryGoodsDTO {
    /**
     * 必备级别
     */
    private String necessaryTag;
    List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods;
    List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods;
    List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods;
    List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods;
    List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods;
    List<PushStoreMdmDTO> pushStoreMdmDTOList;

}
