package com.cowell.scib.service.dto.necessaryContents;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.NecessaryTagEnum;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.extend.NecessaryGroupGoodsExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NecessaryGroupAssemble extends NecessaryAssemble {

    @Autowired
    private NecessaryGroupGoodsExtendMapper necessaryGroupGoodsExtendMapper;

    @Override
    public String deleteNecessary() throws Exception {
        if (!necessaryContentsService.checkModifyAble(delParam.getPlatformOrgId())){
            throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
        }
        NecessaryGroupGoodsExample example = new NecessaryGroupGoodsExample();
        example.createCriteria().andPlatformOrgIdEqualTo(delParam.getPlatformOrgId()).andIdIn(delParam.getNecessaryIds()).andNecessaryTagEqualTo(NecessaryTagEnum.GROUP_NECESSARY.getCode());
        List<NecessaryGroupGoods> necessaryGoodsList = necessaryGroupGoodsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryGoodsList)) {
            throw new AmisBadRequestException("所选据已全部被删除");
        }
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        // set 平台信息
        NecessaryAddParam param = new NecessaryAddParam();
        param.setNecessaryTag(delParam.getNecessaryTag());
        setProp(param, userDTO, CacheVar.getPlatformByOrgId(delParam.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
        checkFullScope();
        List<Long> storeIds = getMdmStroeExDTOS(false).stream().map(MdmStoreBaseDTO::getStoreId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new AmisBadRequestException("没有获取到平台:" + platformOrg.getShortName() + "下的门店");
        }
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        necessaryGoodsList.stream().map(necessaryGoods -> {
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            BeanUtils.copyProperties(necessaryGoods, goodsDTO);
            return goodsDTO;
        }).forEach(v -> delGoodsStoreMap.put(v, storeIds));
        return delStroeGoods(delGoodsStoreMap);
    }

    @Override
    public NecessaryAssemble checkFullScope() {
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(Lists.newArrayList(platformOrg.getId()), userDTO.getUserId());
        if (!orgDataScopeList.contains(platformOrg.getId())) {
            throw new AmisBadRequestException("没有当前平台权限");
        }
        return this;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NecessaryAssemble assemble() throws Exception {
        List<NecessaryCommonGoodsDTO> list = assembleCommon();
        List<NecessaryGroupGoods> groupGoodsList = list.stream().map(v -> {
            NecessaryGroupGoods groupGoods = new NecessaryGroupGoods();
            BeanUtils.copyProperties(v, groupGoods);
            return groupGoods;
        }).collect(Collectors.toList());
        List<Long> storeIds = getMdmStroeExDTOS(true).stream().map(MdmStoreExDTO::getStoreId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new AmisBadRequestException("无符合条件的门店，不可新增必备。");
        }
        necessaryGroupGoodsExtendMapper.batchInsert(groupGoodsList);
        //②查平台必备表：平台、SKU，存在则删除N行。
        NecessaryPlatformGoodsExample platformGoodsExample = new NecessaryPlatformGoodsExample();
        platformGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getPlatformOrgId()).andGoodsNoIn(param.getGoodsNos());
        necessaryPlatformGoodsMapper.deleteByExample(platformGoodsExample);
        //③查企业必备表：平台、SKU，存在则删除N行。
        NecessaryCompanyGoodsExample companyGoodsExample = new NecessaryCompanyGoodsExample();
        companyGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getPlatformOrgId()).andGoodsNoIn(param.getGoodsNos());
        necessaryCompanyGoodsMapper.deleteByExample(companyGoodsExample);
        //④查店型必备表：平台、SKU，存在则删除N行。
        NecessaryStoreTypeGoodsExample storeTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
        storeTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getPlatformOrgId()).andGoodsNoIn(param.getGoodsNos());
        necessaryStoreTypeGoodsMapper.deleteByExample(storeTypeGoodsExample);
        //⑤查店型选配表：平台、SKU，存在则删除N行。
        NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
        chooseStoreTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getPlatformOrgId()).andGoodsNoIn(param.getGoodsNos());
        necessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);
        //⑥查单店必备表：平台、SKU，存在则删除N行。
        NecessarySingleStoreGoodsExample singleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
        singleStoreGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getPlatformOrgId()).andGoodsNoIn(param.getGoodsNos());
        necessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);
        // 添加
        message = addStoreGoods(storeIds, groupGoodsList.stream().map(v -> {
            NecessaryCommonGoodsDTO commonGoodsDTO = new NecessaryCommonGoodsDTO();
            BeanUtils.copyProperties(v, commonGoodsDTO);
            return commonGoodsDTO;
        }).collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
        return this;
    }

    @Override
    public List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add) {
        if (add) {
            return necessaryContentsService.getPlatformStoreInfo(platformOrg.getId(), null, userDTO.getUserId());
        } else {
            return CacheVar.getStoreListByPlatformOrgId(platformOrg.getId()).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList());
        }
    }

    @Override
    public String check() {
        String checkMsg = super.check();
        StringBuilder message = new StringBuilder();
        if (StringUtils.isNotBlank(checkMsg)) {
            if (StringUtils.isNotBlank(checkMsg)) {
                message.append(checkMsg);
                message.append("是否排除掉重复商品，继续添加？");
            }
        }
        return message.toString();
    }
}
