package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/29 14:03
 */
@Data
public class DevelopModuleRecordDTO implements Serializable {
    private static final long serialVersionUID = 1082271155031833698L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 功能模块编码
     */
    private String moduleCode;

    /**
     * 发版记录版本号
     */
    private String developVersion;

    /**
     * 发版类型  1-BUG修复  2-新功能  3-功能优化
     */
    private Byte developType;

    /**
     * 发版记录标题
     */
    private String developTitle;

    /**
     * 发版记录内容
     */
    private String developContent;

    /**
     * 发布时间
     */
    private String developTime;


    /**
     * 操作人
     */
    private String updatedName;

    /**
     * 操作时间
     */
    private String gmtUpdate;

    /**
     * 附件集合
     */
    List<DevelopModuleRecordFileDTO> fileList;

    /**
     * 图片地址，逗号分割（amis要求）
     */
    private String imageUrlList;

    /**
     * 触达通道集合，逗号分割（amis要求）
     */
    private String reachChannelList;

    /**
     * 触达组id集合，逗号分割（amis要求）
     */
    private String reachGroupIdList;

    /**
     * 触达url
     */
    private String developUrl;

    /**
     * 扩展字段
     */
    private String extend;
}
