package com.cowell.scib.service.dto.skuadjust;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> 经营目录-门店SKU数配置调整
 */
@Data
public class JymlStoreSkuLimitAdjustDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 调整单号
     */
    @ApiModelProperty(value = "调整单号")
    private String adjustCode;

    /**
     * 区域平台id
     */
    @ApiModelProperty(value = "区域平台id")
    private Long platformOrgId;

    /**
     * 区域平台
     */
    @ApiModelProperty(value = "区域平台")
    private String platformName;

    /**
     * 项目公司id
     */
    @ApiModelProperty(value = "项目公司id")
    private Long companyOrgId;

    /**
     * businessId
     */
    @ApiModelProperty(value = "businessId")
    private Long businessId;

    /**
     * 项目公司code
     */
    @ApiModelProperty(value = "项目公司code")
    private String companyCode;

    /**
     * 项目公司
     */
    @ApiModelProperty(value = "项目公司")
    private String companyName;

    /**
     * 省份
     */
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 门店orgid
     */
    @ApiModelProperty(value = "门店orgid")
    private Long storeOrgId;

    /**
     * storeId
     */
    @ApiModelProperty(value = "storeId")
    private Long storeId;

    /**
     * 门店code
     */
    @ApiModelProperty(value = "门店code")
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 店型编码
     */
    @ApiModelProperty(value = "店型编码")
    private String storeType;

    /**
     * 组货店型
     */
    @ApiModelProperty(value = "组货店型")
    private String storeTypeName;

    /**
     * 调整状态 0 未提交 1 已提交 2审批中 3 已生效 4 已作废
     */
    private Integer adjustStatus;

    /**
     * 调整状态 0 未提交 1 已提交 2审批中 3 已生效 4 已作废
     */
    @ApiModelProperty(value = "调整状态")
    private String adjustStatusDesc;

    /**
     * oa流程单号
     */
    @ApiModelProperty(value = "oa流程单号")
    private String oaCode;

    @ApiModelProperty(value = "审批结果处理时间")
    private String approveTime;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdName;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updatedBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String gmtUpdate;

    private List<JymlStoreSkuLimitAdjustDetailDTO> detailDTOList;

    public static LinkedHashMap<String, String> getExportMap() {
        LinkedHashMap<String, String> fieldMap = Maps.newLinkedHashMap();
        fieldMap.put("adjustCode", "调整单号");
        fieldMap.put("adjustStatusDesc", "状态");
        fieldMap.put("storeCode", "门店编码");
        fieldMap.put("storeName", "门店名称");
        fieldMap.put("storeTypeName", "店型");
        fieldMap.put("oaCode", "OA流程单号");
        fieldMap.put("createdName", "创建人");
        fieldMap.put("gmtCreate", "创建时间");
        fieldMap.put("approveTime", "审批结果处理时间");
        fieldMap.put("platformName", "区域平台");
        fieldMap.put("province", "省份");
        fieldMap.put("companyName", "项目公司");
        fieldMap.put("city", "城市");
        return fieldMap;
    }

}
