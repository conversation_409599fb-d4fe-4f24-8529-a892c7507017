package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DropBoxParam {
    @ApiModelProperty(value = "类型 1:生效店型下拉框 2:必备标签下拉")
    private Integer type;
    @ApiModelProperty(value = "店型")
    private String storeType;
    @ApiModelProperty(value = "企业orgId type:4时必传")
    private Long companyOrgId;
    @ApiModelProperty(value = "属性标识")
    private String propertyCode;
}
