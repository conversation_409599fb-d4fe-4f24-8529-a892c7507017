package com.cowell.scib.service.dto;

import com.cowell.scib.service.AmisDataInInterface;

import java.util.StringJoiner;

public class MdmStoreExDTO extends MdmStoreBaseDTO implements AmisDataInInterface {


    private Long platformOrgId;
    private String platformOrgName;
    private String businessName;
    // 平台店型编码
    private String platStoreTypeCode;

    // 平台店型
    private String platStoreType;

    // 组货店型
    private String storeTypeCode;

    // 组货店型
    private String storeType;

    // 中参店型编码
    private String zsStoreTypeCode;

    // 中参店型
    private String zsStoreType;

    // 配方店型编码
    private String pfStoreTypeCode;

    // 配方店型
    private String pfStoreType;

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformOrgName() {
        return platformOrgName;
    }

    public void setPlatformOrgName(String platformOrgName) {
        this.platformOrgName = platformOrgName;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getPlatStoreTypeCode() {
        return platStoreTypeCode;
    }

    public void setPlatStoreTypeCode(String platStoreTypeCode) {
        this.platStoreTypeCode = platStoreTypeCode;
    }

    public String getPlatStoreType() {
        return platStoreType;
    }

    public void setPlatStoreType(String platStoreType) {
        this.platStoreType = platStoreType;
    }

    public String getStoreTypeCode() {
        return storeTypeCode;
    }

    public void setStoreTypeCode(String storeTypeCode) {
        this.storeTypeCode = storeTypeCode;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getZsStoreTypeCode() {
        return zsStoreTypeCode;
    }

    public void setZsStoreTypeCode(String zsStoreTypeCode) {
        this.zsStoreTypeCode = zsStoreTypeCode;
    }

    public String getZsStoreType() {
        return zsStoreType;
    }

    public void setZsStoreType(String zsStoreType) {
        this.zsStoreType = zsStoreType;
    }

    public String getPfStoreTypeCode() {
        return pfStoreTypeCode;
    }

    public void setPfStoreTypeCode(String pfStoreTypeCode) {
        this.pfStoreTypeCode = pfStoreTypeCode;
    }

    public String getPfStoreType() {
        return pfStoreType;
    }

    public void setPfStoreType(String pfStoreType) {
        this.pfStoreType = pfStoreType;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MdmStoreExDTO.class.getSimpleName() + "[", "]")
                .add("platformOrgId=" + platformOrgId)
                .add("platformOrgName='" + platformOrgName + "'")
                .add("businessName='" + businessName + "'")
                .add("platStoreTypeCode='" + platStoreTypeCode + "'")
                .add("platStoreType='" + platStoreType + "'")
                .add("storeTypeCode='" + storeTypeCode + "'")
                .add("storeType='" + storeType + "'")
                .add("zsStoreTypeCode='" + zsStoreTypeCode + "'")
                .add("zsStoreType='" + zsStoreType + "'")
                .add("pfStoreTypeCode='" + pfStoreTypeCode + "'")
                .add("pfStoreType='" + pfStoreType + "'")
                .toString();
    }

}
