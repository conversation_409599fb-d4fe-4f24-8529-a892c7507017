package com.cowell.scib.service.dto.TrackResult;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class StoreTypeNecessaryData {

    @ExcelProperty(value = "公司ID",index = 0)
    private Long companyOrgId;

    @ExcelProperty(value = "公司名称",index = 1)
    private String companyName;

    @ExcelProperty(value = "城市",index = 2)
    private String city;

    @ExcelProperty(value = "店型",index = 3)
    private String storeType;

    @ExcelProperty(value = "商品编码",index = 4)
    private String goodsNo;

    @ExcelProperty(value = "错误原因",index = 5)
    private String errorReason;
}
