package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR> mdm任务表
 */
public class MdmTaskDetailCountDTO implements Serializable {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;

    /**
     * 明细行数
     */
    @ApiModelProperty(value = "明细行数")
    private Integer detailCount;

    /**
     * 下发状态 0 已下发 1 失败 2 成功
     */
    private Byte pushStatus;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(Integer detailCount) {
        this.detailCount = detailCount;
    }

    public Byte getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(Byte pushStatus) {
        this.pushStatus = pushStatus;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MdmTaskDetailCountDTO.class.getSimpleName() + "[", "]")
                .add("taskId=" + taskId)
                .add("detailCount=" + detailCount)
                .add("pushStatus=" + pushStatus)
                .toString();
    }
}
