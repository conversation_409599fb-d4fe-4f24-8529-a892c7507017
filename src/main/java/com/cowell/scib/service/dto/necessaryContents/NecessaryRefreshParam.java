package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NecessaryRefreshParam implements Serializable {

    @ApiModelProperty(value = "门店编码集合")
    private List<String> storeCodes;

    @ApiModelProperty(value = "商品编码集合")
    private List<String> goodsNos;

    @ApiModelProperty(value = "必备层级")
    private Integer necessaryTag;

    @ApiModelProperty(value = "必备层级名称")
    private String necessaryTagName;

    @ApiModelProperty(value = "是否下发mdm")
    private Boolean pushMdmAble;

    @ApiModelProperty(value = "原始层级(不填不校验)")
    private Integer sourceNecessaryTag;

}
