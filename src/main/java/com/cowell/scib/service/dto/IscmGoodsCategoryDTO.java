package com.cowell.scib.service.dto;

import java.io.Serializable;
import java.util.List;

public class IscmGoodsCategoryDTO implements Serializable {

    private static final long serialVersionUID = 2901353487844113066L;

    //类目编码
    private Long categoryId;
    //类目等级
    private Byte level;
    //类目名称
    private String categoryName;
    //子类名称
    private List<IscmGoodsCategoryDTO> sonCategorys;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Byte getLevel() {
        return level;
    }

    public void setLevel(Byte level) {
        this.level = level;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<IscmGoodsCategoryDTO> getSonCategorys() {
        return sonCategorys;
    }

    public void setSonCategorys(List<IscmGoodsCategoryDTO> sonCategorys) {
        this.sonCategorys = sonCategorys;
    }

    @Override
    public String toString() {
        return "IscmGoodsCategoryDTO{" +
                "categoryId=" + categoryId +
                ", level=" + level +
                ", categoryName='" + categoryName + '\'' +
                ", sonCategorys=" + sonCategorys +
                '}';
    }
}
