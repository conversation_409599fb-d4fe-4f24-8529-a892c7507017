package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> mdm任务表
 */
@Data
public class MdmTaskDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "任务id")
    private Long id;

    /**
     * 任务来源 1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备 7组货
     */
    @ApiModelProperty(value = "任务来源")
    private Byte taskSource;

    /**
     * 任务来源描述
     */
    @ApiModelProperty(value = "任务来源描述")
    private String taskSourceDesc;

    /**
     * 任务状态 1更新中 2 失败 3 成功
     */
    @ApiModelProperty(value = "任务状态")
    private Byte taskStatus;

    @ApiModelProperty(value = "任务状态描述")
    private String taskStatusDesc;

    /**
     * 明细行数
     */
    private Integer detailCount;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 任务进度
     */
    @ApiModelProperty(value = "任务进度")
    private String process;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtUpdate;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private Long platformOrgId;

    private String platformName;

}
