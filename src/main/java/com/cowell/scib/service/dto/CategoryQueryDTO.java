package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class CategoryQueryDTO implements Serializable {
    private static final long serialVersionUID = -4976346779929588005L;
    @ApiModelProperty(value = "分类id列表")
    private List<Long> categoryIds;

    @ApiModelProperty(value = "类目级别")
    private String categoryLevel;

    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Long> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public void setCategoryLevel(String categoryLevel) {
        this.categoryLevel = categoryLevel;
    }

    public String getCategoryLevel() {
        return categoryLevel;
    }

    @Override
    public String toString() {
        return "CategoryIscmDTO{" +
                "categoryIds=" + categoryIds +
                ", categoryLevel='" + categoryLevel + '\'' +
                '}';
    }

}
