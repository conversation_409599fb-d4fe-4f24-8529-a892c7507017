package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.StringJoiner;

public class SensitiveWarnSaveDTO {

    @ApiModelProperty(value = "企业orgId")
    private Long orgId;

    @ApiModelProperty(value = "预警名称")
    private ControlTowerParamDTO nameConfig = new ControlTowerParamDTO("G000001");

    @ApiModelProperty(value = "是否启用")
    private ControlTowerParamDTO enableConfig = new ControlTowerParamDTO("G000002");

    @ApiModelProperty(value = "生效连锁范围")
    private ControlTowerParamDTO effectBusiness = new ControlTowerParamDTO("G000003");

    @ApiModelProperty(value = "提醒通道")
    private ControlTowerParamDTO warnChannelRange = new ControlTowerParamDTO("G000004");

    @ApiModelProperty(value = "提醒人员工号")
    private ControlTowerParamDTO warnEmpRange = new ControlTowerParamDTO("G000005");

    @ApiModelProperty(value = "敏感品自动更新")
    private ControlTowerParamDTO sensitiveAutoUpdate = new ControlTowerParamDTO("G000006");

    @ApiModelProperty(value = "持续无动销天数")
    private ControlTowerParamDTO nonSalesDays = new ControlTowerParamDTO("G000007");

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public ControlTowerParamDTO getNameConfig() {
        return nameConfig;
    }

    public void setNameConfig(ControlTowerParamDTO nameConfig) {
        this.nameConfig = nameConfig;
    }

    public ControlTowerParamDTO getEnableConfig() {
        return enableConfig;
    }

    public void setEnableConfig(ControlTowerParamDTO enableConfig) {
        this.enableConfig = enableConfig;
    }

    public ControlTowerParamDTO getEffectBusiness() {
        return effectBusiness;
    }

    public void setEffectBusiness(ControlTowerParamDTO effectBusiness) {
        this.effectBusiness = effectBusiness;
    }

    public ControlTowerParamDTO getWarnChannelRange() {
        return warnChannelRange;
    }

    public void setWarnChannelRange(ControlTowerParamDTO warnChannelRange) {
        this.warnChannelRange = warnChannelRange;
    }

    public ControlTowerParamDTO getWarnEmpRange() {
        return warnEmpRange;
    }

    public void setWarnEmpRange(ControlTowerParamDTO warnEmpRange) {
        this.warnEmpRange = warnEmpRange;
    }

    public ControlTowerParamDTO getSensitiveAutoUpdate() {
        return sensitiveAutoUpdate;
    }

    public void setSensitiveAutoUpdate(ControlTowerParamDTO sensitiveAutoUpdate) {
        this.sensitiveAutoUpdate = sensitiveAutoUpdate;
    }

    public ControlTowerParamDTO getNonSalesDays() {
        return nonSalesDays;
    }

    public void setNonSalesDays(ControlTowerParamDTO nonSalesDays) {
        this.nonSalesDays = nonSalesDays;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", SensitiveWarnSaveDTO.class.getSimpleName() + "[", "]")
                .add("orgId=" + orgId)
                .add("nameConfig=" + nameConfig)
                .add("enableConfig=" + enableConfig)
                .add("effectBusiness=" + effectBusiness)
                .add("warnChannelRange=" + warnChannelRange)
                .add("warnEmpRange=" + warnEmpRange)
                .add("sensitiveAutoUpdate=" + sensitiveAutoUpdate)
                .add("nonSalesDays=" + nonSalesDays)
                .toString();
    }
}
