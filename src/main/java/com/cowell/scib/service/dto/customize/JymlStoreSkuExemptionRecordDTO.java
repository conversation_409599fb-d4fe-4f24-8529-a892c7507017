package com.cowell.scib.service.dto.customize;

import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord;
import com.cowell.scib.service.AmisDataInInterface;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 经营目录-门店豁免商品记录
 */
@Data
public class JymlStoreSkuExemptionRecordDTO extends JymlStoreSkuExemptionRecord {
    /**
     * 1：门店添加 2：规划导入 3：系统建议
     */
    private String sourceDesc;

}
