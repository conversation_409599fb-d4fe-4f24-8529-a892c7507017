package com.cowell.scib.service.dto;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.StringJoiner;

/**
 * 通用商品DTO
 * <AUTHOR>
public class CommonGoodsDTO implements Serializable {

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 通用名
     */
    private String goodsCommonName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 批准文号
     */
    private String approvalNumber;


    private static final long serialVersionUID = 1L;

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CommonGoodsDTO.class.getSimpleName() + "[", "]")
                .add("goodsNo='" + goodsNo + "'")
                .add("goodsCommonName='" + goodsCommonName + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("dosageForm='" + dosageForm + "'")
                .add("specifications='" + specifications + "'")
                .add("barCode='" + barCode + "'")
                .add("goodsName='" + goodsName + "'")
                .add("goodsUnit='" + goodsUnit + "'")
                .add("description='" + description + "'")
                .add("approvalNumber='" + approvalNumber + "'")
                .toString();
    }

    public static LinkedHashMap<String, String> getCommonGoodsExportMap() {
        LinkedHashMap map = new LinkedHashMap();
        map.put("goodsNo", "商品编码");
        map.put("description", "商品描述");
        map.put("specifications", "规格");
        map.put("dosageForm", "剂型");
        map.put("manufacturer", "生产厂家");
        return map;
    }

}
