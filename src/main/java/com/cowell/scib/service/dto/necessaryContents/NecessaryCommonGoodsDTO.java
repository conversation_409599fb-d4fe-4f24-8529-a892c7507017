package com.cowell.scib.service.dto.necessaryContents;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> 集团必备商品
 */
public class NecessaryCommonGoodsDTO implements Serializable {

    /**
     * 平台orgid
     */
    private Long platformOrgId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 大类id
     */
    private Long categoryId;

    /**
     * 大类名称
     */
    private String categoryName;

    /**
     * 中类id
     */
    private Long middleCategoryId;

    /**
     * 中类名称
     */
    private String middleCategoryName;

    /**
     * 小类编码
     */
    private Long smallCategoryId;

    /**
     * 小类名称
     */
    private String smallCategoryName;

    /**
     * 子类编码
     */
    private Long subCategoryId;

    /**
     * 子类名称
     */
    private String subCategoryName;

    /**
     * 成分
     */
    private String composition;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品通用名
     */
    private String goodsCommonName;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 采购属性
     */
    private String purchaseAttr;

    /**
     * 必备标识(0非必备 1集团必备)
     */
    private Byte necessaryTag;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getMiddleCategoryId() {
        return middleCategoryId;
    }

    public void setMiddleCategoryId(Long middleCategoryId) {
        this.middleCategoryId = middleCategoryId;
    }

    public String getMiddleCategoryName() {
        return middleCategoryName;
    }

    public void setMiddleCategoryName(String middleCategoryName) {
        this.middleCategoryName = middleCategoryName;
    }

    public Long getSmallCategoryId() {
        return smallCategoryId;
    }

    public void setSmallCategoryId(Long smallCategoryId) {
        this.smallCategoryId = smallCategoryId;
    }

    public String getSmallCategoryName() {
        return smallCategoryName;
    }

    public void setSmallCategoryName(String smallCategoryName) {
        this.smallCategoryName = smallCategoryName;
    }

    public Long getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Long subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(String subCategoryName) {
        this.subCategoryName = subCategoryName;
    }

    public String getComposition() {
        return composition;
    }

    public void setComposition(String composition) {
        this.composition = composition;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getPurchaseAttr() {
        return purchaseAttr;
    }

    public void setPurchaseAttr(String purchaseAttr) {
        this.purchaseAttr = purchaseAttr;
    }

    public Byte getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(Byte necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", platformOrgId=").append(platformOrgId);
        sb.append(", platformName=").append(platformName);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", categoryName=").append(categoryName);
        sb.append(", middleCategoryId=").append(middleCategoryId);
        sb.append(", middleCategoryName=").append(middleCategoryName);
        sb.append(", smallCategoryId=").append(smallCategoryId);
        sb.append(", smallCategoryName=").append(smallCategoryName);
        sb.append(", subCategoryId=").append(subCategoryId);
        sb.append(", subCategoryName=").append(subCategoryName);
        sb.append(", composition=").append(composition);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", barCode=").append(barCode);
        sb.append(", goodsCommonName=").append(goodsCommonName);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsUnit=").append(goodsUnit);
        sb.append(", description=").append(description);
        sb.append(", specifications=").append(specifications);
        sb.append(", dosageForm=").append(dosageForm);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", approvalNumber=").append(approvalNumber);
        sb.append(", purchaseAttr=").append(purchaseAttr);
        sb.append(", necessaryTag=").append(necessaryTag);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof NecessaryCommonGoodsDTO)) return false;
        NecessaryCommonGoodsDTO that = (NecessaryCommonGoodsDTO) o;
        return Objects.equals(getPlatformOrgId(), that.getPlatformOrgId()) &&
                Objects.equals(getGoodsNo(), that.getGoodsNo()) &&
                Objects.equals(getNecessaryTag(), that.getNecessaryTag());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPlatformOrgId(), getGoodsNo(), getNecessaryTag());
    }
}
