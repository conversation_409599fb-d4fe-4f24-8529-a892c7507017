package com.cowell.scib.service.dto.manageContents;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ManageQueryParam extends AmisPageParam implements Serializable {

    @ApiModelProperty(value = "查询类型(1:版本列表 2:城市列表 3:分类列表)")
    private Integer queryType;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "平台orgId")
    private String platformOrgIds;

    @ApiModelProperty(value = "公司orgId")
    private String  businessOrgIds;

    @ApiModelProperty(value = "店型")
    private String storeTypes;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "大类")
    private String category;

    @ApiModelProperty(value = "中类")
    private String middleCategory;

    @ApiModelProperty(value = "小类")
    private String smallCategory;

    @ApiModelProperty(value = "子类")
    private String subCategory;

    private String categoryId;
    private String middleCategoryId;
    private String smallCategoryId;
    private String subCategoryId;

    @ApiModelProperty(value = "门店MDM编码")
    private String storeNo;

    @ApiModelProperty(value = "快速定位类型(1:快速定位(传分类id) 2:待确认类别 3:已确认类别  4:通过商品查询 5:已提交未确认类别)")
    private Integer quickType;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "系统建议")
    private String suggestManageStatusName;

    @ApiModelProperty(value = "我的确认")
    private String  manageStatusName;

    @ApiModelProperty(value = "本次我的确认名称")
    private String myConfirmName;

    @ApiModelProperty(value = "本次审核结果名称")
    private String reviewResultName;

    @ApiModelProperty(value = "规格")
    private String jhiSpecification;

    @ApiModelProperty(value = "生产厂家")
    private String factoryid;

    @ApiModelProperty(value = "成分")
    private String component;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    @ApiModelProperty(value = "升序降序")
    private String orderDir;

    @ApiModelProperty(value = "只显示差异行")
    private Boolean diffRowsOnly;


}
