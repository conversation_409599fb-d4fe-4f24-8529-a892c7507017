package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/24 16:41
 */
@Getter
@Setter
public class NecessaryCategoryCommonDTO implements Serializable {
    private static final long serialVersionUID = -1477367304704043296L;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    /**
     * 大类id
     */
    @ApiModelProperty(value = "大类id")
    private Long categoryId;

    /**
     * 中类id
     */
    @ApiModelProperty(value = "中类id")
    private Long middleCategoryId;

    /**
     * 小类编码
     */
    @ApiModelProperty(value = "小类编码")
    private Long smallCategoryId;

    /**
     * 子类编码
     */
    @ApiModelProperty(value = "子类编码")
    private Long subCategoryId;
}
