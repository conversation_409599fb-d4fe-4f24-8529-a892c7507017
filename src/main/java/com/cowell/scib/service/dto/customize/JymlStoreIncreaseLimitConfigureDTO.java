package com.cowell.scib.service.dto.customize;

import com.cowell.scib.entityTidb.JymlStoreIncreaseLimitConfigure;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> 经营目录-门店在营商品上浮管理
 */
@Data
public class JymlStoreIncreaseLimitConfigureDTO extends JymlStoreIncreaseLimitConfigure implements Serializable {
    @ApiModelProperty("门店已选择商品个数")
    private Integer goodsCount;

    public static LinkedHashMap<String, String> getExportMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap();
        map.put("id", "主键");
        map.put("businessOrgId", "项目公司ID");
        map.put("businessName", "项目公司");
        map.put("storeCode", "门店编码");
        map.put("increaseLow", "二轮选配商品个数下限");
        map.put("increaseLimit", "二轮选配商品个数上限");
        map.put("goodsCount", "门店已选商品个数");
        map.put("version", "版本号");
        return map;
    }
}
