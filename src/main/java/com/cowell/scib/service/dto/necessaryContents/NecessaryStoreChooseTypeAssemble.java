package com.cowell.scib.service.dto.necessaryContents;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.entityDgms.NecessaryChooseStoreTypeGoods;
import com.cowell.scib.entityDgms.NecessaryChooseStoreTypeGoodsExample;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.NecessaryChooseStoreTypeGoodsMapper;
import com.cowell.scib.mapperDgms.extend.NecessaryChooseStoreTypeGoodsExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class NecessaryStoreChooseTypeAssemble extends NecessaryAssemble {

    private final Logger logger = LoggerFactory.getLogger(NecessaryStoreChooseTypeAssemble.class);

    @Autowired
    private NecessaryChooseStoreTypeGoodsMapper necessaryChooseStoreTypeGoodsMapper;
    @Autowired
    private NecessaryChooseStoreTypeGoodsExtendMapper necessaryChooseStoreTypeGoodsExtendMapper;
    @Override
    public String deleteNecessary() throws Exception{
        if (!necessaryContentsService.checkModifyAble(delParam.getPlatformOrgId())){
            throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
        }
        //        1、店型必备表删除该企业*城市*店型*SKU行。
        //        2、用企业*城市*店型取门店列表，清空一店一目表这些门店*SKU的配置类型、最小陈列量
        NecessaryChooseStoreTypeGoodsExample example = new NecessaryChooseStoreTypeGoodsExample();
        example.createCriteria().andPlatformOrgIdEqualTo(delParam.getPlatformOrgId()).andIdIn(delParam.getNecessaryIds());
        List<NecessaryChooseStoreTypeGoods> necessaryGoodsList = necessaryChooseStoreTypeGoodsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryGoodsList)) {
            throw new AmisBadRequestException("所选单据已全部被删除");
        }
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        List<Long> companyOrgIds = necessaryGoodsList.stream().map(NecessaryChooseStoreTypeGoods::getCompanyOrgId).distinct().collect(Collectors.toList());
        // set 平台信息
        NecessaryAddParam param = new NecessaryAddParam();
        param.setNecessaryTag(delParam.getNecessaryTag());
        setProp(param, userDTO, CacheVar.getPlatformByOrgId(delParam.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(companyOrgIds, userDTO.getUserId());
        // 按照 企业, 城市, 店型分组
        Map<String, List<NecessaryChooseStoreTypeGoods>> necessaryGoodsMap = necessaryGoodsList.stream().filter(v -> orgDataScopeList.contains(v.getCompanyOrgId())).collect(Collectors.groupingBy(v -> v.getCompanyOrgId() + "-" + v.getCity() + "-" + v.getStoreType()));
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        companyOrgIds.forEach(c -> {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(c).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        necessaryGoodsMap.forEach((key,v) -> {
            String[] split = StringUtils.split(key, "-");
            List<OrgInfoBaseCache> childOrgsDTOS = CacheVar.getStoreListByBusinessOrgId(Long.valueOf(split[0]));
            if (CollectionUtils.isNotEmpty(childOrgsDTOS)) {
                List<Long> storeIds = childOrgsDTOS.stream().filter(store -> null != store.getOutId()).map(store -> CacheVar.getStoreExtInfoByStoreId(store.getOutId()).orElse(null)).filter(store ->
                        null != store && split[1].equals(store.getCity()) && (split[2].equals(store.getZsStoreTypeCode()) || split[2].equals(store.getStoreTypeCode()) || split[2].equals(store.getPfStoreTypeCode()))).map(MdmStoreExDTO::getStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(storeIds)) {
                    v.forEach(goo -> {
                        NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
                        BeanUtils.copyProperties(goo, goodsDTO);
                        List<Long> storeIdList = Optional.ofNullable(delGoodsStoreMap.get(goodsDTO)).orElse(new ArrayList<>());
                        storeIdList.addAll(storeIds);
                        delGoodsStoreMap.put(goodsDTO, storeIdList);
                    });
                }
            }
        });
        necessaryChooseStoreTypeGoodsMapper.deleteByExample(example);
        if (MapUtils.isEmpty(delGoodsStoreMap)) {
            return "无符合条件的门店，仅删除必备目录，不创建MDM任务。";
        }
        logger.info("delGoodsStoreMap:{}", JSON.toJSONString(delGoodsStoreMap));
        return delStroeGoods(delGoodsStoreMap);
    }

    @Override
    public NecessaryAssemble checkFullScope() {
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(Lists.newArrayList(param.getCompanyOrgId()), userDTO.getUserId());
        if (CollectionUtils.isEmpty(orgDataScopeList)) {
            throw new AmisBadRequestException("没有当前公司权限");
        }
        return this;
    }

    @Override
    public List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add) {
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        if (add) {
            storeInfos.addAll(necessaryContentsService.getPlatformStoreInfo(param.getPlatformOrgId(), Lists.newArrayList(companyOrg.getBusinessOrgId()), userDTO.getUserId()));
        } else {
            storeInfos.addAll(CacheVar.getStoreListByBusinessOrgId(companyOrg.getId()).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            return Lists.newArrayList();
        }
        if (!add) {
            return storeInfos;
        }
        List<ChildOrgsDTO> childOrgsDTOS = permissionService.listChildOrgAssignedType(Lists.newArrayList(param.getCompanyOrgId()), OrgTypeEnum.STORE.getCode());
        if (CollectionUtils.isEmpty(childOrgsDTOS)) {
            return Lists.newArrayList();
        }
        List<Long> children = childOrgsDTOS.get(0).getChildren().stream().map(OrgDTO::getOutId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            return Lists.newArrayList();
        }
        storeInfos = storeInfos.stream().filter(v -> children.contains(v.getStoreId()) && param.getCitys().contains(v.getCity())
                && (param.getStoreTypes().contains(v.getZsStoreTypeCode()) || param.getStoreTypes().contains(v.getStoreTypeCode()) || param.getStoreTypes().contains(v.getPfStoreTypeCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            return Lists.newArrayList();
        }
        return storeInfos;

    }

    @Override
    public NecessaryAssemble assemble() {
        return null;
    }

    @Override
    public String check() {
        throw new AmisBadRequestException("当前目录禁止添加");
    }
}
