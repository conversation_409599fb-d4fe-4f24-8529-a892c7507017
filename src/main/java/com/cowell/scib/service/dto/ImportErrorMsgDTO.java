package com.cowell.scib.service.dto;

import java.util.StringJoiner;

public class ImportErrorMsgDTO<T> {
    private T t;

    private String errorMsg;

    public T getT() {
        return t;
    }

    public void setT(T t) {
        this.t = t;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ImportErrorMsgDTO.class.getSimpleName() + "[", "]")
                .add("t=" + t)
                .add("errorMsg='" + errorMsg + "'")
                .toString();
    }
}
