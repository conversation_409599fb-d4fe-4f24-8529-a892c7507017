package com.cowell.scib.service.dto.rule;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/1 15:26
 */
@Data
public class HotGoodsImportDTO implements Serializable {
    private static final long serialVersionUID = 7660396133041565066L;

    /**
     * 商品目录来源
     */
    private String goodsSource;

    /**
     * 年份
     */
    private String goodsYear;

    /**
     * 时间维度
     */
    private String goodsTimeDimension;

    /**
     * 时间范围
     */
    private String goodsTimeFrame;

    /**
     * 省份
     */
    private String goodsProvince;

    /**
     * 城市
     */
    private String goodsCity;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 排名
     */
    private String goodsRank;
}
