package com.cowell.scib.service.dto.rule;

import com.cowell.scib.service.AmisDataInInterface;
import com.cowell.scib.service.dto.ErrorUnmanageDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/5 16:48
 */
@Setter@Getter
public class UnmanageTipsDTO implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = -3856260109569314152L;

    /**
     * 提示信息
     */
    List<ErrorUnmanageDTO> errorList;

    /**
     * 成功信息
     */
    private String message;
}
