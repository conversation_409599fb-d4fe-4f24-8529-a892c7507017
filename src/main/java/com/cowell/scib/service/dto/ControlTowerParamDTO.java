package com.cowell.scib.service.dto;


import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

public class ControlTowerParamDTO {

    public ControlTowerParamDTO() {
    }

    public ControlTowerParamDTO(String paramCode) {
        this.paramCode = paramCode;
    }

    @ApiModelProperty(value = "参数编码")
    private String paramCode;

    @ApiModelProperty(value = "参数唯一值")
    private String paramUniqueMark;

    @ApiModelProperty(value = "参数名称")
    private String paramName;

    @ApiModelProperty(value = "参数值")
    private String paramValue;

    @ApiModelProperty(value = "商品类型 0:全部商品 1 指定商品")
    private Byte goodsType;

    @ApiModelProperty(value = "商品组件类型")
    private Integer tagType;

    @ApiModelProperty(value = "时间类型 1:日 2:周 3:月")
    private Byte timeType;

    @ApiModelProperty(value = "周几或者月的天数")
    private List<Integer> days = new ArrayList<>();

    @ApiModelProperty(value = "下拉框, code-name")
    private List<String> dropDownBoxDTOS = new ArrayList<>();

    public String getParamCode() {
        return paramCode;
    }

    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }

    public String getParamUniqueMark() {
        return paramUniqueMark;
    }

    public void setParamUniqueMark(String paramUniqueMark) {
        this.paramUniqueMark = paramUniqueMark;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public Byte getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Byte goodsType) {
        this.goodsType = goodsType;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public Byte getTimeType() {
        return timeType;
    }

    public void setTimeType(Byte timeType) {
        this.timeType = timeType;
    }

    public List<Integer> getDays() {
        return days;
    }

    public void setDays(List<Integer> days) {
        this.days = days;
    }

    public List<String> getDropDownBoxDTOS() {
        return dropDownBoxDTOS;
    }

    public void setDropDownBoxDTOS(List<String> dropDownBoxDTOS) {
        this.dropDownBoxDTOS = dropDownBoxDTOS;
    }
    @Override
    public String toString() {
        return new StringJoiner(", ", ControlTowerParamDTO.class.getSimpleName() + "[", "]")
                .add("paramCode='" + paramCode + "'")
                .add("paramUniqueMark='" + paramUniqueMark + "'")
                .add("paramName='" + paramName + "'")
                .add("paramValue='" + paramValue + "'")
                .add("goodsType=" + goodsType)
                .add("tagType=" + tagType)
                .add("timeType='" + timeType + "'")
                .add("days=" + days)
                .add("dropDownBoxDTOS=" + dropDownBoxDTOS)
                .toString();
    }
}
