package com.cowell.scib.service.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Setter
@Getter
@Data
public class MdmStoreBaseExtendDTO implements Serializable {

    /**
     * 中参店型
     */
    private String zsShop;

    /**
     * 配方店型
     */
    private String psStore;

    /**
     * 经营状态
     */
    private String manageState;

    /**
     * 停止营业日期
     */
    private String handoverDate;

    /**
     * 是否电商门店
     */
    private String manSubject;

    /**
     * b2c门店
     */
    private String b2cShop;

    /**
     * 选址商圈
     */
    private String goodTradingArea;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
