package com.cowell.scib.service.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @ProjectName forest
 * @Description:
 */
public class AuctionSpuBaseInfo implements Serializable {
    private static final long serialVersionUID = -8328811927986154743L;

    @ApiModelProperty(value="采购组")
    private String ekgrp;

    @ApiModelProperty(value="采购组织")
    private String orderorg;

    @ApiModelProperty(value="连锁渠道属性")
    private String purchchan;

    @ApiModelProperty(value = "spuId")
    private Long id;
    @ApiModelProperty(value = "商品名")
    private String name;
    @ApiModelProperty(value = "类目id")
    private Long categoryId;

    @ApiModelProperty(value = "通用名")
    private String curName;
    @ApiModelProperty(value = "通用名助记码")
    private String goodsNo;
    @ApiModelProperty(value = "处方类别")
    private String rxtype;
    @ApiModelProperty(value = "规格")
    private String goodsspec;
    @ApiModelProperty(value = "条码")
    private String barcode;
    @ApiModelProperty(value = "生产厂家")
    private String factoryid;
    @ApiModelProperty(value = "商标")
    private String trademark;
    @ApiModelProperty(value = "功能主治")
    private String treatment;
    @ApiModelProperty(value = "国药准字")
    private String apprdocno;
    @ApiModelProperty(value = "剂型")
    private String dosageformsid;
    @ApiModelProperty(value = "生产许可证号(医疗器械必填)")
    private String prodocno;
    @ApiModelProperty(value = "是否特管药品")
    private String specialctrl;
    @ApiModelProperty(value = "基本剂量单位")
    private String goodsunit;
    @ApiModelProperty(value = "是否为慢病用药")
    private String chronicdisease;
    @ApiModelProperty(value = "dtp商品")
    private String dtpGoods;
    @ApiModelProperty(value = "替代编码")
    private String remarks;
    @ApiModelProperty(value = "渠道属性")
    private String purchchannel;
    @ApiModelProperty(value = "成分")
    private String component;
    @ApiModelProperty(value = "销售属性")
    private String pushlevel;
    @ApiModelProperty(value = "经营范围-中文名")
    private String busiscope;
    @ApiModelProperty(value = "经营范围-标识")
    private String busiscopetag;
    @ApiModelProperty(value = "一级类目id")
    private Long oneCategoryId;
    @ApiModelProperty(value = "二级类目id")
    private Long twoCategoryId;
    @ApiModelProperty(value = "三级类目id")
    private Long threeCategoryId;
    @ApiModelProperty(value = "四级类目id")
    private Long fourCategoryId;
    @ApiModelProperty(value = "品牌logo")
    private String brandLogo;
    @ApiModelProperty(value = "适应症")
    private String indication;
    @ApiModelProperty(value = "注册证号")
    private String regdocno;
    @ApiModelProperty(value = "委托厂家")
    private String byfactoryid;
    @ApiModelProperty(value = "产地")
    private String prodarea;
    @ApiModelProperty(value = "用药人群")
    private String population;
    @ApiModelProperty(value = "保健功能")
    private String healthfunc;
    @ApiModelProperty(value = "有效期")
    private String validperiod;
    @ApiModelProperty(value = "存储条件")
    private String storagecond;
    @ApiModelProperty(value = "注意事项")
    private String attention;
    @ApiModelProperty(value = "执行标准号")
    private String executives;
    @ApiModelProperty(value = "禁忌症")
    private String taboo;
    @ApiModelProperty(value = "结构及组成")
    private String Structure;
    @ApiModelProperty(value = "适用用途")
    private String purpose;
    @ApiModelProperty(value = "型号")
    private String prepspec;
    @ApiModelProperty(value = "线上销售，值为是或空时可以线上销售，为否时不可以在线上销售")
    private String onlinesale;
    @ApiModelProperty(value = "商品是否含兴奋剂")
    private String dopegood;
    @ApiModelProperty(value = "医疗器械商品注册批件")
    private String pic7;
    /**--------------------连锁及属性----------------------------**/
    @ApiModelProperty(value = "是否是处方药 1：是 2：否")
    private String rxsalesind;
    @ApiModelProperty(value = "特殊属性")
    private String specialattributes;
    @ApiModelProperty(value = "经营属性")
    private String goodsline;
    /**--------------------连锁及属性----------------------------**/

    /**
     * 新添加的属性值
     */
    private JSONObject otherProperty;

    /**
     * SPU 其他属性
     */
    private JSONObject spuOtherProperty;

    /**
     * SPU 扩展其他属性
     */
    private JSONObject spuExtendOtherProperty;
    public String getEkgrp() {return ekgrp;}

    public void setEkgrp(String ekgrp) {this.ekgrp = ekgrp;}

    public String getOrderorg() {return orderorg;}

    public void setOrderorg(String orderorg) {this.orderorg = orderorg;}

    public String getPurchchan() {return purchchan;}

    public void setPurchchan(String purchchan) {this.purchchan = purchchan;}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRxsalesind() {
        return rxsalesind;
    }

    public void setRxsalesind(String rxsalesind) {
        this.rxsalesind = rxsalesind;
    }

    public String getProdocno() {
        return prodocno;
    }

    public void setProdocno(String prodocno) {
        this.prodocno = prodocno;
    }

    public String getSpecialctrl() {
        return specialctrl;
    }

    public void setSpecialctrl(String specialctrl) {
        this.specialctrl = specialctrl;
    }

    public String getBrandLogo() {
        return brandLogo;
    }

    public void setBrandLogo(String brandLogo) {
        this.brandLogo = brandLogo;
    }

    public String getCurName() {
        return curName;
    }

    public void setCurName(String curName) {
        this.curName = curName;
    }

    public Long getThreeCategoryId() {
        return threeCategoryId;
    }

    public void setThreeCategoryId(Long threeCategoryId) {
        this.threeCategoryId = threeCategoryId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getBusiscope() {
        return busiscope;
    }

    public void setBusiscope(String busiscope) {
        this.busiscope = busiscope;
    }

    public String getPurchchannel() {
        return purchchannel;
    }

    public void setPurchchannel(String purchchannel) {
        this.purchchannel = purchchannel;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getPushlevel() {
        return pushlevel;
    }

    public void setPushlevel(String pushlevel) {
        this.pushlevel = pushlevel;
    }

    public String getChronicdisease() {
        return chronicdisease;
    }

    public void setChronicdisease(String chronicdisease) {
        this.chronicdisease = chronicdisease;
    }

    public String getDtpGoods() {
        return dtpGoods;
    }

    public void setDtpGoods(String dtpGoods) {
        this.dtpGoods = dtpGoods;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSpecialattributes() {
        return specialattributes;
    }

    public void setSpecialattributes(String specialattributes) {
        this.specialattributes = specialattributes;
    }

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    public String getFactoryid() {
        return factoryid;
    }

    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getTrademark() {
        return trademark;
    }

    public void setTrademark(String trademark) {
        this.trademark = trademark;
    }

    public String getTreatment() {
        return treatment;
    }

    public void setTreatment(String treatment) {
        this.treatment = treatment;
    }

    public String getApprdocno() {
        return apprdocno;
    }

    public void setApprdocno(String apprdocno) {
        this.apprdocno = apprdocno;
    }

    public String getDosageformsid() {
        return dosageformsid;
    }

    public void setDosageformsid(String dosageformsid) {
        this.dosageformsid = dosageformsid;
    }

    public String getRxtype() {
        return rxtype;
    }

    public void setRxtype(String rxtype) {
        this.rxtype = rxtype;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getIndication() {
        return indication;
    }

    public void setIndication(String indication) {
        this.indication = indication;
    }

    public String getBusiscopetag() {
        return busiscopetag;
    }

    public void setBusiscopetag(String busiscopetag) {
        this.busiscopetag = busiscopetag;
    }

    public String getRegdocno() {
        return regdocno;
    }

    public void setRegdocno(String regdocno) {
        this.regdocno = regdocno;
    }

    public String getByfactoryid() {
        return byfactoryid;
    }

    public void setByfactoryid(String byfactoryid) {
        this.byfactoryid = byfactoryid;
    }

    public String getProdarea() {
        return prodarea;
    }

    public void setProdarea(String prodarea) {
        this.prodarea = prodarea;
    }

    public String getPopulation() {
        return population;
    }

    public void setPopulation(String population) {
        this.population = population;
    }

    public String getHealthfunc() {
        return healthfunc;
    }

    public void setHealthfunc(String healthfunc) {
        this.healthfunc = healthfunc;
    }

    public String getValidperiod() {
        return validperiod;
    }

    public void setValidperiod(String validperiod) {
        this.validperiod = validperiod;
    }

    public String getStoragecond() {
        return storagecond;
    }

    public void setStoragecond(String storagecond) {
        this.storagecond = storagecond;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public String getExecutives() {
        return executives;
    }

    public void setExecutives(String executives) {
        this.executives = executives;
    }

    public String getTaboo() {
        return taboo;
    }

    public void setTaboo(String taboo) {
        this.taboo = taboo;
    }

    public String getStructure() {
        return Structure;
    }

    public void setStructure(String structure) {
        Structure = structure;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getPrepspec() {
        return prepspec;
    }

    public void setPrepspec(String prepspec) {
        this.prepspec = prepspec;
    }

    public String getOnlinesale() {
        return onlinesale;
    }

    public void setOnlinesale(String onlinesale) {
        this.onlinesale = onlinesale;
    }

    public String getDopegood() {
        return dopegood;
    }

    public void setDopegood(String dopegood) {
        this.dopegood = dopegood;
    }

    public String getPic7() {
        return pic7;
    }

    public void setPic7(String pic7) {
        this.pic7 = pic7;
    }

    public Long getOneCategoryId() {
        return oneCategoryId;
    }

    public void setOneCategoryId(Long oneCategoryId) {
        this.oneCategoryId = oneCategoryId;
    }

    public Long getTwoCategoryId() {
        return twoCategoryId;
    }

    public void setTwoCategoryId(Long twoCategoryId) {
        this.twoCategoryId = twoCategoryId;
    }

    public Long getFourCategoryId() {
        return fourCategoryId;
    }

    public void setFourCategoryId(Long fourCategoryId) {
        this.fourCategoryId = fourCategoryId;
    }

    public JSONObject getOtherProperty() {
        return otherProperty;
    }

    public void setOtherProperty(JSONObject otherProperty) {
        this.otherProperty = otherProperty;
    }

    public JSONObject getSpuOtherProperty() {
        return spuOtherProperty;
    }

    public void setSpuOtherProperty(JSONObject spuOtherProperty) {
        this.spuOtherProperty = spuOtherProperty;
    }

    public JSONObject getSpuExtendOtherProperty() {
        return spuExtendOtherProperty;
    }

    public void setSpuExtendOtherProperty(JSONObject spuExtendOtherProperty) {
        this.spuExtendOtherProperty = spuExtendOtherProperty;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
