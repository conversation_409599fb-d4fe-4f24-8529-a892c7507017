package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.entityDgms.MdmTask;
import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.entityDgms.StoreGoodsContentsExample;
import com.cowell.scib.enums.ManageStatusEnum;
import com.cowell.scib.enums.SuggestManageStatusEnum;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.extend.StoreGoodsContentsExtendMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class MdmGoodsLineChangeAssemble extends StoreContentsAssemble{
    private final Logger logger = LoggerFactory.getLogger(MdmGoodsLineChangeAssemble.class);
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    // 启用/停用
    @Override
    protected List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        logger.info("mdm商品经营属性变更");
        return storeGoodsContentDTOS;
    }

    protected void dealAndPush(List<StoreGoodsContentDTO> processes) {
        Map<Long, List<StoreGoodsContentDTO>> processMap = processes.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId));
        for (Map.Entry<Long, List<StoreGoodsContentDTO>> entry : processMap.entrySet()) {
            StoreGoodsContentsExample example = new StoreGoodsContentsExample();
            example.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList()));
            Map<String, StoreGoodsContents> existsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(StoreGoodsContents::getGoodsNo, Function.identity(), (k1, k2) -> k1));
            List<StoreGoodsContents> update = new ArrayList<>();
            for (StoreGoodsContentDTO contents : entry.getValue()) {
                StoreGoodsContents old = existsMap.get(contents.getGoodsNo());
                ManageStatusEnum newManageEnum = ManageStatusEnum.getEnumByCode(contents.getManageStatus());
                if (null != old && null != newManageEnum) {
                    if (ManageStatusEnum.ORDER.ordinal() >= newManageEnum.ordinal()) {
                        // 正常(新品、一般,核心必备)改非正常(订购、淘汰、清场、作废、拟淘汰,非商品)
                        // 经营状态= MDM商品经营属性修改后的值建议经营状态=MDM商品经营属性修改后的值
                        old.setManageStatus(newManageEnum.getCode());
                        old.setSuggestManageStatus(newManageEnum.getCode());
                        update.add(old);
                    } else {
                        // 如果生效必备标签=必备:经营状态=经营-必备，建议经营状态=经营-必备
                        // 如果生效必备标签=非必备:经营状态和建议经营状态此时应该都是(订购、淘汰、清场、作废、拟淘汰、非商品)中的一种，
                        // 经营状态=经营-选配:建议经营状态=建议经营-选配
                        if (contents.getNecessaryTag() > 0) {
                            old.setManageStatus(ManageStatusEnum.MANAGE_NECESARY.getCode());
                            old.setSuggestManageStatus(SuggestManageStatusEnum.MANAGE_NECESARY.getCode());
                        } else {
                            old.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                            old.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                        }
                        update.add(old);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(update)) {
                storeGoodsContentsExtendMapper.batchUpdate(update, entry.getKey());
            }
        }
    }

}
