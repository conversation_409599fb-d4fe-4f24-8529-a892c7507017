package com.cowell.scib.service.dto.config;

import com.cowell.scib.service.AmisDataInInterface;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 必备层级配置表
 */
@Data
public class NecessaryLevelConfigDTO implements AmisDataInInterface,Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 必备标签
     */
    private String necessaryTag;

    /**
     * 必备标签名称
     */
    private String necessaryTagName;

    /**
     * 组织层级
     */
    private Integer orgLevel;

    /**
     * 区域层级
     */
    private Integer areaLevel;
    /**
     * 组织层级
     */
    private String orgLevelDesc;

    /**
     * 区域层级
     */
    private String areaLevelDesc;

    /**
     * 店型
     */
    private String storeType;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 更新人
     */
    private String updateBy;

}
