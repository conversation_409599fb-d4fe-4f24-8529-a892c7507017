package com.cowell.scib.service.dto.rule;

import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.service.AmisDataInInterface;
import com.cowell.scib.service.dto.StoreComponentQueryParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date  2025年09月04日10:05:59
 */
@Data
public class ScibCommonResultDTO implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = 5431785427727168719L;

    /**
     * 门店id集合
     */
   List<Long> storeIdList;

    /**
     * 组件器参数
     */
   StoreComponentQueryParam queryParam;

   /**
    * 门店商品信息
    */
   List<String> storeGoodsContents;


}
