package com.cowell.scib.service.dto;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class MdmGoodsInfo {
    /**
     *  公司编码
     */
    @JSONField(name = "mdm_company_code")
    private String mdmCompanyCode;
    /**
     *   ⻔店编码
     */
    @JSONField(name = "storeno")
    private String storeNo;
    /**
     *  商品编码
     */
    @JSONField(name = "goodsno")
    private String goodsNo;
    /**
     *  最⼩陈列量
     */
    @JSONField(name = "minimumdisplay")
    private String minimumdisplay;
    /**
     *  处⽅登记品(是和否)
     */
    @JSONField(name = "preregister")
    private String preregister;
    /**
     *  | 随访品(是和否)
     */
    @JSONField(name = "followproduct")
    private String followProduct;
    /**
     *  |冷链品(是和否)
     */
    @JSONField(name = "coldproduct")
    private String coldProduct;
    /**
     *   上翻品(是和否)
     */
    @JSONField(name = "upturnedproduct")
    private String upturnedProduct;
    /**
     *   禁⽌配送
     */
    @JSONField(name = "distribind")
    private String distribind;
    /**
     *   禁⽌返仓
     */
    @JSONField(name = "rtwind")
    private String rtwind;
    /**
     *  禁⽌销售
     */
    @JSONField(name = "retailind")
    private String retailind;
    /**
     *   禁⽌请货
     */
    @JSONField(name = "applyfor")
    private String applyfor;
    /**
     * 禁⽌调拨
     */
    @JSONField(name = "transfer")
    private String transfer;
    /**
     * 请货最⼤上限
     */
    @JSONField(name = "maxreqqty")
    private String maxreqqty;
    /**
     * 销售属性
     */
    @JSONField(name = "pushlevel")
    private String pushLevel;
}
