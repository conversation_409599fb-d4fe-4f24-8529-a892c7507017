package com.cowell.scib.service.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Objects;

public class OrgSimpleDTO implements Serializable {

    private static final long serialVersionUID = -5328998234478247681L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 组织机构名
     */
    private String name;

    /**
     * 组织机构简称
     */
    private String shortName;

    /**
     * 组织机构路径
     */
    private String orgPath;

    /**
     * 组织机构类型
     */
    private Integer type;

    /**
     * 上级组织机构id
     */
    private Long parentId;

    /**
     * 映射的z组织id,组织为门店则为storeid 是连锁为businessid
     */
    private Long outId;

    /**
     * 0-无权限 1-全部权限 2-部分权限
     */
    private Integer isFullScope;

    /**
     * SAP组织编码
     */
    private String sapcode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getOutId() {
        return outId;
    }

    public void setOutId(Long outId) {
        this.outId = outId;
    }

    public Integer getIsFullScope() {
        return isFullScope;
    }

    public void setIsFullScope(Integer isFullScope) {
        this.isFullScope = isFullScope;
    }

    public String getSapcode() {
        return sapcode;
    }

    public void setSapcode(String sapcode) {
        this.sapcode = sapcode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrgSimpleDTO that = (OrgSimpleDTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
