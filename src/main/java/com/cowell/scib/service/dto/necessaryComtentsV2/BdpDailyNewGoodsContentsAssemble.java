package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.MdmTask;
import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.entityDgms.StoreGoodsContentsExample;
import com.cowell.scib.enums.ManageStatusEnum;
import com.cowell.scib.enums.StoreGoodsEffectStatusEnum;
import com.cowell.scib.enums.SuggestManageStatusEnum;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.extend.StoreGoodsContentsExtendMapper;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BdpDailyNewGoodsContentsAssemble extends StoreContentsAssemble{
    private final Logger logger = LoggerFactory.getLogger(BdpDailyNewGoodsContentsAssemble.class);
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    // 启用/停用
    @Override
    protected List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        logger.info("组装大数据推送 - 每天店级新品弥补+新品到期转选配");
        return storeGoodsContentDTOS;
    }

    protected void dealAndPush(List<StoreGoodsContentDTO> processes) {
        Map<Long, List<StoreGoodsContentDTO>> processMap = processes.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId));
        for (Map.Entry<Long, List<StoreGoodsContentDTO>> entry : processMap.entrySet()) {
            StoreGoodsContentsExample example = new StoreGoodsContentsExample();
            example.createCriteria().andStoreIdEqualTo(entry.getKey()).andGoodsNoIn(entry.getValue().stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList()));
            Map<String, StoreGoodsContents> existsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(StoreGoodsContents::getGoodsNo, Function.identity(), (k1, k2) -> k1));
            List<StoreGoodsContents> insert = new ArrayList<>();
            for (StoreGoodsContentDTO contents : entry.getValue()) {
                StoreGoodsContents old = existsMap.get(contents.getGoodsNo());
                if (null == old) {
                    // 店级新品弥补：大数据给门店、商品 dgms查门店商品，存在舍弃；不存在新增，经营状态 = 店级新品
                    StoreGoodsContents newContent = new StoreGoodsContents();
                    BeanUtils.copyProperties(old, newContent);
                    newContent.setNecessaryTag(0);
                    newContent.setNecessaryTagName("");
                    newContent.setManageStatus(ManageStatusEnum.STORE_NEW_GOODS.getCode());
                    newContent.setSuggestManageStatus(SuggestManageStatusEnum.STORE_NEW_GOODS.getCode());
                    newContent.setMinDisplayQuantity(BigDecimal.ZERO);
                    newContent.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                    Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(contents.getStoreId());
                    if (optional.isPresent()) {
                        newContent.setStoreOrgId(optional.get().getId());
                        newContent.setStoreCode(optional.get().getSapCode());
                    }
                    insert.add(newContent);
                }
            }
            if (CollectionUtils.isNotEmpty(insert)) {
                List<Long> storeGoodsIds = getStoreGoodsIds(insert.size());
                insert.forEach(v -> v.setId(storeGoodsIds.remove(0)));
                Lists.partition(insert, Constants.INSERT_MAX_VALUE).forEach(v -> storeGoodsContentsExtendMapper.batchInsert(v));
            }
        }
    }

}
