package com.cowell.scib.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/29 16:52
 */
@Data
public class DevelopModuleDTO implements Serializable {
    private static final long serialVersionUID = 837627818849625149L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 模块编码（唯一）
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 启用状态  0-停用 1-启用
     */
    private Byte useStatus;

    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 更新人名字，对应登录返回的name字段
     */
    private String updatedName;
    /**
     * 更新时间（操作时间）
     */
    private Date gmtUpdate;
}
