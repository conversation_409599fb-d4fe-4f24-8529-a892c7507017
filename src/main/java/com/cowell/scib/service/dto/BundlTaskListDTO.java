package com.cowell.scib.service.dto;

import com.cowell.scib.service.AmisDataInInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/9 17:50
 */
@Data
public class BundlTaskListDTO extends BundlTaskInfoDTO implements Serializable, AmisDataInInterface {
    private static final long serialVersionUID = 3324039640574681366L;

    private String taskTypeDesc;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    private String gmtCreate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "任务创建人")
    private String createdName;


    /**
     * 提交人
     */
    @ApiModelProperty(value = "任务提交人")
    private String commitName;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "任务提交时间")
    private String gmtCommit;

    /**
     * 计算完成时间
     */
    @ApiModelProperty(value = "任务计算完成时间")
    private String gmtCalculated;


    /**
     * 下发人
     */
    @ApiModelProperty(value = "任务下发人")
    private String issuedName;

    /**
     * 任务下发时间
     */
    @ApiModelProperty(value = "任务下发时间")
    private String gmtIssued;


    /**
     * 作废人
     */
    @ApiModelProperty(value = "任务作废人")
    private String cancelName;

    /**
     * 任务作废时间
     */
    @ApiModelProperty(value = "任务作废时间")
    private String gmtCancel;

    @ApiModelProperty(value = "组货商品大类")
    private String bundlGoodsBigKinds;

    @ApiModelProperty(value = "组货公司")
    private String bundlBusiness;

    @ApiModelProperty(value = "组货店型")
    private String bundlStore;

    @ApiModelProperty(value = "中参店型")
    private String zsStore;

    @ApiModelProperty(value = "配方店型")
    private String disposeStore;

    /**
     * 1 暂存 2 计算中 3 计算完成 4已作废 5已更新
     */
    @ApiModelProperty(value = "组货任务状态  1 暂存 2 计算中 3 计算完成 4已作废 5已更新")
    private String taskStatusDesc;

    @ApiModelProperty(value = "组货任务状态  1 暂存 2 计算中 3 计算完成 4已作废 5已更新")
    private Byte taskStatus;

    @ApiModelProperty(value = "组货任务是否可编辑  true-可编辑  false-不可编辑")
    private Boolean taskEditAble;

    @ApiModelProperty(value = "组货任务是否可查看  true-可查看  false-不可查看")
    private Boolean taskViewAble;

    @ApiModelProperty(value = "组货任务是否可作废  true-可作废  false-不可作废")
    private Boolean taskCancelAble;

    @ApiModelProperty(value = "组货结果是否可编辑  true-可编辑  false-不可编辑")
    private Boolean resultEditAble;

    @ApiModelProperty(value = "组货结果是否可查看  true-可查看  false-不可查看")
    private Boolean resultViewAble;

    @ApiModelProperty(value = "组货结果是否更新MDM  true-可更新  false-不可更新")
    private Boolean resultMdmAble;
}
