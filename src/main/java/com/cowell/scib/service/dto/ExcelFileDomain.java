package com.cowell.scib.service.dto;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2023/2/27 19:34
 */
public class ExcelFileDomain {

    /**
     * 组货商品
     * @return
     */
    public static LinkedHashMap<String, String> getGoodsGroupMap(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("商品编码", "goodsNo");
        return map;
    }

    /**
     * 热销品
     * @return
     */
    public static LinkedHashMap<String, String> getHotGoodsMap(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("商品目录来源", "goodsSource");
        map.put("年份", "goodsYear");
        map.put("时间维度", "goodsTimeDimension");
        map.put("时间范围", "goodsTimeFrame");
        map.put("省份", "goodsProvince");
        map.put("城市", "goodsCity");
        map.put("条码", "barCode");
        map.put("商品名称", "goodsName");
        map.put("排名", "goodsRank");
        return map;
    }

    /**
     * 统筹医保目录
     * @return
     */
    public static LinkedHashMap<String, String> getPlanGoodsMap(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("省份", "province");
        map.put("城市", "city");
        map.put("商品编码", "goodsNo");
        return map;
    }

    /**
     * 废弃品或者经营属性不合格的品
     * @return
     */
    public static LinkedHashMap<String, String> getDiscardGoods(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("商品", "goodsNo");
        map.put("企业", "comId");
        map.put("经营属性", "goodsline");
        return map;
    }

    /**
     * Mdm闭店的门店
     * @return
     */
    public static LinkedHashMap<String, String> getMdmCloseStore(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("门店编码", "sapCode");
        return map;
    }

    /**
     * 门店不经营类型
     * @return
     */
    public static LinkedHashMap<String, String> getUnmanageMap(){
        LinkedHashMap map = new LinkedHashMap();
        map.put("门店编码", "storeCode");
        map.put("门店名称", "storeName");
        map.put("无法经营的大类编码", "categoryId");
        map.put("无法经营的中类编码", "middleCategoryId");
        map.put("无法经营的小类编码", "smallCategoryId");
        map.put("无法经营的子类编码", "subCategoryId");
        return map;
    }

}
