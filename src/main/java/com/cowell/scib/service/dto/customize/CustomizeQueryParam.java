package com.cowell.scib.service.dto.customize;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomizeQueryParam extends AmisPageParam implements Serializable {

    @ApiModelProperty(value = "查询类型")
    private String propertyCode;

    @ApiModelProperty(value = "商品编码")
    private String goodsNos;

    @ApiModelProperty(value = "平台orgId")
    private Long platformOrgId;

    @ApiModelProperty(value = "公司orgId")
    private Long companyOrgId;

    @ApiModelProperty(value = "店型编码")
    private String storeType;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

}
