package com.cowell.scib.service.dto.necessaryContents;

import com.beust.jcommander.internal.Lists;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NecessaryPlatformAssemble extends NecessaryAssemble {


    @Override
    public String deleteNecessary() throws Exception {
        if (!necessaryContentsService.checkModifyAble(delParam.getPlatformOrgId())){
            throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
        }
//        1、平台表删除该平台*店型*SKU行。
//        2、用平台*店型取门店列表，清空一店一目表这些门店*SKU的配置类型、最小陈列量
        NecessaryPlatformGoodsExample example = new NecessaryPlatformGoodsExample();
        example.createCriteria().andPlatformOrgIdEqualTo(delParam.getPlatformOrgId()).andIdIn(delParam.getNecessaryIds());
        List<NecessaryPlatformGoods> necessaryGoodsList = necessaryPlatformGoodsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryGoodsList)) {
            throw new AmisBadRequestException("所选单据已全部被删除");
        }
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        // 按照店型分组
        Map<String, List<NecessaryPlatformGoods>> necessaryGoodsMap = necessaryGoodsList.stream().collect(Collectors.groupingBy(NecessaryPlatformGoods::getStoreType));
        if (necessaryGoodsList.size() < delParam.getNecessaryIds().size()) {
            List<Long> existsIds = necessaryGoodsList.stream().map(v -> v.getId()).collect(Collectors.toList());
            String errorIds = delParam.getNecessaryIds().stream().filter(v -> !existsIds.contains(v)).map(v -> v.toString()).collect(Collectors.joining(","));
            throw new AmisBadRequestException("所选序号:" + errorIds + "已全部被删除");
        }
        // set 平台信息
        NecessaryAddParam param = new NecessaryAddParam();
        param.setNecessaryTag(delParam.getNecessaryTag());
        setProp(param, userDTO, CacheVar.getPlatformByOrgId(delParam.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
        checkFullScope();
        List<MdmStoreExDTO> storeInfos = getMdmStroeExDTOS(false);
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        necessaryGoodsMap.forEach((storeType,v) -> {
            List<Long> storeIds = storeInfos.stream().filter(s -> storeType.equals(s.getPlatStoreTypeCode()) || storeType.equals(s.getZsStoreTypeCode())|| storeType.equals(s.getPfStoreTypeCode())).map(MdmStoreBaseDTO::getStoreId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeIds)) {
                v.forEach(goo -> {
                    NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
                    BeanUtils.copyProperties(goo, goodsDTO);
                    List<Long> storeIdList = Optional.ofNullable(delGoodsStoreMap.get(goodsDTO)).orElse(new ArrayList<>());
                    storeIdList.addAll(storeIds);
                    delGoodsStoreMap.put(goodsDTO, storeIdList);
                });
            }
        });
        necessaryPlatformGoodsMapper.deleteByExample(example);
        if (MapUtils.isEmpty(delGoodsStoreMap)) {
            return "无符合条件的门店，仅删除必备目录，不创建MDM任务。";
        }
        return delStroeGoods(delGoodsStoreMap);
    }

    @Override
    public NecessaryAssemble checkFullScope() {
        List<Long> orgDataScopeList = necessaryContentsService.isFullScopeByOrgId(com.google.common.collect.Lists.newArrayList(platformOrg.getId()), userDTO.getUserId());
        if (!orgDataScopeList.contains(platformOrg.getId())) {
            throw new AmisBadRequestException("没有当前平台权限");
        }
        return this;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NecessaryAssemble assemble() throws Exception {
        List<MdmStoreExDTO> mdmStroeExDTOS = getMdmStroeExDTOS(true);
        // 补充店型
        List<Long> categoryIds = getCategoryByStoreTypes(mdmStroeExDTOS.get(0));
        List<NecessaryCommonGoodsDTO> list = assembleCommon();
        // 错误的店型商品
        List<String> errorStoreTypeGoodsNos = new ArrayList<>();
        List<NecessaryPlatformGoods> platformGoodsList = new ArrayList<>();
        List<NecessaryCommonGoodsDTO> addList = new ArrayList<>();
        for (NecessaryCommonGoodsDTO goodsDTO : list) {
            if (!categoryIds.contains(goodsDTO.getCategoryId()) && !categoryIds.contains(goodsDTO.getMiddleCategoryId())) {
                errorStoreTypeGoodsNos.add(goodsDTO.getGoodsNo());
                continue;
            }
            for (String storeType : param.getStoreTypes()) {
                NecessaryPlatformGoods platformGoods = new NecessaryPlatformGoods();
                BeanUtils.copyProperties(goodsDTO, platformGoods);
                platformGoods.setStoreType(storeType);
                platformGoodsList.add(platformGoods);
            }
            addList.add(goodsDTO);
        }
        if (CollectionUtils.isNotEmpty(errorStoreTypeGoodsNos)) {
            throw new AmisBadRequestException("商品:" + errorStoreTypeGoodsNos.stream().collect(Collectors.joining(",")) + "与店型冲突,无法加入,请修改");
        }
        List<String> goodsNos = platformGoodsList.stream().map(NecessaryPlatformGoods::getGoodsNo).collect(Collectors.toList());
        List<Long> storeIds = mdmStroeExDTOS.stream().map(MdmStoreExDTO::getStoreId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new AmisBadRequestException("无符合条件的门店，不可新增必备。");
        }
        Set<String> storeTypes = new HashSet<>();
        param.getStoreTypes().forEach(s -> {
            List<String> platStoreTypes = storeTypeByPlat(s);
            storeTypes.addAll(CollectionUtils.isEmpty(platStoreTypes) ? new HashSet<>() : platStoreTypes);
        });
        //        ④查店型必备表：平台、店型对应的多个【组货店型】或者【中参店型】（根据SKU商品大类确定取哪类店型）、SKU，存在则删除N行。
        NecessaryStoreTypeGoodsExample storeTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
        storeTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getId()).andStoreTypeIn(CollectionUtils.isEmpty(storeTypes) ? param.getStoreTypes() : Lists.newArrayList(storeTypes)).andGoodsNoIn(goodsNos);
        necessaryStoreTypeGoodsMapper.deleteByExample(storeTypeGoodsExample);
//        ⑤查店型选配表：平台、店型对应的多个【组货店型】或者【中参店型】（根据SKU商品大类确定取哪类店型）、SKU，存在则删除N行。
        NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
        chooseStoreTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getId()).andStoreTypeIn(CollectionUtils.isEmpty(storeTypes) ? param.getStoreTypes() : Lists.newArrayList(storeTypes)).andGoodsNoIn(goodsNos);
        necessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);

//        ⑥查单店必备表：平台、店型下的门店list、SKU，存在则删除N行。
        NecessarySingleStoreGoodsExample singleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
        singleStoreGoodsExample.createCriteria().andPlatformOrgIdEqualTo(platformOrg.getId()).andStoreIdIn(storeIds).andGoodsNoIn(goodsNos);
        necessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);

        // 添加
        necessaryPlatformGoodsExtendMapper.batchInsert(platformGoodsList);
        message = addStoreGoods(storeIds, addList.stream().collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
        return this;
    }

    @Override
    public List<MdmStoreExDTO> getMdmStroeExDTOS(boolean add) {
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        if (add) {
            storeInfos.addAll(necessaryContentsService.getPlatformStoreInfo(platformOrg.getPlatformOrgId(), null, userDTO.getUserId()));
        } else {
            storeInfos.addAll(CacheVar.getStoreListByPlatformOrgId(platformOrg.getId()).stream().filter(v -> StringUtils.isNotBlank(v.getSapCode())).map(v -> CacheVar.storeExMap.get(v.getSapCode())).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("没有获取到平台下配置的门店");
        }
        if (!add) {
            return storeInfos;
        }
        storeInfos = storeInfos.stream().filter(v -> param.getStoreTypes().contains(v.getPlatStoreTypeCode()) || param.getStoreTypes().contains(v.getZsStoreTypeCode()) || param.getStoreTypes().contains(v.getPfStoreTypeCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeInfos)) {
            throw new AmisBadRequestException("没有获取到店型:" + param.getStoreTypeNames() + "下的门店");
        }
        return storeInfos;
    }

    @Override
    public String check() {
        if (CollectionUtils.isEmpty(param.getStoreTypes())) {
            throw new AmisBadRequestException("请选择店型");
        }
        // 平台店型==0或==传入的店型size,否则说明店型不都属于平台或者中参
        RuleParam ruleParam = new RuleParam();
        // 写死  查店型用
        ruleParam.setScopeCode("TaskCreate");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
        // 平台必备店型
        Map<String, String> platStoreGroup = ruleEnum.get("PlatStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 中参必备店型
        Map<String, String> zsStoreGroup = ruleEnum.get("ZsStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 配方必备店型
        Map<String, String> pfStoreGroup = ruleEnum.get("PfStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));

        int platStoreTypeSize = param.getStoreTypes().stream().filter(v -> platStoreGroup.keySet().contains(v)).collect(Collectors.toList()).size();
        int pfStoreTypeSize = param.getStoreTypes().stream().filter(v -> pfStoreGroup.keySet().contains(v)).collect(Collectors.toList()).size();
        int zsStoreTypeSize = param.getStoreTypes().stream().filter(v -> zsStoreGroup.keySet().contains(v)).collect(Collectors.toList()).size();
        if (!(
                (platStoreTypeSize == 0 || platStoreTypeSize == param.getStoreTypes().size())
                && (pfStoreTypeSize == 0 || pfStoreTypeSize == param.getStoreTypes().size())
                && (zsStoreTypeSize == 0 || zsStoreTypeSize == param.getStoreTypes().size())
        )) {
            throw new AmisBadRequestException("店型类型不匹配:只能全为中参店型或平台店型或配方店型");
        }
        String checkMsg = super.check();
        StringBuilder message = new StringBuilder();
        if (StringUtils.isNotBlank(checkMsg)) {
            message.append(checkMsg);
        }
        List<String> existsGoodsNos = necessaryPlatformGoodsExtendMapper.selectExistsGoods(param.getPlatformOrgId(), param.getStoreTypes(), param.getGoodsNos());
        if (CollectionUtils.isNotEmpty(existsGoodsNos)) {
            message.append(existsGoodsNos.stream().distinct().collect(Collectors.joining("、")) + "，已在平台必备中存在，不可添加。");
        }
        if (message.length() > 0) {
            if (!param.getExistsIgnore()) {
                message.append("是否排除掉重复商品，继续添加？");
                return message.toString();
            } else {
                param.setGoodsNos(param.getGoodsNos().stream().filter(v -> !existsGoodsNos.contains(v)).distinct().collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                    throw new AmisBadRequestException("排除重复后没有数据了");
                }
            }
        }
        return "";
    }
}
