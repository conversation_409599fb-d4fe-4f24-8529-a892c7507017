package com.cowell.scib.service.dto;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class ListToExcelMultiSheetDTO<T> {
    /**
     * 数据源
     */
    private List<T>  listGroup;
    /**
     * sheetName
     */
    private String sheetName;
    /**
     * 表头映射
     */
    private LinkedHashMap<String, String> fieldMap;
    /**
     * 特殊内容分组
     */
    private Map<String, List<T>> infoMap;
    /**
     * 特殊表头映射
     */
    private LinkedHashMap<String, String> infoFieldMap;

    public Map<String, List<T>> getInfoMap() {
        return infoMap;
    }

    public void setInfoMap(Map<String, List<T>> infoMap) {
        this.infoMap = infoMap;
    }

    public LinkedHashMap<String, String> getInfoFieldMap() {
        return infoFieldMap;
    }

    public void setInfoFieldMap(LinkedHashMap<String, String> infoFieldMap) {
        this.infoFieldMap = infoFieldMap;
    }

    public List<T> getListGroup() {
        return listGroup;
    }

    public void setListGroup(List<T> listGroup) {
        this.listGroup = listGroup;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public LinkedHashMap<String, String> getFieldMap() {
        return fieldMap;
    }

    public void setFieldMap(LinkedHashMap<String, String> fieldMap) {
        this.fieldMap = fieldMap;
    }

    @Override
    public String toString() {
        return "ListToExcelMultiSheetDTO{" +
            "listGroup=" + listGroup +
            ", sheetName='" + sheetName + '\'' +
            ", fieldMap=" + fieldMap +
            '}';
    }
}
