package com.cowell.scib.service.dto.TrackResult;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class NewStoreGoodsData {

    @ExcelProperty(value = "商品编码",index = 0)
    private String goodsNo;

    @ExcelProperty(value = "是否经营",index = 1)
    private String operateAble;

    @ExcelProperty(value = "首次备货数量",index = 2)
    private String stockUpQty;

    @ExcelProperty(value = "错误原因",index = 3)
    private String errorReason;

}
