package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.config.IdGenConfig;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.ManageStatusEnum;
import com.cowell.scib.enums.MdmTaskPushStatusEnum;
import com.cowell.scib.enums.MdmTaskSourceEnum;
import com.cowell.scib.enums.StoreContentBizTypeEnum;
import com.cowell.scib.mapperDgms.DgmsCommonLockMapper;
import com.cowell.scib.mapperDgms.DgmsCommonQueueMapper;
import com.cowell.scib.mapperDgms.MdmTaskDetailMapper;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mapperDgms.extend.DgmsCommonLockExtendMapper;
import com.cowell.scib.mapperDgms.extend.DgmsCommonQueueExtendMapper;
import com.cowell.scib.mapperDgms.extend.MdmTaskDetailExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.TocService;
import com.cowell.scib.service.dto.necessaryContents.NecessaryTaskMbDTO;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.MBUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public abstract class StoreContentsAssemble {
    private final Logger logger = LoggerFactory.getLogger(StoreContentsAssemble.class);

    protected static final String BUSINESS_SCOPE_CACHE = "BUSINESS-SCOPE-CACHE-";
    protected static final String DGMS_STORE_GOODS_DEALING = "DGMS-STORE-GOODS-DEALING-STORE-";
    protected static final String DGMS_STORE_GOODS_DEALING_QUEUE = "DGMS-STORE-GOODS-DEALING-QUEUE-";

    @Resource
    protected RedissonClient redissonClient;
    @Resource
    protected TocService tocService;
    @Resource
    protected MdmTaskMapper mdmTaskMapper;
    @Resource
    protected MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;
    @Resource
    protected MdmTaskDetailMapper mdmTaskDetailMapper;
    @Resource
    protected DgmsCommonLockMapper dgmsCommonLockMapper;
    @Resource
    protected DgmsCommonQueueMapper dgmsCommonQueueMapper;
    @Resource
    protected DgmsCommonLockExtendMapper dgmsCommonLockExtendMapper;
    @Resource
    protected DgmsCommonQueueExtendMapper dgmsCommonQueueExtendMapper;
    @Resource
    protected StoreContentsFactory factory;
    @Resource
    private MBUtils mbUtils;

    @Value("${dgms.mb.necessary.content.properties:}")
    private String necessaryMbPrpo;

    public void work(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        try {
            logger.info("storeGoodsContentDTOS.size:{}",storeGoodsContentDTOS.size());
            Optional<StoreGoodsContentDTO> any = storeGoodsContentDTOS.stream().filter(v -> null == StoreContentBizTypeEnum.getEnumByCode(v.getBizType())).findAny();
            if (any.isPresent()) {
                throw new AmisBadRequestException("业务类型为空");
            }
            List<StoreGoodsContentDTO> filtered = filterDealing(storeGoodsContentDTOS);
            if (CollectionUtils.isEmpty(filtered)) {
                logger.info("商品均在处理中");
                return;
            }
            List<StoreGoodsContentDTO> processes = assembleStoreInfo(filtered);
            if (CollectionUtils.isEmpty(processes)) {
                logger.info("没有需要插入的一店一目数据");
                return;
            }
            dealAndPush(processes);
        } catch (Exception e) {
            logger.error("添加必备目录失败:", e);
        } finally {
            dealCache(storeGoodsContentDTOS);
        }
    }

    protected String goodsline;
    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    protected abstract List<StoreGoodsContentDTO> assembleStoreInfo(List<StoreGoodsContentDTO> storeGoodsContentDTOS);

    protected abstract void dealAndPush(List<StoreGoodsContentDTO> processes);
    /**
     * 过滤正在处理的门店+商品
     * @param storeGoodsContentDTOS
     */
    protected synchronized List<StoreGoodsContentDTO> filterDealing(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
//        RList<String> rList = getDealingCache();
        List<String> rList = getDealingCache();
        List<String> willAddCache = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rList)) {
//            List<String> cache = rList.readAll();
            List<StoreGoodsContentDTO> dealingList = new ArrayList<>();
            Iterator<StoreGoodsContentDTO> iterator = storeGoodsContentDTOS.iterator();
            while (iterator.hasNext()) {
                StoreGoodsContentDTO next = iterator.next();
                if (rList.contains(next.getStoreId() + "-" + next.getGoodsNo())) {
                    dealingList.add(next);
                    iterator.remove();
                } else {
                    willAddCache.add(next.getStoreId() + "-" + next.getGoodsNo());
                }
            }
            if (CollectionUtils.isNotEmpty(dealingList)) {
                logger.info("门店:{},商品:{}在处理中", dealingList.get(0).getStoreId(), dealingList.stream().map(StoreGoodsContentDTO::getGoodsNo).collect(Collectors.toList()));
                pushDealQueueBatch(dealingList);
            }
        } else {
            willAddCache.addAll(storeGoodsContentDTOS.stream().map(v -> v.getStoreId() + "-" + v.getGoodsNo()).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(willAddCache)) {
            pushLockList(willAddCache);
//            rList.addAll(willAddCache);
//            rList.expire(1L, TimeUnit.HOURS);
        }
        return storeGoodsContentDTOS;
    }

    private void pushLockList(List<String> willAddCache) {
        List<DgmsCommonLock> insert = willAddCache.stream().map(v -> {
            DgmsCommonLock lock = new DgmsCommonLock();
            lock.setLockKey(v);
            lock.setThreadId(0L);
            lock.setEntryCount(0);
            lock.setHostIp("");
            return lock;
        }).collect(Collectors.toList());
        Lists.partition(insert, Constants.INSERT_MAX_VALUE).forEach(v -> {
            dgmsCommonLockExtendMapper.batchInsert(v);
        });
    }

    //    protected RList<String> getDealingCache(){
//        RList<String> rList = redissonClient.getList(DGMS_STORE_GOODS_DEALING);
//        return rList;
//    }
    protected List<String> getDealingCache(){
        return dgmsCommonLockExtendMapper.selectLockKeyByExample(null);
    }
//    protected void batchDelCache(List<String> goodsNos){
//        RBatch batch = redissonClient.createBatch();
//        RListAsync<String> rList = batch.getList(DGMS_STORE_GOODS_DEALING);
//        goodsNos.forEach(rList::removeAsync);
//        batch.execute();
//    }
    protected void batchDelCache(List<String> lockKeys) {
        DgmsCommonLockExample example = new DgmsCommonLockExample();
        example.createCriteria().andLockKeyIn(lockKeys);
        dgmsCommonLockMapper.deleteByExample(example);
    }
    protected RDeque<StoreGoodsContentDTO> pushDealQueue(StoreGoodsContentDTO contentDTO){
        RDeque<StoreGoodsContentDTO> rList = redissonClient.getDeque(DGMS_STORE_GOODS_DEALING_QUEUE + contentDTO.getStoreId() + "-" + contentDTO.getGoodsNo());
        rList.expireAsync(1L, TimeUnit.HOURS);
        rList.push(contentDTO);
        return rList;
    }
//    protected StoreGoodsContentDTO popDealQueue(StoreGoodsContentDTO contentDTO){
//        RList<StoreGoodsContentDTO> rList = redissonClient.getList(DGMS_STORE_GOODS_DEALING_QUEUE + contentDTO.getStoreId() + "-" + contentDTO.getGoodsNo());
//        return CollectionUtils.isNotEmpty(rList) ? rList.remove(0) : null;
//    }
    protected StoreGoodsContentDTO popDealQueue(StoreGoodsContentDTO contentDTO){
        DgmsCommonQueueExample example = new DgmsCommonQueueExample();
        example.createCriteria().andQueueKeyEqualTo(contentDTO.getStoreId() + "-" + contentDTO.getGoodsNo());
        example.setLimit(1);
        example.setOrderByClause(" id asc");
        List<DgmsCommonQueue> queueList = dgmsCommonQueueMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(queueList)) {
            return null;
        }
        DgmsCommonQueue pop = queueList.get(0);
        dgmsCommonQueueMapper.deleteByPrimaryKey(pop.getId());
        return JSONObject.parseObject(pop.getQueueValue(), StoreGoodsContentDTO.class);
    }

    protected List<StoreGoodsContentDTO> batchPopDealQueue(List<StoreGoodsContentDTO> contentDTOS){
        List<String> queueKeys = contentDTOS.stream().map(v -> v.getStoreId() + "-" + v.getGoodsNo()).collect(Collectors.toList());
        List<Long> ids = dgmsCommonQueueExtendMapper.selectMinIdByKeys(queueKeys);
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        DgmsCommonQueueExample example = new DgmsCommonQueueExample();
        example.createCriteria().andIdIn(ids);
        List<DgmsCommonQueue> queueList = dgmsCommonQueueMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(queueList)) {
            return null;
        }
        dgmsCommonQueueMapper.deleteByExample(example);
        return queueList.stream().map(v -> JSONObject.parseObject(v.getQueueValue(), StoreGoodsContentDTO.class)).collect(Collectors.toList());
    }

//    protected RBatch pushDealQueueBatch(List<StoreGoodsContentDTO> contentDTOS){
//        RBatch batch = redissonClient.createBatch();
//        contentDTOS.forEach(v -> {
//            RDequeAsync<StoreGoodsContentDTO> deque = batch.getDeque(DGMS_STORE_GOODS_DEALING_QUEUE + v.getStoreId() + "-" + v.getGoodsNo());
//            deque.addAsync(v);
//            deque.expireAsync(1L, TimeUnit.HOURS);
//        });
//        batch.execute();
//        return batch;
//    }
    protected void pushDealQueueBatch(List<StoreGoodsContentDTO> contentDTOS){
        List<DgmsCommonQueue> queueList = contentDTOS.stream().map(v -> {
            DgmsCommonQueue queue = new DgmsCommonQueue();
            queue.setQueueKey(v.getStoreId() + "-" + v.getGoodsNo());
            queue.setQueueValue(JSON.toJSONString(v));
            return queue;
        }).collect(Collectors.toList());
        Lists.partition(queueList, Constants.INSERT_MAX_VALUE).forEach(v -> dgmsCommonQueueExtendMapper.batchInsert(v));
    }

    protected List<Long> getStoreGoodsIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_STORE_GOODS_INFO, count);
    }

    protected List<Long> getMdmTaskDetailIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_MDM_TASK_DETAIL, count);
    }
    protected List<Long> getStoreGoodsProcessIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_STORE_GOODS_PROCESS, count);
    }

    public void pushMdm(List<MdmTaskDetail> taskDetails) throws Exception {
        if (CollectionUtils.isEmpty(taskDetails)) {
            return;
        }
        MdmTask mdmTask = mdmTaskMapper.selectByPrimaryKey(taskDetails.get(0).getTaskId());
        if (null == mdmTask) {
            logger.info("下发海典失败,没有查询到主单, taskId:{}",taskDetails.get(0).getTaskId());
            return;
        }
        // 默认成功
        MdmTaskDetailExample example = new MdmTaskDetailExample();
        example.createCriteria().andTaskIdEqualTo(mdmTask.getId()).andIdIn(taskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()));
        MdmTaskDetail detail = new MdmTaskDetail();
        detail.setExtend("处理成功");
        detail.setPushStatus(MdmTaskPushStatusEnum.SUCCESS.getCode());
        mdmTaskDetailMapper.updateByExampleSelective(detail, example);
//        mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), taskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.SUCCESS.getCode());
        List<NecessaryTaskMbDTO> mbDTOS = taskDetails.stream().map(v -> {
            NecessaryTaskMbDTO mbDTO = new NecessaryTaskMbDTO();
            mbDTO.setStoreCode(v.getStoreCode());
            mbDTO.setGoodsNo(v.getGoodsNo());
            mbDTO.setNecessaryTag(v.getNecessaryTagName());
            if (!(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode() == mdmTask.getTaskSource() || MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode() == mdmTask.getTaskSource())) {
                mbDTO.setMinDisplayQuantity(v.getMinDisplayQuantity());
            }
            mbDTO.setUpdateTime(DateUtils.conventDateStrByPattern(new Date(), DateUtils.DATETIME_PATTERN));
            mbDTO.setTaskId(v.getTaskId());
            mbDTO.setTaskDetailId(v.getId());
            if (StringUtils.isBlank(mbDTO.getNecessaryTag())) {
                mbDTO.setForbidApply(null);
            } else {
                // 必备商品禁止请货=否
                mbDTO.setForbidApply("否");
            }
            if (MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode() == mdmTask.getTaskSource()) {
                // 选配改不经营
                mbDTO.setForbidApply("是");
                mbDTO.setNecessaryTag(ManageStatusEnum.NON_MANAGE.getMessage());
            }
            if (MdmTaskSourceEnum.NON_CHANGE_FORBID_DISTR.getCode() == mdmTask.getTaskSource()) {
                mbDTO.setForbidApply("是");
            }
            if (MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode() == mdmTask.getTaskSource()) {
                // 不经营商品改选配
                mbDTO.setForbidApply("否");
                mbDTO.setNecessaryTag(ManageStatusEnum.MANAGE_CHOOSE.getMessage());
            }
            if (MdmTaskSourceEnum.SEASON_GOODS.getCode() == mdmTask.getTaskSource() || MdmTaskSourceEnum.SENSITIVE_DEL_DISPLAY.getCode() == mdmTask.getTaskSource()) {
                // 张瑜让我这么改的
                mbDTO.setTagModifyFlag(false);
            }
            return mbDTO;
        }).collect(Collectors.toList());
        mbUtils.pushToMB(MBUtils.getMbUrl(necessaryMbPrpo), MBUtils.assembleParam(necessaryMbPrpo, mbDTOS));
    }

    protected synchronized void dealCache(List<StoreGoodsContentDTO> storeGoodsContentDTOS) {
        if (CollectionUtils.isEmpty(storeGoodsContentDTOS)) {
            return;
        }
//        List<String> dealingCache = getDealingCache();
//        logger.info("dealingCache:{}", JSON.toJSONString(dealingCache));
        List<StoreGoodsContentDTO> queue = batchPopDealQueue(storeGoodsContentDTOS);
        List<String> lockKeys = storeGoodsContentDTOS.stream().map(v -> v.getStoreId() + "-" + v.getGoodsNo()).collect(Collectors.toList());
        batchDelCache(lockKeys);
//        storeGoodsContentDTOS.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getStoreId)).forEach((k,v) -> {
//            logger.info("v:{}", JSON.toJSONString(v.stream().map(StoreGoodsContentDTO::getGoodsNo).distinct().collect(Collectors.toList())));
//            if (CollectionUtils.isNotEmpty(dealingCache)) {
//                batchDelCache(v.stream().map(cache -> cache.getStoreId() + "-" + cache.getGoodsNo()).distinct().collect(Collectors.toList()));
//            }
//            v.forEach(dto -> {
//                StoreGoodsContentDTO queueData = popDealQueue(dto);
//                if (null != queueData) {
//                    queue.add(queueData);
//                }
//            });
//        });
        if (CollectionUtils.isNotEmpty(queue)) {
            // 处理队列数据
            logger.info("处理队列数据 queue.size:{}", queue.size());
            queue.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getBizType)).forEach((k,v) -> {
                List<StoreGoodsContentDTO> nonMdmList = v.stream().filter(s -> null == s.getMdmTaskId()).collect(Collectors.toList());
                List<StoreGoodsContentDTO> mdmList = v.stream().filter(s -> null != s.getMdmTaskId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(nonMdmList)){
                        StoreContentsAssemble assemble = factory.getAssemble(k);
                        assemble.work(nonMdmList);
                }
                if (CollectionUtils.isNotEmpty(mdmList)){
                    mdmList.stream().collect(Collectors.groupingBy(StoreGoodsContentDTO::getMdmTaskId)).forEach((taksId, list) -> {
                        StoreContentsAssemble assemble = factory.getAssemble(k);
                        assemble.work(v);
                    });
                }
            });
        }
    }

    protected void pushMdmTask(List<MdmTaskDetail> taskDetails) {
        List<List<MdmTaskDetail>> partition = Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE);
        for (List<MdmTaskDetail> v : partition) {
            mdmTaskDetailExtendMapper.batchInsert(v);
            try {
//                logger.info("mdmTaskDetail:{}", JSON.toJSONString(v));
                pushMdm(v);
            } catch (Exception e) {
                logger.warn("推送mdm失败", e);
                v.forEach(taskDetail -> {
                    taskDetail.setPushStatus(MdmTaskPushStatusEnum.FAIL.getCode());
                    taskDetail.setExtend("推送mdm失败");
                });
                mdmTaskDetailExtendMapper.deleteByTaskId(taskDetails.get(0).getTaskId());
                mdmTaskDetailExtendMapper.batchInsert(v);
            }
        }
    }

}
