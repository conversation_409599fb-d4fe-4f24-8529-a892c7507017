package com.cowell.scib.service.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2018/10/12 16:11
 * @description : Mdm门店主数据
 */
public class MdmStoreBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long businessId;
    private Long storeId;

    private String leaseArea;

    private String goodTradingArea;

    private String region;

    private String orgPeopleNum;

    private String systemType;

    private String province;

    private String comId;

    private String openDate;

    private String orderOrg;

    private String fax;

    private String storeStatus;

    private String lastRenovationDate;

    private String outerRentArea;

    private String calendar;

    private String closeDate;

    private String customeRage;

    private String dept;

    private String storeNo;

    private String insuranceSort;

    private String operatingArea;

    private String tel;

    private String storeShotName;

    private String city;

    private String storeInnerNo;

    private String storeName;

    private String address;

    private String salesChannel;

    private String salesOrg;

    private String refStoreNo;

    private Integer status;

    private String annualRent;

    private String opCode;

    private String comName;

    private String contact;

    private String deliveryDate;

    private String salesLevel;

    private String operationType;

    private String tradingArea;

    private String area;

    private String storeAttr;

    private String operationAreaSort;

    private String businessTime;

    private String orgName;

    private String extend;

    /**
     * 门店医保编码
     */
    private String medicareStoreId;

    private String dtp;

    /**
     * 调拨组
     */
    private String allocationTeam;

    /**
     * 渠道 电商/零售等
     */
    private String channel;

    /**
     * 业态 商超/批发
     */
    private String format;

    /**
     * 特殊业务类型 DTP/慢病
     */
    private String specialType;

    /**
     * 医保证件类型
     */
    private String actualNo;

    /**
     * 数据源标识 MDM/OTHER
     */
    private String dataSign;

    /**
     * 码上放心平台编码
     */
    private String retrospectCode;

    /**
     * 管控门店
     */
    private String dtpType;

    /**
     * 门店收货验收类型  0海典 1智店通
     */
    private String receiveType;

    /**
     * 市场地位
     */
    private String marPosition;

    /**
     * 运营管理区域
     */
    private String operarea;

    /**
     * 双通道门店
     */
    private String stdshop;

    /**
     * 高济门店组织id
     */
    private Long storeOrgId;

    /**
     * 高济连锁组织id
     */
    private Long companyOrgId;

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public String getDtp() {
        return dtp;
    }

    public void setDtp(String dtp) {
        this.dtp = dtp;
    }

    public String getMedicareStoreId() {
        return medicareStoreId;
    }

    public void setMedicareStoreId(String medicareStoreId) {
        this.medicareStoreId = medicareStoreId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getLeaseArea() {
        return leaseArea;
    }

    public void setLeaseArea(String leaseArea) {
        this.leaseArea = leaseArea;
    }

    public String getGoodTradingArea() {
        return goodTradingArea;
    }

    public void setGoodTradingArea(String goodTradingArea) {
        this.goodTradingArea = goodTradingArea;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getOrgPeopleNum() {
        return orgPeopleNum;
    }

    public void setOrgPeopleNum(String orgPeopleNum) {
        this.orgPeopleNum = orgPeopleNum;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    public String getOpenDate() {
        return openDate;
    }

    public void setOpenDate(String openDate) {
        this.openDate = openDate;
    }

    public String getOrderOrg() {
        return orderOrg;
    }

    public void setOrderOrg(String orderOrg) {
        this.orderOrg = orderOrg;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getStoreStatus() {
        return storeStatus;
    }

    public void setStoreStatus(String storeStatus) {
        this.storeStatus = storeStatus;
    }

    public String getLastRenovationDate() {
        return lastRenovationDate;
    }

    public void setLastRenovationDate(String lastRenovationDate) {
        this.lastRenovationDate = lastRenovationDate;
    }

    public String getOuterRentArea() {
        return outerRentArea;
    }

    public void setOuterRentArea(String outerRentArea) {
        this.outerRentArea = outerRentArea;
    }

    public String getCalendar() {
        return calendar;
    }

    public void setCalendar(String calendar) {
        this.calendar = calendar;
    }

    public String getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(String closeDate) {
        this.closeDate = closeDate;
    }

    public String getCustomeRage() {
        return customeRage;
    }

    public void setCustomeRage(String customeRage) {
        this.customeRage = customeRage;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public String getInsuranceSort() {
        return insuranceSort;
    }

    public void setInsuranceSort(String insuranceSort) {
        this.insuranceSort = insuranceSort;
    }

    public String getOperatingArea() {
        return operatingArea;
    }

    public void setOperatingArea(String operatingArea) {
        this.operatingArea = operatingArea;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getStoreShotName() {
        return storeShotName;
    }

    public void setStoreShotName(String storeShotName) {
        this.storeShotName = storeShotName;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStoreInnerNo() {
        return storeInnerNo;
    }

    public void setStoreInnerNo(String storeInnerNo) {
        this.storeInnerNo = storeInnerNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSalesChannel() {
        return salesChannel;
    }

    public void setSalesChannel(String salesChannel) {
        this.salesChannel = salesChannel;
    }

    public String getSalesOrg() {
        return salesOrg;
    }

    public void setSalesOrg(String salesOrg) {
        this.salesOrg = salesOrg;
    }

    public String getRefStoreNo() {
        return refStoreNo;
    }

    public void setRefStoreNo(String refStoreNo) {
        this.refStoreNo = refStoreNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAnnualRent() {
        return annualRent;
    }

    public void setAnnualRent(String annualRent) {
        this.annualRent = annualRent;
    }

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public String getComName() {
        return comName;
    }

    public void setComName(String comName) {
        this.comName = comName;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getSalesLevel() {
        return salesLevel;
    }

    public void setSalesLevel(String salesLevel) {
        this.salesLevel = salesLevel;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getTradingArea() {
        return tradingArea;
    }

    public void setTradingArea(String tradingArea) {
        this.tradingArea = tradingArea;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getStoreAttr() {
        return storeAttr;
    }

    public void setStoreAttr(String storeAttr) {
        this.storeAttr = storeAttr;
    }

    public String getOperationAreaSort() {
        return operationAreaSort;
    }

    public void setOperationAreaSort(String operationAreaSort) {
        this.operationAreaSort = operationAreaSort;
    }

    public String getBusinessTime() {
        return businessTime;
    }

    public void setBusinessTime(String businessTime) {
        this.businessTime = businessTime;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getAllocationTeam() {
        return allocationTeam;
    }

    public void setAllocationTeam(String allocationTeam) {
        this.allocationTeam = allocationTeam;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getSpecialType() {
        return specialType;
    }

    public void setSpecialType(String specialType) {
        this.specialType = specialType;
    }

    public String getDataSign() {
        return dataSign;
    }

    public void setDataSign(String dataSign) {
        this.dataSign = dataSign;
    }

    public String getActualNo() {
        return actualNo;
    }

    public void setActualNo(String actualNo) {
        this.actualNo = actualNo;
    }

    public String getRetrospectCode() {
        return retrospectCode;
    }

    public void setRetrospectCode(String retrospectCode) {
        this.retrospectCode = retrospectCode;
    }

    public String getDtpType() {
        return dtpType;
    }

    public void setDtpType(String dtpType) {
        this.dtpType = dtpType;
    }

    public String getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(String receiveType) {
        this.receiveType = receiveType;
    }

    public String getMarPosition() {
        return marPosition;
    }

    public void setMarPosition(String marPosition) {
        this.marPosition = marPosition;
    }

    public String getOperarea() {
        return operarea;
    }

    public void setOperarea(String operarea) {
        this.operarea = operarea;
    }

    public String getStdshop() {
        return stdshop;
    }

    public void setStdshop(String stdshop) {
        this.stdshop = stdshop;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
