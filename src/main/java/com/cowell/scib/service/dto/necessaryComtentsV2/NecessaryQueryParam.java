package com.cowell.scib.service.dto.necessaryComtentsV2;

import com.cowell.scib.service.vo.amis.AmisPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NecessaryQueryParam extends AmisPageParam implements Serializable {
    @ApiModelProperty("必备标签")
    private List<String> necessaryTags;
    @ApiModelProperty(value = "商品编码多个,分割")
    private String goodsNos;
    @ApiModelProperty(value = "状态 0 生效 -1 失效")
    private Byte status;
    @ApiModelProperty(value = "区域平台")
    private Long platformOrgId;
    @ApiModelProperty(value = "项目公司")
    private List<Long> companyOrgIds;
    @ApiModelProperty(value = "省份")
    private List<String> provinces;
    @ApiModelProperty(value = "城市")
    private List<String> citys;
    @ApiModelProperty(value = "大类")
    private Long categoryId;
    @ApiModelProperty(value = "中类")
    private Long middleCategoryId;
    @ApiModelProperty(value = "小类")
    private Long smallCategoryId;
    @ApiModelProperty(value = "子类")
    private Long subCategoryId;
    @ApiModelProperty(value = "成分")
    private String composition;
    @ApiModelProperty(value = "集团店型")
    private List<String> jtStoreTypes;
    @ApiModelProperty(value = "组货店型")
    private List<String> storeTypes;
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

}
