package com.cowell.scib.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @description: 门店新店推荐商品目录
 * <AUTHOR>
 * @date 2024年07月18日10:43:00
 */
@Data
public class TaskNewStoreRecommendDTO implements Serializable {
    private static final long serialVersionUID = -6065170499257843930L;

    /**
     * 公司组织ID
     */
    @ApiModelProperty(value = "公司组织ID")
    private String businessOrgId="";


    /**
     * 平台组织ID
     */
    @ApiModelProperty(value = "平台组织ID")
    private String platformOrgId="";

    /**
     * 平台组织ID
     */
    @ApiModelProperty(value = "门店来源 1.门店主数据 2.新店管理系统-自建 3.新店管理系统-收购")
    private String storeSource="";
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName="";

    /**
     * 门店编码：SAP编码，编码段A000-ZZZZ
     */
    @ApiModelProperty(value = "门店编码/预选址编码")
    private String storeCode="";

    /**
     * 门店属性 :直营-自建/直营-收购...
     */
    @ApiModelProperty(value = "门店属性")
    private String storeAttr="";


    /**
     * 最高成本单价限制
     */
    @ApiModelProperty(value = "最高成本单价限制")
    private String maxCostPriceLimit="";

    /**
     * 商品黑名单.逗号隔开
     */
    @ApiModelProperty(value = "商品黑名单,逗号隔开")
    private String goodsBlackList="";

    /**
     * 相似门店编码.逗号隔开
     */
    @ApiModelProperty(value = "相似门店编码,逗号隔开")
    private String similarStoreIdList="";

    /**
     * 月销售额等级
     */
    @ApiModelProperty(value = "月销售额等级")
    private String salesLevel="";

    /**
     * 单月盈亏平衡点销售额(元)
     */
    @ApiModelProperty(value = "单月盈亏平衡点销售额(元)")
    private String monthlyBreakEvenSalesRevenue="";

    /**
     * 盈亏平衡点(元)
     */
    @ApiModelProperty(value = "盈亏平衡点(元)")
    private String breakEvenSalesRevenue="";

    /**
     * 选址商圈店型 社商店/商圈类型
     */
    @ApiModelProperty(value = "选址商圈业态")
    private String tradingArea="";

    @ApiModelProperty(value = "组货店型")
    private String assortmentStoreType="";
    /**
     * 中参店型, ZS03等
     */
    @ApiModelProperty(value = "中参店型编码")
    private String zsStoreTypeCode="";


    @ApiModelProperty(value = "市场地位")
    private String marPosition="";

    /**
     * 省: 门店所在省份
     */
    @ApiModelProperty(value = "省")
    private String province="";

    /**
     * 城市: 门店所在城市
     */
    @ApiModelProperty(value = "市")
    private String city="";

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    private String area="";


    /**
     * 详细地址: 详细地址到门牌号，乡镇店
     */
    @ApiModelProperty(value = "详细地址")
    private String address="";

    /**
     * 经营面积: 实际经营面积（对外开放的营业面积，商品陈列区域面积，不含办公区域）
     */
    @ApiModelProperty(value = "经营面积/实用面积")
    private String operatingArea="";

    /**
     * 空间的总面积  门面宽度x纵深(m)
     */
    @ApiModelProperty(value = "门面宽度x纵深")
    private String totalArea="";

    /**
     * 院边店临近医院
     */
    @ApiModelProperty(value = "院边店临近医院")
    private String locatedHospital="";

    /**
     * 院边店临近医院等级
     */
    @ApiModelProperty(value = "院边店临近医院等级")
    private String locatedHospitalLevel="";

    /**
     * 商品存货是否承接
     */
    @ApiModelProperty(value = "商品存货是否承接")
    private String goodsInventoryCarry="";

    /**
     * 1~3月店日销售额(元)
     */
    @ApiModelProperty(value = "1~3月店日销售额(元)")
    private String dailySalesRevenue="";




}
