package com.cowell.scib.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public class CategoryDTO implements Serializable {
    private static final long serialVersionUID = 8288611992905050223L;

    private Long id;

    @NotNull
    @Size(max = 40 ,message = "长度不可超过40中文字符")
    private String categoryName;

    private Long parentId;

    private Integer status = 0;
    @JsonIgnore
    private Integer sort;


    @Size(max = 256)
    private String path;

    @Size(max = 200 , message = "长度最长为200中文字符")
    private String description;

    @JsonIgnore
    private Integer sortValue;
    @Size(max = 1024)
    private String features;
    @JsonIgnore
    private Long version = 1L;

    private List<Long> categoryIds;

    private String categoryLevel = "0";

    @ApiModelProperty(value = "搜索类型 1。积分类目 2。普通类目")
    private String type;

    /**
     * 属性来源 0 ：MDM 1:自建属性
     */
    @ApiModelProperty(value = "来源 0 MDM， 1 自建")
    private Byte source;

    @ApiModelProperty(value = "积分商城展示类目 1 展示实物商品类目， 2 展示线上优惠券类目")
    private byte isQuery;

    @ApiModelProperty(value = "指定类目id(特定连锁一级类目只展示特定类目)")
    private List<Long> fixedIds;
    /**
     * 扩展字段，是否是券 1是
     */
    private Integer tag;

    public String getCategoryLevel() {
        return categoryLevel;
    }

    public void setCategoryLevel(String categoryLevel) {
        this.categoryLevel = categoryLevel;
    }

    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Long> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public List<Long> getFixedIds() {
        return fixedIds;
    }

    public void setFixedIds(List<Long> fixedIds) {
        this.fixedIds = fixedIds;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSortValue() {
        return sortValue;
    }

    public void setSortValue(Integer sortValue) {
        this.sortValue = sortValue;
    }

    public String getFeatures() {
        return features;
    }

    public void setFeatures(String features) {
        this.features = features;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public byte getIsQuery() {
        return isQuery;
    }

    public void setIsQuery(byte isQuery) {
        this.isQuery = isQuery;
    }

    public Integer getTag() {
        return tag;
    }

    public void setTag(Integer tag) {
        this.tag = tag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        CategoryDTO categoryDTO = (CategoryDTO) o;
        if(categoryDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), categoryDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("CategoryDTO{");
        sb.append("id=").append(id);
        sb.append(", categoryName='").append(categoryName).append('\'');
        sb.append(", parentId=").append(parentId);
        sb.append(", status=").append(status);
        sb.append(", sort=").append(sort);
        sb.append(", path='").append(path).append('\'');
        sb.append(", description='").append(description).append('\'');
        sb.append(", sortValue=").append(sortValue);
        sb.append(", features='").append(features).append('\'');
        sb.append(", version=").append(version);
        sb.append(", categoryIds=").append(categoryIds);
        sb.append(", categoryLevel='").append(categoryLevel).append('\'');
        sb.append(", type='").append(type).append('\'');
        sb.append(", source=").append(source);
        sb.append(", isQuery=").append(isQuery);
        sb.append('}');
        return sb.toString();
    }

}
