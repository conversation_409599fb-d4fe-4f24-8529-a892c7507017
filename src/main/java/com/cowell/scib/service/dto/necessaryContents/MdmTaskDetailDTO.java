package com.cowell.scib.service.dto.necessaryContents;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR> mdm任务明细表
 */
public class MdmTaskDetailDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * mdm任务id
     */
    private Long taskId;

    /**
     * 组货公司orgID
     */
    private Long companyOrgId;

    /**
     * 组货公司ID
     */
    private Long businessId;

    /**
     * 组货公司编码
     */
    private String companyCode;

    /**
     * 组货公司
     */
    private String companyName;

    /**
     * 门店orgID
     */
    private Long storeOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品通用名
     */
    private String goodsCommonName;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 必备标识(0非必备 1集团必备 2平台必备 3企业必备 4店型必备 5店型选配 6单店必备)
     */
    private Integer necessaryTag;
    private String necessaryTagName;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 下发状态 0 已下发 1 失败 2 成功
     */
    private Byte pushStatus;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public Integer getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(Integer necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public String getNecessaryTagName() {
        return necessaryTagName;
    }

    public void setNecessaryTagName(String necessaryTagName) {
        this.necessaryTagName = necessaryTagName;
    }

    public BigDecimal getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(BigDecimal minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public Byte getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(Byte pushStatus) {
        this.pushStatus = pushStatus;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MdmTaskDetailDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("taskId=" + taskId)
                .add("companyOrgId=" + companyOrgId)
                .add("businessId=" + businessId)
                .add("companyCode=" + companyCode)
                .add("companyName=" + companyName)
                .add("storeOrgId=" + storeOrgId)
                .add("storeId=" + storeId)
                .add("storeCode='" + storeCode + "'")
                .add("storeName='" + storeName + "'")
                .add("goodsNo='" + goodsNo + "'")
                .add("barCode='" + barCode + "'")
                .add("goodsCommonName='" + goodsCommonName + "'")
                .add("goodsName='" + goodsName + "'")
                .add("goodsUnit='" + goodsUnit + "'")
                .add("description='" + description + "'")
                .add("specifications='" + specifications + "'")
                .add("dosageForm='" + dosageForm + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("approvalNumber='" + approvalNumber + "'")
                .add("necessaryTag=" + necessaryTag)
                .add("necessaryTagName=" + necessaryTagName)
                .add("minDisplayQuantity=" + minDisplayQuantity)
                .add("pushStatus=" + pushStatus)
                .add("status=" + status)
                .add("gmtCreate=" + gmtCreate)
                .add("gmtUpdate=" + gmtUpdate)
                .add("extend='" + extend + "'")
                .add("version=" + version)
                .add("createdBy=" + createdBy)
                .add("createdName='" + createdName + "'")
                .add("updatedBy=" + updatedBy)
                .add("updatedName='" + updatedName + "'")
                .toString();
    }
}
