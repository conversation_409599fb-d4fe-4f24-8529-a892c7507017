package com.cowell.scib.service.dto.iscm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 根据门店与商品列表获取日销列表,请求参数
 */
@Data
public class DailySalesParam  implements Serializable {
        private static final long serialVersionUID = 7892983707110492382L;

        @ApiModelProperty(value = "当前参数所有的商品编码")
        private List<String> goodsNos;

        @ApiModelProperty(value = "当前参数所有的商品编码")
        private Long storeId;
}
