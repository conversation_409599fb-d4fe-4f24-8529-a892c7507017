package com.cowell.scib.service.dto.necessaryComtentsV2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> 必备目录管理表
 */
@Data
public class NecessaryContentsDTO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 必备标签
     */
    @ApiModelProperty(value = "必备标签")
    private String necessaryTag;

    /**
     * 必备标签名称
     */
    @ApiModelProperty(value = "必备标签名称")
    private String necessaryTagName;

    /**
     * orgid
     */
    private Long orgId;

    /**
     * org名称
     */
    private String orgName;

    /**
     * 集团orgid
     */
    @ApiModelProperty(value = "集团orgid")
    private Long groupOrgId;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String groupName;
    /**
     * 平台orgid
     */
    @ApiModelProperty(value = "平台orgid")
    private Long platformOrgId;

    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称")
    private String platformName;

    /**
     * 公司orgId
     */
    @ApiModelProperty(value = "公司orgId")
    private Long companyOrgId;

    /**
     * 连锁id
     */
    @ApiModelProperty(value = "连锁id")
    private Long businessid;

    /**
     * 公司MDM编码
     */
    @ApiModelProperty(value = "公司MDM编码")
    private String companyCode;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 门店orgID
     */
    @ApiModelProperty(value = "门店orgID")
    private Long storeOrgId;

    /**
     * 门店ID
     */
    @ApiModelProperty(value = "门店orgID")
    private Long storeId;

    /**
     * 门店MDM编码
     */
    @ApiModelProperty(value = "门店MDM编码")
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 店型
     */
    @ApiModelProperty(value = "店型编码")
    private String storeType;

    /**
     * 店型名称
     */
    @ApiModelProperty(value = "店型名称")
    private String storeTypeName;

    /**
     * 省份
     */
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 区县
     */
    @ApiModelProperty(value = "区县")
    private String area;

    /**
     * 大类id
     */
    @ApiModelProperty(value = "大类id")
    private Long categoryId;

    /**
     * 大类名称
     */
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 中类id
     */
    @ApiModelProperty(value = "中类id")
    private Long middleCategoryId;

    /**
     * 中类名称
     */
    @ApiModelProperty(value = "中类名称")
    private String middleCategoryName;

    /**
     * 小类编码
     */
    @ApiModelProperty(value = "小类id")
    private Long smallCategoryId;

    /**
     * 小类名称
     */
    @ApiModelProperty(value = "小类名称")
    private String smallCategoryName;

    /**
     * 子类编码
     */
    @ApiModelProperty(value = "子类id")
    private Long subCategoryId;

    /**
     * 子类名称
     */
    @ApiModelProperty(value = "子类名称")
    private String subCategoryName;

    /**
     * 成分
     */
    @ApiModelProperty(value = "成分")
    private String composition;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    /**
     * 条码
     */
    @ApiModelProperty(value = "条码")
    private String barCode;

    /**
     * 商品通用名
     */
    @ApiModelProperty(value = "商品通用名")
    private String goodsCommonName;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String goodsName;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String goodsUnit;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String description;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specifications;

    /**
     * 剂型
     */
    @ApiModelProperty(value = "剂型")
    private String dosageForm;

    /**
     * 生产厂家
     */
    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    /**
     * 批准文号
     */
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    /**
     * 采购属性
     */
    @ApiModelProperty(value = "采购属性")
    private String purchaseAttr;

    /**
     * 入选原因
     */
    @ApiModelProperty(value = "入选原因")
    private String chooseReason;

    /**
     * 作废原因
     */
    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    /**
     * 状态(-1删除，0正常)
     */
    @ApiModelProperty(value = "状态(-1删除，0正常)")
    private Byte status;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtUpdate;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdName;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updatedBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedName;

    @ApiModelProperty(value = "是否可修改")
    private Boolean modifyAble;

    public static LinkedHashMap<String, String> getExportMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap();
        map.put("id", "ID");
        map.put("necessaryTagName", "必备标签");
        map.put("goodsNo", "商品编码");
        map.put("goodsName", "商品名称");
        map.put("specifications", "规格");
        map.put("goodsUnit", "单位");
        map.put("manufacturer", "厂家简称");
        map.put("groupName", "集团");
        map.put("platformOrgId", "区域平台ID");
        map.put("platformName", "区域平台");
        map.put("province", "省份");
        map.put("companyOrgId", "项目公司ID");
        map.put("companyName", "项目公司");
        map.put("city", "城市");
        map.put("area", "区县");
        map.put("storeTypeName", "生效店型");
        map.put("storeCode", "门店编码");
        map.put("storeName", "门店名称");
        map.put("categoryName", "商品大类");
        map.put("middleCategoryName", "商品中类");
        map.put("smallCategoryName", "商品小类");
        map.put("subCategoryName", "商品子类");
        map.put("composition", "成分");
        map.put("chooseReason", "入选原因");
        map.put("invalidReason", "作废原因");
        map.put("purchaseAttr", "采购属性");
        map.put("updatedName", "操作人");
        map.put("gmtUpdate", "操作时间");
        return map;

    }
}
