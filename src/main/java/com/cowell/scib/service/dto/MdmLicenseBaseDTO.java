package com.cowell.scib.service.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2018/10/12 16:51
 * @description :
 */
@Setter
@Getter
public class MdmLicenseBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 高济连锁id
     */
    private Long businessId;

    /**
     * 高济连锁门店id
     */
    private Long storeId;

    /**
     * MDM连锁企业编码
     */
    private String comId;

    /**
     * 档案编号：BP或门店档案编号
     */
    private String archiveNo;

    /**
     * 实体类型: 01 BP、02 门店
     */
    private String entityType;

    /**
     * 实体编码: BP的编码或门店的编码
     */
    private String entityNo;

    /**
     * 原实体编码: 原BP的编码或门店的编码
     */
    private String olderEntityNo;

    /**
     * 实体门店：BP名称或门店名称
     */
    private String entityName;

    /**
     * 经营范围名称
     */
    private String scopeName;

    /**
     * 经营范围编码
     */
    private String businessScope;

    /**
     * 状态 ：-1 删除，0 新增， 1修改， 2 不变
     */
    private Integer status;

    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;

    /**
     * 更新时间
     */
    private ZonedDateTime gmtUpdate;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 可扩展字段
     */
    private String extend;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    public String getArchiveNo() {
        return archiveNo;
    }

    public void setArchiveNo(String archiveNo) {
        this.archiveNo = archiveNo;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getEntityNo() {
        return entityNo;
    }

    public void setEntityNo(String entityNo) {
        this.entityNo = entityNo;
    }

    public String getOlderEntityNo() {
        return olderEntityNo;
    }

    public void setOlderEntityNo(String olderEntityNo) {
        this.olderEntityNo = olderEntityNo;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getScopeName() {
        return scopeName;
    }

    public void setScopeName(String scopeName) {
        this.scopeName = scopeName;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public ZonedDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(ZonedDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public ZonedDateTime getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(ZonedDateTime gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    @Override
    public String toString() {
        return "MdmLicenseBaseDTO{" +
                "id=" + id +
                ", businessId=" + businessId +
                ", storeId=" + storeId +
                ", comId='" + comId + '\'' +
                ", archiveNo='" + archiveNo + '\'' +
                ", entityType='" + entityType + '\'' +
                ", entityNo='" + entityNo + '\'' +
                ", olderEntityNo='" + olderEntityNo + '\'' +
                ", entityName='" + entityName + '\'' +
                ", scopeName='" + scopeName + '\'' +
                ", businessScope='" + businessScope + '\'' +
                ", status=" + status +
                ", gmtCreate=" + gmtCreate +
                ", gmtUpdate=" + gmtUpdate +
                ", version=" + version +
                ", extend='" + extend + '\'' +
                '}';
    }

    public MdmLicenseBaseDTO() {}

    public MdmLicenseBaseDTO(MdmStoreLicenseData mdmStoreLicenseData){

        this.comId = mdmStoreLicenseData.getMdm_company_code();

        this.entityNo = mdmStoreLicenseData.getEntityno();

        this.archiveNo = mdmStoreLicenseData.getArchiveno();

        this.entityType = mdmStoreLicenseData.getEntitytype();

        this.olderEntityNo = mdmStoreLicenseData.getOlderentityno();

        this.entityName = mdmStoreLicenseData.getEntityname();

    }
}
