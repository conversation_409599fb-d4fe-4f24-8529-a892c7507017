package com.cowell.scib.service.dto.TrackResult;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class CompanyGoodsData {

    @ExcelProperty(value = "企业ID",index = 0)
    private Long orgId;

    @ExcelProperty(value = "城市",index = 1)
    private String city;

    @ExcelProperty(value = "商品编码",index = 2)
    private String goodsNo;

    @ExcelProperty(value = "错误原因",index = 3)
    private String errorReason;

}
