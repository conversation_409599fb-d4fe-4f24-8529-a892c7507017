package com.cowell.scib.service.dto.necessaryComtentsV2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.StringJoiner;
@Data
public class NecessaryAddParam {
    @ApiModelProperty(value = "必备标签")
    private String necessaryTag;
    @ApiModelProperty(value = "平台orgId")
    private Long platformOrgId;
    @ApiModelProperty(value = "公司orgId")
    private Long companyOrgId;
    @ApiModelProperty(value = "门店OrgId")
    private List<Long> storeOrgIds;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区县")
    private List<String> areas;
    @ApiModelProperty(value = "店型")
    private List<String> storeTypes;
    @ApiModelProperty(value = "商品编码")
    private List<String> goodsNos;
    @ApiModelProperty(value = "入选原因")
    private String chooseReason;
    @ApiModelProperty(value = "是否忽略重复商品")
    private Boolean existsIgnore = false;
    @ApiModelProperty(value = "启用/停用标识")
    private Boolean useMark;
}
