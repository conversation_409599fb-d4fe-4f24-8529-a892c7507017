package com.cowell.scib.service.dto.necessaryContents;

import io.swagger.annotations.ApiModelProperty;

import java.util.StringJoiner;

public class NecessaryBatchImportDTO {
    @ApiModelProperty(value = "店型")
    private String storeType;

    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", NecessaryBatchImportDTO.class.getSimpleName() + "[", "]")
                .add("storeType='" + storeType + "'")
                .add("storeCode='" + storeCode + "'")
                .add("goodsNo='" + goodsNo + "'")
                .toString();
    }
}
