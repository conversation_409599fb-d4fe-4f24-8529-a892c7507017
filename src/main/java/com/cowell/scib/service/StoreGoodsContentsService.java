package com.cowell.scib.service;

import com.cowell.scib.service.dto.rule.ScibCommonResultDTO;

import java.util.List;

/**
 * StoreGoodsContents Service Interface
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
public interface StoreGoodsContentsService {

    /**
     * 获取门店商品内容
     * 
     * @param storeId 门店ID
     * @param manageStatus 经营状态
     * @param goodsNoList 商品编码列表
     * @return ScibCommonResultDTO
     */
    ScibCommonResultDTO getStoreGoodsContents(Long storeId, Integer manageStatus, List<String> goodsNoList);
}
