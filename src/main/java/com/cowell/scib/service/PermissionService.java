package com.cowell.scib.service;

import com.cowell.permission.dto.*;
import com.cowell.scib.service.dto.EmployeeInfoVO;
import com.cowell.permission.vo.OrgVO;
import com.cowell.permission.vo.ResourceTreeVO;
import com.cowell.scib.service.dto.OrgSimpleDTO;
import com.cowell.scib.service.dto.OrgTreeSimpleDTO;
import com.cowell.scib.service.dto.QueryEmployeeByRoleDTO;
import com.cowell.scib.service.dto.permssion.EmployeeDetailWithWxDTO;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public interface PermissionService {

    /**
     * 根据组织机构id获取平台信息
     * @param userId
     * @param orgIds
     * @param isRecursion
     * @return
     */
    List<OrgSimpleDTO> getUserDataScopeChildOrgByOrgId(Long userId, List<Long> orgIds, boolean isRecursion);

    List<OrgSimpleDTO> getUserDataScopeChildOrgByOrgIdWithResourceId(Long userId, List<Long> orgIds, Long resourceId, boolean isRecursion);
    /**
     * 根据组织机构id获取平台信息
     * @param orgId
     * @param type
     * @return
     */
    List<OrgDTO> getParentOrgByIdAndType(Long orgId, Integer type);

    /**
     * 获取用户权限菜单
     * @param userId
     * @return
     */
    List<ResourceTreeVO> getSubMenuTree(Long userId);

    /*
     * 获取用户权限菜单
     * @param userId
     * @return
     */
    List<ResourceTreeVO> getSubMenuTreeWhitResourceId(Long userId, Long resourceId);


    /**
     * 获取用户权限菜单
     * @param userIdList
     * @return
     */
    Map<Long,List<RoleInfoDTO>> getUserRoleById(List<Long> userIdList);

    /**
     * 通过组织ID获取下级指定类型的组织信息-用户权限
     * @param userId
     * @param parentOrgId
     * @param orgType
     * @return
     */
    List<OrgDTO> listOrgTypeBelowOrgAssignedInScope(Long userId, Long parentOrgId, Integer orgType);

    /**
     * 通过平台获取简化版组织信息
     * @param userId
     * @param parentOrgId
     * @param orgType
     * @return
     */
    List<OrgSimpleDTO> listOrgInfoByPlate(Long userId, Long parentOrgId, Integer orgType);

    OrgVO getOrgInfoById(Long orgId) throws Exception;

    /**
     * 根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表
     *
     * @param ids  上级组织机构id集合
     * @param type 类型
     */
    List<ChildOrgsDTO> listChildOrgAssignedType(List<Long> ids, Integer type);

    /**
     * 根据指定组织类型与组织路径获取当前/子级组织机构
     * @param orgType
     * @param orgPath
     * @return
     */
    List<OrgDTO> listChildOrgByOrgTypeAndOrgPath(Integer orgType, String orgPath);

    /**
     * 获取指定组织下用户有数据权限的树并根据组织类型过滤
     * @param userId
     * @param orgId
     * @param types
     * @return
     */
    List<OrgTreeSimpleDTO> listUserDataScopeTreesByOrgIdAndTypes(Long userId, Long orgId, List<Integer> types);

    List<EmployeeInfoVO> getListByEmpCodesAndStatus(List<String> empCodes);

    /**
     * 功 能: 根据门店ID获取店长数据
     * 作 者: tomas
     * 时 间: 2025-03-06 01:14:06
     */
    List<EmployeeDetailWithWxDTO> getSalesclerkMastersByStoreId(Long storeId);

    /**
     * 功 能: 根据员工编码获取员工信息
     * 作 者: yuema
     * 时 间: 2025-05-16 17:42:06
     */
    List<EmployeeDetailWithWxDTO> getUserNamesByEmpCodes(List<String> empCodes);

    /**
     * @Description 根据角色编码获取相关用户数据
     * @Param queryEmployeeByRoleDTO
     * @Return java.util.List<com.cowell.permission.dto.EmployeeDetailDTO>
     * <AUTHOR>
     * @Date 2025/5/516 18:09
     */
    List<EmployeeDetailDTO> getEmployeesByRole(QueryEmployeeByRoleDTO queryEmployeeByRoleDTO);

    /**
     * 根据角色代码查询所有员工工号
     * 方法会一直分页查询直到没有更多数据为止
     *
     * @param roleCode 角色代码
     * @return 所有符合条件的员工ID集合
     */
    Set<String> getEmpCodeByRoleCode(String roleCode);
}
