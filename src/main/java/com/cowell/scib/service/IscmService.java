package com.cowell.scib.service;

import com.cowell.scib.service.dto.CommonCategoryDTO;
import com.cowell.scib.service.dto.IscmGoodsCategoryDTO;
import com.cowell.scib.service.dto.SensitiveWarnSaveDTO;
import com.cowell.scib.service.dto.iscm.DailySalesParam;
import com.cowell.scib.service.dto.iscm.DailySalesResponse;
import com.cowell.scib.service.dto.iscm.GoodsInfoParams;
import com.cowell.scib.service.dto.iscm.GoodsInfoResponse;

import java.util.List;
import java.util.Map;

public interface IscmService {

    List<IscmGoodsCategoryDTO> getIscmGoodsCategoryListByIds(List<Long> categoryIds);

    Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds);

    SensitiveWarnSaveDTO getSensitivewarnConfig(String paramUniqueMark);

    GoodsInfoResponse getParamGoodsInfos(GoodsInfoParams params);

    //根据门店与商品列表获取日销列表
    Map<String, DailySalesResponse> getAvgSalesList(DailySalesParam dailySalesParam);
}
