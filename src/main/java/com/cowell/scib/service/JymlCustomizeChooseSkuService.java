package com.cowell.scib.service;

import com.cowell.scib.entityDgms.JymlCustomizeChooseSku;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.customize.CustomizeQueryParam;
import com.cowell.scib.service.vo.amis.PageResult;

public interface JymlCustomizeChooseSkuService {
    PageResult<JymlCustomizeChooseSku> getCustomizeChooseSkyList(TokenUserDTO userDTO, CustomizeQueryParam param);

    void delete(TokenUserDTO userDTO, CustomizeQueryParam param);

    void export(TokenUserDTO userDTO, CustomizeQueryParam param);
}
