package com.cowell.scib.service.impl;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.entityDgms.StoreGoodsContentsExample;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.service.StoreGoodsContentsService;
import com.cowell.scib.service.dto.rule.ScibCommonResultDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * StoreGoodsContents Service Implementation
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Service
public class StoreGoodsContentsServiceImpl implements StoreGoodsContentsService {

    private static final Logger logger = LoggerFactory.getLogger(StoreGoodsContentsServiceImpl.class);

    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;

    @Override
    public ScibCommonResultDTO getStoreGoodsContents(Long storeId, Integer manageStatus, List<String> goodsNoList) {
        logger.info("获取门店商品内容，storeId: {}, manageStatus: {}, goodsNoList: {}", storeId, manageStatus, goodsNoList);
        StoreGoodsContentsExample storeGoodsInfoExample = new StoreGoodsContentsExample();
        StoreGoodsContentsExample.Criteria criteria = storeGoodsInfoExample.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
                .andManageStatusEqualTo(manageStatus)
                .andStatusEqualTo(Constants.NORMAL_STATUS);
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            criteria.andGoodsNoIn(goodsNoList);
        }
        List<StoreGoodsContents> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(storeGoodsInfoExample);
        ScibCommonResultDTO scibCommonResultDTO = new ScibCommonResultDTO();
        scibCommonResultDTO.setStoreGoodsContents(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList()));
        logger.info("查询到门店商品内容数量: {}", storeGoodsInfos != null ? storeGoodsInfos.size() : 0);
        return scibCommonResultDTO;
    }
}
