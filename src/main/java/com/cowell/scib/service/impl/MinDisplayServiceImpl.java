package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.dto.EmployeeInfoVO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.SensitiveUpdateInfo;
import com.cowell.scib.entityTidb.SensitiveUpdateInfoExample;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperTidb.SensitiveUpdateInfoMapper;
import com.cowell.scib.mapperTidb.extend.SensitiveUpdateInfoExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreContentsFactory;
import com.cowell.scib.service.dto.necessaryContents.MdmTaskDetailExportDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonGoodsDTO;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.utils.ApacheHttpUtil;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.HutoolUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.DatatypeConverter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MinDisplayServiceImpl implements MinDisplayService {

    private final Logger logger = LoggerFactory.getLogger(MinDisplayServiceImpl.class);

    @Autowired
    private TrackResultAdjustServiceImpl trackResultAdjustServiceImpl;

    @Autowired
    private ForestService forestService;

    @Autowired
    private TagService tagService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private MinDisplayChangeRecordExtendMapper minDisplayChangeRecordExtendMapper;

    @Autowired
    private NecessaryContentsService necessaryContentsService;
    @Autowired
    private StoreContentsFactory factory;

    @Autowired
    private MdmTaskDetailMapper mdmTaskDetailMapper;

    @Autowired
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;

    @Autowired
    private MinDisplayChangeRecordMapper minDisplayChangeRecordMapper;

    @Autowired
    private StoreGoodsInfoMapper storeGoodsInfoMapper;

    @Autowired
    private StoreGoodsInfoExtendMapper storeGoodsInfoExtendMapper;

    @Autowired
    private StoreGoodsContentsMapper storeGoodsContentsMapper;

    @Autowired
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    @Autowired
    private SensitiveUpdateInfoMapper sensitiveUpdateInfoMapper;

    @Autowired
    private SensitiveUpdateInfoExtendMapper sensitiveUpdateInfoExtendMapper;

    @Autowired
    private NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;

    @Autowired
    private NecessarySingleStoreGoodsExtendMapper necessarySingleStoreGoodsExtendMapper;

    @Autowired
    private IscmService iscmService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private IAlertService alertService;

    @Value("${local.dev.flag}")
    private boolean requestStatus;

    @Value("${scib.minDisplay.query.url:}")
    private String url;

    @Value("${scib.minDisplay.query.username:}")
    private String username;

    @Value("${scib.minDisplay.query.password:}")
    private String password;

    @Value("${scib.select.mdm.openFlag:0}")
    private Integer openFlag;

    @Value("${scib.sensitive.warn.prefix:0}")
    private String prefix;


    /**
     * 获取门店范围 ：经营状态=常规经营门店&门店状态=营业&业态=药店连锁
     *             排除范围电商店 和共享药房
     * @return
     */
    public List<Long> selectStoreIdFilterSelector() {
        StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
        queryParam.setManagstate(Collections.singletonList(Constants.MANAGE_STATE));
        queryParam.setStorestatus(Collections.singletonList(Constants.STORE_START));
        queryParam.setFormat(Collections.singletonList(Constants.FORMAT));
        queryParam.setExcludeOrgIds(Constants.EXCLUDE_STORE);
        queryParam.setStoreattr(Constants.STORE_ATTR);
        return tagService.getSelectStoreIdListByType(queryParam);
    }

    /**
     * 门店经营范围与商品经营范围一致性校验
     * @param businessId
     * @param storeId
     * @param goodNos
     * @return
     */
    private List<String> removeGoodsNo(Long businessId,Long storeId,List<String> goodNos) {
        //门店经营范围和商品经营范围：
        List<String> removeGoodsNo = new ArrayList<>();
        List<String> storeLicense = new ArrayList<>();
        if (null != storeId) {
            storeLicense = trackResultAdjustServiceImpl.getStoreLicense(businessId, storeId);
        }
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        List<AuctionSpuBaseInfo> auctionSpuBaseInfoList = forestService.batchFindSpuProperty(goodNos, businessId);
        for (AuctionSpuBaseInfo auctionSpuBaseInfo : auctionSpuBaseInfoList) {
            if (null != storeId && CollectionUtils.isNotEmpty(storeLicense) && !storeLicense.contains(auctionSpuBaseInfo.getBusiscopetag())) {
                removeGoodsNo.add(auctionSpuBaseInfo.getGoodsNo());
                logger.info("businessId:{}门店经营范围和商品经营范围不匹配removeGoodsNo:{}", businessId, removeGoodsNo);
            }
        }
        return removeGoodsNo;
    }

    @Override
    public void getMinDisplayFromMdm(SeasonalGoodsPushDTO seasonalGoodsDTO) {
        if (Objects.isNull(seasonalGoodsDTO)) {
            return;
        }
        if (CollectionUtils.isEmpty(seasonalGoodsDTO.getGoodsNoList()) || null == seasonalGoodsDTO.getBusinessId()) {
            return;
        }

        List<OrgInfoBaseCache> storeListByBusinessId = CacheVar.getStoreListByBusinessId(seasonalGoodsDTO.getBusinessId());
        if (CollectionUtils.isEmpty(storeListByBusinessId)) {
            logger.error("获取连锁下的门店集合为空:{}", storeListByBusinessId);
            return;
        }
        List<OrgInfoBaseCache> filterStoreNoList = new ArrayList<>();
        if (Constants.ADD_SEASON_GOODS.equals(seasonalGoodsDTO.getStatus())) {
            List<Long> longs = selectStoreIdFilterSelector();
            logger.info("通过选择器获取到的门店集合：{}",longs.size());
            if (CollectionUtils.isEmpty(longs)) {
                logger.error("通过选择器获取门店集合为空");
                return;
            }
            filterStoreNoList = storeListByBusinessId.stream().filter(v -> longs.contains(v.getOutId())).collect(Collectors.toList());
        } else {
            filterStoreNoList = storeListByBusinessId;
        }
        logger.info("过滤后的门店集合:{}", filterStoreNoList.size());
        try {
            TokenUserDTO userDTO = new TokenUserDTO();
            userDTO.setUserId(seasonalGoodsDTO.getUserId());
            userDTO.setName(seasonalGoodsDTO.getUserName());
            MdmTask mdmTask = saveMdmTask(MdmTaskSourceEnum.SEASON_GOODS, userDTO, null);
            List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
            for (OrgInfoBaseCache orgInfoBaseCache : filterStoreNoList) {
                List<String> tempGoodsNoList = new ArrayList<>();
                if (Constants.ADD_SEASON_GOODS.equals(seasonalGoodsDTO.getStatus())) {
                    List<String> stringList = removeGoodsNo(seasonalGoodsDTO.getBusinessId(), orgInfoBaseCache.getOutId(), seasonalGoodsDTO.getGoodsNoList());
                    if (CollectionUtils.isNotEmpty(stringList)) {
                        tempGoodsNoList = seasonalGoodsDTO.getGoodsNoList().stream().filter(v -> !stringList.contains(v)).collect(Collectors.toList());
                        logger.info("过滤后的商品集合:{}", tempGoodsNoList.size());
                    } else {
                        tempGoodsNoList = seasonalGoodsDTO.getGoodsNoList();
                    }
                } else {
                    tempGoodsNoList = seasonalGoodsDTO.getGoodsNoList();
                }

                if (CollectionUtils.isNotEmpty(tempGoodsNoList)) {

                    List<List<String>> partition = Lists.partition(tempGoodsNoList, Constants.FEIGN_ONCE_QUERY_MAX);
                    for (List<String> goodsNoList : partition) {
                        Map<String, SpuListVo> spuMap = new HashMap<>();
                        Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
                        MdmQueryParam mdmQueryParam = new MdmQueryParam();
                        if (StringUtils.isBlank(orgInfoBaseCache.getSapCode())) {
                            logger.error("查询门店sapCode编码为空：{}", orgInfoBaseCache.getOutId());
                        }
                        mdmQueryParam.setStoreno(orgInfoBaseCache.getSapCode());
                        mdmQueryParam.setGoodsnoList(goodsNoList);
                        logger.info("请求mdm系统参数：{}", JSONObject.toJSONString(mdmQueryParam));
                        //防止接口限流
                        try {
                            Thread.sleep(100L);
                        } catch (Exception e) {
                            logger.error("必备目录全量表|查询必备目录全量信息出错");
                        }
                        String body = sendPost(mdmQueryParam);
                        if (StringUtils.isBlank(body)) {
                            logger.error("查询mdm获取最小陈列量失败:{}", JSONObject.toJSONString(mdmQueryParam));
                            continue;
                        }
                        JSONObject jsonObject = JSONObject.parseObject(body);
                        String data = jsonObject.getString("data");
                        logger.info("mdm返回最小陈列量data:{}", data);
                        if (StringUtils.isBlank(data)) {
                            logger.error("查询mdm获取最小陈列量数据为空:{}", JSONObject.toJSONString(mdmQueryParam));
                            continue;
                        }
                        List<MdmGoodsInfo> mdmGoodsInfos = JSONObject.parseArray(data, MdmGoodsInfo.class);
                        if (CollectionUtils.isEmpty(mdmGoodsInfos)) {
                            continue;
                        }
                        List<String> nullMinimumDisplayGoods = mdmGoodsInfos.stream().filter(v -> StringUtils.isBlank(v.getMinimumdisplay()) || v.getMinimumdisplay().equals("0") || !StringUtils.isNumeric(v.getMinimumdisplay())).map(MdmGoodsInfo::getGoodsNo).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(nullMinimumDisplayGoods) && Constants.ADD_SEASON_GOODS.equals(seasonalGoodsDTO.getStatus())) {
                            ArrayList<MinDisplayChangeRecord> minDisplayChangeRecords = new ArrayList<>();
                            buildMinDisplayChangeRecord(orgInfoBaseCache, spuMap, nullMinimumDisplayGoods, minDisplayChangeRecords, seasonalGoodsDTO);
                            if (CollectionUtils.isNotEmpty(minDisplayChangeRecords)) {
                                minDisplayChangeRecordExtendMapper.batchInsert(minDisplayChangeRecords);
                                List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(minDisplayChangeRecords.size() + 1);
                                buildMdmTaskDetail(mdmTaskDetails, nullMinimumDisplayGoods, orgInfoBaseCache, mdmTask.getId(), spuMap, mdmTaskDetailIds, seasonalGoodsDTO);
                            }
                        }
                        if (Constants.INTEGER_ZERO.equals(seasonalGoodsDTO.getStatus())) {
                            MinDisplayChangeRecordExample example = new MinDisplayChangeRecordExample();
                            example.createCriteria().andStoreCodeEqualTo(orgInfoBaseCache.getSapCode()).andGoodsNoIn(tempGoodsNoList);
                            List<MinDisplayChangeRecord> minDisplayChangeRecords = minDisplayChangeRecordMapper.selectByExample(example);
                            if (CollectionUtils.isEmpty(minDisplayChangeRecords)) {
                                continue;
                            }
                            StoreGoodsContentsExample storeGoodsInfoExample = new StoreGoodsContentsExample();
                            storeGoodsInfoExample.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andGoodsNoIn(tempGoodsNoList).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode()).andStatusEqualTo(Constants.NORMAL_STATUS).
                                    andNecessaryTagGreaterThan(0);
                            List<String> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(storeGoodsInfoExample).stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList());
                            logger.info("storeGoodsInfos:{}", storeGoodsInfos.size());
                            List<String> mdmMinDisplayGoods = mdmGoodsInfos.stream().filter(v -> StringUtils.isNotBlank(v.getMinimumdisplay()) && v.getMinimumdisplay().equals("1")).map(MdmGoodsInfo::getGoodsNo).collect(Collectors.toList());
                            //记录过更新最小陈列量的商品编码
                            List<String> updateMinDisplayGoodsNoList = minDisplayChangeRecords.stream().map(MinDisplayChangeRecord::getGoodsNo).distinct().collect(Collectors.toList());
                            //一店一目中的不是6级必备更新的商品编码
                            List<String> notNecessaryGoodsNoList = null;
                            if (CollectionUtils.isNotEmpty(storeGoodsInfos) && CollectionUtils.isNotEmpty(mdmMinDisplayGoods)) {
                                notNecessaryGoodsNoList = mdmMinDisplayGoods.stream().filter(v -> !storeGoodsInfos.contains(v)).collect(Collectors.toList());
                                logger.info("一店一目中的不是6级必备更新的商品编码:{}", notNecessaryGoodsNoList.size());
                            } else {
                                notNecessaryGoodsNoList = mdmMinDisplayGoods;
                            }
                            List<String> deleteMinDisplayGoods = null;
                            if (CollectionUtils.isNotEmpty(notNecessaryGoodsNoList)) {
                                List<String> finalNotNecessaryGoodsNoList = notNecessaryGoodsNoList;
                                deleteMinDisplayGoods = updateMinDisplayGoodsNoList.stream().filter(v -> finalNotNecessaryGoodsNoList.contains(v)).collect(Collectors.toList());
                            }
                            if (CollectionUtils.isEmpty(deleteMinDisplayGoods)) {
                                continue;
                            }
                            List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(deleteMinDisplayGoods.size() + 1);
                            buildMdmTaskDetail(mdmTaskDetails, deleteMinDisplayGoods, orgInfoBaseCache, mdmTask.getId(), spuMap, mdmTaskDetailIds, seasonalGoodsDTO);
                            if (CollectionUtils.isNotEmpty(mdmTaskDetails)) {
                                mdmTaskDetails.stream().forEach(v -> v.setMinDisplayQuantity(new BigDecimal(Constants.INTEGER_ZERO)));
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(filterStoreNoList) && Constants.INTEGER_ZERO.equals(seasonalGoodsDTO.getStatus())) {
                minDisplayChangeRecordExtendMapper.batchDel(filterStoreNoList.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList()), seasonalGoodsDTO.getGoodsNoList());
            }
            if (CollectionUtils.isNotEmpty(mdmTaskDetails)) {
                mdmTask.setDetailCount(mdmTaskDetails.size());
                mdmTaskMapper.updateByPrimaryKey(mdmTask);
                Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
                    mdmTaskDetailExtendMapper.batchInsert(v);
                    try {
                        factory.getAssemble(1).pushMdm(v);
                    } catch (Exception e) {
                        logger.info("推送失败");
                        mdmTaskDetailExtendMapper.batchUpdatePushStatus(v.get(0).getTaskId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                        mdmTask.setTaskStatus(MdmStatusTaskEnum.FAIL.getCode());
                        mdmTaskMapper.updateByPrimaryKey(mdmTask);
                    }
                });
            } else {
                mdmTaskMapper.deleteByPrimaryKey(mdmTask.getId());
            }
        } catch (Exception e) {
            logger.error("处理季节品出错", e);
        }
    }

    @Override
    public void updateSingleNecessary(SensitiveUpdateParam param) {
        try {
            List<String> uniqueKeys = StringUtils.isNotBlank(param.getParamUniqueMark()) ? Lists.newArrayList(param.getParamUniqueMark()) : sensitiveUpdateInfoExtendMapper.getUniqueKeys().stream().filter(v -> StringUtils.isNotBlank(v)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(uniqueKeys)) {
                logger.warn("没有需要修改的敏感品数据");
                return;
            }
            TokenUserDTO userDTO = new TokenUserDTO();
            userDTO.setName(Constants.SYS_USER_NAME);
            userDTO.setUserId(Constants.SYS_USER_ID);
            for (String uniqueKey : uniqueKeys) {
                dealSensitive(uniqueKey, param, userDTO);
            }
        } catch (Exception e) {
            logger.error("敏感品消费失败", e);
        }
    }

    @Override
    public void exportMdmTask(Long taskId, HttpServletResponse response) throws Exception {
        MdmTask mdmTask = mdmTaskMapper.selectByPrimaryKey(taskId);
        if (null == mdmTask) {
            throw new BusinessErrorException("没有需要导出的数据");
        }
//        String filename = MdmTaskSourceEnum.getMessageByCode(mdmTask.getTaskSource()) + "_" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN);
        String filename = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN);
        //设置response头信息
        response.reset();
        response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment; filename=" + filename + ".xls");
        try {
            AtomicInteger seq = new AtomicInteger(0);
            HutoolUtil.listToExcelBigData(new HandlerDataExportService<MdmTaskDetailExportDTO>() {
                @Override
                public List<MdmTaskDetailExportDTO> getDataToExport() {
                    return null;
                }

                @Override
                public List<MdmTaskDetailExportDTO> getDataToExport(Integer page, Integer pageSize) {
                    try {
                        return genMdmTaskDetailInfos(page, pageSize, mdmTask, seq);
                    } catch (BusinessErrorException e) {
                        logger.warn("写入登记失败数据到Excel-获取数据列表异常:", e);
                    } catch (Exception e) {
                        logger.error("写入登记失败数据到Excel-获取数据列表异常:", e);
                    }
                    return Collections.emptyList();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }
                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return null;
                }
            }, new ExcelMultiSheetHeaderDTO(MdmTaskDetailExportDTO.getFieldMap(), 5000), response.getOutputStream());
        } catch (Exception e) {
            logger.error("导出敏感品失败:", e);
            throw e;
        }
    }

    private List<MdmTaskDetailExportDTO> genMdmTaskDetailInfos(Integer page, Integer pageSize, MdmTask task, AtomicInteger seq) {
        MdmTaskDetailExample detailExample = new MdmTaskDetailExample();
        detailExample.createCriteria().andTaskIdEqualTo(task.getId());
        detailExample.setLimit(pageSize);
        detailExample.setOffset(Long.valueOf(page * pageSize));
        return mdmTaskDetailMapper.selectByExample(detailExample).stream().map(v -> {
            MdmTaskDetailExportDTO dto = new MdmTaskDetailExportDTO();
            BeanUtils.copyProperties(v, dto);
            Optional<OrgInfoBaseCache> store = CacheVar.getStoreByStoreId(v.getStoreId());
            if (store.isPresent()) {
                dto.setCompanyOrgId(store.get().getBusinessOrgId());
                dto.setBusinessId(store.get().getBusinessId());
                dto.setCompanyCode(store.get().getBusinessSapCode());
                dto.setCompanyName(store.get().getBusinessShortName());
            }
            dto.setSeq(seq.incrementAndGet());
            dto.setTaskSourceDesc(MdmTaskSourceEnum.getMessageByCode(task.getTaskSource()));
            JSONObject jsonObject = JSONObject.parseObject(task.getExtend());
            dto.setPlatformName(jsonObject.getString("platformName"));
            dto.setTaskStatusDesc(MdmStatusTaskEnum.getMessageByCode(task.getTaskStatus()));
//            dto.setNecessaryTagDesc(NecessaryTagEnum.getMessageByCode(v.getNecessaryTag()));
            dto.setPushStatusDesc(MdmTaskPushStatusEnum.getMessageByCode(v.getPushStatus()));
            return dto;
        }).collect(Collectors.toList());
    }

    private void dealSensitive(String paramUniqueMark, SensitiveUpdateParam param, TokenUserDTO userDTO) throws Exception {
        List<String> storeCodesList = CollectionUtils.isNotEmpty(param.getStoreCodes()) ? param.getStoreCodes() : sensitiveUpdateInfoExtendMapper.getStoreCodesByParamUniqueMark(paramUniqueMark);
        if (CollectionUtils.isEmpty(storeCodesList)) {
            logger.warn("paramUniqueMark:{}没有需要修改的敏感品", paramUniqueMark);
            return;
        }
        MdmTask addMdmTask = null;
        MdmTask cancelMdmTask = null;
        MdmTask minDisplayMdmTask = null;
        AtomicInteger addDetailCount = new AtomicInteger(0);
        AtomicInteger cancelDetailCount = new AtomicInteger(0);
        AtomicInteger minDispalyCount = new AtomicInteger(0);
        SensitiveWarnSaveDTO config = iscmService.getSensitivewarnConfig(paramUniqueMark);
        logger.info("config:{}", JSON.toJSONString(config));
        for (String storeCode : storeCodesList) {
            Optional<OrgInfoBaseCache> storeOpt = CacheVar.getStoreBySapCode(storeCode);
            if (!storeOpt.isPresent()) {
                logger.info("ParamUniqueMark:{}门店:{}没有查询到缓存信息", paramUniqueMark, storeCode);
                continue;
            }
            SensitiveUpdateInfoExample example = new SensitiveUpdateInfoExample();
            example.createCriteria().andParamUniqueMarkEqualTo(paramUniqueMark).andStoreCodeEqualTo(storeCode);
            example.setOrderByClause(" id asc");
            List<SensitiveUpdateInfo> sensitiveUpdateInfos = sensitiveUpdateInfoMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(sensitiveUpdateInfos)) {
                logger.info("ParamUniqueMark:{}门店:{}没有待修改敏感品数据", paramUniqueMark);
                continue;
            }
            Map<Byte, List<SensitiveUpdateInfo>> actionMap = sensitiveUpdateInfos.stream().collect(Collectors.groupingBy(SensitiveUpdateInfo::getAction));
            for (Map.Entry<Byte, List<SensitiveUpdateInfo>> entry : actionMap.entrySet()) {
//                if (SensitiveActionEnum.ADD_SINGLE_NECESSARY.getCode().equals(entry.getKey())) {
//                    if (addMdmTask == null) {
//                        addMdmTask = saveMdmTask(MdmTaskSourceEnum.SENSITIVE_ADD, userDTO, paramUniqueMark);
//                    }
//                    try {
//                        sensitiveAdd(entry.getValue(), storeOpt.get(), addMdmTask, addDetailCount, userDTO);
//                    } catch (Exception e) {
//                        logger.error("ParamUniqueMark:{}门店:{}action1修改失败", paramUniqueMark, storeCode, e);
//                    }
//                } else if (SensitiveActionEnum.CANCEL_SINGLE_NECESSARY.getCode().equals(entry.getKey())) {
//                    if (cancelMdmTask == null) {
//                        cancelMdmTask = saveMdmTask(MdmTaskSourceEnum.SENSITIVE_CANCEL, userDTO, paramUniqueMark);
//                    }
//                    try {
//                        sensitiveCancel(entry.getValue(), storeOpt.get(), cancelMdmTask, cancelDetailCount, userDTO);
//                    }  catch (Exception e) {
//                        logger.error("ParamUniqueMark:{}门店:{}action2修改失败", paramUniqueMark, storeCode, e);
//                    }
//                } else
                if (SensitiveActionEnum.MIN_DISPLAY_ZERO.getCode().equals(entry.getKey())){
                    if (minDisplayMdmTask == null) {
                        minDisplayMdmTask = saveMdmTask(MdmTaskSourceEnum.SENSITIVE_DEL_DISPLAY, userDTO, paramUniqueMark);
                    }
                    try {
                        sensitiveMinDisplay(entry.getValue(), storeOpt.get(), minDisplayMdmTask, minDispalyCount, userDTO);
                    }  catch (Exception e) {
                        logger.error("ParamUniqueMark:{}门店:{}action3修改失败", paramUniqueMark, storeCode, e);
                    }
                } else {
                    logger.error("ParamUniqueMark:{}门店:{}action{}无效", paramUniqueMark, entry.getKey());
                }
            }
        }
        logger.info("addMdmTask:{},cancelMdmTask:{},minDisplayMdmTask:{}", JSON.toJSONString(addMdmTask),JSON.toJSONString(cancelMdmTask),JSON.toJSONString(minDisplayMdmTask));
        logger.info("addDetailCount:{},cancelDetailCount:{}, minDispalyCount:{}", addDetailCount, cancelDetailCount, minDispalyCount);
        if (addMdmTask != null && addDetailCount.get() > 0) {
            addMdmTask.setDetailCount(addDetailCount.get());
            addMdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
            mdmTaskMapper.updateByPrimaryKeySelective(addMdmTask);
            pushMdmTask(addMdmTask);
            if (null != config) {
                sendWarn(config, addDetailCount.get(), paramUniqueMark);
            }
        }
        if (cancelMdmTask != null && cancelDetailCount.get() > 0) {
            cancelMdmTask.setDetailCount(cancelDetailCount.get());
            cancelMdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
            mdmTaskMapper.updateByPrimaryKeySelective(cancelMdmTask);
            pushMdmTask(cancelMdmTask);
            if (null != config) {
                sendWarn(config, cancelDetailCount.get(), paramUniqueMark);
            }
        }
        if (minDisplayMdmTask != null && minDispalyCount.get() > 0) {
            minDisplayMdmTask.setDetailCount(minDispalyCount.get());
            minDisplayMdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
            mdmTaskMapper.updateByPrimaryKeySelective(minDisplayMdmTask);
            pushMdmTask(minDisplayMdmTask);
            if (null != config) {
                sendWarn(config, minDispalyCount.get(), paramUniqueMark);
            }
        }
    }

    private void sendWarn(SensitiveWarnSaveDTO config, Integer count, String paramUniqueMark) {
        logger.info( prefix + "/See/amisPageV2/earlyWarning?paramUniqueMark=" + paramUniqueMark);
        String channel = config.getWarnChannelRange().getParamValue();
        List<String> empCodes = config.getWarnEmpRange().getDropDownBoxDTOS().stream().map(emp -> {
            String[] split1 = org.apache.commons.lang.StringUtils.split(emp, "-");
            return split1[0];
        }).distinct().collect(Collectors.toList());
        List<EmployeeInfoVO> empVOS = permissionService.getListByEmpCodesAndStatus(empCodes);
        logger.info("empVOS:{}", JSON.toJSONString(empVOS));
        String msg = empVOS.stream().map(EmployeeInfoVO::getName).collect(Collectors.joining(",")) + "：您好，供应链控制塔提醒，今天更新了" + count + "行一店一目数据，请查看详情。" +
                prefix + "/See/amisPageV2/earlyWarning?paramUniqueMark=" + paramUniqueMark +
                "更新原则：1. 门店敏感品新增，系统自动设置单店必备+最小陈列量=1。\n" +
                "        2. 门店敏感品取消，如果当前为单店必备，系统自动取消必备+最小陈列量=0。\n" +
                "        3. 非必备非敏感品，门店180天无动销，系统自动设置最小陈列量=0。";
        if (channel.contains("企业微信")) {
            String wxUsers = empVOS.stream().map(EmployeeInfoVO::getWxId).filter(org.apache.commons.lang.StringUtils::isNotBlank).distinct().collect(Collectors.joining("|"));
            if (org.apache.commons.lang.StringUtils.isBlank(wxUsers)) {
                logger.info("预警人员企业微信号为空");
            } else {
                AlertContent alert = new AlertContent();
                alert.setType("QYWX");
                alert.setFlag(true);
                alert.setToUsers(wxUsers);
                alert.setSubject("供应链控制塔提醒");
                alert.setMessage(msg);
                alertService.alert(alert);
            }
        }
        if (channel.contains("邮件")) {
            String emails = empVOS.stream().map(EmployeeInfoVO::getEmail).filter(org.apache.commons.lang.StringUtils::isNotBlank).distinct().collect(Collectors.joining("|"));
            if (org.apache.commons.lang.StringUtils.isBlank(emails)) {
                logger.info("预警人员邮件地址为空");
            } else {
                    AlertContent alert = new AlertContent();
                    alert.setType("EMAIL");
                    alert.setFlag(true);
                    alert.setToUsers(emails);
                    alert.setSubject("供应链控制塔提醒");
                    alert.setMessage(msg);
                    alertService.alert(alert);
            }
        }

    }
    private void sensitiveMinDisplay(List<SensitiveUpdateInfo> updateInfos, OrgInfoBaseCache store, MdmTask minDisplayMdmTask, AtomicInteger minDispalyCount, TokenUserDTO userDTO) {
        StoreGoodsContentsExample example = new StoreGoodsContentsExample();
        example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(updateInfos.stream().map(SensitiveUpdateInfo::getGoodsNo).distinct().collect(Collectors.toList())).andNecessaryTagGreaterThan(0).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
        Map<String, StoreGoodsContents> existMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(v -> v.getGoodsNo(), Function.identity(), (k1,k2) -> k1));
        updateInfos = updateInfos.stream().filter(v -> existMap.containsKey(v.getGoodsNo())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateInfos)) {
            StoreGoodsContents update = new StoreGoodsContents();
            update.setMinDisplayQuantity(BigDecimal.ZERO);
            update.setUpdatedBy(userDTO.getUserId());
            update.setUpdatedName(userDTO.getName());
            update.setGmtUpdate(new Date());
            example.clear();
            example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(updateInfos.stream().map(SensitiveUpdateInfo::getGoodsNo).collect(Collectors.toList()));
            storeGoodsContentsMapper.updateByExampleSelective(update, example);
            minDispalyCount.addAndGet(genMdmTaskDetail(minDisplayMdmTask, true, userDTO, updateInfos, store, existMap));
        }
    }

//    private void sensitiveCancel(List<SensitiveUpdateInfo> updateInfos, OrgInfoBaseCache store, MdmTask cancelMdmTask, AtomicInteger cancelDetailCount, TokenUserDTO userDTO) {
//        NecessarySingleStoreGoodsExample singleExample = new NecessarySingleStoreGoodsExample();
//        singleExample.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(updateInfos.stream().map(SensitiveUpdateInfo::getGoodsNo).distinct().collect(Collectors.toList()));
//        necessarySingleStoreGoodsMapper.deleteByExample(singleExample);
//        StoreGoodsInfoExample example = new StoreGoodsInfoExample();
//        example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(updateInfos.stream().map(SensitiveUpdateInfo::getGoodsNo).distinct().collect(Collectors.toList())).andNecessaryTagIn(Lists.newArrayList(NecessaryTagEnum.GROUP_NECESSARY.getCode(),NecessaryTagEnum.PLATFORM_NECESSARY.getCode(),NecessaryTagEnum.COMPANY_NECESSARY.getCode(),NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode(),NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode())).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
//        Map<String, StoreGoodsInfo> existMap  = storeGoodsInfoMapper.selectByExample(example).stream().collect(Collectors.toMap(v -> v.getGoodsNo(), Function.identity(), (k1,k2) -> k1));;
//        List<SensitiveUpdateInfo> dealList = updateInfos.stream().filter(v -> !existMap.containsKey(v.getGoodsNo())).collect(Collectors.toList());
//        // 一店一目数据改为非必备 最小陈列量清零
//        if (CollectionUtils.isNotEmpty(dealList)) {
//            StoreGoodsInfo update = new StoreGoodsInfo();
//            update.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
//            update.setMinDisplayQuantity(BigDecimal.ZERO);
//            update.setUpdatedBy(userDTO.getUserId());
//            update.setUpdatedName(userDTO.getName());
//            update.setGmtUpdate(new Date());
//            example.clear();
//            example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(dealList.stream().map(SensitiveUpdateInfo::getGoodsNo).collect(Collectors.toList()));
//            storeGoodsInfoMapper.updateByExampleSelective(update, example);
////            storeGoodsInfoExtendMapper.updateNecessaryTagByIds(store.getOutId(), storeGoodsInfos.stream().map(StoreGoodsInfo::getId).collect(Collectors.toList()), NecessaryTagEnum.NONE_NECESSARY.getCode(), BigDecimal.ZERO);
//            cancelDetailCount.addAndGet(genMdmTaskDetail(cancelMdmTask, false, userDTO, dealList, store));
//        }
//    }

    private int genMdmTaskDetail(MdmTask mdmTask, boolean mindisplayAble, TokenUserDTO userDTO, List<SensitiveUpdateInfo> updateInfos, OrgInfoBaseCache store, Map<String, StoreGoodsContents> existMap) {
        List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
        Map<String, SpuListVo> spuMap = new HashMap<>();
        List<String> goodsNos = updateInfos.stream().map(SensitiveUpdateInfo::getGoodsNo).collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(goodsNos, Constants.QUERY_SEARCH_PAGESIZE);
        for (List<String> v : partition) {
            try {
                spuMap.putAll(searchService.getSpuVOMap(goodsNos));
            } catch (Exception e) {
                logger.error("取消查询商品错误", e);
                continue;
            }
        }
        List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(updateInfos.size() + 1);
        for (int i = 0; i < updateInfos.size(); i++) {
            MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
            mdmTaskDetail.setId(mdmTaskDetailIds.get(i));
            mdmTaskDetail.setTaskId(mdmTask.getId());
            String goodsNo = updateInfos.get(i).getGoodsNo();
            mdmTaskDetail.setGoodsNo(goodsNo);
            if (MapUtils.isNotEmpty(spuMap) && Objects.nonNull(spuMap.get(goodsNo))) {
                SpuListVo spuListVo = spuMap.get(goodsNo);
                mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
                mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
                mdmTaskDetail.setDescription(spuListVo.getDescription());
                mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
                mdmTaskDetail.setBarCode(spuListVo.getBarCode());
                mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
                mdmTaskDetail.setGoodsName(spuListVo.getName());
                mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
                mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
            }
            StoreGoodsContents contents = existMap.get(goodsNo);
            if (null != contents) {
                mdmTaskDetail.setNecessaryTag(contents.getNecessaryTag());
                mdmTaskDetail.setNecessaryTagName(contents.getNecessaryTagName());
            } else {
                mdmTaskDetail.setNecessaryTag(0);
                mdmTaskDetail.setNecessaryTagName("");
            }
            mdmTaskDetail.setUpdatedBy(userDTO.getUserId());
            mdmTaskDetail.setCreatedBy(userDTO.getUserId());
            mdmTaskDetail.setCreatedName(userDTO.getName());
            mdmTaskDetail.setUpdatedName(userDTO.getName());
            mdmTaskDetail.setStoreOrgId(store.getId());
            mdmTaskDetail.setStoreId(store.getOutId());
            mdmTaskDetail.setStoreCode(store.getSapCode());
            mdmTaskDetail.setStoreName(store.getShortName());
//            if (!mindisplayAble) {
//                mdmTaskDetail.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
//            } else {
//                mdmTaskDetail.setNecessaryTag(NecessaryTagEnum.UNSALABLE_CANCEL_MINDISPLAY.getCode());
//            }
//            mdmTaskDetail.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
            mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
            mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
            mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
            mdmTaskDetail.setGmtUpdate(new Date());
            mdmTaskDetails.add(mdmTaskDetail);
        }
        mdmTaskDetailExtendMapper.batchInsert(mdmTaskDetails);
        return mdmTaskDetails.size();
    }

    private void pushMdmTask(MdmTask mdmTask) {
        MdmTaskDetailExample detailExample = new MdmTaskDetailExample();
        detailExample.createCriteria().andTaskIdEqualTo(mdmTask.getId());
        for (int i = 0;; i++) {
            detailExample.setLimit(Constants.INSERT_MAX_SIZE);
            detailExample.setOffset(Long.valueOf(Constants.INSERT_MAX_SIZE * i));
            List<MdmTaskDetail> mdmTaskDetails = mdmTaskDetailMapper.selectByExample(detailExample);
            if(CollectionUtils.isEmpty(mdmTaskDetails)) {
                return;
            }
            try {
                factory.getAssemble(1).pushMdm(mdmTaskDetails);
            } catch (Exception e) {
                logger.info("第" + (i+1) + "次推送失败");
                mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), mdmTaskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
            }
        }
    }

    private void sensitiveAdd(List<SensitiveUpdateInfo> updateInfos, OrgInfoBaseCache store, MdmTask addMdmTask, AtomicInteger detailCount, TokenUserDTO userDTO) throws Exception {
        // 首先查一店一目是不是必备，如果是必备，则舍弃该行不处理。 如果不是必备，则按照以下逻辑处理：
        // 增加单店必备，引入原因（choose_reason）=‘61单店必备-规则引入-敏感品’   同时创建mdm更新任务，同步mdm。
        // mdm更新任务：任务来源=自动化策略-敏感品打标；创建人=系统管理员
        List<NecessarySingleStoreGoods> singleStoreGoodsList = new ArrayList<>();
        Map<String, NecessaryCommonGoodsDTO> goodsDTOMap = assembleCommon(updateInfos.stream().map(SensitiveUpdateInfo::getGoodsNo).distinct().collect(Collectors.toList()), store, userDTO).stream().collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1, k2) -> k1));
        updateInfos.forEach(v -> {
            NecessaryCommonGoodsDTO goodsDTO = goodsDTOMap.get(v.getGoodsNo());
            Optional<MdmStoreExDTO> storeExt = CacheVar.getStoreExtInfoByStoreId(store.getOutId());
            if (storeExt.isPresent() && null != goodsDTO) {
                NecessarySingleStoreGoods singleStoreGoods = new NecessarySingleStoreGoods();
                BeanUtils.copyProperties(goodsDTO, singleStoreGoods);
                singleStoreGoods.setPlatformOrgId(store.getPlatformOrgId());
                singleStoreGoods.setPlatformName(store.getPlatformShortName());
                singleStoreGoods.setCompanyOrgId(store.getBusinessOrgId());
                singleStoreGoods.setCompanyCode(store.getBusinessSapCode());
                singleStoreGoods.setCompanyName(store.getBusinessShortName());
                singleStoreGoods.setBusinessid(store.getBusinessId());
                singleStoreGoods.setCity(storeExt.get().getCity());
                singleStoreGoods.setStoreType("");
                singleStoreGoods.setStoreOrgId(store.getId());
                singleStoreGoods.setStoreId(store.getOutId());
                singleStoreGoods.setStoreCode(store.getSapCode());
                singleStoreGoods.setStoreName(store.getShortName());
                singleStoreGoods.setChooseReason("61单店必备-规则引入-敏感品");
                singleStoreGoodsList.add(singleStoreGoods);
            }
        });
        if (CollectionUtils.isEmpty(singleStoreGoodsList)) {
            logger.info("敏感品单店必备没有数据");
            return;
        }
        List<NecessarySingleStoreGoods> insertList = new ArrayList<>();
        List<StoreGoodsInfo> insertStoreGoodsList = new ArrayList<>();
        NecessarySingleStoreGoodsExample storeExample = new NecessarySingleStoreGoodsExample();
        storeExample.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(singleStoreGoodsList.stream().map(NecessarySingleStoreGoods::getGoodsNo).distinct().collect(Collectors.toList()));
        Map<String, NecessarySingleStoreGoods> existsMap = necessarySingleStoreGoodsMapper.selectByExample(storeExample).stream().collect(Collectors.toMap(v -> v.getStoreId() + "-" + v.getGoodsNo(), Function.identity(), (k1, k2) -> k1));
        // 查一店一目是不是必备，如果是必备，则舍弃该行不处理。 如果不是必备，则按照以下逻辑处理：
        StoreGoodsInfoExample example = new StoreGoodsInfoExample();
        Map<String, NecessarySingleStoreGoods> map = singleStoreGoodsList.stream().collect(Collectors.toMap(NecessarySingleStoreGoods::getGoodsNo, Function.identity(), (k1, k2) -> k1));
        example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(singleStoreGoodsList.stream().map(NecessarySingleStoreGoods::getGoodsNo).distinct().collect(Collectors.toList())).andNecessaryTagNotEqualTo(NecessaryTagEnum.NONE_NECESSARY.getCode()).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
        Map<String, StoreGoodsInfo> existsStoreGoodsInfos = storeGoodsInfoMapper.selectByExample(example).stream().collect(Collectors.toMap(v -> v.getStoreId() + "-" + v.getGoodsNo(), Function.identity(), (k1, k2) -> k1));
        singleStoreGoodsList.forEach(v -> {
            if (!existsStoreGoodsInfos.containsKey(v.getStoreId() + "-" + v.getGoodsNo())) {
                StoreGoodsInfo storeGoodsInfo = new StoreGoodsInfo();
                BeanUtils.copyProperties(v, storeGoodsInfo, "id");
                storeGoodsInfo.setSubCategoryId(v.getSubCategoryId());
                storeGoodsInfo.setNecessaryTag(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
                storeGoodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                storeGoodsInfo.setMinDisplayQuantity(BigDecimal.ONE);
                insertStoreGoodsList.add(storeGoodsInfo);
                if (!existsMap.containsKey(v.getStoreId() + "-" + v.getGoodsNo())) {
                    insertList.add(map.get(v.getGoodsNo()));
                }
            }
        });
        if (CollectionUtils.isEmpty(singleStoreGoodsList)) {
            logger.info("过滤一店一目存在的品后本次循环没数据");
            return;
        }
        logger.info("insertStoreGoodsList size:{}", insertStoreGoodsList.size());
        logger.info("insertList:{}", insertList.size());
        if (CollectionUtils.isNotEmpty(insertList)) {
            necessarySingleStoreGoodsExtendMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isEmpty(insertStoreGoodsList)) {
            logger.info("没有需要插入的一店一目商品");
            return;
        }
        List<Long> storeGoodsIds = necessaryContentsService.getStoreGoodsIds(insertStoreGoodsList.size() + 1);
        List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(insertStoreGoodsList.size() + 1);

        insertStoreGoodsList.forEach(v -> v.setId(storeGoodsIds.remove(0)));
        StoreGoodsInfoExample delExample = new StoreGoodsInfoExample();
        delExample.createCriteria().andStoreIdEqualTo(store.getOutId()).andStoreOrgIdEqualTo(store.getId()).andGoodsNoIn(insertStoreGoodsList.stream().map(StoreGoodsInfo::getGoodsNo).collect(Collectors.toList()));
        storeGoodsInfoMapper.deleteByExample(delExample);
        Lists.partition(insertStoreGoodsList, Constants.INSERT_MAX_SIZE).forEach(v -> {
            storeGoodsInfoExtendMapper.batchInsert(v);
        });

        List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
        List<String> goodsNos = insertStoreGoodsList.stream().map(StoreGoodsInfo::getGoodsNo).collect(Collectors.toList());
        Map<String, SpuListVo> spuMap = new HashMap<>();
        Lists.partition(goodsNos, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNos)));

        insertStoreGoodsList.forEach(v -> {
            MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
            mdmTaskDetail.setGoodsNo(v.getGoodsNo());
            SpuListVo spuListVo = spuMap.get(v.getGoodsNo());
            if (Objects.nonNull(spuListVo)) {
                mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
                mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
                mdmTaskDetail.setDescription(spuListVo.getDescription());
                mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
                mdmTaskDetail.setBarCode(spuListVo.getBarCode());
                mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
                mdmTaskDetail.setGoodsName(spuListVo.getName());
                mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
                mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
            }
            mdmTaskDetail.setStoreOrgId(store.getId());
            mdmTaskDetail.setStoreId(store.getOutId());
            mdmTaskDetail.setStoreCode(store.getSapCode());
            mdmTaskDetail.setStoreName(store.getShortName());
            mdmTaskDetail.setId(mdmTaskDetailIds.remove(0));
            mdmTaskDetail.setTaskId(addMdmTask.getId());
//            mdmTaskDetail.setNecessaryTag(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
            mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ONE);
            mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
            mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
            mdmTaskDetail.setGmtCreate(new Date());
            mdmTaskDetail.setGmtUpdate(new Date());
            mdmTaskDetails.add(mdmTaskDetail);
        });
        detailCount.addAndGet(mdmTaskDetails.size());
        Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
            mdmTaskDetailExtendMapper.batchInsert(v);
        });
    }

    protected List<NecessaryCommonGoodsDTO> assembleCommon(List<String> goodsNos, OrgInfoBaseCache store, TokenUserDTO userDTO) throws Exception {
        Map<String, SpuListVo> spuMap = new HashMap<>();
        Map<Long, CommonCategoryDTO> categoryMap = new HashMap<>();
        Map<String, Map<String, String>> componentMap = new HashMap<>();
        Map<String, String> grossprofitMap = new HashMap<>();
        Lists.partition(goodsNos, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
            spuMap.putAll(searchService.getSpuVOMap(v));
            categoryMap.putAll(getCategoryBySubIds(spuMap.values().stream().map(goo -> Long.valueOf(goo.getCategoryId())).distinct().collect(Collectors.toList())));
            componentMap.putAll(forestService.querySpuProperties(Lists.newArrayList("component"), v, null));
            Map<String, Map<String, String>> grossprofit = forestService.querySpuProperties(Lists.newArrayList("grossprofit"), v, store.getBusinessId());
            for (Map.Entry<String, Map<String, String>> entry : grossprofit.entrySet()) {
                Map<String, String> value = entry.getValue();
                if (MapUtils.isEmpty(value) || !value.containsKey("grossprofit")) {
                    continue;
                }
                PurchaseAttrEnum newEnum = PurchaseAttrEnum.getEnumByCode(value.get("grossprofit"));
                if (Objects.isNull(newEnum)) {
                    continue;
                }
                if (!grossprofitMap.containsKey(entry.getKey())) {
                    grossprofitMap.put(entry.getKey(), newEnum.getCode());
                } else {
                    PurchaseAttrEnum oldEnum = PurchaseAttrEnum.getEnumByCode(grossprofitMap.get(entry.getKey()));
                    if (oldEnum.ordinal() > newEnum.ordinal()) {
                        grossprofitMap.put(entry.getKey(), newEnum.getCode());
                    }
                }
            }
        });
        if (MapUtils.isEmpty(spuMap)) {
            logger.info("商品:{},信息有误,查不到对应商品" + goodsNos.stream().collect(Collectors.joining("、")) + "");
            return new ArrayList<>();
        }
        List<String> failGoods = new ArrayList<>();
        List<NecessaryCommonGoodsDTO> commonGoodsList = new ArrayList<>();
        for(Map.Entry<String, SpuListVo> entry : spuMap.entrySet()){
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            CommonGoodsDTO commonGoodsDTO = searchService.getCommonGoods(entry.getValue());
            BeanUtils.copyProperties(commonGoodsDTO, goodsDTO);
            CommonUserDTO commonUserDTO = new CommonUserDTO(userDTO);
            BeanUtils.copyProperties(commonUserDTO, goodsDTO);
            CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(entry.getValue().getCategoryId()));
            if (Objects.isNull(commonCategoryDTO)) {
                logger.info("商品:{}没有查询到四级类目信息", entry.getKey());
                failGoods.add(entry.getKey());
                continue;
            }
            BeanUtils.copyProperties(commonCategoryDTO, goodsDTO);
            goodsDTO.setPlatformOrgId(store.getPlatformOrgId());
            goodsDTO.setPlatformName(store.getPlatformShortName());
            // 成分 标品属性 component
            Map<String, String> component = componentMap.get(entry.getKey());
            if (org.apache.commons.collections.MapUtils.isNotEmpty(component)) {
                goodsDTO.setComposition(component.get("component"));
            }
            //采购属性 企业级 grossprofit
            goodsDTO.setPurchaseAttr(grossprofitMap.get(entry.getKey()));
            goodsDTO.setNecessaryTag(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
            commonGoodsList.add(goodsDTO);
        }
        if (CollectionUtils.isEmpty(commonGoodsList)) {
            throw new AmisBadRequestException("商品:" + failGoods.stream().collect(Collectors.joining("、")) + "没有查询到四级类目信息");
        }
        return commonGoodsList;
    }

    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        logger.info("subCategoryIds:{}", JSON.toJSONString(subCategoryIds));
        if (CollectionUtils.isEmpty(subCategoryIds)) {
            return new HashMap<>();
        }
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1,k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        return resultMap;
    }


    private MdmTask saveMdmTask(MdmTaskSourceEnum taskSourceEnum, TokenUserDTO userDTO, String extend) {
        MdmTask mdmTask = new MdmTask();
        mdmTask.setTaskSource(taskSourceEnum.getCode());
        mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
        mdmTask.setDetailCount(0);
        mdmTask.setStatus(Constants.NORMAL_STATUS);
        mdmTask.setUpdatedName(userDTO.getName());
        mdmTask.setUpdatedBy(userDTO.getUserId());
        mdmTask.setGmtUpdate(new Date());
        mdmTask.setCreatedName(userDTO.getName());
        mdmTask.setCreatedBy(userDTO.getUserId());
        mdmTask.setGmtCreate(new Date());
        mdmTask.setGmtUpdate(new Date());
        if (StringUtils.isNotBlank(extend)) {
            String[] split = extend.split("-");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("platformOrgId", Long.valueOf(split[1]));
            Optional<OrgInfoBaseCache> platform = CacheVar.getPlatformByOrgId(Long.valueOf(split[1]));
            if (platform.isPresent()) {
                jsonObject.put("platformName", platform.get().getShortName());
            }
            mdmTask.setExtend(jsonObject.toJSONString());
            mdmTask.setRemarks(extend);
        }
        mdmTaskMapper.insertSelective(mdmTask);
        return mdmTask;
    }

    private void buildMdmTaskDetail(List<MdmTaskDetail> mdmTaskDetails,List<String> nullMinimumDisplayGoods,OrgInfoBaseCache orgInfoBaseCache, Long mdmTaskId, Map<String, SpuListVo> spuMap,List<Long> mdmTaskDetailIds,SeasonalGoodsPushDTO seasonalGoodsDTO) {
        for (int i = 0; i < nullMinimumDisplayGoods.size(); i++) {
            MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
            mdmTaskDetail.setId(mdmTaskDetailIds.get(i));
            mdmTaskDetail.setTaskId(mdmTaskId);
            String goodsNo = nullMinimumDisplayGoods.get(i);
            mdmTaskDetail.setGoodsNo(goodsNo);
            if (MapUtils.isNotEmpty(spuMap) && Objects.nonNull(spuMap.get(goodsNo))){
                SpuListVo spuListVo = spuMap.get(goodsNo);
                mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
                mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
                mdmTaskDetail.setDescription(spuListVo.getDescription());
                mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
                mdmTaskDetail.setBarCode(spuListVo.getBarCode());
                mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
                mdmTaskDetail.setGoodsName(spuListVo.getName());
                mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
                mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
            }
            mdmTaskDetail.setUpdatedBy(seasonalGoodsDTO.getUserId());
            mdmTaskDetail.setCreatedBy(seasonalGoodsDTO.getUserId());
            mdmTaskDetail.setCreatedName(seasonalGoodsDTO.getUserName());
            mdmTaskDetail.setUpdatedName(seasonalGoodsDTO.getUserName());
            mdmTaskDetail.setStoreOrgId(orgInfoBaseCache.getId());
            mdmTaskDetail.setStoreId(orgInfoBaseCache.getOutId());
            mdmTaskDetail.setStoreCode(orgInfoBaseCache.getSapCode());
            mdmTaskDetail.setStoreName(orgInfoBaseCache.getShortName());
            mdmTaskDetail.setNecessaryTag(0);
            mdmTaskDetail.setNecessaryTagName("");
            mdmTaskDetail.setMinDisplayQuantity(new BigDecimal("1"));
            mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
            mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
            mdmTaskDetail.setGmtUpdate(new Date());
            mdmTaskDetails.add(mdmTaskDetail);
        }

    }

    private void buildMinDisplayChangeRecord(OrgInfoBaseCache orgInfoBaseCache, Map<String, SpuListVo> spuMap, List<String> nullMinimumDisplayGoods, ArrayList<MinDisplayChangeRecord> minDisplayChangeRecords,SeasonalGoodsPushDTO seasonalGoodsDTO) {
        for (String goodsNo : nullMinimumDisplayGoods) {
            MinDisplayChangeRecord minDisplayChangeRecord = new MinDisplayChangeRecord();
            minDisplayChangeRecord.setStoreCode(orgInfoBaseCache.getSapCode());
            minDisplayChangeRecord.setStoreId(orgInfoBaseCache.getOutId());
            minDisplayChangeRecord.setStoreOrgId(orgInfoBaseCache.getId());
            minDisplayChangeRecord.setStoreName(orgInfoBaseCache.getShortName());
            minDisplayChangeRecord.setMinDisplayQuantity(new BigDecimal(1));
            minDisplayChangeRecord.setGoodsNo(goodsNo);
            minDisplayChangeRecord.setGoodsName("");
            if (MapUtils.isNotEmpty(spuMap) && Objects.nonNull(spuMap.get(goodsNo))){
                minDisplayChangeRecord.setGoodsName(spuMap.get(goodsNo).getCurName());
            }
            minDisplayChangeRecord.setGmtCreate(new Date());
            minDisplayChangeRecord.setGmtUpdate(new Date());
            minDisplayChangeRecord.setCreatedBy(Constants.ZERO);
            minDisplayChangeRecord.setUpdatedBy(Constants.ZERO);
            minDisplayChangeRecord.setCreatedName(seasonalGoodsDTO.getUserName());
            minDisplayChangeRecord.setCreatedBy(seasonalGoodsDTO.getUserId());
            minDisplayChangeRecord.setUpdatedName(seasonalGoodsDTO.getUserName());
            minDisplayChangeRecord.setUpdatedBy(seasonalGoodsDTO.getUserId());
            minDisplayChangeRecord.setExtend("");
            minDisplayChangeRecord.setVersion(Constants.INTEGER_ZERO);
            minDisplayChangeRecord.setStatus(Constants.NORMAL_STATUS);
            minDisplayChangeRecords.add(minDisplayChangeRecord);
        }
    }


    private String sendPost(MdmQueryParam mdmQueryParam) {
        try {
            String encoding = DatatypeConverter.printBase64Binary((username + ":" + password).getBytes(StandardCharsets.UTF_8));
            logger.info("请求mdm的url：{}",url);
            Map<String,String>  headermap=new HashMap<>();
            headermap.put("Authorization", "Basic " + encoding);
            System.out.println(headermap);
            String body = ApacheHttpUtil.doPost(url, JSONObject.toJSONString(mdmQueryParam), headermap);
            return body;
        } catch (Exception e) {
            logger.error("请求mdm一店一目数据|发生异常 mdmQueryParam ={}",mdmQueryParam, e );
        }
        return "";
    }
}
