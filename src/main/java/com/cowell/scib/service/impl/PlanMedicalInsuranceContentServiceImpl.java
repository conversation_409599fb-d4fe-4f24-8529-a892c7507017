package com.cowell.scib.service.impl;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.entityDgms.PlanMedicalInsuranceContents;
import com.cowell.scib.entityDgms.PlanMedicalInsuranceContentsExample;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.mapperDgms.PlanMedicalInsuranceContentsMapper;
import com.cowell.scib.mapperDgms.extend.PlanMedicalInsuranceContentsExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.PlanMedicalInsuranceContentService;
import com.cowell.scib.service.dto.ExcelFileDomain;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.planContents.PlanContentParam;
import com.cowell.scib.service.dto.planContents.PlanGoodsImportDTO;
import com.cowell.scib.service.dto.planContents.PlanMedicalInsuranceContentsDTO;
import com.cowell.scib.service.dto.rule.HotGoodsResponseDTO;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.HutoolUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PlanMedicalInsuranceContentServiceImpl implements PlanMedicalInsuranceContentService {

    private final Logger logger = LoggerFactory.getLogger(PlanMedicalInsuranceContentServiceImpl.class);
    @Resource
    private PlanMedicalInsuranceContentsMapper planMedicalInsuranceContentsMapper;
    @Resource
    private PlanMedicalInsuranceContentsExtendMapper planMedicalInsuranceContentsExtendMapper;

    @Value("${scib.plan.medical.insurance.maxLinesNums:20000}")
    private Integer maxLinesNums;

    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;
    @Override
    public PageResult<PlanMedicalInsuranceContentsDTO> list(PlanContentParam param) {
        try {
            PageResult<PlanMedicalInsuranceContentsDTO> pageResult = new PageResult<>(0L, new ArrayList<>());
            PlanMedicalInsuranceContentsExample example = genSelectExample(param);
            long count = planMedicalInsuranceContentsMapper.countByExample(example);
            if (count <= 0L) {
                return pageResult;
            }
            example.setLimit(param.getPerPage());
            example.setOffset(Long.valueOf((param.getPage() - 1) * param.getPerPage()));
            example.setOrderByClause(" province asc");
            List<PlanMedicalInsuranceContentsDTO> result = planMedicalInsuranceContentsMapper.selectByExample(example).stream().map(v -> {
                PlanMedicalInsuranceContentsDTO dto = new PlanMedicalInsuranceContentsDTO();
                BeanUtils.copyProperties(v, dto);
                return dto;
            }).collect(Collectors.toList());
            return new PageResult<>(count, result);
        } catch (Exception e) {
            logger.error("查询统筹医保目录失败:", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void deleteBySelect(PlanContentParam param) {
        try {
            if (StringUtils.isBlank(param.getProvince()) && StringUtils.isBlank(param.getCity()) && StringUtils.isBlank(param.getGoodsNos())) {
                throw new AmisBadRequestException("请至少选择一个查询条件");
            }
            PlanMedicalInsuranceContentsExample example = genSelectExample(param);
            planMedicalInsuranceContentsMapper.deleteByExample(example);
        } catch (Exception e) {
            logger.error("根据查询删除统筹医保目录失败:", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public HotGoodsResponseDTO importPlan(MultipartFile file, TokenUserDTO userDTO) {
        try {
            HotGoodsResponseDTO hotGoodsResponseDTO = new HotGoodsResponseDTO();
            List<String> errorMsg = new ArrayList<>();
            List<PlanGoodsImportDTO> hotGoodsImportDTOList = resolvePlanGoodsAndCheck(file, null, errorMsg);
            if (CollectionUtils.isEmpty(hotGoodsImportDTOList)) {
                if (CollectionUtils.isNotEmpty(errorMsg)) {
                    hotGoodsResponseDTO.setMessage(errorMsg.stream().distinct().collect(Collectors.joining(";")));
                }
                return hotGoodsResponseDTO;
            }
            List<PlanMedicalInsuranceContents> hotGoodsList = hotGoodsImportDTOList.stream().map(v->{
                PlanMedicalInsuranceContents contents = new PlanMedicalInsuranceContents();
                BeanUtils.copyProperties(v, contents);
                contents.setCreateById(userDTO.getUserId());
                contents.setCreateBy(userDTO.getName());
                contents.setUpdateBy(userDTO.getName());
                contents.setUpdateById(userDTO.getUserId());
                return contents;
            }).collect(Collectors.toList());
            asyncTaskExecutor.execute(() -> {
                for (List<PlanMedicalInsuranceContents> list : Lists.partition(hotGoodsList, 500)) {
                    planMedicalInsuranceContentsExtendMapper.batchInsert(list);
                }
            });
            hotGoodsResponseDTO.setMessage(new StringBuilder(errorMsg.stream().distinct().collect(Collectors.joining(";"))).append("\n导入成功").append(hotGoodsList.size()).append("条数据").toString());
            return hotGoodsResponseDTO;

        } catch (Exception e) {
            logger.error("导入统筹医保目录失败:", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }
    protected List<PlanGoodsImportDTO> resolvePlanGoodsAndCheck(MultipartFile file, List<PlanGoodsImportDTO> responseDTOList, List<String> errorMsg) throws Exception {
        unifyFileCheck(file);
        List<PlanGoodsImportDTO> importDTOS = HutoolUtil.excelToList(file.getInputStream(), ExcelFileDomain.getPlanGoodsMap(), PlanGoodsImportDTO.class, 1);
        if (CollectionUtils.isEmpty(importDTOS)) {
            throw new AmisBadRequestException(ErrorCodeEnum.RULE_FILE_EMPTY.getMsg());
        }
        long nullCount = importDTOS.stream().filter(v-> org.apache.commons.lang3.StringUtils.isEmpty(v.getProvince())
                || org.apache.commons.lang3.StringUtils.isEmpty(v.getCity())
                || org.apache.commons.lang3.StringUtils.isEmpty(v.getGoodsNo())).count();
        if(nullCount > 0){
            throw new AmisBadRequestException(ErrorCodeEnum.PLAN_FILE_MUST.getMsg());
        }
        if (importDTOS.size() > maxLinesNums) {
            throw new AmisBadRequestException("超过最大行数："+maxLinesNums);
        }
        List<String> provinces = importDTOS.stream().map(v->v.getProvince()).distinct().collect(Collectors.toList());
        List<String> city = importDTOS.stream().map(v->v.getCity()).distinct().collect(Collectors.toList());
        PlanMedicalInsuranceContentsExample example = new PlanMedicalInsuranceContentsExample();
        example.createCriteria().andProvinceIn(provinces).andCityIn(city);
        List<String> existsKeys = planMedicalInsuranceContentsExtendMapper.selectExistsByExample(example).stream().map(v -> v.getProvince() + "-" + v.getCity()).distinct().collect(Collectors.toList());
        List<PlanGoodsImportDTO> result = new ArrayList<>();
        importDTOS.stream().collect(Collectors.groupingBy(v -> v.getProvince() + "-" + v.getCity())).forEach((k,v) -> {
            errorMsg.addAll(dealByCity(k, v, existsKeys, result));
        });
        return result;
    }

    private List<String> dealByCity(String k, List<PlanGoodsImportDTO> v, List<String> existsKeys, List<PlanGoodsImportDTO> result) {
        List<String> errorMsg = new ArrayList<>();
        if (existsKeys.contains(k)) {
            errorMsg.add(new StringBuilder().append("已导入 ").append(k).append(" 统筹医保目录，不可重复导入。").toString());
        }
        //有错误就返回
        if(CollectionUtils.isNotEmpty(errorMsg)){
            logger.info("重复错误返回");
            return errorMsg;
        }
        Set<String> goodsNoSet = new HashSet<>();
        for(PlanGoodsImportDTO importDTO : v){
            Set<String> citys = Optional.ofNullable(CacheVar.provinceMap.get(importDTO.getProvince())).orElse(new HashSet<>());
            if (citys.size() <= 0) {
                errorMsg.add(new StringBuilder().append("系统不存在此省份名称：").append(importDTO.getProvince()).toString());
                continue;
            }
            if (!citys.contains(importDTO.getCity())) {
                errorMsg.add(new StringBuilder().append("系统不存在此城市名称：").append(importDTO.getCity()).toString());
                continue;
            }
            int perSize = goodsNoSet.size();
            goodsNoSet.add(importDTO.getProvince()+ "-" + importDTO.getCity() + "-" + importDTO.getGoodsNo());
            if (perSize == goodsNoSet.size()) {
                errorMsg.add(new StringBuilder().append(importDTO.getProvince()).append(importDTO.getCity()).append("下商品编码：").append(importDTO.getGoodsNo()).append(" 重复").toString());
            }
        }
        if (CollectionUtils.isEmpty(errorMsg)) {
            result.addAll(v);
        }
        return errorMsg;
    }

    protected void unifyFileCheck(MultipartFile file){
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }

        String originalFilename = file.getOriginalFilename();
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!".xls".equals(fileType) && !".xlsx".equals(fileType)) {
            throw new BusinessErrorException("导入的文件类型有误");
        }
    }

    private PlanMedicalInsuranceContentsExample genSelectExample(PlanContentParam param) {
        PlanMedicalInsuranceContentsExample example = new PlanMedicalInsuranceContentsExample();
        PlanMedicalInsuranceContentsExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(param.getProvince())) {
            criteria.andProvinceEqualTo(param.getProvince());
        }
        if (StringUtils.isNotBlank(param.getCity())) {
            criteria.andCityEqualTo(param.getCity());
        }
        if (StringUtils.isNotBlank(param.getGoodsNos())) {
            criteria.andGoodsNoIn(Arrays.stream(StringUtils.split(param.getGoodsNos().trim(), ",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList()));
        }
        return example;
    }
}
