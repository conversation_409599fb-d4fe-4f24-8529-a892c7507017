package com.cowell.scib.service.impl;

import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.enums.BundlTaskStepEnum;
import com.cowell.scib.enums.ConfigTypeEnum;
import com.cowell.scib.enums.DicApiEnum;
import com.cowell.scib.enums.TaskStatusChangeEnum;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.BundlTaskWriteService;
import com.cowell.scib.service.RuleService;
import com.cowell.scib.service.dto.BundlTaskDetailDTO;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import com.cowell.scib.service.param.BundlTaskAddParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/15 11:10
 */
@Slf4j
@Component
public class BundlTaskThreeStep extends BundlTaskStepHandler<BundlTaskAddParam>{

    @Autowired
    private RuleService ruleService;
    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;
    @Autowired
    private BundlTaskWriteService bundlTaskWriteService;
    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtMapper;

    @Override
    public boolean check(BundlTaskAddParam bundlTaskAddParam) {
        return false;
    }

    @Override
    public void add(BundlTaskAddParam bundlTaskAddParam) {
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(bundlTaskAddParam.getTaskId());
        if(Objects.isNull(bundlingTaskInfo)){
            throw new BusinessErrorException(new StringBuilder().append("无此任务，请检查！").append(bundlTaskAddParam.getTaskId()).toString());
        }
        Map<String, RuleDetailDTO> ruleDetailDTOMap =  ruleService.getConfigRuleList(bundlingTaskInfo.getOrgId(), DicApiEnum.listTaskThreeDict(), ConfigTypeEnum.BB.getType());
        Map<String, BundlTaskDetailDTO> detailDTOMap = new HashMap<>(ruleDetailDTOMap.size());
        ruleDetailDTOMap.forEach((k, dto)->{
            BundlTaskDetailDTO detailDTO = new BundlTaskDetailDTO();
            BeanUtils.copyProperties(dto, detailDTO);
            detailDTOMap.put(k, detailDTO);
        });
        bundlTaskAddParam.setDetailMap(detailDTOMap);
        bundlTaskWriteService.addTaskDetail(bundlTaskAddParam, bundlTaskAddParam.getUserDTO());
        if(Objects.nonNull(bundlTaskAddParam.getSaveAble()) && bundlTaskAddParam.getSaveAble()){
            bundlTaskWriteService.unifyUpdateStasktatus(bundlTaskAddParam.getTaskId(), TaskStatusChangeEnum.SAVE, bundlTaskAddParam.getUserDTO());
        }
    }

    @Override
    public void deleteDetail(BundlTaskAddParam bundlTaskAddParam) {
        if(Objects.isNull(bundlTaskAddParam.getTaskId())){
            log.info("BundlTaskThreeStep|添加不删除");
            return;
        }
        bundlingTaskDetailExtMapper.deleteDetailByStep(bundlTaskAddParam.getTaskId(), bundlTaskAddParam.getTaskStep());
    }

    @Override
    public boolean isHandler(BundlTaskAddParam bundlTaskAddParam) {
        return BundlTaskStepEnum.THREE.getStep().equals(bundlTaskAddParam.getTaskStep());
    }
}
