package com.cowell.scib.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/3/13 22:29
 */
@Slf4j
@Service
public abstract class BundlTaskStepHandler<P> {

    /**
     * 处理方法
     * @param p
     */
    public void handleRequest(P p) {
        check(p);
        deleteDetail(p);
        add(p);
    }

    /**
     * 校验方法
     * @param p
     * @return
     */
    public abstract boolean check(P p);

    /**
     * 添加
     * @param p
     */
    public abstract void add(P p);

    /**
     * 删除明细
     * @param p
     * @return
     */
    public abstract void deleteDetail(P p);

    /**
     * 定位方法
     * @param p
     * @return
     */
    public abstract boolean isHandler(P p);
}
