package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.entityTidb.*;
import com.cowell.scib.enums.NecessaryGoodsLevelEnum;
import com.cowell.scib.entityTidb.TrackRetultAllDetail;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperTidb.*;
import com.cowell.scib.mapperTidb.extend.TaskNecessaryPlatformGoodsExtendMapper;
import com.cowell.scib.mapperTidb.extend.TrackRetultAllDetailExtendMapper;
import com.cowell.scib.service.ForestService;
import com.cowell.scib.service.IscmService;
import com.cowell.scib.service.LevelNecessaryService;
import com.cowell.scib.service.SearchService;
import com.cowell.scib.service.dto.CommonCategoryDTO;
import com.cowell.scib.service.dto.IscmGoodsCategoryDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.TrackRetultDetailParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class LevelNecessaryServiceImpl implements LevelNecessaryService {

    private final Logger logger = LoggerFactory.getLogger(LevelNecessaryServiceImpl.class);

    @Autowired
    private TaskNecessaryPlatformGoodsMapper taskNecessaryPlatformGoodsMapper;

    @Autowired
    private TaskNecessaryCompanyGoodsMapper taskNecessaryCompanyGoodsMapper;

    @Autowired
    private TaskNecessaryStoreTypeGoodsMapper taskNecessaryStoreTypeGoodsMapper;

    @Autowired
    private TaskNecessaryChooseStoreTypeGoodsMapper taskNecessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private TaskNecessarySingleStoreGoodsMapper taskNecessarySingleStoreGoodsMapper;

    @Autowired
    private TrackRetultAllDetailExtendMapper trackRetultAllDetailExtendMapper;

    @Autowired
    private TaskNecessaryPlatformGoodsExtendMapper taskNecessaryPlatformGoodsExtendMapper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private IscmService iscmService;

    @Autowired
    private ForestService forestService;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;


    @Override
    public void insertTaskNecessaryData(List<TrackResultLevelNecessary> trackResultLevelNecessaries) {
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(trackResultLevelNecessaries.get(0).getTaskId());
        logger.info("查询商品信息的goods：{}", JSONObject.toJSONString(trackResultLevelNecessaries.stream().map(TrackResultLevelNecessary::getGoodsId).distinct().collect(Collectors.toList())));
        //防止接口限流
        try {
            Thread.sleep(100L);
        }catch (Exception e){
            logger.error("六级必备目录新增出错");
        }
        Map<String, SpuListVo> spuVOMap = searchService.getSpuVOMap(trackResultLevelNecessaries.stream().map(TrackResultLevelNecessary::getGoodsId).distinct().collect(Collectors.toList()));
        logger.info("查询商品信息：{}",spuVOMap.size());
        List<Long> categoryIds = new ArrayList<>();
        if (MapUtils.isNotEmpty(spuVOMap)) {
            spuVOMap.forEach((k, v) -> {
                categoryIds.add(StringUtils.isNotBlank(v.getCategoryId()) ? Long.valueOf(v.getCategoryId()) : null);
            });
        }
        Map<Long, CommonCategoryDTO> categoryBySubIds = getCategoryBySubIds(categoryIds);
        logger.info("获取子类信息：{}", categoryBySubIds.size());
        try{
            for (TrackResultLevelNecessary trackResultLevelNecessary : trackResultLevelNecessaries) {
                TaskNecessaryPlatformGoods taskNecessaryPlatformGoods = new TaskNecessaryPlatformGoods();
                if (trackResultLevelNecessary.getLevel().equals(Integer.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()))){
                    logger.info("平台必备开始");
                    TaskNecessaryPlatformGoodsExample example = new TaskNecessaryPlatformGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(trackResultLevelNecessary.getTaskId()).andStoreTypeEqualTo(trackResultLevelNecessary.getStoreGroup()).andPlatformOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getPlatOrgid())).andGoodsNoEqualTo(trackResultLevelNecessary.getGoodsId());
                    List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods1 = taskNecessaryPlatformGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessaryPlatformGoods1)){
                        logger.info("查询平台必备数据不是空：{}",JSONObject.toJSONString(taskNecessaryPlatformGoods1));
                        continue;
                    }
                    TrackRetultAllDetail trackRetultAllDetail = getTrackRetultAllDetail(trackResultLevelNecessary);
                    buildTaskNecessaryPlatformDTO(trackResultLevelNecessary, taskNecessaryPlatformGoods, trackRetultAllDetail,spuVOMap,categoryBySubIds);
                    if (Objects.isNull(taskNecessaryPlatformGoods.getGoodsNo())){
                        logger.info("查询平台必备数据没有商品信息：{}",taskNecessaryPlatformGoods.getGoodsNo());
                        continue;
                    }
                    taskNecessaryPlatformGoods.setPlatformName(bundlingTaskInfo.getOrgName());
                    taskNecessaryPlatformGoodsMapper.insertSelective(taskNecessaryPlatformGoods);
                }else if (trackResultLevelNecessary.getLevel().equals(Integer.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()))){
                    logger.info("企业必备开始");
                    if (StringUtils.isBlank(trackResultLevelNecessary.getCompid())){
                        continue;
                    }
                    TaskNecessaryCompanyGoodsExample example = new TaskNecessaryCompanyGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(trackResultLevelNecessary.getTaskId()).andPlatformOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getPlatOrgid())).andCompanyOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getCompid())).andCityEqualTo(trackResultLevelNecessary.getCity()).andGoodsNoEqualTo(trackResultLevelNecessary.getGoodsId());
                    List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods1 = taskNecessaryCompanyGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessaryCompanyGoods1)){
                        logger.info("查询企业必备数据不是空：{}",JSONObject.toJSONString(taskNecessaryCompanyGoods1));
                        continue;
                    }
                    TaskNecessaryCompanyGoods taskNecessaryCompanyGoods = new TaskNecessaryCompanyGoods();
                    TrackRetultAllDetail trackRetultAllDetail = getTrackRetultAllDetail(trackResultLevelNecessary);
                    buildTaskNecessaryPlatformDTO(trackResultLevelNecessary, taskNecessaryPlatformGoods, trackRetultAllDetail,spuVOMap,categoryBySubIds);
                    BeanUtils.copyProperties(taskNecessaryPlatformGoods,taskNecessaryCompanyGoods);
                    if (Objects.isNull(taskNecessaryCompanyGoods.getGoodsNo())){
                        logger.info("查询企业必备没有商品数据：{}",taskNecessaryCompanyGoods.getGoodsNo());
                        continue;
                    }
                    taskNecessaryCompanyGoods.setCompanyOrgId(StringUtils.isNotEmpty(trackResultLevelNecessary.getCompid())?Long.valueOf(trackResultLevelNecessary.getCompid()):0);
                    Optional<OrgInfoBaseCache> storeByOrgId = CacheVar.getBusinessByOrgId(Long.valueOf(trackResultLevelNecessary.getCompid()));

                    if (Objects.nonNull(storeByOrgId) && storeByOrgId.isPresent()) {
                        taskNecessaryCompanyGoods.setBusinessid(storeByOrgId.get().getBusinessId());
                        taskNecessaryCompanyGoods.setCompanyCode(storeByOrgId.get().getBusinessSapCode());
                        taskNecessaryCompanyGoods.setCompanyName(storeByOrgId.get().getName());
                        taskNecessaryCompanyGoods.setStoreFocusLevel(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreConcentration())?new BigDecimal(trackResultLevelNecessary.getStoreConcentration()):new BigDecimal(0));
                        taskNecessaryCompanyGoods.setStoreSalesRate(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreSaleRate())?new BigDecimal(trackResultLevelNecessary.getStoreSaleRate()):new BigDecimal(0));
                        taskNecessaryCompanyGoods.setCity(trackResultLevelNecessary.getCity());
                        taskNecessaryCompanyGoods.setPlatformName(bundlingTaskInfo.getOrgName());
                        taskNecessaryCompanyGoodsMapper.insertSelective(taskNecessaryCompanyGoods);
                    }
                }else if (trackResultLevelNecessary.getLevel().equals(Integer.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()))){
                    logger.info("店型必备开始");
                    TaskNecessaryStoreTypeGoodsExample example = new TaskNecessaryStoreTypeGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(trackResultLevelNecessary.getTaskId()).andPlatformOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getPlatOrgid())).andCompanyOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getCompid())).andCityEqualTo(trackResultLevelNecessary.getCity()).andStoreTypeEqualTo(trackResultLevelNecessary.getStoreGroup()).andGoodsNoEqualTo(trackResultLevelNecessary.getGoodsId());
                    List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods1 = taskNecessaryStoreTypeGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessaryStoreTypeGoods1)){
                        logger.info("查询店型必备数据不是空：{}",JSONObject.toJSONString(taskNecessaryStoreTypeGoods1));
                        continue;
                    }
                    TaskNecessaryStoreTypeGoods taskNecessaryStoreTypeGoods = new TaskNecessaryStoreTypeGoods();
                    TrackRetultAllDetail trackRetultAllDetail = getTrackRetultAllDetail(trackResultLevelNecessary);
                    buildTaskNecessaryPlatformDTO(trackResultLevelNecessary, taskNecessaryPlatformGoods, trackRetultAllDetail,spuVOMap,categoryBySubIds);
                    BeanUtils.copyProperties(taskNecessaryPlatformGoods,taskNecessaryStoreTypeGoods);
                    if (Objects.isNull(taskNecessaryStoreTypeGoods.getGoodsNo())){
                        logger.info("查询店型必备没有商品数据：{}",taskNecessaryStoreTypeGoods.getGoodsNo());
                        continue;
                    }
                    taskNecessaryStoreTypeGoods.setCompanyOrgId(StringUtils.isNotEmpty(trackResultLevelNecessary.getCompid())?Long.valueOf(trackResultLevelNecessary.getCompid()):0);
                    Optional<OrgInfoBaseCache> storeByOrgId = CacheVar.getBusinessByOrgId(Long.valueOf(trackResultLevelNecessary.getCompid()));
                    if (Objects.nonNull(storeByOrgId) && storeByOrgId.isPresent()) {
                        taskNecessaryStoreTypeGoods.setBusinessid(storeByOrgId.get().getBusinessId());
                        taskNecessaryStoreTypeGoods.setCompanyCode(storeByOrgId.get().getSapCode());
                        taskNecessaryStoreTypeGoods.setCity(trackResultLevelNecessary.getCity());
                        taskNecessaryStoreTypeGoods.setCompanyName(storeByOrgId.get().getName());
                        taskNecessaryStoreTypeGoods.setStoreFocusLevel(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreConcentration())?new BigDecimal(trackResultLevelNecessary.getStoreConcentration()):new BigDecimal(0));
                        taskNecessaryStoreTypeGoods.setStoreSalesRate(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreSaleRate())?new BigDecimal(trackResultLevelNecessary.getStoreSaleRate()):new BigDecimal(0));
                        taskNecessaryStoreTypeGoods.setPlatformName(bundlingTaskInfo.getOrgName());
                        taskNecessaryStoreTypeGoodsMapper.insertSelective(taskNecessaryStoreTypeGoods);
                    }
                }else if (trackResultLevelNecessary.getLevel().equals(Integer.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()))){
                    logger.info("店型选配开始");
                    TaskNecessaryChooseStoreTypeGoodsExample example = new TaskNecessaryChooseStoreTypeGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(trackResultLevelNecessary.getTaskId()).andPlatformOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getPlatOrgid())).andCompanyOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getCompid())).andCityEqualTo(trackResultLevelNecessary.getCity()).andStoreTypeEqualTo(trackResultLevelNecessary.getStoreGroup()).andGoodsNoEqualTo(trackResultLevelNecessary.getGoodsId());
                    List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods1 = taskNecessaryChooseStoreTypeGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessaryChooseStoreTypeGoods1)){
                        logger.info("查询店型选配数据不是空：{}",JSONObject.toJSONString(taskNecessaryChooseStoreTypeGoods1));
                        continue;
                    }
                    TaskNecessaryChooseStoreTypeGoods taskNecessaryChooseStoreTypeGoods = new TaskNecessaryChooseStoreTypeGoods();
                    TrackRetultAllDetail trackRetultAllDetail = getTrackRetultAllDetail(trackResultLevelNecessary);
                    buildTaskNecessaryPlatformDTO(trackResultLevelNecessary, taskNecessaryPlatformGoods, trackRetultAllDetail,spuVOMap,categoryBySubIds);
                    BeanUtils.copyProperties(taskNecessaryPlatformGoods,taskNecessaryChooseStoreTypeGoods);
                    if (Objects.isNull(taskNecessaryChooseStoreTypeGoods.getGoodsNo())){
                        logger.info("查询店型选配没有商品数据：{}",taskNecessaryChooseStoreTypeGoods.getGoodsNo());
                        continue;
                    }
                    taskNecessaryChooseStoreTypeGoods.setCompanyOrgId(StringUtils.isNotEmpty(trackResultLevelNecessary.getCompid())?Long.valueOf(trackResultLevelNecessary.getCompid()):0);
                    Optional<OrgInfoBaseCache> storeByOrgId = CacheVar.getBusinessByOrgId(Long.valueOf(trackResultLevelNecessary.getCompid()));
                    if (storeByOrgId.isPresent()) {
                        taskNecessaryChooseStoreTypeGoods.setCity(trackResultLevelNecessary.getCity());
                        taskNecessaryChooseStoreTypeGoods.setBusinessid(storeByOrgId.get().getBusinessId());
                        taskNecessaryChooseStoreTypeGoods.setCompanyCode(trackResultLevelNecessary.getCompid());
                        taskNecessaryChooseStoreTypeGoods.setStoreFocusLevel(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreConcentration())?new BigDecimal(trackResultLevelNecessary.getStoreConcentration()):new BigDecimal(0));
                        taskNecessaryChooseStoreTypeGoods.setStoreSalesRate(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreSaleRate())?new BigDecimal(trackResultLevelNecessary.getStoreSaleRate()):new BigDecimal(0));

                        taskNecessaryChooseStoreTypeGoods.setCompanyName(storeByOrgId.get().getName());
                        taskNecessaryChooseStoreTypeGoods.setPlatformName(bundlingTaskInfo.getOrgName());
                        taskNecessaryChooseStoreTypeGoodsMapper.insertSelective(taskNecessaryChooseStoreTypeGoods);
                    }

                }else if (trackResultLevelNecessary.getLevel().equals(Integer.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode()))){
                    logger.info("单店必备开始");
                    Optional<OrgInfoBaseCache> storeOrgIds = CacheVar.getStoreBySapCode(trackResultLevelNecessary.getStoreCode());
                    Long storeOrgId=null;
                    if (Objects.nonNull(storeOrgIds) && storeOrgIds.isPresent()) {
                        storeOrgId=storeOrgIds.get().getId();
                    }
                    TaskNecessarySingleStoreGoodsExample example = new TaskNecessarySingleStoreGoodsExample();
                    example.createCriteria().andTaskIdEqualTo(trackResultLevelNecessary.getTaskId()).andPlatformOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getPlatOrgid())).andCompanyOrgIdEqualTo(Long.valueOf(trackResultLevelNecessary.getCompid())).andCityEqualTo(trackResultLevelNecessary.getCity()).andStoreOrgIdEqualTo(storeOrgId).andGoodsNoEqualTo(trackResultLevelNecessary.getGoodsId());
                    List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods1 = taskNecessarySingleStoreGoodsMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskNecessarySingleStoreGoods1)){
                        logger.info("查询单店必备数据不是空：{}",JSONObject.toJSONString(taskNecessarySingleStoreGoods1));
                        continue;
                    }
                    TaskNecessarySingleStoreGoods taskNecessarySingleStoreGoods = new TaskNecessarySingleStoreGoods();
                    TrackRetultAllDetail trackRetultAllDetail = getTrackRetultAllDetail(trackResultLevelNecessary);
                    buildTaskNecessaryPlatformDTO(trackResultLevelNecessary, taskNecessaryPlatformGoods, trackRetultAllDetail,spuVOMap,categoryBySubIds);
                    BeanUtils.copyProperties(taskNecessaryPlatformGoods,taskNecessarySingleStoreGoods);
                    if (Objects.isNull(taskNecessarySingleStoreGoods.getGoodsNo())){
                        logger.info("查询单店没有商品数据：{}",taskNecessarySingleStoreGoods.getGoodsNo());
                        continue;
                    }
                    taskNecessarySingleStoreGoods.setCompanyOrgId(StringUtils.isNotEmpty(trackResultLevelNecessary.getCompid())?Long.valueOf(trackResultLevelNecessary.getCompid()):0);
                    Optional<OrgInfoBaseCache> storeByOrgId = CacheVar.getBusinessByOrgId(Long.valueOf(trackResultLevelNecessary.getCompid()));
                    if (Objects.nonNull(storeByOrgId) && storeByOrgId.isPresent()) {
                        taskNecessarySingleStoreGoods.setBusinessid(storeByOrgId.get().getBusinessId());
                        taskNecessarySingleStoreGoods.setCompanyCode(trackResultLevelNecessary.getCompid());
                        taskNecessarySingleStoreGoods.setCompanyName(storeByOrgId.get().getName());
                        taskNecessarySingleStoreGoods.setCity(trackResultLevelNecessary.getCity());
                        taskNecessarySingleStoreGoods.setStoreOrgId(storeOrgId);
                        taskNecessarySingleStoreGoods.setStoreId(storeOrgIds.get().getOutId());
                        taskNecessarySingleStoreGoods.setStoreName(storeOrgIds.get().getName());
                        taskNecessarySingleStoreGoods.setStoreCode(storeOrgIds.get().getSapCode());
                        taskNecessarySingleStoreGoods.setPlatformName(bundlingTaskInfo.getOrgName());
                        taskNecessarySingleStoreGoodsMapper.insertSelective(taskNecessarySingleStoreGoods);
                    }
                }
            }
        }catch (Exception e){
            logger.error("拆分表数据入库失败",e);
        }
    }

    private TrackRetultAllDetail getTrackRetultAllDetail(TrackResultLevelNecessary trackResultLevelNecessary) {
        TrackRetultDetailParam param = new TrackRetultDetailParam();
        param.setTaskId(trackResultLevelNecessary.getTaskId());
        param.setPlatOrgid(trackResultLevelNecessary.getPlatOrgid());
        param.setCompid(trackResultLevelNecessary.getCompid());
        param.setCity(trackResultLevelNecessary.getCity());
        param.setOrgNoList(Arrays.asList(trackResultLevelNecessary.getStoreCode()));
        param.setReviseStoreGroup(trackResultLevelNecessary.getStoreSaleRate());
        param.setGoodsId(trackResultLevelNecessary.getGoodsId());
        param.setLevelList(Arrays.asList(trackResultLevelNecessary.getLevel().toString()));
        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(param);
        if (CollectionUtils.isNotEmpty(trackRetultAllDetails)){
            return trackRetultAllDetails.get(0);
        }
        return new TrackRetultAllDetail();
    }

    private void buildTaskNecessaryPlatformDTO(TrackResultLevelNecessary trackResultLevelNecessary, TaskNecessaryPlatformGoods taskNecessaryPlatformGoods, TrackRetultAllDetail trackRetultAllDetail, Map<String, SpuListVo> spuVOMap,Map<Long, CommonCategoryDTO> categoryBySubIds) {
        taskNecessaryPlatformGoods.setTaskId(trackResultLevelNecessary.getTaskId());
        taskNecessaryPlatformGoods.setPlatformOrgId(StringUtils.isNotEmpty(trackResultLevelNecessary.getPlatOrgid())?Long.valueOf(trackResultLevelNecessary.getPlatOrgid()):0);
        Optional<OrgInfoBaseCache> platform = CacheVar.getPlatformByOrgId(Long.valueOf(trackResultLevelNecessary.getPlatOrgid()));
        logger.info("根据平台Id获取平台信息：{}",JSONObject.toJSONString(platform));
        if (platform.isPresent()){
            taskNecessaryPlatformGoods.setPlatformName(platform.get().getName());
        }
        if (Objects.nonNull(trackRetultAllDetail)){
            taskNecessaryPlatformGoods.setPlatformName(trackRetultAllDetail.getZoneNew());
            taskNecessaryPlatformGoods.setCategoryName(trackRetultAllDetail.getClassoneName());
            taskNecessaryPlatformGoods.setMiddleCategoryName(trackRetultAllDetail.getClasstwoName());
            taskNecessaryPlatformGoods.setSmallCategoryName(trackRetultAllDetail.getClassthreeName());
            taskNecessaryPlatformGoods.setSubCategoryName(trackRetultAllDetail.getClassfourName());
            taskNecessaryPlatformGoods.setComposition(trackRetultAllDetail.getComponent());
            taskNecessaryPlatformGoods.setManufacturer(trackRetultAllDetail.getManufacturer());
            taskNecessaryPlatformGoods.setPurchaseAttr(trackRetultAllDetail.getGrossprofit());
            taskNecessaryPlatformGoods.setChooseReason(trackRetultAllDetail.getBak());
        }
        taskNecessaryPlatformGoods.setStoreType(trackResultLevelNecessary.getStoreGroup());
        if (MapUtils.isNotEmpty(spuVOMap)){
            SpuListVo spuListVo = spuVOMap.get(trackResultLevelNecessary.getGoodsId());
            if (Objects.nonNull(spuListVo)){
                String categoryId = spuListVo.getCategoryId();
                taskNecessaryPlatformGoods.setCategoryId(StringUtils.isNotBlank(categoryId)?Long.valueOf(categoryId.substring(0,2)):null);
                taskNecessaryPlatformGoods.setMiddleCategoryId(StringUtils.isNotBlank(categoryId)?Long.valueOf(categoryId.substring(0,4)):null);
                taskNecessaryPlatformGoods.setSmallCategoryId(StringUtils.isNotBlank(categoryId)?Long.valueOf(categoryId.substring(0,6)):null);
                taskNecessaryPlatformGoods.setSubCategoryId(StringUtils.isNotBlank(categoryId)?Long.valueOf(categoryId):null);
                taskNecessaryPlatformGoods.setBarCode(spuListVo.getBarCode());
                taskNecessaryPlatformGoods.setGoodsNo(spuListVo.getGoodsNo());
                taskNecessaryPlatformGoods.setGoodsName(spuListVo.getName());
                taskNecessaryPlatformGoods.setGoodsUnit(spuListVo.getGoodsunit());
                taskNecessaryPlatformGoods.setSpecifications(spuListVo.getJhiSpecification());
                taskNecessaryPlatformGoods.setDosageForm(spuListVo.getDosageformsid());
                taskNecessaryPlatformGoods.setDescription(spuListVo.getDescription());
                taskNecessaryPlatformGoods.setGoodsCommonName(spuListVo.getCurName());
                taskNecessaryPlatformGoods.setManufacturer(spuListVo.getFactoryid());
                taskNecessaryPlatformGoods.setComposition(spuListVo.getComponent());
                taskNecessaryPlatformGoods.setApprovalNumber(spuListVo.getApprdocno());
                logger.info("categoryId信息：{}",JSONObject.toJSONString(categoryId));
                if (MapUtils.isNotEmpty(categoryBySubIds) && Objects.nonNull(categoryBySubIds.get(Long.valueOf(categoryId)))) {
                    CommonCategoryDTO commonCategoryDTO = categoryBySubIds.get(Long.valueOf(categoryId));
                    logger.info("大类中类信息：{}",JSONObject.toJSONString(commonCategoryDTO));
                    taskNecessaryPlatformGoods.setCategoryName(commonCategoryDTO.getCategoryName());
                    taskNecessaryPlatformGoods.setMiddleCategoryName(commonCategoryDTO.getMiddleCategoryName());
                    taskNecessaryPlatformGoods.setSmallCategoryName(commonCategoryDTO.getSmallCategoryName());
                    taskNecessaryPlatformGoods.setSubCategoryName(commonCategoryDTO.getSubCategoryName());
                }
            }else {
                logger.warn("查询不到商品信息：{}",trackResultLevelNecessary.getGoodsId());
                return;
            }
        }else {
            logger.error("查询商品信息为空,商品为：{}",trackResultLevelNecessary.getGoodsId());
            return;
        }
        taskNecessaryPlatformGoods.setChooseReason(trackResultLevelNecessary.getBak());
        taskNecessaryPlatformGoods.setStoreSalesRate(StringUtils.isNotEmpty(trackResultLevelNecessary.getStoreSaleRate())?new BigDecimal(trackResultLevelNecessary.getStoreSaleRate()):new BigDecimal(0));
        taskNecessaryPlatformGoods.setStatus(Constants.NORMAL_STATUS);
        taskNecessaryPlatformGoods.setExtend("");
        taskNecessaryPlatformGoods.setVersion(Constants.DEF_VERSION);
        taskNecessaryPlatformGoods.setCreatedBy(null);
        taskNecessaryPlatformGoods.setCreatedName("");
        taskNecessaryPlatformGoods.setUpdatedBy(null);
        taskNecessaryPlatformGoods.setUpdatedName("");
    }

    /**
     * 根据子类id获取四级类目信息
     * @param subCategoryIds
     * @return
     */
    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        logger.info("subCategoryIds:{}", JSON.toJSONString(subCategoryIds));
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1,k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        logger.info("resultMap:{}", JSON.toJSONString(resultMap));
        return resultMap;
    }
}
