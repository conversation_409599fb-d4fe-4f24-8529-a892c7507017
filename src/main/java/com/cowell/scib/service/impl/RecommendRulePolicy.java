package com.cowell.scib.service.impl;

import com.cowell.scib.enums.DicApiEnum;
import com.cowell.scib.service.IRuleAddService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.rule.RuleAddParam;
import org.springframework.stereotype.Component;

@Component
public class RecommendRulePolicy implements IRuleAddService{


    @Override
    public void check(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
    }

    @Override
    public String getCode() {
        return DicApiEnum.RECOMMEND.getCode();
    }
}
