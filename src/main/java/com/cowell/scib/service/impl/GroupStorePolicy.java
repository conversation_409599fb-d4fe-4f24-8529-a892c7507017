package com.cowell.scib.service.impl;

import com.cowell.permission.dto.CrmStoreDTO;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.DicApiEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.CheckService;
import com.cowell.scib.service.IRuleAddService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import com.cowell.scib.service.feign.PermissionFeignClient;
import com.cowell.scib.service.param.rule.RuleAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:52
 */
@Slf4j
@Component
public class GroupStorePolicy implements IRuleAddService {

    @Autowired
    private CheckService checkService;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Override
    public void check(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        RuleDetailDTO storeWhiteListDTO = ruleAddParam.getDetailMap().get(Constants.STORE_WHITE_LIST);
        RuleDetailDTO storeBlackListDTO = ruleAddParam.getDetailMap().get(Constants.STORE_BLACK_LIST);
        if (Objects.isNull(storeWhiteListDTO) || Objects.isNull(storeBlackListDTO)) {
            return;
        }
        List<Long> whiteOrgIdList = storeWhiteListDTO.getOrgIdList();
        List<Long> blackorgIdList = storeBlackListDTO.getOrgIdList();
        if (CollectionUtils.isEmpty(whiteOrgIdList) || CollectionUtils.isEmpty(blackorgIdList)) {
            return;
        }
        List<Long> commonStore = whiteOrgIdList.stream().filter(blackorgIdList::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(commonStore)){
            return;
        }
        List<CrmStoreDTO> crmStoreDTOS = permissionFeignClient.listStoreByOrgIds(commonStore);
        if (CollectionUtils.isNotEmpty(crmStoreDTOS)){
            List<String> storeSapCodeList = crmStoreDTOS.stream().map(CrmStoreDTO::getSapcode).collect(Collectors.toList());
            String join = String.join("、",storeSapCodeList);
            StringBuilder sb = new StringBuilder();
            sb.append("【");
            sb.append(join);
            sb.append("】同时存在黑/白名单中，请选择在黑名单或者白名单中删除这些门店，再保存。");
            throw new BusinessErrorException(sb.toString());
        }
    }

    @Override
    public String getCode() {
        return DicApiEnum.ZH_STORE.getCode();
    }

}
