package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.AsyncExportActionEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.HandlerDataExportService;
import com.cowell.scib.service.dto.ExcelMultiSheetHeaderDTO;
import com.cowell.scib.service.dto.FileDownloadTaskDTO;
import com.cowell.scib.service.dto.ListToExcelMultiSheetDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.feign.ScrmFeignClient;
import com.cowell.scib.service.feign.StoreFeignClient;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.HutoolUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

public class AsyncExportFileCallable implements Callable {
    private static final Logger logger = LoggerFactory.getLogger("AsyncExportFileCallable");


    private String fileName;
    private TokenUserDTO userDTO;
    private ScrmFeignClient scrmFeignClient;
    private List<ListToExcelMultiSheetDTO> dataToExcelList;
    private HandlerDataExportService handlerDataExportService;
    private AsyncExportActionEnum action;
    private StoreFeignClient storeFeignClient;

    public AsyncExportFileCallable(String fileName, AsyncExportActionEnum action, TokenUserDTO userDTO, ScrmFeignClient scrmFeignClient, StoreFeignClient storeFeignClient, List<ListToExcelMultiSheetDTO> dataToExcelList) {
        this.fileName = fileName;
        this.userDTO = userDTO;
        this.scrmFeignClient = scrmFeignClient;
        this.storeFeignClient = storeFeignClient;
        this.action = action;
        this.dataToExcelList = dataToExcelList;
    }

    public AsyncExportFileCallable(String fileName, AsyncExportActionEnum action, TokenUserDTO userDTO, ScrmFeignClient scrmFeignClient, StoreFeignClient storeFeignClient, HandlerDataExportService handlerDataExportService) {
        this.fileName = fileName;
        this.userDTO = userDTO;
        this.scrmFeignClient = scrmFeignClient;
        this.storeFeignClient = storeFeignClient;
        this.action = action;
        this.handlerDataExportService = handlerDataExportService;
    }

    @Override
    public Object call() throws Exception {
        logger.debug("{}文件异步任务开始", fileName);
        Long taskId = null;
        String name = userDTO.getName() + "-" + action.getName() + fileName;
//        name = URLEncoder.encode(name, "UTF-8");
//        fileName = URLEncoder.encode(fileName, "UTF-8");
        FileDownloadTaskDTO param = new FileDownloadTaskDTO();
        param.setFileName(fileName);
        param.setName(name);
        param.setCreatedBy(userDTO.getUserId());
        param.setPlatformType(Constants.PLATFORM_TYPE);
        param.setFilePlace(1);

        try {
            // 2 新建下载任务
            FileDownloadTaskDTO createResponse = scrmFeignClient.createFileDownloadTask(param);

            taskId = createResponse.getId();

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            Date startDate = new Date();
            // 查询数据
            if (CollectionUtils.isEmpty(dataToExcelList)) {
                if(handlerDataExportService.isPageable()){
                    ExcelMultiSheetHeaderDTO headerDTO = new ExcelMultiSheetHeaderDTO();
                    if(MapUtils.isEmpty(handlerDataExportService.getFieldMap())){
                        logger.error("字段映射关系为空");
                        return null;
                    }
                    headerDTO.setPageSize(10000);
                    headerDTO.setFieldMap(handlerDataExportService.getFieldMap());
                    logger.debug("使用分页查询写入数据-开始");
                    HutoolUtil.listToExcleBigData(handlerDataExportService,headerDTO,outputStream);
                    logger.debug("使用分页查询写入数据-结束");
                }else {
                    dataToExcelList = handlerDataExportService.getDataToExport();
                    logger.debug("使用接口数据");
                    logger.debug("写入文件开始");
                    HutoolUtil.listToExcle(dataToExcelList, outputStream);
                    logger.debug("写入文件结束");
                }

            } else {
                logger.debug("使用接口数据");
                logger.debug("写入文件开始");
                HutoolUtil.listToExcle(dataToExcelList, outputStream);
                logger.debug("写入文件结束");
            }
            Map<String, Object> map = storeFeignClient.uploadFileByByte(name, outputStream.toByteArray());

            logger.info("上传文件结果 -> {}", JSON.toJSONString(map));

            // 2 更新任务
            FileDownloadTaskDTO updateParam = new FileDownloadTaskDTO();
            updateParam.setPlatformType(Constants.PLATFORM_TYPE);
            updateParam.setId(taskId);
            updateParam.setFileUrl(String.valueOf(map.get("fileUrl")));
            updateParam.setStatus(2);
            updateParam.setName(name);
            updateParam.setFileName(fileName);
            updateParam.setStartTime(DateUtils.conventDateStrByPattern(startDate, "yyyy-MM-dd HH:mm:ss"));
            updateParam.setEndTime(DateUtils.conventDateStrByPattern(new Date(), "yyyy-MM-dd HH:mm:ss"));
            updateParam.setFilePlace(1);
            if (map.get("success").equals(true)) {
                updateParam.setStatus(Constants.FILE_DOWN_STATUS_SUCCESS);
                updateParam.setReason("成功");
                updateParam.setFileUrl(String.valueOf(map.get("fileUrl")));
            } else {
                updateParam.setStatus(Constants.FILE_DOWN_STATUS_FAIL);
                updateParam.setReason("上传文件失败");
            }
            scrmFeignClient.updateFileDownloadTask(updateParam);

        } catch (Throwable e) {
            // 删除任务
            param.setStatus(Constants.FILE_DOWN_STATUS_FAIL);
            param.setReason(e.getMessage());
            scrmFeignClient.updateFileDownloadTask(param);
            logger.info("异步上传文件到cos失败", e);
            throw new BusinessErrorException("导出失败");
        }finally {
            logger.debug("{}上传结束",fileName);
        }
        return null;
    }
}
