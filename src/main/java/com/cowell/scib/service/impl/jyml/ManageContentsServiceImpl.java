package com.cowell.scib.service.impl.jyml;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.nyuwa.cos.util.CosService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.ConfigOrgDetailExMapper;
import com.cowell.scib.mapperDgms.extend.ConfigOrgExtendMapper;
import com.cowell.scib.mapperTidb.*;
import com.cowell.scib.mapperTidb.extend.JymlSkuMaxLimitConfigureExtendMapper;
import com.cowell.scib.mapperTidb.extend.JymlStoreSkuConfirmDetailExtendMapper;
import com.cowell.scib.mapperTidb.extend.JymlStoreSkuExemptionRecordExtendMapper;
import com.cowell.scib.mq.producer.ManageSuggestProducer;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.customize.*;
import com.cowell.scib.service.dto.iscm.DailySalesParam;
import com.cowell.scib.service.dto.iscm.DailySalesResponse;
import com.cowell.scib.service.dto.manageContents.*;
import com.cowell.scib.service.dto.necessaryComtentsV2.*;
import com.cowell.scib.service.dto.permssion.EmployeeDetailWithWxDTO;
import com.cowell.scib.service.listener.LimitExcelReadListener;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.ExcelCheck;
import com.cowell.scib.utils.ExtendUtil;
import com.cowell.scib.utils.TracerBean;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cowell.scib.constant.Constants.*;
import static com.cowell.scib.enums.ScibCommonEnums.ManageDropTypeEnum;

/**
 * <AUTHOR>
 */
@Service
public class ManageContentsServiceImpl implements ManageContentsService {

    private final Logger logger = LoggerFactory.getLogger(ManageContentsServiceImpl.class);

    @Autowired
    private JymlStoreSkuSuggestMapper jymlStoreSkuSuggestMapper;

    @Autowired
    private JymlStoreSkuSuggestProcessMapper jymlStoreSkuSuggestProcessMapper;

    @Autowired
    private JymlStoreSkuSuggestProcessDetailMapper jymlStoreSkuSuggestProcessDetailMapper;

    @Autowired
    private JymlSkuMaxLimitConfigureMapper jymlSkuMaxLimitConfigureMapper;
    @Autowired
    private JymlSkuMaxLimitConfigureExtendMapper jymlSkuMaxLimitConfigureExtendMapper;
    @Autowired
    private JymlAdjustmentCycleMapper jymlAdjustmentCycleMapper;

    @Autowired
    private JymlSkuLimitMapper jymlSkuLimitMapper;

    @Autowired
    private StoreGoodsContentsMapper storeGoodsContentsMapper;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;
    @Autowired
    private ConfigOrgDetailExMapper configOrgDetailExMapper;

    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;

    @Autowired
    private JymlStoreIncreaseLimitConfigureMapper jymlStoreIncreaseLimitConfigureMapper;
    @Autowired
    private JymlStoreSkuExemptionRecordMapper jymlStoreSkuExemptionRecordMapper;
    @Autowired
    private JymlStoreSkuExemptionRecordExtendMapper jymlStoreSkuExemptionRecordExtendMapper;
    @Autowired
    private  JymlStoreSkuConfirmDetailMapper jymlStoreSkuConfirmDetailMapper;
    @Autowired
    private JymlStoreSkuConfirmDetailExtendMapper jymlStoreSkuConfirmDetailExtendMapper;
    @Autowired
    private JymlStoreSkuLimitAdjustEffectMapper jymlStoreSkuLimitAdjustEffectMapper;

    @Autowired
    private TagService tagService;
    @Autowired
    private BundlTaskService bundlTaskService;

    @Autowired
    private ManageSuggestProducer manageSuggestProducer;

    @Autowired
    private SearchService searchService;

    @Resource
    private StoreContentsFactory factory;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private TracerBean tracerBean;
    @Resource
    @Qualifier("trackResultTaskExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Value("${scib.easyexcel.necessary.import.num:20000}")
    private Integer importNum;

    @Value("${scib.jyml.canConfirm.role:SZHSPJYMLSH}")
    private String canConfirmRole;
    @Resource
    private AsyncExportFileService asyncExportFileService;

    @Autowired
    private IscmService iscmService;

    @Autowired
    @Qualifier("manageContentsConfirmExecutor")
    private AsyncTaskExecutor manageContentsConfirmExecutor;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${scib.easyexcel.nonjy.import.num:50000}")
    private Integer nonjyImportNum;
    @Autowired
    private CosService cosService;

    private static final String IMPORT_NONJY_GOODS = "DGMS-IMPORT-NONJY-GOODS-";


    public static List<String> CollNameList=Lists.newArrayList("错误原因");

    @Override
    public CommonResponse<OptionDto> manageContentsDropBox(TokenUserDTO userDTO, ManageQueryParam param) {
        JymlSkuMaxLimitConfigureExample example=new JymlSkuMaxLimitConfigureExample();
        JymlSkuMaxLimitConfigureExample.Criteria criteria = example.createCriteria();
        if (ManageDropTypeEnum.MANAGE_DROP_TYPE_VERSION.getCode().equals(param.getQueryType())){
            String minVersion = DateUtils.subDayAndConventDateByPatten(new Date(), -180, DateUtils.DATE_AZN_PATTERN);
            criteria.andVersionGreaterThan(Long.parseLong(minVersion));
            example.setOrderByClause("id desc");
            example.setLimit(3);
            List<ManageDropOptionDTO> options = jymlSkuMaxLimitConfigureMapper.selectOptionsByExample("version", example);
            List<OptionDto> optionDtoList= Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(options)){
                optionDtoList = options.stream().map(v->{
                    return new OptionDto(String.valueOf(v.getVersion()), String.valueOf(v.getVersion()));
                }).collect(Collectors.toList());
            }
            logger.info("optionDtoList ={}", JSONObject.toJSONString(optionDtoList));
            return CommonResponse.ok(optionDtoList);

        }
        if (ManageDropTypeEnum.MANAGE_DROP_TYPE_CITY.getCode().equals(param.getQueryType())){
            if (Objects.nonNull(param.getVersion())){
                criteria.andVersionEqualTo(param.getVersion());
            }else {
                return CommonResponse.error(ErrorCodeEnum.MANAGE_VERSION_ERROR);
            }
            if (StringUtils.isBlank(param.getPlatformOrgIds()) || StringUtils.isBlank(param.getBusinessOrgIds())){
                return CommonResponse.error(ErrorCodeEnum.MANAGE_ORG_ERROR);
            }
            List<Long> businessOrgIdList = Arrays.stream(param.getBusinessOrgIds().split(","))
                    .map(String::trim) // 去除可能存在的空格
                    .filter(s -> !s.isEmpty()) // 过滤掉空字符串
                    .map(Long::parseLong) // 将字符串转换为 Long 类型
                    .collect(Collectors.toList());
            criteria.andBusinessOrgIdIn(businessOrgIdList);
            List<ManageDropOptionDTO> options = jymlSkuMaxLimitConfigureMapper.selectOptionsByExample("city", example);
            List<OptionDto> optionDtoList= Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(options)){
                optionDtoList = options.stream().map(v->{
                    return new OptionDto(String.valueOf(v.getCity()), String.valueOf(v.getCity()));
                }).collect(Collectors.toList());
            }
            logger.info("optionDtoList ={}", JSONObject.toJSONString(optionDtoList));
            return CommonResponse.ok(optionDtoList);
        }
        if (ManageDropTypeEnum.MANAGE_DROP_TYPE_CATEGORY.getCode().equals(param.getQueryType())){
            if (Objects.nonNull(param.getVersion())){
                criteria.andVersionEqualTo(param.getVersion());
            }else {
                return CommonResponse.error(ErrorCodeEnum.MANAGE_VERSION_ERROR);
            }
            String filed="category";
            if (Objects.nonNull(param.getCategory())){
                criteria.andCategoryEqualTo(param.getCategory());
                filed="middle_category";
                if (Objects.nonNull(param.getMiddleCategory())){
                    criteria.andMiddleCategoryEqualTo(param.getMiddleCategory());
                    filed="small_category";
                    if (Objects.nonNull(param.getSmallCategory())){
                        criteria.andSmallCategoryEqualTo(param.getSmallCategory());
                        filed="sub_category";
                    }
                }
            }
            List<ManageDropOptionDTO> options = jymlSkuMaxLimitConfigureMapper.selectOptionsByExample(filed, example);
            List<OptionDto> optionDtoList= Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(options)){
                String finalFiled = filed;
                optionDtoList = options.stream().map(v->{
                    switch (finalFiled) {
                        case "middle_category":
                            return new OptionDto(v.getMiddleCategoryName(),v.getMiddleCategory());
                        case "small_category":
                            return new OptionDto(v.getSmallCategoryName(),v.getSmallCategory());
                        case "sub_category":
                            return new OptionDto(v.getSubCategoryName(),v.getSubCategory());
                        default:
                            return new OptionDto(v.getCategoryName(), v.getCategory());
                    }
                }).collect(Collectors.toList());
            }
            logger.info("optionDtoList ={}", JSONObject.toJSONString(optionDtoList));
            return CommonResponse.ok(optionDtoList);
        }
        return CommonResponse.ok();
    }
    @Override
    public CommonResult bdpManageSuggestResultNotify(Long version,String businessOrgIds) {
        logger.info("bdpManageSuggestResultNotify version={},businessOrgIds={}", version, businessOrgIds);
        List<Long> businessIdList = Arrays.stream(businessOrgIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessIdList)){
            logger.error("bdpManageSuggestResultNotify businessOrgIds is empty");
           return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        try {
            asyncTaskExecutor.execute(() -> {
                Span span = tracerBean.startSpan();
                logger.info("bdpManageSuggestResultNotify 回调接口异步处理 开始 ");
                for (Long businessOrgId : businessIdList) {
                    JymlStoreSkuSuggestExample example = new JymlStoreSkuSuggestExample();
                    example.createCriteria().andVersionEqualTo(version).andBusinessOrgIdEqualTo(businessOrgId);
                    List<String> storeNoList = jymlStoreSkuSuggestMapper.selectSapCodeByExample(example);
                    if (CollectionUtils.isNotEmpty(storeNoList)) {
                        //进度再后面处理 所以删除时
                        while (true) {
                            JymlStoreSkuSuggestProcessExample processExample = new JymlStoreSkuSuggestProcessExample();
                            processExample.createCriteria().andVersionLessThan(version).andBusinessOrgIdEqualTo(businessOrgId);
                            // 设置分页，每次取 100 条记录，这里使用的是 limit 语法的逻辑模拟(重新生成时注意,limit 需要重新加上)
                            processExample.setLimit(200);
                            int deletedCount = jymlStoreSkuSuggestProcessMapper.deleteByExample(processExample);
                            // 如果本次删除的记录数为 0，说明已经没有数据可删，退出循环
                            if (deletedCount == 0) {
                                break;
                            }
                        }
                        while (true) {
                            JymlStoreSkuSuggestProcessDetailExample processDetailExample = new JymlStoreSkuSuggestProcessDetailExample();
                            processDetailExample.createCriteria().andVersionLessThan(version).andBusinessOrgIdEqualTo(businessOrgId);
                            // 设置分页，每次取 100 条记录，这里使用的是 limit 语法的逻辑模拟(重新生成时注意,limit 需要重新加上)
                            processDetailExample.setLimit(500);
                            int deletedCount = jymlStoreSkuSuggestProcessDetailMapper.deleteByExample(processDetailExample);
                            // 如果本次删除的记录数为 0，说明已经没有数据可删，退出循环
                            if (deletedCount == 0) {
                                break;
                            }

                        }
                        //删除历史建议明细 和 门店上浮上限 时 只能删除version 小于 当前推送版本的 数据
                        while (true) {
                            JymlStoreSkuSuggestExample suggestExample = new JymlStoreSkuSuggestExample();
                            suggestExample.createCriteria().andVersionLessThan(version).andBusinessOrgIdEqualTo(businessOrgId);
                            // 设置分页，每次取 100 条记录，这里使用的是 limit 语法的逻辑模拟(重新生成时注意,limit 需要重新加上)
                            suggestExample.setLimit(2000);
                            int deletedCount = jymlStoreSkuSuggestMapper.deleteByExample(suggestExample);
                            // 如果本次删除的记录数为 0，说明已经没有数据可删，退出循环
                            if (deletedCount == 0) {
                                break;
                            }
                        }
                        while (true) {
                            JymlStoreIncreaseLimitConfigureExample limitConfigureExample = new JymlStoreIncreaseLimitConfigureExample();
                            limitConfigureExample.createCriteria().andVersionLessThan(version).andBusinessOrgIdEqualTo(businessOrgId);
                            limitConfigureExample.setLimit(200);
                            // 设置分页，每次取 100 条记录，这里使用的是 limit 语法的逻辑模拟(重新生成时注意,limit 需要重新加上)
                            int deletedCount = jymlStoreIncreaseLimitConfigureMapper.deleteByExample(limitConfigureExample);
                            // 如果本次删除的记录数为 0，说明已经没有数据可删，退出循环
                            if (deletedCount == 0) {
                                break;
                            }
                        }
                        while (true) {
                            JymlStoreSkuExemptionRecordExample exemptionRecordExample = new JymlStoreSkuExemptionRecordExample();
                            exemptionRecordExample.createCriteria().andVersionLessThan(version).andBusinessOrgIdEqualTo(businessOrgId);
                            exemptionRecordExample.setLimit(200);
                            // 设置分页，每次取 100 条记录，这里使用的是 limit 语法的逻辑模拟(重新生成时注意,limit 需要重新加上)
                            int deletedCount = jymlStoreSkuExemptionRecordMapper.deleteByExample(exemptionRecordExample);
                            // 如果本次删除的记录数为 0，说明已经没有数据可删，退出循环
                            if (deletedCount == 0) {
                                break;
                            }
                        }
                        storeNoList.forEach(v -> {
                            ManageSuggestMqDTO suggestMqDTO = new ManageSuggestMqDTO();
                            suggestMqDTO.setVersion(version);
                            suggestMqDTO.setStoreNo(v);
                            logger.info("bdpGroupingTaskResultNotify send msg ={}", suggestMqDTO);
                            manageSuggestProducer.send(JSONObject.toJSONString(suggestMqDTO));
                        });
                    }
                }
                tracerBean.close(span);
            });
        }  catch (Exception e) {
            logger.warn("bdpManageSuggestResultNotify 失败: ", e);
        }
        return CommonResult.ok(0,"成功");
    }
    @Override
    public CommonResult bdpManageSkuLimitResultNotify(Long version, String businessOrgIds) {
        List<Long> businessIdList = Arrays.stream(businessOrgIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessIdList)){
            logger.error("bdpManageSkuLimitResultNotify businessOrgIds is empty");
            return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        asyncTaskExecutor.execute(() -> {
            JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
            example.createCriteria().andVersionEqualTo(version).andBusinessOrgIdIn(businessIdList);
            long count = jymlSkuMaxLimitConfigureMapper.countByExample(example);
            if (count >0){
                JymlSkuMaxLimitConfigure record = new JymlSkuMaxLimitConfigure();
                record.setStatus(Constants.DEL_STATUS);
                JymlSkuMaxLimitConfigureExample delExample = new JymlSkuMaxLimitConfigureExample();
                delExample.createCriteria().andVersionLessThan(version).andStatusEqualTo(Constants.NORMAL_STATUS).andBusinessOrgIdIn(businessIdList);
                long loopCount = (count / Constants.TIDB_UPDATE_MAX_VALUE) + 1;
                for (int i = 0; i < loopCount; i++) {
                    jymlSkuMaxLimitConfigureExtendMapper.updateByExampleSelective(record, delExample, Constants.TIDB_UPDATE_MAX_VALUE);
                }
//                jymlSkuMaxLimitConfigureMapper.updateByExampleSelective(record, delExample);
                logger.info("bdpManageSkuLimitResultNotify del config ={}",delExample);
            }
        });
        return CommonResult.ok(0,"成功");
    }
    @Override
    public CommonResponse<ManageCategoryTreeDTO> manageContentsTree(TokenUserDTO userDTO, ManageQueryParam param) {
        OrgInfoBaseCache storeInfo = getStoreInfoByCache(param);
        if (Objects.isNull(storeInfo)) {
            return CommonResponse.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
        }
        JymlStoreSkuSuggestProcessDetailExample example = new JymlStoreSkuSuggestProcessDetailExample();
        JymlStoreSkuSuggestProcessDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreCodeEqualTo(param.getStoreNo());
        if (ScibCommonEnums.ManageTreeQuickTypeEnum.MANAGE_TREE_QUICK_TYPE_GOODS_FILTER.getCode().equals(param.getQuickType()) && Objects.nonNull(param.getGoodsNo())){
            JymlStoreSkuSuggestExample suggestExample=new JymlStoreSkuSuggestExample();
            suggestExample.createCriteria().andBusinessOrgIdEqualTo(storeInfo.getBusinessOrgId()).andGoodsNoEqualTo(param.getGoodsNo()).andStoreCodeEqualTo(param.getStoreNo());
            //jymlStoreSkuSuggestMapper.selectByExample()
        }
        example.setOrderByClause("category asc");
        List<JymlStoreSkuSuggestProcessDetail> options = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(example);
        return  CommonResponse.ok(ManageDataHelper.convertToTree(options,param));
    }
    @Override
    public CommonResponse<ManageCommonDTO> manageContentsList(TokenUserDTO userDTO, ManageQueryParam param) {
        OrgInfoBaseCache orgInfoBaseCache = getStoreInfoByCache(param);
        if (Objects.isNull(orgInfoBaseCache)) {
            return CommonResponse.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
        }
        if (Objects.isNull(param.getCategory()) || !param.getCategory().contains("/")) {
            return CommonResponse.error(ErrorCodeEnum.MANAGE_CATEGORY_ERROR);
        }
        StoreGoodsContentsExample storeGoodsInfoExample = new StoreGoodsContentsExample();
        StoreGoodsContentsExample.Criteria criteria = storeGoodsInfoExample.createCriteria();
        criteria.andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andStatusEqualTo(Constants.NORMAL_STATUS);
        String category = param.getCategory();
        if(Objects.nonNull(param.getCategory()) && param.getCategory().contains("/")){
            String[] split = param.getCategory().split("/");
            if (split.length == 4) {
                param.setSubCategoryId(split[3]);
                param.setSmallCategoryId(split[2]);
                param.setMiddleCategoryId(split[1]);
                criteria.andSubCategoryIdEqualTo(Long.parseLong(split[3]));
            }else  if (split.length == 3) {
                param.setSmallCategoryId(split[2]);
                param.setMiddleCategoryId(split[1]);
                criteria.andSubCategoryIdBetween(Long.parseLong(split[2]) * 100, Long.parseLong(split[2]) * 100 + 99);
            }else if (split.length == 2) {
                param.setMiddleCategoryId(split[1]);
                criteria.andSubCategoryIdBetween(Long.parseLong(split[1])*10000,Long.parseLong(split[1])*10000+9999);
            }
            param.setCategoryId(split[0]);
        }
        List<StoreGoodsContents> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(storeGoodsInfoExample);
        if (CollectionUtils.isEmpty(storeGoodsInfos)) {
            return CommonResponse.ok(new ArrayList<>());
        }
        JymlStoreSkuSuggestExample suggestExample  = new JymlStoreSkuSuggestExample();
        JymlStoreSkuSuggestExample.Criteria criteriaSuggest = suggestExample.createCriteria();
        criteriaSuggest.andBusinessOrgIdEqualTo(orgInfoBaseCache.getBusinessOrgId()).andStoreCodeEqualTo(orgInfoBaseCache.getSapCode());
        if(Objects.nonNull(param.getCategoryId())){
            criteriaSuggest.andCategoryEqualTo(param.getCategoryId());
        }
        if(Objects.nonNull(param.getMiddleCategoryId())){
            criteriaSuggest.andMiddleCategoryEqualTo(param.getMiddleCategoryId());
        }
        if(Objects.nonNull(param.getSmallCategoryId())){
            criteriaSuggest.andSmallCategoryEqualTo(param.getSmallCategoryId());
        }
        if(Objects.nonNull(param.getSubCategoryId())){
            criteriaSuggest.andSubCategoryEqualTo(param.getSubCategoryId());
        }
        List<JymlStoreSkuSuggest> jymlStoreSkuSuggests = jymlStoreSkuSuggestMapper.selectByExample(suggestExample);
        if (CollectionUtils.isEmpty(jymlStoreSkuSuggests)){
            return CommonResponse.ok(new ArrayList<>());
        }

        Set<String> validGoodsNos = jymlStoreSkuSuggests.stream()
                .map(JymlStoreSkuSuggest::getGoodsNo)
                .collect(Collectors.toCollection(HashSet::new));
        logger.info("manageContentsList validGoodsNos={}",validGoodsNos.size());
        storeGoodsInfos = storeGoodsInfos.stream()
                .filter(v -> validGoodsNos.contains(v.getGoodsNo()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeGoodsInfos)) {
            logger.info("manageContentsList 有效的商品集合为空 param={}",param);
            return CommonResponse.ok(new ArrayList<>());
        }
        SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
        spuNewParamVo.setBusinessId(orgInfoBaseCache.getBusinessId());
        spuNewParamVo.setGoodsNoList(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).filter(Objects::nonNull).collect(Collectors.toList()));
        Map<String, SpuNewVo> newSpuMap = searchService.getNewSpuMap(spuNewParamVo);
        Map<String, SpuListVo> jtSpuMap = searchService.getSpuVOMap(Lists.newArrayList(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).filter(Objects::nonNull).collect(Collectors.toList())));
        logger.info("manageContentsList newSpuMap: {}",newSpuMap.size());
        Map<String, JymlStoreSkuSuggest> skuSuggestMap = jymlStoreSkuSuggests.stream().filter(v -> Objects.nonNull(v.getGoodsNo())).collect(Collectors.toMap(JymlStoreSkuSuggest::getGoodsNo, Function.identity(), (k1, k2) -> k1));
        List<ManageCommonDTO> optionDtoList = new ArrayList<>();
        JymlStoreSkuSuggestProcess process = getProcess(orgInfoBaseCache.getSapCode());
        if (Objects.isNull(process)){
            logger.info("manageContentsList 进度为空 param={}",param);
            return CommonResponse.ok(new ArrayList<>());
        }
        boolean editAble;
        if (Objects.nonNull(process.getBeginProcessTime()) && Objects.nonNull(process.getEndProcessTime())){
            Date currentTime = new Date();
            editAble= DateUtil.isIn(currentTime, process.getBeginProcessTime(), process.getEndProcessTime());
        } else {
            editAble = Boolean.FALSE;
        }
        // 获取分类进度明细
        JymlStoreSkuSuggestProcessDetailExample exampleProcessDetail = new JymlStoreSkuSuggestProcessDetailExample();
        JymlStoreSkuSuggestProcessDetailExample.Criteria criteria2 = exampleProcessDetail.createCriteria();
        criteria2.andStoreCodeEqualTo(orgInfoBaseCache.getSapCode()).andStatusEqualTo(NORMAL_STATUS);
        if (Objects.nonNull(category) && category.contains("/")) {
            String[] split =category.split("/");
            if (split.length >= 1) {
                criteria2.andCategoryEqualTo(split[0]);
            }
            if (split.length >= 2) {
                criteria2.andMiddleCategoryEqualTo(split[1]);
            }
            if (split.length >= 3) {
                criteria2.andSmallCategoryEqualTo(split[2]);
            }
            if (split.length >= 4) {
                criteria2.andSubCategoryEqualTo(split[3]);
            }
        }
        exampleProcessDetail.setOrderByClause("id desc");
        exampleProcessDetail.setLimit(1);
        List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(exampleProcessDetail);
        if (CollectionUtils.isEmpty(jymlStoreSkuSuggestProcessDetails)){
            return CommonResponse.ok(new ArrayList<>());
        }

        DailySalesParam dailySalesParam = new DailySalesParam();
        dailySalesParam.setGoodsNos(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).filter(Objects::nonNull).collect(Collectors.toList()));
        dailySalesParam.setStoreId(orgInfoBaseCache.getOutId());
        Map<String, DailySalesResponse> avgSalesList = iscmService.getAvgSalesList(dailySalesParam);

        if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
            logger.info("manageContentsList storeGoodsInfos: {}",storeGoodsInfos.size());
            JymlStoreSkuSuggestProcessDetail jymlStoreSkuSuggestProcessDetail = jymlStoreSkuSuggestProcessDetails.get(0);
            JymlStoreSkuConfirmDetailExample confirmDetailExample=new JymlStoreSkuConfirmDetailExample();
            JymlStoreSkuConfirmDetailExample.Criteria criteria1 = confirmDetailExample.createCriteria();
            criteria1.andProcessDetailIdEqualTo(jymlStoreSkuSuggestProcessDetail.getId()).andVersionEqualTo(process.getVersion()).andStoreIdEqualTo(process.getStoreId()).andCategoryPathEqualTo(category).andStatusEqualTo(NORMAL_STATUS);
            Map<String, JymlStoreSkuConfirmDetail> confirmDetailMap  = jymlStoreSkuConfirmDetailMapper.selectByExample(confirmDetailExample).stream().filter(v -> Objects.nonNull(v.getGoodsNo())).collect(Collectors.toMap(JymlStoreSkuConfirmDetail::getGoodsNo, Function.identity(), (k1, k2) -> k1));
            Integer processStatus =jymlStoreSkuSuggestProcessDetail.getConfirmed();
            optionDtoList = storeGoodsInfos.stream()
                .map(v -> {
                    //logger.info("storeGoodsInfo 一店一目数据   v={}",JSONObject.toJSONString(v));
                    ManageCommonDTO manageCommonDTO = new ManageCommonDTO();
                    BeanUtils.copyProperties(v, manageCommonDTO);
                    if (jtSpuMap.containsKey(v.getGoodsNo())) {
                        SpuListVo jtSpuVo = jtSpuMap.get(v.getGoodsNo());
                        BeanUtils.copyProperties(jtSpuVo, manageCommonDTO);
                    }
                    if (newSpuMap.containsKey(v.getGoodsNo())) {
                        SpuNewVo spuNewVo = newSpuMap.get(v.getGoodsNo());
                        BeanUtils.copyProperties(spuNewVo, manageCommonDTO);
                    }
                    if (skuSuggestMap.containsKey(v.getGoodsNo())){
                        JymlStoreSkuSuggest skuSuggest = skuSuggestMap.get(v.getGoodsNo());
                        BeanUtils.copyProperties(skuSuggest, manageCommonDTO, "manageStatus", "suggestManageStatus","suggestManageStatusName");
                    }
                    if (avgSalesList.containsKey(v.getGoodsNo())){
                        DailySalesResponse dailySalesResponse = avgSalesList.get(v.getGoodsNo());
                        manageCommonDTO.setForbidApply(dailySalesResponse.getApplyForbidDesc());
                        manageCommonDTO.setForbidDistr(dailySalesResponse.getDistrForbidDesc());
                    }
                    //logger.info("manageCommonDTO v4: {}",manageCommonDTO);
                    try {
                        //其他数值时,取枚举返回前端即可
                        manageCommonDTO.setManageStatus(String.valueOf(v.getManageStatus()));
                        manageCommonDTO.setSuggestManageStatus(String.valueOf(v.getSuggestManageStatus()));
                        manageCommonDTO.setManageStatusName(ManageStatusEnum.getEnumByCode(v.getManageStatus()).getMessage());
                        manageCommonDTO.setSuggestManageStatusName(SuggestManageStatusEnum.getEnumByCode(v.getSuggestManageStatus()).getMessage());
                    }catch (Exception e){
                        logger.error("ManageContentsServiceImpl.handleSuggest error: {}",e.getMessage());
                    }
                    JymlStoreSkuConfirmDetail confirmDetail= confirmDetailMap.get(v.getGoodsNo());
                    if (Objects.nonNull(confirmDetail)){
                        logger.info("confirmDetail : {}",confirmDetail);
                        if(Objects.nonNull(confirmDetail.getMyConfirm())){
                            manageCommonDTO.setMyConfirm(confirmDetail.getMyConfirm());
                            manageCommonDTO.setMyConfirmName(ManageStatusEnum.getEnumByCode(confirmDetail.getMyConfirm()).getMessage());
                        }
                        if(Objects.nonNull(confirmDetail.getReviewResult())){
                            manageCommonDTO.setReviewResult(confirmDetail.getReviewResult());
                            manageCommonDTO.setReviewResultName(ManageStatusEnum.getEnumByCode(confirmDetail.getReviewResult()).getMessage());
                        }
                    }else {
                        //当一店一目表查询出上次确认为0时 默认显示 一店一目表中系统建议 getEnumBySuggestCode 转化公成 ManageStatus 填给前端
                        if (INTEGER_ZERO.equals(v.getManageStatus())) {
                            ManageStatusEnum enumBySuggestCode = ManageStatusEnum.getEnumBySuggestCode(v.getSuggestManageStatus());
                            if (Objects.nonNull(enumBySuggestCode)) {
                                logger.warn("商品 确认为空 SuggestCode转化公成ManageStatus");
                                manageCommonDTO.setMyConfirm(enumBySuggestCode.getCode());
                                manageCommonDTO.setMyConfirmName(enumBySuggestCode.getMessage());
                            }
                        } else {
                            manageCommonDTO.setMyConfirm(Integer.valueOf(manageCommonDTO.getManageStatus()));
                            manageCommonDTO.setMyConfirmName(ManageStatusEnum.getEnumByCode(Integer.valueOf(manageCommonDTO.getManageStatus())).getMessage());
                        }
                    }
                    logger.info("manageCommonDTO v5: {}",manageCommonDTO);
                    manageCommonDTO.setEditAble(editAble);
                    manageCommonDTO.setProcessStatus(processStatus);
                    manageCommonDTO.setCategory(category);
                    return manageCommonDTO;
                })
                .collect(Collectors.toList());
        }
        //logger.info("manageContentsList  optionDtoList.size={}  param={}", optionDtoList.size(),JSONObject.toJSONString(param));
        optionDtoList = ManageDataHelper.filterAndSortOptionDtoList(optionDtoList, param);
        //logger.info("manageContentsList: optionDtoList.size={}",optionDtoList.size());
        // 经营选配的商品判断是否是二轮选配
        List<String> goodsNos = optionDtoList.stream().filter(v -> ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(v.getMyConfirm())).map(ManageCommonDTO::getGoodsNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsNos)) {
            JymlStoreSkuExemptionRecordExample exemptionRecordExample = new JymlStoreSkuExemptionRecordExample();
            exemptionRecordExample.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andGoodsNoIn(goodsNos);
            List<String> secondGoodsNos = jymlStoreSkuExemptionRecordMapper.selectByExample(exemptionRecordExample).stream().map(JymlStoreSkuExemptionRecord::getGoodsNo).collect(Collectors.toList());
            optionDtoList.forEach(v -> {
                if (secondGoodsNos.contains(v.getGoodsNo())) {
                    v.setSecondAble(true);
                }
            });
        }
        return CommonResponse.ok(optionDtoList);
    }
    @Override
    public boolean handleSuggest(ManageSuggestMqDTO suggestMqDTO) {
        //分门店处理 月度建议到一店一目表
        ManageQueryParam param=new ManageQueryParam();
        param.setStoreNo(suggestMqDTO.getStoreNo());
        OrgInfoBaseCache storeInfo = getStoreInfoByCache(param);
        logger.info("handleSuggest storeNo={}",suggestMqDTO.getStoreNo());
        if (Objects.isNull(storeInfo) || Objects.isNull(storeInfo.getBusinessId())) {
            logger.warn("门店信息缓存不符合处理条件 storeNo={}  storeInfo={}",suggestMqDTO.getStoreNo(),JSONObject.toJSONString(storeInfo));
            return true;
        }
        //0 先判断门店是否sku管控 是管控的时候 走下面config取逻辑 即使写入到一店一目表里 process表里加载不出来左侧树 无法展示 所以先略过
        JymlSkuMaxLimitConfigure configure= getLastConfigTemplateByStore(param);
        if (Objects.isNull(configure)){
            logger.warn("门店没有配置Sku上限管控 storeInfo={}",JSONObject.toJSONString(storeInfo));
            return true;
        }
        //1.取得版本日对应的建议数据(国辉推送)
        JymlStoreSkuSuggestExample example  = new JymlStoreSkuSuggestExample();
        example.createCriteria().andVersionEqualTo(suggestMqDTO.getVersion()).andBusinessOrgIdEqualTo(storeInfo.getBusinessOrgId()).andStoreCodeEqualTo(suggestMqDTO.getStoreNo());
        List<JymlStoreSkuSuggest> jymlStoreSkuSuggests = jymlStoreSkuSuggestMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggests)){
            //2.查询spu信息(后改成查询集团品)
            SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
            spuNewParamVo.setBusinessId(storeInfo.getBusinessId());
            spuNewParamVo.setGoodsNoList(jymlStoreSkuSuggests.stream().map(JymlStoreSkuSuggest::getGoodsNo).filter(Objects::nonNull).sorted().collect(Collectors.toList()));
            Map<String, SpuListVo> newSpuMap = searchService.getSpuVOMap(spuNewParamVo.getGoodsNoList());
            logger.info("buildStoreGoodsContentDTO jymlStoreSkuSuggests.size={} newSpuMap.size={}" ,jymlStoreSkuSuggests.size(),newSpuMap.size());
            if (newSpuMap.isEmpty()){
                logger.warn("buildStoreGoodsContentDTO newSpuMap is null suggestMqDTO={}", suggestMqDTO);
                return true;
            }
            //5.调用马东推送月度建议数据
            TokenUserDTO userDTO= new TokenUserDTO();
            userDTO.setUserId(Constants.SYS_USER_ID);
            userDTO.setName(Constants.SYS_USER_NAME);
            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MONTH_MANAGE.getCode());
            List<StoreGoodsContentDTO> storeGoodsContentDTOS=buildStoreGoodsContentDTO(storeInfo,jymlStoreSkuSuggests,
                    StoreContentBizTypeEnum.MONTH_MANAGE.getCode(),userDTO,newSpuMap);
            if(CollectionUtils.isNotEmpty(storeGoodsContentDTOS)){
                assemble.work(storeGoodsContentDTOS);
                JymlStoreSkuSuggestProcessDetailExample detailExample = new JymlStoreSkuSuggestProcessDetailExample();
                detailExample.createCriteria().andStoreCodeEqualTo(suggestMqDTO.getStoreNo());
                jymlStoreSkuSuggestProcessDetailMapper.deleteByExample(detailExample);
                JymlStoreSkuSuggestProcessExample detailProcessExample = new JymlStoreSkuSuggestProcessExample();
                detailProcessExample.createCriteria().andStoreCodeEqualTo(suggestMqDTO.getStoreNo());
                jymlStoreSkuSuggestProcessMapper.deleteByExample(detailProcessExample);
                JymlStoreSkuExemptionRecordExample exemptionRecordExample = new JymlStoreSkuExemptionRecordExample();
                exemptionRecordExample.createCriteria().andStoreCodeEqualTo(suggestMqDTO.getStoreNo()).andVersionLessThan(suggestMqDTO.getVersion());
                jymlStoreSkuExemptionRecordMapper.deleteByExample(exemptionRecordExample);
                //3.汇总门店,分类级别进度数据 ( confirm 设置是否确认逻辑: 不管控门店和不需要用户确认的分类(不超上线) confirm直接确认     分类是否超上限逻辑: suggest 除了新品  (其他商品数X > 分类管控上线Y )  分类下无数据 当前分类不进入进度表
                List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails = summarizeProcessCategory(storeInfo,jymlStoreSkuSuggests,newSpuMap);
                Lists.partition(jymlStoreSkuSuggestProcessDetails,BATCH_INSERT_MAX_VALUE).forEach(v->{
                    jymlStoreSkuSuggestProcessDetailMapper.batchInsertSelective(v);
                });
                //4.汇总门店级别 进度数据 ( 汇总店级进度  )
                logger.info("JymlStoreSkuSuggest[0]={}",JSONObject.toJSONString(jymlStoreSkuSuggests.get(0)));
                JymlStoreSkuSuggestProcess jymlStoreSkuSuggestProcess = summarizeMdProcess(storeInfo,jymlStoreSkuSuggests,jymlStoreSkuSuggestProcessDetails);
                logger.info("jymlStoreSkuSuggestProcess={}",JSONObject.toJSONString(jymlStoreSkuSuggestProcess));
                if (Objects.nonNull(jymlStoreSkuSuggestProcess)){
                    jymlStoreSkuSuggestProcessMapper.insertSelective(jymlStoreSkuSuggestProcess);
                }
            }
        }
        logger.info("handleSuggest storeNo= {} end ", storeInfo.getSapCode());
        return true;
    }
    /**
     * 批量更新商品经营状态
     * @param userDTO 用户信息
     * @param manageBatchUpdateDTO 批量更新参数
     * @return 处理结果
     */
    public CommonResult batchUpdate(TokenUserDTO userDTO, ManageBatchUpdateParam manageBatchUpdateDTO) {
        // 获取所有需要处理的行
        List<ManageCommonDTO> rows = CollectionUtils.isNotEmpty(manageBatchUpdateDTO.getUnModifiedItems()) ?
                manageBatchUpdateDTO.getUnModifiedItems() : Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(manageBatchUpdateDTO.getRows())) {
            rows.addAll(manageBatchUpdateDTO.getRows());
        }
        rows = rows.stream().filter(v -> Objects.nonNull(v.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rows)) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_VERSION_ERROR);
        }

        // 获取门店信息
        ManageCommonDTO manageCommonDTO = rows.get(0);
        Optional<OrgInfoBaseCache> storeBase = CacheVar.getStoreBySapCode(manageCommonDTO.getStoreCode());
        if (!storeBase.isPresent() || storeBase.get().getBusinessId() == null) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
        }
        OrgInfoBaseCache storeInfo = storeBase.get();
        logger.info("batchUpdate storeInfo= {} ", storeInfo);
        Optional<MdmStoreExDTO> storeExtOpt = CacheVar.getStoreExtInfoBySapCode(manageCommonDTO.getStoreCode());
        if (!storeExtOpt.isPresent()) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_VERSION_ERROR);
        }
        // 获取分类进度明细
        JymlStoreSkuSuggestProcessDetailExample example = new JymlStoreSkuSuggestProcessDetailExample();
        JymlStoreSkuSuggestProcessDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreCodeEqualTo(storeInfo.getSapCode()).andStatusEqualTo(NORMAL_STATUS);
        if (Objects.nonNull(manageCommonDTO.getCategory()) && manageCommonDTO.getCategory().contains("/")) {
            String[] split = manageCommonDTO.getCategory().split("/");
            if (split.length >= 1) {
                criteria.andCategoryEqualTo(split[0]);
            }
            if (split.length >= 2) {
                criteria.andMiddleCategoryEqualTo(split[1]);
            }
            if (split.length >= 3) {
                criteria.andSmallCategoryEqualTo(split[2]);
            }
            if (split.length >= 4) {
                criteria.andSubCategoryEqualTo(split[3]);
            }
        }
        example.setOrderByClause("id desc");
        example.setLimit(1);
        List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(jymlStoreSkuSuggestProcessDetails)){
            return CommonResult.error(ErrorCodeEnum.MANAGE_PROCESS_ERROR);
        }
        // 该类别下经营状态 sku count （我的确认） in （经营-必备，经营-选配）的商品个数
        List<ManageCommonDTO> jyList =new ArrayList<>();
        // 根据不同的 action 值选择不同的字段进行过滤
        switch (manageBatchUpdateDTO.getAction()) {
            case 1: // 已提交待确认状态，使用 myConfirm 字段
                jyList = rows.stream()
                        .filter(v -> v.getMyConfirm() != null &&
                                ManageStatusEnum.jyStatusList.contains(v.getMyConfirm()))
                        .collect(Collectors.toList());
                break;
            case 2: // 已确认状态，使用 reviewResult 字段
                jyList = rows.stream()
                        .filter(v -> v.getReviewResult() != null &&
                                ManageStatusEnum.jyStatusList.contains(v.getReviewResult()))
                        .collect(Collectors.toList());
                break;
            case 0: // 页面不需要二次确认 ，也使用 myConfirm 字段
            default:
                jyList = rows.stream()
                        .filter(v -> v.getMyConfirm() != null &&
                                ManageStatusEnum.jyStatusList.contains(v.getMyConfirm()))
                        .collect(Collectors.toList());
                break;
        }
        // 当是店长批量确认时，则判断该类别下经营状态 sku count 是否超过上限
        // (注意:AutoConfirm 自动确认时不需要判断分类是否超上限)
        if (!manageBatchUpdateDTO.isAutoConfirm() && CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessDetails)) {
            JymlSkuMaxLimitConfigure newConfigure = getNewConfigure(manageCommonDTO.getCategory(), storeInfo, storeExtOpt.get());
            logger.info("newConfigure:{}", JSON.toJSONString(newConfigure));
            JymlStoreSkuSuggestProcessDetail jymlStoreSkuSuggestProcessDetail = jymlStoreSkuSuggestProcessDetails.get(0);
            if (null != newConfigure) {
                // 重新设置上下限
                jymlStoreSkuSuggestProcessDetail.setSkuLowerLimit(newConfigure.getSkuLowerLimit());
                jymlStoreSkuSuggestProcessDetail.setSkuMaxLimit(newConfigure.getSkuMaxLimit());
            }
            // 判断是否超过最大数量
            if (Objects.nonNull(jymlStoreSkuSuggestProcessDetail.getSkuMaxLimit()) &&
                    jyList.size() > jymlStoreSkuSuggestProcessDetail.getSkuMaxLimit()) {
                String msg = String.format(ErrorCodeEnum.MANAGE_SKU_LIMIT_ERROR.getMsg(),
                        jyList.size(), jymlStoreSkuSuggestProcessDetail.getSkuMaxLimit(),
                        jyList.size() - jymlStoreSkuSuggestProcessDetail.getSkuMaxLimit());
                logger.warn("batchUpdate storeNo={} msg {} ", jymlStoreSkuSuggestProcessDetail.getStoreCode(), msg);
                return CommonResult.error(ErrorCodeEnum.MANAGE_SKU_LIMIT_ERROR.getCode(), msg);
            }

            // 判断是否低于最小数量（新增下限检查）
            if (Objects.nonNull(jymlStoreSkuSuggestProcessDetail.getSkuLowerLimit()) &&
                    jyList.size() < jymlStoreSkuSuggestProcessDetail.getSkuLowerLimit()) {
                String msg = String.format(ErrorCodeEnum.MANAGE_SKU_LOWER_LIMIT_ERROR.getMsg(),
                        jyList.size(), jymlStoreSkuSuggestProcessDetail.getSkuLowerLimit(),
                        jymlStoreSkuSuggestProcessDetail.getSkuLowerLimit() - jyList.size());
                logger.warn("batchUpdate storeNo={} msg {} ", jymlStoreSkuSuggestProcessDetail.getStoreCode(), msg);
                return CommonResult.error(ErrorCodeEnum.MANAGE_SKU_LOWER_LIMIT_ERROR.getCode(), msg);
            }
        }
        // 根据action参数处理不同的逻辑
        Integer action = manageBatchUpdateDTO.getAction();
        // action=0 或 autoConfirm=true 时，执行原有逻辑
        if (ScibCommonEnums.ManageBatchUpdateActionEnum.JYML_BATCH_UPDATE_CONFIRM_SUBMIT.getCode().equals(action) ||
                manageBatchUpdateDTO.isAutoConfirm()) {
            return processDirectConfirm(userDTO, rows, storeInfo, jymlStoreSkuSuggestProcessDetails,manageBatchUpdateDTO.isAutoConfirm());
        }
        // action=1 时，执行提交审核逻辑
        else if (ScibCommonEnums.ManageBatchUpdateActionEnum.JYML_BATCH_UPDATE_SUBMIT_REVIEW.getCode().equals(action)) {
            return processSubmitForReview(userDTO, rows, storeInfo, jymlStoreSkuSuggestProcessDetails);
        }
        // action=2 时，执行审核确认逻辑
        else if (ScibCommonEnums.ManageBatchUpdateActionEnum.JYML_BATCH_UPDATE_REVIEW_CONFIRM.getCode().equals(action)) {
            return processReviewConfirm(userDTO, rows, storeInfo, jymlStoreSkuSuggestProcessDetails);
        } else {
            return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
    }
    /**
     * 处理直接确认逻辑（原有逻辑）
     */
    private CommonResult processDirectConfirm(TokenUserDTO userDTO, List<ManageCommonDTO> rows,
                                              OrgInfoBaseCache storeInfo,
                                              List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails,
                                              Boolean isAutoConfirm) {
        // 处理经营-选配商品
        List<ManageCommonDTO> chooseList = rows.stream()
                .filter(v -> ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(v.getMyConfirm()))
                .collect(Collectors.toList());
        logger.info("batchUpdate chooseList= {} ", chooseList.size());
        // 处理不经营商品
        int nonJyCount;
        //排除掉二轮选配商品
        JymlStoreSkuExemptionRecordExample exemptionRecordExample = new JymlStoreSkuExemptionRecordExample();
        exemptionRecordExample.createCriteria().andStoreIdEqualTo(storeInfo.getId());
        List<String> secondGoodsNos = jymlStoreSkuExemptionRecordMapper.selectByExample(exemptionRecordExample).stream().map(JymlStoreSkuExemptionRecord::getGoodsNo).distinct().collect(Collectors.toList());
        List<ManageCommonDTO> nonList = rows.stream()
                .filter(v -> ManageStatusEnum.NON_MANAGE.getCode().equals(v.getMyConfirm()) && !secondGoodsNos.contains(v.getGoodsNo()))
                .collect(Collectors.toList());
        logger.info("batchUpdate nonList= {} ", nonList.size());
        nonJyCount = nonList.size();
        manageContentsConfirmExecutor.submit(() -> {
            if (CollectionUtils.isNotEmpty(chooseList)) {
                MdmTask task = new MdmTask();
                task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                task.setDetailCount(chooseList.size());
                mdmTaskMapper.insertSelective(task);
                logger.info("batchUpdate task= {} ", task);

                StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
                List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(storeInfo, chooseList,
                        StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task);
                if (CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                    assemble.work(storeGoodsContentDTOS);
                }
            }
            if (CollectionUtils.isNotEmpty(nonList)) {
                MdmTask task = new MdmTask();
                task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                task.setDetailCount(nonList.size());
                mdmTaskMapper.insertSelective(task);
                logger.info("batchUpdate task= {} ", task);

                StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NON_MANAGE.getCode());
                List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(storeInfo, nonList,
                        StoreContentBizTypeEnum.NON_MANAGE.getCode(), userDTO, task);
                if (CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                    assemble.work(storeGoodsContentDTOS);
                }
            }
        });
        // 更新进度信息
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessDetails)) {
            logger.info("batchUpdate jymlStoreSkuSuggestProcessDetails= {} ", jymlStoreSkuSuggestProcessDetails.size());
            JymlStoreSkuSuggestProcessDetail jymlStoreSkuSuggestProcessDetail = jymlStoreSkuSuggestProcessDetails.get(0);

            // 确认经营商品个数 （我的确认） in （经营-必备，经营-选配）的商品个数
            List<ManageCommonDTO> jyList = rows.stream()
                    .filter(v -> ManageStatusEnum.jyStatusList.contains(v.getMyConfirm()))
                    .collect(Collectors.toList());
            jymlStoreSkuSuggestProcessDetail.setSkuCount(jyList.size());
            jymlStoreSkuSuggestProcessDetail.setGmtUpdate(new Date());
            jymlStoreSkuSuggestProcessDetail.setUpdatedBy(userDTO.getUserId());
            jymlStoreSkuSuggestProcessDetail.setUpdatedName(userDTO.getName());
            jymlStoreSkuSuggestProcessDetail.setConfirmed(INTEGER_ONE); // 设置为已确认
            jymlStoreSkuSuggestProcessDetail.setConfirmedBy(userDTO.getUserId());
            jymlStoreSkuSuggestProcessDetail.setConfirmedName(userDTO.getName());
            jymlStoreSkuSuggestProcessDetail.setExtend(ExtendUtil.putExtendValue(jymlStoreSkuSuggestProcessDetail.getExtend(), "nonJyCount", nonJyCount));
            logger.info("batchUpdate nonJyCount= {} jymlStoreSkuSuggestProcessDetail={}", nonJyCount, JSONObject.toJSONString(jymlStoreSkuSuggestProcessDetail));
            jymlStoreSkuSuggestProcessDetailMapper.updateByPrimaryKeySelective(jymlStoreSkuSuggestProcessDetail);
            batchSaveSkuConfirmDetail(userDTO, rows, storeInfo, jymlStoreSkuSuggestProcessDetail,isAutoConfirm);
            // 检查是否所有分类都已确认
            updateProcessConfirmStatus(userDTO, storeInfo, jymlStoreSkuSuggestProcessDetail, nonJyCount);
        }

        return CommonResult.ok();
    }
    /**
     * 处理提交审核逻辑
     */
    private CommonResult processSubmitForReview(TokenUserDTO userDTO, List<ManageCommonDTO> rows,
                                                OrgInfoBaseCache storeInfo,
                                                List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails) {
        if (CollectionUtils.isEmpty(jymlStoreSkuSuggestProcessDetails)) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_PROCESS_ERROR);
        }
        JymlStoreSkuSuggestProcessDetail processDetail = jymlStoreSkuSuggestProcessDetails.get(0);// 获取经营商品列表
        List<ManageCommonDTO> jyList = rows.stream()
                .filter(v -> ManageStatusEnum.jyStatusList.contains(v.getMyConfirm()))
                .collect(Collectors.toList());
        // 获取不经营商品列表
        List<ManageCommonDTO> nonList = rows.stream()
                .filter(v -> ManageStatusEnum.NON_MANAGE.getCode().equals(v.getMyConfirm()))
                .collect(Collectors.toList());
        int nonJyCount = nonList.size();

        // 1. 更新进度明细状态为待审核
        processDetail.setSkuCount(jyList.size());
        processDetail.setGmtUpdate(new Date());
        processDetail.setUpdatedBy(userDTO.getUserId());
        processDetail.setUpdatedName(userDTO.getName());
        processDetail.setConfirmed(INTEGER_NEGATIVE_ONE); // 设置为待审核状态 (-1)
        processDetail.setConfirmedBy(userDTO.getUserId());
        processDetail.setConfirmedName(userDTO.getName());
        processDetail.setExtend(ExtendUtil.putExtendValue(processDetail.getExtend(), "nonJyCount", nonJyCount));
        logger.info("processSubmitForReview nonJyCount= {} processDetail={}", nonJyCount,
                JSONObject.toJSONString(processDetail));
        jymlStoreSkuSuggestProcessDetailMapper.updateByPrimaryKeySelective(processDetail);
        batchSaveSkuConfirmDetail(userDTO, rows, storeInfo, processDetail,false);
        // 3. 检查是否所有分类都已提交审核
        updateProcessConfirmStatus(userDTO, storeInfo, processDetail, nonJyCount);
        return CommonResult.ok();
    }

    private void batchSaveSkuConfirmDetail(TokenUserDTO userDTO, List<ManageCommonDTO> rows, OrgInfoBaseCache storeInfo, JymlStoreSkuSuggestProcessDetail processDetail, Boolean isAutoConfirm) {
        // 2. 保存商品确认明细
        List<JymlStoreSkuConfirmDetail> confirmDetails = new ArrayList<>();
        Date now = new Date();

        for (ManageCommonDTO row : rows) {
            JymlStoreSkuConfirmDetail confirmDetail = new JymlStoreSkuConfirmDetail();
            confirmDetail.setBusinessOrgId(storeInfo.getBusinessId());
            confirmDetail.setStoreId(storeInfo.getOutId());
            confirmDetail.setStoreCode(storeInfo.getSapCode());
            confirmDetail.setProcessDetailId(processDetail.getId());
            confirmDetail.setGoodsNo(row.getGoodsNo());
            confirmDetail.setCategory(processDetail.getCategory());
            confirmDetail.setMiddleCategory(processDetail.getMiddleCategory());
            confirmDetail.setSmallCategory(processDetail.getSmallCategory());
            confirmDetail.setSubCategory(processDetail.getSubCategory());
            confirmDetail.setCategoryPath(row.getCategory());

            // 设置系统建议
            if (StringUtils.isNotBlank(row.getSuggestManageStatus())) {
                confirmDetail.setSystemSuggest(Integer.valueOf(row.getSuggestManageStatus()));
            }
            // 设置上次审核结果（当前manage_status）
            if (StringUtils.isNotBlank(row.getManageStatus())) {
                confirmDetail.setPreviousReviewResult(Integer.valueOf(row.getManageStatus()));
            }
            // 自动审核,取系统建议转设置本次我的确认
            if (isAutoConfirm && StringUtils.isNotBlank(row.getSuggestManageStatus())) {
                //张瑜确认: 自动确认时,取建议状态 转化成 我的确认提交给后续
                ManageStatusEnum enumBySuggestCode = ManageStatusEnum.getEnumBySuggestCode(Integer.valueOf(row.getSuggestManageStatus()));
                confirmDetail.setMyConfirm(enumBySuggestCode.getCode());
            }else if (Objects.nonNull(row.getMyConfirm())) {
                confirmDetail.setMyConfirm(row.getMyConfirm());
            }
            // 设置本次审核结果，默认等于本次我的确认
            confirmDetail.setReviewResult(confirmDetail.getMyConfirm());
            // 设置提交信息
            confirmDetail.setSubmitBy(userDTO.getUserId());
            confirmDetail.setSubmitName(userDTO.getName());
            confirmDetail.setSubmitTime(now);

            // 设置版本和状态
            confirmDetail.setVersion(processDetail.getVersion());
            confirmDetail.setStatus(NORMAL_STATUS);
            confirmDetail.setGmtCreate(now);
            confirmDetail.setGmtUpdate(now);
            confirmDetail.setCreatedBy(userDTO.getUserId());
            confirmDetail.setCreatedName(userDTO.getName());
            confirmDetail.setUpdatedBy(userDTO.getUserId());
            confirmDetail.setUpdatedName(userDTO.getName());

            confirmDetails.add(confirmDetail);
        }
        // 批量插入确认明细
        if (CollectionUtils.isNotEmpty(confirmDetails)) {
            JymlStoreSkuConfirmDetailExample example=new JymlStoreSkuConfirmDetailExample();
            example.createCriteria().andProcessDetailIdEqualTo(processDetail.getId());
            jymlStoreSkuConfirmDetailMapper.deleteByExample(example);
            for (List<JymlStoreSkuConfirmDetail> batch : Lists.partition(confirmDetails, INSERT_MAX_SIZE)) {
                jymlStoreSkuConfirmDetailExtendMapper.batchInsert(batch);
            }
        }
    }

    /**
     * 处理审核确认逻辑
     */
    private CommonResult processReviewConfirm(TokenUserDTO userDTO, List<ManageCommonDTO> rows,
                                              OrgInfoBaseCache storeInfo,
                                              List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails) {
        if (CollectionUtils.isEmpty(jymlStoreSkuSuggestProcessDetails)) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_PROCESS_ERROR);
        }

        JymlStoreSkuSuggestProcessDetail processDetail = jymlStoreSkuSuggestProcessDetails.get(0);
        // 检查当前状态是否为待审核
        if (INTEGER_ZERO.equals(processDetail.getConfirmed())) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_PROCESS_STATUS_ERROR);
        }
        // 获取经营商品列表
        List<ManageCommonDTO> jyList = rows.stream()
                .filter(v -> ManageStatusEnum.jyStatusList.contains(v.getReviewResult()))
                .collect(Collectors.toList());

        // 获取不经营商品列表
        List<ManageCommonDTO> nonList = rows.stream()
                .filter(v -> ManageStatusEnum.NON_MANAGE.getCode().equals(v.getReviewResult()))
                .collect(Collectors.toList());

        int nonJyCount = nonList.size();

        // 1. 更新进度明细状态为已审核确认
        processDetail.setSkuCount(jyList.size());
        processDetail.setGmtUpdate(new Date());
        processDetail.setUpdatedBy(userDTO.getUserId());
        processDetail.setUpdatedName(userDTO.getName());
        processDetail.setConfirmed(INTEGER_ONE); // 设置为已审核确认状态 (1)
        processDetail.setConfirmedBy(userDTO.getUserId());
        processDetail.setConfirmedName(userDTO.getName());
        processDetail.setExtend(ExtendUtil.putExtendValue(processDetail.getExtend(), "nonJyCount", nonJyCount));
        logger.info("processReviewConfirm nonJyCount= {} processDetail={}", nonJyCount,
                JSONObject.toJSONString(processDetail));
        jymlStoreSkuSuggestProcessDetailMapper.updateByPrimaryKeySelective(processDetail);

        // 2. 更新商品确认明细
        Date now = new Date();
        for (ManageCommonDTO row : rows) {
            // 查询确认明细
            JymlStoreSkuConfirmDetailExample example = new JymlStoreSkuConfirmDetailExample();
            example.createCriteria()
                    .andProcessDetailIdEqualTo(processDetail.getId())
                    .andGoodsNoEqualTo(row.getGoodsNo())
                    .andStatusEqualTo(NORMAL_STATUS);

            List<JymlStoreSkuConfirmDetail> confirmDetails = jymlStoreSkuConfirmDetailMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(confirmDetails)) {
                JymlStoreSkuConfirmDetail confirmDetail = confirmDetails.get(0);
                // 更新审核结果
                if (Objects.nonNull(row.getReviewResult())) {
                    confirmDetail.setReviewResult(row.getReviewResult());
                }
                // 设置审核信息
                confirmDetail.setReviewBy(userDTO.getUserId());
                confirmDetail.setReviewName(userDTO.getName());
                confirmDetail.setReviewTime(now);
                confirmDetail.setGmtUpdate(now);
                confirmDetail.setUpdatedBy(userDTO.getUserId());
                confirmDetail.setUpdatedName(userDTO.getName());
                jymlStoreSkuConfirmDetailMapper.updateByPrimaryKeySelective(confirmDetail);
            }
        }

        // 3. 处理经营-选配商品
        List<ManageCommonDTO> chooseList = rows.stream()
                .filter(v -> ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(v.getReviewResult()))
                .collect(Collectors.toList());
        logger.info("processReviewConfirm chooseList= {} ", chooseList.size());

        if (CollectionUtils.isNotEmpty(chooseList)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(chooseList.size());
            mdmTaskMapper.insertSelective(task);
            logger.info("processReviewConfirm task= {} ", task);

            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
            List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(storeInfo, chooseList,
                    StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task);
            if (CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                assemble.work(storeGoodsContentDTOS);
            }
        }

        // 4. 处理不经营商品
        if (CollectionUtils.isNotEmpty(nonList)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(nonList.size());
            mdmTaskMapper.insertSelective(task);
            logger.info("processReviewConfirm task= {} ", task);

            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NON_MANAGE.getCode());
            List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(storeInfo, nonList,
                    StoreContentBizTypeEnum.NON_MANAGE.getCode(), userDTO, task);
            if (CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                assemble.work(storeGoodsContentDTOS);
            }
        }
        // 5. 检查是否所有分类都已审核确认
        updateProcessConfirmStatus(userDTO, storeInfo, processDetail, nonJyCount);
        return CommonResult.ok();
    }

    /**
     * 更新进度确认状态
     */
    private void updateProcessConfirmStatus(TokenUserDTO userDTO, OrgInfoBaseCache storeInfo,
                                            JymlStoreSkuSuggestProcessDetail processDetail, int nonJyCount) {
        JymlStoreSkuSuggestProcessDetailExample exampleProcess = new JymlStoreSkuSuggestProcessDetailExample();
        exampleProcess.createCriteria()
                .andVersionEqualTo(processDetail.getVersion())
                .andStoreCodeEqualTo(processDetail.getStoreCode())
                .andStatusEqualTo(NORMAL_STATUS);
        List<JymlStoreSkuSuggestProcessDetail> allDetails = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(exampleProcess);

        if (CollectionUtils.isNotEmpty(allDetails)) {
            JymlStoreSkuSuggestProcess process = getProcess(storeInfo.getSapCode());
            logger.info("updateProcessConfirmStatus process={} before ", JSONObject.toJSONString(process));
            if (Objects.nonNull(process)) {
                Integer nonJyCountTotal = Optional.ofNullable(ExtendUtil.getExtendValue(process.getExtend(), "nonJyCount", Integer.class))
                        .map(count -> count + nonJyCount)
                        .orElse(nonJyCount);
                process.setExtend(ExtendUtil.putExtendValue(process.getExtend(), "nonJyCount", nonJyCountTotal));
                //是否全部确认
                boolean present = allDetails.stream().anyMatch(v -> v.getConfirmed()<=INTEGER_ZERO);
                if (!present){
                    process.setConfirmed(INTEGER_ONE);
                }
                logger.info("batchUpdate process={} 更新前",JSONObject.toJSONString(process));
                jymlStoreSkuSuggestProcessMapper.updateByPrimaryKeySelective(process);
                process.setUpdatedBy(userDTO.getUserId());
                process.setUpdatedName(userDTO.getName());
                logger.info("updateProcessConfirmStatus process={} after", JSONObject.toJSONString(process));
            }
        }
    }

    @Override
    public CommonResult summarizeMdProcessData(TokenUserDTO userDTO, ManageQueryParam param) {
        OrgInfoBaseCache storeInfoByCache = getStoreInfoByCache(param);
        if (Objects.isNull(storeInfoByCache)){
            return CommonResult.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
        }
        ManageProcessDTO manageProcessDTO = new ManageProcessDTO();
        setConfirmAndAdjustEditAble(storeInfoByCache, manageProcessDTO);
        JymlStoreSkuSuggestProcessDetailExample example=new JymlStoreSkuSuggestProcessDetailExample();
        JymlStoreSkuSuggestProcessDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreCodeEqualTo(storeInfoByCache.getSapCode()).andUpperLimitEqualTo(INTEGER_ONE).andStatusEqualTo(NORMAL_STATUS);
        example.setOrderByClause("id desc");
        List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessDetails)){
            long confirmedCount = jymlStoreSkuSuggestProcessDetails.stream()
                    .filter(detail -> detail.getConfirmed() != null && INTEGER_ONE.equals(detail.getConfirmed()) )
                    .count();
            // 获取总个数
            long totalCount = jymlStoreSkuSuggestProcessDetails.size();
            // 计算进度百分比
            double progressPercentage = totalCount == 0 ? 0 : (double) confirmedCount / totalCount * 100;
            // 格式化进度百分比为整数
            int formattedPercentage = (int) Math.round(progressPercentage);
            // 组装进度描述信息
            String processDesc = String.format("进度：%d%% ，完成%d个/共%d个类别待确认", formattedPercentage, confirmedCount, totalCount);
            manageProcessDTO.setProcessDesc(processDesc);
            manageProcessDTO.setStoreCode(storeInfoByCache.getSapCode());
            manageProcessDTO.setProcessPercent(BigDecimal.valueOf(formattedPercentage));

        }else {
            manageProcessDTO.setProcessDesc("进度：100%, 完成0个/共0个类别待确认");
            manageProcessDTO.setStoreCode(storeInfoByCache.getSapCode());
            manageProcessDTO.setProcessPercent(BigDecimal.valueOf(100));
        }
        setSkuIncreaseLimit(manageProcessDTO);
        return CommonResult.ok(manageProcessDTO);
    }

    private void setSkuIncreaseLimit(ManageProcessDTO manageProcessDTO) {
        if(manageProcessDTO.getEditAble()){
            JymlStoreIncreaseLimitConfigureExample example = new JymlStoreIncreaseLimitConfigureExample();
            example.createCriteria().andStoreCodeEqualTo(manageProcessDTO.getStoreCode());
            List<JymlStoreIncreaseLimitConfigure> jymlStoreIncreaseLimitConfigures = jymlStoreIncreaseLimitConfigureMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(jymlStoreIncreaseLimitConfigures)){
                JymlStoreIncreaseLimitConfigure jymlStoreIncreaseLimitConfigure = jymlStoreIncreaseLimitConfigures.get(0);
                manageProcessDTO.setIncreaseLimit(jymlStoreIncreaseLimitConfigure.getIncreaseLimit());
                manageProcessDTO.setIncreaseLow(jymlStoreIncreaseLimitConfigure.getIncreaseLow());
                return;
            }
        }
        manageProcessDTO.setIncreaseLimit(0);
    }

    @Override
    public CommonResult manageCategoryLimit(TokenUserDTO userDTO, ManageQueryParam param) {
        logger.info("manageCategoryLimit#param={}",JSONObject.toJSONString(param));
        OrgInfoBaseCache storeInfo = getStoreInfoByCache(param);
        if (null == storeInfo) {
            return CommonResult.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
        }
        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(storeInfo.getSapCode());
        ManageJymlCategoryLimitAndProcessDTO manageProcessDTO = new ManageJymlCategoryLimitAndProcessDTO();
        manageProcessDTO.setStoreCode(storeInfo.getSapCode());
        manageProcessDTO.setSkuCount(0);
        manageProcessDTO.setSkuLimit(0);
        manageProcessDTO.setSkuLowerLimit(0);
        manageProcessDTO.setIncreaseLimit(0);
        manageProcessDTO.setSubmitBtnAble(false);
        manageProcessDTO.setAuditBtnAble(false);
        manageProcessDTO.setProcessStatus(0);
        manageProcessDTO.setProcessStatusStr("");
        manageProcessDTO.setNeedConfirm(false);
        setConfirmAndAdjustEditAble(storeInfo, manageProcessDTO);
        if(Objects.nonNull(param.getCategory()) && param.getCategory().contains("/")){
            JymlStoreSkuSuggestProcessDetailExample example = getDetailExample(param, storeInfo);
            List<JymlStoreSkuSuggestProcessDetail> options = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(options)){
                JymlStoreSkuSuggestProcessDetail jymlStoreSkuSuggestProcessDetail = options.get(0);
                // 设置SKU数量
                // 未确认或待审核状态，需要重新计算当前SKU数量
                CommonResponse<ManageCommonDTO> manageCommonDTOCommonResponse = manageContentsList(userDTO, param);
                if (ErrorCodeEnum.SUCCESS.getCode().equals(manageCommonDTOCommonResponse.getStatus())
                        && CollectionUtils.isNotEmpty(manageCommonDTOCommonResponse.getData())) {
                    List<ManageCommonDTO> data = manageCommonDTOCommonResponse.getData();
                    long count = data.stream()
                            .filter(v -> ManageStatusEnum.jyStatusList.contains(v.getMyConfirm()))
                            .count();
                    manageProcessDTO.setSkuCount((int) count);
                } else {
                    manageProcessDTO.setSkuCount(0);
                }
                // 设置按钮状态
                boolean hasConfirmRole = CollectionUtils.isNotEmpty(userDTO.getRoles()) && userDTO.getRoles().contains(canConfirmRole);
                boolean isEditable = !Boolean.FALSE.equals(manageProcessDTO.getEditAble());
                // 默认所有按钮不可用
                manageProcessDTO.setSubmitBtnAble(Boolean.FALSE);
                manageProcessDTO.setAuditBtnAble(Boolean.FALSE);
                // 只有在可编辑状态下才考虑按钮可用性
                if (isEditable) {
                    if (Boolean.TRUE.equals(manageProcessDTO.getNeedConfirm())) {
                        Integer confirmStatus = jymlStoreSkuSuggestProcessDetail.getConfirmed();
                        // 需要二次确认的情况
                        switch (confirmStatus) {
                            case 0: // 待确认
                                manageProcessDTO.setSubmitBtnAble(Boolean.TRUE);
                                break;
                            case -1: // 已提交待确认
                                if (hasConfirmRole) {
                                    manageProcessDTO.setAuditBtnAble(Boolean.TRUE);
                                }
                                break;
                            case 1: // 已确认
                                if (hasConfirmRole) {
                                    manageProcessDTO.setAuditBtnAble(Boolean.TRUE);
                                }
                                break;
                            default:
                                break;
                        }
                    } else {
                        // 不需要二次确认的情况
                        manageProcessDTO.setSubmitBtnAble(Boolean.TRUE);
                    }
                }
                // 设置SKU限制和处理状态
                JymlSkuMaxLimitConfigure newConfigure = getNewConfigure(param.getCategory(), storeInfo, mdmStoreExDTO);
                logger.info("newConfigure:{}", JSON.toJSONString(newConfigure));
                manageProcessDTO.setSkuLimit(null != newConfigure ? newConfigure.getSkuMaxLimit() : jymlStoreSkuSuggestProcessDetail.getSkuMaxLimit());
                manageProcessDTO.setSkuLowerLimit(null != newConfigure ? newConfigure.getSkuLowerLimit() : jymlStoreSkuSuggestProcessDetail.getSkuLowerLimit());
                //在营SKU数 <= 下限
                Integer zySkuCount = ExtendUtil.getExtendValue(jymlStoreSkuSuggestProcessDetail.getExtend(), "zySkuCount", Integer.class);
                if(Objects.nonNull(zySkuCount) && zySkuCount<manageProcessDTO.getSkuLowerLimit() && zySkuCount<manageProcessDTO.getSkuLimit()  ){
                    manageProcessDTO.setSubmitBtnAble(Boolean.FALSE);
                    manageProcessDTO.setAuditBtnAble(Boolean.FALSE);
                }
                manageProcessDTO.setProcessStatus(jymlStoreSkuSuggestProcessDetail.getConfirmed());
                manageProcessDTO.setProcessStatusStr(Objects.requireNonNull(
                        ScibCommonEnums.ManageConfirmStatusEnum.getEnumByCode(jymlStoreSkuSuggestProcessDetail.getConfirmed())
                ).getMessage());
                logger.info("manageCategoryLimit#manageProcessDTO={}",JSONObject.toJSONString(manageProcessDTO));
                return CommonResult.ok(manageProcessDTO);
            }
        }
        return CommonResult.ok(manageProcessDTO);
    }

    private JymlSkuMaxLimitConfigure getNewConfigure(String category, OrgInfoBaseCache storeInfo, MdmStoreExDTO mdmStoreExDTO) {
        Long version = getNewVersion(storeInfo.getBusinessOrgId());
        JymlStoreSkuLimitAdjustEffectExample effectExample = new JymlStoreSkuLimitAdjustEffectExample();
        JymlStoreSkuLimitAdjustEffectExample.Criteria criteria = effectExample.createCriteria();
        criteria.andStoreOrgIdEqualTo(storeInfo.getId());
        JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
        JymlSkuMaxLimitConfigureExample.Criteria criteria1 = example.createCriteria();
        criteria1.andVersionEqualTo(version).andBusinessOrgIdEqualTo(storeInfo.getBusinessOrgId()).andCityEqualTo(mdmStoreExDTO.getCity());
        String[] split = StringUtils.split(category, "/");
        CategoryLevelEnum categoryLevelEnum = CategoryLevelEnum.getEnumByCode(split.length);
        switch (categoryLevelEnum) {
            case SUB:
                criteria.andSubCategoryEqualTo(split[3]);
                criteria1.andSubCategoryEqualTo(split[3]);
                criteria.andSmallCategoryEqualTo(split[2]);
                criteria1.andSmallCategoryEqualTo(split[2]);
                criteria.andMiddleCategoryEqualTo(split[1]);
                criteria1.andMiddleCategoryEqualTo(split[1]);
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            case SMALL:
                criteria.andSubCategoryEqualTo("");
                criteria1.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo(split[2]);
                criteria1.andSmallCategoryEqualTo(split[2]);
                criteria.andMiddleCategoryEqualTo(split[1]);
                criteria1.andMiddleCategoryEqualTo(split[1]);
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            case MIDDLE:
                criteria.andSubCategoryEqualTo("");
                criteria1.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo("");
                criteria1.andSmallCategoryEqualTo("");
                criteria.andMiddleCategoryEqualTo(split[1]);
                criteria1.andMiddleCategoryEqualTo(split[1]);
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            case CATEGROY:
                criteria.andSubCategoryEqualTo("");
                criteria1.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo("");
                criteria1.andSmallCategoryEqualTo("");
                criteria.andMiddleCategoryEqualTo("");
                criteria1.andMiddleCategoryEqualTo("");
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            default:throw new AmisBadRequestException("所选分类有误");
        }
        Optional<JymlStoreSkuLimitAdjustEffect> effectAny = jymlStoreSkuLimitAdjustEffectMapper.selectByExample(effectExample).stream().findAny();
        if (effectAny.isPresent()) {
            JymlStoreSkuLimitAdjustEffect effect = effectAny.get();
            criteria1.andStoreTypeEqualTo(effect.getStoreType());
        } else {
            criteria1.andStoreTypeEqualTo(mdmStoreExDTO.getStoreTypeCode());
        }
        return jymlSkuMaxLimitConfigureMapper.selectByExample(example).stream().findAny().orElse(null);
    }

    private Long getNewVersion(Long businessOrgId){
        JymlSkuMaxLimitConfigureExample configureExample = new JymlSkuMaxLimitConfigureExample();
        configureExample.createCriteria().andStatusEqualTo(Constants.NORMAL_STATUS).andBusinessOrgIdEqualTo(businessOrgId);
        configureExample.setLimit(1);
        configureExample.setOrderByClause(" id desc");
        jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample);
        return jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample).stream().map(JymlSkuMaxLimitConfigure::getVersion).findAny().orElse(null);
    }

    /**
     * 组装查询分类处理进度明细条件
     * @param param
     * @param storeInfo
     * @return
     */
    private static JymlStoreSkuSuggestProcessDetailExample getDetailExample(ManageQueryParam param, OrgInfoBaseCache storeInfo) {
        JymlStoreSkuSuggestProcessDetailExample example = new JymlStoreSkuSuggestProcessDetailExample();
        JymlStoreSkuSuggestProcessDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreCodeEqualTo(storeInfo.getSapCode());
        String[] split = param.getCategory().split("/");
        if (split.length >= 1) {
            criteria.andCategoryEqualTo(split[0]);
        }
        if (split.length >= 2) {
            criteria.andMiddleCategoryEqualTo(split[1]);
        }
        if (split.length >= 3) {
            criteria.andSmallCategoryEqualTo(split[2]);
        }
        if (split.length >= 4) {
            criteria.andSubCategoryEqualTo(split[3]);
        }
        example.setOrderByClause("category asc");
        example.setLimit(1);
        return example;
    }

    @Override
    public CommonResult skuLimitUpdate(ManageJymlSkuMaxLimitParam limitParam, TokenUserDTO userDTO) {
        JymlSkuMaxLimitConfigure configure = jymlSkuMaxLimitConfigureMapper.selectByPrimaryKey(limitParam.getId());
        if(Objects.isNull(configure)){
            return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        if (null == limitParam.getSkuMaxLimit() && null == limitParam.getSkuLowerLimit()) {
            return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getCode(), "请填写修改上/下限数量");
        }
        Boolean lowerUpdate = !configure.getSkuLowerLimit().equals(limitParam.getSkuLowerLimit());
        logger.info("lowerUpdate:{}", lowerUpdate);
        if(lowerUpdate) {
            if(configure.getSkuMaxLimit() < limitParam.getSkuLowerLimit()) {
                return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getCode(), "下限数量不能大于上限数量");
            }
            updateLimit(limitParam, lowerUpdate, userDTO);
            return CommonResult.ok("更新成功");
        }
        if(limitParam.getSkuMaxLimit() < configure.getSkuLowerLimit()) {
            return CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getCode(), "上限数量不能小于下限数量");
        }
        JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
        example.createCriteria().andPlatformOrgIdEqualTo(configure.getPlatformOrgId())
                .andBusinessOrgIdEqualTo(configure.getBusinessOrgId())
                .andCityEqualTo(configure.getCity())
                .andStoreTypeEqualTo(configure.getStoreType())
                .andStatusEqualTo(Constants.NORMAL_STATUS)
                .andVersionEqualTo(configure.getVersion());
        List<JymlSkuMaxLimitConfigure> jymlSkuMaxLimitConfigures = jymlSkuMaxLimitConfigureMapper.selectByExample(example);
        int sum = jymlSkuMaxLimitConfigures.stream().filter(v -> !v.getId().equals(limitParam.getId()) && Objects.nonNull(v.getSkuMaxLimit())).mapToInt(JymlSkuMaxLimitConfigure::getSkuMaxLimit)
                .sum();
        JymlSkuLimitExample groupExample = new JymlSkuLimitExample();
        groupExample.createCriteria().andStoreGroupCodeEqualTo(configure.getStoreType())
                .andPropertyCodeEqualTo(BudnlTaskDetailDicEnum.STORE.getCode())
                .andStatusEqualTo(Constants.NORMAL_STATUS);
        groupExample.setLimit(1);
        List<JymlSkuLimit> groupSkuLimits = jymlSkuLimitMapper.selectByExample(groupExample);
        if (CollectionUtils.isNotEmpty(groupSkuLimits)){
            JymlSkuLimit groupSkuLimit = groupSkuLimits.get(0);
            if (Objects.nonNull(groupSkuLimit.getTotalSku()) &&  limitParam.getSkuMaxLimit() + sum > groupSkuLimit.getTotalSku()* 1.05){
                String msg = ErrorCodeEnum.MANAGE_GROUP_LIMIT_ERROR.getMsg().replace("X", groupSkuLimit.getTotalSku() + "");
                return CommonResult.error(ErrorCodeEnum.MANAGE_GROUP_LIMIT_ERROR.getCode(),msg);
            }
        }
        updateLimit(limitParam, lowerUpdate, userDTO);
        return CommonResult.ok("更新成功");
    }

    private void updateLimit(ManageJymlSkuMaxLimitParam limitParam, Boolean lowerUpdate, TokenUserDTO userDTO) {
        JymlSkuMaxLimitConfigure record= new JymlSkuMaxLimitConfigure();
        record.setId(limitParam.getId());
        if (null != limitParam.getSkuMaxLimit()) {
        }
        if (lowerUpdate) {
            record.setSkuLowerLimit(limitParam.getSkuLowerLimit());
        } else {
            record.setSkuMaxLimit(limitParam.getSkuMaxLimit());
        }
        record.setUpdateBy(userDTO.getName());
        record.setUpdateById(userDTO.getUserId());
        int rows = jymlSkuMaxLimitConfigureMapper.updateByPrimaryKeySelective(record);
    }

    public Boolean selectMdmStoreIdFilterSelector(OrgInfoBaseCache storeInfo){
        List<Long> selectorStoreIdList = new ArrayList<>();
        logger.info("selectMdmStoreIdFilterSelector|storeInfo:{}.", JSONObject.toJSONString(storeInfo));
        if(Objects.isNull(storeInfo.getBusinessOrgId()) || Objects.isNull(storeInfo.getOutId())){
            logger.warn("selectMdmStoreFilterSelector|入参为空");
            return Boolean.FALSE;
        }
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ROOT_ORG_ID,ConfigTypeEnum.JY.getType(), null);
        if (Objects.isNull(configOrg)){
            logger.warn("selectMdmStoreFilterSelector|经营范围参数配置为空");
            return Boolean.FALSE;
        }
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Lists.newArrayList("jymu_businesslist"));
        if(CollectionUtils.isEmpty(configOrgDetails)){
            logger.warn("selectMdmStoreFilterSelector|企业连锁为空");
            return null;
        }else {
            List<Long> businessOrgIds = configOrgDetails.stream().map(ConfigOrgDetail::getPerprotyValue).map(Long::parseLong).collect(Collectors.toList());
            if (businessOrgIds.contains(storeInfo.getBusinessOrgId())){
                RuleParam ruleParam = new RuleParam();
                ruleParam.setOrgId(ROOT_ORG_ID);
                ruleParam.setScopeCode(DicApiEnum.JYML_STORE.getCode());
                ruleParam.setConfigType(ConfigTypeEnum.JY.getType().toString());
                Map<String, List<String>> selectParamMap = bundlTaskService.buildSelectParam(ruleParam, null);
                //Map<String, List<String>> selectParamMap = buildSelectParam(ruleParam);
                if(MapUtils.isEmpty(selectParamMap)){
                    logger.warn("selectMdmStoreFilterSelector|选择器入参为空");
                    return Boolean.FALSE;
                }
                StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
                queryParam.setScopeCode(String.valueOf(storeInfo.getBusinessOrgId()));
                queryParam.setManagstate(selectParamMap.get(DictAliasEnum.MANAGSTATE.getAlias()));
                queryParam.setStorestatus(selectParamMap.get(DictAliasEnum.STORESTATUS.getAlias()));
                queryParam.setFormat(selectParamMap.get(DictAliasEnum.FORMAT.getAlias()));
                queryParam.setStoretype(selectParamMap.get(DictAliasEnum.STORETYPE.getAlias()));
                queryParam.setOperationtype(selectParamMap.get(DictAliasEnum.OPERATIONTYPE.getAlias()));
                queryParam.setSpecialtype(selectParamMap.get(DictAliasEnum.SPECIALTYPE.getAlias()));
                queryParam.setExcludeOrgIds(selectParamMap.get(DictAliasEnum.EXCLUDEORGIDS.getAlias()));
                queryParam.setStoreattr(selectParamMap.get(DictAliasEnum.STOREATTR.getAlias()));
                queryParam.setOrgIds(selectParamMap.get(DictAliasEnum.ORGIDS.getAlias()));
                selectorStoreIdList = tagService.getSelectStoreIdListByType(queryParam);
            }
        }
        boolean contains = selectorStoreIdList.contains(storeInfo.getOutId());
        logger.info("selectMdmStoreIdFilterSelector| storeNo ={} contains={} ",storeInfo.getSapCode(), contains);
        return contains;
    }
    @Override
    public Boolean handleSuggestByStoreOrBusiness(ManageSuggestMqDTO suggestMqDTO) {
        if (Objects.isNull(suggestMqDTO)||Objects.isNull(suggestMqDTO.getVersion())){
            logger.warn("handleSuggestByStoreOrBusiness|入参为空");
            return Boolean.FALSE;
        }
        if (StringUtils.isNotBlank(suggestMqDTO.getStoreNo())){
           return handleSuggest(suggestMqDTO);
        }else if(StringUtils.isNotBlank(suggestMqDTO.getBusinessIds())) {
            List<Long> businessIdList = Arrays.stream(suggestMqDTO.getBusinessIds().split(","))
                    .map(String::trim) // 去除可能存在的空格
                    .filter(s -> !s.isEmpty()) // 过滤掉空字符串
                    .map(Long::parseLong) // 将字符串转换为 Long 类型
                    .collect(Collectors.toList());
            List<OrgInfoBaseCache> businessList = CacheVar.getBusinessOrgByBusinessIdList(businessIdList).orElse(null);
            if(CollectionUtils.isNotEmpty(businessList)){
                String orgIds = businessList.stream()
                        .map(OrgInfoBaseCache::getBusinessOrgId)
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(orgIds)){
                    CommonResult commonResult = bdpManageSuggestResultNotify(suggestMqDTO.getVersion(), orgIds);
                    if(commonResult.getStatus() == 0){
                        return Boolean.TRUE;
                    }
                }
            }
        }
        return Boolean.FALSE;
    }
    @Override
    public CommonResponse<JymlStoreSkuExemptionRecord> getNonJyGoods(ManageJymlNonJyGoodsParam param, TokenUserDTO userDTO) {
         if (param.getStoreId() != null) {
             Optional<OrgInfoBaseCache>  storeOptional = CacheVar.getStoreByStoreId(param.getStoreId());
             if (storeOptional.isPresent()){
                 OrgInfoBaseCache orgInfoBaseCache = storeOptional.get();
                 StoreGoodsContentsExample storeGoodsInfoExample = new StoreGoodsContentsExample();
                 StoreGoodsContentsExample.Criteria criteria = storeGoodsInfoExample.createCriteria();
                 criteria.andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andManageStatusEqualTo(ManageStatusEnum.NON_MANAGE.getCode()).andStatusEqualTo(Constants.NORMAL_STATUS);
                 List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(storeGoodsInfoExample);
                 if (CollectionUtils.isNotEmpty(storeGoodsContents)){
                     JymlStoreSkuSuggestExample suggestExample  = new JymlStoreSkuSuggestExample();
                     JymlStoreSkuSuggestExample.Criteria criteriaSuggest = suggestExample.createCriteria();
                     criteriaSuggest.andBusinessOrgIdEqualTo(orgInfoBaseCache.getBusinessOrgId()).andStoreCodeEqualTo(orgInfoBaseCache.getSapCode());
                     // 批量查询 小于500 in  大于500 查询整个门店
                     if(storeGoodsContents.size()<Constants.EXPORT_ONCE_QUERY_MAX){
                         criteriaSuggest.andGoodsNoIn(storeGoodsContents.stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList()));
                     }
                     List<JymlStoreSkuSuggest> jymlStoreSkuSuggests = jymlStoreSkuSuggestMapper.selectByExample(suggestExample);
                     if (CollectionUtils.isEmpty(jymlStoreSkuSuggests)){
                         return CommonResponse.ok();
                     }
                     Map<String, JymlStoreSkuSuggest> skuSuggestMap = jymlStoreSkuSuggests.stream().filter(v -> Objects.nonNull(v.getGoodsNo())).collect(Collectors.toMap(JymlStoreSkuSuggest::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                     logger.info("getNonJyGoods jymlStoreSkuSuggests.size: {}",jymlStoreSkuSuggests.size());
                     List<String> notFindGoodsNo=new ArrayList<>();
                     List<JymlStoreSkuExemptionRecord>  optionDtoList = storeGoodsContents.stream()
                             .map(v -> {
                                 //logger.info("storeGoodsInfo 一店一目数据   v={}",JSONObject.toJSONString(v));
                                 JymlStoreSkuExemptionRecord record =  new JymlStoreSkuExemptionRecord();
                                 BeanUtils.copyProperties(v, record);
                                 if (skuSuggestMap.containsKey(v.getGoodsNo())){
                                     JymlStoreSkuSuggest skuSuggest = skuSuggestMap.get(v.getGoodsNo());
                                     record.setGoodsName(skuSuggest.getGoodsName());
                                 }else {
                                     notFindGoodsNo.add(v.getGoodsNo());
                                 }
                                 record.setId(null);
                                 return record;
                             })
                             .collect(Collectors.toList());
                     if (CollectionUtils.isNotEmpty(notFindGoodsNo)){
                         logger.info("getNonJyGoods notFindGoodsNo: {}",notFindGoodsNo);
                         Map<String, SpuListVo> newSpuMap = searchService.getSpuVOMap(notFindGoodsNo);
                         optionDtoList.forEach(item -> {
                             SpuListVo spuNewVo = newSpuMap.get(item.getGoodsNo());
                             if (Objects.nonNull(spuNewVo)){
                                 item.setGoodsName(spuNewVo.getName());
                             }
                         });
                     }
                     if (StringUtils.isNotBlank(param.getKeyword())){
                         optionDtoList = optionDtoList.stream()
                                 .filter(item ->
                                         (item.getGoodsNo() != null && item.getGoodsNo().toLowerCase().contains(param.getKeyword()))
                                                 // 如果你的类中有 goodsName 字段，可以添加下面这行
                                                 || (item.getGoodsName() != null && item.getGoodsName().toLowerCase().contains(param.getKeyword()))
                                 )
                                 .collect(Collectors.toList());
                     }
                     return CommonResponse.ok(optionDtoList);
                 }
                 return CommonResponse.ok(new ArrayList<>());
             }
        }
        return CommonResponse.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
    }
    @Override
    public CommonResponse<JymlStoreSkuExemptionRecordDTO> nonJyGoodsPage(JymlStoreSkuExemptionRecord record, TokenUserDTO userDTO) {
        Optional<OrgInfoBaseCache>  storeOptional = CacheVar.getStoreByStoreId(record.getStoreId());
        if (storeOptional.isPresent()){
            OrgInfoBaseCache orgInfoBaseCache = storeOptional.get();
            JymlStoreSkuExemptionRecordExample recordExample = new JymlStoreSkuExemptionRecordExample();
            recordExample.createCriteria().andStoreCodeEqualTo(orgInfoBaseCache.getSapCode());
            recordExample.setOrderByClause(" id desc");
            List<JymlStoreSkuExemptionRecordDTO> jymlStoreSkuExemptionRecords = jymlStoreSkuExemptionRecordMapper.selectByExample(recordExample).stream().map(v -> {
                JymlStoreSkuExemptionRecordDTO dto = new JymlStoreSkuExemptionRecordDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setSourceDesc(ExemptionSourceEnum.getNameByCode(v.getSource()));
                return dto;
            }).collect(Collectors.toList());
            return CommonResponse.ok(jymlStoreSkuExemptionRecords);
        }
        return CommonResponse.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
    }
    @Override
    public CommonResult nonJyGoodsUpdate(JymlStoreSkuExemptionRecord record, TokenUserDTO userDTO) {
        if (record != null && record.getId() != null){
            JymlStoreSkuExemptionRecord oldRecord = jymlStoreSkuExemptionRecordMapper.selectByPrimaryKey(record.getId());
            Assert.notNull(oldRecord, ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
            Optional<OrgInfoBaseCache>  storeOptional = CacheVar.getStoreByStoreId(oldRecord.getStoreId());
            if (storeOptional.isPresent()){
                JymlStoreIncreaseLimitConfigureExample example = new JymlStoreIncreaseLimitConfigureExample();
                example.createCriteria().andStoreCodeEqualTo(storeOptional.get().getSapCode());
                List<JymlStoreIncreaseLimitConfigure> jymlStoreIncreaseLimitConfigures = jymlStoreIncreaseLimitConfigureMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(jymlStoreIncreaseLimitConfigures)) {
                    JymlStoreIncreaseLimitConfigure jymlStoreIncreaseLimitConfigure = jymlStoreIncreaseLimitConfigures.get(0);
                    Integer increaseLow = Optional.ofNullable(jymlStoreIncreaseLimitConfigure.getIncreaseLow()).orElse(0);
                    JymlStoreSkuExemptionRecordExample recordExample = new JymlStoreSkuExemptionRecordExample();
                    recordExample.createCriteria().andStoreCodeEqualTo(storeOptional.get().getSapCode());
                    long count = jymlStoreSkuExemptionRecordMapper.countByExample(recordExample);
                    if (increaseLow.longValue() >= count) {
                        return new CommonResult(ErrorCodeEnum.MANAGE_MIN_ADD_ERROR.getCode(), "不允许删除，二轮选配商品个数需在"+increaseLow+"-" +jymlStoreIncreaseLimitConfigure.getIncreaseLimit()+ "个之间");
                    }
                }
                OrgInfoBaseCache orgInfoBaseCache = storeOptional.get();
                //删除时 选配改成不经营
                List<ManageCommonDTO> confirmList = new ArrayList<>();
                ManageBatchUpdateParam manageBatchUpdateDTO = new ManageBatchUpdateParam();
                manageBatchUpdateDTO.setAutoConfirm(true);

                ManageCommonDTO manageCommonDTO = new ManageCommonDTO();
                BeanUtils.copyProperties(oldRecord, manageCommonDTO);
                manageCommonDTO.setManageStatus(String.valueOf(ManageStatusEnum.NON_MANAGE.getCode()));
                manageCommonDTO.setSuggestManageStatus(String.valueOf(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode()));
                confirmList.add(manageCommonDTO);

                MdmTask task = new MdmTask();
                task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                task.setDetailCount(1);
                mdmTaskMapper.insertSelective(task);
                logger.info("nonJyGoodsUpdate batchUpdate task= {} ",task);
                StoreContentsAssemble assemble = factory.getAssemble( StoreContentBizTypeEnum.NON_MANAGE.getCode());
                List<StoreGoodsContentDTO> storeGoodsContentDTOS=buildStoreGoodsContentDTOConfirm(orgInfoBaseCache,confirmList, StoreContentBizTypeEnum.NON_MANAGE.getCode(),userDTO, task);
                if(CollectionUtils.isNotEmpty(storeGoodsContentDTOS)){
                    assemble.work(storeGoodsContentDTOS);
                    jymlStoreSkuExemptionRecordMapper.deleteByPrimaryKey(record.getId());
                    updateConfirmDetailByNonJyGoodsUpdate(orgInfoBaseCache,record,userDTO,ManageStatusEnum.NON_MANAGE);
                    return CommonResult.ok("操作成功");
                }
            }
        }else {
            Assert.notNull(record, ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
            Assert.notNull(record.getStoreId(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
            Optional<OrgInfoBaseCache>  storeOptional = CacheVar.getStoreByStoreId(record.getStoreId());
            if (storeOptional.isPresent()){
                OrgInfoBaseCache orgInfoBaseCache = storeOptional.get();
                Assert.notNull(record.getGoodsNo(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
                JymlStoreSkuSuggestProcess process = getProcess(orgInfoBaseCache.getSapCode());
                if (Objects.nonNull(process) && Objects.nonNull(process.getBeginProcessTime()) && Objects.nonNull(process.getEndProcessTime())){
                    JymlStoreIncreaseLimitConfigureExample example = new JymlStoreIncreaseLimitConfigureExample();
                    example.createCriteria().andStoreCodeEqualTo(orgInfoBaseCache.getSapCode());
                    List<JymlStoreIncreaseLimitConfigure> jymlStoreIncreaseLimitConfigures = jymlStoreIncreaseLimitConfigureMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(jymlStoreIncreaseLimitConfigures)){
                        JymlStoreIncreaseLimitConfigure jymlStoreIncreaseLimitConfigure = jymlStoreIncreaseLimitConfigures.get(0);
                        Integer increaseLimit = jymlStoreIncreaseLimitConfigure.getIncreaseLimit();
                        if (increaseLimit != null && increaseLimit > 0){
                            JymlStoreSkuExemptionRecordExample recordExample = new JymlStoreSkuExemptionRecordExample();
                            recordExample.createCriteria().andStoreCodeEqualTo(orgInfoBaseCache.getSapCode());
                            List<JymlStoreSkuExemptionRecord> jymlStoreSkuExemptionRecords = jymlStoreSkuExemptionRecordMapper.selectByExample(recordExample);
                            if (CollectionUtils.isNotEmpty(jymlStoreSkuExemptionRecords)){
                                if (jymlStoreSkuExemptionRecords.size() >= increaseLimit){
                                    return CommonResult.error(ErrorCodeEnum.MANAGE_MAX_ADD_ERROR);
                                }
                            }
                            boolean present = jymlStoreSkuExemptionRecords.stream().anyMatch(v -> v.getGoodsNo().equals(record.getGoodsNo()));
                            if (present){
                                return CommonResult.error(ErrorCodeEnum.MANAGE_HAS_IN_ERROR);
                            }
                            //2.查询spu信息(后改成查询集团品)
                            Map<String, SpuListVo> newSpuMap = searchService.getSpuVOMap(Lists.newArrayList(record.getGoodsNo()));
                            Map<Long, CommonCategoryDTO> categoryMap = getCategoryBySubIds(newSpuMap.values().stream().map(SpuListVo::getCategoryId).filter(StringUtils::isNotBlank).map(Long::valueOf).distinct().collect(Collectors.toList()));
                            if (MapUtils.isNotEmpty(newSpuMap)){
                                SpuListVo spuNewVo = newSpuMap.get(record.getGoodsNo());
                                record.setGoodsName(spuNewVo.getName());
                                record.setJhispecification(spuNewVo.getJhiSpecification());
                                record.setFactoryid(spuNewVo.getFactoryid());
                                record.setGoodsunit(spuNewVo.getGoodsunit());
                                record.setComponent(spuNewVo.getComponent());
                                CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(spuNewVo.getCategoryId()));
                                record.setSmallCategory(null != commonCategoryDTO ? commonCategoryDTO.getSmallCategoryId().toString() : "");
                                record.setSmallCategoryName(null != commonCategoryDTO ? commonCategoryDTO.getSmallCategoryName():"");
                                record.setSubCategory(null != commonCategoryDTO ?commonCategoryDTO.getSubCategoryId().toString():"");
                                record.setSubCategoryName(null != commonCategoryDTO ?commonCategoryDTO.getSubCategoryName():"");
                            }
                            record.setId(null);
                            record.setBusinessOrgId(orgInfoBaseCache.getBusinessOrgId());
                            record.setStoreId(orgInfoBaseCache.getOutId());
                            record.setStoreCode(orgInfoBaseCache.getSapCode());
                            record.setCreatedBy(userDTO.getUserId());
                            record.setCreatedName(userDTO.getName());
                            //添加时 不经营改成建议选配
                            List<ManageCommonDTO> chooseList = new ArrayList<>();
                            ManageBatchUpdateParam manageBatchUpdateDTO = new ManageBatchUpdateParam();
                            manageBatchUpdateDTO.setAutoConfirm(true);
                            ManageCommonDTO manageCommonDTO = new ManageCommonDTO();
                            BeanUtils.copyProperties(record, manageCommonDTO);
                            manageCommonDTO.setManageStatus(String.valueOf(ManageStatusEnum.MANAGE_CHOOSE.getCode()));
                            manageCommonDTO.setSuggestManageStatus(String.valueOf(SuggestManageStatusEnum.SUGGEST_NON_MANAGE.getCode()));
                            chooseList.add(manageCommonDTO);
                            MdmTask task = new MdmTask();
                            task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
                            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                            task.setDetailCount(1);
                            mdmTaskMapper.insertSelective(task);
                            logger.info("nonJyGoodsUpdate batchUpdate add task= {} ",task);
                            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
                            List<StoreGoodsContentDTO> storeGoodsContentDTOS=buildStoreGoodsContentDTOConfirm(orgInfoBaseCache,chooseList, StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(),userDTO,task);
                            if(CollectionUtils.isNotEmpty(storeGoodsContentDTOS)){
                                assemble.work(storeGoodsContentDTOS);
                                jymlStoreSkuExemptionRecordMapper.insertSelective(record);
                                updateConfirmDetailByNonJyGoodsUpdate(orgInfoBaseCache,record,userDTO,ManageStatusEnum.MANAGE_CHOOSE);
                                return CommonResult.ok("操作成功");
                            }
                        }else {
                            return CommonResult.error(ErrorCodeEnum.MANAGE_MAX_ADD_ERROR);
                        }
                    }
                } else {
                    return CommonResult.error(ErrorCodeEnum.MANAGE_NOT_IN_TIME_ERROR);
                }
            }else {
                return CommonResult.error(ErrorCodeEnum.MANAGE_STORE_ERROR);
            }
        }
        return CommonResult.error(ErrorCodeEnum.GOODS_NOT_FIND_ERROR);
    }

    /**
     * 根据子类id获取四级类目信息
     * @param subCategoryIds
     * @return
     */
    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        if (CollectionUtils.isEmpty(subCategoryIds)) {
            logger.info("待查询的子类id为空");
            return new HashMap<>();
        }
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1, k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        return resultMap;
    }

    /**
     * 豁免商品 添加和删除 更新 我的确认 或 审核确认 记录
     */
    private void updateConfirmDetailByNonJyGoodsUpdate(OrgInfoBaseCache orgInfoBaseCache, JymlStoreSkuExemptionRecord record,TokenUserDTO userDTO,ManageStatusEnum manageStatusEnum){
        Assert.notNull(record.getGoodsNo(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        JymlStoreSkuSuggestProcess process = getProcess(orgInfoBaseCache.getSapCode());
        if(Objects.isNull(process)){
            logger.warn("豁免商品查询进度表异常 record={}",record);
            return;
        }
        // 查询确认明细
        JymlStoreSkuConfirmDetailExample example = new JymlStoreSkuConfirmDetailExample();
        example.createCriteria()
                .andStoreIdEqualTo(orgInfoBaseCache.getOutId())
                .andVersionEqualTo(process.getVersion())
                .andGoodsNoEqualTo(record.getGoodsNo())
                .andStatusEqualTo(NORMAL_STATUS);
        example.setLimit(1);
        List<JymlStoreSkuConfirmDetail> confirmDetails = jymlStoreSkuConfirmDetailMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(confirmDetails)) {
            JymlStoreSkuConfirmDetail confirmDetail = confirmDetails.get(0);
            ManageJymlCategoryLimitAndProcessDTO manageProcessDTO = new ManageJymlCategoryLimitAndProcessDTO();
            manageProcessDTO.setNeedConfirm(false);
            setConfirmAndAdjustEditAble(orgInfoBaseCache, manageProcessDTO);
            if (!manageProcessDTO.getNeedConfirm()){
                confirmDetail.setMyConfirm(manageStatusEnum.getCode());
                confirmDetail.setSubmitBy(userDTO.getUserId());
                confirmDetail.setSubmitName(userDTO.getName());
                confirmDetail.setSubmitTime(new Date());
            }
            confirmDetail.setReviewResult(manageStatusEnum.getCode());
            confirmDetail.setReviewBy(userDTO.getUserId());
            confirmDetail.setReviewName(userDTO.getName());
            confirmDetail.setReviewTime(new Date());
            // 设置审核信息
            confirmDetail.setGmtUpdate(new Date());
            confirmDetail.setUpdatedBy(userDTO.getUserId());
            confirmDetail.setUpdatedName(userDTO.getName());
            jymlStoreSkuConfirmDetailMapper.updateByPrimaryKeySelective(confirmDetail);
        }
    }

    private void batchUpdateConfirmDetailByNonJyGoodsUpdate(OrgInfoBaseCache orgInfoBaseCache, List<JymlStoreSkuExemptionRecord> records,TokenUserDTO userDTO,ManageStatusEnum manageStatusEnum){
        if (CollectionUtils.isEmpty(records)) {
            throw new BusinessErrorException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        }
        JymlStoreSkuSuggestProcess process = getProcess(orgInfoBaseCache.getSapCode());
        if(Objects.isNull(process)){
            logger.warn("豁免商品查询进度表异常 ");
            return;
        }
        // 查询确认明细
        JymlStoreSkuConfirmDetailExample example = new JymlStoreSkuConfirmDetailExample();
        example.createCriteria()
                .andStoreIdEqualTo(orgInfoBaseCache.getOutId())
                .andVersionEqualTo(process.getVersion())
                .andGoodsNoIn(records.stream().map(JymlStoreSkuExemptionRecord::getGoodsNo).distinct().collect(Collectors.toList()))
                .andStatusEqualTo(NORMAL_STATUS);
        List<JymlStoreSkuConfirmDetail> confirmDetails = jymlStoreSkuConfirmDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(confirmDetails)) {
            logger.info("confirmDetails is empty");
            return;
        }
        for (JymlStoreSkuConfirmDetail confirmDetail : confirmDetails) {
            ManageJymlCategoryLimitAndProcessDTO manageProcessDTO = new ManageJymlCategoryLimitAndProcessDTO();
            manageProcessDTO.setNeedConfirm(false);
            setConfirmAndAdjustEditAble(orgInfoBaseCache, manageProcessDTO);
            if (!manageProcessDTO.getNeedConfirm()){
                confirmDetail.setMyConfirm(manageStatusEnum.getCode());
                confirmDetail.setSubmitBy(userDTO.getUserId());
                confirmDetail.setSubmitName(userDTO.getName());
                confirmDetail.setSubmitTime(new Date());
            }
            confirmDetail.setReviewResult(manageStatusEnum.getCode());
            confirmDetail.setReviewBy(userDTO.getUserId());
            confirmDetail.setReviewName(userDTO.getName());
            confirmDetail.setReviewTime(new Date());
            // 设置审核信息
            confirmDetail.setGmtUpdate(new Date());
            confirmDetail.setUpdatedBy(userDTO.getUserId());
            confirmDetail.setUpdatedName(userDTO.getName());
//            jymlStoreSkuConfirmDetailMapper.updateByPrimaryKeySelective(confirmDetail);
        }
        Lists.partition(confirmDetails, FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE).forEach(v -> {
            jymlStoreSkuConfirmDetailExtendMapper.batchUpdateByPrimaryKeySelective(v);
        });
    }


    @Override
    public void updateDiffManageStatus(List<String> storeCodes) {
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(SYS_USER_ID);
        userDTO.setName(SYS_USER_NAME);
        userDTO.setUserName(SYS_USER_NAME);
        List<OrgInfoBaseCache> stores = CacheVar.getStoreByStoreCodeList(storeCodes).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(stores)) {
            throw new BusinessErrorException("所有门店均没查询到缓存,请联系管理员");
        }
        asyncTaskExecutor.execute(() -> {
            for (OrgInfoBaseCache store : stores) {
                try {
                    updateManageStatus(store, userDTO);
                } catch (Exception e) {
                    logger.error("门店:" + store.getSapCode() + "刷新失败", e);
                }
            }
        });
    }

    private void updateManageStatus(OrgInfoBaseCache store, TokenUserDTO userDTO) {
        if (null == store.getOutId()) {
            logger.info("门店:{}没有outid", store.getSapCode());
            return;
        }
        logger.info("门店:{}开始处理", store.getSapCode());
        StoreGoodsContentsExample example = new StoreGoodsContentsExample();
        example.createCriteria().andStoreIdEqualTo(store.getOutId()).andNecessaryTagEqualTo(0).andSuggestManageStatusIn(Lists.newArrayList(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode(),SuggestManageStatusEnum.SUGGEST_NON_MANAGE.getCode(),SuggestManageStatusEnum.MUST_NON_MANAGE.getCode()));
        Map<Integer, List<StoreGoodsContents>> contentsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.groupingBy(StoreGoodsContents::getSuggestManageStatus));
        if (MapUtils.isEmpty(contentsMap)) {
            logger.info("门店:{}没有需要修改的数据", store.getSapCode());
            return;
        }
        List<StoreGoodsContents> nonToChooseList = new ArrayList<>();
        List<StoreGoodsContents> chooseToNonList = new ArrayList<>();
        // 建议经营状态=3，经营状态=4的，更新成3； 建议经营状态=4和5，经营状态=3的，更新成4。这样严格一些
        contentsMap.forEach((k,v) -> {
            if (SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode().equals(k)) {
                nonToChooseList.addAll(v.stream().filter(goods -> ManageStatusEnum.NON_MANAGE.getCode().equals(goods.getManageStatus())).collect(Collectors.toList()));
            } else {
                chooseToNonList.addAll(v.stream().filter(goods -> ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(goods.getManageStatus())).collect(Collectors.toList()));
            }
        });
        logger.info("nonToChooseList.size:{}", nonToChooseList.size());
        logger.info("chooseToNonList.size:{}", chooseToNonList.size());

        if (CollectionUtils.isEmpty(nonToChooseList) && CollectionUtils.isEmpty(chooseToNonList)) {
            logger.info("门店:{}没有匹配到需要修改的数据", store.getSapCode());
            return;
        }
        if (CollectionUtils.isNotEmpty(nonToChooseList)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(nonToChooseList.size());
            mdmTaskMapper.insertSelective(task);
            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
            assemble.work(buildStoreGoodsContentDTOByContents(store, nonToChooseList, StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task));
        }
        if (CollectionUtils.isNotEmpty(chooseToNonList)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(chooseToNonList.size());
            mdmTaskMapper.insertSelective(task);
            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NON_MANAGE.getCode());
            assemble.work(buildStoreGoodsContentDTOByContents(store, chooseToNonList, StoreContentBizTypeEnum.NON_MANAGE.getCode(), userDTO, task));
        }
        logger.info("门店:{}刷新完毕", store.getSapCode());
    }

    @Override
    public void importUpdateManageStatus(MultipartFile file) {
        try (InputStream in = file.getInputStream()) {
            TokenUserDTO userDTO = new TokenUserDTO();
            userDTO.setUserId(SYS_USER_ID);
            userDTO.setName(SYS_USER_NAME);
            userDTO.setUserName(SYS_USER_NAME);
            checkFile(file);
            String s = ExcelCheck.checkExcelPattern(file, new ImportManageStatusData(), CollNameList);
            if (StringUtils.isNotEmpty(s)) {
                throw new AmisBadRequestException(s);
            }
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            List<ImportManageStatusData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(ImportManageStatusData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            if (CollectionUtils.isEmpty(excelList)) {
                throw new BusinessErrorException("没有需要导入的数据");
            }
            List<String> storeCodes = excelList.stream().map(ImportManageStatusData::getStoreCode).filter(StringUtils::isNotBlank).map(StringUtils::trim).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeCodes)) {
                throw new BusinessErrorException("门店编码全为空");
            }
            Map<String, OrgInfoBaseCache> storeMap = CacheVar.getStoreByStoreCodeList(storeCodes).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OrgInfoBaseCache::getSapCode, Function.identity(), (k1,k2) -> k1));
            if (MapUtils.isEmpty(storeMap)) {
                throw new BusinessErrorException("所有门店均没查询到缓存,请联系管理员");
            }

            Iterator<ImportManageStatusData> iterator = excelList.iterator();
            while (iterator.hasNext()) {
                ImportManageStatusData next = iterator.next();
                if (StringUtils.isBlank(next.getStoreCode()) || StringUtils.isBlank(next.getGoodsNo()) || null == next.getManageStatus()) {
                    logger.info("有空值");
                    iterator.remove();
                    continue;
                }
                if (!storeMap.containsKey(next.getStoreCode())) {
                    logger.info("门店编码:{}不存在", next.getStoreCode());
                    iterator.remove();
                    continue;
                }
                if (!(ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(next.getManageStatus()) || ManageStatusEnum.NON_MANAGE.getCode().equals(next.getManageStatus()))) {
                    logger.info("目标状态:{}只允许修改3,4", next.getManageStatus());
                    iterator.remove();
                }
            }
            if (CollectionUtils.isEmpty(excelList)) {
                throw new BusinessErrorException("处理后的导入数据为空");
            }
            asyncTaskExecutor.execute(() -> {
                excelList.stream().collect(Collectors.groupingBy(ImportManageStatusData::getStoreCode)).forEach((storeCode, list) -> {
                    try{
                        updateImport(storeCode, list, storeMap, userDTO);
                    } catch (Exception e) {
                        logger.error("门店:" + storeCode + "处理失败", e);
                    }
                });
            });
        } catch (Exception e) {
            logger.error("导入失败", e);
            throw new BusinessErrorException(e.getMessage());
        }
    }

    @Override
    public PageResult<JymlSkuMaxLimitConfigureDTO> getSkuLimitList(ManageJymlSkuMaxLimitQueryParam queryParam, TokenUserDTO userDTO) {
        PageResult<JymlSkuMaxLimitConfigureDTO> pageResult = new PageResult<>(0L, new ArrayList<JymlSkuMaxLimitConfigureDTO>());
        try {
            if (null == queryParam.getVersion()) {
                throw new BusinessErrorException("请输入查询版本号");
            }
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), null == queryParam.getPlatformOrgId() ? 3L : queryParam.getPlatformOrgId(), Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTOS)) {
                logger.info("userId:{}, userName:{}没有企业级权限", userDTO.getUserId(), userDTO.getName());
                return pageResult;
            }
            List<OrgTreeSimpleDTO> companys = orgTreeSimpleDTOS.get(0).getChildren();
            if (CollectionUtils.isEmpty(companys)) {
                logger.info("userId:{}, userName:{}企业级权限为空", userDTO.getUserId(), userDTO.getName());
                return pageResult;
            }
            Map<Long, OrgTreeSimpleDTO> companyMap = companys.stream().collect(Collectors.toMap(OrgTreeSimpleDTO::getId, Function.identity(), (k1, k2) -> k1));
            JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
            JymlSkuMaxLimitConfigureExample.Criteria criteria = example.createCriteria();
            criteria.andVersionEqualTo(queryParam.getVersion());
            if (null != queryParam.getPlatformOrgId()) {
                criteria.andPlatformOrgIdEqualTo(queryParam.getPlatformOrgId());
            }
            if (null != queryParam.getBusinessOrgId()) {
                if (!companyMap.containsKey(queryParam.getBusinessOrgId())) {
                    throw new BusinessErrorException("您没有该企业的查询权限");
                }
                criteria.andBusinessOrgIdEqualTo(queryParam.getBusinessOrgId());
            } else {
                criteria.andBusinessOrgIdIn(Lists.newArrayList(companyMap.keySet()));
            }
            if (StringUtils.isNotBlank(queryParam.getCity())) {
                criteria.andCityIn(Arrays.stream(queryParam.getCity().split(",")).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(queryParam.getCategory())) {
                criteria.andCategoryEqualTo(queryParam.getCategory());
            }
            if (StringUtils.isNotBlank(queryParam.getMiddleCategory())) {
                criteria.andMiddleCategoryEqualTo(queryParam.getMiddleCategory());
            }
            if (StringUtils.isNotBlank(queryParam.getSmallCategory())) {
                criteria.andSmallCategoryEqualTo(queryParam.getSmallCategory());
            }
            if (StringUtils.isNotBlank(queryParam.getSubCategory())) {
                criteria.andSubCategoryIn(Arrays.stream(queryParam.getSubCategory().split(",")).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(queryParam.getStoreType())) {
                criteria.andStoreTypeIn(Arrays.stream(queryParam.getStoreType().split(",")).collect(Collectors.toList()));
            }
            long count = jymlSkuMaxLimitConfigureMapper.countByExample(example);
            pageResult.setTotal(count);
            if (count <= 0L) {
                return pageResult;
            }
            example.setLimit(queryParam.getPerPage());
            example.setOffset(Long.valueOf(queryParam.getPage() - 1) * queryParam.getPerPage());
            pageResult.setRows(jymlSkuMaxLimitConfigureMapper.selectByExample(example).stream().map(v -> {
                JymlSkuMaxLimitConfigureDTO dto = new JymlSkuMaxLimitConfigureDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                dto.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                dto.setVersionStr(v.getVersion().toString());
                return dto;
            }).collect(Collectors.toList()));
            return pageResult;
        } catch (Exception e) {
            logger.error("查询SKU数配置管理失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public PageResult<JymlSkuLimit> getJtJymlLimitList(JtQueryParam queryParam, TokenUserDTO userDTO) {
        try {
            PageResult<JymlSkuLimit> pageResult = new PageResult<>(0L, new ArrayList<JymlSkuLimit>());
//            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), null == queryParam.getPlatformOrgId() ? 3L : queryParam.getPlatformOrgId(), Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
//            if (CollectionUtils.isEmpty(orgTreeSimpleDTOS)) {
//                logger.info("userId:{}, userName:{}没有企业级权限", userDTO.getUserId(), userDTO.getName());
//                return pageResult;
//            }
//            List<OrgTreeSimpleDTO> companys = orgTreeSimpleDTOS.get(0).getChildren();
//            if (CollectionUtils.isEmpty(companys)) {
//                logger.info("userId:{}, userName:{}企业级权限为空", userDTO.getUserId(), userDTO.getName());
//                return pageResult;
//            }
//            Map<Long, OrgTreeSimpleDTO> companyMap = companys.stream().collect(Collectors.toMap(OrgTreeSimpleDTO::getId, Function.identity(), (k1, k2) -> k1));
            JymlSkuLimitExample example = new JymlSkuLimitExample();
            JymlSkuLimitExample.Criteria criteria = example.createCriteria();
            if (null != queryParam.getPlatformOrgId()) {
                criteria.andPlatformOrgIdEqualTo(queryParam.getPlatformOrgId());
            }
//            if (null != queryParam.getCompanyOrgId()) {
//                if (!companyMap.containsKey(queryParam.getCompanyOrgId())) {
//                    throw new BusinessErrorException("您没有该企业的查询权限");
//                }
//                criteria.andCompanyOrgIdEqualTo(queryParam.getCompanyOrgId());
//            } else {
//                criteria.andCompanyOrgIdIn(Lists.newArrayList(companyMap.keySet()));
//            }
            if (null != queryParam.getCompanyOrgId()) {
                criteria.andCompanyOrgIdEqualTo(queryParam.getCompanyOrgId());
            }
            if (StringUtils.isNotBlank(queryParam.getCity())) {
                criteria.andCityIn(Arrays.stream(queryParam.getCity().split(",")).collect(Collectors.toList()));
            }

            long count = jymlSkuLimitMapper.countByExample(example);
            pageResult.setTotal(count);
            if (count <= 0L) {
                return pageResult;
            }
            example.setLimit(queryParam.getPerPage());
            example.setOffset(Long.valueOf(queryParam.getPage() - 1) * queryParam.getPerPage());
            pageResult.setRows(jymlSkuLimitMapper.selectByExample(example));
            return pageResult;
        } catch (Exception e) {
            logger.error("查询集团标准列表失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public ImportResult importNonJyGoods(MultipartFile file, TokenUserDTO userDTO) throws Exception {
        String key = IMPORT_NONJY_GOODS + userDTO.getUserId();
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new AmisBadRequestException("您当前导入任务正在进行中,请稍后重试。");
        }
        checkFile(file);
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new NonJyGoodsImportData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new AmisBadRequestException(s);
        }
        result.setResult(key);
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result, 12, TimeUnit.HOURS);

        try (InputStream in = file.getInputStream()) {
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(nonjyImportNum);
            List<NonJyGoodsImportData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(NonJyGoodsImportData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            if (CollectionUtils.isEmpty(excelList)) {
                throw new AmisBadRequestException("导入文件为空");
            }
            List<String> storeCodes = excelList.stream().map(NonJyGoodsImportData::getStoreCode).filter(v -> StringUtils.isNotBlank(v)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeCodes)) {
                throw new AmisBadRequestException("门店编码均为空");
            }
            asyncTaskExecutor.execute(() -> {
                dealImportData(excelList, result, key, rBucket, userDTO, storeCodes);
            });

        }  catch (Exception e) {
            result.setCode("1");
            result.setMessage("批量导入二轮选配商品失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("批量导入二轮选配商品失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }

    @Override
    public PageResult<JymlStoreIncreaseLimitConfigureDTO> getMaintenanceNonJyGoods(JymlSkyExemptionParam param, TokenUserDTO userDTO, List<Long> scopeCompanyOrgIds) {
        try {
            PageResult<JymlStoreIncreaseLimitConfigureDTO> pageResult = new PageResult<>(0L, new ArrayList<>());
            JymlStoreIncreaseLimitConfigureExample example = new JymlStoreIncreaseLimitConfigureExample();
            JymlStoreIncreaseLimitConfigureExample.Criteria criteria = example.createCriteria();
            List<OrgTreeSimpleDTO> userDataScopeStore = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), 3L, Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            OrgTreeSimpleDTO orgTreeSimpleDTO = userDataScopeStore.stream().findFirst().orElseThrow(() -> new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置"));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTO.getChildren())) {
                throw new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置");
            }
            if (CollectionUtils.isEmpty(scopeCompanyOrgIds)) {
                scopeCompanyOrgIds = orgTreeSimpleDTO.getChildren().stream().map(OrgTreeSimpleDTO::getId).distinct().collect(Collectors.toList());
            }
            if (null != param.getBusinessOrgId()) {
                if (!scopeCompanyOrgIds.contains(param.getBusinessOrgId())) {
                    throw new AmisBadRequestException("您没有所选项目公司数据权限,请联系管理员配置");
                }
                criteria.andBusinessOrgIdEqualTo(param.getBusinessOrgId());
            } else {
                criteria.andBusinessOrgIdIn(scopeCompanyOrgIds);
            }
            if (StringUtils.isNotBlank(param.getStoreIds())) {
                criteria.andStoreIdIn(Arrays.stream(StringUtils.split(param.getStoreIds(), ",")).map(String::trim).map(Long::valueOf).distinct().collect(Collectors.toList()));
            }
            long count = jymlStoreIncreaseLimitConfigureMapper.countByExample(example);
            if (count < 0L) {
                return pageResult;
            }
            pageResult.setTotal(count);
            example.setLimit(param.getPerPage());
            example.setOffset(Long.valueOf(param.getPage() - 1) * param.getPerPage());
            List<JymlStoreIncreaseLimitConfigure> jymlStoreIncreaseLimitConfigures = jymlStoreIncreaseLimitConfigureMapper.selectByExample(example);
            List<String> storeCodes = jymlStoreIncreaseLimitConfigures.stream().map(JymlStoreIncreaseLimitConfigure::getStoreCode).distinct().collect(Collectors.toList());
            // > 1000直接查所有门店的商品数
            Map<String, Integer> goodsCountMap = jymlStoreSkuExemptionRecordExtendMapper.getGoodsCountByStore(storeCodes.size() > INSERT_MAX_VALUE ? null : storeCodes).stream().collect(Collectors.toMap(StoreGoodsCountDTO::getStoreCode, StoreGoodsCountDTO::getGoodsCount, (k1,k2) -> k1));
            List<JymlStoreIncreaseLimitConfigureDTO> configureDTOS = jymlStoreIncreaseLimitConfigures.stream().map(v -> {
                JymlStoreIncreaseLimitConfigureDTO dto = new JymlStoreIncreaseLimitConfigureDTO();
                BeanUtils.copyProperties(v, dto);
                Integer goodsCount = Optional.ofNullable(goodsCountMap.get(v.getStoreCode())).orElse(0);
                dto.setGoodsCount(goodsCount);
                return dto;
            }).collect(Collectors.toList());
            pageResult.setRows(configureDTOS);
            return pageResult;
        } catch (Exception e) {
            logger.error("获取二轮选配商品批量维护失败", e);
            throw e;
        }
    }

    @Override
    public void exportMaintenanceNonJyGoods(TokenUserDTO userDTO, JymlSkyExemptionParam param) {
        try {
            param.setToPage(false);
            List<OrgTreeSimpleDTO> userDataScopeStore = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), 3L, Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            OrgTreeSimpleDTO orgTreeSimpleDTO = userDataScopeStore.stream().findFirst().orElseThrow(() -> new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置"));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTO.getChildren())) {
                throw new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置");
            }
            List<Long> scopeCompanyOrgIds = orgTreeSimpleDTO.getChildren().stream().map(OrgTreeSimpleDTO::getId).distinct().collect(Collectors.toList());
            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.SECOND_CHOOSE, userDTO, new HandlerDataExportService<JymlStoreIncreaseLimitConfigureDTO>() {
                @Override
                public List<JymlStoreIncreaseLimitConfigureDTO> getDataToExport() {
                    return null;
                }

                @Override
                public List<JymlStoreIncreaseLimitConfigureDTO> getDataToExport(Integer page, Integer pageSize) {
                    param.setPage(page + 1);
                    param.setPerPage(pageSize);
                    return getMaintenanceNonJyGoods(param, userDTO, scopeCompanyOrgIds).getRows();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }
                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return JymlStoreIncreaseLimitConfigureDTO.getExportMap();
                }
            });

        } catch (Exception e) {
            logger.error("获取二轮选配商品批量维护失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    private void dealImportData(List<NonJyGoodsImportData> excelList, ImportResult result, String key, RBucket<ImportResult> rBucket, TokenUserDTO userDTO, List<String> storeCodes) {
        try {
            Date now = new Date();
            String fileName = "批量导入二轮选配商品_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            List<Long> userScopeCompanyOrgIds = new ArrayList<>();
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), 3L, Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            if (CollectionUtils.isNotEmpty(orgTreeSimpleDTOS) && CollectionUtils.isNotEmpty(orgTreeSimpleDTOS.get(0).getChildren())) {
                userScopeCompanyOrgIds.addAll(orgTreeSimpleDTOS.get(0).getChildren().stream().map(OrgTreeSimpleDTO::getId).collect(Collectors.toList()));
            }
            List<String> goodsNos = excelList.stream().map(NonJyGoodsImportData::getGoodsNo).filter(Objects::nonNull).map(String::trim).distinct().collect(Collectors.toList());
            Map<String, SpuListVo> spuVOMap = new HashMap<>();
            Map<Long, CommonCategoryDTO> categoryDTOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(goodsNos)) {
                spuVOMap.putAll(searchService.getSpuVOMap(goodsNos));
                List<Long> subCategoryIds = spuVOMap.values().stream().map(SpuListVo::getCategoryId).distinct().map(Long::valueOf).collect(Collectors.toList());
                Lists.partition(subCategoryIds, FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE).forEach(v -> {
                    categoryDTOMap.putAll(getCategoryBySubIds(v));
                });
            }
            // 查询门店的生效时间
            Map<String, JymlStoreSkuSuggestProcess> storeProcessTimeMap = new HashMap<>();
            Lists.partition(storeCodes, EXPORT_ONCE_QUERY_MAX).forEach(v -> {
                JymlStoreSkuSuggestProcessExample processExample = new JymlStoreSkuSuggestProcessExample();
                processExample.createCriteria().andStoreCodeIn(v).andStatusEqualTo(NORMAL_STATUS);
                storeProcessTimeMap.putAll(jymlStoreSkuSuggestProcessMapper.selectByExample(processExample).stream().collect(Collectors.toMap(JymlStoreSkuSuggestProcess::getStoreCode, Function.identity(), (k1,k2) -> k1)));
            });
            JymlStoreIncreaseLimitConfigureExample limitConfigureExample = new JymlStoreIncreaseLimitConfigureExample();
            limitConfigureExample.createCriteria().andStoreCodeIn(storeCodes);
            Map<String, Integer> increaseLimitMap = jymlStoreIncreaseLimitConfigureMapper.selectByExample(limitConfigureExample).stream().collect(Collectors.toMap(JymlStoreIncreaseLimitConfigure::getStoreCode, JymlStoreIncreaseLimitConfigure::getIncreaseLimit, (k1, k2) -> k1));
            Map<String, Integer> storeGoodsCount = jymlStoreSkuExemptionRecordExtendMapper.getGoodsCountByStore(storeCodes).stream().collect(Collectors.toMap(StoreGoodsCountDTO::getStoreCode, StoreGoodsCountDTO::getGoodsCount, (k1,k2) -> k1));
            Map<String, List<StoreGoodsDTO>> storeGoodsMap = jymlStoreSkuExemptionRecordExtendMapper.getGoodsByStore(storeCodes).stream().collect(Collectors.groupingBy(StoreGoodsDTO::getStoreCode));
            jymlStoreSkuExemptionRecordExtendMapper.getGoodsCountByStore(storeCodes);
            List<NonJyGoodsImportData> errorData = new ArrayList<>();
            Iterator<NonJyGoodsImportData> iterator = excelList.iterator();
            List<JymlStoreSkuExemptionRecord> insertList = new ArrayList<>();
            Set<String> storeGoodsSet = new HashSet<>();
            while (iterator.hasNext()) {
                NonJyGoodsImportData next = iterator.next();
                Optional<OrgInfoBaseCache> storeOpt = CacheVar.getStoreBySapCode(next.getStoreCode());
                if (!storeOpt.isPresent()) {
                    next.setErrorReason("门店编码不存在");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                OrgInfoBaseCache store = storeOpt.get();
                if (!userScopeCompanyOrgIds.contains(store.getBusinessOrgId())) {
                    next.setErrorReason("没有该门店的操作权限");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                JymlStoreSkuSuggestProcess process = storeProcessTimeMap.get(next.getStoreCode());
                if (null == process) {
                    next.setErrorReason("该门店未在门店选配调整期内");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (null == process.getBeginProcessTime() || null == process.getEndProcessTime()) {
                    next.setErrorReason("该门店未在门店选配调整期内");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (!(now.after(process.getBeginProcessTime()) && now.before(process.getEndProcessTime()))) {
                    next.setErrorReason("该门店未在门店选配调整期内（" + DateUtils.conventDateStrByDate(process.getScheduledProcessTime(), DateUtils.DATE_PATTERN)+ "号0点-" + DateUtils.conventDateStrByDate(process.getEndProcessTime()) + "号24点）");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getGoodsNo())) {
                    next.setErrorReason("商品编码为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                SpuListVo spuListVo = spuVOMap.get(next.getGoodsNo());
                if (null == spuListVo) {
                    next.setErrorReason("商品不存在");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                int perSize = storeGoodsSet.size();
                storeGoodsSet.add(next.getStoreCode() + next.getGoodsNo());
                if (perSize == storeGoodsSet.size()) {
                    next.setErrorReason( "该行重复");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                Integer countLimit = Optional.ofNullable(increaseLimitMap.get(next.getStoreCode())).orElse(0);
                if (countLimit == 0) {
                    next.setErrorReason("导入后超过门店二轮选配商品个数上限，不允许。");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                Integer storeCount = Optional.ofNullable(storeGoodsCount.get(next.getStoreCode())).orElse(0) + 1;
                if (countLimit < storeCount) {
                    next.setErrorReason("导入后超过门店二轮选配商品个数上限，不允许。");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                List<StoreGoodsDTO> storeGoodsDTOS = storeGoodsMap.get(next.getStoreCode());
                if (CollectionUtils.isNotEmpty(storeGoodsDTOS) && storeGoodsDTOS.stream().filter(v -> v.getGoodsNo().equals(next.getGoodsNo())).findAny().isPresent()) {
                    next.setErrorReason("商品已存在,不能重复导入。");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                storeGoodsCount.put(next.getStoreCode(), storeCount);
                insertList.add(genExemptionRecord(spuListVo, store, categoryDTOMap, userDTO));
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                Lists.partition(insertList, EXPORT_ONCE_QUERY_MAX).forEach(v -> {
                    jymlStoreSkuExemptionRecordExtendMapper.batchInsert(v);
                });
            }
            Map<String, List<NonJyGoodsImportData>> importMap = excelList.stream().collect(Collectors.groupingBy(NonJyGoodsImportData::getStoreCode));
            if (CollectionUtils.isNotEmpty(insertList)) {
            insertList.stream().collect(Collectors.groupingBy(JymlStoreSkuExemptionRecord::getStoreCode)).forEach((k,v) -> {
                List<String> goodsNoList = v.stream().map(JymlStoreSkuExemptionRecord::getGoodsNo).distinct().collect(Collectors.toList());
                try {
                    MdmTask task = new MdmTask();
                    task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
                    BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                    task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                    task.setDetailCount(v.size());
                    mdmTaskMapper.insertSelective(task);
                    StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
                    List<ManageCommonDTO> chooseList = v.stream().map(record -> {
                        ManageCommonDTO manageCommonDTO = new ManageCommonDTO();
                        BeanUtils.copyProperties(record, manageCommonDTO);
                        manageCommonDTO.setManageStatus(String.valueOf(ManageStatusEnum.MANAGE_CHOOSE.getCode()));
                        manageCommonDTO.setSuggestManageStatus(String.valueOf(SuggestManageStatusEnum.SUGGEST_NON_MANAGE.getCode()));
                        return manageCommonDTO;
                    }).collect(Collectors.toList());
                    OrgInfoBaseCache orgInfoBaseCache = CacheVar.getStoreBySapCode(k).get();
                    List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(orgInfoBaseCache, chooseList, StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task);
                    assemble.work(storeGoodsContentDTOS);
                    batchUpdateConfirmDetailByNonJyGoodsUpdate(orgInfoBaseCache, v, userDTO, ManageStatusEnum.MANAGE_CHOOSE);
                } catch (Exception e) {
                    logger.error("门店:" + k + "处理门店二轮选配商品失败", e);
                    try {
                        JymlStoreSkuExemptionRecordExample delExample = new JymlStoreSkuExemptionRecordExample();
                        delExample.createCriteria().andStoreCodeEqualTo(k).andGoodsNoIn(goodsNoList);
                        jymlStoreSkuExemptionRecordMapper.deleteByExample(delExample);
                    } catch (Exception e1) {
                        logger.error("门店:" + k + "删除二轮选配失败商品失败", e1);
                    }
                    errorData.addAll(importMap.get(k).stream().map(im -> {
                        im.setErrorReason(e.getMessage());
                        return im;
                    }).collect(Collectors.toList()));
                }
                });
            }
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, NonJyGoodsImportData.class).sheet("sheet1").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("店型级必备明细错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        } catch (Exception e) {
            logger.error("数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }

    private JymlStoreSkuExemptionRecord genExemptionRecord(SpuListVo spuNewVo, OrgInfoBaseCache orgInfoBaseCache, Map<Long, CommonCategoryDTO> categoryMap, TokenUserDTO userDTO) {
        JymlStoreSkuExemptionRecord record = new JymlStoreSkuExemptionRecord();
        record.setSource(ExemptionSourceEnum.IMPORT.getCode());
        record.setGoodsNo(spuNewVo.getGoodsNo());
        record.setGoodsName(spuNewVo.getName());
        record.setJhispecification(spuNewVo.getJhiSpecification());
        record.setFactoryid(spuNewVo.getFactoryid());
        record.setGoodsunit(spuNewVo.getGoodsunit());
        record.setComponent(null == spuNewVo.getComponent() ? "" : spuNewVo.getComponent());
        CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(spuNewVo.getCategoryId()));
        record.setSmallCategory(null != commonCategoryDTO ? commonCategoryDTO.getSmallCategoryId().toString() : "");
        record.setSmallCategoryName(null != commonCategoryDTO ? commonCategoryDTO.getSmallCategoryName():"");
        record.setSubCategory(null != commonCategoryDTO ?commonCategoryDTO.getSubCategoryId().toString():"");
        record.setSubCategoryName(null != commonCategoryDTO ?commonCategoryDTO.getSubCategoryName():"");
        record.setBusinessOrgId(orgInfoBaseCache.getBusinessOrgId());
        record.setStoreId(orgInfoBaseCache.getOutId());
        record.setStoreCode(orgInfoBaseCache.getSapCode());
        record.setCreatedBy(userDTO.getUserId());
        record.setCreatedName(userDTO.getName());
        record.setUpdatedBy(userDTO.getUserId());
        record.setUpdatedName(userDTO.getName());
        return record;
    }

    private String uplodaErrorData(String fileName, String filePath) {
        File errorFile = new File(filePath);
        String key = fileName;
        String presignatureUrl = "";
        try {
            String url = cosService.multipartUploadFile(key, errorFile);
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, +7);
            presignatureUrl = cosService.getPresignatureUrl(key, calendar.getTime());
        } catch (Exception e) {
            logger.error("分片上传文件失败", e);
        }
        return presignatureUrl;
    }
    private void delTempFile(String filePath) {
        Path path = Paths.get(filePath);
        try {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
                    Files.delete(file);
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException ioException) {
            logger.error("删除文件异常", ioException);
            ioException.printStackTrace();
        }
    }

    private void toRedis(RBucket<ImportResult> rBucket, int size,
                         ImportResult result, String key, String fileFileUrl) {
        if (size > 0) {
            result.setCode("1");
            result.setMessage("上传数据已处理完成，存在" + size + "行无效数据，请下载查看。");
            result.setResult(key);
            result.setFailFileUrl(fileFileUrl);
            rBucket.set(result, 12, TimeUnit.HOURS);
        } else {
            result.setCode("0");
            result.setMessage("上传数据全部处理成功。");
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }

    private ImportResult getOperationPermissions(String key) {
        RBucket<ImportResult> rBucket1 = redissonClient.getBucket(key);
        if (rBucket1.isExists()) {
            ImportResult importResult = rBucket1.get();
            if (Objects.nonNull(importResult) &&importResult.getCode().equals("9999")){
                return importResult;
            }
        }
        return null;
    }


    @Override
    public void exportSkuLimitList(TokenUserDTO userDTO, ManageJymlSkuMaxLimitQueryParam param) {
        try {
            param.setToPage(false);
            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.JYML_SKU_MAX_LIMIT, userDTO, new HandlerDataExportService<JymlSkuMaxLimitConfigureDTO>() {
                @Override
                public List<JymlSkuMaxLimitConfigureDTO> getDataToExport() {
                    return null;
                }

                @Override
                public List<JymlSkuMaxLimitConfigureDTO> getDataToExport(Integer page, Integer pageSize) {
                    param.setPage(page + 1);
                    param.setPerPage(pageSize);
                    return getSkuLimitList(param, userDTO).getRows();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }
                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return JymlSkuMaxLimitConfigureDTO.getExportMap();
                }
            });

        } catch (Exception e) {
            logger.error("导出SKU数配置管理异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }

    }

    private void updateImport(String storeCode, List<ImportManageStatusData> list, Map<String, OrgInfoBaseCache> storeMap, TokenUserDTO userDTO) {
        logger.info("门店:{}开始处理", storeCode);
        OrgInfoBaseCache store = storeMap.get(storeCode);
        List<String> goodsNos = list.stream().map(ImportManageStatusData::getGoodsNo).map(String::trim).collect(Collectors.toList());
        StoreGoodsContentsExample example = new StoreGoodsContentsExample();
        example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(goodsNos).andNecessaryTagEqualTo(0).andManageStatusIn(Lists.newArrayList(ManageStatusEnum.MANAGE_CHOOSE.getCode(), ManageStatusEnum.NON_MANAGE.getCode()));
        Map<String, StoreGoodsContents> contentsMap = storeGoodsContentsMapper.selectByExample(example).stream().collect(Collectors.toMap(StoreGoodsContents::getGoodsNo, Function.identity(), (k1,k2) -> k1));
        if (MapUtils.isEmpty(contentsMap)) {
            logger.info("门店编码:{}没有需要修改的数据", storeCode);
            return;
        }
        List<StoreGoodsContents> nonToChooseList = new ArrayList<>();
        List<StoreGoodsContents> chooseToNonList = new ArrayList<>();
        list.forEach(l -> {
            StoreGoodsContents contents = contentsMap.get(l.getGoodsNo().trim());
            if (null == contents) {
                logger.info("门店编码:{}商品:{}不在一店一目中", storeCode, l.getGoodsNo());
                return;
            }
            if (contents.getManageStatus().equals(l.getManageStatus())) {
                logger.info("门店编码:{}商品:{}经营状态相同", storeCode, l.getGoodsNo());
                return;
            }
            contents.setManageStatus(l.getManageStatus());
            boolean b = ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(l.getManageStatus()) ? nonToChooseList.add(contents) : chooseToNonList.add(contents);
        });
        if (CollectionUtils.isEmpty(nonToChooseList) && CollectionUtils.isEmpty(chooseToNonList)) {
            logger.info("门店:{}没有匹配到需要修改的数据", store.getSapCode());
            return;
        }
        if (CollectionUtils.isNotEmpty(nonToChooseList)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(nonToChooseList.size());
            mdmTaskMapper.insertSelective(task);
            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
            assemble.work(buildStoreGoodsContentDTOByContents(store, nonToChooseList, StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task));
        }
        if (CollectionUtils.isNotEmpty(chooseToNonList)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(chooseToNonList.size());
            mdmTaskMapper.insertSelective(task);
            StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NON_MANAGE.getCode());
            assemble.work(buildStoreGoodsContentDTOByContents(store, chooseToNonList, StoreContentBizTypeEnum.NON_MANAGE.getCode(), userDTO, task));
        }
        logger.info("门店:{}处理完毕", storeCode);
    }

    private void checkFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }
        //获取文件名+后缀
        String filename = file.getOriginalFilename();
        if (filename != null) {
            //获取其后缀
            String extension = filename.substring(filename.lastIndexOf(".") + 1);
            if (!(extension.equals("xls") || extension.equals("xlsx"))) {
                //此处为自定义异常捕获,可使用其他方式
                throw new BusinessErrorException("文件格式有误,请检查上传文件格式!!");
            }
        }
    }

    public List<JymlStoreSkuSuggestProcessDetail> summarizeProcessCategory( OrgInfoBaseCache storeInfo ,List<JymlStoreSkuSuggest> jymlStoreSkuSuggests, Map<String, SpuListVo> newSpuMap) {
        // 获取门店的配置列表
        List<JymlSkuMaxLimitConfigure> allConfigListByStoreNo = getAllConfigListByStoreNo(jymlStoreSkuSuggests.get(0).getStoreCode());
        // 存储每个分类组合的最大限制
        Map<String, Integer> categoryMaxLimitMap = new HashMap<>();
        // 存储每个分类组合的最大限制
        Map<String, Integer> categoryMinLimitMap = new HashMap<>();
        // 存储每个分类在营商品计数    建议Status 不等于新品的其他全部 计数
        Map<String, Integer> categoryCountMap = new HashMap<>();
        // 存储每个分类的 在营商品列表
        Map<String, List<String>> categoryGoodsMap = new HashMap<>();
        // 初始化分类组合的最大限制
        for (JymlSkuMaxLimitConfigure config : allConfigListByStoreNo) {
            String key = ManageDataHelper.getCategoryKeyByConfig(config);
           // logger.info("summarizeProcessCategory|categoryMaxLimitMap|key:{},value:{}", key, config.getSkuMaxLimit());
            categoryMaxLimitMap.put(key, Objects.nonNull(config.getSkuMaxLimit())? config.getSkuMaxLimit():9999);
            categoryMinLimitMap.put(key, Objects.nonNull(config.getSkuLowerLimit())? config.getSkuLowerLimit():0);
        }
        // 遍历 jymlStoreSkuSuggests 进行建议计数
        for (JymlStoreSkuSuggest suggest : jymlStoreSkuSuggests) {
            if (StringUtils.isBlank(suggest.getGoodsNo()) || !newSpuMap.containsKey(suggest.getGoodsNo()) || SuggestManageStatusEnum.STORE_NEW_GOODS.getCode().equals(Integer.parseInt(suggest.getSuggestManageStatus()))){
                continue;
            }
            String fullKey =  ManageDataHelper.getCategoryKey(suggest.getCategory(), suggest.getMiddleCategory(), suggest.getSmallCategory(), suggest.getSubCategory());
            String middleKey =  ManageDataHelper.getCategoryKey(suggest.getCategory(), suggest.getMiddleCategory(), suggest.getSmallCategory(), "");
            String largeMiddleKey = ManageDataHelper.getCategoryKey(suggest.getCategory(), suggest.getMiddleCategory(), "", "");
            String largeKey =  ManageDataHelper.getCategoryKey(suggest.getCategory(), "", "", "");
            if (categoryMaxLimitMap.containsKey(fullKey)) {
                categoryCountMap.put(fullKey, categoryCountMap.getOrDefault(fullKey, 0) + 1);
                categoryGoodsMap.computeIfAbsent(fullKey, k -> new ArrayList<>()).add(suggest.getGoodsNo());
            } else if (categoryMaxLimitMap.containsKey(middleKey)) {
                categoryCountMap.put(middleKey, categoryCountMap.getOrDefault(middleKey, 0) + 1);
                categoryGoodsMap.computeIfAbsent(middleKey, k -> new ArrayList<>()).add(suggest.getGoodsNo());
            } else if (categoryMaxLimitMap.containsKey(largeMiddleKey)) {
                categoryCountMap.put(largeMiddleKey, categoryCountMap.getOrDefault(largeMiddleKey, 0) + 1);
                categoryGoodsMap.computeIfAbsent(largeMiddleKey, k -> new ArrayList<>()).add(suggest.getGoodsNo());
            } else if (categoryMaxLimitMap.containsKey(largeKey)) {
                categoryCountMap.put(largeKey, categoryCountMap.getOrDefault(largeKey, 0) + 1);
                categoryGoodsMap.computeIfAbsent(largeKey, k -> new ArrayList<>()).add(suggest.getGoodsNo());
            }
        }

        // 存储汇总结果的列表
        List<JymlStoreSkuSuggestProcessDetail> result = new ArrayList<>();
        JymlStoreSkuSuggest firstSuggest = jymlStoreSkuSuggests.get(0);
        boolean contains = selectMdmStoreIdFilterSelector(storeInfo);
        // 处理每个配置记录
        for (JymlSkuMaxLimitConfigure config : allConfigListByStoreNo) {
            String key =  ManageDataHelper.getCategoryKeyByConfig(config);
            //在营(非新品)sku记录计数
            int zySkuCount = categoryCountMap.getOrDefault(key, 0);
            if (zySkuCount<500){
                logger.info("分类={}, zySkuCount={} goodsNo={}",key,zySkuCount,categoryGoodsMap.get(key));
            }
            if(zySkuCount==0){
                logger.warn("summarizeProcessData|指定分类={} 商品的计数为0，跳过", key);
                continue;
            }
            Integer upperLimit = INTEGER_ZERO;
            // 判断当前是否在经营目录管控范围内
            int skuMaxLimit = contains? categoryMaxLimitMap.get(key):9999;
            int skuMinLimit = contains? categoryMinLimitMap.get(key):0;
            // 判断当前在营是否超上限
            // 3.每个门店每种管控类别都进入进度表，是否确认完成默认为否。其中符合以下条件的数据标记“需店长确认”：门店*管理类别下的在营SKU个数 X >上限 Y。
            // X：从国辉推送的表里计算本门店下，建议经营状态 != 店级新品的全部商品个数。Y：本门店对应店型下这个类别的SKU上限数。
            if (zySkuCount > skuMaxLimit) {
                upperLimit=INTEGER_ONE;
            }
            JymlStoreSkuSuggestProcessDetail detail = new JymlStoreSkuSuggestProcessDetail();
            detail.setBusinessOrgId(firstSuggest.getBusinessOrgId());
            detail.setStoreId(firstSuggest.getStoreId());
            detail.setStoreCode(firstSuggest.getStoreCode());
            detail.setCategory(config.getCategory());
            detail.setCategoryName(config.getCategoryName());
            String rxOtc="";
            if (config.getCategory().contains(GOODS_OTC)){
                rxOtc=GOODS_OTC;
            }else if (config.getCategory().contains(GOODS_RX)){
                rxOtc=GOODS_RX;
            }
            detail.setRxOtc(rxOtc);
            detail.setMiddleCategory(config.getMiddleCategory());
            detail.setMiddleCategoryName(config.getMiddleCategoryName());
            detail.setSmallCategory(config.getSmallCategory());
            detail.setSmallCategoryName(config.getSmallCategoryName());
            detail.setSubCategory(config.getSubCategory());
            detail.setSubCategoryName(config.getSubCategoryName());
            detail.setSkuCount(0); //经营sku count 取该类别下经营状态（我的确认） in （经营-必备，经营-选配）的商品个数。
            detail.setUpperLimit(upperLimit);
            detail.setSkuMaxLimit(skuMaxLimit);
            detail.setSkuLowerLimit(skuMinLimit);
            detail.setConfirmed(0);
            detail.setConfirmedBy(0L);
            detail.setConfirmedName("");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("zySkuCount", zySkuCount);
            jsonObject.put("skuMaxLimit", skuMaxLimit);
            jsonObject.put("skuMinLimit", skuMinLimit);
            jsonObject.put("reduce", INTEGER_ZERO.equals(upperLimit)?INTEGER_ZERO:zySkuCount-skuMaxLimit);
            detail.setExtend(jsonObject.toJSONString());
            detail.setStatus(firstSuggest.getStatus());
            detail.setVersion(firstSuggest.getVersion());
            result.add(detail);
        }
        return result;
    }
    public JymlStoreSkuSuggestProcess summarizeMdProcess(OrgInfoBaseCache storeInfo, List<JymlStoreSkuSuggest> jymlStoreSkuSuggests, List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails) {
        if (CollectionUtils.isEmpty(jymlStoreSkuSuggests) || CollectionUtils.isEmpty(jymlStoreSkuSuggestProcessDetails)) {
            return null;
        }
        JymlStoreSkuSuggest firstSuggest = jymlStoreSkuSuggests.get(0);
        logger.warn("summaryMdProcess storeInfo={}",storeInfo);
        JymlAdjustmentCycleExample example= new JymlAdjustmentCycleExample();
        if(Objects.nonNull(storeInfo.getBusinessOrgId())){
            example.createCriteria().andOrgIdEqualTo(storeInfo.getBusinessOrgId()).andStatusEqualTo(NORMAL_STATUS);
            example.setOrderByClause("id desc");
            example.setLimit(1);
        }else {
            logger.warn("summaryMdProcess AdjustmentCycle为空  storeNo={}",firstSuggest.getStoreCode());
            return null;
        }
        List<JymlAdjustmentCycle> jymlAdjustmentCycles = jymlAdjustmentCycleMapper.selectByExample(example);
        // 处理结果
        if (CollectionUtils.isNotEmpty(jymlAdjustmentCycles)) {
            JymlAdjustmentCycle jymlAdjustmentCycle = jymlAdjustmentCycles.get(0);
            logger.info("设置周期:{} ", jymlAdjustmentCycle);
            // 取列表中的第一个元素来获取门店相关信息
            JymlStoreSkuSuggestProcess process = new JymlStoreSkuSuggestProcess();
            // 从 JymlStoreSkuSuggest 中获取门店相关信息
            process.setBusinessOrgId(storeInfo.getBusinessOrgId());
            process.setStoreId(firstSuggest.getStoreId());
            process.setStoreCode(firstSuggest.getStoreCode());

            Date adjustChannelOpenDate = jymlAdjustmentCycle.getAdjustChannelOpenDate();
            // 将 Date 转换为 LocalDate
            LocalDate firstOpenDate = adjustChannelOpenDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate newLocalDate = ManageDataHelper.findNextValidStartDate(jymlAdjustmentCycle.getExtend(),firstOpenDate,
                    jymlAdjustmentCycle.getDaysAfterStartDateForbidEffect()+jymlAdjustmentCycle.getDaysAfterEffectCloseChannel());
            if (newLocalDate != null) {
               logger.info("下一个有效的活动开始日期是:{}", newLocalDate);
            } else {
                logger.warn("没有找到合适的活动开始日期。");
                return null;
            }
            // 修改年份为当前年份
            LocalDate  localDateForbidEffect = newLocalDate.plusDays(jymlAdjustmentCycle.getDaysAfterStartDateForbidEffect());
            LocalDate localDateCloseChannel = localDateForbidEffect.plusDays(jymlAdjustmentCycle.getDaysAfterEffectCloseChannel());
            LocalDate localDateNext = newLocalDate.plusMonths(Integer.parseInt(jymlAdjustmentCycle.getAdjustFrequency()));
            LocalDate localDateCloseChannelNext = localDateCloseChannel.plusMonths(Integer.parseInt(jymlAdjustmentCycle.getAdjustFrequency()));
            // 将修改后的 LocalDate 转换回 Date
            Date newDate = Date.from(newLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dateForbidEffect = Date.from(localDateForbidEffect.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dateForbidEffectYesterday = Date.from(localDateForbidEffect.minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dateCloseChannel = Date.from(localDateCloseChannel.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dateCloseChannelYesterday = Date.from(localDateCloseChannel.minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dateNext = Date.from(localDateNext.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dateCloseChannelNext = Date.from(localDateCloseChannelNext.atStartOfDay(ZoneId.systemDefault()).toInstant());
            process.setBeginProcessTime(newDate);
            process.setScheduledProcessTime(dateForbidEffect);
            process.setEndProcessTime(dateCloseChannel);
            process.setConfirmed(0);
            process.setNextBeginProcessTime(dateNext);
            process.setNextEndProcessTime(dateCloseChannelNext);
            process.setStatus(firstSuggest.getStatus());
            process.setVersion(firstSuggest.getVersion());
            String zdtStartNotice="";

            SimpleDateFormat sdf = new SimpleDateFormat("M月d日", Locale.CHINA);
            // 判断是否在管控门店范围内 (不受管控的门店,不用给店长发微信消息)
            boolean contains = selectMdmStoreIdFilterSelector(storeInfo);
            logger.info("contains={}",contains);
            process.setZdtStartNoticeFalg(-1);
            process.setZdtEndNoticeFalg(-1);
            // 不受经营目录管控的门店： 店长，您好。当前您的门店在营商品2300个，您可以将不想请货的商品标记为“不经营”。“不经营”商品，无法手工请货，不再推送请货建议。
            if (!contains){
                long zyCount = jymlStoreSkuSuggests.stream().filter(v -> SuggestManageStatusEnum.zyStatusList.contains(Integer.parseInt(v.getSuggestManageStatus()))).count();
                zdtStartNotice = "店长，您好。当前您的门店在营商品"+zyCount+"个，您可以将不想请货的商品标记为“不经营”。“不经营”商品，无法手工请货，不再推送请货建议。";
                process.setZdtEndNotice("店长，您好。当前您的门店在营商品"+zyCount+"个，您可以将不想请货的商品标记为“不经营”。“不经营”商品，无法手工请货，不再推送请货建议。下次调整经营目录周期为"+sdf.format(dateNext)+"-"+sdf.format(dateCloseChannelNext)+"。");
                // 不受经营目录管控的门店：汇总时相当于已经完成自动确认，所以直接设置确认为1
                process.setConfirmed(INTEGER_ONE);
            }else {
                List<EmployeeDetailWithWxDTO> salesclerkMasters = permissionService.getSalesclerkMastersByStoreId(firstSuggest.getStoreId());
                if (CollectionUtils.isNotEmpty(salesclerkMasters)) {
                    if (INTEGER_ONE.equals(jymlAdjustmentCycle.getSendWxNotice())){
                        List<String> empCodes = salesclerkMasters.stream().map(EmployeeDetailWithWxDTO::getWxId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(empCodes)){
                            String wxUsers = empCodes.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("|"));
                            if (StringUtils.isNotBlank(wxUsers)) {
                                logger.info("wxUsers={}",wxUsers);
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("wxUsers", wxUsers);
                                process.setExtend(JSONObject.toJSONString(jsonObject));
                                process.setZdtStartNoticeFalg(1);
                                process.setZdtEndNoticeFalg(1);
                            }
                        }
                    }
                }
                List<JymlSkuMaxLimitConfigure> allConfigList = getAllConfigListByStoreNo(storeInfo.getSapCode());
                JymlStoreIncreaseLimitConfigureExample limitConfigureExample = new JymlStoreIncreaseLimitConfigureExample();
                limitConfigureExample.createCriteria().andStoreCodeEqualTo(storeInfo.getSapCode());
                JymlStoreIncreaseLimitConfigure jymlStoreIncreaseLimitConfigure = jymlStoreIncreaseLimitConfigureMapper.selectByExample(limitConfigureExample).stream().findAny().orElse(null);
                logger.info("jymlStoreIncreaseLimitConfigure:{}", JSON.toJSONString(jymlStoreIncreaseLimitConfigure));
                Integer increaseLimit = null == jymlStoreIncreaseLimitConfigure ? 0 : jymlStoreIncreaseLimitConfigure.getIncreaseLimit();
                Integer increaseLow = null == jymlStoreIncreaseLimitConfigure ? 0 : jymlStoreIncreaseLimitConfigure.getIncreaseLow();
                logger.info("allConfigList.size={}",allConfigList.size());
                //如果再管控范围内 但是当前门店 sku limit 为空，则跳过 因为聚合不出来分类
                if(CollectionUtils.isEmpty(allConfigList)){
                    logger.warn("门店={}聚合不出来分类 跳过 allConfigList.size={}",storeInfo.getSapCode(),allConfigList.size());
                    return null;
                }
                long zyCount = jymlStoreSkuSuggests.stream().filter(v -> SuggestManageStatusEnum.zyStatusList.contains(Integer.parseInt(v.getSuggestManageStatus()))).count();
                int sum = allConfigList.stream().filter(v -> Objects.nonNull(v.getSkuMaxLimit())).mapToInt(JymlSkuMaxLimitConfigure::getSkuMaxLimit).sum();
                long count = jymlStoreSkuSuggestProcessDetails.stream().filter(d -> INTEGER_ONE.equals(d.getUpperLimit())).count();
                int limitSum = 0;
                int lowSum = 0;
                for (JymlStoreSkuSuggestProcessDetail detail : jymlStoreSkuSuggestProcessDetails) {
                    try {
                        if (INTEGER_ZERO.equals(detail.getUpperLimit())) {
                            limitSum += ExtendUtil.getExtendValue(detail.getExtend(), "zySkuCount", Integer.class);
                            lowSum += ExtendUtil.getExtendValue(detail.getExtend(), "zySkuCount", Integer.class);
                        }
                        if (INTEGER_ONE.equals(detail.getUpperLimit())) {
                            limitSum += ExtendUtil.getExtendValue(detail.getExtend(), "skuMaxLimit", Integer.class);
                            lowSum += ExtendUtil.getExtendValue(detail.getExtend(), "skuMinLimit", Integer.class);
                        }
                    } catch (Exception e) {
                        logger.error("limitSumlowSu计算错误", e);
                    }
                }
                logger.info("zyCount={} sum={} count={} limitSum={} lowSum={}",zyCount,sum,count,limitSum,lowSum);
                if (count>0){
                    // 过滤 extend 不为空且取出 reduce 字段求和
                    int reduceSum = jymlStoreSkuSuggestProcessDetails.stream()
                            .filter(d -> d.getExtend() != null && !d.getExtend().isEmpty())
                            .map(d -> {
                                JSONObject extendJson = JSONObject.parseObject(d.getExtend());
                                return extendJson.getInteger("reduce");
                            })
                            .filter(Objects::nonNull)
                            .mapToInt(Integer::intValue)
                            .sum();
                    logger.info("zyCount={} sum={} count={} reduceSum={}",zyCount,sum,count,reduceSum);
                    zdtStartNotice =  "店长，您好。当前您的门店在营商品"+zyCount+"个，公司要求的下限"+(lowSum+increaseLow)+"个，上限"+(limitSum+increaseLimit)+"个。按照商品类别sku数要求，超上限的类别"+count+"个，需要您在其中选出"+reduceSum+"个商品调整为“不经营”；并在二轮选配商品里增加至少"+increaseLow+"个，至多"+increaseLimit+"个商品。“不经营”商品，无法手工请货，不再推送请货建议。请在"+sdf.format(dateForbidEffectYesterday)+"24点前完成调整，如不操作，"+sdf.format(dateForbidEffectYesterday)+"24点将按照系统建议自动生效。如有问题，可联系片区经理。";
                }else {
                    zdtStartNotice = "店长，您好。当前您的门店在营商品"+zyCount+"个，您可以将不想请货的商品标记为“不经营”。“不经营”商品，无法手工请货，不再推送请货建议。";
                }
                process.setZdtEndNotice("店长，您好。"+sdf.format(dateForbidEffectYesterday)+"24点，系统已按照建议，选择了0个商品调整为“不经营”，这些商品无法手工请货，不再推送请货建议。如有疑义，请登录海典系统进入”经营目录管理“菜单，按需调整。注意：调整通道将在"+sdf.format(dateCloseChannelYesterday)+"24点关闭！");
            }
            // 受经营目录管控的门店 设置默认值
            process.setZdtStartNotice(zdtStartNotice);
            return process;
        } else {
            logger.warn("summaryMdProcess jymlAdjustmentCyclesList 为空  storeNo={}",firstSuggest.getStoreCode());
            return null;
        }
    }
    private List<StoreGoodsContentDTO> buildStoreGoodsContentDTO( OrgInfoBaseCache storeInfo, List<JymlStoreSkuSuggest> jymlStoreSkuSuggests, Integer contentBizType, TokenUserDTO userDTO, Map<String, SpuListVo> newSpuMap) {
        List<StoreGoodsContentDTO> storeGoodsContentDTOS = new ArrayList<>();
        ListIterator<JymlStoreSkuSuggest> iterator = jymlStoreSkuSuggests.listIterator();
        while (iterator.hasNext()) {
            JymlStoreSkuSuggest jymlStoreSkuSuggest = iterator.next();
            SpuListVo spuNewVo = newSpuMap.get(jymlStoreSkuSuggest.getGoodsNo());
            if (Objects.isNull(spuNewVo)){
                logger.warn("buildStoreGoodsContent not found goodsNo={}",jymlStoreSkuSuggest.getGoodsNo());
                iterator.remove();
                continue;
            }
            StoreGoodsContentDTO storeGoodsContentDTO = new StoreGoodsContentDTO();
            storeGoodsContentDTO.setBizType(contentBizType);
            storeGoodsContentDTO.setStoreId(storeInfo.getOutId());
            storeGoodsContentDTO.setBusinessId(storeInfo.getBusinessId());
            storeGoodsContentDTO.setPlatformOrgId(storeInfo.getPlatformOrgId());
            storeGoodsContentDTO.setGoodsNo(jymlStoreSkuSuggest.getGoodsNo());
            storeGoodsContentDTO.setBarCode(spuNewVo.getBarCode());
            storeGoodsContentDTO.setGoodsName(spuNewVo.getName());
            storeGoodsContentDTO.setGoodsCommonName(spuNewVo.getCurName());
            storeGoodsContentDTO.setGoodsUnit(spuNewVo.getGoodsunit());
            storeGoodsContentDTO.setDescription(spuNewVo.getDescription());
            storeGoodsContentDTO.setSpecifications(spuNewVo.getJhiSpecification());
            storeGoodsContentDTO.setDosageForm(spuNewVo.getDosageformsid());
            storeGoodsContentDTO.setManufacturer(spuNewVo.getFactoryid());
            storeGoodsContentDTO.setApprovalNumber(spuNewVo.getApprdocno());
            storeGoodsContentDTO.setSuggestManageStatus(Integer.parseInt(jymlStoreSkuSuggest.getSuggestManageStatus()));
            storeGoodsContentDTO.setSubCategoryId(Long.parseLong(jymlStoreSkuSuggest.getSubCategory()));
            storeGoodsContentDTO.setCreatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setUpdatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setCreatedName(userDTO.getName());
            storeGoodsContentDTO.setUpdatedName(userDTO.getName());
            storeGoodsContentDTOS.add(storeGoodsContentDTO);
        }
        logger.warn("buildStoreGoodsContent ydym goodsNo={}",storeGoodsContentDTOS.stream().map(StoreGoodsContentDTO::getGoodsNo).collect(Collectors.toList()));
        return storeGoodsContentDTOS;
    }
    private List<StoreGoodsContentDTO> buildStoreGoodsContentDTOConfirm(OrgInfoBaseCache storeInfo,  List<ManageCommonDTO> confirmList, Integer contentBizType, TokenUserDTO userDTO, MdmTask task) {
        List<StoreGoodsContentDTO> storeGoodsContentDTOS = new ArrayList<>();
        logger.info("buildStoreGoodsContentDTO storeInfo={}",storeInfo);
        SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
        spuNewParamVo.setBusinessId(storeInfo.getBusinessId());
        spuNewParamVo.setGoodsNoList(confirmList.stream().map(ManageCommonDTO::getGoodsNo).filter(Objects::nonNull).collect(Collectors.toList()));
        Map<String, SpuListVo> newSpuMap = searchService.getSpuVOMap(spuNewParamVo.getGoodsNoList());
        for (ManageCommonDTO manageCommonDTO : confirmList) {
            SpuListVo spuNewVo = newSpuMap.get(manageCommonDTO.getGoodsNo());
            if (Objects.isNull(spuNewVo)){
                continue;
            }
            StoreGoodsContentDTO storeGoodsContentDTO = new StoreGoodsContentDTO();
            storeGoodsContentDTO.setBizType(contentBizType);
            storeGoodsContentDTO.setStoreId(storeInfo.getOutId());
            storeGoodsContentDTO.setBusinessId(storeInfo.getBusinessId());
            storeGoodsContentDTO.setPlatformOrgId(storeInfo.getPlatformOrgId());
            storeGoodsContentDTO.setGoodsNo(manageCommonDTO.getGoodsNo());
            storeGoodsContentDTO.setBarCode(spuNewVo.getBarCode());
            storeGoodsContentDTO.setGoodsName(spuNewVo.getName());
            storeGoodsContentDTO.setGoodsCommonName(spuNewVo.getCurName());
            storeGoodsContentDTO.setGoodsUnit(spuNewVo.getGoodsunit());
            storeGoodsContentDTO.setDescription(spuNewVo.getDescription());
            storeGoodsContentDTO.setSpecifications(spuNewVo.getJhiSpecification());
            storeGoodsContentDTO.setDosageForm(spuNewVo.getDosageformsid());
            storeGoodsContentDTO.setManufacturer(spuNewVo.getFactoryid());
            storeGoodsContentDTO.setApprovalNumber(spuNewVo.getApprdocno());
            storeGoodsContentDTO.setSuggestManageStatus(Integer.parseInt(manageCommonDTO.getSuggestManageStatus()));
            storeGoodsContentDTO.setManageStatus(manageCommonDTO.getMyConfirm());
            storeGoodsContentDTO.setSubCategoryId(Long.parseLong(manageCommonDTO.getSubCategory()));
            storeGoodsContentDTO.setCreatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setUpdatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setCreatedName(userDTO.getName());
            storeGoodsContentDTO.setUpdatedName(userDTO.getName());
            storeGoodsContentDTO.setMdmTaskId(task.getId());
            logger.info("buildStoreGoodsContentDTO  storeGoodsContentDTO={}",storeGoodsContentDTO);
            storeGoodsContentDTOS.add(storeGoodsContentDTO);
        }
        return storeGoodsContentDTOS;
    }
    private List<StoreGoodsContentDTO> buildStoreGoodsContentDTOByContents(OrgInfoBaseCache storeInfo,  List<StoreGoodsContents> contents, Integer contentBizType, TokenUserDTO userDTO, MdmTask task) {
        List<StoreGoodsContentDTO> storeGoodsContentDTOS = new ArrayList<>();
        logger.info("buildStoreGoodsContentDTO  storeInfo={}",storeInfo);
        for (StoreGoodsContents manageCommonDTO : contents) {
            StoreGoodsContentDTO storeGoodsContentDTO = new StoreGoodsContentDTO();
            storeGoodsContentDTO.setBizType(contentBizType);
            storeGoodsContentDTO.setStoreId(storeInfo.getOutId());
            storeGoodsContentDTO.setBusinessId(storeInfo.getBusinessId());
            storeGoodsContentDTO.setPlatformOrgId(storeInfo.getPlatformOrgId());
            storeGoodsContentDTO.setGoodsNo(manageCommonDTO.getGoodsNo());
            storeGoodsContentDTO.setSuggestManageStatus(manageCommonDTO.getSuggestManageStatus());
            storeGoodsContentDTO.setManageStatus(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode().equals(contentBizType) ? ManageStatusEnum.MANAGE_CHOOSE.getCode() : ManageStatusEnum.NON_MANAGE.getCode());
            storeGoodsContentDTO.setSubCategoryId(manageCommonDTO.getSubCategoryId());
            storeGoodsContentDTO.setCreatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setUpdatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setCreatedName(userDTO.getName());
            storeGoodsContentDTO.setUpdatedName(userDTO.getName());
            storeGoodsContentDTO.setMdmTaskId(task.getId());
            storeGoodsContentDTOS.add(storeGoodsContentDTO);
        }
        return storeGoodsContentDTOS;
    }
    private JymlSkuMaxLimitConfigure getLastConfigTemplateByStore(ManageQueryParam param){
        OrgInfoBaseCache storeInfo = getStoreInfoByCache(param);
        if (null == storeInfo) {
            return null;
        }
        //根据门店查询门店拓展信息
        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(storeInfo.getSapCode());
        logger.info("getLastConfigTemplateByStore mdmStoreExDTO={}",mdmStoreExDTO);
        if (null == mdmStoreExDTO || StringUtils.isBlank(mdmStoreExDTO.getStoreTypeCode())) {
            return null;
        }
        JymlStoreSkuLimitAdjustEffectExample effectExample = new JymlStoreSkuLimitAdjustEffectExample();
        effectExample.createCriteria().andStoreOrgIdEqualTo(storeInfo.getId());
        JymlStoreSkuLimitAdjustEffect effect = jymlStoreSkuLimitAdjustEffectMapper.selectByExample(effectExample).stream().findAny().orElse(null);
        JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
        example.createCriteria()
                .andBusinessOrgIdEqualTo(storeInfo.getBusinessOrgId())
                .andCityEqualTo(mdmStoreExDTO.getCity())
                .andStatusEqualTo(Constants.NORMAL_STATUS)
                .andStoreTypeEqualTo(null == effect ? mdmStoreExDTO.getStoreTypeCode() : effect.getStoreType());
        example.setOrderByClause("version desc");
        example.setLimit(1);
        List<JymlSkuMaxLimitConfigure> options = jymlSkuMaxLimitConfigureMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(options)) {
            return options.get(0);
        }
        return null;
    }
    private List<JymlSkuMaxLimitConfigure> getAllConfigListByStoreNo(String storeNo){
        ManageQueryParam param = new ManageQueryParam();
        param.setStoreNo(storeNo);
        JymlSkuMaxLimitConfigure configure = getLastConfigTemplateByStore(param);
        if (Objects.isNull(configure)){
            return new ArrayList<>();
        }
        JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
        example.createCriteria()
                .andBusinessOrgIdEqualTo(configure.getBusinessOrgId())
                .andCityEqualTo(configure.getCity())
                .andStoreTypeEqualTo(configure.getStoreType())
                .andVersionEqualTo(configure.getVersion());
        List<JymlSkuMaxLimitConfigure> options = jymlSkuMaxLimitConfigureMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(options)) {
            return options;
        }
        return new ArrayList<>();
    }
    private OrgInfoBaseCache getStoreInfoByCache(ManageQueryParam param) {
        try {
            Optional<OrgInfoBaseCache> storeOptional = Optional.empty();
            if (StringUtils.isNotBlank(param.getStoreNo())) {
                storeOptional = CacheVar.getStoreBySapCode(param.getStoreNo());
            } else if (param.getStoreId() != null) {
                storeOptional = CacheVar.getStoreByStoreId(param.getStoreId());
            }
            if (storeOptional.isPresent()){
                OrgInfoBaseCache orgInfoBaseCache = storeOptional.get();
                param.setStoreNo(orgInfoBaseCache.getSapCode());
                //根据门店查询门店拓展信息
                MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(orgInfoBaseCache.getSapCode());
                if (null == mdmStoreExDTO || StringUtils.isBlank(mdmStoreExDTO.getStoreTypeCode())) {
                    logger.warn("getStoreInfoByCache storeTypeCode is null mdmStoreExDTO={}",mdmStoreExDTO);
                    return null;
                }
                logger.info("getStoreInfoByCache storeInfo={}",JSONObject.toJSONString(orgInfoBaseCache));
                return orgInfoBaseCache;
            }
            return null;
        } catch (Exception e) {
            // 记录异常日志
            logger.error("Error occurred while fetching store info from cache", e);
            return null;
        }
    }

    /**
     * 设置是否需要二次确认和是否在调整期
     * @param storeInfo
     * @param manageProcessDTO
     */
    private void setConfirmAndAdjustEditAble( OrgInfoBaseCache storeInfo, ManageProcessDTO manageProcessDTO) {
        JymlStoreSkuSuggestProcess process = getProcess(storeInfo.getSapCode());
        if (Objects.nonNull(process) && DateUtil.isIn(new Date(),process.getBeginProcessTime(), process.getEndProcessTime())){
            manageProcessDTO.setDzNotice(process.getZdtStartNotice());
            manageProcessDTO.setEditAble(true);
            JymlAdjustmentCycleExample example= new JymlAdjustmentCycleExample();
            if(Objects.nonNull(storeInfo.getBusinessOrgId())) {
                example.createCriteria().andOrgIdEqualTo(storeInfo.getBusinessOrgId()).andStatusEqualTo(NORMAL_STATUS);
                example.setOrderByClause("id desc");
                example.setLimit(1);
                List<JymlAdjustmentCycle> jymlAdjustmentCycles = jymlAdjustmentCycleMapper.selectByExample(example);
                // 处理结果
                if (CollectionUtils.isNotEmpty(jymlAdjustmentCycles)) {
                    JymlAdjustmentCycle jymlAdjustmentCycle = jymlAdjustmentCycles.get(0);
                    logger.info("设置周期:{} ", jymlAdjustmentCycle);
                    if (Objects.nonNull(jymlAdjustmentCycle) && INTEGER_ONE.equals(jymlAdjustmentCycle.getNeedConfirm())){
                        manageProcessDTO.setNeedConfirm(true);
                    }
                }
            }
        }else {
            manageProcessDTO.setDzNotice("店长，您好。当前不是经营目录调整期，只能查看无法调整。");
            manageProcessDTO.setEditAble(false);
        }
    }
    private JymlStoreSkuSuggestProcess getProcess(String storeNo){
        JymlStoreSkuSuggestProcessExample example= new JymlStoreSkuSuggestProcessExample();
        example.createCriteria().andStoreCodeEqualTo(storeNo);
        example.setOrderByClause("id desc");
        example.setLimit(1);
        List<JymlStoreSkuSuggestProcess> jymlStoreSkuSuggestProcesses = jymlStoreSkuSuggestProcessMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcesses)){
            return jymlStoreSkuSuggestProcesses.get(0);
        }
        return null;
    }
}
