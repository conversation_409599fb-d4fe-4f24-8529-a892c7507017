package com.cowell.scib.service.impl;

import com.cowell.scib.entity.DevelopModuleRecordFile;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/8/29 15:10
 */
@Slf4j
@Service
public class DevelopModuleWriteServiceImpl extends DevelopChechService implements DevelopModuleWriteService {

    @Autowired
    private ITencentCOSService tencentCOSService;
    @Autowired
    private DevelopModuleService developModuleService;
    @Autowired
    private DevelopModuleRecordService developModuleRecordService;
    @Autowired
    private DevelopModuleRecordFileService developModuleRecordFileService;
    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Override
    public void moduleAddOrEdit(DevelopAddParam developAddParam, TokenUserDTO userDTO) {
        log.info("recordAddOrEdit|developAddParam:{}.", developAddParam);
        Long moduleCodeCount = checkModuleAddOrEditParam(developAddParam);

        if(moduleCodeCount>0){
            //编辑
            developModuleService.editModule(developAddParam, userDTO);
        }else{
            //创建
            developModuleService.addModule(developAddParam, userDTO);
        }
    }

    @Override
    public void recordAddOrEdit(DevelopRecordAddParam developRecordAddParam, TokenUserDTO userDTO) {
        log.info("recordAddOrEdit|developRecordAddParam:{}.", developRecordAddParam);
        try {
            checkModuleRecordAddOrEditParam(developRecordAddParam);
            Integer recordId = developRecordAddParam.getRecordId();
            boolean isCreate = Objects.isNull(recordId);
            if(isCreate){
                //创建
                developModuleRecordService.addModuleRecord(developRecordAddParam, userDTO);
            }else{
                //编辑
                developModuleRecordService.editModuleRecord(developRecordAddParam, userDTO);
            }
            CompletableFuture.runAsync(() -> {
                developModuleRecordService.sendMessage(developRecordAddParam);
            },taskExecutor);
        } catch (AmisBadRequestException ae) {
            log.error("recordAddOrEdit|error", ae);
            throw ae;
        } catch (Exception e) {
            log.error("recordAddOrEdit|error", e);
            throw new AmisBadRequestException(ErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public void deleteFile(Integer id) {
        if(Objects.isNull(id)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        DevelopModuleRecordFile recordFile = developModuleRecordFileService.recordFileById(id);
        if(Objects.isNull(recordFile)){
            log.info("无需删除");
            return;
        }
        tencentCOSService.delSingleFile(recordFile.getFileUrl());
        developModuleRecordFileService.deleteFileById(id);
    }
}
