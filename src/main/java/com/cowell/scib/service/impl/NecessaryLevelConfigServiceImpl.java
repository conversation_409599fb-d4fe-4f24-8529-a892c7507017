package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.NecessaryConfigAreaEnum;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.CommonEnumsMapper;
import com.cowell.scib.mapperDgms.NecessaryLevelConfigMapper;
import com.cowell.scib.mapperDgms.NecessaryLevelRoleConfigMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.NecessaryLevelConfigService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelRoleConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryRoleConfigUpdateParam;
import com.cowell.scib.service.dto.config.NecessaryUpdateParam;
import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.cowell.scib.service.vo.amis.CommonResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class NecessaryLevelConfigServiceImpl implements NecessaryLevelConfigService {
    private final Logger logger = LoggerFactory.getLogger(NecessaryLevelConfigServiceImpl.class);

    @Resource
    private CommonEnumsMapper commonEnumsMapper;
    @Resource
    private NecessaryLevelConfigMapper necessaryLevelConfigMapper;
    @Resource
    private NecessaryLevelRoleConfigMapper necessaryLevelRoleConfigMapper;
    @Resource
    private PermissionService permissionService;

    @Value("${dgms.necessary.config.proptype:necessaryTag}")
    private String propType;
    @Override
    public CommonResponse<NecessaryLevelConfigDTO> list(AmisPageParam param) {
        try {
            List<CommonEnums> commonEnums = getEnumsByPropType(Arrays.stream(StringUtils.split(propType, ",")).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(commonEnums)) {
                return CommonResponse.ok();
            }
            NecessaryLevelConfigExample configExample = new NecessaryLevelConfigExample();
            configExample.createCriteria().andNecessaryTagIn(commonEnums.stream().map(CommonEnums::getEnumValue).collect(Collectors.toList()));
            Map<String, NecessaryLevelConfig> configMap = necessaryLevelConfigMapper.selectByExample(configExample).stream().collect(Collectors.toMap(v -> v.getNecessaryTag(), Function.identity(), (k1, k2) -> k1));
            List<NecessaryLevelConfigDTO> result = commonEnums.stream().map(v -> {
                NecessaryLevelConfigDTO dto = new NecessaryLevelConfigDTO();
                NecessaryLevelConfig config = configMap.get(v.getEnumValue());
                if (null == config) {
                    dto.setNecessaryTag(v.getEnumValue());
                    dto.setNecessaryTagName(v.getEnumName());
                } else {
                    BeanUtils.copyProperties(config, dto);
                    NecessaryConfigAreaEnum areaEnum = NecessaryConfigAreaEnum.getEnumByCode(config.getAreaLevel());
                    dto.setAreaLevelDesc(areaEnum.getMessage());
                }
                return dto;
            }).collect(Collectors.toList());
            return CommonResponse.ok(result);
        } catch (Exception e) {
            logger.error("查询必备层级配置失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void edit(NecessaryUpdateParam param, TokenUserDTO userDTO) {
        try {
            logger.info("edit necessaryConfig param:{}", JSON.toJSONString(param));
            if (StringUtils.isBlank(param.getNecessaryTag())) {
                throw new AmisBadRequestException("请输入必备标签-枚举项值");
            }
            OrgTypeEnum orgEnum = Optional.ofNullable(OrgTypeEnum.getEnumByCode(param.getOrgLevel())).orElseThrow(() -> new AmisBadRequestException("请选择组织层级"));
            NecessaryConfigAreaEnum areaEnum = Optional.ofNullable(NecessaryConfigAreaEnum.getEnumByCode(param.getAreaLevel())).orElseThrow(() -> new AmisBadRequestException("请选择区域层级"));
            NecessaryLevelConfigExample configExample = new NecessaryLevelConfigExample();
            configExample.createCriteria().andNecessaryTagEqualTo(param.getNecessaryTag());
            NecessaryLevelConfig config = necessaryLevelConfigMapper.selectByExample(configExample).stream().findAny().orElse(null);
            if (null == config) {
                config = new NecessaryLevelConfig();
                config.setNecessaryTag(param.getNecessaryTag());
                config.setNecessaryTagName(param.getNecessaryTagName());
                config.setCreateById(userDTO.getUserId());
                config.setCreateBy(userDTO.getName());

            }
            config.setOrgLevel(orgEnum.getCode());
            config.setAreaLevel(areaEnum.getCode());
            config.setStoreType(param.getStoreType());
            config.setUpdateById(userDTO.getUserId());
            config.setUpdateBy(userDTO.getName());
            if (null == config.getId()) {
                necessaryLevelConfigMapper.insertSelective(config);
            } else {
                necessaryLevelConfigMapper.updateByPrimaryKeySelective(config);
            }
        } catch (Exception e) {
            logger.error("编辑必备层级配置失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public CommonResponse<NecessaryLevelRoleConfigDTO> roleList(Long orgId) {
        try {
            if (null == orgId) {
                throw new AmisBadRequestException("请选择平台");
            }
            List<CommonEnums> commonEnums = getEnumsByPropType(Arrays.stream(StringUtils.split(propType, ",")).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(commonEnums)) {
                return CommonResponse.ok();
            }
            if (CollectionUtils.isEmpty(commonEnums)) {
                return CommonResponse.ok();
            }
            NecessaryLevelRoleConfigExample example = new NecessaryLevelRoleConfigExample();
            example.createCriteria().andOrgIdEqualTo(orgId);
            Map<String, NecessaryLevelRoleConfig> roleConfigMap = necessaryLevelRoleConfigMapper.selectByExample(example).stream().collect(Collectors.toMap(NecessaryLevelRoleConfig::getNecessaryTag, Function.identity(), (k1, k2) -> k1));
            OrgInfoBaseCache orgInfoBaseCache = CacheVar.getPlatformByOrgId(orgId).orElseThrow(() -> new AmisBadRequestException("没有查询到所选的平台"));
            return CommonResponse.ok(commonEnums.stream().map(v -> {
                NecessaryLevelRoleConfigDTO dto = new NecessaryLevelRoleConfigDTO();
                NecessaryLevelRoleConfig config = roleConfigMap.get(v.getEnumValue());
                if (null == config) {
                    dto.setNecessaryTag(v.getEnumValue());
                    dto.setNecessaryTagName(v.getEnumName());
                    dto.setOrgId(orgInfoBaseCache.getId());
                    dto.setOrgName(StringUtils.isBlank(orgInfoBaseCache.getShortName()) ? orgInfoBaseCache.getName() : orgInfoBaseCache.getShortName());
                    return dto;
                }
                BeanUtils.copyProperties(config, dto);
                return dto;
            }).collect(Collectors.toList()));
        } catch (Exception e) {
            logger.error("查询必备层级角色配置失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void roleEdit(NecessaryRoleConfigUpdateParam param, TokenUserDTO userDTO) {
        try {
            logger.info("edit necessaryRoleConfig param:{}", JSON.toJSONString(param));
            if (null == param.getOrgId()) {
                throw new AmisBadRequestException("请选择平台");
            }
            OrgInfoBaseCache orgInfoBaseCache = CacheVar.getPlatformByOrgId(param.getOrgId()).orElseThrow(() -> new AmisBadRequestException("没有查询到所选的平台"));
            if (StringUtils.isBlank(param.getRoles())) {
                throw new AmisBadRequestException("请输入角色清单");
            }
            if (StringUtils.isBlank(param.getNecessaryTag())) {
                throw new AmisBadRequestException("请输入必备标签-枚举项值");
            }
            NecessaryLevelRoleConfigExample example = new NecessaryLevelRoleConfigExample();
            example.createCriteria().andOrgIdEqualTo(orgInfoBaseCache.getId()).andNecessaryTagEqualTo(param.getNecessaryTag());
            NecessaryLevelRoleConfig roleConfig = necessaryLevelRoleConfigMapper.selectByExample(example).stream().findAny().orElse(null);
            if (null == roleConfig) {
                roleConfig = new NecessaryLevelRoleConfig();
                roleConfig.setOrgId(orgInfoBaseCache.getId());
                roleConfig.setOrgName(StringUtils.isBlank(orgInfoBaseCache.getShortName()) ? orgInfoBaseCache.getName() : orgInfoBaseCache.getShortName());
                roleConfig.setNecessaryTag(param.getNecessaryTag());
                roleConfig.setNecessaryTagName(param.getNecessaryTagName());
                roleConfig.setCreateById(userDTO.getUserId());
                roleConfig.setCreateBy(userDTO.getName());
            }
            roleConfig.setRoles(param.getRoles());
            roleConfig.setUpdateById(userDTO.getUserId());
            roleConfig.setUpdateBy(userDTO.getName());
            if (null == roleConfig.getId()) {
                necessaryLevelRoleConfigMapper.insertSelective(roleConfig);
            } else {
                necessaryLevelRoleConfigMapper.updateByPrimaryKeySelective(roleConfig);
            }
        } catch (Exception e) {
            logger.error("编辑必备层级角色配置失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    private List<CommonEnums> getEnumsByPropType(List<String> propType) {
        CommonEnumsExample enumsExample = new CommonEnumsExample();
        enumsExample.createCriteria().andPropertyCodeIn(propType).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
        List<CommonEnums> commonEnums = commonEnumsMapper.selectByExample(enumsExample);
        return commonEnums;
    }

    public List<NecessaryLevelConfigDTO> getNecessaryTagByRole(TokenUserDTO userDTO){
        if (null == userDTO.getRoles() || userDTO.getRoles().size() <= 0) {
            return new ArrayList<>();
        }
        List<OrgDTO> orgDTOList = permissionService.listOrgTypeBelowOrgAssignedInScope(userDTO.getUserId(), 3L, OrgTypeEnum.PLATFORM.getCode());
        if (CollectionUtils.isEmpty(orgDTOList)) {
            logger.info("用户没有平台权限");
            return new ArrayList<>();
        }
        // 张瑜说多平台权限任取一个就行
        NecessaryLevelRoleConfigExample example = new NecessaryLevelRoleConfigExample();
        example.createCriteria().andOrgIdEqualTo(orgDTOList.get(0).getId());
        List<NecessaryLevelRoleConfig> roleConfigs = necessaryLevelRoleConfigMapper.selectByExample(example).stream()
                .filter(v -> StringUtils.isNotBlank(v.getRoles()) ?
                        Arrays.stream(StringUtils.split(v.getRoles(), ",")).filter(r -> userDTO.getRoles().contains(r)).findAny().isPresent() : false).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgDTOList)) {
            logger.info("过滤角色后没有符合的必备标签");
            return new ArrayList<>();
        }
        NecessaryLevelConfigExample configExample = new NecessaryLevelConfigExample();
        configExample.createCriteria().andNecessaryTagIn(roleConfigs.stream().map(NecessaryLevelRoleConfig::getNecessaryTag).collect(Collectors.toList()));
        List<NecessaryLevelConfigDTO> result = new ArrayList<>(roleConfigs.size());
        Map<String, NecessaryLevelConfig> configMap = necessaryLevelConfigMapper.selectByExample(configExample).stream().collect(Collectors.toMap(NecessaryLevelConfig::getNecessaryTag, Function.identity(), (k1, k2) -> k2));
        roleConfigs.forEach(v -> {
            NecessaryLevelConfig config = configMap.get(v.getNecessaryTag());
            if (null == config) {
                config = new NecessaryLevelConfig();
                config.setNecessaryTag(v.getNecessaryTag());
                config.setNecessaryTagName(v.getNecessaryTagName());
            }
            NecessaryLevelConfigDTO dto = new NecessaryLevelConfigDTO();
            BeanUtils.copyProperties(config, dto);
            result.add(dto);
        });
        return result;
    }
}
