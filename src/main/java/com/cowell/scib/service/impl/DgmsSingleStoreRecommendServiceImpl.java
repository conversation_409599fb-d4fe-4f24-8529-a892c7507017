package com.cowell.scib.service.impl;

import com.cowell.permission.vo.OrgVO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityTidb.DgmsSingleStoreRecommend;
import com.cowell.scib.entityTidb.DgmsSingleStoreRecommendExample;
import com.cowell.scib.mapperTidb.DgmsSingleStoreRecommendMapper;
import com.cowell.scib.service.DgmsSingleStoreRecommendService;
import com.cowell.scib.service.GoodsCommissionService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.StoreService;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.singleStoreSuggest.GoodsPerformanceRuleDTO;
import com.cowell.scib.service.dto.singleStoreSuggest.PerformanceRuleGoodsDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DgmsSingleStoreRecommendServiceImpl implements DgmsSingleStoreRecommendService {

    @Autowired
    private DgmsSingleStoreRecommendMapper dgmsSingleStoreRecommendMapper;

    @Autowired
    private StoreService storeService;

    @Override
    public void updateDgmsSingleStoreRecommends(List<GoodsPerformanceRuleDTO> goodsPerformanceRuleDTOS) {
        if (CollectionUtils.isEmpty(goodsPerformanceRuleDTOS)){
            log.info("获取提成数据为空");
            return;
        }
        List<Long> soreIdList = goodsPerformanceRuleDTOS.stream().map(v -> v.getStoreId()).distinct().collect(Collectors.toList());
        Map<Long, OrgInfoBaseCache> collectMap=new HashMap<>();
        if(CacheVar.getStoreByStoreIdList(soreIdList).isPresent()){
            collectMap = CacheVar.getStoreByStoreIdList(soreIdList).get().stream().collect(Collectors.toMap(v -> v.getOutId(), Function.identity(), (k1, k2) -> k1));
        };
        for (GoodsPerformanceRuleDTO goodsPerformanceRuleDTO : goodsPerformanceRuleDTOS) {
            if (CollectionUtils.isEmpty(goodsPerformanceRuleDTO.getAwardInfoList())){
                continue;
            }
            log.info("计算后提成信息={}",goodsPerformanceRuleDTO);
            OrgInfoBaseCache orgInfoBaseCache = collectMap.get(goodsPerformanceRuleDTO.getStoreId());
            if(Objects.nonNull(orgInfoBaseCache)){
                DgmsSingleStoreRecommendExample dgmsSingleStoreRecommendExample = new DgmsSingleStoreRecommendExample();
                dgmsSingleStoreRecommendExample.createCriteria().andStoreCodeEqualTo(orgInfoBaseCache.getSapCode()).andGoodsNoEqualTo(goodsPerformanceRuleDTO.getGoodsNo());
                List<DgmsSingleStoreRecommend> dgmsSingleStoreRecommends = dgmsSingleStoreRecommendMapper.selectByExample(dgmsSingleStoreRecommendExample);
                if (CollectionUtils.isNotEmpty(dgmsSingleStoreRecommends)){
                    DgmsSingleStoreRecommend dgmsSingleStoreRecommend = dgmsSingleStoreRecommends.get(0);
                    dgmsSingleStoreRecommend.setPromotionAble(Byte.valueOf("1"));
                    dgmsSingleStoreRecommend.setPromotionName(goodsPerformanceRuleDTO.getAwardInfoList().get(0).getPromotionName());
                    dgmsSingleStoreRecommend.setPromotionWay(goodsPerformanceRuleDTO.getAwardInfoList().get(0).getPromotionWay());
                    if (CollectionUtils.isNotEmpty(goodsPerformanceRuleDTO.getAwardInfoList().get(0).getAwardRuleList())){
                        dgmsSingleStoreRecommend.setThresholdInfo(goodsPerformanceRuleDTO.getAwardInfoList().get(0).getAwardRuleList().get(0).getThresholdInfo());
                        dgmsSingleStoreRecommend.setFavInfo(goodsPerformanceRuleDTO.getAwardInfoList().get(0).getAwardRuleList().get(0).getFavInfo());
                    }
                    log.info("计算后提成信息={} 更新表={}",goodsPerformanceRuleDTO, dgmsSingleStoreRecommend);
                    dgmsSingleStoreRecommendMapper.updateByPrimaryKey(dgmsSingleStoreRecommend);
                }
            }
        }
    }
}
