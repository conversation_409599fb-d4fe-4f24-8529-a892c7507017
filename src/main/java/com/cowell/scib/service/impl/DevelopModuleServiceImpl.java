package com.cowell.scib.service.impl;

import com.beust.jcommander.internal.Lists;
import com.cowell.scib.entity.DevelopModule;
import com.cowell.scib.entity.DevelopModuleExample;
import com.cowell.scib.enums.UseStatusEnum;
import com.cowell.scib.mapperDgms.DevelopModuleMapper;
import com.cowell.scib.service.DevelopModuleService;
import com.cowell.scib.service.dto.DevelopModuleDTO;
import com.cowell.scib.service.dto.ModelDevelopDTOCover;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:27
 */
@Slf4j
@Service
public class DevelopModuleServiceImpl implements DevelopModuleService {

    @Autowired
    private DevelopModuleMapper developModuleMapper;

    @Override
    public List<DevelopModuleDTO> listDevelopModuleByCondition(List<String> moduleCodeList, Byte useStatus) {
        log.info("listDevelopModuleByCondition|moduleCodeList:{}..useStatus:{}.", moduleCodeList, useStatus);
        DevelopModuleExample moduleExample = new DevelopModuleExample();
        DevelopModuleExample.Criteria moduleCriteria = moduleExample.createCriteria();
        moduleExample.setOrderByClause(" gmt_create asc");
        if(CollectionUtils.isNotEmpty(moduleCodeList)){
            moduleCriteria.andModuleCodeIn(moduleCodeList);
        }
        if(Objects.nonNull(useStatus)){
            moduleCriteria.andUseStatusEqualTo(useStatus);
        }

        List<DevelopModule> moduleList = developModuleMapper.selectByExample(moduleExample);
        if(CollectionUtils.isEmpty(moduleList)){
            return Lists.newArrayList();
        }
        return moduleList.stream().map(v->{
            return ModelDevelopDTOCover.moduleToDTO(v);
        }).collect(Collectors.toList());
    }

    @Override
    public long countDevelopModuleByName(String moduleName) {
        DevelopModuleExample moduleExample = new DevelopModuleExample();
        moduleExample.createCriteria().andModuleNameEqualTo(moduleName);
        moduleExample.setLimit(1);
        return developModuleMapper.countByExample(moduleExample);
    }

    @Override
    public long countDevelopModuleByCode(String moduleCode) {
        DevelopModuleExample moduleExample = new DevelopModuleExample();
        moduleExample.createCriteria().andModuleCodeEqualTo(moduleCode).andUseStatusEqualTo(UseStatusEnum.START.getCode());
        moduleExample.setLimit(1);
        return developModuleMapper.countByExample(moduleExample);
    }

    @Override
    public void addModule(DevelopAddParam developAddParam, TokenUserDTO userDTO) {
        DevelopModule developModule = new DevelopModule();
        BeanUtils.copyProperties(developAddParam, developModule);
        developModule.setCreatedBy(userDTO.getUserId());
        developModule.setCreatedName(userDTO.getName());
        developModule.setUpdatedBy(userDTO.getUserId());
        developModule.setUpdatedName(userDTO.getName());
        developModuleMapper.insertSelective(developModule);
    }

    @Override
    public void editModule(DevelopAddParam developAddParam, TokenUserDTO userDTO) {
        DevelopModule developModule = new DevelopModule();
        BeanUtils.copyProperties(developAddParam, developModule);
        developModule.setUpdatedBy(userDTO.getUserId());
        developModule.setUpdatedName(userDTO.getName());
        developModuleMapper.updateDevelopModule(developModule);
    }

    @Override
    public long countModuleByCode(String moduleCode) {
        DevelopModuleExample moduleExample = new DevelopModuleExample();
        moduleExample.createCriteria().andModuleCodeEqualTo(moduleCode);
        return developModuleMapper.countByExample(moduleExample);
    }
}
