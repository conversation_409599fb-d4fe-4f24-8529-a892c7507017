package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.ForestService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.feign.ForestFeignClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ForestServiceImpl implements ForestService {
    private final Logger logger = LoggerFactory.getLogger(ForestServiceImpl.class);

    @Autowired
    private ForestFeignClient forestFeignClient;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 集团属性
     */
    private static final Integer COMPANY_RANK = 1;
    /**
     * 连锁属性
     */
    private static final Integer BUSINESS_RANK = 2;

    @Override
    public List<AuctionSpuBaseInfo> batchFindSpuProperty(List<String> goodsNoList, Long businessId) {
        try {
            List<AuctionSpuBaseInfo> response = Lists.newArrayList();
            if (CollectionUtils.isEmpty(goodsNoList)) {
                logger.warn("|getAuctionSpuBaseInfo|参数有误,商品编码不能为空！");
                return response;
            }
            List<String> goodsNoKey = goodsNoList.stream().map(x -> String.format(RedisConstant.SCIB_BUSINESS_GOODS_SPU_PROPERTY,businessId,x)).collect(Collectors.toList());
            List<String> cacheResult = get(goodsNoKey);
            List<String> noCacheGoods = Lists.newArrayList();

            if (cacheResult == null) {
                noCacheGoods = goodsNoList;
            } else {
                for (int i = 0; i < cacheResult.size(); i++) {
                    String result = cacheResult.get(i);
                    String goodsNo = goodsNoList.get(i);
                    if (StringUtils.isBlank(result)) {
                        noCacheGoods.add(goodsNo);
                        continue;
                    }
                    AuctionSpuBaseInfo auctionSpuBaseInfo = JSON.parseObject(result, AuctionSpuBaseInfo.class);
                    response.add(auctionSpuBaseInfo);
                }
            }
            List<AuctionSpuBaseInfo> noCacheSpuBaseInfos = null;
            if (CollectionUtils.isNotEmpty(noCacheGoods)) {
                noCacheSpuBaseInfos = getAuctionSpuBaseInfos(noCacheGoods, businessId);
                response.addAll(noCacheSpuBaseInfos);
            }
            if (CollectionUtils.isNotEmpty(noCacheSpuBaseInfos)) {
                Map<String, Object> map = noCacheSpuBaseInfos.stream().collect(Collectors.toMap(info -> String.format(RedisConstant.SCIB_BUSINESS_GOODS_SPU_PROPERTY,businessId, info.getGoodsNo()), info -> info, (v1, v2) -> v2));
                batchSetCache(map);
            }
            return response;
        } catch (Exception e) {
            logger.error("连锁商品编码获取商品详情信息失败:", e);
            throw e;
        }
    }

    private List<AuctionSpuBaseInfo> getAuctionSpuBaseInfos(List<String> goodsNoList, Long businessId) {
        try {
            List<AuctionSpuBaseInfo> resultList = new ArrayList<>();
            List<List<String>> partition = Lists.partition(goodsNoList, 50);
            for (List<String> paramGoodsNos : partition) {
                logger.info("调用forest根据商品编码,连锁编码获取商品详情 goodsNoList -> {}, businessId={}", JSON.toJSONString(paramGoodsNos), businessId);
                ResponseEntity<List<AuctionSpuBaseInfo>> responseEntity = forestFeignClient.batchFindSpuProperty(paramGoodsNos, businessId);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {

                    throw new BusinessErrorException("没有获取到商品详情信息");
                }
//                logger.info("调用forest根据商品编码,连锁编码获取商品详情 response -> {}", JSON.toJSONString(responseEntity.getBody()));
                resultList.addAll(responseEntity.getBody());
                try {
                    Thread.sleep(50L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            return resultList;

        } catch (BusinessErrorException e) {
            logger.warn("调用forest根据商品编码,连锁编码获取商品详情信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用forest根据商品编码,连锁编码获取商品详情信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<CategoryDTO> queryCategoryListByIdsAndLevel(List<Long> categoryIds, String categoryLevel) {
        try {
            CategoryQueryDTO categoryQueryDTO = new CategoryQueryDTO();
            categoryQueryDTO.setCategoryIds(categoryIds);
            categoryQueryDTO.setCategoryLevel(categoryLevel);
            logger.info("调用forest根据根据类目ids和类目级别批量查询分类基本信息 categoryIscmDTO -> {}", JSON.toJSONString(categoryQueryDTO));
            ResponseEntity<List<CategoryDTO>> responseEntity = forestFeignClient.queryCategoryListByIdsAndLevel(categoryQueryDTO);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有获取到类目信息");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e){
            logger.warn("调用forest根据根据类目ids和类目级别批量查询分类基本信息失败:", e);
            throw e;
        } catch (Exception e){
            logger.error("调用forest根据根据类目ids和类目级别批量查询分类基本信息失败:", e);
            throw e;
        }
    }

    @Override
    public Map<String, Map<String, String>> querySpuProperties(List<String> propertyNames, List<String> goodsCodes, Long businessId) {
        try {
            Map<String, Map<String, String>> propMap = new HashMap<>();
            Lists.partition(goodsCodes, Constants.INSERT_MAX_SIZE).forEach(goods -> {
                List<PropertyParamDTO> param = new ArrayList<>();
                List<PropertyParamDTO> propertyParamDtos = goods.stream().map(v -> {
                    PropertyParamDTO property = new PropertyParamDTO();
                    List<PropertyParamRankDto> rankDtos = propertyNames.stream().distinct().map(p -> {
                        PropertyParamRankDto rankDto = new PropertyParamRankDto();
                        rankDto.setRank(null == businessId ? COMPANY_RANK : BUSINESS_RANK);
                        rankDto.setProperty(p);
                        return rankDto;
                    }).collect(Collectors.toList());
                    property.setParamRankDtos(rankDtos);
                    property.setBusinessId(businessId);
                    property.setGoodsNo(v);
                    return property;
                }).collect(Collectors.toList());
                logger.info("调用forest获取商品属性 param : {}", JSON.toJSONString(propertyParamDtos));
                ResponseEntity<Map<String, Map<String, String>>> responseEntity = forestFeignClient.querySpuProperties(propertyParamDtos);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                    throw new BusinessErrorException("没有获取到商品属性信息");
                }
                propMap.putAll(responseEntity.getBody());
            });
            return propMap;
        } catch (BusinessErrorException e){
            logger.warn("调用forest根据获取商品属性失败:", e);
            throw e;
        } catch (Exception e){
            logger.error("调用forest根据获取商品属性失败:", e);
            throw e;
        }
    }

    @Override
    public Map<Long, String> getPathsByIds(List<Long> categoryIds) {
        try {
            return forestFeignClient.getPathsByIds(categoryIds);
        } catch (Exception e){
            logger.error("调用forest根据获取商品属性失败:", e);
            throw e;
        }
    }

    private void batchSetCache(Map<String, Object> map) {
        if (MapUtils.isEmpty(map)){
            return;
        }
        try {
            RBatch batch = redissonClient.createBatch();
            map.entrySet().forEach(entry -> {
                if (entry.getValue() instanceof String){
                    batch.getBucket(entry.getKey()).setAsync(entry.getValue(),1 * 24L,TimeUnit.HOURS);
                }else {
                    batch.getBucket(entry.getKey()).setAsync(JSON.toJSONString(entry.getValue()),1 * 24L,TimeUnit.HOURS);
                }
            });
            batch.execute();
        }catch (Exception e){
            logger.error("=======> batchSetCache exception",e);
        }
    }

    private List<String> get(List<String> keys) {
        try {
            RBatch batch = redissonClient.createBatch();
            keys.forEach((key)->{
                batch.getBucket(key).getAsync();
            });
            BatchResult execute = batch.execute();
            return (List<String>) execute.getResponses();
        } catch (Exception e) {
            logger.error("=====>[reids] error", e);
        }
        return null;
    }

}
