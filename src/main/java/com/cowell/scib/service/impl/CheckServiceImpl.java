package com.cowell.scib.service.impl;

import com.beust.jcommander.internal.Lists;
import com.cowell.permission.dto.RoleInfoDTO;
import com.cowell.permission.vo.ResourceTreeVO;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.*;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.CheckService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.OrgSimpleDTO;
import com.cowell.scib.service.dto.rule.HotGoodsImportDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/27 16:46
 */
@Slf4j
@Service
public class CheckServiceImpl implements CheckService {

    @Autowired
    private PermissionService permissionService;
    @Value("${scib.smartgoods.menu:/DigitalGoods/IndexMenu}")
    private String smartgoodsMenu;

    @Override
    public boolean fullPermByOrgId(Long orgId, Long userId) {
        if(Objects.isNull(orgId) || Objects.isNull(userId)){
            throw new AmisBadRequestException(ErrorCodeEnum.INVALID_TOKEN);
        }
        List<OrgSimpleDTO> orgSimpleDTOList = permissionService.getUserDataScopeChildOrgByOrgId(userId, Lists.newArrayList(orgId), true);
        List<OrgSimpleDTO> finaleOrgList = orgSimpleDTOList.stream().filter(v->OrgTypeEnum.PLATFORM.getCode().equals(v.getType())&&v.getIsFullScope() == 1).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(finaleOrgList);
    }
    @Override
    public boolean fullPermByOrgIdWhitResourceId(Long orgId, Long userId, Long resourceId) {
        if(Objects.isNull(orgId) || Objects.isNull(userId) || Objects.isNull(resourceId)){
            throw new AmisBadRequestException(ErrorCodeEnum.INVALID_TOKEN);
        }
        List<OrgSimpleDTO> orgSimpleDTOList = permissionService.getUserDataScopeChildOrgByOrgIdWithResourceId(userId, Lists.newArrayList(orgId), resourceId,true);
        List<OrgSimpleDTO> finaleOrgList = orgSimpleDTOList.stream().filter(v->OrgTypeEnum.PLATFORM.getCode().equals(v.getType())&&v.getIsFullScope() == 1).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(finaleOrgList);
    }

    @Override
    public List<ResourceTreeVO> getUserTreeMap(Long userId) {
        List<ResourceTreeVO> resourceTreeVOList = permissionService.getSubMenuTree(userId);
        List<ResourceTreeVO> fatherResourceTreeVOList = resourceTreeVOList.stream().filter(v-> smartgoodsMenu.equals(v.getAction())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fatherResourceTreeVOList)){
            return Lists.newArrayList();
        }
        List<ResourceTreeVO> chileResourceTree = fatherResourceTreeVOList.stream().findFirst().get().getChild();
        if(CollectionUtils.isEmpty(chileResourceTree)){
            return Lists.newArrayList();
        }
        return chileResourceTree;
    }
    @Override
    public List<ResourceTreeVO> getUserTreeMapWhitResourceId(Long userId, Long resourceId) {
        List<ResourceTreeVO> resourceTreeVOList = permissionService.getSubMenuTreeWhitResourceId(userId, resourceId);
        List<ResourceTreeVO> fatherResourceTreeVOList = resourceTreeVOList.stream().filter(v-> smartgoodsMenu.equals(v.getAction())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fatherResourceTreeVOList)){
            return Lists.newArrayList();
        }
        List<ResourceTreeVO> chileResourceTree = fatherResourceTreeVOList.stream().findFirst().get().getChild();
        if(CollectionUtils.isEmpty(chileResourceTree)){
            return Lists.newArrayList();
        }
        return chileResourceTree;
    }

    @Override
    public boolean judgeActionExit(String action, Long userId) {
        List<ResourceTreeVO> chileResourceTree = getUserTreeMap(userId);
        String permAction = DicApiEnum.getActionByCode(action);
        return chileResourceTree.stream().filter(v-> !StringUtils.isEmpty(v.getAction())&&permAction.equals(v.getAction())).count()>0;
    }
    @Override
    public boolean judgeActionExitWithResourceId(String action, Long userId, Long resourceId) {
        List<ResourceTreeVO> chileResourceTree = getUserTreeMapWhitResourceId(userId, resourceId);
        String permAction = DicApiEnum.getActionByCode(action);
        return chileResourceTree.stream().filter(v-> !StringUtils.isEmpty(v.getAction())&&permAction.equals(v.getAction())).count()>0;
    }

    @Override
    public boolean judgeUserRoleExit(String roleCode, Long userId) {
        Map<Long,List<RoleInfoDTO>>  roleInfoDTOMap = permissionService.getUserRoleById(Collections.singletonList(userId));
        List<RoleInfoDTO> roleInfoDTOList = roleInfoDTOMap.get(userId);
        long count = roleInfoDTOList.stream().filter(v->v.getCode().equals(roleCode)).count();
        return count>0;
    }

    @Override
    public Boolean judgeEditAble(Long orgId, String action, Long userId) {
        log.info("judgeEditAble|orgId:{}.action:{}.userId:{}.", orgId, action, userId);
        if(judgeUserRoleExit(Constants.SUPPER_ROLE, userId)){
            log.info("judgeEditAble|超管返回true");
            return true;
        }

        if(!fullPermByOrgId(orgId, userId)){
            log.info("judgeEditAble|平台部分权限返回false");
            return false;
        }

        if(DicApiEnum.isTj(action) || judgeActionExit(action, userId)){
            log.info("judgeEditAble|有菜单权限或是推荐返回true");
            return true;
        }
        return false;
    }

    @Override
    public Boolean judgeEditAbleWithResource(Long orgId, String action, Long userId, Long resourceId) {
        log.info("judgeEditAbleWithResource|orgId:{}.action:{}.userId:{}.resourceId:{}", orgId, action, userId, resourceId);
        if(judgeUserRoleExit(Constants.SUPPER_ROLE, userId)){
            log.info("judgeEditAble|超管返回true");
            return true;
        }

        if(!fullPermByOrgIdWhitResourceId(orgId, userId, resourceId)){
            log.info("judgeEditAble|平台部分权限返回false");
            return false;
        }

        if(DicApiEnum.isTj(action) || judgeActionExitWithResourceId(action, userId, resourceId)){
            log.info("judgeEditAble|有菜单权限或是推荐返回true");
            return true;
        }
        return false;
    }

    @Override
    public int operatePerm(Long orgId, String action, Long userId) {
        log.info("singlePerm|orgId:{}.action:{}.userId:{}.", orgId, action, userId);
        if(judgeUserRoleExit(Constants.SUPPER_ROLE, userId)){
            return RulePermEnum.P_SUPPER.getCode();
        }else if(fullPermByOrgId(orgId, userId) && (judgeActionExit(action, userId) || DicApiEnum.isTj(action))){
            return RulePermEnum.P_EDIT.getCode();
        }
        return RulePermEnum.P_UNEDIT.getCode();
    }

    @Override
    public int operatePermWhitResource(Long orgId, String action,  Long resourceId, Long userId) {
        log.info("singlePerm|orgId:{}.action:{}.userId:{}.resourceId:{}.", orgId, action, userId, resourceId);
        if(judgeUserRoleExit(Constants.SUPPER_ROLE, userId)){
            return RulePermEnum.P_SUPPER.getCode();
        }else if(fullPermByOrgIdWhitResourceId(orgId, userId, resourceId) && (judgeActionExitWithResourceId(action, userId, resourceId) || DicApiEnum.isTj(action))){
            return RulePermEnum.P_EDIT.getCode();
        }
        return RulePermEnum.P_UNEDIT.getCode();
    }

    @Override
    public String checkHotGoodsParam(HotGoodsImportDTO hotGoodsImportDTO) {
        if(!HotSourceEnum.messageExit(hotGoodsImportDTO.getGoodsSource())){
            return ErrorCodeEnum.HOT_SOURCE_WRONG.getMsg();
        }
        if(!Constants.YEAR_PATTERN.matcher(hotGoodsImportDTO.getGoodsYear()).matches()){
            return ErrorCodeEnum.HOT_YEAR_WRONG.getMsg();
        }
        if(!HotTimeRelationEnum.exit(hotGoodsImportDTO.getGoodsTimeDimension())){
            return ErrorCodeEnum.HOT_DIMENSION_WRONG.getMsg();
        }
        if(!HotTimeRelationEnum.exit(hotGoodsImportDTO.getGoodsTimeFrame())){
            return ErrorCodeEnum.HOT_FRAME_WRONG.getMsg();
        }
        if(!HotTimeRelationEnum.dimensionAndFrameExit(hotGoodsImportDTO.getGoodsTimeDimension(), hotGoodsImportDTO.getGoodsTimeFrame())){
            return ErrorCodeEnum.HOT_DIMENSION_FRAME_WRONG.getMsg();
        }
        return "";
    }


    @Override
    public Boolean isUpNumber(String num) {
        if(StringUtils.isEmpty(num)){
            return false;
        }
        return num.matches(Constants.UP_NUM);
    }
}
