package com.cowell.scib.service.impl;

import com.cowell.scib.entityDgms.CommonDictionary;
import com.cowell.scib.entityDgms.CommonEnums;
import com.cowell.scib.mapperDgms.CommonDictionaryMapper;
import com.cowell.scib.mapperDgms.CommonEnumsMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.RuleConfigService;
import com.cowell.scib.service.param.rule.RuleDictParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:22
 */
@Slf4j
@Service
public class RuleConfigServiceImpl implements RuleConfigService {

    @Autowired
    private CommonDictionaryMapper dictionaryMapper;
    @Autowired
    private CommonEnumsMapper commonEnumsMapper;

    @Override
    public void addOrEdit(RuleDictParam ruleDictParam) {
        try {
            CommonDictionary commonDictionary = new CommonDictionary();
            BeanUtils.copyProperties(ruleDictParam, commonDictionary);
            dictionaryMapper.insertSelective(commonDictionary);
        } catch (BeansException e) {
            log.warn("RuleConfigServiceImpl|addOrEdit|error." , e);
        }
    }

    @Override
    public void bindingStoreTag(Long id, Long tagId, Integer type) {
        try {
            if (null == type) {
                throw new AmisBadRequestException("未知的操作类型");
            }
            CommonEnums commonEnums = commonEnumsMapper.selectByPrimaryKey(id);
            if (null == commonEnums) {
                throw new AmisBadRequestException("没有找到对应的枚举记录");
            }
            if (type.equals(1)) {
                if (null == tagId) {
                    throw new AmisBadRequestException("请选择需要绑定的标签");
                }
                commonEnums.setTagId(tagId);
            } else {
                commonEnums.setTagId(null);
            }
            commonEnumsMapper.updateByPrimaryKey(commonEnums);
        } catch (Exception e) {
            log.error("绑定标签失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }
}
