package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.BundlingTaskStoreDetailMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtendExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.iscm.GoodsInfoParams;
import com.cowell.scib.service.dto.iscm.GoodsInfoResponse;
import com.cowell.scib.service.param.BundlStoreConfirmParam;
import com.cowell.scib.service.param.BundlTaskAddParam;
import com.cowell.scib.service.param.BundlUpdateStoreConfirmParam;
import com.cowell.scib.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/13 15:29
 */
@Slf4j
@Service
public class BundlTaskWriteServiceImpl extends BundlTaskStepService<BundlTaskAddParam> implements BundlTaskWriteService {

    @Autowired
    private SendService sendService;
    @Autowired
    private TagService tagService;
    @Autowired
    protected IscmService iscmService;
    @Autowired
    private BundlTaskService bundlTaskService;
    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;
    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtMapper;
    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;
    @Autowired
    private BundlingTaskStoreDetailMapper bundlingTaskStoreDetailMapper;
    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;
    @Autowired
    private BundlingTaskDetailExtendExtMapper bundlingTaskDetailExtendExtMapper;
    @Autowired
    private NecessaryContentsService necessaryContentsService;
    @Value("${scib.task.bdp.environment:300}")
    private int environment;
    @Value("${scib.task.bdp.commit.interval:08:00-20:00}")
    private String commitInterval;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object add(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO) {
        try {
            bundlTaskAddParam.setUserDTO(userDTO);
            processRequest(bundlTaskAddParam);
            //只有新店推荐任务,且不需要暂存需要直接发送大数据走这里.
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlTaskAddParam.getTaskType()) && !bundlTaskAddParam.getSaveAble()){
               //todo 可以重新写一个新店推荐类型的发送BDP任务方法 目前是一推判断
                BdpResponseDTO bdpResponseDTO = commitTaskToBdp(bundlTaskAddParam.getTaskId(), userDTO);
                log.info("BundlTaskWriteServiceImpl|commitTaskToBdp|发送任务结果:{}",bdpResponseDTO);
                return bdpResponseDTO;
            }
        } catch (Exception e) {
            log.warn("BundlTaskWriteServiceImpl|add|失败，", e);
            throw e;
        }
        return bundlTaskAddParam.getTaskId();
    }

    @Override
    public void updateMdmStore(BundlStoreConfirmParam confirmParam, TokenUserDTO userDTO){
        if(Objects.isNull(confirmParam.getPlateOrgId()) || Objects.isNull(confirmParam.getTaskId())){
            throw new BusinessErrorException("参数错误");
        }
        Long taskId = confirmParam.getTaskId();
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if(Objects.isNull(bundlingTaskInfo)){
            throw new BusinessErrorException(new StringBuilder().append("不存在的任务id：{}。").append(confirmParam.getTaskId()).toString());
        }
        try {
            List<Long> bundlStoreIdList = null;
            if(Objects.nonNull(confirmParam.getSelectId())){
                BundlingTaskInfo taskInfo = new BundlingTaskInfo();
                taskInfo.setId(confirmParam.getTaskId());
                taskInfo.setSelectId(confirmParam.getSelectId());
                bundlingTaskInfoMapper.updateByPrimaryKeySelective(taskInfo);
                bundlStoreIdList = tagService.getSelectStoreIdList(confirmParam.getSelectId());
            }else{
                //没有selectId不处理标识
                log.debug("未使用门店选择器");
                return;
            }
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);

            List<Long> finalBundlStoreIdList = bundlStoreIdList;
            bundlingTaskStoreDetailList.stream().forEach(v->{

                if((CollectionUtils.isNotEmpty(finalBundlStoreIdList) && finalBundlStoreIdList.contains(v.getStoreId()))){
                    v.setBundlAdviceAble(BundlBoolEnum.YES.getCode());
                    v.setBundlConfirmAble(BundlBoolEnum.YES.getCode());
                }else{
                    v.setBundlAdviceAble(BundlBoolEnum.NO.getCode());
                    v.setBundlConfirmAble(BundlBoolEnum.NO.getCode());
                }
            });

            if(CollectionUtils.isNotEmpty(bundlingTaskStoreDetailList)){
                for (List<BundlingTaskStoreDetail> list : com.google.common.collect.Lists.partition(bundlingTaskStoreDetailList, 200)) {
                    bundlingTaskStoreDetailExtendMapper.batchUpdateType(list);
                }
            }
        } catch (Exception e) {
            log.warn("updateMdmStore|更新异常。", e);
            throw e;
        }
    }

    @Override
    public void addTaskDetail(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO) {
        Map<String, BundlTaskDetailDTO> taskDetailDTOMap = bundlTaskAddParam.getDetailMap();
        if(MapUtils.isEmpty(taskDetailDTOMap)){
            log.debug("addTaskDetail|没有需要保存的字典明细");
            return;
        }
//        List<BundlingTaskDetail> detailList = new ArrayList<>();
//        taskDetailDTOMap.forEach((dictCode, detailDTO)->{
//            List<String> perprotyValueList = detailDTO.getPerprotyValueList();
//            if(CollectionUtils.isNotEmpty(perprotyValueList)){
//                for(String value : perprotyValueList){
//                    detailList.add(buildTaskDetailDTO(bundlTaskAddParam, detailDTO, value, userDTO));
//                }
//            }
//        });

        Map<String,List<BundlingTaskDetailExtend>> configOrgDetailExtendMap=new HashMap<>();
        List<BundlingTaskDetail> detailList = buildTaskDetail(bundlTaskAddParam, userDTO, configOrgDetailExtendMap);
        if(CollectionUtils.isNotEmpty(detailList)){
            bundlingTaskDetailExtMapper.batchInsert(detailList);
        }
        if (org.apache.commons.collections.MapUtils.isEmpty(configOrgDetailExtendMap)){
            return;
        }
        List<BundlingTaskDetail> configOrgDetailsSave = bundlingTaskDetailExtMapper.selectDetailByTaskId(bundlTaskAddParam.getTaskId());
        if (CollectionUtils.isEmpty(configOrgDetailsSave)){
            return;
        }
        Map<String, BundlingTaskDetail> tempConfigOrgDetailMap = configOrgDetailsSave.stream().filter(v -> Objects.equals(v.getPerprotyType(), Constants.COLLECTION)).collect(Collectors.toMap(BundlingTaskDetail::getDictCode, Function.identity(), (k1, k2) -> k1));
        if (org.apache.commons.collections.MapUtils.isEmpty(tempConfigOrgDetailMap)){
            return;
        }

        List<BundlingTaskDetailExtend> configOrgDetailExtendList = new ArrayList<>();
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.STORE_WHITE_LIST))){
            List<BundlingTaskDetailExtend> storeWhiteListExtends = configOrgDetailExtendMap.get(Constants.STORE_WHITE_LIST);
            if (CollectionUtils.isNotEmpty(storeWhiteListExtends)){
                storeWhiteListExtends.stream().forEach(v->{v.setTaskDetailId(tempConfigOrgDetailMap.get(Constants.STORE_WHITE_LIST).getId());});
                configOrgDetailExtendList.addAll(storeWhiteListExtends);
            }

        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.STORE_BLACK_LIST))){
            List<BundlingTaskDetailExtend> storeBlackListExtends = configOrgDetailExtendMap.get(Constants.STORE_BLACK_LIST);
            if (CollectionUtils.isNotEmpty(storeBlackListExtends)){
                storeBlackListExtends.stream().forEach(v->{v.setTaskDetailId(tempConfigOrgDetailMap.get(Constants.STORE_BLACK_LIST).getId());});
                configOrgDetailExtendList.addAll(storeBlackListExtends);
            }
        }
        if (Objects.nonNull(tempConfigOrgDetailMap.get(Constants.GOODS_BLACK_LIST))){
            List<BundlingTaskDetailExtend> storeBlackListExtends = configOrgDetailExtendMap.get(Constants.GOODS_BLACK_LIST);

            if(CollectionUtils.isNotEmpty(storeBlackListExtends)){
                storeBlackListExtends.stream().forEach(v->{v.setTaskDetailId(tempConfigOrgDetailMap.get(Constants.GOODS_BLACK_LIST).getId());});
                configOrgDetailExtendList.addAll(storeBlackListExtends);
            }

        }
        if(CollectionUtils.isNotEmpty(configOrgDetailExtendList)){
            bundlingTaskDetailExtendExtMapper.batchInsert(configOrgDetailExtendList);
        }

    }

    @Override
    public void addTaskDetailExtend(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO) {
        Map<String, BundlTaskDetailDTO> taskDetailDTOMap = bundlTaskAddParam.getDetailMap();
        if(MapUtils.isEmpty(taskDetailDTOMap)){
            log.debug("addTaskDetailExtend|没有需要保存的字典明细扩展");
            return;
        }
    }

    @Override
    public void addMdmStore(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO) {
        Long taskId = bundlTaskAddParam.getTaskId();
        Byte taskType = bundlTaskAddParam.getTaskType();
        Map<String, BundlTaskDetailDTO> dictDetailMap = Objects.isNull(bundlTaskAddParam.getDetailMap())?new HashMap<>():bundlTaskAddParam.getDetailMap();
        List<Long> companyOrgIdList = Lists.newArrayList();
        Map<String, List<String>> dictMap = new HashMap<>();
        if(!BundlTaskTypeEnum.PLATE_BUNDL.getCode().equals(taskType)){
            BundlTaskDetailDTO companyDetailDTO = dictDetailMap.get(BudnlTaskDetailDicEnum.BUSINESS.getCode());
            if(Objects.nonNull(companyDetailDTO)&&CollectionUtils.isNotEmpty(companyDetailDTO.getPerprotyValueList())){
                companyOrgIdList = companyDetailDTO.getPerprotyValueList().stream().filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            }
        }
        if(BundlTaskTypeEnum.STORE_BUNDL.getCode().equals(taskType)){
            buildDictMap(BudnlTaskDetailDicEnum.STORE.getCode(), dictDetailMap, dictMap);
            buildDictMap(BudnlTaskDetailDicEnum.ZS.getCode(), dictDetailMap, dictMap);
        }
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList=new ArrayList<>();
        if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(taskType)){
            Optional<MdmStoreExDTO> storeExtInfoByStoreId = CacheVar.getStoreExtInfoByStoreId(bundlTaskAddParam.getSelectStoreId());
            mdmStoreBaseDTOList = storeExtInfoByStoreId.<List<MdmStoreBaseDTO>>map(Collections::singletonList).orElse(Collections.emptyList());
        }else {
            mdmStoreBaseDTOList = bundlTaskService.selectMdmStoreFilterStoreType(bundlTaskAddParam.getPlateOrgId(), userDTO.getUserId(), companyOrgIdList, dictMap);
        }
        log.info("临时日志: MDM门店信息接口返回|{}", JSONObject.toJSONString(mdmStoreBaseDTOList));
        if(CollectionUtils.isEmpty(mdmStoreBaseDTOList)){
            log.warn("MDM门店信息接口返回均为空，不能入库");
            return;
        }
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if(Objects.isNull(bundlingTaskInfo)){
            throw new BusinessErrorException(new StringBuilder().append("不存在的任务id：{}。").append(taskId).toString());
        }
        List<BundlingTaskStoreDetail> storeDetailList = Lists.newArrayList();
        mdmStoreBaseDTOList.stream().forEach(v->{
            MdmStoreExDTO mdmStoreExDTO = necessaryContentsService.getCopyStoreInfo(v.getStoreId());
            log.info("临时日志: 获取门店信息接口返回|{}", JSONObject.toJSONString(mdmStoreExDTO));
            if(!BundlTaskTypeEnum.STORE_BUNDL.getCode().equals(taskType) && !BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(taskType)){
                //如果不是店型必备(且不是新店推荐目录组货)，门店属性里的组货店型必须不为空，否则过滤掉
                if(StringUtils.isBlank(mdmStoreExDTO.getStoreTypeCode())){
                    log.info("组货店型为空|storeNo:{}", v.getStoreNo());
                    return;
                }
            }
            BundlingTaskStoreDetail taskStoreDetail = new BundlingTaskStoreDetail();
            BeanUtils.copyProperties(v, taskStoreDetail);
            taskStoreDetail.setTaskId(bundlingTaskInfo.getId());
            taskStoreDetail.setTaskCode(bundlingTaskInfo.getTaskCode());
            taskStoreDetail.setCompanyOrgId(bundlTaskAddParam.getBusinessOrgId());
            taskStoreDetail.setCompanyCode(v.getComId());
            taskStoreDetail.setCompanyName(mdmStoreExDTO.getBusinessName());
            taskStoreDetail.setStoreOrgId(v.getStoreOrgId());
            taskStoreDetail.setStoreCode(v.getStoreNo());
            taskStoreDetail.setStoreName(v.getStoreShotName());
            taskStoreDetail.setBundlStore(mdmStoreExDTO.getStoreType());
            taskStoreDetail.setPlatStoreTypeCode(mdmStoreExDTO.getPlatStoreTypeCode());
            taskStoreDetail.setZsStoreTypeCode(mdmStoreExDTO.getZsStoreTypeCode());
            taskStoreDetail.setStoreTypeCode(mdmStoreExDTO.getStoreTypeCode());
            taskStoreDetail.setBundlAdviceAble(BundlBoolEnum.NO.getCode());
            taskStoreDetail.setBundlConfirmAble(BundlBoolEnum.NO.getCode());
            JSONObject jsonObject = new JSONObject();
            if(Objects.nonNull(v.getExtend())){
                jsonObject = JSONObject.parseObject(v.getExtend());
            }
            log.info("临时日志: taskStoreDetail|{}", JSONObject.toJSONString(taskStoreDetail));
            buildXDStoreExtendInfo(bundlTaskAddParam, taskType, mdmStoreExDTO, jsonObject, taskStoreDetail);
            taskStoreDetail.setExtend(JSONObject.toJSONString(jsonObject));
            taskStoreDetail.setCreatedBy(userDTO.getUserId());
            taskStoreDetail.setCreatedName(userDTO.getName());
            taskStoreDetail.setUpdatedBy(userDTO.getUserId());
            taskStoreDetail.setUpdatedName(userDTO.getName());
            storeDetailList.add(taskStoreDetail);
        });

        if(CollectionUtils.isNotEmpty(storeDetailList)){
            for (List<BundlingTaskStoreDetail> list : com.google.common.collect.Lists.partition(storeDetailList, 200)) {
                bundlingTaskStoreDetailExtendMapper.batchInsert(list);
            }
        }
    }

    /**
     * 构建扩展信息
     *
     * @param bundlTaskAddParam 捆绑任务添加参数
     * @param taskType 任务类型
     * @param mdmStoreExDTO 门店扩展信息DTO
     * @param jsonObject JSON对象
     * @param taskStoreDetail 捆绑任务门店详情
     */
    private void buildXDStoreExtendInfo(BundlTaskAddParam bundlTaskAddParam, Byte taskType, MdmStoreExDTO mdmStoreExDTO, JSONObject jsonObject, BundlingTaskStoreDetail taskStoreDetail) {
        if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(taskType)){
            if(Objects.nonNull(bundlTaskAddParam.getBlackListParamUniqueMark())){
                try {
                    GoodsInfoParams params= new GoodsInfoParams();
                    params.setPage(0);
                    params.setPageSize(1L);
                    params.setParamUniqueMark(bundlTaskAddParam.getBlackListParamUniqueMark());
                    params.setParamScope(7);
                    if(Objects.isNull(mdmStoreExDTO.getCompanyOrgId())){
                        throw new BusinessErrorException("组织信息不存在");
                    }
                    params.setOrgId(mdmStoreExDTO.getCompanyOrgId());
                    log.info("临时日志:  解析黑名单参数|{}", JSONObject.toJSONString(params));
                    GoodsInfoResponse paramGoodsInfos = iscmService.getParamGoodsInfos(params);
                    log.info("临时日志:  解析黑名单结果|{}", JSONObject.toJSONString(paramGoodsInfos));
                    if(Objects.nonNull(paramGoodsInfos) && CollectionUtils.isNotEmpty(paramGoodsInfos.getAllGoodsNos())){
                        jsonObject.put("goodsBlackList", String.join(",", paramGoodsInfos.getAllGoodsNos()));
                    }
                }catch (Exception e){
                    log.error("解析黑名单参数失败,跳过黑名单", e);
                }
            }
            taskStoreDetail.setBundlAdviceAble(BundlBoolEnum.YES.getCode());
            taskStoreDetail.setBundlConfirmAble(BundlBoolEnum.YES.getCode());
        }
    }

    @Override
    public void updateTaskStatusByTaskId(Long taskId, Byte targetTaskStatus){
        BundlingTaskInfo bundlingTaskInfo = new BundlingTaskInfo();
        bundlingTaskInfo.setTaskStatus(targetTaskStatus);
        bundlingTaskInfo.setId(taskId);
        bundlingTaskInfoMapper.updateByPrimaryKeySelective(bundlingTaskInfo);
    }

    @Override
    public void updateBundlConfirm(BundlUpdateStoreConfirmParam confirmParam, TokenUserDTO userDTO) {
        BundlingTaskStoreDetail bundlingTaskStoreDetail = new BundlingTaskStoreDetail();
        bundlingTaskStoreDetail.setId(confirmParam.getId());
        bundlingTaskStoreDetail.setBundlConfirmAble(confirmParam.getTargetBundlConfirmAble()?(byte)1:(byte)0);
        bundlingTaskStoreDetailMapper.updateByPrimaryKeySelective(bundlingTaskStoreDetail);
    }

    @Override
    public BdpResponseDTO commitTaskToBdp(Long taskId, TokenUserDTO userDTO) {
        if(Objects.isNull(taskId)){
            return null;
        }
        try {
            log.debug("commitTaskToBdp|taskId:{}.", taskId);
            BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
            if(Objects.isNull(bundlingTaskInfo)){
                throw new BusinessErrorException("无此任务，请检查！" + taskId);
            }
            //先写if 判断如果再有新的任务类型 考虑重构该方法
            if(!BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())  && !DateUtils.betweenTime(commitInterval)){
                log.warn("commitTaskToBdp|不再提交允许区间内：{}，任务ID：{}。",commitInterval, taskId);
                BdpResponseDTO responseDTO = new BdpResponseDTO();
                responseDTO.setCode(ResponseCodeEnum.INTERVAL.getCode());
                responseDTO.setMsg(String.format(ResponseCodeEnum.INTERVAL.getMessage(), commitInterval));
                unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.COMMIT_FAIL, userDTO);
                return responseDTO;
            }
            if(!(BundlTaskStatusEnum.SAVE.getCode() == bundlingTaskInfo.getTaskStatus()) && !(BundlTaskStatusEnum.CREATING.getCode() == bundlingTaskInfo.getTaskStatus())){
                throw new BusinessErrorException("此任务状态不能提交，请检查！" + taskId);
            }
            List<TaskStoreDTO> taskStoreDTOList = createTaskStore(taskId);
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
                if(CollectionUtils.isEmpty(taskStoreDTOList)){
                    throw new BusinessErrorException("新开门店不能为空，请检查！" + taskId);
                }
                List<String> storeCodeList = bundlTaskService.queryAlreadyBundlStoreListAndTaskType(bundlingTaskInfo.getOrgId(),BundlingTaskInfo.getDefaultTaskTypeListByTaskType(bundlingTaskInfo.getTaskType()),taskStoreDTOList.get(0).getStoreCode());
                if(CollectionUtils.isNotEmpty(storeCodeList)){
                    BdpResponseDTO responseDTO = new BdpResponseDTO();
                    responseDTO.setCode(ResponseCodeEnum.STORE.getCode());
                    responseDTO.setMsg("该新店经营目录推荐任务正在计算中，任务号为:"+storeCodeList.get(0)+"，请耐心等待");
                    bundlingTaskInfoMapper.deleteByPrimaryKey(taskId);
                    bundlingTaskStoreDetailExtendMapper.deleteDetailByTaskId(taskId);
                    return responseDTO;
                }
            }else {
                // 1,2,3,4 是一组货任务 ，5是新店推荐目录  暂时这样写，后续需要优化
                List<String> storeCodeList = bundlTaskService.queryAlreadyBundlStoreListAndTaskType(bundlingTaskInfo.getOrgId(),BundlingTaskInfo.getDefaultTaskTypeListByTaskType(null),null);
                boolean alreadyStore = false;
                if(CollectionUtils.isNotEmpty(storeCodeList)){
                    for(TaskStoreDTO storeDTO : taskStoreDTOList){
                        if(storeCodeList.contains(storeDTO.getStoreCode())){
                            alreadyStore = true;
                            break;
                        }
                    }
                }
                if(alreadyStore){
                    BdpResponseDTO responseDTO = new BdpResponseDTO();
                    responseDTO.setCode(ResponseCodeEnum.STORE.getCode());
                    responseDTO.setMsg(ResponseCodeEnum.STORE.getMessage());
                    return responseDTO;
                }
            }
            TaskHeadDTO taskHeadDTO = createTaskHead(bundlingTaskInfo);
            BundlTaskBdpDTO bundlTaskBdpDTO = new BundlTaskBdpDTO();
            bundlTaskBdpDTO.setHead(taskHeadDTO);
            //先写if 判断如果再有新的任务类型 考虑重构该方法
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
                // 新店推荐目录
                TaskNewStoreRecommendDTO newStoreRecommendDTO=new TaskNewStoreRecommendDTO();
                BundlingTaskStoreDetail storeDetail = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId).get(0);
                newStoreRecommendDTO.setBusinessOrgId(Objects.nonNull(storeDetail.getCompanyOrgId())?storeDetail.getCompanyOrgId().toString():"");
                newStoreRecommendDTO.setPlatformOrgId(Objects.nonNull(bundlingTaskInfo.getOrgId())?bundlingTaskInfo.getOrgId().toString():"");
                newStoreRecommendDTO.setStoreSource("0");
                newStoreRecommendDTO.setStoreCode(Objects.nonNull(storeDetail.getStoreCode())?storeDetail.getStoreCode():"");
                newStoreRecommendDTO.setStoreAttr(Objects.nonNull(storeDetail.getStoreAttr())?storeDetail.getStoreAttr():"");
                if (StringUtils.isNotBlank(bundlingTaskInfo.getExtend())) {
                    BundlTaskInfoDTO bundlTaskInfoDTO = new BundlTaskInfoDTO();
                    BeanUtils.copyProperties(bundlingTaskInfo, bundlTaskInfoDTO);
                    if (StringUtils.isNotBlank(bundlingTaskInfo.getExtend())) {
                        bundlTaskInfoDTO.initXDMLParams(JSONObject.parseObject(bundlingTaskInfo.getExtend()));
                    }
                    newStoreRecommendDTO.setMaxCostPriceLimit(Objects.nonNull(bundlTaskInfoDTO.getGoodMaxCostPrice())?bundlTaskInfoDTO.getGoodMaxCostPrice().toPlainString():"");
                    if(CollectionUtils.isNotEmpty(bundlTaskInfoDTO.getSimilarStoreIdList())){
                        List<String> similarStoreCodeList = CacheVar.getStoreByStoreIdList(bundlTaskInfoDTO.getSimilarStoreIdList()).orElseGet(ArrayList::new).stream().map(OrgInfoBaseCache::getSapCode).collect(Collectors.toList());
                        newStoreRecommendDTO.setSimilarStoreIdList(CollectionUtils.isNotEmpty(similarStoreCodeList)? String.join(",", similarStoreCodeList):"");
                    }
                    JSONObject extendJson = JSONObject.parseObject(storeDetail.getExtend());
                    boolean hasBlackList = extendJson != null && extendJson.containsKey("goodsBlackList");
                    if (hasBlackList) {
                        newStoreRecommendDTO.setGoodsBlackList(StringUtils.isNotBlank(extendJson.getString("goodsBlackList"))?extendJson.getString("goodsBlackList"):"");
                    }
                    newStoreRecommendDTO.setSalesLevel(Objects.nonNull(storeDetail.getSalesLevel())?storeDetail.getSalesLevel():"");
                }
                newStoreRecommendDTO.setTradingArea(StringUtils.isNotBlank(storeDetail.getTradingArea())?storeDetail.getTradingArea():"");
                newStoreRecommendDTO.setAssortmentStoreType(StringUtils.isNotBlank(storeDetail.getBundlStore())?storeDetail.getBundlStore():"");
                CacheVar.getStoreExtInfoByStoreId(storeDetail.getStoreId()).ifPresent(storeExtInfo -> {
                    newStoreRecommendDTO.setMarPosition(StringUtils.isNotBlank(storeExtInfo.getMarPosition())?storeExtInfo.getMarPosition():"");
                    newStoreRecommendDTO.setZsStoreTypeCode(StringUtils.isNotBlank(storeExtInfo.getZsStoreTypeCode())?storeExtInfo.getZsStoreTypeCode():"");
                });
                newStoreRecommendDTO.setProvince(StringUtils.isNotBlank(storeDetail.getProvince())?storeDetail.getProvince():"");
                newStoreRecommendDTO.setCity(StringUtils.isNotBlank(storeDetail.getCity())?storeDetail.getCity():"");
                newStoreRecommendDTO.setArea(StringUtils.isNotBlank(storeDetail.getArea())?storeDetail.getArea():"");
                newStoreRecommendDTO.setAddress(StringUtils.isNotBlank(storeDetail.getAddress())?storeDetail.getAddress():"");
                log.warn("commitTaskToBdp|newStoreRecommendDTO：{}，任务ID：{}。",newStoreRecommendDTO, taskId);
                bundlTaskBdpDTO.setNewStoreRecommendDTO(newStoreRecommendDTO);
            }else {
                TaskGoodsDTO taskGoodsDTO = createTaskGoods(taskId);
                List<TaskRuleDTO> taskRuleDTOList = createTaskRule(taskId);
                TaskNecessaryGooodsDTO taskNecessaryGooodsDTODetail = createTaskNecessaryGooods(bundlingTaskInfo, taskStoreDTOList, userDTO.getUserId());
                bundlTaskBdpDTO.setStoreDetail(taskStoreDTOList);
                bundlTaskBdpDTO.setGoodsDetail(taskGoodsDTO);
                bundlTaskBdpDTO.setRuleDetail(taskRuleDTOList);
                bundlTaskBdpDTO.setNecessaryGooodsDetail(taskNecessaryGooodsDTODetail);
            }
            log.info("commitTaskToBdp|bundlTaskBdpDTO:{}.", JSONObject.toJSONString(bundlTaskBdpDTO));
            ResponseEntity<BdpResponseDTO> responseEntity = sendService.sendBdp(bundlTaskBdpDTO);
            if(Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody())){
                throw new BusinessErrorException(ErrorCodeEnum.TASK_BDP_ERROR);
            }
            BdpResponseDTO bdpResponseDTO = responseEntity.getBody();
            log.info("commitTaskToBdp|bdpResponseDTO:{}.", JSONObject.toJSONString(bdpResponseDTO));
            if(ResponseCodeEnum.ERROR.getCode().equals(bdpResponseDTO.getCode())){
                throw new BusinessErrorException(ErrorCodeEnum.TASK_BDP_ERROR);
            }
            if(ResponseCodeEnum.COMPUTING.getCode().equals(bdpResponseDTO.getCode())){
                if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
                    bdpResponseDTO.setMsg(ResponseCodeEnum.COMPUTING_XD.getMessage());
                }else {
                    bdpResponseDTO.setMsg(ResponseCodeEnum.COMPUTING.getMessage());
                }
                return bdpResponseDTO;
            }
            if(ResponseCodeEnum.SUCCESS.getCode().equals(bdpResponseDTO.getCode())){
                if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
                    bdpResponseDTO.setMsg("提交成功，预计1小时可返回经营目录推荐结果，请耐心等待");
                }else {
                    bdpResponseDTO.setMsg(String.format(ResponseCodeEnum.SUCCESS.getMessage(), bundlingTaskInfo.getTaskCode()));
                }
                unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.COMPUTING, userDTO);
            }else{
                unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.COMMIT_FAIL, userDTO);
            }
            return bdpResponseDTO;
        } catch (Exception e) {
            log.warn("commitTaskToBdp|组货任务提交异常。", e);
            throw e;
        }
    }

    /**
     * 更新状态
     * @param changeEnum
     */
    @Override
    public void unifyUpdateStasktatus(Long taskId, TaskStatusChangeEnum changeEnum, TokenUserDTO userDTO){
        BundlingTaskInfo old = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        // 如果是新店推荐组货， 作废的 conditionStatusList 更多一些
        if(Objects.nonNull(old) && BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(old.getTaskType())){
            if(changeEnum.getTargetStatus().equals(BundlTaskStatusEnum.CANCEL.getCode())){
                changeEnum = TaskStatusChangeEnum.CANCEL_XD_RECOMMEND;
            }
        }
        BundlingTaskInfo bundlingTaskInfo = new BundlingTaskInfo();
        bundlingTaskInfo.setTaskStatus(changeEnum.getTargetStatus());
        buildUserInfo(bundlingTaskInfo, changeEnum, userDTO);
        BundlingTaskInfoExample infoExample = new BundlingTaskInfoExample();
        infoExample.createCriteria().andIdEqualTo(taskId).andTaskStatusIn(changeEnum.getConditionStatusList());
        bundlingTaskInfoMapper.updateByExampleSelective(bundlingTaskInfo, infoExample);
    }

    @Override
    public void updateTaskStatus(Long taskId, Byte taskStatus, TokenUserDTO userDTO) {
        try {
            BundlingTaskInfo update = new BundlingTaskInfo();
            BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
            if(Objects.nonNull(bundlingTaskInfo) && BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
                if(taskStatus.equals(BundlTaskStatusEnum.UPDATED.getCode())){
                    update.setIssuedBy(userDTO.getUserId());
                    update.setIssuedName(userDTO.getName());
                    update.setGmtIssued(new Date());
                }
            }
            update.setTaskStatus(taskStatus);
            update.setId(taskId);
            bundlingTaskInfoMapper.updateByPrimaryKeySelective(update);
        } catch (Exception e) {
            log.warn("updateTaskStatus|warn.", e);
        }
    }

    @Override
    public void updateTaskStatusByCode(String taskCode, Byte taskStatus) {
        try {
            BundlingTaskInfo bundlingTaskInfo = new BundlingTaskInfo();
            bundlingTaskInfo.setTaskStatus(taskStatus);
            BundlingTaskInfoExample infoExample = new BundlingTaskInfoExample();
            infoExample.createCriteria().andTaskCodeEqualTo(taskCode);
            bundlingTaskInfoMapper.updateByExampleSelective(bundlingTaskInfo, infoExample);
        } catch (Exception e) {
            log.warn("updateTaskStatusByCode|warn.", e);
        }
    }

    /**
     *
     * @param bundlingTaskInfo
     * @param changeEnum
     * @param userDTO
     */
    private void buildUserInfo(BundlingTaskInfo bundlingTaskInfo, TaskStatusChangeEnum changeEnum, TokenUserDTO userDTO) {
        if(TaskStatusChangeEnum.CANCEL.equals(changeEnum)||TaskStatusChangeEnum.CANCEL_XD_RECOMMEND.equals(changeEnum)){
            bundlingTaskInfo.setCancelBy(userDTO.getUserId());
            bundlingTaskInfo.setCancelName(userDTO.getName());
            bundlingTaskInfo.setGmtCancel(new Date());
        }else if(TaskStatusChangeEnum.COMPUTING.equals(changeEnum) || TaskStatusChangeEnum.COMMIT_FAIL.equals(changeEnum)){
            bundlingTaskInfo.setCommitBy(userDTO.getUserId());
            bundlingTaskInfo.setCommitName(userDTO.getName());
            bundlingTaskInfo.setGmtCommit(new Date());
        }else if(TaskStatusChangeEnum.COMPUTED.equals(changeEnum)){
            bundlingTaskInfo.setGmtCalculated(new Date());
        }else if(TaskStatusChangeEnum.UPDATED.equals(changeEnum)){
            bundlingTaskInfo.setIssuedBy(userDTO.getUserId());
            bundlingTaskInfo.setIssuedName(userDTO.getName());
            bundlingTaskInfo.setGmtIssued(new Date());
        }
    }

   private void buildStoreExtendInfo(BundlingTaskStoreDetail taskStoreDetail){

   }

    /**
     * 头信息
     * @param bundlingTaskInfo
     * @return
     */
    private TaskHeadDTO createTaskHead(BundlingTaskInfo bundlingTaskInfo) {
        Long taskId = bundlingTaskInfo.getId();
        TaskHeadDTO taskHeadDTO = new TaskHeadDTO();
        taskHeadDTO.setEnvironment(environment);
        taskHeadDTO.setTaskId(bundlingTaskInfo.getId());
        taskHeadDTO.setTaskCode(bundlingTaskInfo.getTaskCode());
        taskHeadDTO.setTaskType(bundlingTaskInfo.getTaskType().toString());
        taskHeadDTO.setPlateOrgId(bundlingTaskInfo.getOrgId());
        List<BundlingTaskDetail> detailList = bundlingTaskDetailExtendMapper.selectByTaskIdAndDictCodes(taskId, BudnlTaskDetailDicEnum.storeTypeAllList());
        Map<String, List<BundlingTaskDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(v->v.getTaskId()+"_"+v.getDictCode()));
        taskHeadDTO.setBundlGoodsType(listTaskToString(detailMap, taskId, BudnlTaskDetailDicEnum.GOODS.getCode()));
        taskHeadDTO.setBundlComapny(listTaskToString(detailMap, taskId, BudnlTaskDetailDicEnum.BUSINESS.getCode()));
        taskHeadDTO.setBundlStoreType(listTaskToString(detailMap, taskId, BudnlTaskDetailDicEnum.STORE.getCode()));
        taskHeadDTO.setZsStoreType(listTaskToString(detailMap, taskId, BudnlTaskDetailDicEnum.ZS.getCode()));
        taskHeadDTO.setVersion(bundlingTaskInfo.getVersion() + "");

        if(BundlTaskTypeEnum.bbPlateType(bundlingTaskInfo.getTaskType()) && StringUtils.isBlank(taskHeadDTO.getBundlGoodsType())){
            throw new BusinessErrorException(new StringBuilder().append("全层级必备组货任务：").append(taskId).append("组货商品类型不能为空").toString());
        }
        if(BundlTaskTypeEnum.bbBusinessType(bundlingTaskInfo.getTaskType()) && StringUtils.isBlank(taskHeadDTO.getBundlComapny())){
            throw new BusinessErrorException(new StringBuilder().append("企业必备组货任务：").append(taskId).append("组货公司不能为空").toString());
        }
        if(BundlTaskTypeEnum.bbStoreType(bundlingTaskInfo.getTaskType()) && (StringUtils.isBlank(taskHeadDTO.getBundlStoreType()) && StringUtils.isBlank(taskHeadDTO.getZsStoreType()))){
            throw new BusinessErrorException(new StringBuilder().append("店型必备组货任务：").append(taskId).append("组货店型不能为空").toString());
        }

        return taskHeadDTO;
    }

    /**
     * 门店信息
     * @param taskId
     * @return
     */
    private List<TaskStoreDTO> createTaskStore(Long taskId) {
        List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByConfirm(taskId, BundlBoolEnum.YES.getCode());
        if(CollectionUtils.isEmpty(bundlingTaskStoreDetailList)){
            return Lists.newArrayList(new TaskStoreDTO());
        }
        CopyOnWriteArrayList<TaskStoreDTO> taskStoreDTOS = new CopyOnWriteArrayList<>();
        com.google.common.collect.Lists.partition(bundlingTaskStoreDetailList, Constants.QUERY_SEARCH_PAGESIZE).parallelStream().forEach(v -> {
            v.forEach(store -> {
                TaskStoreDTO taskStoreDTO = new TaskStoreDTO();
                taskStoreDTO.setBusinessOrgId(store.getCompanyOrgId());
                taskStoreDTO.setStoreCode(store.getStoreCode());
                taskStoreDTO.setSalesLevel(Objects.nonNull(store.getSalesLevel())?store.getSalesLevel():"");
                taskStoreDTO.setBundlStore(Objects.nonNull(store.getStoreTypeCode())?store.getStoreTypeCode():"");
                taskStoreDTO.setTradingArea(Objects.nonNull(store.getTradingArea())?store.getTradingArea():"");
                taskStoreDTO.setProvince(Objects.nonNull(store.getProvince())?store.getProvince():"");
                taskStoreDTO.setCity(Objects.nonNull(store.getCity())?store.getCity():"");
                taskStoreDTO.setArea(Objects.nonNull(store.getArea())?store.getArea():"");
                taskStoreDTO.setZsShop(Objects.nonNull(store.getZsStoreTypeCode())?store.getZsStoreTypeCode():"");
                taskStoreDTO.setPsStore("");
                taskStoreDTO.setPlateStore(Objects.nonNull(store.getPlatStoreTypeCode())?store.getPlatStoreTypeCode():"");
                taskStoreDTOS.add(taskStoreDTO);
            });
        });
        return taskStoreDTOS;
    }

    /**
     * 任务商品
     * @param taskId
     * @return
     */
    private TaskGoodsDTO createTaskGoods(Long taskId) {
        TaskGoodsDTO taskGoodsDTO = new TaskGoodsDTO();
        List<BundlingTaskDetail> detailList = bundlingTaskDetailExtendMapper.selectByTaskIdAndDictCodes(taskId, Lists.newArrayList(DictGoodsEnum.GOODSLINE.getCode(),DictGoodsEnum.EXCLUDEATTRIBUTE.getCode(),DictGoodsEnum.GOODSBLACKLIST.getCode()));
        Map<String, List<BundlingTaskDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(v->v.getTaskId()+"_"+v.getDictCode()));
        taskGoodsDTO.setManageAttr(listTaskToString(detailMap, taskId, DictGoodsEnum.GOODSLINE.getCode()));
        taskGoodsDTO.setUnGoodsAttr(listTaskToString(detailMap, taskId, DictGoodsEnum.EXCLUDEATTRIBUTE.getCode()));
        List<BundlingTaskDetail> taskDetailList = detailMap.get(new StringBuilder().append(taskId).append(Constants.KLINE).append(DictGoodsEnum.GOODSBLACKLIST.getCode()).toString());
        if(CollectionUtils.isEmpty(taskDetailList)){
            taskGoodsDTO.setGoodsCode("");
            return taskGoodsDTO;
        }
        List<Long> detailIdList = taskDetailList.stream().filter(v->Objects.nonNull(v.getId())).map(v->v.getId()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(detailIdList)){
            taskGoodsDTO.setGoodsCode("");
            return taskGoodsDTO;
        }
        List<BundlingTaskDetailExtend> detailExtendList = bundlingTaskDetailExtendExtMapper.selectExtendByDetailId(taskId, detailIdList);
        if(CollectionUtils.isEmpty(detailExtendList)){
            taskGoodsDTO.setGoodsCode("");
            return taskGoodsDTO;
        }
        List<String> goodsCodeList = detailExtendList.stream().filter(v->StringUtils.isNotBlank(v.getKeyword())).map(v->v.getKeyword()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(goodsCodeList)){
            taskGoodsDTO.setGoodsCode("");
            return taskGoodsDTO;
        }
        taskGoodsDTO.setGoodsCode(StringUtils.join(goodsCodeList, ","));
        return taskGoodsDTO;
    }

    /**
     * 创建规则
     * @param taskId
     * @return
     */
    private List<TaskRuleDTO> createTaskRule(Long taskId) {
        List<BundlingTaskDetail> taskDetailList = bundlingTaskDetailExtMapper.selectByTaskIdAndStep(taskId, BundlTaskStepEnum.THREE.getStep());
        List<TaskRuleDTO> taskRuleDTOList = new ArrayList<>(taskDetailList.size());
        taskDetailList.forEach(v->{
            TaskRuleDTO taskRuleDTO = new TaskRuleDTO();
            taskRuleDTO.setDictCode(v.getDictCode());
            taskRuleDTO.setDictValue(v.getPerprotyValue());
            taskRuleDTOList.add(taskRuleDTO);
        });

        return taskRuleDTOList;
    }

    /**
     * 创建必备商品明细
     * @param bundlingTaskInfo
     * @return
     */
    private TaskNecessaryGooodsDTO createTaskNecessaryGooods(BundlingTaskInfo bundlingTaskInfo, List<TaskStoreDTO> taskStoreDTOList, Long userId) {
        Byte taskType = bundlingTaskInfo.getTaskType();
        TaskNecessaryGooodsDTO taskNecessaryGooodsDTO = new TaskNecessaryGooodsDTO();
        taskNecessaryGooodsDTO.setGroupGoods("");
        taskNecessaryGooodsDTO.setCompanyGoods(Lists.newArrayList());
        taskNecessaryGooodsDTO.setPlatformGoods(Lists.newArrayList());

        if(BundlTaskTypeEnum.PLATE_BUNDL.getCode().equals(taskType)){
            //集团必备
            taskNecessaryGooodsDTO.setGroupGoods(necessaryContentsService.listGroupGoods(bundlingTaskInfo.getOrgId(), bundlingTaskInfo.getId()));
        }else if(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode().equals(taskType)){
            //取集团必备+平台必备
            taskNecessaryGooodsDTO.setGroupGoods(necessaryContentsService.listGroupGoods(bundlingTaskInfo.getOrgId(), bundlingTaskInfo.getId()));
            taskNecessaryGooodsDTO.setPlatformGoods(necessaryContentsService.listPlatformGoods(bundlingTaskInfo.getOrgId(), bundlingTaskInfo.getId(), taskStoreDTOList));

        }else if(BundlTaskTypeEnum.STORE_BUNDL.getCode().equals(taskType)){
            //取集团必备+平台必备+企业必备
            taskNecessaryGooodsDTO.setGroupGoods(necessaryContentsService.listGroupGoods(bundlingTaskInfo.getOrgId(), bundlingTaskInfo.getId()));
            taskNecessaryGooodsDTO.setPlatformGoods(necessaryContentsService.listPlatformGoods(bundlingTaskInfo.getOrgId(), bundlingTaskInfo.getId(), taskStoreDTOList));
            List<String> companyOrgIdList = bundlingTaskDetailExtMapper.selectDictValueListByCode(bundlingTaskInfo.getId(), Lists.newArrayList(BudnlTaskDetailDicEnum.BUSINESS.getCode()));
            taskNecessaryGooodsDTO.setCompanyGoods(necessaryContentsService.listCompanyGoods(bundlingTaskInfo.getOrgId(), companyOrgIdList));

        }
        return taskNecessaryGooodsDTO;
    }


    /**
     * 转化
     * @param detailMap
     * @param taskId
     * @param dictCode
     * @return
     */
    private String listTaskToString(Map<String, List<BundlingTaskDetail>> detailMap, Long taskId, String dictCode){
        String key = new StringBuilder().append(taskId).append("_").append(dictCode).toString();
        List<BundlingTaskDetail> detailList = detailMap.get(key);
        if(CollectionUtils.isEmpty(detailList)){
            return "";
        }
        List<String> valueList = detailList.stream().filter(v->StringUtils.isNotBlank(v.getPerprotyValue())).map(v->v.getPerprotyValue()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(valueList)){
            return StringUtils.join(valueList, ",");
        }
        return "";
    }

    /**
     * 字典MAP
     * @param dictCode
     * @param dictDetailMap
     * @param dictMap
     */
    private void buildDictMap(String dictCode, Map<String, BundlTaskDetailDTO> dictDetailMap, Map<String, List<String>> dictMap){
        BundlTaskDetailDTO storeDetailDTO = dictDetailMap.get(dictCode);
        if(Objects.isNull(storeDetailDTO)){
            return;
        }
        dictMap.put(dictCode, storeDetailDTO.getPerprotyValueList());
    }

    /**
     * 明细和扩展信息组装
     * @param bundlTaskAddParam
     * @param userDTO
     * @param taskDetailExtendMap
     * @return
     */
    private List<BundlingTaskDetail> buildTaskDetail(BundlTaskAddParam bundlTaskAddParam, TokenUserDTO userDTO, Map<String,List<BundlingTaskDetailExtend>> taskDetailExtendMap){
        List<BundlingTaskDetail> configOrgDetailList = new ArrayList<>();
        if (org.apache.commons.collections.MapUtils.isEmpty(bundlTaskAddParam.getDetailMap())){
            return configOrgDetailList;
        }
        if(StringUtils.isBlank(bundlTaskAddParam.getTaskCode())){
            BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(bundlTaskAddParam.getTaskId());
            bundlTaskAddParam.setTaskCode(bundlingTaskInfo.getTaskCode());
        }
        Set<Map.Entry<String, BundlTaskDetailDTO>> entries = bundlTaskAddParam.getDetailMap().entrySet();
        Iterator<Map.Entry<String, BundlTaskDetailDTO>> iterator = entries.iterator();
        while (iterator.hasNext()){
            BundlingTaskDetail configOrgDetail = new BundlingTaskDetail();
            configOrgDetail.setTaskId(bundlTaskAddParam.getTaskId());
            configOrgDetail.setTaskCode(bundlTaskAddParam.getTaskCode());
            configOrgDetail.setTaskStep(bundlTaskAddParam.getTaskStep());
            Map.Entry<String, BundlTaskDetailDTO> next = iterator.next();
            configOrgDetail.setDictCode(next.getKey());
            BundlTaskDetailDTO ruleDetailDTO = next.getValue();
            if (Objects.isNull(ruleDetailDTO)){
                configOrgDetailList.add(configOrgDetail);
            }else {
                if (CollectionUtils.isNotEmpty(ruleDetailDTO.getPerprotyValueList())){
                    for (String s : ruleDetailDTO.getPerprotyValueList()) {
                        BundlingTaskDetail configOrgDetail1 = new BundlingTaskDetail();
                        BeanUtils.copyProperties(configOrgDetail,configOrgDetail1);
                        configOrgDetail1.setPerprotyValue(s);
                        configOrgDetail1.setPerprotyType(Constants.CHECKBOX);
                        buildConfigOrgDetailCommonInfo(configOrgDetail1,userDTO, null);
                        configOrgDetailList.add(configOrgDetail1);
                    }
                }
                if (StringUtils.isNotEmpty(ruleDetailDTO.getPerprotyValue())){
                    configOrgDetail.setPerprotyValue(ruleDetailDTO.getPerprotyValue());
                    configOrgDetail.setPerprotyType(Constants.OBJECT);
                    buildConfigOrgDetailCommonInfo(configOrgDetail,userDTO, null);
                    configOrgDetailList.add(configOrgDetail);
                }
                if (CollectionUtils.isNotEmpty(ruleDetailDTO.getOrgIdList())){
                    ArrayList<BundlingTaskDetailExtend> configOrgDetailExtendList = new ArrayList<>();
                    configOrgDetail.setPerprotyValue("");
                    configOrgDetail.setPerprotyType(Constants.COLLECTION);
                    for (Long aLong : ruleDetailDTO.getOrgIdList()) {
                        BundlingTaskDetailExtend configOrgDetailExtend = new BundlingTaskDetailExtend();
                        configOrgDetailExtend.setTaskId(bundlTaskAddParam.getTaskId());
                        configOrgDetailExtend.setTaskCode(bundlTaskAddParam.getTaskCode());
                        configOrgDetailExtend.setExtendType(Constants.STORE);
                        configOrgDetailExtend.setKeyword(String.valueOf(aLong));
                        buildConfigOrgDetailExtendCommonInfo(configOrgDetailExtend,userDTO);
                        configOrgDetailExtendList.add(configOrgDetailExtend);
                    }
                    taskDetailExtendMap.put(next.getKey(),configOrgDetailExtendList);
                    buildConfigOrgDetailCommonInfo(configOrgDetail,userDTO, null);
                    configOrgDetailList.add(configOrgDetail);
                }

                if(CollectionUtils.isNotEmpty(ruleDetailDTO.getGoodsCodeList())){
                    ArrayList<BundlingTaskDetailExtend> configOrgDetailExtendList = new ArrayList<>();
                    configOrgDetail.setPerprotyValue("");
                    configOrgDetail.setPerprotyType(Constants.COLLECTION);
                    for (ConfigOrgDetailExtend goods : ruleDetailDTO.getGoodsCodeList()) {
                        BundlingTaskDetailExtend configOrgDetailExtend = new BundlingTaskDetailExtend();
                        configOrgDetailExtend.setTaskId(bundlTaskAddParam.getTaskId());
                        configOrgDetailExtend.setTaskCode(bundlTaskAddParam.getTaskCode());
                        configOrgDetailExtend.setExtendType(Constants.GOODS);
                        configOrgDetailExtend.setKeyword(goods.getKeyword());
                        configOrgDetailExtend.setExtend(goods.getExtend());
                        buildConfigOrgDetailExtendCommonInfo(configOrgDetailExtend,userDTO);
                        configOrgDetailExtendList.add(configOrgDetailExtend);
                    }
                    taskDetailExtendMap.put(next.getKey(),configOrgDetailExtendList);
                    buildConfigOrgDetailCommonInfo(configOrgDetail,userDTO, null);
                    configOrgDetailList.add(configOrgDetail);
                }
            }
        }
        return configOrgDetailList;
    }

    private void buildConfigOrgDetailCommonInfo(BundlingTaskDetail configOrgDetail, TokenUserDTO userDTO, Integer version) {
        configOrgDetail.setCreatedBy(userDTO.getUserId());
        configOrgDetail.setCreatedName(userDTO.getName());
        configOrgDetail.setUpdatedBy(userDTO.getUserId());
        configOrgDetail.setUpdatedName(userDTO.getName());
    }

    private void buildConfigOrgDetailExtendCommonInfo(BundlingTaskDetailExtend configOrgDetailExtend, TokenUserDTO userDTO) {
        if (Objects.isNull(configOrgDetailExtend.getExtend())){
            configOrgDetailExtend.setExtend("");
        }
        configOrgDetailExtend.setCreatedBy(userDTO.getUserId());
        configOrgDetailExtend.setCreatedName(userDTO.getName());
        configOrgDetailExtend.setUpdatedBy(userDTO.getUserId());
        configOrgDetailExtend.setUpdatedName(userDTO.getName());
    }

}
