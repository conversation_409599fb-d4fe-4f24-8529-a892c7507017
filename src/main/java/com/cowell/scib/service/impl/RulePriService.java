package com.cowell.scib.service.impl;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.vo.OrgVO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.ConfigOrg;
import com.cowell.scib.entityDgms.ConfigOrgUnmanageCategory;
import com.cowell.scib.enums.ConfigTypeEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.ConfigOrgMapper;
import com.cowell.scib.mapperDgms.extend.ConfigOrgExtendMapper;
import com.cowell.scib.mapperDgms.extend.ConfigOrgUnmanageCategoryExtendMapper;
import com.cowell.scib.mapperDgms.extend.OnlineHotGoodsExtendMapper;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.CheckService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.rule.HotGoodsImportDTO;
import com.cowell.scib.service.dto.rule.HotGoodsTipsDTO;
import com.cowell.scib.utils.HutoolUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 保存私有方法
 * <AUTHOR>
 * @date 2023/2/28 13:08
 */
@Slf4j
@Service
public class RulePriService {

    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;
    @Autowired
    private ConfigOrgMapper configOrgMapper;
    @Autowired
    private CheckService checkService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private OnlineHotGoodsExtendMapper onlineHotGoodsExtendMapper;
    @Autowired
    private ConfigOrgUnmanageCategoryExtendMapper configOrgUnmanageCategoryExtendMapper;
    @Value("${scib.groupGoods.importFile.maxLinesNums:5000}")
    private Integer maxLinesNums;
    @Value("${scib.unmanage.importFile.maxLinesNums:20000}")
    private Integer unmanageMaxLinesNums;

    /**
     * 添加配置
     * @param orgId
     * @param configType
     * @param
     * @return
     */
    protected long addConfig(Long orgId, Byte configType, TokenUserDTO userDTO, Integer version){
        OrgDTO orgDTO = null;
       if (Constants.ROOT_ORG_ID.equals(orgId)){
           try {
               OrgVO orgInfo = permissionService.getOrgInfoById(orgId);
               orgDTO = new OrgDTO();
               BeanUtils.copyProperties(orgInfo, orgDTO);
           } catch (Exception e) {
               log.error("根据组织机构id获取平台信息失败:", e);
               throw new BusinessErrorException(ErrorCodeEnum.ORG_INFO_PLATFORM_NOT_EXIST);
           }
       }else {
           List<OrgDTO> orgDTOList = permissionService.getParentOrgByIdAndType(orgId, OrgTypeEnum.PLATFORM.getCode());
           orgDTO = orgDTOList.get(0);
       }
        ConfigOrg configOrgEntity = configOrgExtendMapper.selectConfigByOrgId(orgId, configType, version);
        long configId = 0;
        if(Objects.isNull(configOrgEntity)){
            configOrgEntity = buildConfigOrg(orgDTO, orgId, configType, userDTO, version);
            configOrgMapper.insert(configOrgEntity);
            configId= configOrgEntity.getId();
        }else{
            configId = configOrgEntity.getId().intValue();
        }
        return configId;
    }

    private ConfigOrg buildConfigOrg(OrgDTO orgDTO,Long orgId, Byte configType,TokenUserDTO userDTO, Integer version){
        ConfigOrg configOrg = new ConfigOrg();
        configOrg.setOrgId(orgId);
        configOrg.setConfigType(configType);
        configOrg.setOrgName(orgDTO.getName());
        configOrg.setOutId(orgDTO.getOutId());
        configOrg.setSapCode(orgDTO.getSapcode());
        buildConfigCommonInfo(configOrg,userDTO, version);
        return configOrg;
    }

    private void buildConfigCommonInfo(ConfigOrg configOrg, TokenUserDTO userDTO, Integer version) {
        configOrg.setStatus(Constants.NORMAL_STATUS);
        configOrg.setExtend("");
        configOrg.setVersion(version==null?Constants.DEF_VERSION:version);
        configOrg.setCreatedBy(userDTO.getUserId());
        configOrg.setCreatedName(userDTO.getName());
        configOrg.setUpdatedBy(userDTO.getUserId());
        configOrg.setUpdatedName(userDTO.getName());
    }

    /**
     * 解析并校验接口
     * @param file
     * @param orgId
     * @param scopeCode
     * @param configType
     * @return
     */
    protected List<CommonGoodsDTO> resolveAndCheck(MultipartFile file, Long orgId, String scopeCode, String configType) throws Exception {
        unifyFileCheck(file);
        Assert.notNull(orgId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(scopeCode, ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(configType, ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());

        List<CommonGoodsDTO> goodsCodeList = HutoolUtil.excelToList(file.getInputStream(), ExcelFileDomain.getGoodsGroupMap(), CommonGoodsDTO.class, 2);
        if (CollectionUtils.isEmpty(goodsCodeList)) {
            throw new BusinessErrorException("待导入数据为空");
        }
        if (goodsCodeList.size() > maxLinesNums) {
            throw new BusinessErrorException("导入的excel过大，超过了" + maxLinesNums + "行数据");
        }
        List<CommonGoodsDTO> result = new ArrayList<>();
        result.addAll(goodsCodeList.stream().filter(v -> org.apache.commons.lang.StringUtils.isNotBlank(v.getGoodsNo())).map(v -> {
            v.setGoodsNo(v.getGoodsNo().trim());
            return v;
        }).collect(Collectors.toMap(CommonGoodsDTO::getGoodsNo, Function.identity(), (k1,k2) -> k1)).values());
        if (CollectionUtils.isEmpty(result)) {
            throw new BusinessErrorException("待导入数据为空");
        }
        return result;
    }

    /**
     * 解析并校验接口
     * @param file
     * @param orgId
     * @param scopeCode
     * @param configType
     * @return
     */
    protected List<CommonUnmanageDTO> resolveUnmanageAndCheck(MultipartFile file, Long orgId, String scopeCode, String configType) throws Exception {
        unifyFileCheck(file);
        Assert.notNull(orgId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(scopeCode, ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(configType, ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());

        List<CommonUnmanageDTO> commonUnmanageDTOList = HutoolUtil.excelToList(file.getInputStream(), ExcelFileDomain.getUnmanageMap(), CommonUnmanageDTO.class, 2);
        if (CollectionUtils.isEmpty(commonUnmanageDTOList)) {
            throw new BusinessErrorException("待导入数据为空");
        }
        if (commonUnmanageDTOList.size() > unmanageMaxLinesNums) {
            throw new BusinessErrorException("导入的excel过大，超过了" + unmanageMaxLinesNums + "行数据");
        }
        long storeCodeNullCount = commonUnmanageDTOList.stream().filter(v->StringUtils.isEmpty(v.getStoreCode())).count();
        if (storeCodeNullCount > 0) {
            throw new BusinessErrorException("待导入数据中门店编码有为空的，请检查");
        }
        return commonUnmanageDTOList;
    }

    /**
     * 校验热销品并解析
     * @param file
     * @return
     * @throws Exception
     */
    protected List<HotGoodsImportDTO> resolveHotGoodsAndCheck(MultipartFile file, List<HotGoodsTipsDTO> responseDTOList, StringBuilder sbMessage) throws Exception {
        unifyFileCheck(file);
        List<HotGoodsImportDTO> hotGoodsImportDTOList = HutoolUtil.excelToList(file.getInputStream(), ExcelFileDomain.getHotGoodsMap(), HotGoodsImportDTO.class, 1);
        if (CollectionUtils.isEmpty(hotGoodsImportDTOList)) {
            throw new AmisBusinessException(ErrorCodeEnum.RULE_FILE_EMPTY);
        }
        long nullCount = hotGoodsImportDTOList.stream().filter(v-> StringUtils.isEmpty(v.getGoodsSource())
                || StringUtils.isEmpty(v.getGoodsYear())
                || StringUtils.isEmpty(v.getGoodsTimeFrame())
                || StringUtils.isEmpty(v.getGoodsTimeDimension())).count();
        if(nullCount > 0){
            throw new AmisBusinessException(ErrorCodeEnum.RULE_FILE_MUST);
        }
        if (hotGoodsImportDTOList.size() > maxLinesNums) {
            throw new AmisBusinessException(ErrorCodeEnum.RULE_SIZE_SUPPER, "超过最大行数："+maxLinesNums);
        }


        List<String> hotGoodsKeyList = hotGoodsImportDTOList.stream().map(v->new StringBuilder().append(v.getGoodsSource()).append(Constants.UNDER_LINE).append(v.getGoodsYear()).append(Constants.UNDER_LINE).append(v.getGoodsTimeFrame()).append(Constants.UNDER_LINE).append(v.getGoodsTimeDimension()).toString()).distinct().collect(Collectors.toList());
//        List<String> rebateHotGoodsKeyList = Lists.newArrayList();
        for(String key : hotGoodsKeyList){
            String[] param = key.split(Constants.UNDER_LINE);
            int count = onlineHotGoodsExtendMapper.exitHotGoodsCount(param[0],param[1],param[2],param[3]);
            if(count > 0){
                sbMessage.append(new StringBuilder().append("已导入 ").append(key).append(" 数据，不可重复导入"));
                break;
//                rebateHotGoodsKeyList.add(key);
            }
        }

        //有错误就返回
        if(!StringUtils.isEmpty(sbMessage.toString())){
            log.info("重复错误返回");
            return hotGoodsImportDTOList;
        }

        for(HotGoodsImportDTO hotGoodsImportDTO : hotGoodsImportDTOList){
            String message = checkService.checkHotGoodsParam(hotGoodsImportDTO);
            if(!StringUtils.isEmpty(message)){
                String key = new StringBuilder().append(hotGoodsImportDTO.getGoodsSource()).append(Constants.UNDER_LINE).append(hotGoodsImportDTO.getGoodsYear()).append(Constants.UNDER_LINE).append(hotGoodsImportDTO.getGoodsTimeFrame()).append(Constants.UNDER_LINE).append(hotGoodsImportDTO.getGoodsTimeDimension()).toString();
                sbMessage.append(key).append(" 该数据行错误：").append(message);
                break;
            }
        }

//        Iterator<HotGoodsImportDTO> hotGoodsImportDTOIterator = hotGoodsImportDTOList.iterator();
//        while(hotGoodsImportDTOIterator.hasNext()){
//            HotGoodsImportDTO v = hotGoodsImportDTOIterator.next();
//            String hotKey = new StringBuilder().append(v.getGoodsSource()).append(Constants.UNDER_LINE).append(v.getGoodsYear()).append(Constants.UNDER_LINE).append(v.getGoodsTimeFrame()).append(Constants.UNDER_LINE).append(v.getGoodsTimeDimension()).toString();
//            if(rebateHotGoodsKeyList.contains(hotKey)){
//                HotGoodsTipsDTO responseDTO = new HotGoodsTipsDTO();
//                responseDTO.setHotKey(hotKey);
//                responseDTO.setMessage(new StringBuilder().append("已导入 ").append(hotKey).append(" 数据，不可重复导入").toString());
//                responseDTOList.add(responseDTO);
//                hotGoodsImportDTOIterator.remove();
//            }
//        }
//
//        if(CollectionUtils.isEmpty(hotGoodsImportDTOList)){
//            return hotGoodsImportDTOList;
//        }
//
//        Iterator<HotGoodsImportDTO> hotGoodsImportDTOIterator2 = hotGoodsImportDTOList.iterator();
//        while(hotGoodsImportDTOIterator2.hasNext()){
//            HotGoodsImportDTO v = hotGoodsImportDTOIterator2.next();
//            String hotKey = new StringBuilder().append(v.getGoodsSource()).append(Constants.UNDER_LINE).append(v.getGoodsYear()).append(Constants.UNDER_LINE).append(v.getGoodsTimeFrame()).append(Constants.UNDER_LINE).append(v.getGoodsTimeDimension()).toString();
//            String message = checkService.checkHotGoodsParam(v);
//            if(!StringUtils.isEmpty(message)){
//                HotGoodsTipsDTO responseDTO = new HotGoodsTipsDTO();
//                responseDTO.setHotKey(hotKey);
//                responseDTO.setMessage(new StringBuilder().append(hotKey).append(" 导入错误： ").append(message).toString());
//                responseDTOList.add(responseDTO);
//                hotGoodsImportDTOIterator2.remove();
//            }
//        }

        return hotGoodsImportDTOList;
    }

    /**
     * 统一文件校验
     * @param file
     */
    protected void unifyFileCheck(MultipartFile file){
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }

        if (file.getSize() > 10 * 1024 * 1024) {
            throw new BusinessErrorException("导入文件大于" + (file.getSize() / (1024 * 1024)) + "M");
        }
        String originalFilename = file.getOriginalFilename();
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!".xls".equals(fileType) && !".xlsx".equals(fileType)) {
            throw new BusinessErrorException("导入的文件类型有误");
        }
    }

    /**
     * 删除线上热销品
     * @param hotGoodsImportDTO
     */
    protected void checkHotGoodsParam(HotGoodsImportDTO hotGoodsImportDTO) {
        if(Objects.isNull(hotGoodsImportDTO)){
            throw new AmisBusinessException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
    }

    /**
     * 入库
     * @param configId
     * @param detailId
     * @param commonGoodsDTOS
     * @param userDTO
     * @return
     */
    protected List<ErrorUnmanageDTO> addUnmanageCategory(long orgId, long configId, long detailId, List<CommonUnmanageDTO> commonGoodsDTOS, TokenUserDTO userDTO) {
        CopyOnWriteArrayList<CommonUnmanageDTO> commonUnmanageDTOS = new CopyOnWriteArrayList<>();
        CopyOnWriteArrayList<ErrorUnmanageDTO> errorList = new CopyOnWriteArrayList<>();
        Lists.partition(commonGoodsDTOS, Constants.QUERY_SEARCH_PAGESIZE).parallelStream().forEach(v -> {
            v.forEach(sto -> {
                Optional<OrgInfoBaseCache> store = CacheVar.getStoreBySapCode(sto.getStoreCode().trim());
                if (store.isPresent()) {
                    OrgInfoBaseCache orgInfoBaseCache = store.get();
                    if(orgInfoBaseCache.getOrgPath().contains(String.valueOf(orgId))){
                        commonUnmanageDTOS.add(sto);
                    }else{
                        ErrorUnmanageDTO errorUnmanageDTO = new ErrorUnmanageDTO();
                        errorUnmanageDTO.setStoreCode(sto.getStoreCode());
                        errorUnmanageDTO.setErrorMsg("门店编码"+sto.getStoreCode()+"不属于该平台，请核查。");
                        errorList.add(errorUnmanageDTO);
                    }
                } else {
                    ErrorUnmanageDTO errorUnmanageDTO = new ErrorUnmanageDTO();
                    errorUnmanageDTO.setStoreCode(sto.getStoreCode());
                    errorUnmanageDTO.setErrorMsg("门店编码"+sto.getStoreCode()+"不存在");
                    errorList.add(errorUnmanageDTO);
                }
            });
        });

        // insert
        if (CollectionUtils.isNotEmpty(commonUnmanageDTOS)) {
            // 先删再增
            configOrgUnmanageCategoryExtendMapper.deleteByConfig(configId, detailId);
            List<ConfigOrgUnmanageCategory> unmanageCategoryList = covertDTOToEntity(configId, detailId, orgId, userDTO, commonUnmanageDTOS);
            Lists.partition(unmanageCategoryList, Constants.INSERT_MAX_SIZE).forEach(v -> {
                configOrgUnmanageCategoryExtendMapper.batchInsert(v);
            });
        }
        return errorList;
    }

    /**
     * 转换 covertDTOToEntity
     * @param configId
     * @param detailId
     * @param orgId
     * @param userDTO
     * @param commonUnmanageDTOS
     * @return
     */
    private List<ConfigOrgUnmanageCategory> covertDTOToEntity(long configId, long detailId, long orgId, TokenUserDTO userDTO, CopyOnWriteArrayList<CommonUnmanageDTO> commonUnmanageDTOS){
        CopyOnWriteArrayList<ConfigOrgUnmanageCategory> commonUnmanageList = new CopyOnWriteArrayList<>();
        Map<String, List<Long>> unmanageCategoryMap = covertListToMap(commonUnmanageDTOS);
        List<String> storeCodeList = commonUnmanageDTOS.stream().map(v->v.getStoreCode()).distinct().collect(Collectors.toList());
        Lists.partition(storeCodeList, Constants.QUERY_SEARCH_PAGESIZE).parallelStream().forEach(dtos->{
            dtos.forEach(v->{
                List<Long> idList = unmanageCategoryMap.get(v);
                commonUnmanageList.addAll(idList.stream().map(id->{
                    Optional<OrgInfoBaseCache> store = CacheVar.getStoreBySapCode(v);
                    ConfigOrgUnmanageCategory unmanageCategory = new ConfigOrgUnmanageCategory();
                    unmanageCategory.setConfigId(configId);
                    unmanageCategory.setDetailId(detailId);
                    unmanageCategory.setPlatOrgId(orgId);
                    unmanageCategory.setStoreCode(v);
                    unmanageCategory.setStoreName(store.isPresent()?store.get().getName():"");
                    unmanageCategory.setGoodsCategoryId(id);
                    unmanageCategory.setGoodsCategoryType(String.valueOf(id).length()/2);
                    unmanageCategory.setCreatedBy(userDTO.getUserId());
                    unmanageCategory.setCreatedName(userDTO.getName());
                    unmanageCategory.setUpdatedBy(userDTO.getUserId());
                    unmanageCategory.setUpdatedName(userDTO.getName());
                    return unmanageCategory;
                }).collect(Collectors.toList()));
            });
        });
        return commonUnmanageList;
    }

    /**
     * 转换
     *
     * @param commonUnmanageDTOS
     * @return
     */
    private Map<String, List<Long>> covertListToMap(CopyOnWriteArrayList<CommonUnmanageDTO> commonUnmanageDTOS) {
        Map<String, List<CommonUnmanageDTO>> commonUnmanageDTOSMap = commonUnmanageDTOS.stream().filter(v->StringUtils.isNotBlank(v.getStoreCode())).collect(Collectors.groupingBy(v -> v.getStoreCode()));
        Map<String, List<Long>> finalCommonUnmanageDTOSMap = new HashMap<>(commonUnmanageDTOSMap.size());
        commonUnmanageDTOSMap.forEach((code,list)->{
            List<Long> idList = new ArrayList<>();
            list.forEach(dto->{
                String finalCategoryId = retrievalId(dto);
                idList.addAll(categoryList(finalCategoryId));
            });
            finalCommonUnmanageDTOSMap.put(code, idList.stream().filter(v->Objects.nonNull(v)).distinct().collect(Collectors.toList()));
        });
        return finalCommonUnmanageDTOSMap;
    }

    /**
     * 从小到大检索最终ID
     * @param dto
     * @return
     */
    private String retrievalId(CommonUnmanageDTO dto) {
        if(StringUtils.isNotBlank(dto.getSubCategoryId())){
            return dto.getSubCategoryId();
        }
        if(StringUtils.isNotBlank(dto.getSmallCategoryId())){
            return dto.getSmallCategoryId();
        }
        if(StringUtils.isNotBlank(dto.getMiddleCategoryId())){
            return dto.getMiddleCategoryId();
        }
        if(StringUtils.isNotBlank(dto.getCategoryId())){
            return dto.getCategoryId();
        }
        return null;
    }

    /**
     * 字符串转换集合
     * @param categoryIds
     * @return
     */
    private List<Long> categoryList(String categoryIds){
        if(StringUtils.isBlank(categoryIds)){
            return new ArrayList<>(1);
        }
        return Arrays.asList(categoryIds.split("/")).stream().filter(v->StringUtils.isNotBlank(v)).map(v->Long.parseLong(v)).distinct().collect(Collectors.toList());
    }
}
