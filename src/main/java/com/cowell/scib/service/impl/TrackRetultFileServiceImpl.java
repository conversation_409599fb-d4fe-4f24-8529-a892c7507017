package com.cowell.scib.service.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.nyuwa.cos.util.CosService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.entityDgms.BundlingTaskInfoExample;
import com.cowell.scib.entityDgms.BundlingTaskStoreDetail;
import com.cowell.scib.entityTidb.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.mapperTidb.TrackResultFileMapper;
import com.cowell.scib.mapperTidb.TrackRetultNewStoreAllDetailMapper;
import com.cowell.scib.mapperTidb.extend.*;
import com.cowell.scib.mq.producer.LevelNecessaryProducer;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.SendService;
import com.cowell.scib.service.TrackRetultFileService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.newStore.TrackRetultNewStoreAllDetailDTO;
import com.cowell.scib.service.dto.newStore.TrackRetultNewStoreRankDetailDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class TrackRetultFileServiceImpl implements TrackRetultFileService {

    private final Logger logger = LoggerFactory.getLogger(TrackRetultFileServiceImpl.class);

    @Autowired
    private TrackRetultLevelReviewExtendMapper trackRetultLevelReviewExtendMapper;

    @Autowired
    private TrackRetultCompositionReviewExtendMapper trackRetultCompositionReviewExtendMapper;

    @Autowired
    private TrackRetultEfficiencyAnalyseExtendMapper trackRetultEfficiencyAnalyseExtendMapper;

    @Autowired
    private TrackRetultAllDetailExtendMapper trackRetultAllDetailExtendMapper;

    @Autowired
    private TrackRetultTop4levelStoregroupExtendMapper trackRetultTop4levelStoregroupExtendMapper;

    @Autowired
    private TrackResultLevelNecessaryExtendMapper trackResultLevelNecessaryExtendMapper;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    @Autowired
    private TrackRetultNewStoreAllDetailExtendMapper trackRetultNewStoreAllDetailExtendMapper;

    @Autowired
    private TrackRetultNewStoreRankDetailExtendMapper trackRetultNewStoreRankDetailExtendMapper;

    @Autowired
    @Qualifier("trackResultFileUploadExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Autowired
    private CosService cosService;

    @Autowired
    private TrackResultFileMapper trackResultFileMapper;

    @Autowired
    private LevelNecessaryProducer levelNecessaryProducer;

    @Override
    public void createFileLevelReviewFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            long count = trackRetultLevelReviewExtendMapper.countTrackRetultLevelReview(taskId);
            if (count <= 0) {
                return;
            }
            List<TrackRetultLevelReview> trackRetultLevelReviews = trackRetultLevelReviewExtendMapper.selectTrackRetultLevelReviewByPage(taskId, 0, 1);
            if (CollectionUtils.isEmpty(trackRetultLevelReviews)) {
                return;
            }

            //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
            String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultLevelReviews.get(0).getGmtCreate());
            String fileName = taskId + "_企业店型级复盘_" + "V"+ version + dateStr + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            try {
                ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                WriteSheet writeSheet = EasyExcel.writerSheet("企业店型级复盘").head(TrackRetultLevelReview.class).build();
                for (int i = 0; i <= count; ) {
                    List<TrackRetultLevelReview> trackRetultLevelReviewList = trackRetultLevelReviewExtendMapper.selectTrackRetultLevelReviewByPage(taskId, i, Constants.EXPORT_ONCE_QUERY_MAX);
                    if (CollectionUtils.isEmpty(trackRetultLevelReviewList)){
                        break;
                    }
                    excelWriter.write(trackRetultLevelReviewList, writeSheet);
                    logger.info("查询数据条数：{}", trackRetultLevelReviewList.size());
                    i += Constants.EXPORT_ONCE_QUERY_MAX;
                }
                excelWriter.finish();
                String presignatureUrl = uploadFile(fileName, filePath);
                insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.TRACK_RESULT_LEVEL_REVIEW_FILE.getCode(), version);
            } catch (Exception e) {
                logger.error("生成文件失败,删除文件", e);
                throw e;
            } finally {
                delTempFile(filePath);
            }
        });
    }

    @Override
    public void createTrackRetultCompositionReviewFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            long count = trackRetultCompositionReviewExtendMapper.countTrackRetultCompositionReview(taskId);
            if (count <= 0) {
                return;
            }
            List<TrackRetultCompositionReview> trackRetultCompositionReviews = trackRetultCompositionReviewExtendMapper.selectTrackRetultCompositionReviewByPage(taskId, 0, 1);
            if (CollectionUtils.isEmpty(trackRetultCompositionReviews)) {
                return;
            }
            //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
            String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultCompositionReviews.get(0).getGmtCreate());
            String fileName = taskId + "_成分复盘_" + "V"+ version + dateStr + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            try {
                ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                WriteSheet writeSheet = EasyExcel.writerSheet("成分复盘").head(TrackRetultCompositionReview.class).build();
                for (int i = 0; i <= count; ) {
                    List<TrackRetultCompositionReview> trackRetultCompositionReviews1 = trackRetultCompositionReviewExtendMapper.selectTrackRetultCompositionReviewByPage(taskId, i, Constants.EXPORT_ONCE_QUERY_MAX);
                    if (CollectionUtils.isEmpty(trackRetultCompositionReviews1)){
                        break;
                    }
                    excelWriter.write(trackRetultCompositionReviews1, writeSheet);
                    i += Constants.EXPORT_ONCE_QUERY_MAX;
                }
                excelWriter.finish();
                String presignatureUrl = uploadFile(fileName, filePath);
                insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.TRACK_RETULT_COMPOSITION_REVIEW_FILE.getCode(), version);
            } catch (Exception e) {
                logger.error("成分复盘生成文件失败,删除文件", e);
                throw e;
            } finally {
                delTempFile(filePath);
            }
        });
    }

    @Override
    public void createTrackRetultEfficiencyAnalyseFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            long count = trackRetultEfficiencyAnalyseExtendMapper.countTrackRetultEfficiencyAnalyse(taskId);
            if (count <= 0) {
                return;
            }
            List<TrackRetultEfficiencyAnalyse> trackRetultEfficiencyAnalyses = trackRetultEfficiencyAnalyseExtendMapper.selectTrackRetultEfficiencyAnalyseByPage(taskId, 0, 1);
            if (CollectionUtils.isEmpty(trackRetultEfficiencyAnalyses)) {
                return;
            }
            //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
            String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultEfficiencyAnalyses.get(0).getGmtCreate());
            String fileName = taskId + "_组货效率分析_" + "V"+ version + dateStr + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            try {
                ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                WriteSheet writeSheet = EasyExcel.writerSheet("成分复盘").head(TrackRetultEfficiencyAnalyse.class).build();
                for (int i = 0; i <= count; ) {
                    List<TrackRetultEfficiencyAnalyse> trackRetultEfficiencyAnalyses1 = trackRetultEfficiencyAnalyseExtendMapper.selectTrackRetultEfficiencyAnalyseByPage(taskId, i, Constants.EXPORT_ONCE_QUERY_MAX);
                    logger.info("组货效率分析查询数据条数：{}", trackRetultEfficiencyAnalyses1.size());
                    if (CollectionUtils.isEmpty(trackRetultEfficiencyAnalyses1)){
                        break;
                    }
                    excelWriter.write(trackRetultEfficiencyAnalyses1, writeSheet);
                    i += Constants.EXPORT_ONCE_QUERY_MAX;
                }
                excelWriter.finish();
                String presignatureUrl = uploadFile(fileName, filePath);
                insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.TRACK_RESULT_EFFICIENCY_ANALYSE_FILE.getCode(), version);
            } catch (Exception e) {
                logger.error("组货效率分析生成文件失败,删除文件", e);
                throw e;
            } finally {
                delTempFile(filePath);
            }
        });
    }

    @Override
    public void createTrackRetultAllDetailFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            long count = trackRetultAllDetailExtendMapper.countTrackRetultAllDetail(taskId, null, null);
            if (count < 0) {
                return;
            }
            List<String> stringList = Arrays.asList(String.valueOf(NecessaryTagEnum.GROUP_NECESSARY.getCode()), String.valueOf(NecessaryTagEnum.PLATFORM_NECESSARY.getCode())
                    ,String.valueOf(NecessaryTagEnum.COMPANY_NECESSARY.getCode()),String.valueOf(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode()),String.valueOf(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()),String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode()));
            List<TrackResultTaskCompidDTO> trackResultTaskCompidDTOS = trackRetultAllDetailExtendMapper.selectTrackRetultCompid(taskId, stringList);
            for (TrackResultTaskCompidDTO trackResultTaskCompidDTO : trackResultTaskCompidDTOS) {
                List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.selectTrackRetultAllDetailByPage(taskId, trackResultTaskCompidDTO.getCompid(), null, null, 1);
                if (CollectionUtils.isEmpty(trackRetultAllDetails)) {
                    continue;
                }
                //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
                long companyCount = trackRetultAllDetailExtendMapper.countTrackRetultAllDetail(taskId, trackResultTaskCompidDTO.getCompid(), stringList);
                String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultAllDetails.get(0).getGmtCreate());
                String businessOrgName = getBudinessOrgName(trackRetultAllDetails.get(0).getCompid(), trackRetultAllDetails.get(0).getDataFrom());
                String fileName = taskId + "_必备目录全量表(一店一目级别)_"  + "V"+ version + dateStr + businessOrgName + ExcelTypeEnum.XLSX.getValue();
                String filePath = "/tmp/" + fileName;
                try {
                    ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("必备目录全量表").head(TrackRetultAllDetail.class).build();
                    Long id = trackRetultAllDetails.get(0).getId()-1;
                    for (int i = 0; i <= companyCount; ) {
                        List<TrackRetultAllDetail> trackRetultAllDetails1 = trackRetultAllDetailExtendMapper.selectTrackRetultAllDetailByPage(taskId, trackResultTaskCompidDTO.getCompid(), null, id, Constants.EXPORT_ONCE_QUERY_MAX);
                        logger.info("必备目录全量表(一店一目级别)查询数据条数：{}", trackRetultAllDetails1.size());
                        trackRetultAllDetails1.stream().forEach(v->{
                            NecessaryTagEnum enumByCode = NecessaryTagEnum.getEnumByCode(Byte.valueOf(v.getLevel()));
                            v.setLevel(enumByCode.getMessage());
                        });
                        if (CollectionUtils.isEmpty(trackRetultAllDetails1)){
                            break;
                        }
                        excelWriter.write(trackRetultAllDetails1, writeSheet);
                        id = trackRetultAllDetails1.get(trackRetultAllDetails1.size() - 1).getId();
                        i += Constants.EXPORT_ONCE_QUERY_MAX;
                        //防止接口限流
                        try {
                            Thread.sleep(100L);
                        }catch (Exception e){
                            logger.error("必备目录全量表|查询必备目录全量信息出错");
                        }
                    }
                    excelWriter.finish();
                    String presignatureUrl = uploadFile(fileName, filePath);
                    insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.SINFGL_STORE_SIX_DETAIL_FILE.getCode(), version);

                } catch (Exception e) {
                    logger.error("必备目录全量表(一店一目级别)生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
        });

    }

    private String getBudinessOrgName(String compid, String dataFromV2) {
        if (StringUtils.isEmpty(compid) || !StringUtils.isNumeric(compid)) {
            return dataFromV2;
        }
        String businessOrgName = "";
        Optional<OrgInfoBaseCache> businessByOrgId = CacheVar.getBusinessByOrgId(Long.valueOf(compid));
        if (!businessByOrgId.isPresent()) {
            businessOrgName = dataFromV2;
        }else {
            businessOrgName = businessByOrgId.get().getBusinessShortName();
        }
        return businessOrgName;
    }

    @Override
    public void createGroupGoodsResultsFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            long count = trackRetultTop4levelStoregroupExtendMapper.countTrackRetultTop4level(taskId, null);
            if (count < 0) {
                return;
            }
            List<String> orgIdList = trackRetultTop4levelStoregroupExtendMapper.selectTrackRetultCompidTop4level(taskId);
            if (CollectionUtils.isEmpty(orgIdList)) {
                return;
            }
            for (String s : orgIdList) {
                List<TrackRetultTop4levelStoregroup> trackRetultTop4levelStoregroups = trackRetultTop4levelStoregroupExtendMapper.selectTrackRetultTop4levelByPage(taskId, s, null, 1);
                //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
                String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultTop4levelStoregroups.get(0).getGmtCreate());
                String businessOrgName = getBudinessOrgName(trackRetultTop4levelStoregroups.get(0).getOrgid(), trackRetultTop4levelStoregroups.get(0).getDataFrom());
                String fileName = taskId + "_组货结果明细_" + "V"+ version + dateStr + businessOrgName + ExcelTypeEnum.XLSX.getValue();
                String filePath = "/tmp/" + fileName;
                long compidCount = trackRetultTop4levelStoregroupExtendMapper.countTrackRetultTop4level(taskId, s);
                try {
                    ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("前四级必备全量（店型级）").head(TrackRetultTop4levelStoregroup.class).build();
                    Long id = trackRetultTop4levelStoregroups.get(0).getId()-1;
                    for (int i = 0; i <= compidCount; ) {
                        List<TrackRetultTop4levelStoregroup> trackRetultTop4levelStoregroups1 = trackRetultTop4levelStoregroupExtendMapper.selectTrackRetultTop4levelByPage(taskId, s, id, Constants.EXPORT_ONCE_QUERY_MAX);
                        trackRetultTop4levelStoregroups1.stream().forEach(v->{
                            NecessaryTagEnum enumByCode = NecessaryTagEnum.getEnumByCode(Byte.valueOf(v.getLevel()));
                            v.setLevel(enumByCode.getMessage());
                        });
                        if (CollectionUtils.isEmpty(trackRetultTop4levelStoregroups1)){
                            break;
                        }
                        excelWriter.write(trackRetultTop4levelStoregroups1, writeSheet);
                        id =trackRetultTop4levelStoregroups1.get(trackRetultTop4levelStoregroups1.size() - 1).getId();
                        i += Constants.EXPORT_ONCE_QUERY_MAX;
                        try {
                            Thread.sleep(1000L);
                        }catch (Exception e){
                            logger.error("必备目录全量表|查询必备目录全量信息出错");
                        }
                    }
                    List<String> stringList = Arrays.asList(String.valueOf(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()), String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode()));
                    List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.selectTrackRetultAllDetailByPage(taskId, s, stringList, null, 1);
                    if (CollectionUtils.isEmpty(trackRetultAllDetails)) {
                        continue;
                    }
                    WriteSheet writeSheet2 = EasyExcel.writerSheet("单店级必备明细（后2级）").head(TrackRetultAllDetail.class).build();
                    Long TrackRetultAllDetailId = trackRetultAllDetails.get(0).getId()-1;
                    for (int i = 0; i <= compidCount; ) {
                        List<TrackRetultAllDetail> trackRetultAllDetails1 = trackRetultAllDetailExtendMapper.selectTrackRetultAllDetailByPage(taskId, s, stringList, TrackRetultAllDetailId, Constants.EXPORT_ONCE_QUERY_MAX);
                        logger.info("单店级必备明细（后2级）查询数据条数：{}", trackRetultAllDetails1.size());
                        trackRetultAllDetails1.stream().forEach(v->{
                            NecessaryTagEnum enumByCode = NecessaryTagEnum.getEnumByCode(Byte.valueOf(v.getLevel()));
                            v.setLevel(enumByCode.getMessage());
                        });
                        excelWriter.write(trackRetultAllDetails1,writeSheet2);
                        if (CollectionUtils.isEmpty(trackRetultAllDetails1)){
                            break;
                        }
                        TrackRetultAllDetailId = trackRetultAllDetails1.get(trackRetultAllDetails1.size() - 1).getId();
                        i += Constants.EXPORT_ONCE_QUERY_MAX;
                        try {
                            Thread.sleep(100L);
                        }catch (Exception e){
                            logger.error("必备目录全量表|查询必备目录全量信息出错");
                        }
                    }
                    WriteSheet writeSheet3 = EasyExcel.writerSheet("企业店型级复盘").head(TrackRetultLevelReview.class).build();

                    List<TrackRetultLevelReview> trackRetultLevelReviewList = trackRetultLevelReviewExtendMapper.selectTrackRetultLevelReviewByChainName(taskId, trackRetultTop4levelStoregroups.get(0).getDataFromV2());
                    if (CollectionUtils.isEmpty(trackRetultLevelReviewList)){
                        break;
                    }
                    excelWriter.write(trackRetultLevelReviewList, writeSheet3);
                    logger.info("查询数据条数：{}", trackRetultLevelReviewList.size());
                    excelWriter.finish();
                    String presignatureUrl = uploadFile(fileName, filePath);
                    insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.GROUP_GOODS_RESULT_FILE.getCode(), version);
                } catch (Exception e) {
                    logger.error("前四级必备全量（店型级）生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
        });

    }

    @Override
    public void createTrackRetultTop4levelStoreGroupFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            long count = trackRetultTop4levelStoregroupExtendMapper.countTrackRetultTop4level(taskId, null);
            if (count < 0) {
                return;
            }
            List<String> orgIdList = trackRetultTop4levelStoregroupExtendMapper.selectTrackRetultCompidTop4level(taskId);
            if (CollectionUtils.isEmpty(orgIdList)) {
                return;
            }
            for (String s : orgIdList) {
                List<TrackRetultTop4levelStoregroup> trackRetultTop4levelStoregroups = trackRetultTop4levelStoregroupExtendMapper.selectTrackRetultTop4levelByPage(taskId, s, null, 1);
                //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
                String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultTop4levelStoregroups.get(0).getGmtCreate());
                String businessOrgName = getBudinessOrgName(trackRetultTop4levelStoregroups.get(0).getOrgid(), trackRetultTop4levelStoregroups.get(0).getDataFrom());
                String fileName = taskId + "_前四级必备全量（店型级）_" + "V"+ version + dateStr +businessOrgName + ExcelTypeEnum.XLSX.getValue();
                String filePath = "/tmp/" + fileName;
                long compidCount = trackRetultTop4levelStoregroupExtendMapper.countTrackRetultTop4level(taskId, s);
                try {
                    ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("前四级必备全量（店型级）").head(TrackRetultTop4levelStoregroup.class).build();
                    Long id = trackRetultTop4levelStoregroups.get(0).getId()-1;
                    for (int i = 0; i <= compidCount; ) {
                        List<TrackRetultTop4levelStoregroup> trackRetultTop4levelStoregroups1 = trackRetultTop4levelStoregroupExtendMapper.selectTrackRetultTop4levelByPage(taskId, s, id, Constants.EXPORT_ONCE_QUERY_MAX);
                        if (CollectionUtils.isEmpty(trackRetultTop4levelStoregroups1)){
                            break;
                        }
                        trackRetultTop4levelStoregroups1.stream().forEach(v->{
                            NecessaryTagEnum enumByCode = NecessaryTagEnum.getEnumByCode(Byte.valueOf(v.getLevel()));
                            v.setLevel(enumByCode.getMessage());
                        });
                        excelWriter.write(trackRetultTop4levelStoregroups1, writeSheet);
                        id =trackRetultTop4levelStoregroups1.get(trackRetultTop4levelStoregroups1.size() - 1).getId();
                        i += Constants.EXPORT_ONCE_QUERY_MAX;
                        try {
                            Thread.sleep(1000L);
                        }catch (Exception e){
                            logger.error("必备目录全量表|查询必备目录全量信息出错");
                        }
                    }
                    excelWriter.finish();
                    String presignatureUrl = uploadFile(fileName, filePath);
                    insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.STORE_GROUP_DETAIL_FILE.getCode(), version);
                } catch (Exception e) {
                    logger.error("前四级必备全量（店型级）生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
        });
    }

    @Override
    public void createTrackRetult2levelSingleFile(Long taskId, Integer version) {

        asyncTaskExecutor.submit(() -> {
            List<String> stringList = Arrays.asList(String.valueOf(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()), String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode()));
            long count = trackRetultAllDetailExtendMapper.countTrackRetultAllDetail(taskId, null, stringList);
            if (count < 0) {
                return;
            }
            List<TrackResultTaskCompidDTO> trackResultTaskCompidDTOS = trackRetultAllDetailExtendMapper.selectTrackRetultCompid(taskId, stringList);
            if (CollectionUtils.isEmpty(trackResultTaskCompidDTOS)) {
                return;
            }
            List<String> collect = trackResultTaskCompidDTOS.stream().map(TrackResultTaskCompidDTO::getCompid).collect(Collectors.toList());
            for (String s : collect) {
                List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.selectTrackRetultAllDetailByPage(taskId, s, stringList, null, 1);
                //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
                String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(trackRetultAllDetails.get(0).getGmtCreate());
                String businessOrgName = getBudinessOrgName(trackRetultAllDetails.get(0).getCompid(), trackRetultAllDetails.get(0).getDataFrom());
                String fileName = taskId + "_单店级必备明细（后2级）_" + "V"+ version + dateStr + businessOrgName + ExcelTypeEnum.XLSX.getValue();
                String filePath = "/tmp/"+ fileName;
                long compidCount = trackRetultAllDetailExtendMapper.countTrackRetultAllDetail(taskId, s, stringList);

                try {
                    ExcelWriter excelWriter = new ExcelWriterBuilder().file(new File(filePath)).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("单店级必备明细（后2级）").head(TrackRetultAllDetail.class).build();
                    Long id = trackRetultAllDetails.get(0).getId()-1;
                    for (int i = 0; i <= compidCount; ) {
                        List<TrackRetultAllDetail> trackRetultAllDetails1 = trackRetultAllDetailExtendMapper.selectTrackRetultAllDetailByPage(taskId, s, stringList, id, Constants.EXPORT_ONCE_QUERY_MAX);
                        logger.info("单店级必备明细（后2级）查询数据条数：{}", trackRetultAllDetails1.size());
                        trackRetultAllDetails1.stream().forEach(v->{
                            NecessaryTagEnum enumByCode = NecessaryTagEnum.getEnumByCode(Byte.valueOf(v.getLevel()));
                            v.setLevel(enumByCode.getMessage());
                        });
                        excelWriter.write(trackRetultAllDetails1,writeSheet);
                        if (CollectionUtils.isEmpty(trackRetultAllDetails1)){
                            break;
                        }
                        id = trackRetultAllDetails1.get(trackRetultAllDetails1.size() - 1).getId();
                        i += Constants.EXPORT_ONCE_QUERY_MAX;
                        try {
                            Thread.sleep(100L);
                        }catch (Exception e){
                            logger.error("必备目录全量表|查询必备目录全量信息出错");
                        }
                    }
                    excelWriter.finish();
                    String presignatureUrl = uploadFile(fileName, filePath);
                    insertTrackResultFile(taskId, fileName, presignatureUrl, FileTypeEnum.SINFGL_STORE_TWO_DETAIL_FILE.getCode(), version);
                } catch (Exception e) {
                    logger.error("单店级必备明细（后2级）生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
        });
    }

    @Override
    public void selectTrackRetultLevelNecessary(Long taskId) {

        asyncTaskExecutor.execute(() -> {
            long count = trackResultLevelNecessaryExtendMapper.countTrackRetultLevelNecessary(taskId);
            if (count < 0) {
                return;
            }
            for (int i = 0; i <= count; ) {
                List<TrackResultLevelNecessary> trackResultLevelNecessaries = trackResultLevelNecessaryExtendMapper.selectTrackRetultLevelNecessaryByPage(taskId, i, 20);
                if (CollectionUtils.isNotEmpty(trackResultLevelNecessaries)) {
                    levelNecessaryProducer.send(JSON.toJSONString(trackResultLevelNecessaries));
                }
                i+=20;
            }
        });
    }

    @Override
    public void selectTrackRetultLevelNecessaryByPage(Long taskId, int page, int perPage) {
        List<TrackResultLevelNecessary> trackResultLevelNecessaries = trackResultLevelNecessaryExtendMapper.selectTrackRetultLevelNecessaryByPage(taskId, page, perPage);
        if (CollectionUtils.isNotEmpty(trackResultLevelNecessaries)) {
            levelNecessaryProducer.send(JSON.toJSONString(trackResultLevelNecessaries));
        }
    }

    @Override
    public Map<Integer, List<TrackResultFile>> queryLevelNecessaryFile(Long taskId, Integer version) {
        HashMap<Integer, List<TrackResultFile>> result = new HashMap<>();
        TrackResultFileExample trackResultFileExample = new TrackResultFileExample();
        trackResultFileExample.createCriteria().andTaskIdEqualTo(taskId).andVersionEqualTo(version);
        List<TrackResultFile> trackResultFiles = trackResultFileMapper.selectByExample(trackResultFileExample);
        if (CollectionUtils.isEmpty(trackResultFiles)) {
//            throw new BusinessErrorException("任务" + taskId + "下的文件不存在");
            return new HashMap<>();
        }
        trackResultFiles.stream().forEach(v -> {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, +7);
            try {
                String presignatureUrl = cosService.getPresignatureUrl(v.getGroupGoodsResultFileName(), calendar.getTime());
                v.setGroupGoodsResultFileUrl(presignatureUrl);
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("获取可以下载的访问的URL出错", e);
            }
        });
        result.put(FileTypeEnum.TRACK_RESULT_LEVEL_REVIEW_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.TRACK_RESULT_LEVEL_REVIEW_FILE.getCode())).collect(Collectors.toList()));
        result.put(FileTypeEnum.TRACK_RETULT_COMPOSITION_REVIEW_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.TRACK_RETULT_COMPOSITION_REVIEW_FILE.getCode())).collect(Collectors.toList()));
        result.put(FileTypeEnum.TRACK_RESULT_EFFICIENCY_ANALYSE_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.TRACK_RESULT_EFFICIENCY_ANALYSE_FILE.getCode())).collect(Collectors.toList()));
        result.put(FileTypeEnum.SINFGL_STORE_SIX_DETAIL_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.SINFGL_STORE_SIX_DETAIL_FILE.getCode())).collect(Collectors.toList()));
        result.put(FileTypeEnum.STORE_GROUP_DETAIL_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.STORE_GROUP_DETAIL_FILE.getCode())).collect(Collectors.toList()));
        result.put(FileTypeEnum.SINFGL_STORE_TWO_DETAIL_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.SINFGL_STORE_TWO_DETAIL_FILE.getCode())).collect(Collectors.toList()));
        result.put(FileTypeEnum.GROUP_GOODS_RESULT_FILE.getCode(),trackResultFiles.stream().filter(v->v.getType().equals(FileTypeEnum.GROUP_GOODS_RESULT_FILE.getCode())).collect(Collectors.toList()));
        return result;
    }

    private void insertTrackResultFile(Long taskId, String fileName, String presignatureUrl, int code, Integer version) {
        TrackResultFileExample trackResultFileExample = new TrackResultFileExample();
        trackResultFileExample.createCriteria().andTaskIdEqualTo(taskId).andGroupGoodsResultFileNameEqualTo(fileName).andTypeEqualTo(code).andVersionEqualTo(version);
        List<TrackResultFile> trackResultFiles = trackResultFileMapper.selectByExample(trackResultFileExample);
        if (CollectionUtils.isEmpty(trackResultFiles)){
            TrackResultFile trackResultFile = new TrackResultFile();
            trackResultFile.setGroupGoodsResultFileName(fileName);
            trackResultFile.setGroupGoodsResultFileUrl(presignatureUrl);
            trackResultFile.setType(code);
            trackResultFile.setTaskId(taskId);
            trackResultFile.setStatus(Constants.NORMAL_STATUS);
            trackResultFile.setVersion(version);
            trackResultFile.setExtend("");
            trackResultFile.setGmtCreate(new Date());
            trackResultFile.setGmtUpdate(new Date());
            trackResultFileMapper.insert(trackResultFile);
        }else {
            TrackResultFile trackResultFile = trackResultFiles.get(0);
            trackResultFile.setGroupGoodsResultFileUrl(presignatureUrl);
            trackResultFileMapper.updateByPrimaryKeySelective(trackResultFile);
        }

    }

    private String uploadFile(String fileName, String filePath) {
        File file = new File(filePath);
        String key = fileName;
        String presignatureUrl = "";
        try {
            String url = cosService.multipartUploadFile(key, file);
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, +7);
            presignatureUrl = cosService.getPresignatureUrl(key, calendar.getTime());
        } catch (Exception e) {
            logger.error("分片上传文件失败", e);
        }
        return presignatureUrl;
    }

    private void delTempFile(String filePath) {
        Path path = Paths.get(filePath);
        try {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
                    Files.delete(file);
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException ioException) {
            logger.error("删除文件异常", ioException);
            ioException.printStackTrace();
        }
    }

    @Override
    public void createFileNewStoreFile(Long taskId, HttpServletResponse response) throws Exception{

        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");        //改成输出excel文件
        response.setCharacterEncoding("utf-8");

//        asyncTaskExecutor.submit(() -> {
        try {
            BundlingTaskStoreDetail detail = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId).stream().findFirst().orElseThrow(() -> new BusinessErrorException("没有查询到新开门店任务信息"));

            ExcelWriter excelWriter = new ExcelWriterBuilder().file(response.getOutputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("建议经营目录").head(TrackRetultNewStoreAllDetailDTO.class).build();
            long count = trackRetultNewStoreAllDetailExtendMapper.count(taskId);
            long rankCount = trackRetultNewStoreRankDetailExtendMapper.count(taskId);
            if (count <= 0 && rankCount <= 0) {
                excelWriter.write(new ArrayList<>(), writeSheet);
                excelWriter.finish();
                return;
            }

            //任务单号_文件名称_结果接收日期YYYYMMDD时间戳
            String dateStr = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(new Date());
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(taskId + "_JYJYML_" + detail.getStoreCode() + dateStr, "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
            WriteSheet otherChooseSheet = EasyExcel.writerSheet("其他可经营的商品清单(供选择)").head(TrackRetultNewStoreRankDetailDTO.class).build();
            AtomicInteger index = new AtomicInteger(0);
            BigDecimal hunderd = new BigDecimal("100");
            for (int i = 0;; i++) {
                List<TrackRetultNewStoreAllDetailDTO> trackRetultNewStoreAllDetailDTOS = trackRetultNewStoreAllDetailExtendMapper.selectByTaskId(taskId, i * Constants.EXPORT_ONCE_QUERY_MAX, Constants.EXPORT_ONCE_QUERY_MAX).stream().map(v -> {
                    TrackRetultNewStoreAllDetailDTO dto = new TrackRetultNewStoreAllDetailDTO();
                    BeanUtils.copyProperties(v, dto);
                    dto.setIndex(index.incrementAndGet());
                    dto.setSuggestPhQty(v.getSuggestPhQty() == null ? "0.0" : v.getSuggestPhQty().setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toEngineeringString());
                    dto.setPhCost(v.getPhCost() == null ? "0.0" : v.getPhCost().setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toEngineeringString());
                    // 集中度
                    dto.setInStockRateDx(new BigDecimal(v.getInStockRateDx()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInStockRateCity(new BigDecimal(v.getInStockRateCity()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInStockRateQy(new BigDecimal(v.getInStockRateQy()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInStockRateMd(new BigDecimal(v.getInStockRateMd()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    // 动销率
                    dto.setInSalesRateDx(new BigDecimal(v.getInSalesRateDx()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInSalesRateCity(new BigDecimal(v.getInSalesRateCity()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInSalesRateQy(new BigDecimal(v.getInSalesRateQy()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInSalesRateMd(new BigDecimal(v.getInSalesRateMd()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    // 毛利率
                    dto.setProfitRate90Dx(new BigDecimal(v.getProfitRate90Dx()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setProfitRate90City(new BigDecimal(v.getProfitRate90City()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setProfitRate90Qy(new BigDecimal(v.getProfitRate90Qy()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setProfitRate90Md(new BigDecimal(v.getProfitRate90Md()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    //近90天销售金额
                    dto.setAmtCum90Dx(new BigDecimal(v.getAmtCum90Dx()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setAmtCum90City(new BigDecimal(v.getAmtCum90City()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setAmtCum90Qy(new BigDecimal(v.getAmtCum90Qy()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setAmtCum90Md(new BigDecimal(v.getAmtCum90Md()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setJyAbleDesc(v.getJyAble() == 1 ? "是" : "否");
                    return dto;
                }).collect(Collectors.toList());
                List<TrackRetultNewStoreRankDetailDTO> trackRetultNewStoreRankDetailDTOS = trackRetultNewStoreRankDetailExtendMapper.selectByTaskId(taskId, i * Constants.EXPORT_ONCE_QUERY_MAX, Constants.EXPORT_ONCE_QUERY_MAX).stream().map(v -> {
                    TrackRetultNewStoreRankDetailDTO dto = new TrackRetultNewStoreRankDetailDTO();
                    BeanUtils.copyProperties(v, dto);
                    dto.setSuggestPhQty(v.getSuggestPhQty() == null ? "0" : v.getSuggestPhQty().setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toEngineeringString());
                    dto.setPhCost(v.getPhCost() == null ? "0" : v.getPhCost().setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toEngineeringString());
                    // 集中度
                    dto.setInStockRateDx(new BigDecimal(v.getInStockRateDx()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInStockRateCity(new BigDecimal(v.getInStockRateCity()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInStockRateQy(new BigDecimal(v.getInStockRateQy()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInStockRateMd(new BigDecimal(v.getInStockRateMd()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    // 动销率
                    dto.setInSalesRateDx(new BigDecimal(v.getInSalesRateDx()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInSalesRateCity(new BigDecimal(v.getInSalesRateCity()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInSalesRateQy(new BigDecimal(v.getInSalesRateQy()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setInSalesRateMd(new BigDecimal(v.getInSalesRateMd()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    // 毛利率
                    dto.setProfitRate90Dx(new BigDecimal(v.getProfitRate90Dx()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setProfitRate90City(new BigDecimal(v.getProfitRate90City()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setProfitRate90Qy(new BigDecimal(v.getProfitRate90Qy()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    dto.setProfitRate90Md(new BigDecimal(v.getProfitRate90Md()).multiply(hunderd).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                    //近90天销售金额
                    dto.setAmtCum90Dx(new BigDecimal(v.getAmtCum90Dx()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setAmtCum90City(new BigDecimal(v.getAmtCum90City()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setAmtCum90Qy(new BigDecimal(v.getAmtCum90Qy()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setAmtCum90Md(new BigDecimal(v.getAmtCum90Md()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    dto.setJyAbleDesc(v.getJyAble() == 1 ? "是" : "否");
                    return dto;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(trackRetultNewStoreAllDetailDTOS) && CollectionUtils.isEmpty(trackRetultNewStoreRankDetailDTOS)){
                    break;
                }
                if (CollectionUtils.isNotEmpty(trackRetultNewStoreAllDetailDTOS)) {
                    excelWriter.write(trackRetultNewStoreAllDetailDTOS, writeSheet);
                } else if (CollectionUtils.isEmpty(trackRetultNewStoreAllDetailDTOS) && i == 0){
                    excelWriter.write(trackRetultNewStoreAllDetailDTOS, writeSheet);
                }
                if (CollectionUtils.isNotEmpty(trackRetultNewStoreRankDetailDTOS)) {
                    excelWriter.write(trackRetultNewStoreRankDetailDTOS, otherChooseSheet);
                }else if (CollectionUtils.isEmpty(trackRetultNewStoreRankDetailDTOS) && i == 0){
                    excelWriter.write(trackRetultNewStoreRankDetailDTOS, otherChooseSheet);
                }
                logger.info("查询数据条数：{}", trackRetultNewStoreAllDetailDTOS.size());
            }
            excelWriter.finish();
        } catch (Exception e) {
            logger.error("生成文件失败,删除文件", e);
            throw e;
        }
//        finally {
//            delTempFile(filePath);
//        }
//        });
    }

}
