package com.cowell.scib.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.cowell.nyuwa.cos.util.CosService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.config.IdGenConfig;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.DgmsBdpTask;
import com.cowell.scib.entityTidb.DgmsBdpTaskExample;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.mapperTidb.DgmsBdpTaskMapper;
import com.cowell.scib.mapperTidb.extend.DgmsBdpTaskExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.necessaryComtentsV2.*;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCopyParam;
import com.cowell.scib.service.dto.necessaryContents.StoreGoodsQueryParam;
import com.cowell.scib.service.listener.LimitExcelReadListener;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.ExcelCheck;
import com.cowell.scib.utils.HutoolUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class NecessaryContentsV2ServiceImpl implements NecessaryContentsV2Service {

    private final Logger logger = LoggerFactory.getLogger(NecessaryContentsV2ServiceImpl.class);

    @Resource
    private NecessaryContentsMapper necessaryContentsMapper;
    @Resource
    private NecessaryLevelConfigMapper necessaryLevelConfigMapper;
    @Resource
    private NecessaryContentsExtendMapper necessaryContentsExtendMapper;
    @Resource
    private NecessaryLevelRoleConfigMapper necessaryLevelRoleConfigMapper;
    @Resource
    private CommonEnumsMapper commonEnumsMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SearchService searchService;
    @Resource
    private IscmService iscmService;
    @Resource
    private ForestService forestService;
    @Resource
    private TagService tagService;
    @Resource
    private StoreService storeService;
    @Resource
    private BundlTaskService bundlTaskService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RuleService ruleService;
    @Resource
    private ConfigOrgExtendMapper configOrgExtendMapper;
    @Resource
    private ConfigOrgDetailExMapper configOrgDetailExMapper;
    @Resource
    private MdmTaskMapper mdmTaskMapper;
    @Resource
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Resource
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;
    @Resource
    private StoreGoodsProcessMapper storeGoodsProcessMapper;
    @Resource
    private StoreGoodsProcessExtendMapper storeGoodsProcessExtendMapper;
    @Resource
    private AsyncExportFileService asyncExportFileService;
    @Resource
    private CosService cosService;
    @Resource
    private TocService tocService;
    @Resource
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;
    @Resource
    private StoreContentsFactory factory;
    @Resource
    private DgmsBdpTaskMapper dgmsBdpTaskMapper;
    @Resource
    private DgmsBdpTaskExtendMapper dgmsBdpTaskExtendMapper;
    @Resource
    @Qualifier("trackResultTaskExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;
    @Value("${scib.easyexcel.necessary.import.num:20000}")
    private Integer importNum;
    @Value("${dgms.mb.necessary.contents.v2.zbOrgId:3}")
    private Long zbOrgId;
    @Value("${dgms.mb.necessary.contents.v2.zbOrgName:高济健康}")
    private String zbOrgName;

    private static final String USER_DATA_STORE = "DGMS-USER-DATA-STORE-";
    private static final String IMPORT_NECESSARY_CONTENTS = "DGMS-IMPORT-NECESSARY-CONTENTS-";

    public static List<String> CollNameList=Lists.newArrayList("错误原因");
    @Override
    public PageResult<NecessaryContentsDTO> getContents(TokenUserDTO userDTO, NecessaryQueryParam param) {
        try {
            PageResult<NecessaryContentsDTO> pageResult = new PageResult<>(0L, new ArrayList<>());
            Map<Long, List<Long>> userDataMap = getUserDataMap(userDTO);
            if (MapUtils.isEmpty(userDataMap)) {
                logger.info("当前用户没有权限");
                return pageResult;
            }
            List<Long> platformOrgIds = new ArrayList<>();
            if (null != param.getPlatformOrgId()) {
                if (userDataMap.containsKey(param.getPlatformOrgId())) {
                    platformOrgIds.add(param.getPlatformOrgId());
                } else {
                    logger.info("没有所选查询平台的权限");
                    return pageResult;
                }
            } else {
                platformOrgIds.addAll(Lists.newArrayList(userDataMap.keySet()));
            }
            List<Long> companyOrgIds = new ArrayList<>();
            List<Long> userDataCompanyIds = userDataMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(param.getCompanyOrgIds())) {
                companyOrgIds.addAll(param.getCompanyOrgIds().stream().filter(v -> userDataCompanyIds.contains(v)).distinct().collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(companyOrgIds)) {
                    logger.info("没有所选查询连锁的权限");
                    return pageResult;
                }
            } else {
                companyOrgIds.addAll(userDataCompanyIds);
            }
            if (CollectionUtils.isEmpty(companyOrgIds)) {
                logger.info("没有所选查询连锁的权限");
                return pageResult;
            }
            NecessaryContentsExample example = new NecessaryContentsExample();
            NecessaryContentsExample.Criteria criteria = genQueryCondition(param, example);
            List<Long> orgIds = new ArrayList<>();
            orgIds.add(zbOrgId);
            orgIds.addAll(platformOrgIds);
            orgIds.addAll(companyOrgIds);
            criteria.andOrgIdIn(orgIds).andOrgTypeLessThan(OrgTypeEnum.STORE.getCode());
            NecessaryContentsExample.Criteria criteria1 = genQueryCondition(param, example);
            criteria1.andCompanyOrgIdIn(companyOrgIds).andOrgTypeEqualTo(OrgTypeEnum.STORE.getCode());
            example.or(criteria1);
            long count = necessaryContentsMapper.countByExample(example);
            if (count <= 0L) {
                return pageResult;
            }
            pageResult.setTotal(count);
            example.setLimit(param.getPerPage());
            example.setOffset(Long.valueOf(param.getPage() - 1) * param.getPerPage());
            CommonEnumsExample enumsExample = new CommonEnumsExample();
            enumsExample.createCriteria().andPropertyCodeEqualTo("necessaryReasonList").andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
            Map<String, String> chooseReasonMap = commonEnumsMapper.selectByExample(enumsExample).stream().collect(Collectors.toMap(CommonEnums::getEnumValue, CommonEnums::getEnumName, (k1, k2) -> k1));
            List<NecessaryContents> contentsList = necessaryContentsMapper.selectByExample(example);
            NecessaryLevelRoleConfigExample roleConfigExample = new NecessaryLevelRoleConfigExample();
            roleConfigExample.createCriteria().andNecessaryTagIn(contentsList.stream().map(NecessaryContents::getNecessaryTag).distinct().collect(Collectors.toList())).andOrgIdIn(contentsList.stream().map(v -> v.getPlatformOrgId() == null ? CacheVar.platformCacheMap.keySet().stream().findAny().get() : v.getPlatformOrgId()).collect(Collectors.toList()));
            Map<String, List<NecessaryLevelRoleConfig>> roleConfigMap = necessaryLevelRoleConfigMapper.selectByExample(roleConfigExample).stream().collect(Collectors.groupingBy(NecessaryLevelRoleConfig::getNecessaryTag));

            pageResult.setRows(contentsList.stream().map(v -> {
                NecessaryContentsDTO dto = new NecessaryContentsDTO();
                BeanUtils.copyProperties(v, dto);
                if (StringUtils.isNotBlank(v.getChooseReason())) {
                    String chooseReason = chooseReasonMap.get(v.getChooseReason());
                    dto.setChooseReason(chooseReason);
                }
                dto.setGroupOrgId(zbOrgId);
                dto.setGroupName(zbOrgName);
                dto.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                dto.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                List<NecessaryLevelRoleConfig> roleConfigs = roleConfigMap.get(dto.getNecessaryTag());
                if (CollectionUtils.isNotEmpty(roleConfigs)) {
                    NecessaryLevelRoleConfig roleConfig;
                    if (null != dto.getPlatformOrgId()) {
                        roleConfig = roleConfigs.stream().filter(c -> dto.getPlatformOrgId().equals(c.getOrgId())).findAny().orElseThrow(() -> new AmisBadRequestException("您没有权限编辑" + v.getNecessaryTagName()));
                    } else {
                        roleConfig = roleConfigs.get(0);
                    }
                    if (StringUtils.isBlank(roleConfig.getRoles()) || !Arrays.stream(StringUtils.split(roleConfig.getRoles(), ",")).filter(c -> userDTO.getRoles().contains(c)).findAny().isPresent()) {
                        dto.setModifyAble(false);
                    } else {
                        dto.setModifyAble(true);
                    }

                } else {
                    logger.info("没有查询到必备标签配置:{}", dto.getNecessaryTag());
                    dto.setModifyAble(false);
                }
                return dto;
            }).collect(Collectors.toList()));
            return pageResult;
        } catch (Exception e) {
            logger.error("查询必备目录异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public String editNecessaryGoods(TokenUserDTO userDTO, NecessaryAddParam param, MdmTask task) {
        logger.info("editNecessaryGoods param:{}", JSON.toJSONString(param));
        try {
            if (StringUtils.isBlank(param.getNecessaryTag())) {
                throw new AmisBadRequestException("请选择必备标签");
            }
            if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                 throw new AmisBadRequestException("请选择商品");
            }
            NecessaryContentsExample example = new NecessaryContentsExample();
            NecessaryContentsExample.Criteria criteria = example.createCriteria();
            criteria.andNecessaryTagEqualTo(param.getNecessaryTag());
            NecessaryLevelConfigExample configExample = new NecessaryLevelConfigExample();
            configExample.createCriteria().andNecessaryTagEqualTo(param.getNecessaryTag());
            NecessaryLevelConfig config = necessaryLevelConfigMapper.selectByExample(configExample).stream().findAny().orElseThrow(() -> new AmisBadRequestException("必备标签不存在,请重新选择"));
            NecessaryLevelRoleConfigExample roleConfigExample = new NecessaryLevelRoleConfigExample();
            roleConfigExample.createCriteria().andNecessaryTagEqualTo(config.getNecessaryTag()).andOrgIdEqualTo(null == param.getPlatformOrgId() ? CacheVar.platformCacheMap.keySet().stream().findAny().get(): param.getPlatformOrgId());
            NecessaryLevelRoleConfig roleConfig = necessaryLevelRoleConfigMapper.selectByExample(roleConfigExample).stream().findAny().orElseThrow(() -> new AmisBadRequestException("您没有权限编辑" + config.getNecessaryTagName()));
            if (StringUtils.isBlank(roleConfig.getRoles()) || !Arrays.stream(StringUtils.split(roleConfig.getRoles(), ",")).filter(v -> userDTO.getRoles().contains(v)).findAny().isPresent()) {
                throw new AmisBadRequestException("您没有权限编辑" + config.getNecessaryTagName());
            }
            OrgTypeEnum orgTypeEnum = Optional.ofNullable(OrgTypeEnum.getEnumByCode(config.getOrgLevel())).orElseThrow(() -> new AmisBadRequestException("必备层级配置有误,请配置必备标签对应的组织层级"));
            NecessaryConfigAreaEnum configAreaEnum = Optional.ofNullable(NecessaryConfigAreaEnum.getEnumByCode(config.getAreaLevel())).orElseThrow(() -> new AmisBadRequestException("必备层级配置有误,请配置必备标签对应的区域层级"));
            List<CommonEnums> commonEnums = new ArrayList<>();
            if (StringUtils.isNotBlank(config.getStoreType())) {
                if (CollectionUtils.isEmpty(param.getStoreTypes())) {
                    throw new AmisBadRequestException("请选择店型");
                }
                CommonEnumsExample enumsExample = new CommonEnumsExample();
                enumsExample.createCriteria().andPropertyCodeIn(Arrays.stream(StringUtils.split(config.getStoreType())).collect(Collectors.toList())).andEnumValueIn(param.getStoreTypes()).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
                commonEnums = commonEnumsMapper.selectByExample(enumsExample);
                List<String> storeTypes = commonEnums.stream().map(CommonEnums::getEnumValue).distinct().collect(Collectors.toList());
                List<String> noExists = param.getStoreTypes().stream().filter(v -> !storeTypes.contains(v)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noExists)) {
                    throw new AmisBadRequestException("所选店型[" + noExists.stream().collect(Collectors.joining(",")) + "]不在必备标签配置的店型中,请重新选择");
                }
                criteria.andStoreTypeIn(param.getStoreTypes());
            }
            criteria.andGoodsNoIn(param.getGoodsNos());
            List<NecessaryContents> contentsList = new ArrayList<>();
            NecessaryContents content = new NecessaryContents();
            content.setNecessaryTag(config.getNecessaryTag());
            content.setNecessaryTagName(config.getNecessaryTagName());
            content.setChooseReason(param.getChooseReason());
            content.setStatus(Constants.NORMAL_STATUS);
            CommonUserDTO commonUserDTO = new CommonUserDTO(userDTO);
            BeanUtils.copyProperties(commonUserDTO, content);
            contentsList.add(content);
//            checkAssembleOrgType(param, orgTypeEnum, criteria, contentsList)
//                    .checkAssembleArea(param, configAreaEnum, criteria, contentsList)
//                    .assembleGoodsInfo(param.getGoodsNos(), existsGoods, contentsList)
//                    .assembleStoreTypeProp(param, commonEnums, contentsList);
            NecessaryContentsV2ServiceImpl service = checkAssembleOrgType(param, orgTypeEnum, criteria, contentsList);
            service = checkAssembleArea(param, configAreaEnum, criteria, contentsList);
            List<NecessaryContents> necessaryContents = necessaryContentsMapper.selectByExample(example);
            List<String> existsGoods = necessaryContents.stream().filter(v -> Constants.NORMAL_STATUS.equals(v.getStatus())).map(NecessaryContents::getGoodsNo).distinct().collect(Collectors.toList());
            List<NecessaryContents> existsDelGoods = necessaryContents.stream().filter(v -> Constants.DEL_STATUS.equals(v.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existsGoods)) {
                if (!Boolean.TRUE.equals(param.getExistsIgnore())) {
                    return "商品编码:" + existsGoods.stream().collect(Collectors.joining(",")) + "已添加必备，无需重复添加。是否排除掉重复商品，继续添加？";
                } else {
                    if (existsGoods.size() == param.getGoodsNos().size()) {
                        throw new AmisBadRequestException("商品均已添加过,无需重复添加");
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(existsDelGoods)) {
                NecessaryContentsExample updateExample = new NecessaryContentsExample();
                updateExample.createCriteria().andIdIn(existsDelGoods.stream().map(NecessaryContents::getId).collect(Collectors.toList()));
                necessaryContentsMapper.deleteByExample(updateExample);
            }
            Map<String, String> goodsBusiscope = new HashMap<>();
            service = assembleGoodsInfo(param.getGoodsNos(), existsGoods, contentsList, goodsBusiscope);
            service = assembleStoreTypeProp(param, commonEnums, contentsList);
            Lists.partition(contentsList, Constants.INSERT_MAX_SIZE).forEach(v -> necessaryContentsExtendMapper.batchInsert(v));
            logger.info("goodsBusiscope:{}", JSON.toJSONString(goodsBusiscope));
            task = dealContensByEdit(userDTO, contentsList, commonEnums, task, goodsBusiscope);
            return "更新MDM任务创建成功，任务号：" + task.getId() + "，更新结果可去更新MDM任务管理模块查看";
        } catch (Exception e) {
            logger.error("添加必备目录异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    // 页面添加可用
    private MdmTask dealContens(TokenUserDTO userDTO, List<NecessaryContents> contentsList, List<CommonEnums> commonEnums, MdmTask task, Map<String, String> goodsLineMap) {
        if (null == task) {
            task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(0);
            mdmTaskMapper.insertSelective(task);
        }
        Long taskId = task.getId();
        asyncTaskExecutor.execute(() -> {
            Map<String, List<NecessaryContents>> contentsMap = contentsList.stream().collect(Collectors.groupingBy(NecessaryContents::getGoodsNo));
            Map<String, CommonEnums> enumsMap = Optional.ofNullable(commonEnums).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CommonEnums::getEnumValue, Function.identity(), (k1, k2) -> k1));
            List<String> goodsNos = contentsList.stream().map(NecessaryContents::getGoodsNo).distinct().collect(Collectors.toList());
            Map<String, Map<String, String>> componentMap = new HashMap<>();
            Lists.partition(goodsNos, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
                componentMap.putAll(forestService.querySpuProperties(Lists.newArrayList( "busiscope"), v, null));
            });
            logger.info("componentMap:{}", JSON.toJSONString(componentMap));
            contentsMap.forEach((k,v) -> {
                v.forEach(contents -> {
                    List<Long> userDataStore = getUserDataStore(userDTO, contents.getOrgId(), contents.getOrgType(), contents.getStatus());
                    if (CollectionUtils.isEmpty(userDataStore)) {
                        logger.info("没有查询到组货门店");
                        return;
                    }
                    CommonEnums storeTypeEnum = StringUtils.isNotBlank(contents.getStoreType()) ? enumsMap.get(contents.getStoreType()) : null;
                    List<OrgInfoBaseCache> storeInfos = getEffectStore(userDTO, storeTypeEnum, contents, userDataStore);
                    if (CollectionUtils.isEmpty(storeInfos)) {
                        logger.info("目录{}下没有合适的门店", contents.getId());
                        return;
                    }
                    // 成分 标品属性 component
                    Map<String, String> component = componentMap.get(contents.getGoodsNo());
                    logger.info("component:{}", JSON.toJSONString(component));
                    Map<String, String> goodsBusiscope = new HashMap<>();
                    if (MapUtils.isNotEmpty(component)) {
                        goodsBusiscope.put(contents.getGoodsNo(), component.get("busiscope"));
                    }
                    List<StoreGoodsContentDTO> storeGoodsContentDTOS = genStoreInfo(userDTO, contents, storeInfos, goodsBusiscope, taskId);
                    Map<String, StoreGoodsContentDTO> collect = storeGoodsContentDTOS.stream().collect(Collectors.toMap(c -> c.getStoreId() + "-" + c.getNecessaryTag() + c.getGoodsNo(), Function.identity(), (k1, k2) -> k1));
                    StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NECESSARY.getCode());
                    if (null != goodsLineMap) {
                        assemble.setGoodsline(goodsLineMap.get(contents.getGoodsNo()));
                    }
                    assemble.work(Lists.newArrayList(collect.values()));
                });
            });
        });
        return task;
    }
    // 编辑时用
    private MdmTask dealContensByEdit(TokenUserDTO userDTO, List<NecessaryContents> contentsList, List<CommonEnums> commonEnums, MdmTask task, Map<String, String> goodsBusiscope) {
        logger.info("开始处理目录:");
        if (null == task) {
            task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(0);
            mdmTaskMapper.insertSelective(task);
        }
        Long taskId = task.getId();
        asyncTaskExecutor.execute(() -> {
            Map<String, CommonEnums> enumsMap = Optional.ofNullable(commonEnums).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CommonEnums::getEnumValue, Function.identity(), (k1, k2) -> k1));
            for (NecessaryContents contents : contentsList) {
                List<Long> userDataStore = getUserDataStore(userDTO, contents.getOrgId(), contents.getOrgType(), contents.getStatus());
                if (CollectionUtils.isEmpty(userDataStore)) {
                    logger.info("目录:{}没有查询到组货门店", contents.getId());
                    continue;
                }
                CommonEnums storeTypeEnum = StringUtils.isNotBlank(contents.getStoreType()) ? enumsMap.get(contents.getStoreType()) : null;
                List<OrgInfoBaseCache> storeInfos = getEffectStore(userDTO, storeTypeEnum, contents, userDataStore);
                if (CollectionUtils.isEmpty(storeInfos)) {
                    logger.info("目录{}下没有合适的门店", contents.getId());
                    continue;
                }
                List<StoreGoodsContentDTO> storeGoodsContentDTOS = genStoreInfo(userDTO, contents, storeInfos, goodsBusiscope, taskId);
                StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NECESSARY.getCode());
                assemble.work(storeGoodsContentDTOS);
            }
            logger.info("添加完毕");
        });
        return task;
    }

    private List<StoreGoodsContentDTO> genStoreInfo(TokenUserDTO userDTO, NecessaryContents contents, List<OrgInfoBaseCache> storeInfos, Map<String, String> goodsBusiscope, Long taskId) {
        logger.info("goodsBusiscope:{}", JSON.toJSONString(goodsBusiscope));

        return storeInfos.stream().map(v -> {
            StoreGoodsContentDTO contentDTO = new StoreGoodsContentDTO();
            BeanUtils.copyProperties(contents, contentDTO);
            contentDTO.setNecessaryTag(Integer.valueOf(contents.getNecessaryTag()));
            contentDTO.setBusinessId(v.getBusinessId());
            contentDTO.setStoreId(v.getOutId());
            contentDTO.setPlatformOrgId(v.getPlatformOrgId());
            contentDTO.setDelContent(Constants.DEL_STATUS.equals(contents.getStatus()));
            contentDTO.setBizType(StoreContentBizTypeEnum.NECESSARY.getCode());
            contentDTO.setScopeName(null != goodsBusiscope ? goodsBusiscope.get(contents.getGoodsNo()) : "");
            contentDTO.setMdmTaskId(taskId);
            return contentDTO;
        }).collect(Collectors.toList());
    }


    private List<Long> getUserDataStore(TokenUserDTO userDTO, Long orgId, Integer orgType, Byte status) {
        List<OrgInfoBaseCache> userDataStoreOrgIds = getUserDataCache(orgId, orgType);
        logger.info("orgId:{},orgType:{}userDataStoreOrgIds:{}", orgId,orgType,JSON.toJSONString(userDataStoreOrgIds));
        if (Constants.DEL_STATUS.equals(status)) {
            logger.info("删除直接返回userDataStoreOrgIds:{}", JSON.toJSONString(userDataStoreOrgIds));
            return userDataStoreOrgIds.stream().map(OrgInfoBaseCache::getId).collect(Collectors.toList());
        }
        List<Long> platformOrgIds = userDataStoreOrgIds.stream().map(v -> v.getPlatformOrgId()).distinct().collect(Collectors.toList());
        List<Long> selectorStoreIdList = new ArrayList<>();
        for (Long platformOrgId : platformOrgIds) {
            selectorStoreIdList.addAll(bundlTaskService.selectMdmStoreIdFilterSelector(platformOrgId, userDTO.getUserId(), null));
        }
        logger.info("selectorStoreIdList:{}", JSON.toJSONString(selectorStoreIdList));
        if (CollectionUtils.isEmpty(selectorStoreIdList)) {
            logger.info("组织:{}没有查询到符合条件的组货门店", orgId);
            return new ArrayList<>();
        }
//        List<Long> selectorStoreOrgIdList = CacheVar.getStoreByStoreIdList(selectorStoreIdList).orElse(new ArrayList<>()).stream().map(OrgInfoBaseCache::getId).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(selectorStoreOrgIdList)) {
//            logger.info("组织:{}门店选择器没有找到门店信息", orgId);
//            return new ArrayList<>();
//        }
        return userDataStoreOrgIds.stream().filter(v -> selectorStoreIdList.contains(v.getOutId())).map(OrgInfoBaseCache::getId).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(selectorStoreOrgIdList)) {
//            logger.info("组织:{}门店的权限交集为空", orgId);
//            return new ArrayList<>();
//        }
//        return selectorStoreIdList;
    }

    private List<OrgInfoBaseCache> getUserDataCache(Long orgId, Integer orgType) {
        OrgTypeEnum orgTypeEnum = OrgTypeEnum.getEnumByCode(orgType);
        if (null == orgTypeEnum) {
            return new ArrayList<>();
        }
        switch (orgTypeEnum) {
            case GROUP: return Lists.newArrayList(CacheVar.storeCacheMap.values());
            case PLATFORM: return CacheVar.getStoreListByPlatformOrgId(orgId);
            case BUSINESS: return CacheVar.getStoreListByBusinessOrgId(orgId);
            case STORE: return Lists.newArrayList(CacheVar.getStoreByOrgId(orgId)).stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            default:return new ArrayList<>();
        }
    }
    public List<OrgInfoBaseCache> getEffectStore(TokenUserDTO userDTO, CommonEnums enums, NecessaryContents contents, List<Long> userDataStore) {
        logger.info("storeTypeEnum:{}", JSON.toJSONString(enums));
        if (null != enums && null != enums.getTagId()) {
            StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
            queryParam.setId(enums.getTagId());
            List<Long> tagStoreIds = tagService.getSelectStoreIdListByType(queryParam);
            if (CollectionUtils.isEmpty(tagStoreIds)) {
                logger.info("店型:{} 选择器:{}下没有维护门店", enums.getEnumValue(), enums.getTagId());
                return new ArrayList<>();
            }
            List<Long> tagStoreOrgIds = tagStoreIds.stream().map(v -> CacheVar.getStoreByStoreId(v)).filter(Optional::isPresent).map(v -> v.get().getId()).collect(Collectors.toList());
            logger.info("tagStoreOrgIds:{}", JSON.toJSONString(tagStoreOrgIds));
            logger.info("tagStoreOrgIds.size:{}", CollectionUtils.isEmpty(tagStoreOrgIds) ? 0 : tagStoreOrgIds.size());
            userDataStore = userDataStore.stream().filter(v -> tagStoreOrgIds.contains(v)).collect(Collectors.toList());
        }
        Iterator<Long> iterator = userDataStore.iterator();
        List<OrgInfoBaseCache> storeInfos = new ArrayList<>();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            Optional<MdmStoreExDTO> optional = CacheVar.getStoreExtInfoByStoreOrgId(next);
            if (!optional.isPresent()) {
                iterator.remove();
                continue;
            }
            MdmStoreExDTO storeExDTO = optional.get();
            if (StringUtils.isNotBlank(contents.getProvince()) && !contents.getProvince().equals(storeExDTO.getProvince())) {
                iterator.remove();
                continue;
            }
            if (StringUtils.isNotBlank(contents.getCity()) && !contents.getCity().equals(storeExDTO.getCity())) {
                iterator.remove();
                continue;
            }
            if (StringUtils.isNotBlank(contents.getArea()) && !contents.getArea().equals(storeExDTO.getArea())) {
                iterator.remove();
                continue;
            }
            if (null != contents.getStoreId() && !contents.getStoreId().equals(storeExDTO.getStoreId())) {
                iterator.remove();
                continue;
            }
            Optional<OrgInfoBaseCache> storeOpt = CacheVar.getStoreByStoreId(storeExDTO.getStoreId());
            if (!storeOpt.isPresent()) {
                iterator.remove();
                continue;
            }
            OrgInfoBaseCache store = storeOpt.get();
            if (null != contents.getPlatformOrgId() && !contents.getPlatformOrgId().equals(store.getPlatformOrgId())) {
                iterator.remove();
                continue;
            }
            if (null != contents.getCompanyOrgId() && !contents.getCompanyOrgId().equals(store.getBusinessOrgId())) {
                iterator.remove();
                continue;
            }
            if (null == store.getBusinessId()) {
                iterator.remove();
                continue;
            }
            storeInfos.add(store);
        }
        if (CollectionUtils.isEmpty(storeInfos)) {
            logger.info("目录:{}组织:{}所属门店没有找到缓存", contents.getId(), contents.getOrgId());
            return new ArrayList<>();
        }
        return storeInfos;
    }

    @Override
    public String modify(TokenUserDTO userDTO, List<Long> ids, Byte status, String invalidReason, MdmTask task, Map<String, String> goodsLineMap) {
        try {
            if (CollectionUtils.isEmpty(ids) || null == status) {
                logger.info("id/status为空");
                throw new AmisBadRequestException("请选择需要变更的目录");
            }
            NecessaryContentsExample example = new NecessaryContentsExample();
            example.createCriteria().andIdIn(ids);
            List<NecessaryContents> contentsList = necessaryContentsMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(contentsList)) {
                logger.info("id={}的必备目录商品不存在", ids);
                throw new AmisBadRequestException("必备目录不存在");
            }
            contentsList = contentsList.stream().filter(v -> !v.getStatus().equals(status)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentsList)) {
                throw new BusinessErrorException("所选单据已变更,不需要再次修改");
            }
            if (!Constants.SYS_USER_ID.equals(userDTO.getUserId())) {
                NecessaryLevelRoleConfigExample roleConfigExample = new NecessaryLevelRoleConfigExample();
                roleConfigExample.createCriteria().andNecessaryTagIn(contentsList.stream().map(NecessaryContents::getNecessaryTag).distinct().collect(Collectors.toList())).andOrgIdIn(contentsList.stream().map(v -> v.getPlatformOrgId() == null ? CacheVar.platformCacheMap.keySet().stream().findAny().get() : v.getPlatformOrgId()).collect(Collectors.toList()));
                Map<String, List<NecessaryLevelRoleConfig>> roleConfigs = necessaryLevelRoleConfigMapper.selectByExample(roleConfigExample).stream().collect(Collectors.groupingBy(NecessaryLevelRoleConfig::getNecessaryTag));
                contentsList.forEach(v -> {
                    List<NecessaryLevelRoleConfig> configs = roleConfigs.get(v.getNecessaryTag());
                    if (CollectionUtils.isEmpty(configs)) {
                        throw new AmisBadRequestException("您没有权限编辑" + v.getNecessaryTagName());
                    }
                    NecessaryLevelRoleConfig roleConfig;
                    if (null != v.getPlatformOrgId()) {
                        roleConfig = configs.stream().filter(c -> v.getPlatformOrgId().equals(c.getOrgId())).findAny().orElseThrow(() -> new AmisBadRequestException("您没有权限编辑" + v.getNecessaryTagName()));
                    } else {
                        roleConfig = configs.get(0);
                    }
                    if (StringUtils.isBlank(roleConfig.getRoles()) || !Arrays.stream(StringUtils.split(roleConfig.getRoles(), ",")).filter(c -> userDTO.getRoles().contains(c)).findAny().isPresent()) {
                        throw new AmisBadRequestException("您没有权限编辑" + v.getNecessaryTagName());
                    }
                });
            }
            if (!(Constants.NORMAL_STATUS.equals(status) || Constants.DEL_STATUS.equals(status))) {
                logger.info("status={}参数错误", status);
                throw new AmisBadRequestException("请选择需要变更的目录");
            }
            NecessaryContents update = new NecessaryContents();
            update.setStatus(status.byteValue());
            update.setUpdatedBy(userDTO.getUserId());
            update.setUpdatedName(userDTO.getName());
            update.setGmtUpdate(new Date());
            if (Constants.DEL_STATUS.equals(status) && StringUtils.isNotBlank(invalidReason)) {
                update.setInvalidReason(invalidReason);
            }
            NecessaryContentsExample updateExample = new NecessaryContentsExample();
            updateExample.createCriteria().andIdIn(contentsList.stream().map(NecessaryContents::getId).collect(Collectors.toList()));
            necessaryContentsMapper.updateByExampleSelective(update, updateExample);
            NecessaryLevelConfigExample configExample = new NecessaryLevelConfigExample();
            configExample.createCriteria().andNecessaryTagIn(contentsList.stream().map(NecessaryContents::getNecessaryTag).distinct().collect(Collectors.toList()));
            List<NecessaryLevelConfig> configs = necessaryLevelConfigMapper.selectByExample(configExample).stream().filter(v -> StringUtils.isNotBlank(v.getStoreType())).collect(Collectors.toList());
            List<CommonEnums> commonEnums = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(configs) && configs.stream().map(v -> v.getStoreType()).filter(StringUtils::isNotBlank).findAny().isPresent()) {
                CommonEnumsExample enumsExample = new CommonEnumsExample();
                enumsExample.createCriteria().andPropertyCodeIn(Arrays.stream(StringUtils.split(configs.stream().map(NecessaryLevelConfig::getStoreType).collect(Collectors.joining(",")), ",")).collect(Collectors.toList())).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
                commonEnums = commonEnumsMapper.selectByExample(enumsExample);
            }
            task = dealContens(userDTO, contentsList.stream().map(v -> {
                v.setStatus(status);
                return v;
            }).collect(Collectors.toList()), commonEnums, task, goodsLineMap);
            return "更新MDM任务创建成功，任务号：" + task.getId() + "，更新结果可去更新MDM任务管理模块查看";
        } catch (Exception e) {
            logger.error("停用/启用必备目录商品异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void exportContents(TokenUserDTO userDTO, NecessaryQueryParam param) {
        try {
        param.setToPage(false);
        String fileName = "必备目录管理" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
        asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.NECESSARY_CONTENT, userDTO, new HandlerDataExportService<NecessaryContentsDTO>() {
            @Override
            public List<NecessaryContentsDTO> getDataToExport() {
                return null;
            }

            @Override
            public List<NecessaryContentsDTO> getDataToExport(Integer page, Integer pageSize) {
                param.setPage(page + 1);
                param.setPerPage(pageSize);
                return getContents(userDTO, param).getRows();
            }
            @Override
            public boolean isPageable() {
                return true;
            }
            @Override
            public LinkedHashMap<String, String> getFieldMap() {
                return NecessaryContentsDTO.getExportMap();
            }
        });

    } catch (Exception e) {
        logger.error("导出必备目录异常", e);
        throw new AmisBadRequestException(e.getMessage());
    }
}

    @Override
    public ImportResult importContent(MultipartFile file, Integer bizType, TokenUserDTO userDTO) throws Exception {
        String key = IMPORT_NECESSARY_CONTENTS + userDTO.getUserId() + "-" + bizType;
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new AmisBadRequestException("您当前导入任务正在进行中,请稍后重试。");
        }
        checkFile(file);
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new NecessaryContentsImportData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new AmisBadRequestException(s);
        }
        result.setResult(key);
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result, 12, TimeUnit.HOURS);
        try (InputStream in = file.getInputStream()) {
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            List<NecessaryContentsImportData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(NecessaryContentsImportData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncTaskExecutor.execute(() -> {
                dealImportData(excelList, result, key, rBucket, userDTO);
            });
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入必备目录文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入必备目录文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }
    @Override
    public ImportResult importDelContent(MultipartFile file, Integer bizType, TokenUserDTO userDTO) throws Exception {
        String key = IMPORT_NECESSARY_CONTENTS + userDTO.getUserId() + "-" + bizType;
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new AmisBadRequestException("您当前导入任务正在进行中,请稍后重试。");
        }
        checkFile(file);
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new NecessaryContentsImportDelData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new AmisBadRequestException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        result.setResult(key);
        rBucket.set(result, 12, TimeUnit.HOURS);
        try (InputStream in = file.getInputStream()) {
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            List<NecessaryContentsImportDelData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(NecessaryContentsImportDelData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncTaskExecutor.execute(() -> {
                dealDelImportData(excelList, result, key, rBucket, userDTO);
            });
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入必备目录文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入必备目录文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }

    @Override
    public void deleteStoreGoods(List<Long> orgIds, TokenUserDTO userDTO, List<OrgInfoBaseCache> orgInfoBaseCaches) {
        try {
            if (CollectionUtils.isEmpty(orgIds)) {
                throw new AmisBadRequestException("单门店取消必备参数错误");
            }
            if (org.apache.commons.collections.CollectionUtils.isEmpty(orgInfoBaseCaches)) {
                Optional<List<OrgInfoBaseCache>> storeByStoreOrgIdList = CacheVar.getStoreByStoreOrgIdList(orgIds);
                if (!storeByStoreOrgIdList.isPresent()) {
                    throw new BusinessErrorException("获取门店编码错误");
                }
                orgInfoBaseCaches = storeByStoreOrgIdList.get();
            }
            for (OrgInfoBaseCache orgInfoBaseCache : orgInfoBaseCaches) {
                StoreGoodsContentsExample storeGoodsInfoExample = new StoreGoodsContentsExample();
                storeGoodsInfoExample.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andNecessaryTagGreaterThan(0);
                List<StoreGoodsContents> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(storeGoodsInfoExample);
                if (CollectionUtils.isEmpty(storeGoodsInfos)) {
                    continue;
                }
                StoreGoodsProcessExample example = new StoreGoodsProcessExample();
                example.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId());
                storeGoodsProcessMapper.deleteByExample(example);
//                storeGoodsInfos.stream().collect(Collectors.groupingBy(StoreGoodsContents::getNecessaryTag)).forEach((k,v) -> {
//                    StoreGoodsProcessExample example = new StoreGoodsProcessExample();
//                    example.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andGoodsNoIn(v.stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList())).andNecessaryTagEqualTo(k);
//                    storeGoodsProcessMapper.deleteByExample(example);
//                });
                List<String> goodsNoList = storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList());
                Map<String, SpuListVo> spuMap = new HashMap<>();
                Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
                MdmTask mdmTask = new MdmTask();
                saveMdmTask(mdmTask, userDTO, storeGoodsInfos, orgInfoBaseCache);
                List<Long> mdmTaskDetailIds = getMdmTaskDetailIds(storeGoodsInfos.size() + 1);
                List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
                for (int i = 0; i < storeGoodsInfos.size(); i++) {
                    StoreGoodsContents storeGoodsInfo = storeGoodsInfos.get(i);
                    if (Objects.isNull(storeGoodsInfo)) {
                        continue;
                    }
                    MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
                    buildMdmTaskDetail(userDTO, spuMap, mdmTask, mdmTaskDetailIds, i, storeGoodsInfo, mdmTaskDetail,orgInfoBaseCache);
                    mdmTaskDetails.add(mdmTaskDetail);
                }
                if (CollectionUtils.isNotEmpty(mdmTaskDetails)){
                    StoreGoodsContentsExample delExample = new StoreGoodsContentsExample();
                    delExample.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andIdIn(storeGoodsInfos.stream().map(StoreGoodsContents::getId).collect(Collectors.toList()));
                    List<StoreGoodsContents> olds = storeGoodsContentsMapper.selectByExample(delExample).stream().map(v -> buildDelContents(v, userDTO)).collect(Collectors.toList());
                    storeGoodsContentsExtendMapper.batchUpdate(olds, orgInfoBaseCache.getOutId());
                    //推送mdm
                    List<List<MdmTaskDetail>> partition = Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE);
                    for(List<MdmTaskDetail> v : partition) {
                        mdmTaskDetailExtendMapper.batchInsert(v);
                        try {
                            factory.getAssemble(1).pushMdm(v);
                        } catch (Exception e) {
                            logger.warn("推送mdm失败", e);
                            mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), mdmTaskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                        }
                    }
                }else {
                    mdmTaskMapper.deleteByPrimaryKey(mdmTask.getId());
                }
            }
        } catch (Exception e){
            logger.error("单门店取消必备失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    private StoreGoodsContents buildDelContents(StoreGoodsContents contents, TokenUserDTO userDTO) {
        contents.setUpdatedBy(userDTO.getUserId());
        contents.setUpdatedName(userDTO.getName());
        contents.setNecessaryTag(0);
        contents.setNecessaryTagName("");
        if (ManageStatusEnum.MANAGE_NECESARY.getCode().equals(contents.getManageStatus())) {
            contents.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
            contents.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
        }
        contents.setMinDisplayQuantity(BigDecimal.ZERO);
        return contents;
    }

    private MdmTaskDetail getMdmTaskDetail(OrgInfoBaseCache targetStore, MdmTask mdmTask, Map<String, SpuListVo> spuMap, List<Long> detailIds, StoreGoodsContents storeGoodsInfo, TokenUserDTO userDTO) {
        MdmTaskDetail taskDetail = new MdmTaskDetail();
        BeanUtils.copyProperties(storeGoodsInfo, taskDetail);
        taskDetail.setId(detailIds.remove(0));
        taskDetail.setTaskId(mdmTask.getId());
        taskDetail.setStoreName(targetStore.getShortName());
        taskDetail.setCreatedBy(userDTO.getUserId());
        taskDetail.setCreatedName(userDTO.getName());
        taskDetail.setUpdatedBy(userDTO.getUserId());
        taskDetail.setUpdatedName(userDTO.getName());
        SpuListVo spuListVo = spuMap.get(storeGoodsInfo.getGoodsNo());
        if (Objects.nonNull(spuListVo)) {
            taskDetail.setBarCode(spuListVo.getBarCode());
            taskDetail.setGoodsCommonName(spuListVo.getCurName());
            taskDetail.setGoodsName(spuListVo.getName());
            taskDetail.setGoodsUnit(spuListVo.getGoodsunit());
            taskDetail.setDescription(spuListVo.getDescription());
            taskDetail.setSpecifications(spuListVo.getJhiSpecification());
            taskDetail.setDosageForm(spuListVo.getDosageformsid());
            taskDetail.setManufacturer(spuListVo.getFactoryid());
            taskDetail.setApprovalNumber(spuListVo.getApprdocno());
        }
        taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        return taskDetail;
    }

    @Override
    public String importCopyStore(MultipartFile file, TokenUserDTO userDTO) {
        try {
            RBucket<CommonProcessDTO<List<ImportStoreDTO>>> bucket = redissonClient.getBucket(BATCH_IMPORT_COPY_STORE + userDTO.getUserId());
            if(bucket.isExists()) {
                if (bucket.get().getProcessFinished()) {
                    bucket.delete();
                } else {
                    throw new BusinessErrorException("你当前有批量任务正在进行,请稍后操作");
                }
            }
            if (file == null) {
                throw new BusinessErrorException("导入文件为空");
            }
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new BusinessErrorException("导入文件大于2M");
            }
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put("来源门店编码", "sourceStoreCode");
            map.put("目标门店编码", "targetStoreCode");
            List<ImportStoreDTO> importStoreCodes = HutoolUtil.excelToList(file.getInputStream(), map, ImportStoreDTO.class, 2);
            List<ImportStoreDTO> errorList = new ArrayList<>();
            Iterator<ImportStoreDTO> iterator = importStoreCodes.iterator();
            while (iterator.hasNext()) {
                ImportStoreDTO next = iterator.next();
                if (StringUtils.isBlank(next.getSourceStoreCode())) {
                    next.setErrorMsg("第" + next.getLineNum() + "行来源门店编码为空");
                    errorList.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getTargetStoreCode())) {
                    next.setErrorMsg("第" + next.getLineNum() + "行目标门店编码为空");
                    errorList.add(next);
                    iterator.remove();
                    continue;
                }
                next.setSourceStoreCode(next.getSourceStoreCode().trim());
                next.setTargetStoreCode(next.getTargetStoreCode().trim());
            }
            if (org.apache.commons.collections.CollectionUtils.isEmpty(importStoreCodes)) {
                throw new BusinessErrorException("导入文件为空");
            }
            if (importStoreCodes.size() > Constants.FEIGN_ONCE_QUERY_MAX) {
                throw new BusinessErrorException("批量操作最多支持" + Constants.FEIGN_ONCE_QUERY_MAX + "行，已超出，系统无法处理。");
            }
            CommonProcessDTO<List<ImportStoreDTO>> processDTO = new CommonProcessDTO<>();
            processDTO.setT(errorList);
            processDTO.setProcessCount(importStoreCodes.size());
            processDTO.setErrorCount(errorList.size());
            bucket.set(processDTO, 1L, TimeUnit.HOURS);
            Map<String, List<ImportStoreDTO>> importMap = importStoreCodes.stream().collect(Collectors.groupingBy(ImportStoreDTO::getSourceStoreCode));
            MdmTask mdmTask = new MdmTask();
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), mdmTask);
            mdmTask.setTaskSource(MdmTaskSourceEnum.STORE_COPY.getCode());
            mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            mdmTask.setDetailCount(0);
            mdmTaskMapper.insertSelective(mdmTask);
            Set<String> distinctSet = new HashSet<>();
            asyncTaskExecutor.execute(() -> {
                for (ImportStoreDTO storeDTO : importStoreCodes) {
                    try {
                        Optional<OrgInfoBaseCache> sourceStore = CacheVar.getStoreBySapCode(storeDTO.getSourceStoreCode());
                        Optional<OrgInfoBaseCache> targetStore = CacheVar.getStoreBySapCode(storeDTO.getTargetStoreCode());
                        if (!sourceStore.isPresent()) {
                            logger.info("来源门店编码:{}没查询到对应门店", storeDTO.getSourceStoreCode());
                            errorProcess(bucket, errorList, processDTO, storeDTO, "来源门店编码没查询到对应门店");
                            continue;
                        }
                        if (!targetStore.isPresent()) {
                            logger.info("目标门店编码:{}没查询到对应门店", storeDTO.getTargetStoreCode());
                            errorProcess(bucket, errorList, processDTO, storeDTO, "目标门店编码没查询到对应门店");
                            continue;
                        }
                        int perSize = distinctSet.size();
                        distinctSet.add(storeDTO.getSourceStoreCode() + storeDTO.getTargetStoreCode());
                        if (perSize == distinctSet.size()) {
                            errorProcess(bucket, errorList, processDTO, storeDTO, "该行重复");
                            continue;
                        }
                        StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                        StoreGoodsContentsExample.Criteria criteria = example.createCriteria();
                        criteria.andStoreIdEqualTo(sourceStore.get().getOutId()).andNecessaryTagGreaterThan(0).andSubCategoryIdNotBetween(12020000L, 12029999L);
                        Long count = storeGoodsContentsMapper.countByExample(example);
                        if (count <= 0L) {
                            logger.info("来源门店编码:{}没有查询到数据", storeDTO.getSourceStoreCode());
                            errorProcess(bucket, errorList, processDTO, storeDTO, "来源门店没有查询到待复制数据");
                            continue;
                        }
                        // 把目标门店的必备状态全部改成0
                        StoreGoodsContentsExample deleteExample = new StoreGoodsContentsExample();
                        deleteExample.createCriteria().andStoreIdEqualTo(targetStore.get().getOutId()).andNecessaryTagGreaterThan(0);
                        Long delCount = storeGoodsContentsMapper.countByExample(deleteExample);
                        if (delCount > 0L) {
                            List<StoreGoodsContents> oldList = storeGoodsContentsMapper.selectByExample(deleteExample);
                            StoreGoodsContentsExample delExample = new StoreGoodsContentsExample();
                            delExample.createCriteria().andStoreIdEqualTo(targetStore.get().getOutId()).andIdIn(oldList.stream().map(StoreGoodsContents::getId).collect(Collectors.toList()));
                            List<StoreGoodsContents> olds = storeGoodsContentsMapper.selectByExample(delExample).stream().map(v -> buildDelContents(v, userDTO)).collect(Collectors.toList());
                            storeGoodsContentsExtendMapper.batchUpdate(olds, targetStore.get().getOutId());
                            Iterator<StoreGoodsContents> oldIter = oldList.iterator();
                            while (oldIter.hasNext()) {
                                StoreGoodsContents next = oldIter.next();
                                next.setNecessaryTag(0);
                                next.setNecessaryTagName("");
                                next.setMinDisplayQuantity(BigDecimal.ZERO);
                            }
                            if (CollectionUtils.isNotEmpty(oldList)) {
                                List<Long> ids = getStoreGoodsIds(oldList.size());
                                List<Long> taskIds = getMdmTaskDetailIds(oldList.size());
                                Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(oldList.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList()));
                                List<Long> oldTaskIds = getMdmTaskDetailIds(oldList.size());
                                List<MdmTaskDetail> oldTaskDetails = oldList.stream().map(v -> getMdmTaskDetail(targetStore.get(), mdmTask, spuMap, oldTaskIds, v, userDTO)).collect(Collectors.toList());
                                mdmTask.setDetailCount(mdmTask.getDetailCount() + oldTaskDetails.size());
                                mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                                List<List<MdmTaskDetail>> partition = Lists.partition(oldTaskDetails, Constants.INSERT_MAX_SIZE);
                                for (List<MdmTaskDetail> v : partition) {
                                    mdmTaskDetailExtendMapper.batchInsert(v);
                                    try {
                                        factory.getAssemble(1).pushMdm(v);
                                    } catch (Exception e) {
                                        mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                                    }
                                }
                            }
                        }
                        List<Long> ids = getStoreGoodsIds(count.intValue());
                        List<Long> taskIds = getMdmTaskDetailIds(count.intValue());
                        int loopSize = (count.intValue() / Constants.INSERT_MAX_VALUE) + 1;
                        mdmTask.setDetailCount(mdmTask.getDetailCount() + count.intValue());
                        mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                        for (int i = 0; i <= loopSize; i++) {
                            example.setLimit(Constants.INSERT_MAX_VALUE);
                            example.setOffset(Long.valueOf(i * Constants.INSERT_MAX_VALUE));
                            List<StoreGoodsContents> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(example);
                            if (CollectionUtils.isEmpty(storeGoodsInfos)) {
                                break;
                            }
                            // 删除目标门店的对应商品
                            deleteExample.clear();
                            deleteExample.createCriteria().andStoreIdEqualTo(targetStore.get().getOutId()).andGoodsNoIn(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList()));
                            storeGoodsContentsMapper.deleteByExample(deleteExample);

                            List<StoreGoodsContents> newList = storeGoodsInfos.stream().map(v -> {
                                v.setId(ids.remove(0));
                                v.setStoreOrgId(targetStore.get().getId());
                                v.setStoreId(targetStore.get().getOutId());
                                v.setStoreCode(targetStore.get().getSapCode());
                                v.setCreatedBy(userDTO.getUserId());
                                v.setCreatedName(userDTO.getName());
                                v.setUpdatedBy(userDTO.getUserId());
                                v.setUpdatedName(userDTO.getName());
                                v.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                                return v;
                            }).collect(Collectors.toList());
                            storeGoodsContentsExtendMapper.batchInsert(newList);
                            StoreGoodsProcessExample processExample = new StoreGoodsProcessExample();
                            StoreGoodsProcessExample.Criteria processCriteria = processExample.createCriteria();
                            processCriteria.andStoreIdEqualTo(sourceStore.get().getOutId()).andNecessaryTagGreaterThan(0).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
                            processCriteria.andSubCategoryIdNotBetween(12000000L, 12999999L);
                            List<StoreGoodsProcess> processList = storeGoodsProcessMapper.selectByExample(processExample);
                            if (CollectionUtils.isNotEmpty(processList)) {
                                List<Long> processIds = getStoreGoodsProcessIds(processList.size());
                                processList.forEach(v -> {
                                    v.setId(processIds.remove(0));
                                    v.setStoreId(targetStore.get().getOutId());
                                    v.setStoreCode(targetStore.get().getSapCode());
                                    v.setStoreOrgId(targetStore.get().getId());
                                    v.setUpdatedBy(userDTO.getUserId());
                                    v.setUpdatedName(userDTO.getName());
                                });
                                Lists.partition(processList, Constants.INSERT_MAX_VALUE).forEach(v -> storeGoodsProcessExtendMapper.batchInsert(v));
                            }
                            Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(newList.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList()));
                            List<MdmTaskDetail> mdmTaskDetails = storeGoodsInfos.stream().map(v -> {
                                MdmTaskDetail taskDetail = getMdmTaskDetail(targetStore.get(), mdmTask, spuMap, taskIds, v, userDTO);
                                taskDetail.setStoreOrgId(targetStore.get().getId());
                                taskDetail.setStoreId(targetStore.get().getOutId());
                                taskDetail.setStoreCode(targetStore.get().getSapCode());
                                taskDetail.setGoodsNo(v.getGoodsNo());
                                return taskDetail;
                            }).collect(Collectors.toList());
                            mdmTaskDetailExtendMapper.batchInsert(mdmTaskDetails);
                            Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
                                try {
                                    factory.getAssemble(1).pushMdm(v);
                                } catch (Exception e) {
                                    mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                                }
                            });
                        }
                        processDTO.setPassCount(processDTO.getPassCount() + 1);
                        bucket.set(processDTO, 1L, TimeUnit.HOURS);
                    } catch (Exception e) {
                        processDTO.setErrorCount(processDTO.getErrorCount() + 1);
                        if (processDTO.getPassCount() + processDTO.getErrorCount() == processDTO.getProcessCount()) {
                            processDTO.setProcessFinished(true);
                        }
                        bucket.set(processDTO, 1L, TimeUnit.HOURS);
                    }
                }
                mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                processDTO.setProcessFinished(true);
                bucket.set(processDTO, 1L, TimeUnit.HOURS);
            });
            return "更新MDM任务创建成功，任务号：" + mdmTask.getId() + "，更新结果可去更新MDM任务管理模块查看";
        } catch (Exception e) {
            logger.error("门店复制-导入门店失败", e);
            throw new BusinessErrorException(e.getMessage());
        }
    }
    public List<Long> getStoreGoodsIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_STORE_GOODS_INFO, count);
    }

    private void errorProcess(RBucket<CommonProcessDTO<List<ImportStoreDTO>>> bucket, List<ImportStoreDTO> errorList, CommonProcessDTO<List<ImportStoreDTO>> processDTO, ImportStoreDTO storeDTO, String errorMsg) {
        storeDTO.setErrorMsg(errorMsg);
        errorList.add(storeDTO);
        processDTO.setErrorCount(processDTO.getErrorCount() + 1);
        bucket.set(processDTO, 1L, TimeUnit.HOURS);
    }

    @Override
    public CommonProcessDTO<List<ImportStoreDTO>> getImportProcess(TokenUserDTO userDTO) {
        RBucket<CommonProcessDTO<List<ImportStoreDTO>>> bucket = redissonClient.getBucket(BATCH_IMPORT_COPY_STORE + userDTO.getUserId());
        if (bucket.isExists()) {
            CommonProcessDTO<List<ImportStoreDTO>> commonProcessDTO = bucket.get();
            if (commonProcessDTO.getProcessFinished()) {
                bucket.delete();
            }
            return commonProcessDTO;
        } else {
            CommonProcessDTO<List<ImportStoreDTO>> commonProcessDTO = new CommonProcessDTO<>();
            commonProcessDTO.setProcessFinished(true);
            commonProcessDTO.setT(new ArrayList<>());
            return commonProcessDTO;
        }
    }

    @Override
    public PageResult<StoreGoodsContentDTO> getSourceStoreInfos(StoreGoodsQueryParam param, TokenUserDTO userDTO) {
        if (Objects.isNull(param.getStoreId())) {
            throw new AmisBadRequestException("请选择门店进行复制");
        }
        StoreGoodsContentsExample example = new StoreGoodsContentsExample();
        StoreGoodsContentsExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(param.getStoreId()).andNecessaryTagGreaterThan(0).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
        if (StringUtils.isNotBlank(param.getGoodsNo())) {
            criteria.andGoodsNoEqualTo(param.getGoodsNo().trim());
        }
        // 排除掉配方饮片类商品。大类=中药参茸 and 中类=配方中药。
        criteria.andSubCategoryIdNotBetween(12000000L, 12009999L);
        long count = storeGoodsContentsMapper.countByExample(example);
        if (count <= 0L){
            return new PageResult<>(count, new ArrayList<>());
        }
        example.setOffset(Long.valueOf(param.getPage() - 1) * param.getPerPage());
        example.setLimit(param.getPerPage());
        example.setOrderByClause(" necessary_tag asc, goods_no asc");
        List<StoreGoodsContents> storeGoodsInfo = storeGoodsContentsMapper.selectByExample(example);
        List<String> goodsNos = storeGoodsInfo.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList());
        Map<String, SpuListVo> spuVOMap = new HashMap<>();
        Lists.partition(goodsNos, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
            spuVOMap.putAll(searchService.getSpuVOMap(storeGoodsInfo.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList())));
        });
        String redisKey = COPY_STORE_INFO + userDTO.getUserId().toString() + "-" + param.getStoreId().toString();
        RBucket<Set<String>> bucket = redissonClient.getBucket(redisKey);
        Set<String> goodsNoSet = bucket.get();
        return new PageResult<>(count, storeGoodsInfo.stream().map(v -> {
            StoreGoodsContentDTO dto = new StoreGoodsContentDTO();
            BeanUtils.copyProperties(v, dto);
            SpuListVo spuListVo = spuVOMap.get(v.getGoodsNo());
            if (Objects.nonNull(spuListVo)) {
                dto.setGoodsName(spuListVo.getName());
                dto.setSpecifications(spuListVo.getJhiSpecification());
                dto.setManufacturer(spuListVo.getFactoryid());
            }
            Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(dto.getStoreId());
            if (optional.isPresent()) {
                OrgInfoBaseCache orgInfoBaseCache = optional.get();
                dto.setPlatformName(orgInfoBaseCache.getPlatformShortName());
                dto.setCompanyName(orgInfoBaseCache.getBusinessShortName());
            }
            return dto;
        }).collect(Collectors.toList()));

    }

    @Override
    public String copyStoreGoods(TokenUserDTO userDTO, NecessaryCopyParam param) {
        List<String> cacheKeys = new ArrayList<>();
        try {
            String redisKey = COPY_STORE_INFO + userDTO.getUserId().toString() + "-" + param.getSourceStoreId().toString();
            OrgInfoBaseCache sourceStore = CacheVar.getStoreByStoreId(param.getSourceStoreId()).orElseThrow(() -> new AmisBadRequestException("请选择来源门店"));
            OrgInfoBaseCache targetStore = CacheVar.getStoreByStoreId(param.getTargetStoreId()).orElseThrow(() -> new AmisBadRequestException("请选择目标门店"));
            StoreGoodsContentsExample example = new StoreGoodsContentsExample();
            StoreGoodsContentsExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(param.getSourceStoreId()).andNecessaryTagGreaterThan(0).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
            // 排除掉配方饮片类商品。大类=中药参茸 and 中类=配方中药。
            criteria.andSubCategoryIdNotBetween(12000000L, 12999999L);
            RBucket<Set<String>> bucket = redissonClient.getBucket(redisKey);
            Set<String> goodsNoSet = bucket.get();
            logger.info("redisKey:{} goodsNoSet:{}", redisKey, JSON.toJSONString(goodsNoSet));
            if (CollectionUtils.isNotEmpty(goodsNoSet)) {
                criteria.andGoodsNoNotIn(Lists.newArrayList(goodsNoSet));
            }
            List<StoreGoodsContents> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(storeGoodsInfos)) {
                throw new AmisBadRequestException("没有需要复制的数据");
            }
            StoreGoodsProcessExample processExample = new StoreGoodsProcessExample();
            StoreGoodsProcessExample.Criteria processCriteria = processExample.createCriteria();
            processCriteria.andStoreIdEqualTo(param.getSourceStoreId()).andNecessaryTagGreaterThan(0).andGoodsNoIn(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).collect(Collectors.toList())).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
            processCriteria.andSubCategoryIdNotBetween(12000000L, 12999999L);
            List<StoreGoodsProcess> processList = storeGoodsProcessMapper.selectByExample(processExample);
            StoreGoodsProcessExample delProcessExample = new StoreGoodsProcessExample();
            StoreGoodsProcessExample.Criteria delProcessCriteria = delProcessExample.createCriteria();
            delProcessCriteria.andStoreIdEqualTo(param.getTargetStoreId()).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
            storeGoodsProcessMapper.deleteByExample(delProcessExample);
            if (CollectionUtils.isNotEmpty(processList)) {
                List<Long> processIds = getStoreGoodsProcessIds(processList.size());
                processList.forEach(v -> {
                    v.setId(processIds.remove(0));
                    v.setStoreId(targetStore.getOutId());
                    v.setStoreCode(targetStore.getSapCode());
                    v.setStoreOrgId(targetStore.getId());
                    v.setUpdatedBy(userDTO.getUserId());
                    v.setUpdatedName(userDTO.getName());
                });
                Lists.partition(processList, Constants.INSERT_MAX_VALUE).forEach(v -> storeGoodsProcessExtendMapper.batchInsert(v));
            }
            List<Long> ids = getStoreGoodsIds(storeGoodsInfos.size());
            List<Long> taskIds = getMdmTaskDetailIds(storeGoodsInfos.size());
            List<String> goodsNoList = storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList());
            Iterator<StoreGoodsContents> iterator = storeGoodsInfos.iterator();
            while (iterator.hasNext()) {
                StoreGoodsContents storeGoodsInfo = iterator.next();
                storeGoodsInfo.setId(ids.remove(0));
                storeGoodsInfo.setStoreOrgId(targetStore.getId());
                storeGoodsInfo.setStoreId(targetStore.getOutId());
                storeGoodsInfo.setStoreCode(targetStore.getSapCode());
                storeGoodsInfo.setCreatedBy(userDTO.getUserId());
                storeGoodsInfo.setCreatedName(userDTO.getName());
                storeGoodsInfo.setUpdatedBy(userDTO.getUserId());
                storeGoodsInfo.setUpdatedName(userDTO.getName());
                storeGoodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
            }
            if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
                MdmTask mdmTask = new MdmTask();
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), mdmTask);
                mdmTask.setTaskSource(MdmTaskSourceEnum.STORE_COPY.getCode());
                mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                mdmTask.setDetailCount(storeGoodsInfos.size());
                mdmTaskMapper.insertSelective(mdmTask);
                asyncTaskExecutor.execute(() -> {
                    StoreGoodsContentsExample deleteExample = new StoreGoodsContentsExample();
                    // 先查出目标门店必备的且可以下发的
                    deleteExample.createCriteria().andStoreIdEqualTo(param.getTargetStoreId()).andNecessaryTagGreaterThan(0);
                    List<StoreGoodsContents> oldList = storeGoodsContentsMapper.selectByExample(deleteExample);
                    if (CollectionUtils.isNotEmpty(oldList)) {
                        StoreGoodsContentsExample delExample = new StoreGoodsContentsExample();
                        delExample.createCriteria().andStoreIdEqualTo(param.getTargetStoreId()).andIdIn(oldList.stream().map(StoreGoodsContents::getId).collect(Collectors.toList()));
                        List<StoreGoodsContents> olds = storeGoodsContentsMapper.selectByExample(delExample).stream().map(v -> buildDelContents(v, userDTO)).collect(Collectors.toList());
                        storeGoodsContentsExtendMapper.batchUpdate(olds, param.getTargetStoreId());
                        Iterator<StoreGoodsContents> oldIter = oldList.iterator();
                        while (oldIter.hasNext()) {
                            StoreGoodsContents next = oldIter.next();
                            next.setNecessaryTag(0);
                            next.setNecessaryTagName("");
                            if (ManageStatusEnum.MANAGE_NECESARY.getCode().equals(next.getManageStatus())) {
                                next.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                                next.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                            }
                            next.setMinDisplayQuantity(BigDecimal.ZERO);
                        }
                    }
                    deleteExample.clear();
                    deleteExample.createCriteria().andStoreIdEqualTo(param.getTargetStoreId()).andGoodsNoIn(storeGoodsInfos.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList()));
                    storeGoodsContentsMapper.deleteByExample(deleteExample);
                    Lists.partition(storeGoodsInfos, Constants.INSERT_MAX_SIZE).forEach(v -> storeGoodsContentsExtendMapper.batchInsert(v));
                    if (CollectionUtils.isNotEmpty(oldList)) {
                        Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(oldList.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList()));
                        List<Long> oldTaskIds = getMdmTaskDetailIds(oldList.size());
                        List<MdmTaskDetail> oldTaskDetails = oldList.stream().map(v -> getMdmTaskDetail(targetStore, mdmTask, spuMap, oldTaskIds, v, userDTO)).collect(Collectors.toList());
                        mdmTask.setDetailCount(mdmTask.getDetailCount() + oldTaskDetails.size());
                        mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                        List<List<MdmTaskDetail>> partition = Lists.partition(oldTaskDetails, Constants.INSERT_MAX_SIZE);
                        for (List<MdmTaskDetail> v : partition) {
                            mdmTaskDetailExtendMapper.batchInsert(v);
                            try {
                                factory.getAssemble(1).pushMdm(v);
                            } catch (Exception e) {
                                mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                            }
                        }
                    }
                    Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(goodsNoList);
                    List<MdmTaskDetail> taskDetails = storeGoodsInfos.stream().map(v -> {
                        MdmTaskDetail taskDetail = getMdmTaskDetail(targetStore, mdmTask, spuMap, taskIds, v, userDTO);
                        taskDetail.setStoreOrgId(targetStore.getId());
                        taskDetail.setStoreId(targetStore.getOutId());
                        taskDetail.setStoreCode(targetStore.getSapCode());
                        taskDetail.setGoodsNo(v.getGoodsNo());
                        return taskDetail;
                    }).collect(Collectors.toList());
                    mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                    mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                    List<List<MdmTaskDetail>> partition = Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE);
                    for (List<MdmTaskDetail> v : partition) {
                        mdmTaskDetailExtendMapper.batchInsert(v);
                        try {
                            factory.getAssemble(1).pushMdm(v);
                        } catch (Exception e) {
                            mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                        }
                    }
                    bucket.delete();

                });
                return "更新MDM任务创建成功，任务号：" + mdmTask.getId() + "，更新结果可去更新MDM任务管理模块查看";
            } else {
                return "复制的门店没有需要下发的数据";
            }
        } catch (Exception e) {
            logger.error("复制一店一目信息失败", e);
            throw new AmisBadRequestException(e.getMessage());
        } finally {
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(cacheKeys)) {
                cacheKeys.forEach(v -> {
                    RBucket rBucket = redissonClient.getBucket(v);
                    rBucket.delete();
                });
            }
        }

    }

    private List<Long> getStoreGoodsProcessIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_STORE_GOODS_PROCESS, count);
    }

    @Override
    public void updateCopyStoreGoodsStatus(Long sourceStoreId, String goodsNo, Byte effectStatus, TokenUserDTO userDTO) {
        if (null == sourceStoreId || StringUtils.isBlank(goodsNo) || null == effectStatus) {
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        String redisKey = COPY_STORE_INFO + userDTO.getUserId().toString() + "-" + sourceStoreId.toString();
        RBucket<Set<String>> bucket = redissonClient.getBucket(redisKey);
        Set<String> goodsSet = bucket.get();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(goodsSet)) {
            goodsSet = new HashSet<>();
        }
        if (StoreGoodsEffectStatusEnum.NO.getCode() == effectStatus.byteValue()) {
            goodsSet.add(goodsNo);
        } else {
            goodsSet.remove(goodsNo);
        }
        bucket.delete();
        logger.info("redisKey:{} goodsNoSet:{}", redisKey, JSON.toJSONString(goodsSet));
        bucket.set(goodsSet, 1L, TimeUnit.DAYS);

    }

    @Override
    public NecessaryLevelConfig getConfigLevelByNecessaryTag(Integer necessaryTag) {
        if (null == necessaryTag) {
            throw new BusinessErrorException("请选择必备标签");
        }
        NecessaryLevelConfigExample example = new NecessaryLevelConfigExample();
        example.createCriteria().andNecessaryTagEqualTo("" +necessaryTag);
        return necessaryLevelConfigMapper.selectByExample(example).stream().findAny().orElseThrow(() -> new BusinessErrorException("没有配置必备层级"));
    }

    @Override
    public Boolean checkHasEditPerm(TokenUserDTO userDTO) {
        if (CollectionUtils.isEmpty(userDTO.getRoles())) {
            return false;
        }
        List<NecessaryLevelRoleConfig> roleConfigs = necessaryLevelRoleConfigMapper.selectByExample(null);
        if (CollectionUtils.isEmpty(roleConfigs)) {
            return false;
        }
        String roles = roleConfigs.stream().map(NecessaryLevelRoleConfig::getRoles).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        if (StringUtils.isBlank(roles)) {
            return false;
        }
        return Arrays.stream(StringUtils.split(roles, ",")).filter(v -> userDTO.getRoles().contains(v)).findAny().isPresent();
    }

    private void saveMdmTask(MdmTask mdmTask,TokenUserDTO userDTO, List<StoreGoodsContents> storeGoodsInfos,OrgInfoBaseCache orgInfoBaseCache) {
        mdmTask.setTaskSource(MdmTaskSourceEnum.SINGLE_STORE_GOODS_REMOVE.getCode());
        mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
        mdmTask.setDetailCount(storeGoodsInfos.size());
        mdmTask.setStatus(Constants.NORMAL_STATUS);
        mdmTask.setRemarks(orgInfoBaseCache.getOutId().toString());
        mdmTask.setUpdatedName(userDTO.getName());
        mdmTask.setUpdatedBy(userDTO.getUserId());
        mdmTask.setGmtUpdate(new Date());
        mdmTask.setCreatedBy(userDTO.getUserId());
        mdmTask.setCreatedName(userDTO.getName());
        mdmTask.setGmtCreate(new Date());
        mdmTaskMapper.insertSelective(mdmTask);
    }

    private void buildMdmTaskDetail(TokenUserDTO userDTO, Map<String, SpuListVo> spuMap, MdmTask mdmTask, List<Long> mdmTaskDetailIds, int i, StoreGoodsContents storeGoodsInfo, MdmTaskDetail mdmTaskDetail,OrgInfoBaseCache orgInfoBaseCache) {
        mdmTaskDetail.setId(mdmTaskDetailIds.get(i));

        mdmTaskDetail.setTaskId(mdmTask.getId());
        mdmTaskDetail.setGoodsNo(storeGoodsInfo.getGoodsNo());
        SpuListVo spuListVo = spuMap.get(storeGoodsInfo.getGoodsNo());
        if (Objects.nonNull(spuListVo)) {
            mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
            mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
            mdmTaskDetail.setDescription(spuListVo.getDescription());
            mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
            mdmTaskDetail.setBarCode(spuListVo.getBarCode());
            mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
            mdmTaskDetail.setGoodsName(spuListVo.getName());
            mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
            mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
        }
        mdmTaskDetail.setUpdatedBy(userDTO.getUserId());
        mdmTaskDetail.setUpdatedName(userDTO.getUserName());
        mdmTaskDetail.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
        mdmTaskDetail.setStoreId(storeGoodsInfo.getStoreId());
        mdmTaskDetail.setStoreCode(storeGoodsInfo.getStoreCode());
        Optional<OrgInfoBaseCache> storeByStoreId = CacheVar.getStoreByStoreId(storeGoodsInfo.getStoreId());
        storeByStoreId.ifPresent(infoBaseCache -> mdmTaskDetail.setStoreName(infoBaseCache.getShortName()));
        if (Objects.isNull(mdmTaskDetail.getStoreName())){
            mdmTaskDetail.setStoreName(orgInfoBaseCache.getShortName());
        }
        mdmTaskDetail.setNecessaryTag(0);
        mdmTaskDetail.setNecessaryTagName("");
        mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
        mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
        mdmTaskDetail.setGmtUpdate(new Date());
    }

    private List<Long> getMdmTaskDetailIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_MDM_TASK_DETAIL, count);
    }
    public void dealImportData(List<NecessaryContentsImportData> excelList, ImportResult result, String key, RBucket<ImportResult> rBucket, TokenUserDTO userDTO) {
        try {
            List<NecessaryContentsImportData> errorData = new ArrayList<>();
            List<NecessaryContentsImportData> repeatDataList = new ArrayList<>();
            if (CollectionUtils.isEmpty(excelList)) {
                throw new AmisBadRequestException("导入文件为空");
            }
            CommonEnumsExample example = new CommonEnumsExample();
            example.createCriteria().andPropertyCodeEqualTo("necessaryTag").andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
            Map<String, CommonEnums> enumsMap = commonEnumsMapper.selectByExample(example).stream().collect(Collectors.toMap(CommonEnums::getEnumName, Function.identity(), (k1, k2) -> k1));
            List<String> storeTypeNameList = excelList.stream().map(NecessaryContentsImportData::getStoreType).filter(StringUtils::isNotBlank).map(StringUtils::trim).distinct().collect(Collectors.toList());
            Map<String, String> storeTypeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(storeTypeNameList)) {
                // 根据店型名称查询店型
                CommonEnumsExample enumsExample = new CommonEnumsExample();
//                enumsExample.createCriteria().andPropertyCodeIn(Lists.newArrayList("JtStoreGroup","StoreGroup")).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
                enumsExample.createCriteria().andEnumNameIn(storeTypeNameList).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
                storeTypeMap.putAll(commonEnumsMapper.selectByExample(null).stream().collect(Collectors.toMap(CommonEnums::getEnumName, CommonEnums::getEnumValue, (k1,k2) -> k1)));
            }
            example.clear();
            example.createCriteria().andPropertyCodeEqualTo("necessaryReasonList").andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
            Map<String, String> chooseReasonMap = commonEnumsMapper.selectByExample(example).stream().collect(Collectors.toMap(CommonEnums::getEnumName, CommonEnums::getEnumValue, (k1, k2) -> k1));

            // 先过滤没有必备标签的
            Iterator<NecessaryContentsImportData> iterator = excelList.iterator();
            while (iterator.hasNext()) {
                NecessaryContentsImportData next = iterator.next();
                if (StringUtils.isBlank(next.getNecessaryTagName())) {
                    next.setErrorReason("必备标签不能为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (!enumsMap.containsKey(next.getNecessaryTagName())) {
                    next.setErrorReason("必备标签不存在");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getGoodsNo())) {
                    next.setErrorReason("商品不能为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isNotBlank(next.getStoreType()) && !storeTypeMap.containsKey(next.getStoreType())) {
                    next.setErrorReason("店型不存在");
                    errorData.add(next);
                    iterator.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(excelList)) {
                MdmTask task = new MdmTask();
                task.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                task.setDetailCount(0);
                mdmTaskMapper.insertSelective(task);
                for (NecessaryContentsImportData data : excelList) {
                    NecessaryAddParam addParam = new NecessaryAddParam();
                    BeanUtils.copyProperties(data, addParam);
                    addParam.setNecessaryTag(enumsMap.get(data.getNecessaryTagName()).getEnumValue());
                    if (StringUtils.isNotBlank(data.getStoreCode())) {
                        Optional<OrgInfoBaseCache> optional = CacheVar.getStoreBySapCode(data.getStoreCode());
                        if (optional.isPresent()) {
                            addParam.setStoreOrgIds(Lists.newArrayList(optional.get().getId()));
                        } else {
                            data.setErrorReason("门店编码不存在");
                            errorData.add(data);
                            continue;
                        }
                    }
                    if (StringUtils.isNotBlank(data.getArea())) {
                        addParam.setAreas(Lists.newArrayList(data.getArea()));
                    }
                    if (StringUtils.isNotBlank(data.getStoreType())) {
                        addParam.setStoreTypes(Lists.newArrayList(storeTypeMap.get(data.getStoreType())));
                    }
                    addParam.setGoodsNos(Lists.newArrayList(data.getGoodsNo()));
                    addParam.setChooseReason(chooseReasonMap.get(data.getChooseReason()));
                    try {
                        String msg = editNecessaryGoods(userDTO, addParam, task);
                        if (StringUtils.isNotBlank(msg) && !msg.startsWith("更新MDM任务创建成功")) {
                            data.setErrorReason(msg);
                            errorData.add(data);
                        }
                    } catch (AmisBadRequestException e) {
                        data.setErrorReason(e.getMessage());
                        errorData.add(data);
                    }
                }
            }
            String fileName = "必备目录导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, NecessaryContentsImportData.class).sheet("sheet1").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("店型级必备明细错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        } catch (Exception e) {
            logger.error("数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }
    public void dealDelImportData(List<NecessaryContentsImportDelData> excelList, ImportResult result, String key, RBucket<ImportResult> rBucket, TokenUserDTO userDTO) {
        try {
            List<NecessaryContentsImportDelData> errorData = new ArrayList<>();
            if (CollectionUtils.isEmpty(excelList)) {
                throw new AmisBadRequestException("导入文件为空");
            }
            Iterator<NecessaryContentsImportDelData> iterator = excelList.iterator();
            while (iterator.hasNext()) {
                NecessaryContentsImportDelData next = iterator.next();
                if (null == next.getId()) {
                    next.setErrorReason("ID不能为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getInvalidReason())) {
                    next.setErrorReason("作废原因不能为空");
                    errorData.add(next);
                    iterator.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(excelList)) {
                MdmTask task = new MdmTask();
                task.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                task.setDetailCount(0);
                mdmTaskMapper.insertSelective(task);
                excelList.stream().collect(Collectors.groupingBy(NecessaryContentsImportDelData::getInvalidReason)).forEach((k,v) -> {
                    try {
                        modify(userDTO, v.stream().map(NecessaryContentsImportDelData::getId).distinct().collect(Collectors.toList()), Constants.DEL_STATUS, k, task, null);
                    } catch (AmisBadRequestException e) {
                        v.forEach(err -> err.setErrorReason(e.getMessage()));
                        errorData.addAll(v);
                    }
                });
            }
            String fileName = "必备目录删除_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, NecessaryContentsImportDelData.class).sheet("sheet1").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("店型级必备明细错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        } catch (Exception e) {
            logger.error("数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }
    private String uplodaErrorData(String fileName, String filePath) {
        File errorFile = new File(filePath);
        String key = fileName;
        String presignatureUrl = "";
        try {
            String url = cosService.multipartUploadFile(key, errorFile);
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, +7);
            presignatureUrl = cosService.getPresignatureUrl(key, calendar.getTime());
        } catch (Exception e) {
            logger.error("分片上传文件失败", e);
        }
        return presignatureUrl;
    }
    private void delTempFile(String filePath) {
        Path path = Paths.get(filePath);
        try {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
                    Files.delete(file);
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException ioException) {
            logger.error("删除文件异常", ioException);
            ioException.printStackTrace();
        }
    }
    private void toRedis(RBucket<ImportResult> rBucket, int size,
                         ImportResult result, String key, String fileFileUrl) {
        if (size > 0) {
            result.setCode("1");
            result.setMessage("上传数据已处理完成，存在" + size + "行无效数据，请下载查看。");
            result.setResult(key);
            result.setFailFileUrl(fileFileUrl);
            rBucket.set(result, 12, TimeUnit.HOURS);
        } else {
            result.setCode("0");
            result.setMessage("上传数据全部处理成功。");
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }

    private void checkFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }
        //获取文件名+后缀
        String filename = file.getOriginalFilename();
        if (filename != null) {
            //获取其后缀
            String extension = filename.substring(filename.lastIndexOf(".") + 1);
            if (!(extension.equals("xls") || extension.equals("xlsx"))) {
                //此处为自定义异常捕获,可使用其他方式
                throw new BusinessErrorException("文件格式有误,请检查上传文件格式!!");
            }
        }
    }

    private ImportResult getOperationPermissions(String key) {
        RBucket<ImportResult> rBucket1 = redissonClient.getBucket(key);
        if (rBucket1.isExists()) {
            ImportResult importResult = rBucket1.get();
            if (Objects.nonNull(importResult) &&importResult.getCode().equals("9999")){
                return importResult;
            }
        }
        return null;
    }

    private NecessaryContentsV2ServiceImpl assembleStoreTypeProp(NecessaryAddParam param, List<CommonEnums> commonEnums, List<NecessaryContents> contents) {
        if (CollectionUtils.isEmpty(param.getStoreTypes())) {
            return this;
        }
        List<NecessaryContents> result = new ArrayList<>();
        commonEnums.stream().filter(v -> param.getStoreTypes().contains(v.getEnumValue())).forEach(v -> {
            contents.forEach(c -> {
                NecessaryContents copy = new NecessaryContents();
                BeanUtils.copyProperties(c, copy);
                copy.setStoreType(v.getEnumValue());
                copy.setStoreTypeName(v.getEnumName());
                copy.setStoreTypeProp(v.getPropertyCode());
                copy.setStoreTypePropName(v.getPropertyDesc());
                result.add(copy);
            });
        });
        contents.clear();
        contents.addAll(result);
        return this;
    }

    private NecessaryContentsV2ServiceImpl checkAssembleArea(NecessaryAddParam param, NecessaryConfigAreaEnum configAreaEnum, NecessaryContentsExample.Criteria criteria, List<NecessaryContents> contents) {
        switch (configAreaEnum) {
            case COUNTRY:
                break;
            case PROVINCE:
                if (StringUtils.isBlank(param.getProvince())) {
                    throw new AmisBadRequestException("请选择省份");
                }
                contents.forEach(v -> v.setProvince(param.getProvince()));
                if (null != criteria) {
                    criteria.andProvinceEqualTo(param.getProvince());
                }
                break;
            case CITY:
                if (StringUtils.isBlank(param.getProvince())) {
                    throw new AmisBadRequestException("请选择省份");
                }
                if (StringUtils.isBlank(param.getCity())) {
                    throw new AmisBadRequestException("请选择城市");
                }
                contents.forEach(v -> {
                    v.setProvince(param.getProvince());
                    v.setCity(param.getCity());
                });
                if (null != criteria) {
                    criteria.andProvinceEqualTo(param.getProvince()).andCityEqualTo(param.getCity());
                }
                break;
            case AREA:
                if (StringUtils.isBlank(param.getProvince())) {
                    throw new AmisBadRequestException("请选择省份");
                }
                if (StringUtils.isBlank(param.getCity())) {
                    throw new AmisBadRequestException("请选择城市");
                }
                if (CollectionUtils.isEmpty(param.getAreas())) {
                    throw new AmisBadRequestException("请选择区县");
                }
                if (null != criteria) {
                    criteria.andProvinceEqualTo(param.getProvince()).andCityEqualTo(param.getCity()).andAreaIn(param.getAreas());
                }
                List<NecessaryContents> result = new ArrayList<>();
                param.getAreas().forEach(v -> {
                    contents.forEach(c -> {
                        NecessaryContents copy = new NecessaryContents();
                        BeanUtils.copyProperties(c, copy);
                        copy.setProvince(param.getProvince());
                        copy.setCity(param.getCity());
                        copy.setCity(v);
                        result.add(copy);
                    });
                });
                contents.clear();
                contents.addAll(result);
                break;
           default:throw new AmisBadRequestException("未知的区域层级");
        }
        return this;
    }

    private NecessaryContentsV2ServiceImpl checkAssembleOrgType(NecessaryAddParam param, OrgTypeEnum orgTypeEnum, NecessaryContentsExample.Criteria criteria, List<NecessaryContents> contents) {
        switch (orgTypeEnum) {
            case GROUP:
                if (null != criteria) {
                    criteria.andOrgIdEqualTo(zbOrgId);
                }
                contents.forEach(v -> {
                    v.setOrgId(zbOrgId);
                    v.setOrgName(zbOrgName);
                    v.setOrgType(OrgTypeEnum.GROUP.getCode());
                });
                break;
            case PLATFORM:
                if (null == param.getPlatformOrgId()) {
                    throw new AmisBadRequestException("请选择平台");
                }
                OrgInfoBaseCache platform = CacheVar.getPlatformByOrgId(param.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息"));
                contents.forEach(v -> {
                    v.setOrgId(platform.getId());
                    v.setOrgName(platform.getShortName());
                    v.setPlatformOrgId(platform.getId());
                    v.setPlatformName(platform.getShortName());
                    v.setOrgType(OrgTypeEnum.PLATFORM.getCode());

                });
                if (null != criteria) {
                    criteria.andOrgIdEqualTo(param.getPlatformOrgId());
                }
                break;
            case BUSINESS:
                if (null == param.getPlatformOrgId()) {
                    throw new AmisBadRequestException("请选择平台");
                }
                if (null == param.getCompanyOrgId()) {
                    throw new AmisBadRequestException("请选择项目公司");
                }
                OrgInfoBaseCache company = CacheVar.getBusinessByOrgId(param.getCompanyOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到项目公司信息"));
                contents.forEach(v -> {
                    v.setOrgId(company.getId());
                    v.setOrgName(company.getShortName());
                    v.setOrgType(OrgTypeEnum.BUSINESS.getCode());
                    v.setPlatformOrgId(company.getPlatformOrgId());
                    v.setPlatformName(company.getPlatformShortName());
                    v.setCompanyOrgId(company.getId());
                    v.setCompanyCode(company.getSapCode());
                    v.setCompanyName(company.getShortName());
                    v.setBusinessId(company.getOutId());
                });

                if (null != criteria) {
                    criteria.andPlatformOrgIdEqualTo(param.getPlatformOrgId()).andOrgIdEqualTo(param.getCompanyOrgId());
                }
                break;
            case STORE:
                if (null == param.getPlatformOrgId()) {
                    throw new AmisBadRequestException("请选择平台");
                }
                if (null == param.getCompanyOrgId()) {
                    throw new AmisBadRequestException("请选择项目公司");
                }
                if (CollectionUtils.isEmpty(param.getStoreOrgIds())) {
                    throw new AmisBadRequestException("请选择门店");
                }
                List<NecessaryContents> result = new ArrayList<>();
                param.getStoreOrgIds().forEach(v -> {
                    OrgInfoBaseCache store = CacheVar.getStoreByOrgId(v).orElseThrow(() -> new AmisBadRequestException("没有获取到门店ID:" + v + "的信息"));
                    contents.forEach(c -> {
                        NecessaryContents copy = new NecessaryContents();
                        BeanUtils.copyProperties(c, copy);
                        copy.setOrgId(store.getId());
                        copy.setOrgName(store.getShortName());
                        copy.setOrgType(OrgTypeEnum.STORE.getCode());
                        copy.setPlatformOrgId(store.getPlatformOrgId());
                        copy.setPlatformName(store.getPlatformShortName());
                        copy.setCompanyOrgId(store.getBusinessOrgId());
                        copy.setCompanyCode(store.getBusinessSapCode());
                        copy.setCompanyName(store.getBusinessShortName());
                        copy.setBusinessId(store.getBusinessId());
                        copy.setStoreOrgId(store.getId());
                        copy.setStoreCode(store.getSapCode());
                        copy.setStoreId(store.getOutId());
                        copy.setStoreName(store.getShortName());
                        result.add(copy);
                    });
                });
                contents.clear();
                contents.addAll(result);

                if (null != criteria) {
                    criteria.andPlatformOrgIdEqualTo(param.getPlatformOrgId()).andCompanyOrgIdEqualTo(param.getCompanyOrgId()).andOrgIdIn(param.getStoreOrgIds());
                }
                break;
            default:throw new AmisBadRequestException("未知的组织层级");
        }
        return this;
    }

    private NecessaryContentsV2ServiceImpl assembleGoodsInfo(List<String> goods, List<String> existsGoods, List<NecessaryContents> contents, Map<String, String> goodsBusiscope) {
        Map<String, SpuListVo> spuMap = new HashMap<>();
        Map<Long, CommonCategoryDTO> categoryMap = new HashMap<>();
        Map<String, Map<String, String>> componentMap = new HashMap<>();
        goods = goods.stream().filter(v -> !existsGoods.contains(v)).collect(Collectors.toList());
        Lists.partition(goods, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
            spuMap.putAll(searchService.getSpuVOMap(v));
            categoryMap.putAll(getCategoryBySubIds(spuMap.values().stream().map(goo -> Long.valueOf(goo.getCategoryId())).distinct().collect(Collectors.toList())));
            componentMap.putAll(forestService.querySpuProperties(Lists.newArrayList("component", "busiscope"), v, null));
        });
        List<NecessaryContents> result = new ArrayList<>();
        spuMap.forEach((k,v) -> {
            CommonGoodsDTO commonGoodsDTO = searchService.getCommonGoods(v);
            contents.forEach(c -> {
                NecessaryContents copy = new NecessaryContents();
                BeanUtils.copyProperties(c, copy);
                BeanUtils.copyProperties(commonGoodsDTO, copy);
                // 成分 标品属性 component
                Map<String, String> component = componentMap.get(k);
                if (MapUtils.isNotEmpty(component)) {
                    c.setComposition(component.get("component"));
                    goodsBusiscope.put(v.getGoodsNo(), component.get("busiscope"));
                }
                CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(v.getCategoryId()));
                if (Objects.isNull(commonCategoryDTO)) {
                    logger.info("商品:{}没有查询到四级类目信息", k);
                    throw new AmisBadRequestException("商品:" + k + "没有查询到四级类目信息");
                }
                BeanUtils.copyProperties(commonCategoryDTO, copy);
                result.add(copy);
            });
        });
        contents.clear();
        contents.addAll(result);
        return this;
    }

    /**
     * 根据子类id获取四级类目信息
     * @param subCategoryIds
     * @return
     */
    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1, k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        return resultMap;
    }

    private NecessaryContentsExample.Criteria genQueryCondition(NecessaryQueryParam param, NecessaryContentsExample example) {
        NecessaryContentsExample.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isNotEmpty(param.getNecessaryTags())) {
            criteria.andNecessaryTagIn(param.getNecessaryTags());
        }
        if (StringUtils.isNotBlank(param.getGoodsNos())) {
            criteria.andGoodsNoIn(Arrays.stream(StringUtils.split(param.getGoodsNos().trim(), ",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList()));
        }
        if (null != param.getStatus()) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (CollectionUtils.isNotEmpty(param.getProvinces())) {
            criteria.andProvinceIn(param.getProvinces());
        }
        if (CollectionUtils.isNotEmpty(param.getCitys())) {
            criteria.andCityIn(param.getCitys());
        }
        if (null != param.getPlatformOrgId()) {
            criteria.andPlatformOrgIdEqualTo(param.getPlatformOrgId());
        }
        if (CollectionUtils.isNotEmpty(param.getCompanyOrgIds())) {
            criteria.andCompanyOrgIdIn(param.getCompanyOrgIds());
        }
        if (null != param.getCategoryId()) {
            criteria.andCategoryIdEqualTo(param.getCategoryId());
        }
        if (null != param.getMiddleCategoryId()) {
            criteria.andMiddleCategoryIdEqualTo(param.getMiddleCategoryId());
        }
        if (null != param.getSmallCategoryId()) {
            criteria.andSmallCategoryIdEqualTo(param.getSmallCategoryId());
        }
        if (null != param.getSubCategoryId()) {
            criteria.andSubCategoryIdEqualTo(param.getSubCategoryId());
        }
        if (StringUtils.isNotBlank(param.getComposition())) {
            criteria.andCompositionEqualTo(param.getComposition().trim());
        }
        if (StringUtils.isNotBlank(param.getGoodsName())) {
            criteria.andGoodsNameLike(Constants.QUERY_LIKE + param.getGoodsName() + Constants.QUERY_LIKE);
        }
        List<String> storeTypes = new ArrayList<>();
        storeTypes.addAll(null == param.getJtStoreTypes() ? Lists.newArrayList() : param.getJtStoreTypes());
        storeTypes.addAll(null == param.getStoreTypes() ? Lists.newArrayList() : param.getStoreTypes());
        if (CollectionUtils.isNotEmpty(storeTypes)) {
            criteria.andStoreTypeIn(storeTypes);
        }
        return criteria;
    }

    private Map<Long, List<Long>> getUserDataMap(TokenUserDTO userDTO){
        List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), zbOrgId, Lists.newArrayList(OrgTypeEnum.PLATFORM.getCode(), OrgTypeEnum.BUSINESS.getCode()));
        if (CollectionUtils.isEmpty(orgTreeSimpleDTOS) || CollectionUtils.isEmpty(orgTreeSimpleDTOS.get(0).getChildren())) {
            return new HashMap<>();
        }
        return orgTreeSimpleDTOS.get(0).getChildren().stream().collect(Collectors.toMap(v -> v.getId(), v -> v.getChildren().stream().map(OrgTreeSimpleDTO::getId).collect(Collectors.toList()), (k1, k2) -> k1));
    }

    @Override
    public List<Long> selectMdmStoreIdFilterSelector(Long platformOrgId, TokenUserDTO userDTO) {
        return bundlTaskService.selectMdmStoreIdFilterSelector(platformOrgId, userDTO.getUserId(), null);
    }

    @Override
    public void importUpdateManageStauts(MultipartFile file, TokenUserDTO userDTO) throws Exception {
        try (InputStream in = file.getInputStream()) {
            checkFile(file);
            String s = ExcelCheck.checkExcelPattern(file, new ManageStatusImportData(), CollNameList);
            if (StringUtils.isNotEmpty(s)) {
                throw new AmisBadRequestException(s);
            }
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            List<ManageStatusImportData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(ManageStatusImportData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            for (ManageStatusImportData next : excelList) {
                if (null == next.getStoreId()) {
                    throw new BusinessErrorException("门店ID不能为空");
                }
                if (StringUtils.isBlank(next.getGoodsNo())) {
                    throw new BusinessErrorException("商品编码不能为空");
                }
                Optional.ofNullable(ManageStatusEnum.getEnumByCode(next.getManageStatus())).orElseThrow(() -> new BusinessErrorException("经营状态不存在"));
                Optional.ofNullable(SuggestManageStatusEnum.getEnumByCode(next.getSuggestManageStatus())).orElseThrow(() -> new BusinessErrorException("建议经营状态不存在"));
            }
            excelList.stream().map(v -> {
                StoreGoodsContents contents = new StoreGoodsContents();
                contents.setStoreId(v.getStoreId());
                contents.setGoodsNo(v.getGoodsNo());
                contents.setManageStatus(v.getManageStatus());
                contents.setSuggestManageStatus(v.getSuggestManageStatus());
                return contents;
            }).collect(Collectors.groupingBy(StoreGoodsContents::getStoreId)).forEach((k, v) -> {
                Lists.partition(v, Constants.EXPORT_ONCE_QUERY_MAX).forEach(insert -> {
                    storeGoodsContentsExtendMapper.batchUpdateManageStatus(insert, k);
                });
            });
            logger.info("修改完毕");
        } catch (Exception e) {
            logger.error("导入修改经营状态失败", e);
            throw e;
        }
    }

    @Override
    public void bdpTaskCallback() {
        logger.info("bdp回调开始处理");
        List<Integer> bizType = dgmsBdpTaskExtendMapper.selectBizType();
        if (CollectionUtils.isEmpty(bizType)) {
            logger.info("没有需要处理的数据");
        }
        asyncTaskExecutor.execute(() -> {
            bizType.forEach(v -> {
                try {
                    bdpTaskCallback(v);
                } catch (Exception e) {
                    logger.error("bizType :" + v + "bdp回调处理失败", e);
                }
            });
        });
    }
    @Override
    public void bdpTaskCallback(String storeCode, Integer bizType) {
        logger.info("bdp回调开始处理");
        try {
            BdpTaskBizTypeEnum bizTypeEnum = Optional.ofNullable(BdpTaskBizTypeEnum.getEnumByCode(bizType)).orElseThrow(() -> new AmisBadRequestException("不支持的业务类型"));
            OrgInfoBaseCache store = CacheVar.getStoreBySapCode(storeCode).orElseThrow(() -> new BusinessErrorException("没有查询到门店信息"));
            switch (bizTypeEnum) {
                case TO_NO_MANAGE:
                    changeToNoManage(Lists.newArrayList(store.getOutId()), bizTypeEnum);
                    break;
                case TO_MANAGE:
                    changeToManage(Lists.newArrayList(store.getOutId()), bizTypeEnum);
                    break;
                case FORBID_APPLY:
                    refushForbidApply(Lists.newArrayList(store.getOutId()), bizTypeEnum);
                    break;
                case CATEGORY_CHANGED:
                    refushSubCategory(Lists.newArrayList(store.getOutId()), bizTypeEnum);
                    break;
                default:
                    break;

            }
        } catch (Exception e) {
            logger.error("bdp回调失败", e);
            throw e;
        }
    }
    @Override
    public void bdpTaskCallback(Integer bizType) throws Exception {
        try {
            BdpTaskBizTypeEnum bizTypeEnum = Optional.ofNullable(BdpTaskBizTypeEnum.getEnumByCode(bizType)).orElseThrow(() -> new AmisBadRequestException("不支持的业务类型"));
            List<Long> storeIds = dgmsBdpTaskExtendMapper.selectStoreIdsByBizType(bizTypeEnum.getCode());
            if (CollectionUtils.isEmpty(storeIds)) {
                logger.info("bizType:{}没有需要处理的任务", bizType);
                return;
            }
            switch (bizTypeEnum) {
                case TO_NO_MANAGE: changeToNoManage(storeIds, bizTypeEnum);
                    break;
                case TO_MANAGE: changeToManage(storeIds, bizTypeEnum);
                    break;
                case FORBID_APPLY: refushForbidApply(storeIds, bizTypeEnum);
                    break;
                case CATEGORY_CHANGED: refushSubCategory(storeIds, bizTypeEnum);
                    break;
                default:break;
            }
        } catch (Exception e) {
            logger.error("bdp回调失败", e);
            throw e;
        }
    }

    private void changeToNoManage(List<Long> storeIds, BdpTaskBizTypeEnum bizTypeEnum) {
        try {
            //正常转异常：需要取消必备，设置为异常经营。修改一店一目表tag=0，manage_status=异常经营属性（淘汰那些）。不下发mdm。
            for (Long storeId : storeIds) {
                DgmsBdpTaskExample bdpexample = new DgmsBdpTaskExample();
                bdpexample.createCriteria().andStoreIdEqualTo(storeId).andBizTypeEqualTo(bizTypeEnum.getCode());
                Map<String, DgmsBdpTask> dgmsBdpTasksMap = dgmsBdpTaskMapper.selectByExample(bdpexample).stream().collect(Collectors.toMap(DgmsBdpTask::getGoodsNo, Function.identity(), (k1,k2) -> k1));
                if (MapUtils.isEmpty(dgmsBdpTasksMap)) {
                    logger.info("storeId:{},bizType:{}没有商品需要处理", storeId,bizTypeEnum.getCode());
                    continue;
                }
                StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoIn(Lists.newArrayList(dgmsBdpTasksMap.keySet()));
                List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(storeGoodsContents)) {
                    logger.info("storeId:{},bizType:{}在一店一目中需要转的数据", storeId,bizTypeEnum.getCode());
                    continue;
                }
                storeGoodsContents.forEach(v -> {
                    v.setNecessaryTag(0);
                    v.setNecessaryTagName("");
                    v.setMinDisplayQuantity(BigDecimal.ZERO);
                    v.setManageStatus(dgmsBdpTasksMap.get(v.getGoodsNo()).getManageStatus());
                });
                Lists.partition(storeGoodsContents, Constants.INSERT_MAX_SIZE).forEach(v -> {
                    storeGoodsContentsExtendMapper.batchUpdate(v, storeId);
                });
            }
        } catch (Exception e) {
            logger.error("bdp回调-正常转异常失败", e);
        }
    }
    private void changeToManage(List<Long> storeIds, BdpTaskBizTypeEnum bizTypeEnum) {
        // 异常转正常：需要设置为选配。首先判断一店一目表tag，tag>0的行舍弃，tag=0的行，manage_status = 3,suggest_manage_status=3。下发mdm，配置配置刷新为经营-选配，禁止请货=否。
        try {
            List<MdmTaskDetail> taskDetails = new ArrayList<>();
            for (Long storeId : storeIds) {
                DgmsBdpTaskExample bdpexample = new DgmsBdpTaskExample();
                bdpexample.createCriteria().andStoreIdEqualTo(storeId).andBizTypeEqualTo(bizTypeEnum.getCode());
                Map<String, DgmsBdpTask> dgmsBdpTasksMap = dgmsBdpTaskMapper.selectByExample(bdpexample).stream().collect(Collectors.toMap(DgmsBdpTask::getGoodsNo, Function.identity(), (k1,k2) -> k1));
                if (MapUtils.isEmpty(dgmsBdpTasksMap)) {
                    logger.info("storeId:{},bizType:{}没有商品需要处理", storeId,bizTypeEnum.getCode());
                    continue;
                }
                StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoIn(Lists.newArrayList(dgmsBdpTasksMap.keySet())).andNecessaryTagEqualTo(0);
                List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(storeGoodsContents)) {
                    logger.info("storeId:{},bizType:{}在一店一目中需要转的数据", storeId,bizTypeEnum.getCode());
                    continue;
                }
                storeGoodsContents.forEach(v -> {
                    v.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                    v.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                    v.setForbidApply("否");
                });
                storeGoodsContentsExtendMapper.batchUpdate(storeGoodsContents, storeId);
                taskDetails.addAll(storeGoodsContents.stream().map(v -> {
                    MdmTaskDetail taskDetail = new MdmTaskDetail();
                    BeanUtils.copyProperties(v, taskDetail);
                    taskDetail.setStoreName(CacheVar.getStoreByStoreId(v.getStoreId()).orElse(new OrgInfoBaseCache()).getShortName());
                    taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
                    return taskDetail;
                }).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(taskDetails)) {
                logger.info("bizType:{}没有需要处理的数据", bizTypeEnum.getCode());
                return;
            }
            List<Long> taskDetailIds = getMdmTaskDetailIds(taskDetails.size());
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(Constants.SYS_USER_ID, Constants.SYS_USER_NAME), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(taskDetails.size());
            mdmTaskMapper.insertSelective(task);
            taskDetails.forEach(v -> {
                v.setTaskId(task.getId());
                v.setId(taskDetailIds.remove(0));
            });
            Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
                mdmTaskDetailExtendMapper.batchInsert(v);
                try {
                    factory.getAssemble(1).pushMdm(v);
                } catch (Exception e) {
                    logger.error("bdp回调-异常转正常下发失败", e);
                }
            });
        } catch (Exception e) {
            logger.error("bdp回调-异常转正常失败", e);
        }
    }

    private void refushForbidApply(List<Long> storeIds, BdpTaskBizTypeEnum bizTypeEnum) {
        try {
            List<MdmTaskDetail> taskDetails = new ArrayList<>();
            for (Long storeId : storeIds) {
                DgmsBdpTaskExample bdpexample = new DgmsBdpTaskExample();
                bdpexample.createCriteria().andStoreIdEqualTo(storeId).andBizTypeEqualTo(bizTypeEnum.getCode());
                Map<String, DgmsBdpTask> dgmsBdpTasksMap = dgmsBdpTaskMapper.selectByExample(bdpexample).stream().collect(Collectors.toMap(DgmsBdpTask::getGoodsNo, Function.identity(), (k1,k2) -> k1));
                if (MapUtils.isEmpty(dgmsBdpTasksMap)) {
                    logger.info("storeId:{},bizType:{}没有商品需要处理", storeId,bizTypeEnum.getCode());
                    continue;
                }
                StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoIn(Lists.newArrayList(dgmsBdpTasksMap.keySet()));
                List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(storeGoodsContents)) {
                    logger.info("storeId:{},bizType:{}在一店一目中需要转的数据", storeId,bizTypeEnum.getCode());
                    continue;
                }
                taskDetails.addAll(storeGoodsContents.stream().map(v -> {
                    MdmTaskDetail taskDetail = new MdmTaskDetail();
                    BeanUtils.copyProperties(v, taskDetail);
                    taskDetail.setStoreName(CacheVar.getStoreByStoreId(v.getStoreId()).orElse(new OrgInfoBaseCache()).getShortName());
                    taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
                    return taskDetail;
                }).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(taskDetails)) {
                logger.info("bizType:{}没有需要处理的数据", bizTypeEnum.getCode());
                return;
            }
            List<Long> taskDetailIds = getMdmTaskDetailIds(taskDetails.size());
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(Constants.SYS_USER_ID, Constants.SYS_USER_NAME), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(taskDetails.size());
            mdmTaskMapper.insertSelective(task);
            taskDetails.forEach(v -> {
                v.setTaskId(task.getId());
                v.setId(taskDetailIds.remove(0));
            });
            Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
                mdmTaskDetailExtendMapper.batchInsert(v);
                try {
                    factory.getAssemble(1).pushMdm(v);
                } catch (Exception e) {
                    logger.error("bdp回调-异常转正常下发失败", e);
                }
            });

        } catch (Exception e) {
            logger.error("bdp回调-刷新禁止请货失败", e);
        }
    }

    private void refushSubCategory(List<Long> storeIds, BdpTaskBizTypeEnum bizTypeEnum) {
        try {
            for (Long storeId : storeIds) {
                DgmsBdpTaskExample bdpexample = new DgmsBdpTaskExample();
                bdpexample.createCriteria().andStoreIdEqualTo(storeId).andBizTypeEqualTo(bizTypeEnum.getCode());
                Map<String, DgmsBdpTask> dgmsBdpTasksMap = dgmsBdpTaskMapper.selectByExample(bdpexample).stream().collect(Collectors.toMap(DgmsBdpTask::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                if (MapUtils.isEmpty(dgmsBdpTasksMap)) {
                    logger.info("storeId:{},bizType:{}没有商品需要处理", storeId, bizTypeEnum.getCode());
                    continue;
                }
                StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoIn(Lists.newArrayList(dgmsBdpTasksMap.keySet()));
                List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(storeGoodsContents)) {
                    logger.info("storeId:{},bizType:{}在一店一目中需要转的数据", storeId, bizTypeEnum.getCode());
                    continue;
                }
                storeGoodsContents.forEach(v -> {
                    DgmsBdpTask dgmsBdpTask = dgmsBdpTasksMap.get(v.getGoodsNo());
                    v.setSubCategoryId(dgmsBdpTask.getSubCategory());
                });
                storeGoodsContentsExtendMapper.batchUpdateCategory(storeGoodsContents, storeId);
            }
        } catch (Exception e) {
            logger.error("bdp回调-刷新子类失败", e);
        }
    }

    @Override
    public void importUpdateCategory(MultipartFile file, TokenUserDTO userDTO) throws Exception {
        try (InputStream in = file.getInputStream()){
            checkFile(file);
            String s = ExcelCheck.checkExcelPattern(file, new UpdateCategoryImportData(), CollNameList);
            if (StringUtils.isNotEmpty(s)) {
                throw new BusinessErrorException(s);
            }
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            List<UpdateCategoryImportData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(UpdateCategoryImportData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            for (UpdateCategoryImportData next : excelList) {
                if (null == next.getStoreId()) {
                    throw new BusinessErrorException("门店ID不能为空");
                }
                if (StringUtils.isBlank(next.getGoodsNo())) {
                    throw new BusinessErrorException("商品编码不能为空");
                }
                if (null == next.getCategoryId()) {
                    throw new BusinessErrorException("子类ID不能为空");
                }
            }
            excelList.stream().collect(Collectors.groupingBy(UpdateCategoryImportData::getStoreId)).forEach((k,v) -> {
                List<StoreGoodsContents> updateList = v.stream().map(goo -> {
                    StoreGoodsContents contents = new StoreGoodsContents();
                    contents.setStoreId(goo.getStoreId());
                    contents.setGoodsNo(goo.getGoodsNo());
                    contents.setSubCategoryId(goo.getCategoryId());
                    return contents;
                }).collect(Collectors.toList());
                Lists.partition(updateList, Constants.EXPORT_ONCE_QUERY_MAX).forEach(insert -> {
                    storeGoodsContentsExtendMapper.batchUpdateCategory(insert, k);
                });
            });
            logger.info("刷新商品分类完毕");
        } catch (Exception e) {
            logger.error("导入失败", e);
            throw e;
        }
    }
}
