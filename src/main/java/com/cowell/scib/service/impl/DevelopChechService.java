package com.cowell.scib.service.impl;

import com.cowell.scib.enums.AlertTypeEnum;
import com.cowell.scib.enums.DevelopTypeEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.UseStatusEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.DevelopModuleService;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 校验类
 * <AUTHOR>
 * @date 2022/8/30 13:49
 */
@Slf4j
@Service
public class DevelopChechService {

    @Autowired
    private DevelopModuleService developModuleService;

    private final static Integer NAME_MAX_SIZE = 20;
    private final static Integer TITLE_MAX_SIZE = 50;
    private final static Integer CONTENT_MAX_SIZE = 500;

    /**
     * 模块详情参数校验
     */
    protected void checkModuleDetailParam(DevelopListParam developListParam){
        if(Objects.isNull(developListParam) || StringUtils.isBlank(developListParam.getModuleCode())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
    }

    /**
     * 模块记录详情参数校验
     */
    protected void checkRecordDetailParam(DevelopListParam developListParam){
        if(Objects.isNull(developListParam) || Objects.isNull(developListParam.getRecordId())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
    }

    /**
     * 校验模块添加接口
     * @param developAddParam
     */
    protected Long checkModuleAddOrEditParam(DevelopAddParam developAddParam){
        if(Objects.isNull(developAddParam)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        String moduleName = developAddParam.getModuleName();
        chehckMaxLimit(moduleName, NAME_MAX_SIZE);
        if(!UseStatusEnum.exitCode(developAddParam.getUseStatus())){
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_USE_ILLEGAL);
        }
        long moduleCodeCount = developModuleService.countModuleByCode(developAddParam.getModuleCode());
        if (moduleCodeCount<1){
            long nameCount = developModuleService.countDevelopModuleByName(moduleName);
            if(nameCount > 0){
                throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_NAME_EXIT);
            }
        }
        if (developAddParam.getCreateMark() != null && developAddParam.getCreateMark().equals(1) && moduleCodeCount > 0) {
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_CODE_EXIT);
        }
        return moduleCodeCount;
    }

    /**
     * 校验模块添加接口
     * @param developRecordAddParam
     */
    protected void checkModuleRecordAddOrEditParam(DevelopRecordAddParam developRecordAddParam){
        if(Objects.isNull(developRecordAddParam)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        if(StringUtils.isBlank(developRecordAddParam.getDevelopVersion())){
            throw new AmisBadRequestException(ErrorCodeEnum.VERSION_NULL_ERROR);
        }
        if(StringUtils.isBlank(developRecordAddParam.getDevelopTitle())){
            throw new AmisBadRequestException(ErrorCodeEnum.TITLE_NULL_ERROR);
        }
        if(StringUtils.isBlank(developRecordAddParam.getDevelopContent())){
            throw new AmisBadRequestException(ErrorCodeEnum.CONTENT_NULL_ERROR);
        }
        if(Objects.isNull(developRecordAddParam.getDevelopType())){
            throw new AmisBadRequestException(ErrorCodeEnum.TYPE_NULL_ERROR);
        }
        if(StringUtils.isBlank(developRecordAddParam.getModuleCode())){
            throw new AmisBadRequestException(ErrorCodeEnum.MODULE_NULL_ERROR);
        }
        if(StringUtils.isBlank(developRecordAddParam.getDevelopTime())){
            throw new AmisBadRequestException(ErrorCodeEnum.TIME_NULL_ERROR);
        }
        chehckMaxLimit(developRecordAddParam.getDevelopVersion(), NAME_MAX_SIZE);
        chehckMaxLimit(developRecordAddParam.getDevelopTitle(), TITLE_MAX_SIZE);
        chehckMaxLimit(developRecordAddParam.getDevelopContent(), CONTENT_MAX_SIZE);
        if(!DevelopTypeEnum.exitCode(developRecordAddParam.getDevelopType())){
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_USE_ILLEGAL);
        }
        Date date = new Date(Long.parseLong(developRecordAddParam.getDevelopTime())*1000);
        developRecordAddParam.setDevelopTime(DateUtils.conventDateStrByDate(date, DateUtils.DATE_MINUTE_PATTERN));
        if(!checkDevelopDate(developRecordAddParam.getDevelopTime())){
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_TIME_ILLEGAL);
        }
        long nameCount = developModuleService.countDevelopModuleByCode(developRecordAddParam.getModuleCode());
        if(nameCount == 0){
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_CODE_ILLEGAL);
        }
    }

    /**
     * 长度校验方法
     * @param target
     * @param maxSize
     */
    protected void chehckMaxLimit(String target, int maxSize){
        if(StringUtils.isNotBlank(target) && target.length() > maxSize){
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_INPUT_LIMIT, String.format(ErrorCodeEnum.DEVELOP_MODULE_INPUT_LIMIT.getMsg(), maxSize));
        }
    }

    /**
     * 校验发布日期格式
     * @param targetDate
     * @return
     * @throws Exception
     */
    public boolean checkDevelopDate(String targetDate) {
        log.info("checkDevelopDate|targetDate:{}.", targetDate);
        if(!DateUtils.checkDateFormat(targetDate, DateUtils.DATE_MINUTE_PATTERN)){
            return false;
        }
        if(!DateUtils.checkDateScope(targetDate)){
            return false;
        }
        return true;
    }

    /**
     * 校验文件
     * @param file
     */
    protected static void checkFileInfo(MultipartFile file, String fileSuffix) {
        log.info("checkFileInfo|后缀名:{}.", fileSuffix);
        if (null == file) {

            throw new AmisBadRequestException(ErrorCodeEnum.UPLOAD_FILE_NOT_EXISTS);
        }

        String fileOriginalName = file.getOriginalFilename();
        if (org.apache.commons.lang.StringUtils.isNotBlank(fileOriginalName)) {

            int index = fileOriginalName.lastIndexOf(".");
            List<String> suffixList = Arrays.stream(fileSuffix.split(",")).collect(Collectors.toList());

            if (index > -1 && !suffixList.contains(fileOriginalName.substring(index))) {
                throw new AmisBadRequestException(ErrorCodeEnum.FILE_UPLOAD_FORMAT_ERROR);
            }

        }
    }

}
