package com.cowell.scib.service.impl;

import com.beust.jcommander.internal.Lists;
import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtendExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.BundlTaskWriteService;
import com.cowell.scib.service.RuleService;
import com.cowell.scib.service.dto.BundlTaskDetailDTO;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import com.cowell.scib.service.param.BundlTaskAddParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 第二步操作
 * <AUTHOR>
 * @date 2023/3/15 9:56
 */
@Slf4j
@Component
public class BundlTaskTwoStep extends BundlTaskStepHandler<BundlTaskAddParam>{

    @Autowired
    private RuleService ruleService;
    @Autowired
    private BundlTaskWriteService bundlTaskWriteService;
    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtMapper;
    @Autowired
    private BundlingTaskDetailExtendExtMapper bundlingTaskDetailExtendExtMapper;
    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;
    @Autowired
    private BundlingTaskStoreDetailExtendMapper taskStoreDetailExtendMapper;
    @Override
    public boolean check(BundlTaskAddParam bundlTaskAddParam) {
        if(Objects.isNull(bundlTaskAddParam.getTaskId())){
            throw new BusinessErrorException("taskId不能为空");
        }
        long taskStoreCount = taskStoreDetailExtendMapper.countBundlTaskStoreListByTaskId(bundlTaskAddParam.getTaskId());
        if(taskStoreCount <= 0){
            throw new BusinessErrorException("门店清单不能为空");
        }
        long confirmStoreCount = taskStoreDetailExtendMapper.countBundlTaskStoreListByConfirm(bundlTaskAddParam.getTaskId(), BundlBoolEnum.YES.getCode());
        if(confirmStoreCount <= 0){
            throw new BusinessErrorException("业务确认参与组货都为否");
        }
        return false;
    }

    @Override
    public void add(BundlTaskAddParam bundlTaskAddParam) {
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(bundlTaskAddParam.getTaskId());
        if(Objects.isNull(bundlingTaskInfo)){
            throw new BusinessErrorException(new StringBuilder().append("无此任务，请检查！").append(bundlTaskAddParam.getTaskId()).toString());
        }
        Map<String, RuleDetailDTO> ruleDetailDTOMap =  ruleService.getConfigRuleList(bundlingTaskInfo.getOrgId(), Lists.newArrayList(DicApiEnum.ZH_GOODS.getCode()), ConfigTypeEnum.BB.getType());
        Map<String, BundlTaskDetailDTO> detailDTOMap = new HashMap<>(ruleDetailDTOMap.size());
        ruleDetailDTOMap.forEach((k, dto)->{
            if(!DictGoodsEnum.GOODSTYPE.getCode().equals(k)){
                BundlTaskDetailDTO detailDTO = new BundlTaskDetailDTO();
                BeanUtils.copyProperties(dto, detailDTO);
                detailDTOMap.put(k, detailDTO);
            }
        });
        bundlTaskAddParam.setDetailMap(detailDTOMap);
        bundlTaskWriteService.addTaskDetail(bundlTaskAddParam, bundlTaskAddParam.getUserDTO());
    }

    @Override
    public void deleteDetail(BundlTaskAddParam bundlTaskAddParam) {
        if(Objects.isNull(bundlTaskAddParam.getTaskId())){
            log.info("BundlTaskTwoStep|添加不删除");
            return;
        }
        bundlingTaskDetailExtMapper.deleteDetailByStep(bundlTaskAddParam.getTaskId(), bundlTaskAddParam.getTaskStep());
        bundlingTaskDetailExtendExtMapper.deleteDetailByTaskId(bundlTaskAddParam.getTaskId());
    }

    @Override
    public boolean isHandler(BundlTaskAddParam bundlTaskAddParam) {
        return BundlTaskStepEnum.TWO.getStep().equals(bundlTaskAddParam.getTaskStep());
    }
}
