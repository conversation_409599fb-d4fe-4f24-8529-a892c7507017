package com.cowell.scib.service.impl;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.ITencentCOSService;
import com.cowell.scib.service.vo.amis.UrlResult;
import com.cowell.scib.utils.DateUtils;
import com.mysql.jdbc.StringUtils;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.Download;
import com.qcloud.cos.transfer.TransferManager;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class TencentCOSServiceImpl extends DevelopChechService implements ITencentCOSService {

    private final Logger logger = LoggerFactory.getLogger(TencentCOSServiceImpl.class);

    //地域
    @Value("${tencent.deve.region}")
    private String region;
    //appId
    @Value("${tencent.deve.appId}")
    private String appId;
    //密钥Id
    @Value("${tencent.deve.secretId}")
    private String secretId;
    //密钥Key
    @Value("${tencent.deve.secretKey}")
    private String secretKey;
    //默认路径
    @Value("${tencent.deve.baseUrl}")
    private String BASE_URL;
    //默认Bucket
    @Value("${tencent.deve.defaultBucketName}")
    private String DEFAULT_BUCKET_NAME;
    @Value("${develop.file.suffix:}")
    private String fileSuffix;

    private static COSClient cosClient;

    public TencentCOSServiceImpl() {
    }

    @PostConstruct
    public void init() {
        // 1 初始化用户身份信息(appid, secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(region));
        // 3 生成cos客户端
        cosClient = new COSClient(cred, clientConfig);
    }

    /**
     * 获取预授权下载地址
     *
     * @param key
     * @return
     */
    @Override
    public String getSign(String key) {

        GeneratePresignedUrlRequest req =
            new GeneratePresignedUrlRequest(new StringBuilder().append(DEFAULT_BUCKET_NAME).append("-").append(appId).toString(), key, HttpMethodName.GET);
        // 设置签名过期时间(可选), 过期时间不做限制，只需比当前时间大, 若未进行设置, 则默认使用ClientConfig中的签名过期时间(5分钟)
        // 这里设置签名在半个小时后过期
        Date expirationDate = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        req.setExpiration(expirationDate);

        URL url = cosClient.generatePresignedUrl(req);
        String urlString = url.toString();

        try {
            urlString = URLDecoder.decode(urlString, "utf-8");
            urlString = urlString.replace("sign=", "");
        } catch (UnsupportedEncodingException e) {
            logger.error("腾讯云url解密失败", e);
        }
        return urlString;
    }

    private String getBucketName() {
        return new StringBuilder().append(DEFAULT_BUCKET_NAME).append("-").append(appId).toString();
    }

    /**
     * @param key key就是fileurl的path 不带域名的部分
     * @return
     */
    @Override
    public InitiateMultipartUploadResult initiateMultipartUpload(String key) {
        InitiateMultipartUploadRequest initiateMultipartUploadRequest = new InitiateMultipartUploadRequest(getBucketName(), key);
        InitiateMultipartUploadResult result = null;
        try {
            result = cosClient.initiateMultipartUpload(initiateMultipartUploadRequest);
        } catch (CosClientException e) {
            logger.error("cos初始化分片上传失败", e);
            throw new BusinessErrorException("cos初始化分片上传失败");
        } finally {
            shutdown();
        }
        return result;
    }


    @Override
    public UploadPartResult uploadPart(String key, String uploadId, int partNumber, long partSize, InputStream inputStream) {
        UploadPartRequest uploadPartRequest = new UploadPartRequest();
        uploadPartRequest.setBucketName(getBucketName());
        uploadPartRequest.setKey(key);
        uploadPartRequest.setUploadId(uploadId);
        uploadPartRequest.setInputStream(inputStream);
        uploadPartRequest.setPartNumber(partNumber);
        uploadPartRequest.setPartSize(partSize);

        UploadPartResult result = null;
        try {
            result = cosClient.uploadPart(uploadPartRequest);
        } catch (Exception e) {
            logger.error("cos分片上传失败", e);
            throw e;
        } finally {
            shutdown();
        }
        return result;
    }

    @Override
    public CompleteMultipartUploadResult completeMultipartUpload(String key, String uploadId, List<PartETag> partETags) {
        CompleteMultipartUploadRequest completeMultipartUploadRequest = new CompleteMultipartUploadRequest();

        completeMultipartUploadRequest.setBucketName(getBucketName());
        completeMultipartUploadRequest.setKey(key);
        completeMultipartUploadRequest.setUploadId(uploadId);
        completeMultipartUploadRequest.setPartETags(partETags);


        CompleteMultipartUploadResult result = null;
        try {
            result = cosClient.completeMultipartUpload(completeMultipartUploadRequest);
        } catch (CosClientException e) {
            logger.error("cos分片完成上传失败", e);
            throw new BusinessErrorException("cos分片完成上传失败");
        } finally {
            shutdown();
        }
        return result;
    }

    @Override
    public void abortMultipartUpload(String key, String uploadId) {
        AbortMultipartUploadRequest abortMultipartUploadRequest = new AbortMultipartUploadRequest(getBucketName(), key, uploadId);

        try {
            cosClient.abortMultipartUpload(abortMultipartUploadRequest);
        } catch (CosClientException e) {
            logger.error("cos分片完成上传失败", e);
            throw new BusinessErrorException("cos分片完成上传失败");
        } finally {
            shutdown();
        }
    }

    @Override
    public ObjectMetadata getObjectMetadata(String key) {
        ObjectMetadata objectMetadata = null;
        try {
            objectMetadata = cosClient.getObjectMetadata(getBucketName(), key);
        } catch (CosClientException e) {
            logger.error("cos获取文件元数据失败", e);
        } finally {
            shutdown();
        }
        return objectMetadata;
    }

    @Override
    public InputStream rangeDownloadFileStream(String key, long start, long end) {

        GetObjectRequest getObjectRequest = new GetObjectRequest(getBucketName(), key);
//        getObjectRequest.setRange(pageSize * (page - 1), page * pageSize - 1);
        getObjectRequest.setRange(start, end);
        logger.debug("rangeDownloadFileStream,rang={}", getObjectRequest.getRange());
        COSObject cosObject = cosClient.getObject(getObjectRequest);
        COSObjectInputStream cosObjectInput = null;
        if (null != cosObject) {
            cosObjectInput = cosObject.getObjectContent();
        }
        return cosObjectInput;
    }

    @Override
    public UrlResult uploadAndGetFile(String fileName, MultipartFile file) {
        checkFileInfo(file, fileSuffix);
        try {

            if(StringUtils.isNullOrEmpty(fileName)){
                logger.warn("fileName为空");
                fileName = file.getOriginalFilename();
            }
//            else{
//                fileName = fileName.substring(0, fileName.indexOf("."));
//            }
            logger.info("uploadAndGetFile|fileName:{}.", fileName);
            String targetPath = new StringBuilder().append(Constants.CATALOG).append(new DateTime(System.currentTimeMillis()).toString(DateUtils.DATE_SAP_PATTERN)).append("/").toString();
            logger.info("uploadAndGetFile|targetPath:{}.", targetPath);
            String url = simpleUploadFileStream(targetPath, fileName, file.getInputStream());
            return new UrlResult(url);
        } catch (BusinessErrorException be){
            logger.warn("uploadAndGetFile|warn", be);
            throw be;
        } catch (IOException ie) {
            logger.error("uploadAndGetFile|error",ie);
            return new UrlResult();
        } catch (Exception e){
            logger.error("uploadAndGetFile|异常",e);
            throw e;
        }
    }

    /**
     * 关闭客户端
     *
     * @param
     */
    private void shutdown() {
        cosClient.shutdown();
    }

    private String getBucketName(String name) {
        if (StringUtils.isNullOrEmpty(name)) {
            return new StringBuilder().append(DEFAULT_BUCKET_NAME).append("-").append(appId).toString();
        }
        return new StringBuilder().append(name).append("-").append(appId).toString();
    }

    /**
     * 创建Bucket-暂不可用
     *
     * @param name（仅支持小写字母、数字和 - 的组合，不能超过40字符）
     */
    @Override
    public String createBucket(String name) {
        if (cosClient.doesBucketExist(getBucketName(name))) { //判断是否存在
            // 关闭客户端
            shutdown();
            return name;
        }
        CreateBucketRequest createBucketRequest = new CreateBucketRequest(getBucketName(name));
        // 设置bucket的权限为public staticRead(公有读私有写), 其他可选有私有读写, 公有读私有写
        createBucketRequest.setCannedAcl(CannedAccessControlList.PublicRead);
        try {
            cosClient.createBucket(createBucketRequest);
        } catch (Exception e) {
            logger.error("创建桶失败:{}", e);
        } finally {
            // 关闭客户端
            shutdown();
        }
        return name;
    }

    /**
     * 删除bucket, 只用于空bucket, 含有数据的bucket需要在删除前清空删除。-暂不可用
     */
    @Override
    public void deleteBucket(String name) {
        // 删除bucket
        cosClient.deleteBucket(getBucketName(name));
        // 关闭客户端
        shutdown();
    }

    /**
     * 将本地单文件上传到COS
     *
     * @param bucketName-桶名称，可空
     * @param targetPath-目标存储路径，空为根目录
     * @param fileName-文件名称-不可空
     * @param localFile-文件-不可空
     * @return
     */
    @Override
    public boolean simpleUploadFile(String bucketName, String targetPath, String fileName, File localFile) {
        String filePath = new StringBuilder().append(targetPath != null ? targetPath : "").append(fileName != null ? fileName : "").toString();
        PutObjectRequest putObjectRequest = new PutObjectRequest(getBucketName(bucketName), filePath, localFile);
        // 设置存储类型, 默认是标准(Standard), 低频(standard_ia), 近线(nearline)
        return uploadFile(putObjectRequest);
    }

    /**
     * 从输入流进行读取并上传到COS
     *
     * @param targetPath-目标存储路径，空为根目录
     * @param fileName-文件名称-不可空
     * @return
     */
    @Override
    public String simpleUploadFileStream(String targetPath, String fileName, InputStream inputStream) {
        return simpleUploadFileStream(null, targetPath, fileName, inputStream);
    }

    /**
     * 从输入流进行读取并上传到COS
     *
     * @param bucketName-桶名称，可空
     * @param targetPath-目标存储路径，空为根目录
     * @param fileName-文件名称-不可空
     * @return
     */
    @Override
    public String simpleUploadFileStream(String bucketName, String targetPath, String fileName, InputStream inputStream) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 从输入流上传必须制定content length, 否则http客户端可能会缓存所有数据，存在内存OOM的情况
        try {
            objectMetadata.setContentLength(inputStream.available());
        } catch (IOException e) {
            logger.error("文件上传出错-服务端数据流转换异常错误：{}", fileName, e);
            return null;
        }
        // 默认下载时根据cos路径key的后缀返回响应的contenttype, 上传时设置contenttype会覆盖默认值
        //objectMetadata.setContentType("image/jpeg");
        String filePath = new StringBuilder().append(targetPath != null ? targetPath : "").append(fileName != null ? fileName : "").toString();
        PutObjectRequest putObjectRequest =
            new PutObjectRequest(getBucketName(bucketName), filePath, inputStream, objectMetadata);
        String fileUrl = null;
        if (uploadFile(putObjectRequest)) {
            fileUrl = new StringBuilder().append(BASE_URL.replace("BUCKET", getBucketName(bucketName))
                    .replace("REGION", region)).append(filePath).toString();
        }
        return fileUrl;
    }

    @Override
    public String uploadFileStreamByContentLength(String targetPath, String fileName, InputStream inputStream, Long contentLength) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(contentLength);
        // 默认下载时根据cos路径key的后缀返回响应的contenttype, 上传时设置contenttype会覆盖默认值
        //objectMetadata.setContentType("image/jpeg");
        String filePath = new StringBuilder().append(targetPath != null ? targetPath : "").append(fileName != null ? fileName : "").toString();
        PutObjectRequest putObjectRequest =
            new PutObjectRequest(getBucketName(null), filePath, inputStream, objectMetadata);
        String fileUrl = null;
        if (uploadFile(putObjectRequest)) {
            fileUrl = new StringBuilder().append(BASE_URL.replace("BUCKET", getBucketName(null))
                    .replace("REGION", region)).append(filePath).toString();
        }
        return fileUrl;
    }

    /**
     * 文件上传
     *
     * @param putObjectRequest
     * @return
     */
    public boolean uploadFile(PutObjectRequest putObjectRequest) {
        putObjectRequest.setStorageClass(StorageClass.Standard);
        try {
            cosClient.putObject(putObjectRequest);
        } catch (CosServiceException e) {
            logger.warn("文件上传出错-服务端错误, putObjectRequest:{}", putObjectRequest, e);
            return false;
        } catch (CosClientException e) {
            logger.warn("文件上传出错-客户端错误, putObjectRequest:{}", putObjectRequest, e);
            return false;
        }

        // 关闭客户端
        shutdown();
        return true;
    }

    /**
     * 删除单个文件(不带版本号, 即bucket未开启多版本)
     *
     * @param fileName-文件名称，不可空
     * @param filePath-文件存储路径，空为根目录
     */
    @Override
    public boolean delSingleFile(String filePath, String fileName) {
        return delSingleFile(null, filePath, fileName);
    }

    /**
     * 删除单个文件(不带版本号, 即bucket未开启多版本)
     *
     * @param filePath-文件存储路径，空为根目录
     */
    @Override
    public boolean delSingleFile(String filePath) {
        return delSingleFile(null, filePath, "");
    }

    /**
     * 删除单个文件(不带版本号, 即bucket未开启多版本)
     *
     * @param bucketName-桶名称，可空
     * @param fileName-文件名称，不可空
     * @param filePath-文件存储路径，空为根目录
     */
    @Override
    public boolean delSingleFile(String bucketName, String filePath, String fileName) {
        try {
            cosClient.deleteObject(getBucketName(bucketName), new StringBuilder().append(filePath).append(fileName).toString());
        } catch (CosServiceException e) { // 如果是其他错误, 比如参数错误， 身份验证不过等会抛出CosServiceException
            logger.error("文件删除出错-服务端：{}", new StringBuilder().append(filePath).append(fileName).toString(), e);
            return false;
        } catch (CosClientException e) { // 如果是客户端错误，比如连接不上COS
            logger.error("文件删除出错-客户端：{}", new StringBuilder().append(filePath).append(fileName).toString(), e);
            return false;
        }

        // 关闭客户端
        shutdown();
        return true;
    }

    /**
     * 批量删除文件(不带版本号, 即bucket未开启多版本)
     *
     * @param bucketName
     * @param keyList（max1000）
     */
    @Override
    public boolean batchDelFile(String bucketName, ArrayList<DeleteObjectsRequest.KeyVersion> keyList) {
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(getBucketName(bucketName));
        // 设置要删除的key列表, 最多一次删除1000个
        deleteObjectsRequest.setKeys(keyList);

        // 批量删除文件
        try {
            DeleteObjectsResult deleteObjectsResult = cosClient.deleteObjects(deleteObjectsRequest);
            List<DeleteObjectsResult.DeletedObject> deleteObjectResultArray = deleteObjectsResult.getDeletedObjects();
        } catch (Exception e) {
            logger.error("文件删除出错：{}", bucketName, e);
            return false;
        }

        // 关闭客户端
        shutdown();
        return true;
    }

    /**
     * 获取文件列表
     *
     * @param bucketName
     * @param prefix
     * @return
     */
    @Override
    public List<Map<String, Object>> listObjects(String bucketName, String prefix) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        // 设置bucket名称
        listObjectsRequest.setBucketName(getBucketName(bucketName));
        // prefix表示列出的object的key以prefix开始
        listObjectsRequest.setPrefix(prefix);
        // deliter表示分隔符, 设置为/表示列出当前目录下的object, 设置为空表示列出所有的object
        listObjectsRequest.setDelimiter("/");
        // 如果object的路径中含有特殊字符, 建议使用url编码方式, 得到object的key后, 需要进行url decode
        listObjectsRequest.setEncodingType("url");
        // 设置最大遍历出多少个对象, 一次listobject最大支持1000
        listObjectsRequest.setMaxKeys(1000);
        ObjectListing objectListing = null;
        try {
            objectListing = cosClient.listObjects(listObjectsRequest);
        } catch (Exception e) {
            logger.error("文件列表获取出错：{}", new StringBuilder().append(bucketName).append(prefix).toString(), e);
            return new ArrayList<>();
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        if (null != objectListing) {
            // object summary表示所有列出的object列表
            List<COSObjectSummary> cosObjectSummaries = objectListing.getObjectSummaries();
            for (COSObjectSummary cosObjectSummary : cosObjectSummaries) {
                // 文件的路径key
                String key = cosObjectSummary.getKey();
                // 如果使用的encodingtype是url, 则进行url decode
                try {
                    key = URLDecoder.decode(key, "utf-8");
                } catch (UnsupportedEncodingException e) {
                    logger.error("文件转码异常:", e);
                    continue;
                }
                // 文件的etag
                String etag = cosObjectSummary.getETag();
                // 文件的长度
                long fileSize = cosObjectSummary.getSize();
                // 文件的存储类型
                String storageClasses = cosObjectSummary.getStorageClass();
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("key", key);
                resultMap.put("etag", etag);
                resultMap.put("fileSize", fileSize);
                resultMap.put("storageClasses", storageClasses);
                resultList.add(resultMap);
            }
        }

        shutdown();
        return resultList;
    }

    /**
     * 文件下载
     *
     * @param bucketName-桶名称，可空
     * @param filePath-文件存储路径，空为根目录
     * @param fileName-文件名称，不可空
     * @throws InterruptedException
     */
    @Override
    public File downloadFile(String bucketName, String filePath, String fileName, String targetPath) {
        File file = new File(targetPath);
        String targetFilePath = new StringBuilder().append(filePath != null ? filePath : "").append(fileName != null ? fileName : "").toString();
        GetObjectRequest getObjectRequest = new GetObjectRequest(getBucketName(bucketName), targetFilePath);

        // 传入一个threadpool, 若不传入线程池, 默认TransferManager中会生成一个单线程的线程池。
        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        TransferManager transferManager = new TransferManager(cosClient, threadPool);
        Download download = transferManager.download(getObjectRequest, file);
        // 等待传输结束（如果想同步的等待上传结束，则调用 waitForCompletion）
        try {
            download.waitForCompletion();
        } catch (InterruptedException e) {
            logger.error("文件下载出错-路径：{}", new StringBuilder().append(filePath).append(fileName).toString(), e);
        } finally {
            shutdown();
        }
        return file;
    }

    /**
     * 获取文件下载流
     *
     * @param bucketName-桶名称，可空
     * @param filePath-文件存储路径，空为根目录
     * @param fileName-文件名称，不可空
     */
    @Override
    public InputStream downloadFileStream(String bucketName, String filePath, String fileName) {
        String targetFilePath = new StringBuilder().append(filePath != null ? filePath : "").append(fileName != null ? fileName : "").toString();
        GetObjectRequest getObjectRequest = new GetObjectRequest(getBucketName(bucketName), targetFilePath);

        COSObject cosObject = cosClient.getObject(getObjectRequest);
        COSObjectInputStream cosObjectInput = null;
        if (null != cosObject) {
            cosObjectInput = cosObject.getObjectContent();
        }
        return cosObjectInput;
    }

    /**
     * 获取文件下载流
     *
     * @param fileUrl-文件url，不可空
     */
    @Override
    public InputStream downloadFileStream(String fileUrl) {
        fileUrl = fileUrl.split(getBaseUrl())[1];
        logger.debug("下载文件路径:{}", fileUrl);
        GetObjectRequest getObjectRequest = new GetObjectRequest(getBucketName(null), fileUrl);

        COSObject cosObject = cosClient.getObject(getObjectRequest);
        COSObjectInputStream cosObjectInput = null;
        if (null != cosObject) {
            cosObjectInput = cosObject.getObjectContent();
        }
        return cosObjectInput;
    }

    /**
     * 获取云存储URL
     *
     * @return
     */
    @Override
    public String getBaseUrl() {
        return BASE_URL.replace("BUCKET", getBucketName(null))
            .replace("REGION", region);
    }

    @Override
    public boolean isExsit(String bucketName, String fileName) {
        return cosClient.doesObjectExist(getBucketName(bucketName), fileName);
    }

}
