package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.*;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.service.dto.*;
import com.cowell.permission.vo.OrgVO;
import com.cowell.permission.vo.ResourceTreeVO;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.permssion.EmployeeDetailWithWxDTO;
import com.cowell.scib.service.feign.PermissionFeignClient;
import com.cowell.scib.service.feign.PermissionFeignSyncClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class PermissionServiceImpl implements PermissionService {
    private final Logger logger = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Autowired
    private PermissionFeignClient permissionFeignClient;
    @Autowired
    private PermissionFeignSyncClient permissionFeignSyncClient;

    @Value("${scib.resourceId:6}")
    private Long scibResourceId;

    @Override
    public List<OrgSimpleDTO> getUserDataScopeChildOrgByOrgId(Long userId, List<Long> orgIds, boolean isRecursion) {
        try {
            int isRecurs = isRecursion ? 1 : 0;
            logger.info("开始调用权限获取用户是否有权限访问指定orgId下的所有子级组织 userId:{},resourceId:{},orgIds:{},isRecursion:{}", userId, scibResourceId, JSON.toJSONString(orgIds), isRecurs);
            ResponseEntity<List<OrgSimpleDTO>> responseEntity = permissionFeignClient.getUserDataScopeChildOrgByOrgId(userId, scibResourceId, orgIds, isRecurs);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限获取用户是否有权限访问指定orgId下的所有子级组织失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取用户是否有权限访问指定orgId下的所有子级组织失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权获取用户是否有权限访问指定orgId下的所有子级组织失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgSimpleDTO> getUserDataScopeChildOrgByOrgIdWithResourceId(Long userId, List<Long> orgIds, Long resourceId, boolean isRecursion) {
        try {
            int isRecurs = isRecursion ? 1 : 0;
            logger.info("开始调用权限获取用户是否有权限访问指定orgId下的所有子级组织 userId:{},resourceId:{},orgIds:{},isRecursion:{}", userId, resourceId, JSON.toJSONString(orgIds), isRecurs);
            ResponseEntity<List<OrgSimpleDTO>> responseEntity = permissionFeignClient.getUserDataScopeChildOrgByOrgId(userId, resourceId, orgIds, isRecurs);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限获取用户是否有权限访问指定orgId下的所有子级组织失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取用户是否有权限访问指定orgId下的所有子级组织失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权获取用户是否有权限访问指定orgId下的所有子级组织失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgDTO> getParentOrgByIdAndType(Long orgId, Integer type) {
        try {
            ResponseEntity<List<OrgDTO>> entity = permissionFeignClient.getParentOrgByIdAndType(orgId, type);
            if (entity == null || CollectionUtils.isEmpty(entity.getBody())) {
                throw new BusinessErrorException(ErrorCodeEnum.ORG_INFO_PLATFORM_NOT_EXIST);
            }
            return entity.getBody();
        } catch (Exception e) {
            logger.error("根据组织机构id获取平台信息失败:", e);
            throw e;
        }

    }


    @Override
    public List<ResourceTreeVO> getSubMenuTree(Long userId) {
        ResponseEntity<List<ResourceTreeVO>> responseEntity = null;
        try {
            responseEntity = permissionFeignClient.getSubMenuTree(userId, scibResourceId);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("getSubMenuTree|获取用户菜单树失败");
            }
            return responseEntity.getBody();
        } catch (Exception e) {
            logger.warn("getSubMenuTree|获取用户菜单树异常.", e);
            throw e;
        }
    }

    @Override
    public List<ResourceTreeVO> getSubMenuTreeWhitResourceId(Long userId, Long resourceId) {
        ResponseEntity<List<ResourceTreeVO>> responseEntity = null;
        try {
            responseEntity = permissionFeignClient.getSubMenuTree(userId, resourceId);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("getSubMenuTree|获取用户菜单树失败");
            }
            return responseEntity.getBody();
        } catch (Exception e) {
            logger.warn("getSubMenuTree|获取用户菜单树异常.", e);
            throw e;
        }
    }

    @Override
    public Map<Long, List<RoleInfoDTO>> getUserRoleById(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new HashMap<>();
        }
        ResponseEntity<Map<Long, List<RoleInfoDTO>>> responseEntity = permissionFeignClient.getUserRoleByUserIds(userIdList);
        if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || MapUtils.isEmpty(responseEntity.getBody())) {
            throw new BusinessErrorException("没有获取到连锁信息");
        }
        return responseEntity.getBody();
    }

    @Override
    public List<OrgDTO> listOrgTypeBelowOrgAssignedInScope(Long userId, Long parentOrgId, Integer orgType) {
        logger.debug("listOrgTypeBelowOrgAssignedInScope|userId:{}.parentOrgId:{}.orgType:{}.", userId, parentOrgId, orgType);
        if (Objects.isNull(userId) || Objects.isNull(parentOrgId) || Objects.isNull(orgType)) {
            return null;
        }
        QueryOrgDTO queryDTO = new QueryOrgDTO();
        queryDTO.setUserId(userId);
        queryDTO.setParentOrgId(parentOrgId);
        queryDTO.setResourceId(scibResourceId);
        queryDTO.setOrgIdType(1);
        queryDTO.setOrgType(orgType);
        ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.listOrgTypeBelowOrgAssignedInScope(queryDTO);
        if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || CollectionUtils.isEmpty(responseEntity.getBody())) {
            logger.warn("listOrgTypeBelowOrgAssignedInScope|没有获取到组织信息|");
            return null;
        }
        return responseEntity.getBody();
    }

    @Override
    public List<OrgSimpleDTO> listOrgInfoByPlate(Long userId, Long parentOrgId, Integer orgType) {
        List<OrgDTO> orgDTOList = listOrgTypeBelowOrgAssignedInScope(userId, parentOrgId, orgType);
        if (CollectionUtils.isEmpty(orgDTOList)) {
            return Lists.newArrayList();
        }
        List<OrgSimpleDTO> orgSimpleDTOList = orgDTOList.stream().filter(v -> StringUtils.isNotBlank(v.getSapcode())).map(v -> {
            OrgSimpleDTO orgSimpleDTO = new OrgSimpleDTO();
            BeanUtils.copyProperties(v, orgSimpleDTO);
            return orgSimpleDTO;
        }).collect(Collectors.toList());
        orgDTOList.clear();
        orgDTOList = null;
        return orgSimpleDTOList;
    }

    @Override
    public OrgVO getOrgInfoById(Long orgId) throws Exception {
        try {
            logger.info("调用权限根据orgId获取组织信息 param -> {}", orgId);
            ResponseEntity<OrgVO> responseEntity = permissionFeignClient.getOrgInfoById(orgId);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || null == responseEntity.getBody()) {
                throw new BusinessErrorException("没有获取到组织机构信息");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据orgId获取组织信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据orgId获取组织信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<ChildOrgsDTO> listChildOrgAssignedType(List<Long> ids, Integer type) {
        try {
            logger.info("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表 -> ids={},type={}", ids, type);
            ResponseEntity<List<ChildOrgsDTO>> responseEntity = permissionFeignClient.listChildOrgAssignedType(ids, type);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表失败:", e);
            throw new BusinessErrorException("获取权限异常");
        }
    }

    @Override
    public List<OrgDTO> listChildOrgByOrgTypeAndOrgPath(Integer orgType, String orgPath) {
        try {
            ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.listChildOrgByOrgTypeAndOrgPath(orgType, orgPath);
            if (Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody()) || responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new BusinessErrorException("根据指定组织类型与组织路径获取当前/子级组织机构异常");
            }
            return responseEntity.getBody();
        } catch (Exception e) {
            logger.error("根据指定组织类型与组织路径获取当前/子级组织机构异常", e);
            throw new BusinessErrorException("根据指定组织类型与组织路径获取当前/子级组织机构异常");
        }
    }

    @Override
    public List<OrgTreeSimpleDTO> listUserDataScopeTreesByOrgIdAndTypes(Long userId, Long orgId, List<Integer> types) {
        try {
            logger.info("开始调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤 userId:{},resourceId:{},orgIds:{},isRecursion:{}", userId, scibResourceId, orgId, JSON.toJSONString(types));
            ResponseEntity<List<OrgTreeSimpleDTO>> responseEntity = permissionFeignClient.listUserDataScopeTreesByOrgIdAndTypes(userId, scibResourceId, orgId, types);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤失败:", e);
            throw e;
        }
    }

    @Override
    public List<EmployeeInfoVO> getListByEmpCodesAndStatus(List<String> empCodes) {
        try {
            EmpCodeListAndStatusVO param = new EmpCodeListAndStatusVO();
            param.setStatusList(com.google.common.collect.Lists.newArrayList(0));
            param.setEmpCodes(empCodes);
            logger.info("开始调用权限根据用户empCodes和状态查询用户信息 empCodes -> {}", JSON.toJSONString(param));
            ResponseEntity<List<EmployeeInfoVO>> responseEntity = permissionFeignClient.getListByEmpCodesAndStatus(param);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限根据用户empCodes和状态查询用户信息失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权根据用户empCodes和状态查询用户信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据用户empCodes和状态查询用户信息失败:", e);
            throw e;
        }
    }

    /**
     * @Description 根据门店ID获取店长用户数据
     * @Param storeId
     * @Return java.util.List<com.cowell.permission.dto.EmployeeDetailDTO>
     * <AUTHOR>
     * @Date 2021/3/18 16:13
     */
    @Override
    public List<EmployeeDetailWithWxDTO> getSalesclerkMastersByStoreId(Long storeId) {
        logger.info("StockBacklogServiceImpl#getSalesclerkMastersByStoreId 入参 storeId:{}", storeId);
        // 拼装参数
        QueryEmployeeDTO queryEmployeeDTO = new QueryEmployeeDTO();
        queryEmployeeDTO.setIdType(Constants.PERMISSION_ORG_ID_TYPE_OUT_ID);
        queryEmployeeDTO.setOrgId(storeId);
        queryEmployeeDTO.setQueryScope(Constants.PERMISSION_QUERY_SCOPE_NOT);
        queryEmployeeDTO.setRoleCodes(new String[]{Constants.DZ_ROLE_CODE});
        // 根据组织ID和角色编码获取相关用户数据
        logger.info("开始执行PermissionServiceImpl#getEmployeesByOrgIdAndRole方法,queryEmployeeDTO={}", queryEmployeeDTO);
        List<EmployeeDetailWithWxDTO> employeeDetailDTOList = Lists.newArrayList();
        try {
            ResponseEntity<List<EmployeeDetailWithWxDTO>> responseEntity =  permissionFeignClient.getEmployeesByOrgIdAndRole(queryEmployeeDTO);
            if (responseEntity == null || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("调用权限根据用户empCodes和状态查询用户信息失败");
            }
            employeeDetailDTOList = responseEntity.getBody();
        } catch (Exception e) {
            logger.warn("请求异常[PermissionServiceImpl#getEmployeesByOrgIdAndRole方法queryEmployeeDTO={}]", queryEmployeeDTO, e);
        }
        employeeDetailDTOList = employeeDetailDTOList.parallelStream()
                .filter(employeeDetailDTO -> employeeDetailDTO.getUserId() != null && !org.springframework.util.StringUtils.isEmpty(employeeDetailDTO.getEmpCode()))
                .collect(Collectors.toList());
        logger.info("StockBacklogServiceImpl#getSalesclerkMastersByStoreId 出参 employeeDetailDTOList:{}", JSON.toJSONString(employeeDetailDTOList));
        return employeeDetailDTOList;
    }

    @Override
    public List<EmployeeDetailWithWxDTO> getUserNamesByEmpCodes(List<String> empCodes) {
        logger.info("StockBacklogServiceImpl#getUserNamesByEmpCodes 入参 empCodesSize:{}", empCodes.size());
        if (CollectionUtils.isEmpty(empCodes)) {
            return Lists.newArrayList();
        }
        List<EmployeeDetailWithWxDTO> employeeDetailDTOList = Lists.newArrayList();
        // 分批查询，每批200个
        List<List<String>> batches = Lists.partition(empCodes, Constants.SELECT_MAX_SIZE);
        for (List<String> batch : batches) {
            try {
                List<EmployeeDetailWithWxDTO> batchResult = permissionFeignClient.getUserNamesByEmpCodes(batch);
                if (CollectionUtils.isNotEmpty(batchResult)) {
                    employeeDetailDTOList.addAll(batchResult);
                }
            } catch (Exception e) {
                logger.warn("请求异常[PermissionServiceImpl|getUserNamesByEmpCodes|empCodes={}]", batch, e);
            }
        }
        if (CollectionUtils.isEmpty(employeeDetailDTOList)) {
            logger.warn("请求异常[PermissionServiceImpl#getUserNamesByEmpCodes]|调用权限根据用户empCodes未查询到用户信息|empCodes:{}", empCodes);
            throw new BusinessErrorException("调用权限根据用户empCodes和状态查询用户信息失败");
        }
        // 过滤有效数据
        employeeDetailDTOList = employeeDetailDTOList.parallelStream()
                .filter(employeeDetailDTO -> employeeDetailDTO.getUserId() != null
                        && !org.springframework.util.StringUtils.isEmpty(employeeDetailDTO.getEmpCode()))
                .collect(Collectors.toList());

        return employeeDetailDTOList;
    }

    @Override
    public List<EmployeeDetailDTO> getEmployeesByRole(QueryEmployeeByRoleDTO queryEmployeeByRoleDTO) {
        List<EmployeeDetailDTO> employeeDetailDTOList = null;
        try {
            employeeDetailDTOList = permissionFeignSyncClient.getEmployeesByRole(queryEmployeeByRoleDTO);
        } catch (Exception e) {
            logger.warn("请求异常[PermissionServiceImpl#getEmployeesByRole方法queryEmployeeByRoleDTO={}]", queryEmployeeByRoleDTO, e);
        }
        return employeeDetailDTOList;
    }

    /**
     * 根据角色代码查询所有员工ID
     * 方法会一直分页查询直到没有更多数据为止
     *
     * @param roleCode 角色代码
     * @return 所有符合条件的员工ID集合
     */
    public Set<String> getEmpCodeByRoleCode(String roleCode) {
        // 创建结果集合，用于存储所有员工的ID
        Set<String> empCodes = new HashSet<>();

        // 初始化分页参数
        int page = 1;
        int size = 100;
        boolean hasMoreData = true;

        // 在循环外创建查询参数对象，避免重复创建对象
        QueryEmployeeByRoleDTO queryDTO = new QueryEmployeeByRoleDTO();
        queryDTO.setRoleCode(roleCode);
        queryDTO.setSize(size);

        // 设置最大查询次数，防止无限循环
        int maxQueryCount = 1000; // 根据实际情况调整
        int queryCount = 0;

        try {
            while (hasMoreData && queryCount < maxQueryCount) {
                queryCount++;

                // 只更新页码参数
                queryDTO.setPage(page);

                // 调用原有方法获取一页员工数据
                List<EmployeeDetailDTO> employeePage = getEmployeesByRole(queryDTO);

                // 检查查询结果
                if (employeePage == null || employeePage.isEmpty()) {
                    // 如果返回为空或为空列表，表示没有更多数据了
                    hasMoreData = false;
                } else {
                    // 将本页员工的ID添加到结果集合中
                    for (EmployeeDetailDTO employee : employeePage) {
                        if (employee != null && employee.getId() != null) {
                            empCodes.add(employee.getEmpCode());
                        }
                    }

                    // 如果返回的数据量小于请求的大小，说明没有更多数据了
                    if (employeePage.size() < size) {
                        hasMoreData = false;
                    } else {
                        // 否则，查询下一页
                        page++;
                    }

                    // 主动清理本次查询结果，帮助GC回收内存
                    employeePage.clear();
                }

                // 如果结果集合太大，可以考虑使用外部存储或分批处理
                if (empCodes.size() > 100000) { // 阈值可根据实际情况调整
                    logger.warn("查询到的员工数量过多，建议优化查询条件或使用分批处理");
                }
            }

            if (queryCount >= maxQueryCount) {
                logger.warn("查询次数达到上限({})，可能未获取全部数据", maxQueryCount);
            }

        } catch (Exception e) {
            logger.error("查询员工数据时发生一次: {}", e.getMessage(), e);
        }

        return empCodes;
    }


}
