package com.cowell.scib.service.impl;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.ConfigTypeEnum;
import com.cowell.scib.enums.DicApiEnum;
import com.cowell.scib.enums.DictAliasEnum;
import com.cowell.scib.mapperDgms.CommonEnumsMapper;
import com.cowell.scib.mapperDgms.NecessaryLevelRoleConfigMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.BundlTaskService;
import com.cowell.scib.service.CommonService;
import com.cowell.scib.service.TagService;
import com.cowell.scib.service.dto.DropBoxParam;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.StoreComponentQueryParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.ScibCommonResultDTO;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CommonServiceImpl implements CommonService {

    private final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Resource
    private CommonEnumsMapper commonEnumsMapper;
    @Resource
    private NecessaryLevelRoleConfigMapper necessaryLevelRoleConfigMapper;
    @Autowired
    private BundlTaskService bundlTaskService;
    @Autowired
    private TagService tagService;

    @Override
    public List<OptionDto> dropBox(DropBoxParam param, TokenUserDTO userDTO) {
        try {
            if (null == param.getType()) {
                logger.info("下拉类型为空");
                return new ArrayList<>();
            }
            switch (param.getType()) {
                case 1:
                    return getStoreType(param.getStoreType());
                case 2:
                    return getNecessaryTag(userDTO);
                case 3:
                    return getChooseReason(userDTO);
                case 4:
                    return getEnumByProp(param.getPropertyCode());
                case 5:
                    return getCityListByCompanyOrgId(param.getCompanyOrgId());
                default:
                    return new ArrayList<>();
            }
        } catch (Exception e) {
            logger.error("获取通用下拉框失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    private List<OptionDto> getCityListByCompanyOrgId(Long companyOrgId) {
        List<OrgInfoBaseCache> storeList = CacheVar.getStoreListByBusinessOrgId(companyOrgId);
        if (CollectionUtils.isEmpty(storeList)) {
            return new ArrayList<>();
        }
        return storeList.stream().map(v -> CacheVar.getStoreExtInfoByStoreOrgId(v.getId())).filter(Optional::isPresent).map(v -> v.get().getCity()).filter(StringUtils::isNotBlank).distinct().map(v -> new OptionDto(v, v)).collect(Collectors.toList());
    }

    private List<OptionDto> getEnumByProp(String propertyCode) {
        if (StringUtils.isBlank(propertyCode)) {
            return new ArrayList<>();
        }
        CommonEnumsExample example = new CommonEnumsExample();
        example.createCriteria().andPropertyCodeEqualTo(propertyCode).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
        return commonEnumsMapper.selectByExample(example).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList());
    }


    private List<OptionDto> getChooseReason(TokenUserDTO userDTO) {
        CommonEnumsExample example = new CommonEnumsExample();
        example.createCriteria().andPropertyCodeEqualTo("necessaryReasonList").andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
        return commonEnumsMapper.selectByExample(example).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList());
    }

    private List<OptionDto> getNecessaryTag(TokenUserDTO userDTO) {
        if (CollectionUtils.isEmpty(userDTO.getRoles())) {
            return new ArrayList<>();
        }
//        List<NecessaryLevelRoleConfig> roleConfigs = necessaryLevelRoleConfigMapper.selectByExample(null);
//        if (CollectionUtils.isEmpty(roleConfigs)) {
//            return new ArrayList<>();
//        }
//        return roleConfigs.stream().filter(v -> StringUtils.isNotBlank(v.getRoles()))
//                .filter(v -> Arrays.stream(StringUtils.split(v.getRoles(), ",")).filter(r -> userDTO.getRoles().contains(r)).findAny().isPresent())
//                .map(v -> new OptionDto(v.getNecessaryTagName(), v.getNecessaryTag())).distinct().collect(Collectors.toList());
        CommonEnumsExample example = new CommonEnumsExample();
        example.createCriteria().andPropertyCodeEqualTo("necessaryTag").andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
        return commonEnumsMapper.selectByExample(example).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList());
    }

    private List<OptionDto> getStoreType(String storeType) {
        CommonEnumsExample example = new CommonEnumsExample();
        example.createCriteria().andPropertyCodeEqualTo(storeType).andIsDeleteEqualTo(Integer.valueOf(Constants.NORMAL_STATUS));
        return commonEnumsMapper.selectByExample(example).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList());
    }

    @Override
    public ScibCommonResultDTO getStoreIdListByStoreSelectorAndConfig(Long orgId, String scopeCode, String configType) {
        ScibCommonResultDTO resultDTO = new ScibCommonResultDTO();
        String actionByCode = DicApiEnum.getActionByCode(scopeCode);
        String messageByType = ConfigTypeEnum.getMessageByType(Byte.parseByte(configType));
        if (Objects.nonNull(orgId) && StringUtils.isNotBlank(actionByCode) && StringUtils.isNotBlank(messageByType)) {
            if (DicApiEnum.BLKC_4_3.getCode().equals(scopeCode)) {
                RuleParam ruleParam1 = new RuleParam();
                ruleParam1.setOrgId(orgId);
                ruleParam1.setScopeCode(DicApiEnum.BLKC_1.getCode());
                ruleParam1.setConfigType(configType);
                Map<String, List<String>> selectParamMap1 = bundlTaskService.buildSelectParam(ruleParam1, null);
                if (!MapUtils.isEmpty(selectParamMap1)) {
                    RuleParam ruleParam = new RuleParam();
                    ruleParam.setOrgId(orgId);
                    ruleParam.setScopeCode(scopeCode);
                    ruleParam.setConfigType(configType);
                    Map<String, List<String>> selectParamMap = bundlTaskService.buildSelectParam(ruleParam, null);
                    if (MapUtils.isEmpty(selectParamMap)) {
                        logger.warn("selectMdmStoreFilterSelector|不经营品参数为空");
                    } else {
                        selectParamMap1.putAll(selectParamMap);
                    }
                    StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
                    queryParam.setScopeCode(String.valueOf(orgId));
                    queryParam.setManagstate(selectParamMap.get(DictAliasEnum.MANAGSTATE.getAlias()));
                    queryParam.setStorestatus(selectParamMap.get(DictAliasEnum.STORESTATUS.getAlias()));
                    queryParam.setFormat(selectParamMap.get(DictAliasEnum.FORMAT.getAlias()));
                    queryParam.setStoretype(selectParamMap.get(DictAliasEnum.STORETYPE.getAlias()));
                    queryParam.setOperationtype(selectParamMap.get(DictAliasEnum.OPERATIONTYPE.getAlias()));
                    queryParam.setSpecialtype(selectParamMap.get(DictAliasEnum.SPECIALTYPE.getAlias()));
                    queryParam.setExcludeOrgIds(selectParamMap.get(DictAliasEnum.EXCLUDEORGIDS.getAlias()));
                    queryParam.setStoreattr(selectParamMap.get(DictAliasEnum.STOREATTR.getAlias()));
                    queryParam.setOrgIds(selectParamMap.get(DictAliasEnum.ORGIDS.getAlias()));
                    List<Long> selectStoreIdListByType = tagService.getSelectStoreIdListByType(queryParam);
                    resultDTO.setStoreIdList(selectStoreIdListByType);
                    resultDTO.setQueryParam(queryParam);
                    return resultDTO;
                }

            } else {
                RuleParam ruleParam = new RuleParam();
                ruleParam.setOrgId(orgId);
                ruleParam.setScopeCode(scopeCode);
                ruleParam.setConfigType(configType);
                Map<String, List<String>> selectParamMap = bundlTaskService.buildSelectParam(ruleParam, null);
                if (!MapUtils.isEmpty(selectParamMap)) {
                    StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
                    queryParam.setScopeCode(String.valueOf(orgId));
                    queryParam.setManagstate(selectParamMap.get(DictAliasEnum.MANAGSTATE.getAlias()));
                    queryParam.setStorestatus(selectParamMap.get(DictAliasEnum.STORESTATUS.getAlias()));
                    queryParam.setFormat(selectParamMap.get(DictAliasEnum.FORMAT.getAlias()));
                    queryParam.setStoretype(selectParamMap.get(DictAliasEnum.STORETYPE.getAlias()));
                    queryParam.setOperationtype(selectParamMap.get(DictAliasEnum.OPERATIONTYPE.getAlias()));
                    queryParam.setSpecialtype(selectParamMap.get(DictAliasEnum.SPECIALTYPE.getAlias()));
                    queryParam.setExcludeOrgIds(selectParamMap.get(DictAliasEnum.EXCLUDEORGIDS.getAlias()));
                    queryParam.setStoreattr(selectParamMap.get(DictAliasEnum.STOREATTR.getAlias()));
                    queryParam.setOrgIds(selectParamMap.get(DictAliasEnum.ORGIDS.getAlias()));
                    List<Long> selectStoreIdListByType = tagService.getSelectStoreIdListByType(queryParam);
                    resultDTO.setStoreIdList(selectStoreIdListByType);
                    resultDTO.setQueryParam(queryParam);
                    return resultDTO;
                }
            }
        }
        resultDTO.setStoreIdList(new ArrayList<>());
        return resultDTO;
    }
}
