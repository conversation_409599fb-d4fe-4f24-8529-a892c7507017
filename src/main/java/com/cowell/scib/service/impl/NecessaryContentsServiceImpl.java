package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.framework.utils.IdUtils;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.config.IdGenConfig;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreContentsAssemble;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreContentsFactory;
import com.cowell.scib.service.dto.necessaryContents.*;
import com.cowell.scib.service.feign.NyuwaFeignClient;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.service.vo.StoreGoodsVo;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.SelectorResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.HutoolUtil;
import com.cowell.scib.utils.MBUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class NecessaryContentsServiceImpl implements NecessaryContentsService {

    private final Logger logger = LoggerFactory.getLogger(NecessaryContentsService.class);

    @Autowired
    private NecessaryGroupGoodsMapper necessaryGroupGoodsMapper;

    @Autowired
    private NecessaryGroupGoodsExtendMapper necessaryGroupGoodsExtendMapper;

    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtMapper;

    @Autowired
    private NecessaryPlatformGoodsMapper necessaryPlatformGoodsMapper;

    @Autowired
    private NecessaryPlatformGoodsExtendMapper necessaryPlatformGoodsExtendMapper;

    @Autowired
    private NecessaryCompanyGoodsMapper necessaryCompanyGoodsMapper;

    @Autowired
    private NecessaryCompanyGoodsExtendMapper necessaryCompanyGoodsExtendMapper;

    @Autowired
    private NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;

    @Autowired
    private NecessaryStoreTypeGoodsExtendMapper necessaryStoreTypeGoodsExtendMapper;

    @Autowired
    private NecessaryChooseStoreTypeGoodsMapper necessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private NecessaryChooseStoreTypeGoodsExtendMapper necessaryChooseStoreTypeGoodsExtendMapper;

    @Autowired
    private NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;

    @Autowired
    private NecessarySingleStoreGoodsExtendMapper necessarySingleStoreGoodsExtendMapper;

    @Autowired
    private StoreGoodsInfoMapper storeGoodsInfoMapper;

    @Autowired
    private StoreGoodsInfoExtendMapper storeGoodsInfoExtendMapper;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private MdmTaskExtendMapper mdmTaskExtendMapper;

    @Autowired
    private MdmTaskDetailMapper mdmTaskDetailMapper;

    @Autowired
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ForestService forestService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NecessaryAssembleFactory factory;

    @Autowired
    private StoreContentsFactory storeContentsFactory;

    @Autowired
    private AsyncExportFileService asyncExportFileService;

    @Autowired
    private MBUtils mbUtils;

    @Value("${dgms.mb.necessary.content.properties:}")
    private String necessaryMbPrpo;

    @Value("${dgms.necessary.add.goods.max:50}")
    private Integer addGoodsMax;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private BundlTaskService bundlTaskService;

    @Autowired
    private TocService tocService;

    @Autowired
    private IscmService iscmService;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;

    @Autowired
    private MdmPushStoreGoodsErrorRecordExtendMapper mdmPushStoreGoodsErrorRecordExtendMapper;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;

    @Autowired
    private NyuwaFeignClient nyuwaFeignClient;
    @Autowired
    private StoreGoodsContentsMapper storeGoodsContentsMapper;

    @Autowired
    @Qualifier("trackResultTaskExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    private static final String BUSINESS_SCOPE_CACHE = "BUSINESS-SCOPE-CACHE-";

    private static final String BATCH_IMPORT_CACHE = "BATCH-IMPORT-CACHE-";

    @Override
    public PageResult<NecessaryCommonDTO> getContents(TokenUserDTO userDTO, NecessaryQueryParam param) {
        try {
            TreeMap<Long, List<NecessaryCommonDTO>> treeMap = getNecessaryCommonDTO(param, userDTO);
            Map.Entry<Long, List<NecessaryCommonDTO>> firstEntry = treeMap.firstEntry();
            return new PageResult<>(firstEntry.getKey(), firstEntry.getValue());
        } catch(Exception e){
            logger.error("查询必备目录异常", e);
            throw e;
        }
    }

    private TreeMap<Long, List<NecessaryCommonDTO>> getNecessaryCommonDTO(NecessaryQueryParam param, TokenUserDTO userDTO) {
        RuleParam ruleParam = new RuleParam();
        // 写死  查店型用
        ruleParam.setScopeCode("TaskCreate");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
        // 平台必备店型
        Map<String, String> platStoreGroup = Optional.ofNullable(ruleEnum.get("PlatStoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 中参必备店型
        Map<String, String> zsStoreGroup = Optional.ofNullable(ruleEnum.get("ZsStoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 配方必备店型
        Map<String, String> pfStoreGroup = Optional.ofNullable(ruleEnum.get("PfStoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 组货必备店型
        Map<String, String> storeGroup = Optional.ofNullable(ruleEnum.get("StoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));

        List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), 3L, Lists.newArrayList(OrgTypeEnum.PLATFORM.getCode()));
        if (CollectionUtils.isEmpty(orgTreeSimpleDTOS)) {
            throw new AmisBadRequestException("您没有平台权限,请联系管理员");
        }
        List<OrgTreeSimpleDTO> platforms = orgTreeSimpleDTOS.get(0).getChildren();
        if (CollectionUtils.isEmpty(platforms)) {
            throw new AmisBadRequestException("您没有平台权限,请联系管理员");
        }
        List<Long> platformIds = platforms.stream().map(OrgTreeSimpleDTO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(param.getPlatformOrgId())) {
            List<Long> fullScopeByOrgId = isFullScopeByOrgId(param.getPlatformOrgId(), userDTO.getUserId());
            List<Long> noPermissionPlatformOrgIds = param.getPlatformOrgId().stream().filter(v -> !fullScopeByOrgId.contains(v)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noPermissionPlatformOrgIds)) {
                throw new AmisBadRequestException("您没有所选平台权限,请联系管理员");
            }
            platformIds.clear();
            platformIds.addAll(param.getPlatformOrgId());
        }
        param.setQueryPlatformOrgIdList(platformIds);
        Map<String, List<OptionDto>> ruleEnumList = ruleService.getRuleEnumList(ruleParam, userDTO);
        NecessaryTagEnum queryTagEnum = NecessaryTagEnum.getEnumByCode(param.getQueryTag());
        if (StringUtils.isNotBlank(param.getGoodsNos())) {
            param.setGoodsNoList(Arrays.stream(StringUtils.split(param.getGoodsNos(), ",")).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(param.getGoodsName())) {
            param.setGoodsName(Constants.QUERY_LIKE + param.getGoodsName() + Constants.QUERY_LIKE);
        }
        Long count = 0L;
        TreeMap<Long ,List<NecessaryCommonDTO>> resultMap = new TreeMap<>();
        resultMap.put(count, new ArrayList<>());
        Integer start = null;
        Integer pageSize = null;
        switch (queryTagEnum) {
            case GROUP_NECESSARY:
                if (param.isToPage()) {
                    count = necessaryGroupGoodsExtendMapper.countNecessary(param);
                    if (count <= 0L) {
                        return resultMap;
                    }
                    start = (param.getPage() - 1) * param.getPerPage();
                    pageSize = param.getPerPage();
                }
                resultMap.clear();
                resultMap.put(count, necessaryGroupGoodsExtendMapper.queryNecessary(param, start, pageSize)
                        .stream().map(v -> {
                            v.setNecessaryTagDesc(NecessaryTagEnum.getMessageByCode(v.getNecessaryTag()));
                            v.setGmtCreateStr(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            v.setGmtUpdateStr(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            return v;
                        }).collect(Collectors.toList()));
                return resultMap;
            case PLATFORM_NECESSARY:
                if (param.isToPage()) {
                    count = necessaryPlatformGoodsExtendMapper.countNecessary(param);
                    if (count <= 0L) {
                        return resultMap;
                    }
                    start = (param.getPage() - 1) * param.getPerPage();
                    pageSize = param.getPerPage();
                }
                resultMap.clear();
                resultMap.put(count, necessaryPlatformGoodsExtendMapper.queryNecessary(param, start, pageSize)
                        .stream().map(v -> {
                            String platStoreType = platStoreGroup.get(v.getStoreType());
                            if (StringUtils.isNotBlank(platStoreType)) {
                                v.setStoreTypeDesc(platStoreType);
                            } else {
                                String pfStoreType = pfStoreGroup.get(v.getStoreType());
                                if (StringUtils.isNotBlank(pfStoreType)) {
                                    v.setStoreTypeDesc(pfStoreType);
                                } else {
                                    v.setStoreTypeDesc(zsStoreGroup.get(v.getStoreType()));
                                }
                            }
                            v.setGmtCreateStr(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            v.setGmtUpdateStr(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            return v;
                        }).collect(Collectors.toList()));
                return resultMap;
                case COMPANY_NECESSARY:
                    if (param.isToPage()) {
                        count = necessaryCompanyGoodsExtendMapper.countNecessary(param);
                        if (count <= 0L) {
                            return resultMap;
                        }
                        start = (param.getPage() - 1) * param.getPerPage();
                        pageSize = param.getPerPage();
                    }
                    resultMap.clear();
                    resultMap.put(count, necessaryCompanyGoodsExtendMapper.queryNecessary(param, start, pageSize)
                            .stream().map(v -> {
                                v.setGmtCreateStr(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                                v.setGmtUpdateStr(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                                return v;
                            }).collect(Collectors.toList()));
                    return resultMap;
            case STORE_TYPE_NECESSARY:
                if (param.isToPage()) {
                    count = necessaryStoreTypeGoodsExtendMapper.countNecessary(param);
                    if (count <= 0L) {
                        return resultMap;
                    }
                    start = (param.getPage() - 1) * param.getPerPage();
                    pageSize = param.getPerPage();
                }
                resultMap.clear();
                resultMap.put(count, necessaryStoreTypeGoodsExtendMapper.queryNecessary(param, start, pageSize)
                        .stream().map(v -> {
                            String storeType = storeGroup.get(v.getStoreType());
                            if (StringUtils.isNotBlank(storeType)) {
                                v.setStoreTypeDesc(storeType);
                            } else {
                                String pfStoreType = pfStoreGroup.get(v.getStoreType());
                                if (StringUtils.isNotBlank(pfStoreType)) {
                                    v.setStoreTypeDesc(pfStoreType);
                                } else {
                                    v.setStoreTypeDesc(zsStoreGroup.get(v.getStoreType()));
                                }
                            }
                            v.setGmtCreateStr(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            v.setGmtUpdateStr(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            return v;
                        }).collect(Collectors.toList()));
                return resultMap;
            case STORE_CHOOSE_NECESSARY:
                if (param.isToPage()) {
                    count = necessaryChooseStoreTypeGoodsExtendMapper.countNecessary(param);
                    if (count <= 0L) {
                        return resultMap;
                    }
                    start = (param.getPage() - 1) * param.getPerPage();
                    pageSize = param.getPerPage();
                }
                resultMap.clear();
                resultMap.put(count, necessaryChooseStoreTypeGoodsExtendMapper.queryNecessary(param, start, pageSize)
                        .stream().map(v -> {
                            String storeType = storeGroup.get(v.getStoreType());
                            if (StringUtils.isNotBlank(storeType)) {
                                v.setStoreTypeDesc(storeType);
                            } else {
                                String pfStoreType = pfStoreGroup.get(v.getStoreType());
                                if (StringUtils.isNotBlank(pfStoreType)) {
                                    v.setStoreTypeDesc(pfStoreType);
                                } else {
                                    v.setStoreTypeDesc(zsStoreGroup.get(v.getStoreType()));
                                }
                            }
                            v.setGmtCreateStr(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            v.setGmtUpdateStr(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            return v;
                        }).collect(Collectors.toList()));
                return resultMap;
            case SINGLE_STORE_NECESSARY:
                if (param.isToPage()) {
                    count = necessarySingleStoreGoodsExtendMapper.countNecessary(param);
                    if (count <= 0L) {
                        return resultMap;
                    }
                    start = (param.getPage() - 1) * param.getPerPage();
                    pageSize = param.getPerPage();
                }
                resultMap.clear();
                resultMap.put(count, necessarySingleStoreGoodsExtendMapper.queryNecessary(param, start, pageSize)
                        .stream().map(v -> {
                            String storeType = storeGroup.get(v.getStoreType());
                            if (StringUtils.isNotBlank(storeType)) {
                                v.setStoreTypeDesc(storeType);
                            } else {
                                String pfStoreType = pfStoreGroup.get(v.getStoreType());
                                if (StringUtils.isNotBlank(pfStoreType)) {
                                    v.setStoreTypeDesc(pfStoreType);
                                }
                                v.setStoreTypeDesc(zsStoreGroup.get(v.getStoreType()));
                            }
                            v.setGmtCreateStr(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            v.setGmtUpdateStr(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            return v;
                        }).collect(Collectors.toList()));
                return resultMap;
            default:
                throw new AmisBadRequestException("非法的查询类型");
        }
    }

    @Override
    public String addNecessaryGoods(TokenUserDTO userDTO, NecessaryAddParam param){
        try {
            if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                throw new AmisBadRequestException("请选择必备目录的商品");
            }
            param.setGoodsNos(param.getGoodsNos().stream().distinct().collect(Collectors.toList()));
            if (Boolean.TRUE.equals(param.getCheckGoodsQty())) {
                if (param.getGoodsNos().size() > addGoodsMax) {
                    throw new AmisBadRequestException("一次操作最多添加" + addGoodsMax + "条商品");
                }
            }
            OrgInfoBaseCache platform = CacheVar.getPlatformByOrgId(param.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息"));
            if (!checkModifyAble(platform.getPlatformOrgId())){
                throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
            }
            NecessaryAssemble necessaryAssemble = factory.getNecessaryAssemble(param, userDTO, platform);
            String checkMsg = necessaryAssemble.checkFullScope().check();
            if (StringUtils.isNotBlank(checkMsg) && !param.getExistsIgnore()) {
                return checkMsg;
            }
            return necessaryAssemble.assemble().getMessage();
        } catch(Exception e){
            logger.error("添加集团必备目录异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void modifyNecessaryGroup(TokenUserDTO userDTO, NecessaryAddParam param){
        try {
            if (CollectionUtils.isEmpty(param.getNecessaryIds())) {
                throw new AmisBadRequestException("请选择需要启用/停用的数据");
            }
            if (Objects.isNull(param.getUseMark())) {
                throw new AmisBadRequestException("请选择需要启用/停用的方式");
            }
            NecessaryTagEnum tagEnum = param.getUseMark() ? NecessaryTagEnum.NONE_NECESSARY : NecessaryTagEnum.GROUP_NECESSARY;
            NecessaryGroupGoodsExample example = new NecessaryGroupGoodsExample();
            example.createCriteria().andIdIn(param.getNecessaryIds()).andNecessaryTagEqualTo(tagEnum.getCode());
            long count = necessaryGroupGoodsMapper.countByExample(example);
            if (count <= 0L) {
                throw new AmisBadRequestException("没有需要启用停用的数据");
            }
            List<NecessaryGroupGoods> groupGoods = necessaryGroupGoodsMapper.selectByExample(example);
            Map<Long, List<NecessaryGroupGoods>> platformGoodsMap = groupGoods.stream().collect(Collectors.groupingBy(NecessaryGroupGoods::getPlatformOrgId));
            for (Map.Entry<Long, List<NecessaryGroupGoods>> entry : platformGoodsMap.entrySet()) {
                if (!checkModifyAble(entry.getKey())){
                    throw new AmisBadRequestException("当前平台有任务正在处理,请稍后再试");
                }
                List<String> goodsNos = entry.getValue().stream().map(NecessaryGroupGoods::getGoodsNo).distinct().collect(Collectors.toList());
                List<Long> storeIds = getPlatformStoreInfo(entry.getKey(), null, userDTO.getUserId()).stream().map(MdmStoreExDTO::getStoreId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(storeIds)) {
                    throw new AmisBadRequestException("平台下没有门店");
                }
                param.setPlatformOrgId(entry.getKey());
                param.setNecessaryTag(NecessaryTagEnum.GROUP_NECESSARY.getCode());
                param.setGoodsNos(goodsNos);
                if (param.getUseMark()) {
                    NecessaryAssemble necessaryAssemble = factory.getNecessaryAssemble(param, userDTO, CacheVar.getPlatformByOrgId(param.getPlatformOrgId()).orElseThrow(() -> new AmisBadRequestException("没有获取到平台信息")));
                    //②查平台必备表：平台、SKU，存在则删除N行。
                    NecessaryPlatformGoodsExample platformGoodsExample = new NecessaryPlatformGoodsExample();
                    platformGoodsExample.createCriteria().andPlatformOrgIdEqualTo(entry.getKey()).andGoodsNoIn(param.getGoodsNos());
                    necessaryPlatformGoodsMapper.deleteByExample(platformGoodsExample);
                    //③查企业必备表：平台、SKU，存在则删除N行。
                    NecessaryCompanyGoodsExample companyGoodsExample = new NecessaryCompanyGoodsExample();
                    companyGoodsExample.createCriteria().andPlatformOrgIdEqualTo(entry.getKey()).andGoodsNoIn(param.getGoodsNos());
                    necessaryCompanyGoodsMapper.deleteByExample(companyGoodsExample);
                    //④查店型必备表：平台、SKU，存在则删除N行。
                    NecessaryStoreTypeGoodsExample storeTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
                    storeTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(entry.getKey()).andGoodsNoIn(param.getGoodsNos());
                    necessaryStoreTypeGoodsMapper.deleteByExample(storeTypeGoodsExample);
                    //⑤查店型选配表：平台、SKU，存在则删除N行。
                    NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
                    chooseStoreTypeGoodsExample.createCriteria().andPlatformOrgIdEqualTo(entry.getKey()).andGoodsNoIn(param.getGoodsNos());
                    necessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);
                    //⑥查单店必备表：平台、SKU，存在则删除N行。
                    NecessarySingleStoreGoodsExample singleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
                    singleStoreGoodsExample.createCriteria().andPlatformOrgIdEqualTo(entry.getKey()).andGoodsNoIn(param.getGoodsNos());
                    necessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);
                    necessaryAssemble.addStoreGoods(storeIds, entry.getValue().stream().map(v -> {
                        NecessaryCommonGoodsDTO commonGoodsDTO = new NecessaryCommonGoodsDTO();
                        BeanUtils.copyProperties(v, commonGoodsDTO);
                        return commonGoodsDTO;
                    }).collect(Collectors.toMap(NecessaryCommonGoodsDTO::getGoodsNo, Function.identity(), (k1,k2) -> k1)));
                } else {
                    // 删除
                    List<NecessaryGroupGoods> value = entry.getValue();
                    NecessaryDelParam delParam = new NecessaryDelParam();
                    delParam.setNecessaryIds(entry.getValue().stream().map(NecessaryGroupGoods::getId).collect(Collectors.toList()));
                    delParam.setNecessaryTag(NecessaryTagEnum.GROUP_NECESSARY.getCode());
                    delParam.setPlatformOrgId(entry.getKey());
                    factory.getDelNecessaryAssemble(delParam, userDTO).deleteNecessary();
                }
            }
            NecessaryGroupGoods update = new NecessaryGroupGoods();
            update.setUpdatedBy(userDTO.getUserId());
            update.setUpdatedName(userDTO.getName());
            update.setNecessaryTag(param.getUseMark() ? NecessaryTagEnum.GROUP_NECESSARY.getCode() : NecessaryTagEnum.NONE_NECESSARY.getCode());
            necessaryGroupGoodsMapper.updateByExampleSelective(update, example);
        } catch (Exception e) {
            logger.error("启用停用失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public String delNecessaryGoods(TokenUserDTO userDTO, NecessaryDelParam param) {
        try {
            if (CollectionUtils.isEmpty(param.getNecessaryIds())) {
                throw new AmisBadRequestException("请选择需要删除的数据");
            }
            if (Objects.isNull(param.getPlatformOrgId())) {
                throw new AmisBadRequestException("请选择需要删除的平台");
            }
            return factory.getDelNecessaryAssemble(param, userDTO).deleteNecessary();
        } catch (Exception e) {
            logger.error("删除失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public String listGroupGoods(Long platFormOrgId, Long taskId){
        logger.debug("listGroupGoods|platFormOrgId:{}.taskId:{}.", platFormOrgId, taskId);
        if(Objects.isNull(platFormOrgId)){
            return "";
        }

        List<NecessaryCategoryCommonDTO> goodsCatagoryList = necessaryGroupGoodsExtendMapper.selectExistsGoodsByPlat(platFormOrgId, NecessaryTagEnum.GROUP_NECESSARY.getCode());
        if(CollectionUtils.isEmpty(goodsCatagoryList)){
            return "";
        }
        List<String> goodsTypeCodeList = bundlingTaskDetailExtMapper.selectDictValueListByCode(taskId, Lists.newArrayList(BudnlTaskDetailDicEnum.GOODS.getCode()));
        Map<String, Boolean> goodsTypeCodeMap = new HashMap<>(goodsTypeCodeList.size());
        goodsTypeCodeList.forEach(v->{
            goodsTypeCodeMap.put(v, true);
        });

        List<String> goodsNoList = Lists.newArrayList();
        for(NecessaryCategoryCommonDTO categoryCommonDTO : goodsCatagoryList){
            String subCatagroyId = String.valueOf(categoryCommonDTO.getSubCategoryId());
            String big = subCatagroyId.substring(0,2);
            String middle = subCatagroyId.substring(0,4);
            if((Objects.nonNull(goodsTypeCodeMap.get(big)) && goodsTypeCodeMap.get(big)) || (Objects.nonNull(goodsTypeCodeMap.get(middle)) && goodsTypeCodeMap.get(middle))){
                goodsNoList.add(categoryCommonDTO.getGoodsNo());
            }
        }
        goodsNoList = goodsNoList.stream().distinct().collect(Collectors.toList());

        return StringUtils.join(goodsNoList, ",");
    }

    @Override
    public List<Map<String, String>> listPlatformGoods(Long platFormOrgId, Long taskId, List<TaskStoreDTO> taskStoreDTOList){
        logger.debug("listPlatformGoods|platFormOrgId:{}.taskId:{}.", platFormOrgId, taskId);
        if(Objects.isNull(platFormOrgId) || Objects.isNull(taskId)){
            return Collections.emptyList();
        }

        List<String> goodsTypeCodeList = bundlingTaskDetailExtMapper.selectDictValueListByCode(taskId, Lists.newArrayList(BudnlTaskDetailDicEnum.GOODS.getCode()));
        Map<String, Boolean> goodsTypeCodeMap = new HashMap<>(goodsTypeCodeList.size());
        goodsTypeCodeList.forEach(v->{
            goodsTypeCodeMap.put(v, true);
        });

        List<String> storeTypeList = Lists.newArrayList();
        if (Constants.GOOD_FIRST.stream().filter(v->goodsTypeCodeList.contains(v)).count() > 0L) {
            storeTypeList.addAll(taskStoreDTOList.stream().filter(v-> StringUtils.isNotBlank(v.getPlateStore())).map(v->v.getPlateStore()).distinct().collect(Collectors.toList()));
        }
        if (goodsTypeCodeList.contains(Constants.GOOD_SECOND)) {
            //中参
            storeTypeList.addAll(taskStoreDTOList.stream().filter(v-> StringUtils.isNotBlank(v.getZsShop())).map(v->v.getZsShop()).distinct().collect(Collectors.toList()));
        }

        List<NecessaryTaskGoodsCommonDTO> goodsAndStoreTypeList = necessaryPlatformGoodsExtendMapper.selectExistsGoodsAndStoreType(platFormOrgId, storeTypeList, null);
        if(CollectionUtils.isEmpty(goodsAndStoreTypeList)){
            return Collections.emptyList();
        }
        List<Map<String, String>> platformGoods = Lists.newArrayList();
        Map<String, List<NecessaryTaskGoodsCommonDTO>> goodsAndStoreTypeMap = goodsAndStoreTypeList.stream().filter(v->Objects.nonNull(v.getStoreType())).collect(Collectors.groupingBy(v->v.getStoreType()));
        goodsAndStoreTypeMap.forEach((k,list)->{
            List<String> goodsNoList = list.stream().filter(v->StringUtils.isNotBlank(v.getGoodsNo())).map(v->v.getGoodsNo()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(goodsNoList)){
                Map<String, String> platformGoodsMap = new HashMap<>();
                platformGoodsMap.put(k, StringUtils.join(goodsNoList, ","));
                platformGoods.add(platformGoodsMap);
            }
        });
        return platformGoods;
    }

    @Override
    public List<Map<String, List<Map<String, String>>>> listCompanyGoods(Long platFormOrgId, List<String> companyOrgIdList){
        logger.debug("listCompanyGoods|platFormOrgId:{}.companyOrgIdList:{}.", platFormOrgId, companyOrgIdList);
        if(Objects.isNull(platFormOrgId) || CollectionUtils.isEmpty(companyOrgIdList)){
            return Collections.emptyList();
        }
        List<NecessaryTaskGoodsCommonDTO> taskGoodsCommonDTOList = necessaryCompanyGoodsExtendMapper.selectExistsGoodsAndCity(platFormOrgId, companyOrgIdList.stream().filter(v->StringUtils.isNotBlank(v)).distinct().map(v->Long.parseLong(v)).collect(Collectors.toList()), null);
        if(CollectionUtils.isEmpty(taskGoodsCommonDTOList)){
            return Collections.emptyList();
        }
        Map<Long, List<NecessaryTaskGoodsCommonDTO>> taskGoodsCompanyMap = taskGoodsCommonDTOList.stream().filter(v->StringUtils.isNotBlank(v.getGoodsNo())).collect(Collectors.groupingBy(NecessaryTaskGoodsCommonDTO::getCompanyOrgId));
        List<Map<String, List<Map<String, String>>>> companyMapList = new ArrayList<>(taskGoodsCompanyMap.size());
        taskGoodsCompanyMap.forEach((orgId, list)->{
            Map<String, List<NecessaryTaskGoodsCommonDTO>> taskGoodsCityMap = list.stream().filter(v->StringUtils.isNotBlank(v.getGoodsNo())).collect(Collectors.groupingBy(v->v.getCity()));
            List<Map<String, String>> cityMapList = new ArrayList<>(taskGoodsCityMap.size());
            taskGoodsCityMap.forEach((city, goodsList)->{
                Map<String, String> cityMap = new HashMap<>();
                cityMap.put(city, StringUtils.join(goodsList.stream().map(v->v.getGoodsNo()).collect(Collectors.toList()), ","));
                cityMapList.add(cityMap);
            });
            Map<String, List<Map<String, String>>> companyMap = new HashMap<>();
            companyMap.put(String.valueOf(orgId), cityMapList);
            companyMapList.add(companyMap);
        });
        return companyMapList;
    }

    @Override
    public List<MdmStoreExDTO> getPlatformStoreInfo(Long platformOrgId, List<Long> companyOrgIds, Long userId) {
        List<Long> selectorStoreIdList = bundlTaskService.selectMdmStoreIdFilterSelector(platformOrgId, userId, companyOrgIds);
        return selectorStoreIdList.stream().map(v -> CacheVar.getStoreExtInfoByStoreId(v)).filter(v -> v.isPresent()).map(Optional::get).collect(Collectors.toList());
    }

    @Override
    public List<Long> getStoreGoodsIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_STORE_GOODS_INFO, count);
    }

    @Override
    public List<Long> getMdmTaskDetailIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_MDM_TASK_DETAIL, count);
    }

    @Override
    public List<Long> getTrackResultAllDetailIds(Integer count) {
        return tocService.getDistributedIDList(IdGenConfig.IDGEN_TRACK_RETULT_ALL_DETAIL, count);
    }

    @Override
    public void pushMdm(List<MdmTaskDetail> taskDetails) throws Exception {
        if (CollectionUtils.isEmpty(taskDetails)) {
            return;
        }
        MdmTask mdmTask = mdmTaskMapper.selectByPrimaryKey(taskDetails.get(0).getTaskId());
        if (null == mdmTask) {
            logger.info("下发海典失败,没有查询到主单, taskId:{}",taskDetails.get(0).getTaskId());
            return;
        }
        List<NecessaryTaskMbDTO> mbDTOS = taskDetails.stream().map(v -> {
            NecessaryTaskMbDTO mbDTO = new NecessaryTaskMbDTO();
            mbDTO.setStoreCode(v.getStoreCode());
            mbDTO.setGoodsNo(v.getGoodsNo());
//            mbDTO.setNecessaryTag(NecessaryTagEnum.getMessageByCode(v.getNecessaryTag()));
            mbDTO.setMinDisplayQuantity(v.getMinDisplayQuantity());
            mbDTO.setUpdateTime(DateUtils.conventDateStrByPattern(new Date(), DateUtils.DATETIME_PATTERN));
            mbDTO.setTaskId(v.getTaskId());
            mbDTO.setTaskDetailId(v.getId());
            if (mbDTO.getNecessaryTag().equals(NecessaryTagEnum.NONE_NECESSARY.getMessage()) || mbDTO.getNecessaryTag().equals(NecessaryTagEnum.SEASON_GOODS.getMessage()) || mbDTO.getNecessaryTag().equals(NecessaryTagEnum.UNSALABLE_CANCEL_MINDISPLAY.getMessage())) {
                mbDTO.setNecessaryTag("");
            } else {
                mbDTO.setForbidApply("否");
                mbDTO.setForbidDistr("否");
            }
            if (MdmTaskSourceEnum.SEASON_GOODS.getCode() == mdmTask.getTaskSource() || MdmTaskSourceEnum.SENSITIVE_DEL_DISPLAY.getCode() == mdmTask.getTaskSource()) {
                // 张瑜让我这么改的
                mbDTO.setTagModifyFlag(false);
            }
            if (v.getNecessaryTag().equals(Integer.valueOf(NecessaryTagEnum.STORE_TYPE_NECESSARY_CATALOGUE.getCode()))) {
                mbDTO.setNecessaryTag(NecessaryTagEnum.STORE_TYPE_NECESSARY.getMessage());
            }
            return mbDTO;
        }).collect(Collectors.toList());
        mbUtils.pushToMB(MBUtils.getMbUrl(necessaryMbPrpo), MBUtils.assembleParam(necessaryMbPrpo, mbDTOS));
    }

    @Override
    public void exportNecessaryContent(TokenUserDTO userDTO, NecessaryQueryParam param) {
        try {
            param.setToPage(false);
            List<NecessaryCommonDTO> necessaryCommonDTOS = getNecessaryCommonDTO(param, userDTO).firstEntry().getValue();
            NecessaryTagEnum queryTag = NecessaryTagEnum.getEnumByCode(param.getQueryTag());
            if (param.getQueryTag().equals(NecessaryTagEnum.PLATFORM_NECESSARY.getCode()) || param.getQueryTag().equals(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode()) || param.getQueryTag().equals(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()) || param.getQueryTag().equals(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode())) {
                List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode(), BudnlTaskDetailDicEnum.ZS.getCode()));
                necessaryCommonDTOS.stream().forEach(v->{
                    if (StringUtils.isNotEmpty(v.getStoreType())){
                        List<CommonEnums> commonEnums1 = commonEnums.stream().filter(x -> x.getEnumValue().equals(v.getStoreType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(commonEnums1)){
                            v.setStoreTypeDesc(commonEnums1.get(0).getEnumName());
                        }
                    }
                });
            }
            String fileName = queryTag.getMessage() + "_目录" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
//            fileName = URLEncoder.encode(fileName, "UTF-8");
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.NECESSARY_CONTENT, userDTO, new HandlerDataExportService<ListToExcelMultiSheetDTO<NecessaryCommonDTO>>() {
                @Override
                public List<ListToExcelMultiSheetDTO<NecessaryCommonDTO>> getDataToExport() {
                    ListToExcelMultiSheetDTO<NecessaryCommonDTO> sheetDTO = new ListToExcelMultiSheetDTO<>();
                    sheetDTO.setListGroup(necessaryCommonDTOS);
                    sheetDTO.setSheetName("sheet0");
                    sheetDTO.setFieldMap(NecessaryCommonDTO.getCommonGoodsExportMap(queryTag));
                    return Lists.newArrayList(sheetDTO);
                }

                @Override
                public List<ListToExcelMultiSheetDTO<NecessaryCommonDTO>> getDataToExport(Integer page, Integer pageSize) {
                    return null;
                }

                @Override
                public boolean isPageable() {
                    return false;
                }

                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return NecessaryCommonDTO.getCommonGoodsExportMap(queryTag);
                }
            });

        } catch (Exception e) {
            logger.error("导出必备目录异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public MdmStoreExDTO getCopyStoreInfo(Long storeId) {
        if (Objects.isNull(storeId)) {
            throw new AmisBadRequestException("请输入门店信息");
        }
        MdmStoreExDTO mdmStoreExDTO= CacheVar.getStoreExtInfoByStoreId(storeId).orElse(new MdmStoreExDTO());
        if (Objects.nonNull(mdmStoreExDTO.getBusinessId())) {
            mdmStoreExDTO.setCompanyOrgId(CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId()).map(OrgInfoBaseCache::getBusinessOrgId).orElse(null));
            mdmStoreExDTO.setBusinessName(CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId()).map(OrgInfoBaseCache::getBusinessShortName).orElse(""));
            mdmStoreExDTO.setOrgName(CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId()).map(OrgInfoBaseCache::getBusinessShortName).orElse(""));
            mdmStoreExDTO.setPlatformOrgId(CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId()).map(OrgInfoBaseCache::getPlatformOrgId).orElse(null));
            mdmStoreExDTO.setPlatformOrgName(CacheVar.getBusinessOrgByBusinessId(mdmStoreExDTO.getBusinessId()).map(OrgInfoBaseCache::getPlatformShortName).orElse(""));
        }
        return mdmStoreExDTO;
    }

    @Override
    public PageResult<StoreGoodsInfoDTO> getSourceStoreInfos(StoreGoodsQueryParam param, TokenUserDTO userDTO) {
        if (Objects.isNull(param.getStoreId())) {
            throw new AmisBadRequestException("请选择门店进行复制");
        }
        StoreGoodsInfoExample example = new StoreGoodsInfoExample();
        StoreGoodsInfoExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(param.getStoreId()).andNecessaryTagNotEqualTo(NecessaryTagEnum.NONE_NECESSARY.getCode()).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
        if (StringUtils.isNotBlank(param.getGoodsNo())) {
            criteria.andGoodsNoEqualTo(param.getGoodsNo().trim());
        }
        // 排除掉配方饮片类商品。大类=中药参茸 and 中类=配方中药。
        criteria.andSubCategoryIdNotBetween(12020000L, 12029999L);
        long count = storeGoodsInfoMapper.countByExample(example);
        if (count <= 0L){
            return new PageResult<>(count, new ArrayList<>());
        }
        example.setOffset(Long.valueOf(param.getPage() - 1) * param.getPerPage());
        example.setLimit(param.getPerPage());
        example.setOrderByClause(" necessary_tag asc, goods_no asc");
        List<StoreGoodsInfo> storeGoodsInfo = storeGoodsInfoMapper.selectByExample(example);
        List<String> goodsNos = storeGoodsInfo.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList());
        Map<String, SpuListVo> spuVOMap = new HashMap<>();
        Lists.partition(goodsNos, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
            spuVOMap.putAll(searchService.getSpuVOMap(storeGoodsInfo.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList())));
        });
        String redisKey = COPY_STORE_INFO + userDTO.getUserId().toString() + "-" + param.getStoreId().toString();
        RBucket<Set<String>> bucket = redissonClient.getBucket(redisKey);
        Set<String> goodsNoSet = bucket.get();
        return new PageResult<>(count, storeGoodsInfo.stream().map(v -> {
            StoreGoodsInfoDTO dto = new StoreGoodsInfoDTO();
            BeanUtils.copyProperties(v, dto);
            SpuListVo spuListVo = spuVOMap.get(v.getGoodsNo());
            if (Objects.nonNull(spuListVo)) {
                dto.setGoodsName(spuListVo.getName());
                dto.setSpecifications(spuListVo.getJhiSpecification());
                dto.setManufacturer(spuListVo.getFactoryid());
            }
            Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(dto.getStoreId());
            if (optional.isPresent()) {
                OrgInfoBaseCache orgInfoBaseCache = optional.get();
                dto.setPlatformName(orgInfoBaseCache.getPlatformShortName());
                dto.setCompanyName(orgInfoBaseCache.getBusinessShortName());
            }
            dto.setNecessaryTagDesc(NecessaryTagEnum.getMessageByCode(v.getNecessaryTag()));
            if (CollectionUtils.isNotEmpty(goodsNoSet) && goodsNoSet.contains(v.getGoodsNo())) {
                dto.setEffectStatus(StoreGoodsEffectStatusEnum.NO.getCode());
            }
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public String copyStoreGoods(TokenUserDTO userDTO, NecessaryCopyParam param){
        List<String> cacheKeys = new ArrayList<>();
        try {
            String redisKey = COPY_STORE_INFO + userDTO.getUserId().toString() + "-" + param.getSourceStoreId().toString();
            OrgInfoBaseCache sourceStore = CacheVar.getStoreByStoreId(param.getSourceStoreId()).orElseThrow(() -> new AmisBadRequestException("请选择来源门店"));
            OrgInfoBaseCache targetStore = CacheVar.getStoreByStoreId(param.getTargetStoreId()).orElseThrow(() -> new AmisBadRequestException("请选择目标门店"));
            StoreGoodsInfoExample example = new StoreGoodsInfoExample();
            StoreGoodsInfoExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(param.getSourceStoreId()).andNecessaryTagNotEqualTo(NecessaryTagEnum.NONE_NECESSARY.getCode()).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode());
            // 排除掉配方饮片类商品。大类=中药参茸 and 中类=配方中药。
            criteria.andSubCategoryIdNotBetween(12020000L, 12029999L);
            RBucket<Set<String>> bucket = redissonClient.getBucket(redisKey);
            Set<String> goodsNoSet = bucket.get();
            logger.info("redisKey:{} goodsNoSet:{}", redisKey, JSON.toJSONString(goodsNoSet));
            if (CollectionUtils.isNotEmpty(goodsNoSet)) {
                criteria.andGoodsNoNotIn(Lists.newArrayList(goodsNoSet));
            }
            List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(storeGoodsInfos)) {
                throw new AmisBadRequestException("没有需要复制的数据");
            }
            List<Long> ids = getStoreGoodsIds(storeGoodsInfos.size());
            List<Long> taskIds = getMdmTaskDetailIds(storeGoodsInfos.size());
            List<String> goodsNoList = storeGoodsInfos.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList());
            Iterator<StoreGoodsInfo> iterator = storeGoodsInfos.iterator();
            AtomicInteger i = new AtomicInteger(0);
            while (iterator.hasNext()) {
                StoreGoodsInfo storeGoodsInfo = iterator.next();
                storeGoodsInfo.setId(ids.get(i.getAndAdd(1)));
                storeGoodsInfo.setStoreOrgId(targetStore.getId());
                storeGoodsInfo.setStoreId(targetStore.getOutId());
                storeGoodsInfo.setStoreCode(targetStore.getSapCode());
                storeGoodsInfo.setCreatedBy(userDTO.getUserId());
                storeGoodsInfo.setCreatedName(userDTO.getName());
                storeGoodsInfo.setUpdatedBy(userDTO.getUserId());
                storeGoodsInfo.setUpdatedName(userDTO.getName());
                storeGoodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
            }
            if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
                MdmTask mdmTask = new MdmTask();
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), mdmTask);
                mdmTask.setTaskSource(MdmTaskSourceEnum.STORE_COPY.getCode());
                mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                mdmTask.setDetailCount(storeGoodsInfos.size());
                mdmTaskMapper.insertSelective(mdmTask);
                asyncTaskExecutor.execute(() -> {
                    StoreGoodsInfoExample deleteExample = new StoreGoodsInfoExample();
                    // 先查出目标门店必备的且可以下发的
                    deleteExample.createCriteria().andStoreIdEqualTo(param.getTargetStoreId()).andNecessaryTagNotEqualTo(NecessaryTagEnum.NONE_NECESSARY.getCode());
                    List<StoreGoodsInfo> oldList = storeGoodsInfoMapper.selectByExample(deleteExample);
                    if (CollectionUtils.isNotEmpty(oldList)) {
                        storeGoodsInfoExtendMapper.updateNecessaryTagByIds(param.getTargetStoreId(), oldList.stream().map(StoreGoodsInfo::getId).collect(Collectors.toList()), NecessaryTagEnum.NONE_NECESSARY.getCode(), BigDecimal.ZERO);
                        Iterator<StoreGoodsInfo> oldIter = oldList.iterator();
                        while (oldIter.hasNext()) {
                            StoreGoodsInfo next = oldIter.next();
                            if (StoreGoodsEffectStatusEnum.NO.getCode() == next.getEffectStatus()) {
                                oldIter.remove();
                                continue;
                            }
                            next.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
                            next.setMinDisplayQuantity(BigDecimal.ZERO);
                        }
                    }
                    deleteExample.clear();
                    deleteExample.createCriteria().andStoreIdEqualTo(param.getTargetStoreId()).andGoodsNoIn(storeGoodsInfos.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList()));
                    storeGoodsInfoMapper.deleteByExample(deleteExample);
                    Lists.partition(storeGoodsInfos, Constants.INSERT_MAX_SIZE).forEach(v -> storeGoodsInfoExtendMapper.batchInsert(v));
                    if (CollectionUtils.isNotEmpty(oldList)) {
                        Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(oldList.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList()));
                        List<Long> oldTaskIds = getMdmTaskDetailIds(oldList.size());
                        i.set(0);
                        List<MdmTaskDetail> oldTaskDetails = oldList.stream().map(v -> getMdmTaskDetail(targetStore, i, mdmTask, spuMap, oldTaskIds, v, userDTO)).collect(Collectors.toList());
                        mdmTask.setDetailCount(mdmTask.getDetailCount() + oldTaskDetails.size());
                        mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                        List<List<MdmTaskDetail>> partition = Lists.partition(oldTaskDetails, Constants.INSERT_MAX_SIZE);
                        for (List<MdmTaskDetail> v : partition) {
                            mdmTaskDetailExtendMapper.batchInsert(v);
                            try {
                                pushMdm(v);
                            } catch (Exception e) {
                                mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                            }
                        }
                    }
                    Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(goodsNoList);
                    i.set(0);
                    List<MdmTaskDetail> taskDetails = storeGoodsInfos.stream().map(v -> {
                        MdmTaskDetail taskDetail = getMdmTaskDetail(targetStore, i, mdmTask, spuMap, taskIds, v, userDTO);
                        taskDetail.setStoreOrgId(targetStore.getId());
                        taskDetail.setStoreId(targetStore.getOutId());
                        taskDetail.setStoreCode(targetStore.getSapCode());
                        taskDetail.setGoodsNo(v.getGoodsNo());
                        return taskDetail;
                    }).collect(Collectors.toList());
                    mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                    mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                    List<List<MdmTaskDetail>> partition = Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE);
                    for (List<MdmTaskDetail> v : partition) {
                        mdmTaskDetailExtendMapper.batchInsert(v);
                        try {
                            pushMdm(v);
                        } catch (Exception e) {
                            mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                        }
                    }
                    bucket.delete();

                });
                return "更新MDM任务创建成功，任务号：" + mdmTask.getId() + "，更新结果可去更新MDM任务管理模块查看";
            } else {
                return "复制的门店没有需要下发的数据";
            }
        } catch (Exception e) {
            logger.error("复制一店一目信息失败", e);
            throw new AmisBadRequestException(e.getMessage());
        } finally {
            if (CollectionUtils.isNotEmpty(cacheKeys)) {
                cacheKeys.forEach(v -> {
                    RBucket rBucket = redissonClient.getBucket(v);
                    rBucket.delete();
                });
            }
        }
    }

    private MdmTaskDetail getMdmTaskDetail(OrgInfoBaseCache targetStore, AtomicInteger i, MdmTask mdmTask, Map<String, SpuListVo> spuMap, List<Long> detailIds, StoreGoodsInfo storeGoodsInfo, TokenUserDTO userDTO) {
        MdmTaskDetail taskDetail = new MdmTaskDetail();
        BeanUtils.copyProperties(storeGoodsInfo, taskDetail);
        taskDetail.setId(detailIds.get(i.getAndAdd(1)));
        taskDetail.setTaskId(mdmTask.getId());
        taskDetail.setStoreName(targetStore.getShortName());
        taskDetail.setCreatedBy(userDTO.getUserId());
        taskDetail.setCreatedName(userDTO.getName());
        taskDetail.setUpdatedBy(userDTO.getUserId());
        taskDetail.setUpdatedName(userDTO.getName());
        SpuListVo spuListVo = spuMap.get(storeGoodsInfo.getGoodsNo());
        if (Objects.nonNull(spuListVo)) {
            taskDetail.setBarCode(spuListVo.getBarCode());
            taskDetail.setGoodsCommonName(spuListVo.getCurName());
            taskDetail.setGoodsName(spuListVo.getName());
            taskDetail.setGoodsUnit(spuListVo.getGoodsunit());
            taskDetail.setDescription(spuListVo.getDescription());
            taskDetail.setSpecifications(spuListVo.getJhiSpecification());
            taskDetail.setDosageForm(spuListVo.getDosageformsid());
            taskDetail.setManufacturer(spuListVo.getFactoryid());
            taskDetail.setApprovalNumber(spuListVo.getApprdocno());
        }
        taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        return taskDetail;
    }

    @Override
    public PageResult<MdmTaskDTO> getMdmTaskList(MdmTaskQueryParam queryParam, TokenUserDTO userDTO) {
        try {
            MdmTaskExample example = new MdmTaskExample();
            MdmTaskExample.Criteria criteria = example.createCriteria();
            MdmStatusTaskEnum taskEnum = MdmStatusTaskEnum.getEnumByCode(queryParam.getTaskStatus());
            MdmTaskSourceEnum sourceEnum = MdmTaskSourceEnum.getEnumByCode(queryParam.getTaskSource());
            if (Objects.nonNull(taskEnum)) {
                criteria.andTaskStatusEqualTo(taskEnum.getCode());
            }
            if (Objects.nonNull(sourceEnum)) {
                criteria.andTaskSourceEqualTo(sourceEnum.getCode());
            }
            if (null != userDTO && !userDTO.getRoles().contains(Constants.SUPPER_ROLE)) {
                criteria.andCreatedByEqualTo(userDTO.getUserId());
            }
            if (StringUtils.isNotBlank(queryParam.getUserName())) {
                criteria.andCreatedNameLike(Constants.QUERY_LIKE + queryParam.getUserName().trim() + Constants.QUERY_LIKE);
            }
            if (StringUtils.isNotBlank(queryParam.getCreateDate())) {
                String[] split = StringUtils.split(queryParam.getCreateDate(), ",");
                if (split.length != 2) {
                    throw new AmisBadRequestException("时间格式错误");
                }
                Date start = DateUtils.dealDateTimeStart(split[0]);
                if (Objects.isNull(start)) {
                    throw new AmisBadRequestException("开始时间格式错误");
                }
                Date end = DateUtils.dealDateTimeEnd(split[1]);
                if (Objects.isNull(end)) {
                    throw new AmisBadRequestException("结束时间格式错误");
                }
                criteria.andGmtCreateBetween(start, end);
            }
            if (StringUtils.isNotBlank(queryParam.getRemarks())) {
                criteria.andRemarksLike(queryParam.getRemarks());
            }
            if (StringUtils.isNotBlank(queryParam.getParamUniqueMark())) {
                criteria.andRemarksEqualTo(queryParam.getParamUniqueMark());
            }
            long count = mdmTaskMapper.countByExample(example);
            if (count <= 0L) {
                return new PageResult<>(count, new ArrayList<>());
            }
            example.setOffset(Long.valueOf(queryParam.getPage() - 1) * queryParam.getPerPage());
            example.setLimit(queryParam.getPerPage());
            example.setOrderByClause(" id DESC");
            List<MdmTaskDTO> taskDTOS = mdmTaskMapper.selectByExample(example).stream().map(v -> {
                MdmTaskDTO dto = new MdmTaskDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setTaskSourceDesc(MdmTaskSourceEnum.getMessageByCode(v.getTaskSource()));
                dto.setTaskStatusDesc(MdmStatusTaskEnum.getMessageByCode(v.getTaskStatus()));
                dto.setGmtCreate(DateUtils.conventDateStrByPattern(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                dto.setGmtUpdate(DateUtils.conventDateStrByPattern(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                if (MdmTaskSourceEnum.SENSITIVE_ADD.getCode() == v.getTaskSource() || MdmTaskSourceEnum.SENSITIVE_DEL_DISPLAY.getCode() == v.getTaskSource() || MdmTaskSourceEnum.SENSITIVE_CANCEL.getCode() == v.getTaskSource()) {
                    JSONObject jsonObject = JSONObject.parseObject(v.getExtend());
                    dto.setPlatformOrgId(jsonObject.getLong("platformOrgId"));
                    dto.setPlatformName(jsonObject.getString("platformName"));
                }
                return dto;
            }).collect(Collectors.toList());
            List<Long> ids = taskDTOS.stream().map(MdmTaskDTO::getId).collect(Collectors.toList());
            Map<Long, List<MdmTaskDetailCountDTO>> countMap = mdmTaskDetailExtendMapper.selectDetailCount(ids).stream().collect(Collectors.groupingBy(MdmTaskDetailCountDTO::getTaskId));
            List<MdmTask> updateList = new ArrayList<>();
            PageResult<MdmTaskDTO> pageResult = new PageResult<>(count, taskDTOS.stream().map(v -> {
                List<MdmTaskDetailCountDTO> detailCountDTOS = countMap.get(v.getId());
                if (CollectionUtils.isNotEmpty(detailCountDTOS)) {
                    Map<Byte, Integer> detailCountMap = detailCountDTOS.stream().collect(Collectors.toMap(MdmTaskDetailCountDTO::getPushStatus, MdmTaskDetailCountDTO::getDetailCount, (k1, k2) -> k1));
                    Integer detailCount = detailCountMap.values().stream().reduce(0, Integer::sum);
                    if (v.getDetailCount().intValue() <= 0) {
                        v.setProcess("0%");
                    } else {
                        v.setProcess(new BigDecimal(detailCount).divide(new BigDecimal(v.getDetailCount()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%");
                        if (v.getDetailCount().equals(detailCount) && v.getTaskStatus() != MdmStatusTaskEnum.SUCCESS.getCode()) {
                            MdmTask task = new MdmTask();
                            task.setId(v.getId());
                            if (detailCountMap.containsKey(MdmTaskPushStatusEnum.FAIL.getCode())) {
                                task.setTaskStatus(MdmStatusTaskEnum.FAIL.getCode());
                            } else {
                                task.setTaskStatus(MdmStatusTaskEnum.SUCCESS.getCode());
                            }
                            v.setTaskStatus(task.getTaskStatus());
                            v.setTaskStatusDesc(MdmStatusTaskEnum.getMessageByCode(task.getTaskStatus()));
                            updateList.add(task);
                        }
                    }
                } else {
                    v.setProcess("0%");
                }
                return v;
            }).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(updateList)) {
                mdmTaskExtendMapper.batchUpdate(updateList);
            }
            return pageResult;
        } catch (Exception e) {
            logger.error("查询任务详情失败", e);
            throw e;
        }
    }

    @Override
    public PageResult<MdmTaskDetailDTO> getMdmTaskErrorList(MdmTaskErrorQueryParam queryParam, TokenUserDTO userDTO) {
        MdmTaskDetailExample example = new MdmTaskDetailExample();
        MdmTaskDetailExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(queryParam.getTaskId());
        if (!Boolean.TRUE.equals(queryParam.getAllAble())) {
            criteria.andPushStatusEqualTo(MdmTaskPushStatusEnum.FAIL.getCode());
            if (null != userDTO && !userDTO.getRoles().contains(Constants.SUPPER_ROLE)) {
                criteria.andCreatedByEqualTo(userDTO.getUserId());
            }
        }
        long count = mdmTaskDetailMapper.countByExample(example);
        if (count <= 0L) {
            return new PageResult<>(count, new ArrayList<>());
        }
        example.setOffset(Long.valueOf(queryParam.getPage() - 1) * queryParam.getPerPage());
        example.setLimit(queryParam.getPerPage());
        return new PageResult<>(count, mdmTaskDetailMapper.selectByExample(example).stream().map(v -> {
            MdmTaskDetailDTO dto = new MdmTaskDetailDTO();
            BeanUtils.copyProperties(v, dto);
            Optional<OrgInfoBaseCache> store = CacheVar.getStoreByStoreId(v.getStoreId());
            if (store.isPresent()) {
                dto.setCompanyOrgId(store.get().getBusinessOrgId());
                dto.setBusinessId(store.get().getBusinessId());
                dto.setCompanyCode(store.get().getBusinessSapCode());
                dto.setCompanyName(store.get().getBusinessShortName());
                if (StringUtils.isBlank(dto.getNecessaryTagName())) {
                    dto.setNecessaryTagName("非必备");
                }
            }
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public void exportStoreGoodsInfo(TokenUserDTO userDTO, NecessaryQueryParam param) {
        try {
            NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(param.getQueryTag())).orElseThrow(() -> new AmisBadRequestException("未知的必备层级"));
            String fileName = "门店必备目录_" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN) + userDTO.getName() + ".xls";
            if (CollectionUtils.isEmpty(param.getPlatformOrgId())) {
                throw new AmisBadRequestException("请选择平台");
            }
            List<OrgInfoBaseCache> storeExDTOS = new ArrayList<>();

            param.getPlatformOrgId().stream().forEach(v->CacheVar.getPlatformByOrgId(v).orElseThrow(() -> new AmisBadRequestException("没有查询到平台信息")));

            //OrgInfoBaseCache platform = CacheVar.getPlatformByOrgId(param.getPlatformOrgId().get(0)).orElseThrow(() -> new AmisBadRequestException("没有查询到平台信息"));
            switch (tagEnum) {
                case GROUP_NECESSARY:
                case PLATFORM_NECESSARY:
                   // fileName = platform.getShortName() + "_" + fileName;
                    param.getPlatformOrgId().stream().forEach(v->{
                        List<OrgInfoBaseCache> collect = CacheVar.getStoreListByPlatformOrgId(v).stream().collect(Collectors.toList());
                        storeExDTOS.addAll(collect);
                    });
                    //storeExDTOS.addAll(CacheVar.getStoreListByPlatformOrgId(platform.getPlatformOrgId()).stream().map(v -> CacheVar.getStoreExtInfoByStoreId(v.getOutId())).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList()));
                    break;
                case COMPANY_NECESSARY:
                case STORE_TYPE_NECESSARY:
                case STORE_CHOOSE_NECESSARY:
                case SINGLE_STORE_NECESSARY:
                    if (CollectionUtils.isEmpty(param.getCompanyOrgId())) {
                        throw new AmisBadRequestException("请选择公司");
                    }
                    param.getCompanyOrgId().stream().forEach(v->{
                        List<OrgInfoBaseCache> storeListByBusinessOrgId = CacheVar.getStoreListByBusinessOrgId(v);
                        storeExDTOS.addAll(storeListByBusinessOrgId);
                    });
                    if (CollectionUtils.isEmpty(storeExDTOS)) {
                        throw new AmisBadRequestException("没有查询到企业下的门店信息");
                    }
                    break;
                default:
                    throw new AmisBadRequestException("未知的必备层级");
            }
            if (CollectionUtils.isEmpty(storeExDTOS)) {
                throw new AmisBadRequestException("当前平台/公司下没有门店信息");
            }
            String storeIds = storeExDTOS.stream().filter(v -> null != v.getOutId()).map(v -> String.valueOf(v.getOutId())).collect(Collectors.joining(", "));
            StoreGoodsVo storeGoodsVo = new StoreGoodsVo();
            storeGoodsVo.setStore_id(storeIds);
            storeGoodsVo.setNecessary_tag(Integer.valueOf(param.getNecessaryTag()));
            storeGoodsVo.setEffect_status(Integer.valueOf(StoreGoodsEffectStatusEnum.YES.getCode()));
            storeGoodsVo.setGoods_no(param.getGoodsNos());
            CommonResult export = nyuwaFeignClient.export(storeGoodsVo);
            if (Objects.isNull(export) || !export.getStatus().equals(Constants.INTEGER_ZERO)) {
                throw new AmisBadRequestException("导出失败");
            }
           /* param.setToPage(false);
            List<NecessaryCommonDTO> necessaryCommonDTOS = getNecessaryCommonDTO(param, userDTO).firstEntry().getValue();
            if (CollectionUtils.isEmpty(necessaryCommonDTOS)) {
                throw new AmisBadRequestException("没有需要导出的一店一目数据");
            }*/

           // List<String> goodsNos = necessaryCommonDTOS.stream().map(NecessaryCommonDTO::getGoodsNo).collect(Collectors.toList());
           /* asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.STORE_GOODS_INFO, userDTO, new HandlerDataExportService<ListToExcelMultiSheetDTO<StoreGoodsInfoDTO>>() {
                @Override
                public List<ListToExcelMultiSheetDTO<StoreGoodsInfoDTO>> getDataToExport() {
                    Map<Long, List<String>> storeGoodsMap = new HashMap<>();
                    List<StoreGoodsInfoDTO> storeGoodsInfos = new ArrayList<>();
                    for (NecessaryCommonDTO necessary : necessaryCommonDTOS) {
                        for (MdmStoreExDTO store : storeExDTOS) {
                            if (NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode() == necessary.getNecessaryTag()) {
                                if (necessary.getStoreId().equals(store.getStoreId())) {
                                    List<String> goodsNos = Optional.ofNullable(storeGoodsMap.get(store.getStoreId())).orElse(new ArrayList<>());
                                    goodsNos.add(necessary.getGoodsNo());
                                    storeGoodsMap.put(store.getStoreId(), goodsNos);
                                }
                                break;
                            } else if (NecessaryTagEnum.PLATFORM_NECESSARY.getCode() == necessary.getNecessaryTag()) {
                                if (necessary.getStoreType().equals(store.getPlatStoreTypeCode()) || necessary.getStoreType().equals(store.getZsStoreTypeCode())) {
                                    List<String> goodsNos = Optional.ofNullable(storeGoodsMap.get(store.getStoreId())).orElse(new ArrayList<>());
                                    goodsNos.add(necessary.getGoodsNo());
                                    storeGoodsMap.put(store.getStoreId(), goodsNos);
                                }
                            } else if (NecessaryTagEnum.COMPANY_NECESSARY.getCode() == necessary.getNecessaryTag()) {
                                if (necessary.getCity().equals(store.getCity())) {
                                    List<String> goodsNos = Optional.ofNullable(storeGoodsMap.get(store.getStoreId())).orElse(new ArrayList<>());
                                    goodsNos.add(necessary.getGoodsNo());
                                    storeGoodsMap.put(store.getStoreId(), goodsNos);
                                }
                            } else if (NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode() == necessary.getNecessaryTag() || NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode() == necessary.getNecessaryTag()) {
                                if (necessary.getCity().equals(store.getCity())) {
                                    List<String> goodsNos = Optional.ofNullable(storeGoodsMap.get(store.getStoreId())).orElse(new ArrayList<>());
                                    goodsNos.add(necessary.getGoodsNo());
                                    storeGoodsMap.put(store.getStoreId(), goodsNos);
                                }
                            } else {
                                List<String> goodsNos = Optional.ofNullable(storeGoodsMap.get(store.getStoreId())).orElse(new ArrayList<>());
                                goodsNos.add(necessary.getGoodsNo());
                                storeGoodsMap.put(store.getStoreId(), goodsNos);
                            }
                        }
                    }
                    if (MapUtils.isNotEmpty(storeGoodsMap)) {
                        storeGoodsMap.forEach((k,v) -> {
                            storeGoodsInfos.addAll(getStoreGoodsInfoByContent(k, v.stream().distinct().collect(Collectors.toList()), tagEnum.getCode()));
                        });
                    }
                    ListToExcelMultiSheetDTO<StoreGoodsInfoDTO> sheetDTO = new ListToExcelMultiSheetDTO<>();
                    sheetDTO.setListGroup(storeGoodsInfos);
                    sheetDTO.setSheetName("sheet0");
                    sheetDTO.setFieldMap(StoreGoodsInfoDTO.getFieldMap());
                    return Lists.newArrayList(sheetDTO);
                }

                @Override
                public List<ListToExcelMultiSheetDTO<StoreGoodsInfoDTO>> getDataToExport(Integer page, Integer pageSize) {
                    return null;
                }

                @Override
                public boolean isPageable() {
                    return false;
                }

                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return StoreGoodsInfoDTO.getFieldMap();
                }
            });*/
        } catch (Exception e) {
            logger.error("导出一店一目异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    public static int getSuffixByStoreId(Long storeId){
        int lastFiveNum = Integer.parseInt(IdUtils.getFirstNumber(IdUtils.getLastNumber(storeId,10),5));
        int firstTwoNum = Integer.parseInt(IdUtils.getFirstNumber(String.valueOf(lastFiveNum),2));
        int lastThreeNum = Integer.parseInt(IdUtils.getLastNumber(String.valueOf(lastFiveNum),3));
        return  (lastThreeNum % 256) * 10 + firstTwoNum % 2;
    }
    private List<StoreGoodsInfoDTO> getStoreGoodsInfoByContent(Long storeId, List<String> goodsNos, Byte queryTag) {
        StoreGoodsInfoExample example = new StoreGoodsInfoExample();
        example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoIn(goodsNos).andNecessaryTagEqualTo(queryTag);
        example.setOrderByClause(" necessary_tag asc, goods_no asc");
        List<StoreGoodsInfoDTO> result = storeGoodsInfoMapper.selectByExample(example).stream().map(v -> {
            StoreGoodsInfoDTO dto = new StoreGoodsInfoDTO();
            BeanUtils.copyProperties(v, dto);
            Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(dto.getStoreId());
            if (optional.isPresent()) {
                OrgInfoBaseCache orgInfoBaseCache = optional.get();
                dto.setPlatformName(orgInfoBaseCache.getPlatformShortName());
                dto.setCompanyName(orgInfoBaseCache.getBusinessShortName());
                dto.setStoreName(orgInfoBaseCache.getShortName());
            }
            dto.setNecessaryTagDesc(NecessaryTagEnum.getMessageByCode(v.getNecessaryTag()));
            return dto;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void consumerAddNecessary(NecessaryAddMqDTO addMqDTO) throws Exception {
        try {
            logger.info("添加必备目录开始消费 商品:{},门店:{}", addMqDTO.getGoods().getGoodsNo(), JSON.toJSONString(addMqDTO.getStoreIds()));
            Map<String, MdmTaskDetail> mdmTaskDetailMap = new HashMap<>();
            List<StoreGoodsInfo> storeGoodsInfos = new ArrayList<>();
            // 分布式id
            List<Long> ids = getStoreGoodsIds(addMqDTO.getStoreIds().size());
            List<Long> taskIds = getMdmTaskDetailIds(addMqDTO.getStoreIds().size());
            Map<Long, List<String>> blackCateIdMap = ruleService.checkExistsUnmanage(addMqDTO.getStoreIds(), Lists.newArrayList(addMqDTO.getGoods().getSubCategoryId().toString()));
            for (int i = 0; i< addMqDTO.getStoreIds().size(); i ++ ){
                Long storeId = addMqDTO.getStoreIds().get(i);
                StoreGoodsInfo goodsInfo = new StoreGoodsInfo();
                Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(storeId);
                if (!optional.isPresent()) {
                    logger.info("门店id:{}没有查询到门店信息", storeId);
                    continue;
                }
                OrgInfoBaseCache store = optional.get();
                BeanUtils.copyProperties(new CommonUserDTO(addMqDTO.getUserDTO()), goodsInfo);
                goodsInfo.setId(ids.get(i));
                goodsInfo.setStoreOrgId(store.getId());
                goodsInfo.setStoreId(store.getOutId());
                goodsInfo.setStoreCode(store.getSapCode());
                goodsInfo.setGoodsNo(addMqDTO.getGoods().getGoodsNo());
                goodsInfo.setSubCategoryId(addMqDTO.getGoods().getSubCategoryId());
                goodsInfo.setNecessaryTag(addMqDTO.getNecessaryTag());
                goodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                goodsInfo.setMinDisplayQuantity(new BigDecimal(1));

                //①商品经营属性合法性校验：
                //查商品在门店所属法人公司（businessid），商品表企业级属性：经营属性=（核心必备、一般、新品）-》从参数BB_2/goodsline中取值，说明经营属性合法，可以打入一店一目。
                AuctionSpuBaseInfo auctionSpuBaseInfo = addMqDTO.getBizScopeMap().get(store.getBusinessId());
                logger.info("门店id:{},商品:{},goodsLine:{}, bizScope:{}", storeId, addMqDTO.getGoods().getGoodsNo(), JSON.toJSONString(addMqDTO.getGoodslineList()), addMqDTO.getBizScopeMap().get(store.getBusinessId()));
                if (Objects.nonNull(auctionSpuBaseInfo) && !addMqDTO.getGoodslineList().contains(auctionSpuBaseInfo.getGoodsline())) {
                    logger.info("门店id:{}商品:{}经营属性对不上", storeId, addMqDTO.getGoods().getGoodsNo());
                    goodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.NO.getCode());
                }
                //②商品是否完成首营：
                //查商品在门店所属的法人公司（businessid）下是否存在，存着则认为完成首营，可以打入一店一目。（集团、平台添加商品调用标品，剩下4级的调用企业级别商品）
                SpuNewVo spuNewVo = null;
                if (!(NecessaryTagEnum.GROUP_NECESSARY.getCode() == addMqDTO.getNecessaryTag().byteValue() || NecessaryTagEnum.PLATFORM_NECESSARY.getCode() == addMqDTO.getNecessaryTag().byteValue())) {
                    spuNewVo = addMqDTO.getBizGoodsMap().get(store.getBusinessId());
                    if (Objects.isNull(spuNewVo)) {
                        logger.info("门店id:{}商品:{}没有查询到企业商品信息", storeId, addMqDTO.getGoods().getGoodsNo());
                        goodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.NO.getCode());
                    }
                }
                //③门店经营范围与商品经营范围相匹配检查：
                //商品经营范围获取：forest接口查商品信息，属性：comPv下78的属性（78:100100）
                //门店经营范围获取：store接口查api/internal/findStoreLicense，返回List<MdmLicenseBaseDTO>，DTO的属性businessScope
                //校验商品的经营范围在门店的经营范围list中，即满足经营范围，可以打入一店一目。
                List<String> goodsLicense = getStoreLicense(store.getBusinessId(), store.getOutId());
                logger.info("goodsLicense:{}", JSON.toJSONString(goodsLicense));
                if (Objects.nonNull(auctionSpuBaseInfo) && !goodsLicense.contains(auctionSpuBaseInfo.getBusiscopetag())) {
                    logger.info("商品:{}的经营范围不在门店:{}的经营范围", addMqDTO.getGoods().getGoodsNo(), storeId);
                    goodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.NO.getCode());
                }
                // 商品分类黑名单
                if (blackCateIdMap.containsKey(storeId)) {
                    logger.info("商品:{}的大类在门店:{}的类型黑名单范围", addMqDTO.getGoods().getGoodsNo(), storeId);
                    goodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.NO.getCode());
                }
                storeGoodsInfos.add(goodsInfo);
                // 添加 mdmtaskdetail
                if (StoreGoodsEffectStatusEnum.YES.getCode() == goodsInfo.getEffectStatus().byteValue()) {
                    MdmTaskDetail taskDetail = new MdmTaskDetail();
                    BeanUtils.copyProperties(goodsInfo, taskDetail);
                    taskDetail.setId(taskIds.get(i));
                    taskDetail.setTaskId(addMqDTO.getMdmTaskId());
                    taskDetail.setStoreName(store.getShortName());
                    taskDetail.setBarCode(addMqDTO.getGoods().getBarCode());
                    taskDetail.setGoodsCommonName(addMqDTO.getGoods().getGoodsCommonName());
                    taskDetail.setGoodsName(addMqDTO.getGoods().getGoodsName());
                    taskDetail.setGoodsUnit(addMqDTO.getGoods().getGoodsUnit());
                    taskDetail.setDescription(addMqDTO.getGoods().getDescription());
                    taskDetail.setSpecifications(addMqDTO.getGoods().getSpecifications());
                    taskDetail.setDosageForm(addMqDTO.getGoods().getDosageForm());
                    taskDetail.setManufacturer(addMqDTO.getGoods().getManufacturer());
                    taskDetail.setApprovalNumber(addMqDTO.getGoods().getApprovalNumber());
                    taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
                    mdmTaskDetailMap.put(taskDetail.getGoodsNo() + "-" + taskDetail.getStoreId(), taskDetail);
                }
            }
            if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
                Map<String, StoreGoodsInfo> existsStoreGoodsList = new HashMap<>();
                Map<Integer, Set<Long>> storeMap = new HashMap<>();
                storeGoodsInfos.forEach(v -> {
                    int suffix = getSuffixByStoreId(v.getStoreId());
                    Set<Long> list = Optional.ofNullable(storeMap.get(suffix)).orElse(new HashSet<>());
                    list.add(v.getStoreId());
                    storeMap.put(suffix, list);
                });
                storeMap.forEach((k,v) -> {
                    StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                    example.createCriteria().andStoreIdIn(Lists.newArrayList(v)).andGoodsNoEqualTo(addMqDTO.getGoods().getGoodsNo());
                    existsStoreGoodsList.putAll(storeGoodsInfoMapper.selectByExample(example).stream().collect(Collectors.toMap(goo -> goo.getGoodsNo() + "-" + goo.getStoreId(), Function.identity(), (k1,k2) -> k1)));
                });
                Iterator<StoreGoodsInfo> iterator = storeGoodsInfos.iterator();
                while(iterator.hasNext()) {
                    StoreGoodsInfo v = iterator.next();
                    StoreGoodsInfo exists = existsStoreGoodsList.get(v.getGoodsNo() + "-" + v.getStoreId());
                    if (Objects.nonNull(exists)) {
                        if (addMqDTO.getNecessaryTag() == NecessaryTagEnum.COMPANY_NECESSARY.getCode()){
                            //集团必备特殊规则 一店一目存在&配置类型=平台必备，不做调整。
                            if (exists.getNecessaryTag().equals(NecessaryTagEnum.PLATFORM_NECESSARY.getCode())) {
                                iterator.remove();
                                mdmTaskDetailMap.remove(v.getGoodsNo() + "-" + v.getStoreId());
                                continue;
                            }
                        }
                        if (!exists.getNecessaryTag().equals(v.getNecessaryTag())) {
                            // 非生效的不用下发
                            if (v.getEffectStatus().byteValue() == StoreGoodsEffectStatusEnum.NO.getCode()) {
                                mdmTaskDetailMap.remove(v.getGoodsNo() + "-" + v.getStoreId());
                            }
                        }
                    }
                }
                //先删后增
                if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
                    Map<Integer, List<StoreGoodsInfo>> map = new HashMap<>();
                    storeGoodsInfos.forEach(v -> {
                        int suffix = getSuffixByStoreId(v.getStoreId());
                        List<StoreGoodsInfo> list = Optional.ofNullable(map.get(suffix)).orElse(new ArrayList<>());
                        list.add(v);
                        map.put(suffix, list);
                    });
                    map.forEach((k,v) -> {
                        StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                        example.createCriteria().andStoreIdIn(v.stream().map(StoreGoodsInfo::getStoreId).collect(Collectors.toList())).andGoodsNoEqualTo(addMqDTO.getGoods().getGoodsNo());
                        storeGoodsInfoMapper.deleteByExample(example);
                        storeGoodsInfoExtendMapper.batchInsert(v);
                    });
                }
            }
            if (MapUtils.isNotEmpty(mdmTaskDetailMap)) {
                List<List<MdmTaskDetail>> partition = Lists.partition(Lists.newArrayList(mdmTaskDetailMap.values()), Constants.INSERT_MAX_SIZE);
                for(List<MdmTaskDetail> v : partition) {
                    mdmTaskDetailExtendMapper.batchInsert(v);
                    pushMdm(v);
                }
            }
            if (NecessaryTagEnum.PLATFORM_NECESSARY.getCode() == addMqDTO.getNecessaryTag().byteValue()) {
                // 查企业必备表：平台下管理公司、SUK，存在，继续查最新一店一目表中这个管理公司下的门店、SKU对应的配置类型，如果全部都是平台必备，则删除对应的企业必备表；如果有企业必备，则不删除企业必备表。
                NecessaryCompanyGoodsExample companyGoodsExample = new NecessaryCompanyGoodsExample();
                companyGoodsExample.createCriteria().andPlatformOrgIdEqualTo(addMqDTO.getPlatformOrg().getPlatformOrgId()).andGoodsNoEqualTo(addMqDTO.getGoods().getGoodsNo());
                List<Long> companyOrgIds = necessaryCompanyGoodsMapper.selectByExample(companyGoodsExample).stream().map(NecessaryCompanyGoods::getCompanyOrgId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(companyOrgIds)) {
                    Map<Long, ChildOrgsDTO> storeCompanyMap = permissionService.listChildOrgAssignedType(Lists.newArrayList(companyOrgIds), OrgTypeEnum.STORE.getCode()).stream().collect(Collectors.toMap(ChildOrgsDTO::getId, Function.identity(), (k1,k2) -> k1));
                    for (Long companyOrgId : companyOrgIds) {
                        ChildOrgsDTO childOrgsDTO = storeCompanyMap.get(companyOrgId);
                        logger.info("childOrgsDTO:{}", JSON.toJSONString(childOrgsDTO));
                        if (Objects.isNull(childOrgsDTO) || CollectionUtils.isEmpty(childOrgsDTO.getChildren())) {
                            continue;
                        }
                        List<Long> companyStoreIds = childOrgsDTO.getChildren().stream().map(OrgDTO::getOutId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        logger.info("companyStoreIds:{}", JSON.toJSONString(companyStoreIds));
                        if (CollectionUtils.isEmpty(companyStoreIds)) {
                            continue;
                        }
                        StoreGoodsInfoExample storeGoodsInfoExample = new StoreGoodsInfoExample();
                        storeGoodsInfoExample.createCriteria().andStoreIdIn(companyStoreIds).andGoodsNoEqualTo(addMqDTO.getGoods().getGoodsNo()).andNecessaryTagEqualTo(NecessaryTagEnum.COMPANY_NECESSARY.getCode());
                        long count = storeGoodsInfoMapper.countByExample(storeGoodsInfoExample);
                        if (count <= 0L) {
                            companyGoodsExample.clear();
                            companyGoodsExample.createCriteria().andPlatformOrgIdEqualTo(addMqDTO.getPlatformOrg().getPlatformOrgId()).andCompanyOrgIdEqualTo(companyOrgId).andGoodsNoEqualTo(addMqDTO.getGoods().getGoodsNo());
                            necessaryCompanyGoodsMapper.deleteByExample(companyGoodsExample);
                        }
                    }
                }
            }
            return;
        } catch (Exception e) {
            logger.info("添加一店一目失败:", e);
            throw e;
        } finally {
            if (Boolean.TRUE.equals(addMqDTO.getGoodsBlock())) {
                RBucket rBucket = redissonClient.getBucket(NECESSARY_ADD_GOODS_CACHE_KEY + addMqDTO.getPlatformOrg().getId().toString() + "-" + addMqDTO.getGoods().getGoodsNo());
                rBucket.delete();
            }
            String finishedKey = addMqDTO.getFinishedKey();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(finishedKey);
            if (atomicLong.decrementAndGet() <= 0L) {
                // 更新mdm明细行数
                MdmTaskDetailExample example = new MdmTaskDetailExample();
                example.createCriteria().andTaskIdEqualTo(addMqDTO.getMdmTaskId());
                long detailCount = mdmTaskDetailMapper.countByExample(example);
                MdmTask mdmTask = new MdmTask();
                mdmTask.setId(addMqDTO.getMdmTaskId());
                mdmTask.setDetailCount((int) detailCount);
                mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                atomicLong.delete();
            }
        }
    }

    @Override
    public List<Long> isFullScopeByOrgId(List<Long> orgIds, Long userId) {
        if(CollectionUtils.isEmpty(orgIds) || Objects.isNull(userId)){
            throw new AmisBadRequestException(ErrorCodeEnum.INVALID_TOKEN);
        }
        List<OrgSimpleDTO> orgSimpleDTOList = permissionService.getUserDataScopeChildOrgByOrgId(userId, orgIds, false);
        return orgSimpleDTOList.stream().filter(v -> v.getIsFullScope() == 1).map(OrgSimpleDTO::getId).collect(Collectors.toList());
    }

    @Override
    public NecessaryCityDTO getCityByCompany(TokenUserDTO userDTO, Long companyOrgId) {
        List<OrgDTO> children = permissionService.listChildOrgAssignedType(Lists.newArrayList(companyOrgId), OrgTypeEnum.STORE.getCode()).get(0).getChildren();
        if (CollectionUtils.isEmpty(children)) {
            throw new AmisBadRequestException("所选企业下没有门店");
        }
        List<String> citys = new ArrayList<>();
        for (OrgDTO store : children) {
            if (Objects.isNull(store.getId())) {
                continue;
            }
            Optional<MdmStoreExDTO> optional = CacheVar.getStoreExtInfoByStoreId(store.getOutId());
            if (!optional.isPresent()) {
                continue;
            }
            citys.add(optional.get().getCity());
        }
        NecessaryCityDTO cityDTO = new NecessaryCityDTO();
        cityDTO.setCitys(citys.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        return cityDTO;
    }

    @Override
    public String refushUpdate(Long taskId, Integer taskSource, TokenUserDTO userDTO) {
        try {
            MdmTaskDetailExample example = new MdmTaskDetailExample();
            MdmTaskDetailExample.Criteria criteria = example.createCriteria();
            criteria.andTaskIdEqualTo(taskId);
            if (!userDTO.getRoles().contains(Constants.SUPPER_ROLE)) {
                criteria.andCreatedByEqualTo(userDTO.getUserId());
            }
            if (taskSource != null && taskSource.byteValue() == MdmTaskSourceEnum.GROUP_GOODS_TASK.getCode()) {
                criteria.andPushStatusEqualTo(MdmTaskPushStatusEnum.NO_PUSH.getCode());
            } else {
                criteria.andPushStatusEqualTo(MdmTaskPushStatusEnum.FAIL.getCode());
            }
            long count = mdmTaskDetailMapper.countByExample(example);
            MdmTask mdmTask = mdmTaskMapper.selectByPrimaryKey(taskId);
            if (taskSource != null && taskSource.byteValue() == MdmTaskSourceEnum.GROUP_GOODS_TASK.getCode() && mdmTask.getTaskStatus().equals(MdmStatusTaskEnum.CREATEING.getCode()) && Long.valueOf(mdmTask.getDetailCount()).longValue() != count) {
                throw new AmisBadRequestException("下发任务未创建完成,请稍后重试");
            }
            if (count > 0L) {
                if (taskSource != null && taskSource.byteValue() == MdmTaskSourceEnum.GROUP_GOODS_TASK.getCode()) {
                    if (!mdmTask.getTaskStatus().equals(MdmStatusTaskEnum.CREATEING.getCode())) {
                        throw new AmisBadRequestException("已启动更新,请耐心等待");
                    }
                    mdmTask.setUpdatedName(userDTO.getName());
                    mdmTask.setUpdatedBy(userDTO.getUserId());
                    mdmTask.setGmtUpdate(new Date());
                    mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                    mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                    asyncTaskExecutor.submit(() -> {
                        try {
                            Long id=null;
                            for (int i = 0;; i++) {
                                /*example.setLimit(Constants.INSERT_MAX_SIZE);
                                example.setOrderByClause(" id asc");*/

                                List<MdmTaskDetail> mdmTaskDetails=new ArrayList<>();
                                if (!userDTO.getRoles().contains(Constants.SUPPER_ROLE)) {
                                    mdmTaskDetails = mdmTaskDetailExtendMapper.selectMdmDetail(taskId, MdmTaskPushStatusEnum.NO_PUSH.getCode(), id, userDTO.getUserId(), Constants.INSERT_MAX_SIZE);
                                } else {
                                    mdmTaskDetails = mdmTaskDetailExtendMapper.selectMdmDetail(taskId, MdmTaskPushStatusEnum.NO_PUSH.getCode(), id, null, Constants.INSERT_MAX_SIZE);
                                }
                                if (CollectionUtils.isEmpty(mdmTaskDetails)) {
                                    break;
                                }
                                id = mdmTaskDetails.stream().map(MdmTaskDetail::getId).max(Comparator.comparing(x -> x)).orElse(null);
                                if (null == id) {
                                    break;
                                }
                                //criteria.andIdGreaterThan(maxId);

                                storeContentsFactory.getAssemble(1).pushMdm(mdmTaskDetails);
                                List<Long> ids = mdmTaskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(ids)){
                                    mdmTaskDetailExtendMapper.batchUpdatePushStatus(taskId,ids , MdmTaskPushStatusEnum.PUSHED.getCode());
                                }
                                try {
                                    Thread.sleep(100L);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                                if (mdmTaskDetails.size() < Constants.INSERT_MAX_SIZE) {
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            logger.error("推送MDM数据报错", e);
                        }
                    });
                    return "已启动更新,请耐心等待";
                } else {
                    for (int i = 0;; i++) {
                        example.setLimit(Constants.INSERT_MAX_SIZE);
                        example.setOffset(Long.valueOf(i * Constants.INSERT_MAX_SIZE));
                        List<MdmTaskDetail> mdmTaskDetails = mdmTaskDetailMapper.selectByExample(example);
                        if (CollectionUtils.isEmpty(mdmTaskDetails)) {
                            break;
                        }
                        storeContentsFactory.getAssemble(1).pushMdm(mdmTaskDetails);
                        if (mdmTaskDetails.size() < Constants.INSERT_MAX_SIZE) {
                            break;
                        }
                    }
                    MdmTask task = new MdmTask();
                    task.setId(taskId);
                    task.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                    mdmTask.setUpdatedName(userDTO.getName());
                    mdmTask.setUpdatedBy(userDTO.getUserId());
                    mdmTask.setGmtUpdate(new Date());
                    mdmTaskMapper.updateByPrimaryKeySelective(task);
                }
            }
            return "";
        } catch (Exception e) {
            logger.error("重新发送失败的任务失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public SelectorResult listSearch(Integer searchType) {
        // 1 必备标识 2 mdm任务状态 3 mdm任务来源
        if (null == searchType) {
            throw new AmisBadRequestException("错误的选择器类型");
        } else if (searchType.intValue() == 1) {
            return new SelectorResult(Arrays.stream(NecessaryTagEnum.values()).map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList()));
        } else if (searchType.intValue() == 2) {
            return new SelectorResult(Arrays.stream(MdmStatusTaskEnum.values()).map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList()));
        } else if (searchType.intValue() == 3) {
            return new SelectorResult(Arrays.stream(MdmTaskSourceEnum.values()).map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList()));
        } else {
            throw new AmisBadRequestException("未知的选择器类型");
        }
    }

    private List<String> getStoreLicense(Long businessId, Long storeId) {
        try {
            RBucket<List<String>> rBucket = redissonClient.getBucket(BUSINESS_SCOPE_CACHE + storeId.toString());
            if (CollectionUtils.isEmpty(rBucket.get())) {
                List<MdmLicenseBaseDTO> storeLicense = storeService.findStoreLicense(businessId, storeId);
                rBucket.set(storeLicense.stream().map(MdmLicenseBaseDTO::getBusinessScope).distinct().collect(Collectors.toList()), 30L, TimeUnit.MINUTES);
            }
            return rBucket.get();
        } catch (Exception e) {
            try {
                logger.warn("查询storeService.findStoreLicense报错 businessId:{}, storeId:{}", businessId, storeId);
                return storeService.findStoreLicense(businessId, storeId).stream().map(MdmLicenseBaseDTO::getBusinessScope).distinct().collect(Collectors.toList());
            } catch (Exception e1) {
                logger.warn("查询storeService.findStoreLicense报错返回空 businessId:{}, storeId:{}", businessId, storeId);
                return new ArrayList<>();
            }
        }
    }

    @Override
    public void permOrgCacheInit() {
        try {

            // 初始化平台缓存
            cacheService.initPlatformCache();
            // 初始化连锁缓存
            cacheService.initBusinessCache();
            // 初始化门店缓存
            cacheService.initStoreCache();
            //加载门店全部数据
            cacheService.refushMdmStoreExCache();
        } catch (Exception e) {
            logger.error("加载缓存异常", e);
        }
    }

    @Override
    public void receiveMdmStoreGoodsInfo(JSONObject param) {
//        logger.info("receiveMdmStoreGoodsInfo param:{}", param.toJSONString());
        if (Objects.isNull(param)) {
            logger.warn("参数为空");
            return;

        }
        JSONArray table = param.getJSONArray("Table");
        if (CollectionUtils.isEmpty(table)) {
            logger.warn("table没有数据");
            return;
        }
        MbCommonDTO mbCommonDTO = table.toJavaList(MbCommonDTO.class).get(0);
        if (Objects.isNull(mbCommonDTO.getBstatus()) || ! (mbCommonDTO.getBstatus() == 5 || mbCommonDTO.getBstatus() == 6)) {
            logger.warn("bstatus状态有误");
            return;
        }
        MdmMbResponse mdmMbResponse = JSON.parseObject(mbCommonDTO.getBdata().toString(), MdmMbResponse.class);
        // bstatus 5 成功 6失败
        if(Objects.isNull(mdmMbResponse.getTaskDetailId()) || Objects.isNull(mdmMbResponse.getTaskId())) {
            logger.warn("没有返回主键ID");
            return;
        }
        if (mbCommonDTO.getBstatus() == 5) {
            logger.info("taskId:{},taskDetailId:{}成功 舍弃",mdmMbResponse.getTaskId(),mdmMbResponse.getTaskDetailId());
            return;
        }
        RAtomicLong atomicLong = redissonClient.getAtomicLong("SCIB_NECESSARY-RECEIVE-LOCK-" + mdmMbResponse.getTaskDetailId());
        if (atomicLong.incrementAndGet() > 1) {
            logger.warn("taskdetailid:{}重复", mdmMbResponse.getTaskDetailId());
            return;
        }
        atomicLong.expire(5L, TimeUnit.MINUTES);
//        RBucket<Long> bucket = redissonClient.getBucket("SCIB_NECESSARY-RECEIVE-LOCK-" + mdmMbResponse.getTaskDetailId());
//        if (bucket.isExists() && Objects.nonNull(bucket.get())) {
//            logger.warn("taskdetailid:{}重复", mdmMbResponse.getTaskDetailId());
//            return;
//        }
//        bucket.set(mdmMbResponse.getTaskId(), 5L, TimeUnit.MINUTES);
        MdmTaskDetailExample example = new MdmTaskDetailExample();
        example.createCriteria().andTaskIdEqualTo(mdmMbResponse.getTaskId()).andIdEqualTo(mdmMbResponse.getTaskDetailId());
        MdmTaskDetail detail = new MdmTaskDetail();
//        detail.setId(mdmMbResponse.getTaskDetailId());
//        detail.setTaskId(mdmMbResponse.getTaskId());
        detail.setExtend(mdmMbResponse.getMessage());
        detail.setPushStatus(mbCommonDTO.getBstatus() == 5 ? MdmTaskPushStatusEnum.SUCCESS.getCode() : MdmTaskPushStatusEnum.FAIL.getCode());
        mdmTaskDetailMapper.updateByExampleSelective(detail, example);
//        example.clear();
//        MdmTaskDetailExample.Criteria criteria = example.createCriteria();
//        criteria.andTaskIdEqualTo(mdmMbResponse.getTaskId()).andPushStatusIn(Lists.newArrayList(MdmTaskPushStatusEnum.SUCCESS.getCode(), MdmTaskPushStatusEnum.FAIL.getCode()));
//        long count = mdmTaskDetailMapper.countByExample(example);
//        MdmTask task = mdmTaskMapper.selectByPrimaryKey(mdmMbResponse.getTaskId());
//        if (task.getDetailCount() == count) {
//            example.clear();
//            example.createCriteria().andTaskIdEqualTo(mdmMbResponse.getTaskId()).andPushStatusEqualTo(MdmTaskPushStatusEnum.FAIL.getCode());
//            long failCount = mdmTaskDetailMapper.countByExample(example);
//            if (failCount > 0) {
//                task.setTaskStatus(MdmTaskPushStatusEnum.FAIL.getCode());
//            } else {
//                task.setTaskStatus(MdmTaskPushStatusEnum.SUCCESS.getCode());
//            }
//            mdmTaskMapper.updateByPrimaryKeySelective(task);
//        }
    }

    // 校验是否可以进行必备目录修改
    @Override
    public boolean checkModifyAble(Long platformOrgId) {
        BundlingTaskInfoExample example = new BundlingTaskInfoExample();
        example.createCriteria().andOrgIdEqualTo(platformOrgId).andTaskTypeIn(BundlTaskTypeEnum.getNecessaryCheckTypeList()).andTaskStatusIn(Lists.newArrayList(BundlTaskStatusEnum.COMPUTING.getCode(), BundlTaskStatusEnum.COMPUTED.getCode()));
        return bundlingTaskInfoMapper.countByExample(example) <= 0L;
    }

    @Override
    public void manualPushMdm(Long taskId) {
        try {
            if (null == taskId) {
                throw new AmisBadRequestException("taskId不能为空");
            }
            MdmTaskDetailExample example = new MdmTaskDetailExample();
            example.createCriteria().andTaskIdEqualTo(taskId);
            List<MdmTaskDetail> mdmTaskDetails = mdmTaskDetailMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(mdmTaskDetails)) {
                throw new AmisBadRequestException("没有需要下发的任务");
            }
            List<List<MdmTaskDetail>> partition = Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE);
            for (List<MdmTaskDetail> v : partition) {
                pushMdm(v);
            }
        } catch (Exception e) {
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void updateCopyStoreGoodsStatus(Long sourceStoreId, String goodsNo, Byte effectStatus, TokenUserDTO userDTO) {
        if (null == sourceStoreId || StringUtils.isBlank(goodsNo) || null == effectStatus) {
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        String redisKey = COPY_STORE_INFO + userDTO.getUserId().toString() + "-" + sourceStoreId.toString();
        RBucket<Set<String>> bucket = redissonClient.getBucket(redisKey);
        Set<String> goodsSet = bucket.get();
        if (CollectionUtils.isEmpty(goodsSet)) {
            goodsSet = new HashSet<>();
        }
        if (StoreGoodsEffectStatusEnum.NO.getCode() == effectStatus.byteValue()) {
            goodsSet.add(goodsNo);
        } else {
            goodsSet.remove(goodsNo);
        }
        bucket.delete();
        logger.info("redisKey:{} goodsNoSet:{}", redisKey, JSON.toJSONString(goodsSet));
        bucket.set(goodsSet, 1L, TimeUnit.DAYS);
    }

    @Override
    public List<OrgDTO> getStoreByStoreType(Long companyOrgId, String storeType) {
        if (null == companyOrgId || StringUtils.isBlank(storeType)) {
            throw new AmisBadRequestException("参数不能为空,请联系管理员");
        }
        ChildOrgsDTO child = permissionService.listChildOrgAssignedType(Lists.newArrayList(companyOrgId), OrgTypeEnum.STORE.getCode()).stream().findFirst().orElse(new ChildOrgsDTO());
        if (CollectionUtils.isEmpty(child.getChildren())) {
            return new ArrayList<>();
        }
        return child.getChildren().stream().filter(v -> StringUtils.isNotBlank(v.getSapcode())).map(v -> {
            MdmStoreExDTO exDTO = CacheVar.storeExMap.get(v.getSapcode());
            if (Objects.isNull(exDTO)) {
                return null;
            }
            if (storeType.equals(exDTO.getStoreTypeCode()) || storeType.equals(exDTO.getZsStoreTypeCode())) {
                return v;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public void receiveInitMdmPush(JSONObject param) {
        if (Objects.isNull(param)) {
            throw new AmisBadRequestException("参数为空");
        }
        JSONArray table = param.getJSONArray("Table");
        if (CollectionUtils.isEmpty(table)) {
            throw new AmisBadRequestException("table没有数据");
        }
        List<MdmStoreGoodsInfoDTO> mdmStoreGoodsInfoDTOS = new ArrayList<>();
        List<MbCommonDTO> mbCommonDTOS = table.toJavaList(MbCommonDTO.class);
        mbCommonDTOS.forEach(mbCommonDTO -> {
            logger.info("接收mdm初始化的一店一目信息 bguid:{}", mbCommonDTO.getBguid());
            MdmMbResponse mdmMbResponse = JSON.parseObject(mbCommonDTO.getBdata().toString(), MdmMbResponse.class);
            JSONObject bdata = JSONObject.parseObject(mbCommonDTO.getBdata().toString());
            mdmStoreGoodsInfoDTOS.addAll(JSONObject.parseArray(bdata.getString("data"), MdmStoreGoodsInfoDTO.class));
        });
        List<String> goodsNos = mdmStoreGoodsInfoDTOS.stream().map(MdmStoreGoodsInfoDTO::getGoodsNo).distinct().collect(Collectors.toList());
        Map<String, SpuListVo> spuVOMap = new HashMap<>();
        try {
            spuVOMap.putAll(searchService.getSpuVOMap(goodsNos));
        } catch (Exception e) {
            logger.error("查询商品异常:{}", e);
            mdmPushStoreGoodsErrorRecordExtendMapper.batchInsert(mdmStoreGoodsInfoDTOS.stream().map(v -> {
                MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                BeanUtils.copyProperties(v, record);
                record.setErrorMsg("查询商品异常");
                return record;
            }).collect(Collectors.toList()));
            return;
        }
        Map<String, List<MdmStoreGoodsInfoDTO>> map = mdmStoreGoodsInfoDTOS.stream().collect(Collectors.groupingBy(MdmStoreGoodsInfoDTO::getStoreCode));
        if (MapUtils.isEmpty(map)) {
            logger.info("没有数据");
            return;
        }
        for (Map.Entry<String, List<MdmStoreGoodsInfoDTO>> entry : map.entrySet()) {
            Optional<OrgInfoBaseCache> store = CacheVar.getStoreBySapCode(entry.getKey());
            List<MdmStoreGoodsInfoDTO> value = entry.getValue();
            if (!store.isPresent()) {
                mdmPushStoreGoodsErrorRecordExtendMapper.batchInsert(value.stream().map(e -> {
                    MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                    BeanUtils.copyProperties(e, record);
                    record.setErrorMsg("门店编码不存在");
                    return record;
                }).collect(Collectors.toList()));
                continue;
            }
            List<MdmPushStoreGoodsErrorRecord> errorRecords = new ArrayList<>();
            List<StoreGoodsInfo> insert = new ArrayList<>();
            List<Long> ids = getStoreGoodsIds(value.size() + 1);
            for (int i = 0; i < value.size(); i++) {
                MdmStoreGoodsInfoDTO dto = value.get(i);
                StoreGoodsInfo goodsInfo = new StoreGoodsInfo();
                goodsInfo.setId(ids.get(i));
                goodsInfo.setStoreOrgId(store.get().getId());
                goodsInfo.setStoreId(store.get().getOutId());
                goodsInfo.setStoreCode(store.get().getSapCode());
                goodsInfo.setGoodsNo(dto.getGoodsNo());
                SpuListVo spuListVo = spuVOMap.get(dto.getGoodsNo());
                if (null == spuListVo) {
                    MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                    BeanUtils.copyProperties(dto, record);
                    record.setErrorMsg("商品编码不存在");
                    errorRecords.add(record);
                    continue;
                }
                goodsInfo.setSubCategoryId(Long.valueOf(spuListVo.getCategoryId()));
                Optional<NecessaryTagEnum> tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByMessage(dto.getNecessaryTagDesc()));
                if (!tagEnum.isPresent()) {
                    MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                    BeanUtils.copyProperties(dto, record);
                    record.setErrorMsg("必备层级不存在");
                    errorRecords.add(record);
                    continue;
                }
                goodsInfo.setNecessaryTag(tagEnum.get().getCode());
                goodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                if (StringUtils.isBlank(dto.getMinDisplayQuantity())) {
                    MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                    BeanUtils.copyProperties(dto, record);
                    record.setErrorMsg("最小陈列量为空");
                    errorRecords.add(record);
                    continue;
                }
                try{
                    goodsInfo.setMinDisplayQuantity(new BigDecimal(dto.getMinDisplayQuantity()));
                    if (goodsInfo.getMinDisplayQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                        BeanUtils.copyProperties(dto, record);
                        record.setErrorMsg("最小陈列量<=0");
                        errorRecords.add(record);
                        continue;
                    }
                } catch (Exception e) {
                    MdmPushStoreGoodsErrorRecord record = new MdmPushStoreGoodsErrorRecord();
                    BeanUtils.copyProperties(dto, record);
                    record.setErrorMsg("最小陈列量不是数字");
                    errorRecords.add(record);
                    continue;
                }
                goodsInfo.setCreatedBy(Constants.SYS_USER_ID);
                goodsInfo.setCreatedName(Constants.SYS_USER_NAME);
                goodsInfo.setUpdatedBy(Constants.SYS_USER_ID);
                goodsInfo.setUpdatedName(Constants.SYS_USER_NAME);
                insert.add(goodsInfo);
            }
            if (CollectionUtils.isNotEmpty(insert)) {
                StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                example.createCriteria().andStoreIdEqualTo(store.get().getOutId()).andGoodsNoIn(insert.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList()));
                storeGoodsInfoMapper.deleteByExample(example);
                storeGoodsInfoExtendMapper.batchInsert(insert);
            }
            if (CollectionUtils.isNotEmpty(errorRecords)) {
                mdmPushStoreGoodsErrorRecordExtendMapper.batchInsert(errorRecords);
            }
        }
    }

    @Override
    public void importRefreshStoreGoods(MultipartFile file, Integer necessaryTag, String necessaryTagName, Integer sourceNecessaryTag, Boolean pushMdmAble, TokenUserDTO userDTO) throws Exception {
        LinkedHashMap map = new LinkedHashMap();
        map.put("商品编码", "goodsNo");
        map.put("门店编码", "storeCode");
        map.put("门店编码", "storeCode");
        map.put("原始层级", "sourceNecessaryTag");
        Map<String, List<StoreGoodsContents>> storeGoodsMap = HutoolUtil.excelToList(file.getInputStream(), map, StoreGoodsContents.class, 1).stream()
                .filter(v -> StringUtils.isNotBlank(v.getGoodsNo()) && StringUtils.isNotBlank(v.getStoreCode())).map(v -> {
                    v.setStoreCode(v.getStoreCode().trim());
                    v.setGoodsNo(v.getGoodsNo().trim());
                    return v;
                }).collect(Collectors.groupingBy(StoreGoodsContents::getStoreCode));
        if (MapUtils.isEmpty(storeGoodsMap)) {
            throw new BusinessErrorException("导入文件为空");
        }
        StringBuilder errorMsg = new StringBuilder();
        List<Long> successTaskIds = new ArrayList<>();
        storeGoodsMap.forEach((storeNo,v) -> {
            NecessaryRefreshParam param = new NecessaryRefreshParam();
            param.setStoreCodes(Lists.newArrayList(storeNo));
            param.setGoodsNos(v.stream().map(StoreGoodsContents::getGoodsNo).distinct().collect(Collectors.toList()));
            param.setNecessaryTag(necessaryTag);
            param.setSourceNecessaryTag(sourceNecessaryTag);
            param.setNecessaryTagName(necessaryTagName);
            param.setPushMdmAble(pushMdmAble);
            try {
                successTaskIds.add(refreshStoreGoods(param, userDTO));
            } catch (Exception e) {
                errorMsg.append(JSON.toJSONString(param));
                errorMsg.append(e.getMessage());
            }
        });
        logger.info("刷新一店一目导入处理完毕");
        logger.info("刷新一店一目导入处理成功Id:{}", JSON.toJSONString(successTaskIds));
        logger.info("刷新一店一目导入处理失败原因:{}", errorMsg.toString());
    }

    @Override
    public Long refreshStoreGoods(NecessaryRefreshParam param, TokenUserDTO userDTO) throws Exception {
        try {
            if (CollectionUtils.isEmpty(param.getStoreCodes())) {
                throw new BusinessErrorException("门店编码不能为空");
            }
            if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                throw new BusinessErrorException("商品编码不能为空");
            }
            if (null == param.getNecessaryTag()) {
                throw new BusinessErrorException("必备层级不能为空");
            }
            if (param.getNecessaryTag() != 0 && StringUtils.isEmpty(param.getNecessaryTagName())) {
                throw new BusinessErrorException("必备层级名称不能为空");
            }
            if (param.getNecessaryTag() == 0) {
                param.setNecessaryTagName("");
            }
            BigDecimal minDisplayQuantity = BigDecimal.ONE;
            if (param.getNecessaryTag() <= 0) {
                minDisplayQuantity = BigDecimal.ZERO;
            }
            List<OrgInfoBaseCache> stores = param.getStoreCodes().stream().map(v -> CacheVar.getStoreBySapCode(v)).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(stores)) {
                throw new BusinessErrorException("门店编码有误");
            }
            if (stores.size() < param.getStoreCodes().size()) {
                List<String> storeCodes = stores.stream().map(OrgInfoBaseCache::getSapCode).distinct().collect(Collectors.toList());
                throw new BusinessErrorException("门店编码:" + param.getStoreCodes().stream().filter(v -> !storeCodes.contains(v)).collect(Collectors.joining(",")) + "有误");
            }
            Long mdmTaskId = null;
//            Map<String, SpuListVo> spuListVoMap = new HashMap<>();
            if (param.getPushMdmAble()) {
                MdmTask mdmTask = new MdmTask();
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), mdmTask);
                mdmTask.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode() == param.getNecessaryTag() ? MdmTaskSourceEnum.GOODS_CATALOGUE_SUPPLE.getCode() : MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
                mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                mdmTask.setDetailCount(0);
                mdmTask.setExtend(stores.stream().map(OrgInfoBaseCache::getSapCode).filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                mdmTaskMapper.insertSelective(mdmTask);
                mdmTaskId = mdmTask.getId();
//                spuListVoMap.putAll(searchService.getSpuVOMap(param.getGoodsNos()));
            }
            List<MdmTaskDetail> details = new ArrayList<>();
            for (OrgInfoBaseCache store : stores) {
                StoreGoodsContentsExample example = new StoreGoodsContentsExample();
                StoreGoodsContentsExample.Criteria criteria = example.createCriteria();
                criteria.andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(param.getGoodsNos());
                if (null != param.getSourceNecessaryTag()) {
                    criteria.andNecessaryTagEqualTo(param.getSourceNecessaryTag());
                }
                StoreGoodsContents storeGoodsInfo = new StoreGoodsContents();
                storeGoodsInfo.setMinDisplayQuantity(minDisplayQuantity);
                storeGoodsInfo.setNecessaryTag(param.getNecessaryTag());
                storeGoodsInfo.setNecessaryTagName(param.getNecessaryTagName());
                storeGoodsInfo.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                storeGoodsContentsMapper.updateByExampleSelective(storeGoodsInfo, example);
                if (!param.getPushMdmAble()) {
                    continue;
                }
                List<StoreGoodsContents> storeGoodsInfos = storeGoodsContentsMapper.selectByExample(example);
                List<Long> mdmTaskDetailIds = getMdmTaskDetailIds(storeGoodsInfos.size() + 1);
                for (int i = 0; i < storeGoodsInfos.size(); i++) {
                    StoreGoodsContents newStoreGoods = storeGoodsInfos.get(i);
//                    SpuListVo spuListVo = spuListVoMap.get(newStoreGoods.getGoodsNo());
//                    if (null == spuListVo) {
//                        logger.info("查无此商品编码:{}", newStoreGoods.getGoodsNo());
//                        continue;
//                    }
                    MdmTaskDetail detail = new MdmTaskDetail();
                    BeanUtils.copyProperties(new CommonUserDTO(userDTO), detail);
                    detail.setId(mdmTaskDetailIds.get(i));
                    detail.setTaskId(mdmTaskId);
                    detail.setStoreOrgId(store.getId());
                    detail.setStoreId(store.getOutId());
                    detail.setStoreCode(store.getSapCode());
                    detail.setStoreName(store.getShortName());
                    detail.setGoodsNo(newStoreGoods.getGoodsNo());
                    detail.setNecessaryTag(newStoreGoods.getNecessaryTag());
                    detail.setNecessaryTagName(newStoreGoods.getNecessaryTagName());
                    detail.setMinDisplayQuantity(minDisplayQuantity);
                    detail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
                    details.add(detail);
                }
            }
            if (param.getPushMdmAble() && CollectionUtils.isNotEmpty(details)) {
                Lists.partition(details, Constants.INSERT_MAX_SIZE).forEach(v -> {
                    mdmTaskDetailExtendMapper.batchInsert(v);
                    try {
                        storeContentsFactory.getAssemble(StoreContentBizTypeEnum.NECESSARY.getCode()).pushMdm(v);
                    } catch (Exception e) {
                        logger.info("推送失败");
                        mdmTaskDetailExtendMapper.batchUpdatePushStatus(details.get(0).getTaskId(), details.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                    }
                });
                MdmTask mdmTask = new MdmTask();
                mdmTask.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
                mdmTask.setId(mdmTaskId);
                mdmTask.setDetailCount(details.size());
                mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
            }
            return mdmTaskId;
        } catch (Exception e) {
            logger.error("刷新一店一目失败", e);
            throw e;
        }
    }

    @Override
    public NecessaryDirectionsDTO getDirections(NecessaryDirectionsParam param, TokenUserDTO userDTO) {
        NecessaryDirectionsDTO necessaryDirectionsDTO = new NecessaryDirectionsDTO();
        // 检查传入参数
        if (null == param) {
            throw new AmisBadRequestException("参数param不能为空!");
        }
        String goodsNo = param.getGoodsNo();
        if (StringUtils.isBlank(goodsNo)) {
            throw new AmisBadRequestException("参数商品编码不能为空!");
        }
        Long platformId = param.getPlatformId();
        if (null == platformId) {
            throw new AmisBadRequestException("参数平台Id不能为空!");
        }
        List<Long> companyIds = param.getCompanyId();

        // 集团必备: 平台 + 商品
        NecessaryGroupGoodsExample example = new NecessaryGroupGoodsExample();
        NecessaryGroupGoodsExample.Criteria criteria = example.createCriteria();
        criteria.andPlatformOrgIdEqualTo(platformId);
        criteria.andStatusEqualTo(new Byte("0"));
        criteria.andGoodsNoEqualTo(goodsNo);
        long count = necessaryGroupGoodsMapper.countByExample(example);
        necessaryDirectionsDTO.setGroupCount(count);

        // 平台必备: 平台 + 商品
        NecessaryPlatformGoodsExample example1 = new NecessaryPlatformGoodsExample();
        NecessaryPlatformGoodsExample.Criteria criteria1 = example1.createCriteria();
        criteria1.andPlatformOrgIdEqualTo(platformId);
        criteria1.andStatusEqualTo(new Byte("0"));
        criteria1.andGoodsNoEqualTo(goodsNo);
        count = necessaryPlatformGoodsMapper.countByExample(example1);
        necessaryDirectionsDTO.setPlatformCount(count);

        // 企业必备: 平台/企业 + 商品
        NecessaryCompanyGoodsExample example2 = new NecessaryCompanyGoodsExample();
        NecessaryCompanyGoodsExample.Criteria criteria2 = example2.createCriteria();
        criteria2.andStatusEqualTo(new Byte("0"));
        criteria2.andGoodsNoEqualTo(goodsNo);
        if (CollectionUtils.isEmpty(companyIds)) {
            criteria2.andPlatformOrgIdEqualTo(platformId);
        } else {
            criteria2.andCompanyOrgIdIn(companyIds);
        }
        count = necessaryCompanyGoodsMapper.countByExample(example2);
        necessaryDirectionsDTO.setCompanyCount(count);

        // 店型必备: 平台/企业 + 商品
        NecessaryStoreTypeGoodsExample example3 = new NecessaryStoreTypeGoodsExample();
        NecessaryStoreTypeGoodsExample.Criteria criteria3 = example3.createCriteria();
        criteria3.andStatusEqualTo(new Byte("0"));
        criteria3.andGoodsNoEqualTo(goodsNo);
        if (CollectionUtils.isEmpty(companyIds)) {
            criteria3.andPlatformOrgIdEqualTo(platformId);
        } else {
            criteria3.andCompanyOrgIdIn(companyIds);
        }
        count = necessaryStoreTypeGoodsMapper.countByExample(example3);
        necessaryDirectionsDTO.setStoreTypeCount(count);

        // 店型选配: 平台/企业 + 商品
        NecessaryChooseStoreTypeGoodsExample example4 = new NecessaryChooseStoreTypeGoodsExample();
        NecessaryChooseStoreTypeGoodsExample.Criteria criteria4 = example4.createCriteria();
        criteria4.andStatusEqualTo(new Byte("0"));
        criteria4.andGoodsNoEqualTo(goodsNo);
        if (CollectionUtils.isEmpty(companyIds)) {
            criteria4.andPlatformOrgIdEqualTo(platformId);
        } else {
            criteria4.andCompanyOrgIdIn(companyIds);
        }
        count = necessaryChooseStoreTypeGoodsMapper.countByExample(example4);
        necessaryDirectionsDTO.setStoreChooseCount(count);

        // 单店必备: 平台/企业 + 商品
        NecessarySingleStoreGoodsExample example5 = new NecessarySingleStoreGoodsExample();
        NecessarySingleStoreGoodsExample.Criteria criteria5 = example5.createCriteria();
        criteria5.andStatusEqualTo(new Byte("0"));
        criteria5.andGoodsNoEqualTo(goodsNo);
        if (CollectionUtils.isEmpty(companyIds)) {
            criteria5.andPlatformOrgIdEqualTo(platformId);
        } else {
            criteria5.andCompanyOrgIdIn(companyIds);
        }
        count = necessarySingleStoreGoodsMapper.countByExample(example5);
        necessaryDirectionsDTO.setStoreCount(count);

        return necessaryDirectionsDTO;
    }

    @Override
    public List<OrgInfoBaseCache> getPlatformBusinessInfo(String storeIds) {
        if(StringUtils.isEmpty(storeIds)){
            return new ArrayList<>();
        }
        List<Long> storeIdList = Arrays.stream(storeIds.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        Optional<List<OrgInfoBaseCache>> storeByStoreIdList = CacheVar.getStoreByStoreIdList(storeIdList);
        return storeByStoreIdList.get();
    }

    @Override
    public List<String> importGoods(Long businessId, Byte necessaryTag, MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                throw new AmisBadRequestException("导入文件为空");
            }
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new AmisBadRequestException("导入文件大于2M");
            }
            NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(necessaryTag)).orElseThrow(() -> new AmisBadRequestException("未知的必备层级"));
            if (tagEnum.equals(NecessaryTagEnum.NONE_NECESSARY)) {
                throw new AmisBadRequestException("非法的必备层级");
            }
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put("商品编码", "goodsNo");
            List<CommonGoodsDTO> importGoodsNos = HutoolUtil.excelToList(file.getInputStream(), map, CommonGoodsDTO.class, 1);
            List<String> goodsNos = importGoodsNos.stream().map(CommonGoodsDTO::getGoodsNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (goodsNos.size() > addGoodsMax) {
                throw new AmisBadRequestException("一次操作最多添加" + addGoodsMax + "条商品");
            }
            if (tagEnum.ordinal() <= NecessaryTagEnum.PLATFORM_NECESSARY.ordinal()) {
                Map<String, SpuListVo> spuVOMap = searchService.getSpuVOMap(goodsNos);
                return Lists.newArrayList(spuVOMap.keySet());
            } else {
                if (null == businessId) {
                    throw new AmisBadRequestException("请选择连锁");
                }
                SpuNewParamVo paramVo = new SpuNewParamVo();
                paramVo.setBusinessId(businessId);
                paramVo.setGoodsNoList(goodsNos);
                Map<String, SpuNewVo> newSpuMap = searchService.getNewSpuMap(paramVo);
                return Lists.newArrayList(newSpuMap.keySet());
            }
        } catch (AmisBadRequestException e) {
            throw e;
        }catch (Exception e) {
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public SelectorResult getStoreTypeList(Long platformId, Byte necessaryTag, Long companyOrgId, String citys, TokenUserDTO userDTO) {
        NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(necessaryTag)).orElseThrow(() -> new AmisBadRequestException("未知的必备层级"));
        Optional.ofNullable(platformId).orElseThrow(() -> new AmisBadRequestException("平台不能为空"));
        List<MdmStoreExDTO> storeInfos = new ArrayList<>();
        List<OptionDto> optionDtos = new ArrayList<>();
        switch (tagEnum) {
            case PLATFORM_NECESSARY:
                storeInfos.addAll(getPlatformStoreInfo(platformId, null, userDTO.getUserId()));
                if (CollectionUtils.isEmpty(storeInfos)) {
                    return new SelectorResult(new ArrayList<>());
                }
                for(MdmStoreExDTO store : storeInfos) {
                    if (StringUtils.isNotBlank(store.getPlatStoreTypeCode()) && StringUtils.isNotBlank(store.getPlatStoreType())) {
                        optionDtos.add(new OptionDto(store.getPlatStoreType(), store.getPlatStoreTypeCode() + "|" + store.getPlatStoreType()));
                    }
                    if (StringUtils.isNotBlank(store.getZsStoreTypeCode()) && StringUtils.isNotBlank(store.getZsStoreType())) {
                        optionDtos.add(new OptionDto(store.getZsStoreType(), store.getZsStoreTypeCode() + "|" + store.getZsStoreType()));
                    }
                    if (StringUtils.isNotBlank(store.getPfStoreTypeCode()) && StringUtils.isNotBlank(store.getPfStoreType())) {
                        optionDtos.add(new OptionDto(store.getPfStoreType(), store.getPfStoreTypeCode() + "|" + store.getPfStoreType()));
                    }
                }
                return new SelectorResult(optionDtos.stream().distinct().collect(Collectors.toList()));
            case STORE_TYPE_NECESSARY:
            case STORE_CHOOSE_NECESSARY:
                Optional.ofNullable(companyOrgId).orElseThrow(() -> new AmisBadRequestException("企业不能为空"));
                if (StringUtils.isBlank(citys)) {
                    throw new AmisBadRequestException("城市不能为空");
                }
                storeInfos.addAll(getPlatformStoreInfo(platformId, Lists.newArrayList(companyOrgId), userDTO.getUserId()));
                if (CollectionUtils.isEmpty(storeInfos)) {
                    return new SelectorResult(new ArrayList<>());
                }
                List<ChildOrgsDTO> childOrgsDTOS = permissionService.listChildOrgAssignedType(Lists.newArrayList(companyOrgId), OrgTypeEnum.STORE.getCode());
                if (CollectionUtils.isEmpty(childOrgsDTOS)) {
                    return new SelectorResult(new ArrayList<>());
                }
                List<Long> children = childOrgsDTOS.get(0).getChildren().stream().map(OrgDTO::getOutId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(children)) {
                    return new SelectorResult(new ArrayList<>());
                }
                List<String> cityList = Lists.newArrayList(StringUtils.split(citys, ","));
                storeInfos = storeInfos.stream().filter(v -> children.contains(v.getStoreId()) && cityList.contains(v.getCity())).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(children)) {
                    return new SelectorResult(new ArrayList<>());
                }
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(storeInfos)) {
                    return new SelectorResult(new ArrayList<>());
                }
                for(MdmStoreExDTO store : storeInfos) {
                    if (StringUtils.isNotBlank(store.getStoreTypeCode()) && StringUtils.isNotBlank(store.getStoreType())) {
                        optionDtos.add(new OptionDto(store.getStoreType(), store.getStoreTypeCode() + "|" + store.getStoreType()));
                    }
                    if (StringUtils.isNotBlank(store.getZsStoreTypeCode()) && StringUtils.isNotBlank(store.getZsStoreType())) {
                        optionDtos.add(new OptionDto(store.getZsStoreType(), store.getZsStoreTypeCode() + "|" + store.getZsStoreType()));
                    }
                    if (StringUtils.isNotBlank(store.getPfStoreTypeCode()) && StringUtils.isNotBlank(store.getPfStoreType())) {
                        optionDtos.add(new OptionDto(store.getPfStoreType(), store.getPfStoreTypeCode() + "|" + store.getPfStoreType()));
                    }
                }
                return new SelectorResult(optionDtos.stream().distinct().collect(Collectors.toList()));
            default:throw new AmisBadRequestException("非法的必备层级");
        }
    }

    @Override
    public void deleteStoreGoodsByStoreCode(List<Long> orgIds, TokenUserDTO userDTO, List<OrgInfoBaseCache> orgInfoBaseCaches) {
        if (CollectionUtils.isEmpty(orgIds)){
            throw new BusinessErrorException("单门店取消必备参数错误");
        }
        try{
            if (CollectionUtils.isEmpty(orgInfoBaseCaches)) {
                Optional<List<OrgInfoBaseCache>> storeByStoreOrgIdList = CacheVar.getStoreByStoreOrgIdList(orgIds);
                if (!storeByStoreOrgIdList.isPresent()) {
                    throw new BusinessErrorException("获取门店编码错误");
                }
                orgInfoBaseCaches = storeByStoreOrgIdList.get();
            }
            for (OrgInfoBaseCache orgInfoBaseCache : orgInfoBaseCaches) {

                StoreGoodsInfoExample storeGoodsInfoExample = new StoreGoodsInfoExample();
                storeGoodsInfoExample.createCriteria().andStoreIdEqualTo(orgInfoBaseCache.getOutId()).andNecessaryTagIn(NecessaryTagEnum.getSixNecessaryTag());
                List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoMapper.selectByExample(storeGoodsInfoExample);
                if (CollectionUtils.isEmpty(storeGoodsInfos)) {
                    continue;
                }
                List<String> goodsNoList = storeGoodsInfos.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList());
                Map<String, SpuListVo> spuMap = new HashMap<>();
                Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> spuMap.putAll(searchService.getSpuVOMap(goodsNoList)));
                MdmTask mdmTask = new MdmTask();
                saveMdmTask(mdmTask, userDTO, storeGoodsInfos,orgInfoBaseCache);
                List<Long> mdmTaskDetailIds = getMdmTaskDetailIds(storeGoodsInfos.size() + 1);
                List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
                for (int i = 0; i < storeGoodsInfos.size(); i++) {
                    StoreGoodsInfo storeGoodsInfo = storeGoodsInfos.get(i);
                    if (Objects.isNull(storeGoodsInfo)) {
                        continue;
                    }
                    MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
                    buildMdmTaskDetail(userDTO, spuMap, mdmTask, mdmTaskDetailIds, i, storeGoodsInfo, mdmTaskDetail,orgInfoBaseCache);
                    mdmTaskDetails.add(mdmTaskDetail);
                }
                if (CollectionUtils.isNotEmpty(mdmTaskDetails)){
                    storeGoodsInfoExtendMapper.batchDel(storeGoodsInfos.stream().map(StoreGoodsInfo::getId).collect(Collectors.toList()),orgInfoBaseCache.getOutId(),userDTO.getName());
                    NecessarySingleStoreGoodsExample example = new NecessarySingleStoreGoodsExample();
                    example.createCriteria().andPlatformOrgIdEqualTo(orgInfoBaseCache.getPlatformOrgId()).andStoreOrgIdEqualTo(orgInfoBaseCache.getId());
                    necessarySingleStoreGoodsMapper.deleteByExample(example);
                    //推送mdm
                    List<List<MdmTaskDetail>> partition = Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE);
                    for(List<MdmTaskDetail> v : partition) {
                        mdmTaskDetailExtendMapper.batchInsert(v);
                        try {
                            pushMdm(v);
                        } catch (Exception e) {
                            logger.warn("推送mdm失败", e);
                            mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), mdmTaskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                        }
                    }
                }else {
                    mdmTaskMapper.deleteByPrimaryKey(mdmTask.getId());
                }
            }
        }catch (Exception e){
            logger.error("单门店取消必备失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public String importCopyStore(MultipartFile file, TokenUserDTO userDTO) {
        try {
            RBucket<CommonProcessDTO<List<ImportStoreDTO>>> bucket = redissonClient.getBucket(BATCH_IMPORT_COPY_STORE + userDTO.getUserId());
            if(bucket.isExists()) {
                 if (bucket.get().getProcessFinished()) {
                     bucket.delete();
                 } else {
                     throw new BusinessErrorException("你当前有批量任务正在进行,请稍后操作");
                 }
            }
            if (file == null || file.isEmpty()) {
                throw new BusinessErrorException("导入文件为空");
            }
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new BusinessErrorException("导入文件大于2M");
            }
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put("来源门店编码", "sourceStoreCode");
            map.put("目标门店编码", "targetStoreCode");
            List<ImportStoreDTO> importStoreCodes = HutoolUtil.excelToList(file.getInputStream(), map, ImportStoreDTO.class, 1);
            List<ImportStoreDTO> errorList = new ArrayList<>();
            Iterator<ImportStoreDTO> iterator = importStoreCodes.iterator();
            while (iterator.hasNext()) {
                ImportStoreDTO next = iterator.next();
                if (StringUtils.isBlank(next.getSourceStoreCode())) {
                    next.setErrorMsg("第" + next.getLineNum() + "行来源门店编码为空");
                    errorList.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getTargetStoreCode())) {
                    next.setErrorMsg("第" + next.getLineNum() + "行目标门店编码为空");
                    errorList.add(next);
                    iterator.remove();
                    continue;
                }
                next.setSourceStoreCode(next.getSourceStoreCode().trim());
                next.setTargetStoreCode(next.getTargetStoreCode().trim());
            }
            if (CollectionUtils.isEmpty(importStoreCodes)) {
                throw new BusinessErrorException("导入文件为空");
            }
            if (importStoreCodes.size() > Constants.FEIGN_ONCE_QUERY_MAX) {
                throw new BusinessErrorException("批量操作最多支持" + Constants.FEIGN_ONCE_QUERY_MAX + "行，已超出，系统无法处理。");
            }
            CommonProcessDTO<List<ImportStoreDTO>> processDTO = new CommonProcessDTO<>();
            processDTO.setT(errorList);
            processDTO.setProcessCount(importStoreCodes.size());
            processDTO.setErrorCount(errorList.size());
            bucket.set(processDTO, 1L, TimeUnit.HOURS);
            Map<String, List<ImportStoreDTO>> importMap = importStoreCodes.stream().collect(Collectors.groupingBy(ImportStoreDTO::getSourceStoreCode));
            MdmTask mdmTask = new MdmTask();
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), mdmTask);
            mdmTask.setTaskSource(MdmTaskSourceEnum.STORE_COPY.getCode());
            mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            mdmTask.setDetailCount(0);
            mdmTaskMapper.insertSelective(mdmTask);
            Set<String> distinctSet = new HashSet<>();
            asyncTaskExecutor.execute(() -> {
                    for (ImportStoreDTO storeDTO : importStoreCodes) {
                        try {
                            Optional<OrgInfoBaseCache> sourceStore = CacheVar.getStoreBySapCode(storeDTO.getSourceStoreCode());
                            Optional<OrgInfoBaseCache> targetStore = CacheVar.getStoreBySapCode(storeDTO.getTargetStoreCode());
                            if (!sourceStore.isPresent()) {
                                logger.info("来源门店编码:{}没查询到对应门店", storeDTO.getSourceStoreCode());
                                errorProcess(bucket, errorList, processDTO, storeDTO, "来源门店编码没查询到对应门店");
                                continue;
                            }
                            if (!targetStore.isPresent()) {
                                logger.info("目标门店编码:{}没查询到对应门店", storeDTO.getTargetStoreCode());
                                errorProcess(bucket, errorList, processDTO, storeDTO, "目标门店编码没查询到对应门店");
                                continue;
                            }
                            int perSize = distinctSet.size();
                            distinctSet.add(storeDTO.getSourceStoreCode() + storeDTO.getTargetStoreCode());
                            if (perSize == distinctSet.size()) {
                                errorProcess(bucket, errorList, processDTO, storeDTO, "该行重复");
                                continue;
                            }
                            StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                            StoreGoodsInfoExample.Criteria criteria = example.createCriteria();
                            criteria.andStoreIdEqualTo(sourceStore.get().getOutId()).andNecessaryTagNotEqualTo(NecessaryTagEnum.NONE_NECESSARY.getCode()).andEffectStatusEqualTo(StoreGoodsEffectStatusEnum.YES.getCode()).andSubCategoryIdNotBetween(12020000L, 12029999L);
                            Long count = storeGoodsInfoMapper.countByExample(example);
                            if (count <= 0L) {
                                logger.info("来源门店编码:{}没有查询到数据", storeDTO.getSourceStoreCode());
                                errorProcess(bucket, errorList, processDTO, storeDTO, "来源门店没有查询到待复制数据");
                                continue;
                            }
                            // 把目标门店的必备状态全部改成0
                            StoreGoodsInfoExample deleteExample = new StoreGoodsInfoExample();
                            deleteExample.createCriteria().andStoreIdEqualTo(targetStore.get().getOutId()).andNecessaryTagNotEqualTo(NecessaryTagEnum.NONE_NECESSARY.getCode());
                            Long delCount = storeGoodsInfoMapper.countByExample(deleteExample);
                            if (delCount > 0L) {
                                List<StoreGoodsInfo> oldList = storeGoodsInfoMapper.selectByExample(deleteExample);
                                storeGoodsInfoExtendMapper.updateNecessaryTagByIds(targetStore.get().getOutId(), oldList.stream().map(StoreGoodsInfo::getId).collect(Collectors.toList()), NecessaryTagEnum.NONE_NECESSARY.getCode(), BigDecimal.ZERO);
                                Iterator<StoreGoodsInfo> oldIter = oldList.iterator();
                                while (oldIter.hasNext()) {
                                    StoreGoodsInfo next = oldIter.next();
                                    if (StoreGoodsEffectStatusEnum.NO.getCode() == next.getEffectStatus()) {
                                        oldIter.remove();
                                        continue;
                                    }
                                    next.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
                                    next.setMinDisplayQuantity(BigDecimal.ZERO);
                                }
                                if (CollectionUtils.isNotEmpty(oldList)) {
                                    AtomicInteger index = new AtomicInteger(0);
                                    List<Long> ids = getStoreGoodsIds(oldList.size());
                                    List<Long> taskIds = getMdmTaskDetailIds(oldList.size());
                                    Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(oldList.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList()));
                                    List<Long> oldTaskIds = getMdmTaskDetailIds(oldList.size());
                                    List<MdmTaskDetail> oldTaskDetails = oldList.stream().map(v -> getMdmTaskDetail(targetStore.get(), index, mdmTask, spuMap, oldTaskIds, v, userDTO)).collect(Collectors.toList());
                                    mdmTask.setDetailCount(mdmTask.getDetailCount() + oldTaskDetails.size());
                                    mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                                    List<List<MdmTaskDetail>> partition = Lists.partition(oldTaskDetails, Constants.INSERT_MAX_SIZE);
                                    for (List<MdmTaskDetail> v : partition) {
                                        mdmTaskDetailExtendMapper.batchInsert(v);
                                        try {
                                            pushMdm(v);
                                        } catch (Exception e) {
                                            mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                                        }
                                    }
                                }
                            }
                            List<Long> ids = getStoreGoodsIds(count.intValue());
                            List<Long> taskIds = getMdmTaskDetailIds(count.intValue());
                            int loopSize = (count.intValue() / Constants.INSERT_MAX_VALUE) + 1;
                            AtomicInteger index = new AtomicInteger(0);
                            AtomicInteger taskIndex = new AtomicInteger(0);
                            mdmTask.setDetailCount(mdmTask.getDetailCount() + count.intValue());
                            mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                            for (int i = 0; i <= loopSize; i++) {
                                example.setLimit(Constants.INSERT_MAX_VALUE);
                                example.setOffset(Long.valueOf(i * Constants.INSERT_MAX_VALUE));
                                List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoMapper.selectByExample(example);
                                if (CollectionUtils.isEmpty(storeGoodsInfos)) {
                                    break;
                                }
                                // 删除目标门店的对应商品
                                deleteExample.clear();
                                deleteExample.createCriteria().andStoreIdEqualTo(targetStore.get().getOutId()).andGoodsNoIn(storeGoodsInfos.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList()));
                                storeGoodsInfoMapper.deleteByExample(deleteExample);

                                List<StoreGoodsInfo> newList = storeGoodsInfos.stream().map(v -> {
                                    v.setId(ids.get(index.getAndIncrement()));
                                    v.setStoreOrgId(targetStore.get().getId());
                                    v.setStoreId(targetStore.get().getOutId());
                                    v.setStoreCode(targetStore.get().getSapCode());
                                    v.setCreatedBy(userDTO.getUserId());
                                    v.setCreatedName(userDTO.getName());
                                    v.setUpdatedBy(userDTO.getUserId());
                                    v.setUpdatedName(userDTO.getName());
                                    v.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                                    return v;
                                }).collect(Collectors.toList());
                                storeGoodsInfoExtendMapper.batchInsert(newList);
                                Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(newList.stream().map(StoreGoodsInfo::getGoodsNo).distinct().collect(Collectors.toList()));
                                List<MdmTaskDetail> mdmTaskDetails = storeGoodsInfos.stream().map(v -> {
                                    MdmTaskDetail taskDetail = getMdmTaskDetail(targetStore.get(), taskIndex, mdmTask, spuMap, taskIds, v, userDTO);
                                    taskDetail.setStoreOrgId(targetStore.get().getId());
                                    taskDetail.setStoreId(targetStore.get().getOutId());
                                    taskDetail.setStoreCode(targetStore.get().getSapCode());
                                    taskDetail.setGoodsNo(v.getGoodsNo());
                                    return taskDetail;
                                }).collect(Collectors.toList());
                                mdmTaskDetailExtendMapper.batchInsert(mdmTaskDetails);
                                try {
                                    pushMdm(mdmTaskDetails);
                                } catch (Exception e) {
                                    mdmTaskDetailExtendMapper.batchUpdatePushStatus(mdmTask.getId(), mdmTaskDetails.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                                }
                            }
                            processDTO.setPassCount(processDTO.getPassCount() + 1);
                            bucket.set(processDTO, 1L, TimeUnit.HOURS);
                        } catch (Exception e) {
                            processDTO.setErrorCount(processDTO.getErrorCount() + 1);
                            if (processDTO.getPassCount() + processDTO.getErrorCount() == processDTO.getProcessCount()) {
                                processDTO.setProcessFinished(true);
                            }
                            bucket.set(processDTO, 1L, TimeUnit.HOURS);
                        }
                }
                mdmTask.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
                mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                processDTO.setProcessFinished(true);
                bucket.set(processDTO, 1L, TimeUnit.HOURS);
            });
            return "更新MDM任务创建成功，任务号：" + mdmTask.getId() + "，更新结果可去更新MDM任务管理模块查看";
        } catch (Exception e) {
            logger.error("门店复制-导入门店失败", e);
            throw new BusinessErrorException(e.getMessage());
        }
    }

    @Override
    public CommonProcessDTO<List<ImportStoreDTO>> getImportProcess(TokenUserDTO userDTO) {
        RBucket<CommonProcessDTO<List<ImportStoreDTO>>> bucket = redissonClient.getBucket(BATCH_IMPORT_COPY_STORE + userDTO.getUserId());
        if (bucket.isExists()) {
            CommonProcessDTO<List<ImportStoreDTO>> commonProcessDTO = bucket.get();
            if (commonProcessDTO.getProcessFinished()) {
                bucket.delete();
            }
            return commonProcessDTO;
        } else {
            CommonProcessDTO<List<ImportStoreDTO>> commonProcessDTO = new CommonProcessDTO<>();
            commonProcessDTO.setProcessFinished(true);
            commonProcessDTO.setT(new ArrayList<>());
            return commonProcessDTO;
        }
    }

    @Override
    public String batchImportGoods(MultipartFile file, Byte necessaryTag, Long platformId, Long companyOrgId, String city, TokenUserDTO userDTO) {
        try {
            if (file == null || file.isEmpty()) {
                throw new AmisBadRequestException("导入文件为空");
            }
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new AmisBadRequestException("导入文件大于2M");
            }
            NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(necessaryTag)).orElseThrow(() -> new AmisBadRequestException("未知的必备层级"));
            if (!CacheVar.getPlatformByOrgId(platformId).isPresent()) {
                new AmisBadRequestException("平台不能为空");
            }
            if (!CacheVar.getBusinessByOrgId(companyOrgId).isPresent()) {
                new AmisBadRequestException("企业不能为空");
            }
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put("商品编码", "goodsNo");
            map.put("店型", "storeType");
            map.put("门店编码", "storeCode");

            List<NecessaryBatchImportDTO> importGoodsNos = HutoolUtil.excelToList(file.getInputStream(), map, NecessaryBatchImportDTO.class, 2);

            RBucket<CommonProcessDTO<List<BatchImportRes>>> bucket = redissonClient.getBucket(getBatchImportCacheKey(platformId, companyOrgId, city, necessaryTag, userDTO));
            if (bucket.isExists() && !bucket.get().getProcessFinished()) {
                throw new AmisBadRequestException("导入任务正在执行中");
            }
            List<BatchImportRes> msgs = new ArrayList<>();
            CommonProcessDTO<List<BatchImportRes>> process = new CommonProcessDTO();
            process.setProcessFinished(false);
            process.setT(msgs);

            if (tagEnum.equals(NecessaryTagEnum.STORE_TYPE_NECESSARY)) {
                if (importGoodsNos.size() > Constants.INSERT_MAX_VALUE) {
                    throw new AmisBadRequestException("导入数据行数超过最大值" + Constants.INSERT_MAX_VALUE + "行");
                }
                if (StringUtils.isBlank(city)) {
                    throw new AmisBadRequestException("城市不能为空");
                }
                RuleParam param = new RuleParam();
                param.setScopeCode("TaskCreate");
                Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(param, null);
                // 中参必备店型
                Map<String, String> zsStoreGroup = ruleEnum.get("ZsStoreGroup").stream().collect(Collectors.toMap(OptionDto::getLabel, OptionDto::getValue, (k1, k2) -> k1));
                // 组货必备店型
                Map<String, String> storeGroup = ruleEnum.get("StoreGroup").stream().collect(Collectors.toMap(OptionDto::getLabel, OptionDto::getValue, (k1, k2) -> k1));
                // 配方必备店型
                Map<String, String> pfStoreGroup = ruleEnum.get("PfStoreGroup").stream().collect(Collectors.toMap(OptionDto::getLabel, OptionDto::getValue, (k1, k2) -> k1));
                storeGroup.putAll(zsStoreGroup);
                storeGroup.putAll(pfStoreGroup);
                Set<String> goodsSet = new HashSet<>();
                for (NecessaryBatchImportDTO goods : importGoodsNos) {
                    if (StringUtils.isBlank(goods.getGoodsNo()) || StringUtils.isBlank(goods.getStoreType())) {
                        throw new AmisBadRequestException("导入数据中存在空数据，请检查。");
                    }
                    if (!storeGroup.containsKey(goods.getStoreType())) {
                        throw new AmisBadRequestException("导入数据中存在未知的店型" + goods.getStoreType() + "，请检查。");
                    }
                    int perSize = goodsSet.size();
                    goodsSet.add(goods.getStoreType().trim()+goods.getGoodsNo().trim());
                    if (perSize == goodsSet.size()) {
                        throw new AmisBadRequestException("导入数据中存在重复行，请检查。");
                    }
                }
                asyncTaskExecutor.execute(() -> {
                    logger.info("开始创建批导任务 platformId={}, companyOrgId={}, city={}, necessaryTag={}", platformId, companyOrgId, city, necessaryTag);
                    bucket.set(process, 1L, TimeUnit.DAYS);
                    importGoodsNos.stream().collect(Collectors.groupingBy(NecessaryBatchImportDTO::getStoreType)).forEach((k,v) -> {
                        List<String> goodsNos = v.stream().map(NecessaryBatchImportDTO::getGoodsNo).collect(Collectors.toList());
                        NecessaryAddParam addParam = new NecessaryAddParam();
                        addParam.setPlatformOrgId(platformId);
                        addParam.setGoodsNos(goodsNos);
                        addParam.setExistsIgnore(false);
                        addParam.setNecessaryTag(tagEnum.getCode());
                        addParam.setStoreTypes(Lists.newArrayList(storeGroup.get(k)));
                        addParam.setStoreTypeNames(Lists.newArrayList(k));
                        addParam.setCompanyOrgId(companyOrgId);
                        addParam.setCitys(Lists.newArrayList(city));
                        addParam.setGoodsBlock(false);
                        addParam.setCheckGoodsQty(false);
                        logger.info("storeType:{},goodsNos:{},addParam:{}", k, JSON.toJSONString(goodsNos), JSON.toJSONString(addParam));
                        try {
                            String result = addNecessaryGoods(userDTO, addParam);
                            if (StringUtils.isNotBlank(result) && !result.startsWith("更新MDM任务创建成功")) {
                                msgs.add(new BatchImportRes("店型:" + k + "导入失败:" + result.replace("是否排除掉重复商品，继续添加？", "")));
                            }
                            logger.info("storeType:{},goodsNos:{},result:{}", k, JSON.toJSONString(goodsNos), result);
                        } catch (Exception e) {
                            msgs.add(new BatchImportRes("店型:" + k + "导入失败:" + e.getMessage()));
                        }
                    });
                    process.setT(msgs);
                    process.setProcessFinished(true);
                    bucket.set(process, 10L, TimeUnit.MINUTES);
                    logger.info("创建批导任务完毕 platformId={}, companyOrgId={}, city={}, necessaryTag={}", platformId, companyOrgId, city, necessaryTag);
                });
            } else if (tagEnum.equals(NecessaryTagEnum.SINGLE_STORE_NECESSARY)) {
                if (importGoodsNos.size() > Constants.INSERT_MAX_VALUE * 20) {
                    throw new AmisBadRequestException("导入数据行数超过最大值" + Constants.INSERT_MAX_VALUE * 20 + "行");
                }
                Set<String> goodsSet = new HashSet<>();
                List<String> storeCodes = importGoodsNos.stream().map(NecessaryBatchImportDTO::getStoreCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(storeCodes)) {
                    throw new AmisBadRequestException("导入数据中存在空数据，请检查。");
                }
                Map<String, OrgInfoBaseCache> storeMap = CacheVar.getStoreByStoreCodeList(storeCodes).orElseThrow(() -> new AmisBadRequestException("导入数据中门店编码:[" + JSON.toJSONString(storeCodes) + "]不存在，请检查。")).stream().collect(Collectors.toMap(v -> v.getSapCode(), Function.identity(), (k1, k2) -> k1));
                for (NecessaryBatchImportDTO goods : importGoodsNos) {
                    if (StringUtils.isBlank(goods.getGoodsNo()) || StringUtils.isBlank(goods.getStoreCode())) {
                        throw new AmisBadRequestException("导入数据中存在空数据，请检查。");
                    }
                    int perSize = goodsSet.size();
                    goodsSet.add(goods.getStoreCode().trim()+goods.getGoodsNo().trim());
                    if (perSize == goodsSet.size()) {
                        throw new AmisBadRequestException("导入数据中存在重复行，请检查。");
                    }
                    if (!storeMap.containsKey(goods.getStoreCode())) {
                        throw new AmisBadRequestException("导入数据中门店编码:[" + goods.getStoreCode() + "]不存在，请检查。");
                    }
                }
                asyncTaskExecutor.execute(() -> {
                    logger.info("开始创建批导任务 platformId={}, companyOrgId={}, city={}, necessaryTag={}", platformId, companyOrgId, city, necessaryTag);
                    bucket.set(process, 1, TimeUnit.DAYS);
                    importGoodsNos.stream().collect(Collectors.groupingBy(NecessaryBatchImportDTO::getStoreCode)).forEach((k,v) -> {
                        List<String> goodsNos = v.stream().map(NecessaryBatchImportDTO::getGoodsNo).collect(Collectors.toList());
                        NecessaryAddParam addParam = new NecessaryAddParam();
                        addParam.setPlatformOrgId(platformId);
                        addParam.setGoodsNos(goodsNos);
                        addParam.setExistsIgnore(false);
                        addParam.setNecessaryTag(tagEnum.getCode());
                        addParam.setCompanyOrgId(companyOrgId);
                        addParam.setStoreOrgIds(Lists.newArrayList(storeMap.get(k).getId()));
                        addParam.setGoodsBlock(false);
                        addParam.setCheckGoodsQty(false);
                        logger.info("storeCode:{},goodsNos:{},addParam:{}", k, JSON.toJSONString(goodsNos), JSON.toJSONString(addParam));
                        try {
                            String result = addNecessaryGoods(userDTO, addParam);
                            if (StringUtils.isNotBlank(result) && !result.startsWith("更新MDM任务创建成功")) {
                                msgs.add(new BatchImportRes("门店:" + k + "导入失败:" + result.replace("是否排除掉重复商品，继续添加？", "")));
                            }
                            logger.info("storeCode:{},goodsNos:{},result:{}", k, JSON.toJSONString(goodsNos), result);
                        } catch (Exception e) {
                            msgs.add(new BatchImportRes("门店:" + k + "导入失败:" + e.getMessage()));
                        }
                    });
                    process.setT(msgs);
                    process.setProcessFinished(true);
                    bucket.set(process, 10L, TimeUnit.MINUTES);
                    logger.info("创建批导任务完毕 platformId={}, companyOrgId={}, city={}, necessaryTag={}", platformId, companyOrgId, city, necessaryTag);
                });
            } else {
                throw new AmisBadRequestException(tagEnum.getMessage() + "不支持批导");
            }

        } catch (Exception e) {
            logger.error("批量导入失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
        return "正在导入,请稍后查看MDM任务";
    }

    @Override
    public CommonProcessDTO getBatchImportProcess(Long platformId, Long companyOrgId, String city, Byte necessaryTag, TokenUserDTO userDTO) {
        RBucket<CommonProcessDTO<List<BatchImportRes>>> bucket = redissonClient.getBucket(getBatchImportCacheKey(platformId, companyOrgId, city, necessaryTag, userDTO));
        CommonProcessDTO<List<BatchImportRes>> processDTO = null;
        if (bucket.isExists()) {
            processDTO = bucket.get();
            if (bucket.get().getProcessFinished()) {
                bucket.delete();
            }
        } else {
            processDTO = new CommonProcessDTO();
            processDTO.setProcessFinished(true);
            processDTO.setT(new ArrayList<>());
        }
        return processDTO;
    }

    private String getBatchImportCacheKey(Long platformId, Long companyOrgId, String city, Byte necessaryTag, TokenUserDTO userDTO) {
        if (necessaryTag == NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode()) {
            return BATCH_IMPORT_CACHE + platformId + "-" + companyOrgId + "-" + city + "-" + necessaryTag + "-" + userDTO.getUserId();
        } else {
            return BATCH_IMPORT_CACHE + platformId + "-" + companyOrgId + "-" + necessaryTag + "-" + userDTO.getUserId();
        }
    }

    private void errorProcess(RBucket<CommonProcessDTO<List<ImportStoreDTO>>> bucket, List<ImportStoreDTO> errorList, CommonProcessDTO<List<ImportStoreDTO>> processDTO, ImportStoreDTO storeDTO, String errorMsg) {
        storeDTO.setErrorMsg(errorMsg);
        errorList.add(storeDTO);
        processDTO.setErrorCount(processDTO.getErrorCount() + 1);
        bucket.set(processDTO, 1L, TimeUnit.HOURS);
    }

    private void buildMdmTaskDetail(TokenUserDTO userDTO, Map<String, SpuListVo> spuMap, MdmTask mdmTask, List<Long> mdmTaskDetailIds, int i, StoreGoodsInfo storeGoodsInfo, MdmTaskDetail mdmTaskDetail,OrgInfoBaseCache orgInfoBaseCache) {
        mdmTaskDetail.setId(mdmTaskDetailIds.get(i));

        mdmTaskDetail.setTaskId(mdmTask.getId());
        mdmTaskDetail.setGoodsNo(storeGoodsInfo.getGoodsNo());
        SpuListVo spuListVo = spuMap.get(storeGoodsInfo.getGoodsNo());
        if (Objects.nonNull(spuListVo)) {
            mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
            mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
            mdmTaskDetail.setDescription(spuListVo.getDescription());
            mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
            mdmTaskDetail.setBarCode(spuListVo.getBarCode());
            mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
            mdmTaskDetail.setGoodsName(spuListVo.getName());
            mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
            mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
        }
        mdmTaskDetail.setUpdatedBy(userDTO.getUserId());
        mdmTaskDetail.setUpdatedName(userDTO.getUserName());
        mdmTaskDetail.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
        mdmTaskDetail.setStoreId(storeGoodsInfo.getStoreId());
        mdmTaskDetail.setStoreCode(storeGoodsInfo.getStoreCode());
        Optional<OrgInfoBaseCache> storeByStoreId = CacheVar.getStoreByStoreId(storeGoodsInfo.getStoreId());
        storeByStoreId.ifPresent(infoBaseCache -> mdmTaskDetail.setStoreName(infoBaseCache.getShortName()));
        if (Objects.isNull(mdmTaskDetail.getStoreName())){
            mdmTaskDetail.setStoreName(orgInfoBaseCache.getShortName());
        }
        mdmTaskDetail.setNecessaryTag(Integer.valueOf(NecessaryTagEnum.NONE_NECESSARY.getCode()));
        mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
        mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
        mdmTaskDetail.setGmtUpdate(new Date());
    }

    private void saveMdmTask(MdmTask mdmTask,TokenUserDTO userDTO, List<StoreGoodsInfo> storeGoodsInfos,OrgInfoBaseCache orgInfoBaseCache) {
        mdmTask.setTaskSource(MdmTaskSourceEnum.SINGLE_STORE_GOODS_REMOVE.getCode());
        mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
        mdmTask.setDetailCount(storeGoodsInfos.size());
        mdmTask.setStatus(Constants.NORMAL_STATUS);
        mdmTask.setRemarks(orgInfoBaseCache.getOutId().toString());
        mdmTask.setUpdatedName(userDTO.getName());
        mdmTask.setUpdatedBy(userDTO.getUserId());
        mdmTask.setGmtUpdate(new Date());
        mdmTask.setCreatedBy(userDTO.getUserId());
        mdmTask.setCreatedName(userDTO.getName());
        mdmTask.setGmtCreate(new Date());
        mdmTaskMapper.insertSelective(mdmTask);
    }

    @Override
    public List<InitImportNecessaryDTO> initNecessary(TokenUserDTO userDTO, Byte necessaryTag, MultipartFile file) {
        List<InitImportNecessaryDTO> errorList = new ArrayList<>();
        try {
            List<InitImportNecessaryDTO> importList = HutoolUtil.excelToList(file.getInputStream(), InitImportNecessaryDTO.initNecessaryFieldMap(), InitImportNecessaryDTO.class, 1);
            if (CollectionUtils.isEmpty(importList)) {
                throw new BusinessErrorException("没有需要初始化的数据");
            }
            userDTO.setUserId(Constants.SYS_USER_ID);
            userDTO.setName(Constants.SYS_USER_NAME);
            NecessaryTagEnum tagEnum = Optional.ofNullable(NecessaryTagEnum.getEnumByCode(necessaryTag)).orElseThrow(() -> new BusinessErrorException("未知的必备层级"));
            Iterator<InitImportNecessaryDTO> iterator = importList.iterator();
            RuleParam param = new RuleParam();
            param.setScopeCode("TaskCreate");
            Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(param, null);
            // 平台必备店型
            Map<String, String> platStoreGroup = ruleEnum.get("PlatStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
            // 中参必备店型
            Map<String, String> zsStoreGroup = ruleEnum.get("ZsStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
            // 组货必备店型
            Map<String, String> storeGroup = ruleEnum.get("StoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
            // 配方必备店型
            Map<String, String> pfStoreGroup = ruleEnum.get("PfStoreGroup").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));

            Map<String, String> storeTypeMap = new HashMap<>();
            while (iterator.hasNext()) {
                InitImportNecessaryDTO next = iterator.next();
                if (null == next.getPlatformOrgId()) {
                    next.setErrorMsg("平台orgid不能为空");
                    errorList.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getGoodsNo())) {
                    next.setErrorMsg("商品不能为空");
                    errorList.add(next);
                    iterator.remove();
                    continue;
                }
                next.setGoodsNo(next.getGoodsNo().trim());
                if (tagEnum.equals(NecessaryTagEnum.PLATFORM_NECESSARY)) {
                    if (StringUtils.isBlank(next.getStoreType())) {
                        next.setErrorMsg("店型不能为空");
                        errorList.add(next);
                        iterator.remove();
                        continue;
                    }
                    storeTypeMap.putAll(platStoreGroup);
                    storeTypeMap.putAll(zsStoreGroup);
                    storeTypeMap.putAll(pfStoreGroup);
                    if(!storeTypeMap.containsKey(next.getStoreType().trim())) {
                        next.setErrorMsg("店型不存在");
                        errorList.add(next);
                        iterator.remove();
                        continue;
                    }
                    next.setStoreType(next.getStoreType().trim());
                }
                if (tagEnum.equals(NecessaryTagEnum.STORE_TYPE_NECESSARY) || tagEnum.equals(NecessaryTagEnum.STORE_CHOOSE_NECESSARY) || tagEnum.equals(NecessaryTagEnum.SINGLE_STORE_NECESSARY)) {
                    if (StringUtils.isBlank(next.getStoreType())) {
                        next.setErrorMsg("店型不能为空");
                        errorList.add(next);
                        iterator.remove();
                        continue;
                    }
                    storeTypeMap.putAll(storeGroup);
                    storeTypeMap.putAll(zsStoreGroup);
                    storeTypeMap.putAll(pfStoreGroup);
                    if(!storeTypeMap.containsKey(next.getStoreType().trim())) {
                        next.setErrorMsg("店型不存在");
                        errorList.add(next);
                        iterator.remove();
                        continue;
                    }
                    next.setStoreType(next.getStoreType().trim());
                }
                if (tagEnum.equals(NecessaryTagEnum.COMPANY_NECESSARY) || tagEnum.equals(NecessaryTagEnum.STORE_CHOOSE_NECESSARY) || tagEnum.equals(NecessaryTagEnum.STORE_TYPE_NECESSARY)|| tagEnum.equals(NecessaryTagEnum.SINGLE_STORE_NECESSARY)) {
                    if (StringUtils.isBlank(next.getCity())) {
                        next.setErrorMsg("城市不能为空");
                        errorList.add(next);
                        iterator.remove();
                        continue;
                    }
                    next.setCity(next.getCity().trim());
                    if (Objects.isNull(next.getCompanyOrgId())) {
                        next.setErrorMsg("企业orgid不能为空");
                        errorList.add(next);
                        iterator.remove();
                        continue;
                    }
                } else  {
                    next.setCompanyOrgId(null);
                }
            }
            if (CollectionUtils.isEmpty(importList)) {
                logger.info("errorList:{}", JSON.toJSONString(errorList));
                return errorList;
            }
            Iterator<InitImportNecessaryDTO> checkIterator = importList.iterator();
            List<String> goodsNoList = importList.stream().map(InitImportNecessaryDTO::getGoodsNo).distinct().collect(Collectors.toList());
            Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(goodsNoList);
            if (MapUtils.isEmpty(spuMap)) {
                throw new BusinessErrorException("没有查询到商品信息");
            }
            Map<String, Map<String, String>> componentMap = forestService.querySpuProperties(Lists.newArrayList("component"), goodsNoList, null);
            Map<Long, CommonCategoryDTO> categoryMap = new HashMap<>();
            categoryMap.putAll(getCategoryBySubIds(spuMap.values().stream().map(goo -> Long.valueOf(goo.getCategoryId())).distinct().collect(Collectors.toList())));
            List<NecessaryCommonDTO> insertList = new ArrayList<>();
            while (checkIterator.hasNext()) {
                InitImportNecessaryDTO next = checkIterator.next();
                Optional<OrgInfoBaseCache> platform = CacheVar.getPlatformByOrgId(next.getPlatformOrgId());
                if (!platform.isPresent()) {
                    next.setErrorMsg("查无此平台orgid");
                    errorList.add(next);
                    checkIterator.remove();
                    continue;
                }
                SpuListVo spuListVo = spuMap.get(next.getGoodsNo());
                if (Objects.isNull(spuListVo)) {
                    next.setErrorMsg("查无此商品");
                    errorList.add(next);
                    checkIterator.remove();
                    continue;
                }
                NecessaryCommonDTO goodsDTO = new NecessaryCommonDTO();
                CommonGoodsDTO commonGoodsDTO = searchService.getCommonGoods(spuListVo);
                BeanUtils.copyProperties(commonGoodsDTO, goodsDTO);
                CommonUserDTO commonUserDTO = new CommonUserDTO(userDTO);
                BeanUtils.copyProperties(commonUserDTO, goodsDTO);
                CommonCategoryDTO commonCategoryDTO = categoryMap.get(Long.valueOf(spuListVo.getCategoryId()));
                if (Objects.isNull(commonCategoryDTO)) {
                    next.setErrorMsg("商品没有查询到四级类目信息");
                    errorList.add(next);
                    checkIterator.remove();
                    continue;
                }
                BeanUtils.copyProperties(commonCategoryDTO, goodsDTO);
                goodsDTO.setPlatformOrgId(platform.get().getPlatformOrgId());
                goodsDTO.setPlatformName(platform.get().getPlatformShortName());
                goodsDTO.setNecessaryTag(tagEnum.getCode());
                goodsDTO.setPurchaseAttr(next.getPurchaseAttr());
                // 成分 标品属性 component
                Map<String, String> component = componentMap.get(next.getGoodsNo());
                if (MapUtils.isNotEmpty(component)) {
                    goodsDTO.setComposition(component.get("component"));
                }
                if (Objects.nonNull(next.getCompanyOrgId())) {
                    Optional<OrgInfoBaseCache> company = CacheVar.getBusinessByOrgId(next.getCompanyOrgId());
                    if (!company.isPresent()) {
                        next.setErrorMsg("企业orgid不能为空");
                        errorList.add(next);
                        checkIterator.remove();
                        continue;
                    }
                    goodsDTO.setBusinessid(company.get().getBusinessId());
                    goodsDTO.setCompanyOrgId(company.get().getBusinessOrgId());
                    goodsDTO.setCompanyCode(company.get().getSapCode());
                    goodsDTO.setCompanyName(company.get().getBusinessShortName());
                }
                if (tagEnum.equals(NecessaryTagEnum.SINGLE_STORE_NECESSARY)) {
                    if (StringUtils.isBlank(next.getStoreCode())) {
                        next.setErrorMsg("门店编码不能为空");
                        errorList.add(next);
                        checkIterator.remove();
                        continue;
                    }
                    Optional<OrgInfoBaseCache> store = CacheVar.getStoreBySapCode(next.getStoreCode().trim());
                    if (!store.isPresent()) {
                        next.setErrorMsg("门店编码不存在");
                        errorList.add(next);
                        checkIterator.remove();
                        continue;
                    }
                    goodsDTO.setBusinessid(store.get().getBusinessId());
                    goodsDTO.setCompanyOrgId(store.get().getBusinessOrgId());
                    goodsDTO.setCompanyCode(store.get().getBusinessSapCode());
                    goodsDTO.setCompanyName(store.get().getBusinessShortName());
                    goodsDTO.setStoreOrgId(store.get().getId());
                    goodsDTO.setStoreCode(store.get().getSapCode());
                    goodsDTO.setStoreName(store.get().getShortName());
                    goodsDTO.setStoreId(store.get().getOutId());
                }

                if (StringUtils.isNotBlank(next.getStoreType())) {
                    goodsDTO.setStoreType(next.getStoreType());
                }
                goodsDTO.setCity(next.getCity());
                insertList.add(goodsDTO);
                checkIterator.remove();
            }
            if (CollectionUtils.isEmpty(insertList)) {
                logger.info("errorList:{}", JSON.toJSONString(errorList));
                throw new BusinessErrorException("补充商品后没数据");
            }
            switch (tagEnum) {
                case GROUP_NECESSARY:
                    List<NecessaryGroupGoods> groupGoodsList = insertList.stream().map(v -> {
                        NecessaryGroupGoods groupGoods = new NecessaryGroupGoods();
                        BeanUtils.copyProperties(v, groupGoods);
                        groupGoods.setNecessaryTag(tagEnum.getCode());
                        groupGoods.setVersion(0);
                        return groupGoods;
                    }).collect(Collectors.toList());
                    Lists.partition(groupGoodsList, 1000).forEach(v -> necessaryGroupGoodsExtendMapper.batchInsert(v));
                    break;
                case PLATFORM_NECESSARY:
                    List<NecessaryPlatformGoods> platformGoodsList = insertList.stream().map(v -> {
                        NecessaryPlatformGoods platformGoods = new NecessaryPlatformGoods();
                        BeanUtils.copyProperties(v, platformGoods);
                        platformGoods.setVersion(0L);
                        return platformGoods;
                    }).collect(Collectors.toList());
                    Lists.partition(platformGoodsList, 1000).forEach(v -> necessaryPlatformGoodsExtendMapper.batchInsert(v));
                    break;
                case COMPANY_NECESSARY:
                    List<NecessaryCompanyGoods> companyGoodsList = insertList.stream().map(v -> {
                        NecessaryCompanyGoods companyGoods = new NecessaryCompanyGoods();
                        BeanUtils.copyProperties(v, companyGoods);
                        companyGoods.setVersion(0L);
                        return companyGoods;
                    }).collect(Collectors.toList());
                    Lists.partition(companyGoodsList, 1000).forEach(v -> necessaryCompanyGoodsExtendMapper.batchInsert(v));
                    break;
                case STORE_TYPE_NECESSARY:
                    List<NecessaryStoreTypeGoods> storeTypeGoodsList = insertList.stream().map(v -> {
                        NecessaryStoreTypeGoods storeTypeGoods = new NecessaryStoreTypeGoods();
                        BeanUtils.copyProperties(v, storeTypeGoods);
                        storeTypeGoods.setVersion(0L);
                        return storeTypeGoods;
                    }).collect(Collectors.toList());
                    Lists.partition(storeTypeGoodsList, 1000).forEach(v -> necessaryStoreTypeGoodsExtendMapper.batchInsert(v));
                    break;
                case STORE_CHOOSE_NECESSARY:
                    List<NecessaryChooseStoreTypeGoods> chooseStoreTypeGoodsList = insertList.stream().map(v -> {
                        NecessaryChooseStoreTypeGoods chooseStoreTypeGoods = new NecessaryChooseStoreTypeGoods();
                        BeanUtils.copyProperties(v, chooseStoreTypeGoods);
                        chooseStoreTypeGoods.setVersion(0L);
                        return chooseStoreTypeGoods;
                    }).collect(Collectors.toList());
                    Lists.partition(chooseStoreTypeGoodsList, 1000).forEach(v -> necessaryChooseStoreTypeGoodsExtendMapper.batchInsert(v));
                    break;
                case SINGLE_STORE_NECESSARY:
                    List<NecessarySingleStoreGoods> singleStoreGoodsList = insertList.stream().map(v -> {
                        NecessarySingleStoreGoods singleStoreGoods = new NecessarySingleStoreGoods();
                        BeanUtils.copyProperties(v, singleStoreGoods);
                        singleStoreGoods.setVersion(0L);
                        return singleStoreGoods;
                    }).collect(Collectors.toList());
                    Lists.partition(singleStoreGoodsList, 1000).forEach(v -> necessarySingleStoreGoodsExtendMapper.batchInsert(v));
                    break;
                default:throw new BusinessErrorException("未知的类型");
            }
            logger.info("errorList:{}", JSON.toJSONString(errorList));
        } catch (Exception e) {
            throw new BusinessErrorException(e.getMessage());
        }
        return errorList;
    }
    private Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        logger.info("subCategoryIds:{}", JSON.toJSONString(subCategoryIds));
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = iscmService.getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1,k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        return resultMap;
    }

}
