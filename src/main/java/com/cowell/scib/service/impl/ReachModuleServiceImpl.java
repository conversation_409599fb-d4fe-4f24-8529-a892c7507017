package com.cowell.scib.service.impl;

import com.cowell.scib.entity.DevelopModule;
import com.cowell.scib.entity.DevelopModuleExample;
import com.cowell.scib.entity.ReachModule;
import com.cowell.scib.entity.ReachModuleExample;
import com.cowell.scib.enums.OperationTypeEnum;
import com.cowell.scib.enums.ScibCommonEnums;
import com.cowell.scib.enums.SelectMethodEnum;
import com.cowell.scib.mapperDgms.DevelopModuleMapper;
import com.cowell.scib.mapperDgms.ReachModuleMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.IReachModuleService;
import com.cowell.scib.service.dto.ReachModuleQueryParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.necessaryContents.ReachModuleDTO;
import com.cowell.scib.service.dto.necessaryContents.RoleDTO;
import com.cowell.scib.service.vo.amis.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReachModuleServiceImpl implements IReachModuleService {
    @Autowired
    private ReachModuleMapper reachModuleMapper;
    @Autowired
    private DevelopModuleMapper developModuleMapper;
    @Autowired
    private RoleCacheServiceImpl roleCacheServiceImpl;

    public PageResult<ReachModuleDTO> getReachModuleList(TokenUserDTO userDTO, ReachModuleQueryParam param) {
        try {
            // 参数校验
            if (param == null) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            // 构建查询条件
            ReachModuleExample example = buildReachModuleExample(param);
            long count = reachModuleMapper.countByExample(example);
            if (count == 0L) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            // 设置分页和排序
            example.setOrderByClause("create_time desc");
            example.setOffset((long) ((Math.max(1, param.getPage()) - 1) * param.getPerPage()));
            example.setLimit(param.getPerPage());
            // 查询模块列表
            List<ReachModule> reachModuleList = reachModuleMapper.selectByExample(example);
            // 预加载所有开发模块信息
            Map<Long, DevelopModule> developModuleMap = loadDevelopModules();
            // 转换为DTO对象
            List<ReachModuleDTO> reachModuleListDto = convertToReachModuleDTOs(reachModuleList, developModuleMap);

            return new PageResult<>(count, reachModuleListDto);
        } catch (Exception e) {
            log.error("查询触达组管理异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    /**
     * 根据查询参数构建查询条件
     */
    private ReachModuleExample buildReachModuleExample(ReachModuleQueryParam param) {
        ReachModuleExample example = new ReachModuleExample();
        ReachModuleExample.Criteria criteria = example.createCriteria();

        // 设置各种查询条件
        if (param.getReachModuleId() != null) {
            criteria.andReachModuleEqualTo(param.getReachModuleId());
        }

        if (StringUtils.isNotEmpty(param.getReachGroupName())) {
            criteria.andReachGroupNameLike("%" + param.getReachGroupName() + "%");
        }

        if (param.getSelectMethod() != null) {
            criteria.andSelectMethodEqualTo(param.getSelectMethod());
        }

        if (StringUtils.isNotEmpty(param.getReachPersonName())) {
            criteria.andReachPersonNamesLike("%" + param.getReachPersonName() + "%");
        }

        if (StringUtils.isNotEmpty(param.getCreatorName())) {
            criteria.andCreatorNameLike("%" + param.getCreatorName() + "%");
        }

        // 只查询正常状态的记录
        criteria.andStatusEqualTo(ScibCommonEnums.StatusEnum.NORMAL.getCode());

        return example;
    }

    /**
     * 加载所有开发模块信息
     */
    private Map<Long, DevelopModule> loadDevelopModules() {
        List<DevelopModule> developModuleList = developModuleMapper.selectByExample(new DevelopModuleExample());
        if (CollectionUtils.isEmpty(developModuleList)) {
            return Collections.emptyMap();
        }

        return developModuleList.stream()
                .collect(Collectors.toMap(
                        module -> ((long) module.getId()),
                        Function.identity(),
                        (existing, replacement) -> existing // 处理可能的键冲突
                ));
    }

    /**
     * 将实体对象转换为DTO对象
     */
    private List<ReachModuleDTO> convertToReachModuleDTOs(List<ReachModule> moduleList, Map<Long, DevelopModule> developModuleMap) {
        if (CollectionUtils.isEmpty(moduleList)) {
            return Collections.emptyList();
        }

        return moduleList.stream()
                .map(module -> convertToReachModuleDTO(module, developModuleMap))
                .collect(Collectors.toList());
    }

    /**
     * 将单个实体对象转换为DTO对象
     */
    private ReachModuleDTO convertToReachModuleDTO(ReachModule module, Map<Long, DevelopModule> developModuleMap) {
        ReachModuleDTO dto = new ReachModuleDTO();
        BeanUtils.copyProperties(module, dto);

        // 设置模块名称
        Long developModuleId = module.getReachModule();
        if (developModuleId != null && developModuleMap.containsKey(developModuleId)) {
            dto.setReachModuleName(developModuleMap.get(developModuleId).getModuleName());
        }

        // 设置选择方法名称
        Integer selectMethod = module.getSelectMethod();
        if (selectMethod != null) {
            SelectMethodEnum methodEnum = SelectMethodEnum.getEnumByCode(selectMethod);
            if (methodEnum != null) {
                dto.setSelectMethodName(methodEnum.getName());
            }
        }

        // 处理触达人员ID列表
        if (StringUtils.isNotBlank(module.getReachPersons())) {
            try {
                List<Long> reachPersonList = Arrays.stream(module.getReachPersons().split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                dto.setReachPersonList(reachPersonList);
            } catch (NumberFormatException e) {
                log.warn("触达人员ID转换异常: {}", module.getReachPersons(), e);
                dto.setReachPersonList(Collections.emptyList());
            }
        }

        // 处理触达人员名称列表
        if (StringUtils.isNotBlank(module.getReachPersonNames())) {
            List<String> reachPersonNameList = Arrays.stream(module.getReachPersonNames().split(","))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            dto.setReachPersonNameList(reachPersonNameList);
        }

        return dto;
    }

    @Override
    public void reachManage(ReachModuleDTO reachModuleDTO, TokenUserDTO userDTO) {
        try {
            // 1. 参数校验
            validateParams(reachModuleDTO);

            // 2. 查询并验证模块是否存在
            verifyModuleExists(reachModuleDTO.getReachModule());

            // 3. 根据操作类型执行相应的操作
            if (OperationTypeEnum.SAVE.getCode() == reachModuleDTO.getOperateType()) {
                saveReachModule(reachModuleDTO, userDTO);
            } else if (OperationTypeEnum.EDIT.getCode() == reachModuleDTO.getOperateType()) {
                updateReachModule(reachModuleDTO, userDTO);
            } else if (OperationTypeEnum.DELETE.getCode() == reachModuleDTO.getOperateType()) {
                deleteReachModule(reachModuleDTO.getId());
            } else {
                throw new AmisBadRequestException("不支持的操作类型：" + reachModuleDTO.getOperateType());
            }
        } catch (Exception e) {
            log.error("触达组管理异常", e);
            throw new AmisBadRequestException("触达组管理异常");
        }
    }

    /**
     * 验证请求参数
     */
    private void validateParams(ReachModuleDTO reachModuleDTO) {
        if (reachModuleDTO == null) {
            throw new AmisBadRequestException("请求数据不能为空");
        }

        if (reachModuleDTO.getOperateType() == null) {
            throw new AmisBadRequestException("操作类型不能为空");
        }

        if (!OperationTypeEnum.existsCode(reachModuleDTO.getOperateType())) {
            throw new AmisBadRequestException("操作类型不正确");
        }

        // 对于非删除操作，需要验证更多字段
        if (!reachModuleDTO.getOperateType().equals(OperationTypeEnum.DELETE.getCode())) {
            if (reachModuleDTO.getReachModule() == null) {
                throw new AmisBadRequestException("触达模块不能为空");
            }

            if (StringUtils.isBlank(reachModuleDTO.getReachGroupName())) {
                throw new AmisBadRequestException("触达组名称不能为空");
            }

            if (CollectionUtils.isEmpty(reachModuleDTO.getReachPersonList())) {
                throw new AmisBadRequestException("触达人员不能为空");
            }

            if (CollectionUtils.isEmpty(reachModuleDTO.getReachPersonNameList())) {
                throw new AmisBadRequestException("触达人员名称不能为空");
            }
        }
        if(reachModuleDTO.getOperateType().equals(OperationTypeEnum.EDIT.getCode())){
            if(null == reachModuleDTO.getId()){
                throw new AmisBadRequestException("触达组ID不能为空");
            }
            ReachModule reachModule = reachModuleMapper.selectByPrimaryKey(reachModuleDTO.getId());
            if(null == reachModule){
                throw new AmisBadRequestException("触达组不存在");
            }
        }
    }

    /**
     * 验证模块是否存在
     */
    private void verifyModuleExists(Long moduleId) {
        if (moduleId == null) {
            return;
        }

        DevelopModule module = developModuleMapper.selectByPrimaryKey(moduleId.intValue());
        if (module == null) {
            throw new AmisBadRequestException("触达模块不在基础维护信息中");
        }
    }

    /**
     * 保存触达模块
     */
    private void saveReachModule(ReachModuleDTO reachModuleDTO, TokenUserDTO userDTO) {
        ReachModule record = convertToEntity(reachModuleDTO);
        record.setReachPersons(Optional.ofNullable(reachModuleDTO.getReachPersonList())
                .map(list -> list.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(",")))
                .orElse(""));

        record.setReachPersonCodes(Optional.ofNullable(reachModuleDTO.getReachPersonList())
                .map(list -> roleCodes(list).stream()
                        .collect(Collectors.joining(",")))
                .orElse(""));

        record.setReachPersonNames(Optional.ofNullable(reachModuleDTO.getReachPersonNameList())
                .map(list -> list.stream()
                        .collect(Collectors.joining(",")))
                .orElse(""));
        // 设置创建和更新信息
        record.setCreatorId(userDTO.getUserId());
        record.setCreatorName(userDTO.getName());
        record.setUpdateId(userDTO.getUserId());
        record.setUpdateName(userDTO.getName());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setStatus(ScibCommonEnums.StatusEnum.NORMAL.getCode());
        reachModuleMapper.insertSelective(record);
    }

    /**
     * 更新触达模块
     */
    private void updateReachModule(ReachModuleDTO reachModuleDTO, TokenUserDTO userDTO) {
        ReachModule record = convertToEntity(reachModuleDTO);
        record.setReachPersons(Optional.ofNullable(reachModuleDTO.getReachPersonList())
                .map(list -> list.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(",")))
                .orElse(""));

        record.setReachPersonCodes(Optional.ofNullable(reachModuleDTO.getReachPersonList())
                .map(list -> roleCodes(list).stream()
                        .collect(Collectors.joining(",")))
                .orElse(""));

        record.setReachPersonNames(Optional.ofNullable(reachModuleDTO.getReachPersonNameList())
                .map(list -> list.stream()
                        .collect(Collectors.joining(",")))
                .orElse(""));
        // 设置更新信息
        record.setUpdateId(userDTO.getUserId());
        record.setUpdateName(userDTO.getName());
        record.setUpdateTime(new Date());
        reachModuleMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 删除触达模块
     */
    private void deleteReachModule(Long id) {
        if (id == null) {
            throw new AmisBadRequestException("删除操作需要指定ID");
        }

        reachModuleMapper.deleteByPrimaryKey(id);
    }

    /**
     * 将DTO转换为实体对象
     */
    private ReachModule convertToEntity(ReachModuleDTO dto) {
        ReachModule entity = new ReachModule();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 根据角色id获取角色编码
     * @param roleIdList
     * @return
     */
    private List<String> roleCodes(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        List<RoleDTO> roleList = roleCacheServiceImpl.getCacheRoles();
        List<String> roleCodeList = roleList.stream()
                .filter(role -> roleIdList.contains(role.getId()))
                .map(RoleDTO::getCode)
                .collect(Collectors.toList());
        return roleCodeList;
    }

}
