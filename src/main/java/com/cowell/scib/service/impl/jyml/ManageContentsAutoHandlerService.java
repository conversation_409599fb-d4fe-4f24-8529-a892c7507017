package com.cowell.scib.service.impl.jyml;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.cache.CacheService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mapperDgms.StoreGoodsContentsMapper;
import com.cowell.scib.mapperDgms.extend.ConfigOrgDetailExMapper;
import com.cowell.scib.mapperDgms.extend.ConfigOrgExtendMapper;
import com.cowell.scib.mapperTidb.JymlStoreSkuExemptionRecordMapper;
import com.cowell.scib.mapperTidb.JymlStoreSkuSuggestMapper;
import com.cowell.scib.mapperTidb.JymlStoreSkuSuggestProcessDetailMapper;
import com.cowell.scib.mapperTidb.JymlStoreSkuSuggestProcessMapper;
import com.cowell.scib.mapperTidb.extend.JymlStoreSkuExemptionRecordExtendMapper;
import com.cowell.scib.mapperTidb.extend.JymlStoreSkuSuggestExtendMapper;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.manageContents.ManageBatchUpdateParam;
import com.cowell.scib.service.dto.manageContents.ManageCommonDTO;
import com.cowell.scib.service.dto.manageContents.ManageDataHelper;
import com.cowell.scib.service.dto.manageContents.ManageQueryParam;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreContentsAssemble;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreContentsFactory;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreGoodsContentDTO;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.utils.ExtendUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cowell.scib.constant.Constants.*;

/**
 *  定时自动确认分类数据
 *  https://app.mockplus.cn/s/WLNbLK4D1?请查看
 * <AUTHOR>
 * @date 2021/07/01 16:09
 */
@Component("manageContentsAutoHandlerService")
public class ManageContentsAutoHandlerService {

    private final Logger logger = LoggerFactory.getLogger(ManageContentsAutoHandlerService.class);

    @Autowired
    private JymlStoreSkuSuggestProcessMapper jymlStoreSkuSuggestProcessMapper;

    @Autowired
    private JymlStoreSkuSuggestProcessDetailMapper jymlStoreSkuSuggestProcessDetailMapper;

    @Autowired
    private JymlStoreSkuExemptionRecordMapper jymlStoreSkuExemptionRecordMapper;
    @Autowired
    private JymlStoreSkuExemptionRecordExtendMapper jymlStoreSkuExemptionRecordExtendMapper;

    @Autowired
    private ManageContentsService manageContentsService;

    @Autowired
    private IAlertService alertService;

    @Autowired
    private StoreGoodsContentsMapper storeGoodsContentsMapper;

    @Autowired
    private JymlStoreSkuSuggestMapper jymlStoreSkuSuggestMapper;
    @Autowired
    private JymlStoreSkuSuggestExtendMapper jymlStoreSkuSuggestExtendMapper;
    @Value("${scib.jyml.autoConfirm.batchSize:500}")
    private int autoConfirmBatchSize;
    @Autowired
    @Qualifier("manageContentsConfirmExecutor")
    private AsyncTaskExecutor manageContentsConfirmExecutor;
    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor taskExecutor;
    @Autowired
    private SearchService searchService;
    @Autowired
    private MdmTaskMapper mdmTaskMapper;
    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;
    @Autowired
    private ConfigOrgDetailExMapper configOrgDetailExMapper;
    @Autowired
    private BundlTaskService bundlTaskService;
    @Autowired
    private TagService tagService;
    @Resource
    private StoreContentsFactory factory;

    public static final String TYPE_NOTICE = "notice";

    public static final String TYPE_CONFIRM = "confirm";

    public void handleProcess(String type) {
        // 1.判断是 通知还是确认
        if (TYPE_NOTICE.equals(type)){
            //8点触发
            handleNotice();
        }
        if (TYPE_CONFIRM.equals(type)){
            //0点触发
            handleAutoDealConfirm();
        }
    }

    /**
     * 8点触发 通知店长
     */
    public void handleNotice() {
        logger.info("经营目录定时任务 date={}", DateUtil.now());
        // 2.查询出所有需要处理的数据
        JymlStoreSkuSuggestProcessExample example = new JymlStoreSkuSuggestProcessExample();
        example.createCriteria().andStatusEqualTo(NORMAL_STATUS);
        List<JymlStoreSkuSuggestProcess> jymlStoreSkuSuggestProcessList = jymlStoreSkuSuggestProcessMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessList)) {
            logger.info("handleNotice，查询到{}条数据", jymlStoreSkuSuggestProcessList.size());
            // 2.1 区分需要 通知确认的数据 门店
            List<JymlStoreSkuSuggestProcess>  jymlStoreSkuSuggestProcessNeedConfirmList = jymlStoreSkuSuggestProcessList.stream().filter(v -> INTEGER_ZERO.equals(v.getConfirmed()) && INTEGER_ONE.equals(v.getZdtStartNoticeFalg()) ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessNeedConfirmList)) {
                // 3.循环处理
                for (JymlStoreSkuSuggestProcess process : jymlStoreSkuSuggestProcessNeedConfirmList) {
                    logger.info("handleNotice，process={}", JSONObject.toJSONString(process));
                    String wxUsers = ExtendUtil.getExtendValue(process.getExtend(), "wxUsers", String.class);
                    logger.info("handleNotice，wxUsers={}", JSONObject.toJSONString(wxUsers));
                    // 4.如果在时间范围内，是否需要发送通知标识 且 通知用户不为空 (启动月度调整功能发企业通知给门店店长，让他们去做目录确认)
                    if (Objects.nonNull(process.getBeginProcessTime()) && Objects.nonNull(process.getScheduledProcessTime())) {
                        JymlStoreSkuSuggestProcessDetailExample detailExample = new JymlStoreSkuSuggestProcessDetailExample();
                        detailExample.createCriteria().andStoreCodeEqualTo(process.getStoreCode()).andUpperLimitEqualTo(INTEGER_ONE).andConfirmedEqualTo(INTEGER_ZERO);
                        long count = jymlStoreSkuSuggestProcessDetailMapper.countByExample(detailExample);
                        if (count == 0L) {
                            continue;
                        }
                        if (StringUtils.isNotBlank(wxUsers)) {
                            boolean inTime = DateUtil.isIn(new Date(), process.getBeginProcessTime(), process.getScheduledProcessTime());
                            logger.info("handleNotice，in 通知周期={}", inTime);
                            if (inTime) {
                                // 5.则发送通知
                                try {
                                    AlertContent alert = new AlertContent();
                                    alert.setType("QYWX");
                                    alert.setFlag(true);
                                    alert.setToUsers(wxUsers);
                                    alert.setSubject("数字化商品-经营目录提醒");
                                    OrgInfoBaseCache orgInfoBaseCache = CacheVar.getStoreBySapCode(process.getStoreCode()).orElse(null);
                                    if (Objects.nonNull(orgInfoBaseCache)&& StringUtils.isNotBlank(orgInfoBaseCache.getShortName())){
                                        alert.setMessage(orgInfoBaseCache.getShortName()+process.getZdtStartNotice());
                                    }else {
                                        alert.setMessage(process.getZdtStartNotice());
                                    }
                                    logger.info("handleNotice alert={}", alert);
                                    alertService.alert(alert);
                                } catch (Exception e) {
                                    logger.info("handleNotice alert 异常", e);
                                }
                            }
                        }
                    }
                }
            }
            // 2.2 区分需要通知 自动确认已完成的 门店
            List<JymlStoreSkuSuggestProcess>  jymlStoreSkuSuggestProcessConfirmedList = jymlStoreSkuSuggestProcessList.stream().
                    filter(v -> INTEGER_ONE.equals(v.getConfirmed())&& INTEGER_ONE.equals(v.getZdtEndNoticeFalg())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessConfirmedList)) {
                // 3.循环处理
                for (JymlStoreSkuSuggestProcess process : jymlStoreSkuSuggestProcessConfirmedList) {
                    logger.info("handleConfirmedNotice，process={}", JSONObject.toJSONString(process));
                    // 4.如果在自动确认当天，是否需要发送已经自动确认通知标识 且 通知用户不为空 (给按照系统建议自动做经营目录确认的店长发企微消息提醒)
                    if (Objects.nonNull(process.getScheduledProcessTime()) && DateUtil.isSameDay(process.getScheduledProcessTime(), new Date())){
                        JymlStoreSkuSuggestProcessDetailExample detailExample = new JymlStoreSkuSuggestProcessDetailExample();
                        detailExample.createCriteria().andStoreCodeEqualTo(process.getStoreCode()).andUpperLimitEqualTo(INTEGER_ONE).andConfirmedEqualTo(INTEGER_ZERO);
                        long count = jymlStoreSkuSuggestProcessDetailMapper.countByExample(detailExample);
                        if (count == 0L) {
                            continue;
                        }
                        logger.info("handleConfirmedNotice，in ScheduledProcessTime");
                        String wxUsers = ExtendUtil.getExtendValue(process.getExtend(), "wxUsers", String.class);
                        logger.info("handleConfirmedNotice，wxUsers={}", JSONObject.toJSONString(wxUsers));
                        if (StringUtils.isNotBlank(wxUsers)) {
                            try {
                                AlertContent alert = new AlertContent();
                                alert.setType("QYWX");
                                alert.setFlag(true);
                                alert.setToUsers(wxUsers);
                                alert.setSubject("数字化商品-经营目录提醒");
                                OrgInfoBaseCache orgInfoBaseCache = CacheVar.getStoreBySapCode(process.getStoreCode()).orElse(null);
                                if (Objects.nonNull(orgInfoBaseCache)&& StringUtils.isNotBlank(orgInfoBaseCache.getShortName())){
                                    alert.setMessage(orgInfoBaseCache.getShortName()+process.getZdtEndNotice());
                                }else {
                                    alert.setMessage(process.getZdtEndNotice());
                                }
                                alertService.alert(alert);
                                //更新 当前门店 不在需要通知了
                                process.setZdtEndNoticeFalg(INTEGER_TWO);
                                logger.info("handleConfirmedNotice EndProcess  process={}", JSONObject.toJSONString(process));
                                jymlStoreSkuSuggestProcessMapper.updateByPrimaryKeySelective(process);
                            } catch (Exception e) {
                                logger.info("handleConfirmedNotice alert 异常",e);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 0点触发 自动确认 和 自动到期关闭 --已废弃
     */
    @Deprecated
    private void handleAutoConfirm() {
        JymlStoreSkuSuggestProcessExample example = new JymlStoreSkuSuggestProcessExample();
        example.createCriteria().andStatusEqualTo(NORMAL_STATUS);
        List<JymlStoreSkuSuggestProcess> jymlStoreSkuSuggestProcessList = jymlStoreSkuSuggestProcessMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessList)) {
            logger.info("handleAutoConfirm，查询到{}条数据", jymlStoreSkuSuggestProcessList.size());
            int batchSize = INTEGER_ZERO.equals(autoConfirmBatchSize)?500:autoConfirmBatchSize;
            for (JymlStoreSkuSuggestProcess process : jymlStoreSkuSuggestProcessList) {
                logger.info("handleAutoConfirm，process={}", JSONObject.toJSONString(process));
                //未确认且自动确认时间等于当前时间，则自动确认
                if (INTEGER_ZERO.equals(process.getConfirmed()) && Objects.nonNull(process.getScheduledProcessTime()) && DateUtil.isSameDay(process.getScheduledProcessTime(), new Date())) {
                    logger.info("handleAutoConfirm，未确认 未确认且自动确认时间等于当前时间，则自动确认 storeNo={}", JSONObject.toJSONString(process.getStoreCode()));
                    JymlStoreSkuSuggestProcessDetailExample autoExample = new JymlStoreSkuSuggestProcessDetailExample();
                    JymlStoreSkuSuggestProcessDetailExample.Criteria criteria = autoExample.createCriteria();
                    criteria.andStoreCodeEqualTo(process.getStoreCode())
                            .andVersionEqualTo(process.getVersion()).andConfirmedEqualTo(INTEGER_ZERO);
                    List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(autoExample);
                    if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessDetails)) {
                        logger.info("handleAutoConfirm，查询到detail {}条数据", jymlStoreSkuSuggestProcessDetails.size());
                        // 创建系统用户DTO
                        TokenUserDTO userDTO = new TokenUserDTO();
                        userDTO.setUserId(Constants.SYS_USER_ID);
                        userDTO.setName(Constants.SYS_USER_NAME);

                        // 收集所有需要处理的数据
                        List<ManageCommonDTO> allItemsToProcess = new ArrayList<>();

                        // 遍历所有需要处理的分类详情
                        for (JymlStoreSkuSuggestProcessDetail jymlStoreSkuSuggestProcessDetail : jymlStoreSkuSuggestProcessDetails) {
                            try {
                                ManageQueryParam param = new ManageQueryParam();
                                param.setStoreNo(jymlStoreSkuSuggestProcessDetail.getStoreCode());
                                String categoryKey = ManageDataHelper.getCategoryKey(jymlStoreSkuSuggestProcessDetail.getCategory(), jymlStoreSkuSuggestProcessDetail.getMiddleCategory(), jymlStoreSkuSuggestProcessDetail.getSmallCategory(), jymlStoreSkuSuggestProcessDetail.getSubCategory());
                                param.setCategory(categoryKey.replace("-", "/"));
                                logger.info("handleAutoConfirm manageContentsList  param={}", JSONObject.toJSONString(param));
                                CommonResponse<ManageCommonDTO> manageCommonDTOCommonResponse = manageContentsService.manageContentsList(userDTO, param);
                                if (ErrorCodeEnum.SUCCESS.getCode().equals(manageCommonDTOCommonResponse.getStatus()) && CollectionUtils.isNotEmpty(manageCommonDTOCommonResponse.getData())) {
                                    // 收集需要处理的数据
                                    allItemsToProcess.addAll(manageCommonDTOCommonResponse.getData());
                                }
                            } catch (Exception e) {
                                logger.error("handleAutoConfirm，获取分类商品数据异常", e);
                            }
                        }

                        // 按每500条数据分组批量处理
                        if (CollectionUtils.isNotEmpty(allItemsToProcess)) {
                            logger.info("handleAutoConfirm，共收集到{}条数据需要自动确认", allItemsToProcess.size());
                            // 按每500条数据分组
                            int batchCount = 0;
                            List<List<ManageCommonDTO>> batches = Lists.partition(allItemsToProcess,batchSize);
                            // 批量处理每组数据
                            for (List<ManageCommonDTO> batch : batches) {
                                batchCount++;
                                try {
                                    ManageBatchUpdateParam manageBatchUpdateParam = new ManageBatchUpdateParam();
                                    manageBatchUpdateParam.setAutoConfirm(true);
                                    manageBatchUpdateParam.setUnModifiedItems(batch);
                                    logger.info("handleAutoConfirm，批次[{}]处理{}条数据", batchCount, batch.size());
                                    manageContentsService.batchUpdate(userDTO, manageBatchUpdateParam);
                                    logger.info("handleAutoConfirm，批次[{}]处理完成", batchCount);
                                } catch (Exception e) {
                                    logger.error("handleAutoConfirm，批次[" + batchCount + "]处理异常", e);
                                }
                            }
                        } else {
                            logger.info("handleAutoConfirm，没有需要处理的数据");
                        }
                    }
                    JymlStoreSkuSuggestProcess jymlStoreSkuSuggestProcessNew = jymlStoreSkuSuggestProcessMapper.selectByPrimaryKey(process.getId());
                    Integer nonJyCount = ExtendUtil.getExtendValue(process.getExtend(), "nonJyCount", Integer.class);
                    // 优化替换逻辑，只调用一次replace方法
                    String replacement = Objects.nonNull(nonJyCount) ? nonJyCount + "个" : "0个";
                    String originalNotice = process.getZdtEndNotice();
                    jymlStoreSkuSuggestProcessNew.setZdtEndNotice(originalNotice.replace("0个", replacement));
                    jymlStoreSkuSuggestProcessNew.setConfirmed(INTEGER_ONE);
                    if (Objects.isNull(nonJyCount) || INTEGER_ZERO.equals(nonJyCount)){
                        jymlStoreSkuSuggestProcessNew.setZdtEndNoticeFalg(INTEGER_NEGATIVE_ONE);
                    }
                    logger.info("handleAutoConfirm EndProcess  process={}", JSONObject.toJSONString(jymlStoreSkuSuggestProcessNew));
                    jymlStoreSkuSuggestProcessMapper.updateByPrimaryKeySelective(jymlStoreSkuSuggestProcessNew);
                }
                //到期结束 调整process状态 已结束
                if (Objects.nonNull(process.getEndProcessTime()) && DateUtil.isSameDay(process.getEndProcessTime(), new Date())) {
                    try {
                        //通知店长自动处理已经完成  (默认:0  0:不需要通知  1:需要通知 -1:条件异常 2:已经结束)
                        process.setZdtStartNoticeFalg(INTEGER_TWO);
                        process.setZdtEndNoticeFalg(INTEGER_TWO);
                        process.setStatus(DEL_STATUS);
                        process.setConfirmed(INTEGER_ONE);
                        logger.info("handleAutoEndProcess EndProcess  process={}", JSONObject.toJSONString(process));
                        jymlStoreSkuSuggestProcessMapper.updateByPrimaryKeySelective(process);
                    } catch (Exception e) {
                        logger.error("handleAutoEndProcess，alert 异常", e);
                    }
                }
            }
        }

    }
    /**
     * 0点触发 自动确认 和 自动到期关闭
     */
    private void handleAutoDealConfirm() {
        JymlStoreSkuSuggestProcessExample example = new JymlStoreSkuSuggestProcessExample();
        example.createCriteria().andStatusEqualTo(NORMAL_STATUS);
        List<JymlStoreSkuSuggestProcess> jymlStoreSkuSuggestProcessList = jymlStoreSkuSuggestProcessMapper.selectByExample(example);
        // 创建系统用户DTO
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(Constants.SYS_USER_ID);
        userDTO.setName(Constants.SYS_USER_NAME);
        Set<Long> dealStoreIds = new HashSet<>();
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessList)) {
            logger.info("handleAutoConfirm，查询到{}条数据", jymlStoreSkuSuggestProcessList.size());
            int batchSize = INTEGER_ZERO.equals(autoConfirmBatchSize)?500:autoConfirmBatchSize;
            for (JymlStoreSkuSuggestProcess process : jymlStoreSkuSuggestProcessList) {
                logger.info("handleAutoConfirm，process={}", JSONObject.toJSONString(process));
                Optional<OrgInfoBaseCache> optional = CacheVar.getStoreBySapCode(process.getStoreCode());
                if (!optional.isPresent()) {
                    logger.error("门店编码不存在:{}", process.getStoreCode());
                }
                OrgInfoBaseCache store = optional.get();
                //未确认且自动确认时间等于当前时间，则自动确认
                if (INTEGER_ZERO.equals(process.getConfirmed()) && Objects.nonNull(process.getScheduledProcessTime()) && DateUtil.isSameDay(process.getScheduledProcessTime(), new Date())) {
                    logger.info("handleAutoConfirm，未确认 未确认且自动确认时间等于当前时间，则自动确认 storeNo={}", JSONObject.toJSONString(process.getStoreCode()));
                    if (null != store.getOutId()) {
                        dealStoreIds.add(store.getOutId());
                    }
                    JymlStoreSkuSuggestProcessDetailExample autoExample = new JymlStoreSkuSuggestProcessDetailExample();
                    JymlStoreSkuSuggestProcessDetailExample.Criteria criteria = autoExample.createCriteria();
                    criteria.andStoreCodeEqualTo(process.getStoreCode())
                            .andVersionEqualTo(process.getVersion()).andConfirmedEqualTo(INTEGER_ZERO);
                    List<JymlStoreSkuSuggestProcessDetail> jymlStoreSkuSuggestProcessDetails = jymlStoreSkuSuggestProcessDetailMapper.selectByExample(autoExample);
                    int nonJyCount = 0;
                    if (CollectionUtils.isNotEmpty(jymlStoreSkuSuggestProcessDetails)) {
                        // 排除二轮选配品
                        JymlStoreSkuExemptionRecordExample exemptionRecordExample = new JymlStoreSkuExemptionRecordExample();
                        exemptionRecordExample.createCriteria().andStoreCodeEqualTo(store.getSapCode());
                        long count = jymlStoreSkuExemptionRecordMapper.countByExample(exemptionRecordExample);
                        nonJyCount = nonJyCount - Long.valueOf(count).intValue();
                        logger.info("handleAutoConfirm，查询到detail {}条数据", jymlStoreSkuSuggestProcessDetails.size());
                        // 根据门店查询一店一目数据
                        StoreGoodsContentsExample contentsExample = new StoreGoodsContentsExample();
                        contentsExample.createCriteria().andStoreIdEqualTo(store.getOutId());
                        contentsExample.setLimit(INSERT_MAX_VALUE);
                        Map<String, List<String>> categoryMap = new HashMap<>();
                        jymlStoreSkuSuggestProcessDetails.forEach(v->{
                            List<String> categorys = Optional.ofNullable(categoryMap.get(v.getCategory())).orElse(new ArrayList<>());
                            if (StringUtils.isNotBlank(v.getSubCategory())) {
                                categorys.add(v.getSubCategory());
                                categoryMap.put(v.getCategory(), categorys);
                            } else if (StringUtils.isNotBlank(v.getSmallCategory())) {
                                categorys.add(v.getSmallCategory());
                                categoryMap.put(v.getCategory(), categorys);
                            } else if (StringUtils.isNotBlank(v.getMiddleCategory())) {
                                categorys.add(v.getMiddleCategory());
                                categoryMap.put(v.getCategory(), categorys);
                            }
                        });
                        logger.info("categoryMap:{}", JSONObject.toJSONString(categoryMap));
                        JymlStoreSkuSuggestExample suggestExample  = new JymlStoreSkuSuggestExample();
                        JymlStoreSkuSuggestExample.Criteria criteriaSuggest = suggestExample.createCriteria();
                        criteriaSuggest.andBusinessOrgIdEqualTo(process.getBusinessOrgId()).andStoreCodeEqualTo(process.getStoreCode()).andVersionEqualTo(process.getVersion());
                        List<JymlStoreSkuSuggest> jymlStoreSkuSuggests = jymlStoreSkuSuggestExtendMapper.selectSimpleByExample(suggestExample);
                        logger.info("过滤分类前size:{}", jymlStoreSkuSuggests.size());
                        if (CollectionUtils.isEmpty(jymlStoreSkuSuggests)){
                            continue;
                        }
                        logger.info("jymlStoreSkuSuggests:{}", JSON.toJSONString(jymlStoreSkuSuggests.get(0)));
                        jymlStoreSkuSuggests = jymlStoreSkuSuggests.stream().filter(v -> {
                            List<String> categoryList = categoryMap.get(v.getCategory());
                            if (CollectionUtils.isEmpty(categoryList)) {
                                return false;
                            }
                            return categoryList.stream().filter(c -> v.getSubCategory().toString().startsWith(c)).findAny().isPresent();
                        }).collect(Collectors.toList());
                        logger.info("过滤分类后size:{}", jymlStoreSkuSuggests.size());
                        if (CollectionUtils.isEmpty(jymlStoreSkuSuggests)){
                            continue;
                        }
                        Set<String> validGoodsNos = jymlStoreSkuSuggests.stream()
                                .map(JymlStoreSkuSuggest::getGoodsNo)
                                .collect(Collectors.toCollection(HashSet::new));
                        logger.info("manageContentsList validGoodsNos={}",validGoodsNos.size());

                        for (int i = 0 ;; i++) {
                            contentsExample.setOffset(Long.valueOf(i * INSERT_MAX_VALUE));
                            List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(contentsExample);
                            if (CollectionUtils.isEmpty(storeGoodsContents)) {
                                break;
                            }
                            storeGoodsContents = storeGoodsContents.stream()
                                    .filter(v -> validGoodsNos.contains(v.getGoodsNo()))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(storeGoodsContents)) {
                                break;
                            }
                            Map<String, JymlStoreSkuSuggest> skuSuggestMap = jymlStoreSkuSuggests.stream().filter(v -> Objects.nonNull(v.getGoodsNo())).collect(Collectors.toMap(JymlStoreSkuSuggest::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                            storeGoodsContents.forEach(goods -> {
//                                if (INTEGER_ZERO.equals(goods.getManageStatus())) {
                                    ManageStatusEnum enumBySuggestCode = ManageStatusEnum.getEnumBySuggestCode(goods.getSuggestManageStatus());
                                    if (Objects.nonNull(enumBySuggestCode)) {
//                                        logger.warn("商品 确认为空 SuggestCode转化公成ManageStatus");
                                        goods.setManageStatus(enumBySuggestCode.getCode());
                                    }
//                                }
                            });

                            // 按每500条数据分组批量处理
                            if (CollectionUtils.isNotEmpty(storeGoodsContents)) {
                                logger.info("handleAutoConfirm，共收集到{}条数据需要自动确认", storeGoodsContents.size());
                                // 按每500条数据分组
                                int batchCount = 0;
                                List<List<StoreGoodsContents>> batches = Lists.partition(storeGoodsContents, batchSize);
                                // 批量处理每组数据
                                for (List<StoreGoodsContents> batch : batches) {
                                    batchCount++;
                                    try {
                                        logger.info("handleAutoConfirm，批次[{}]处理{}条数据", batchCount, batch.size());
                                        List<StoreGoodsContents> chooseList = batch.stream()
                                                .filter(v -> ManageStatusEnum.MANAGE_CHOOSE.getCode().equals(v.getManageStatus()))
                                                .collect(Collectors.toList());
                                        logger.info("batchUpdate chooseList= {} storeCode={}", chooseList.size(), process.getStoreCode());
                                        // 处理不经营商品
                                        List<StoreGoodsContents> nonList = batch.stream()
                                                .filter(v -> ManageStatusEnum.NON_MANAGE.getCode().equals(v.getManageStatus()))
                                                .collect(Collectors.toList());
                                        nonJyCount = nonJyCount + nonList.size();
                                        logger.info("batchUpdate nonList= {} storeCode={}", nonList.size(), process.getStoreCode());
                                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                                            logger.info("CompletableFuture开始处理");
//                                        manageContentsConfirmExecutor.execute(() -> {
                                            if (CollectionUtils.isNotEmpty(chooseList)) {
                                                MdmTask task = new MdmTask();
                                                task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
                                                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                                                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                                                task.setDetailCount(chooseList.size());
                                                mdmTaskMapper.insertSelective(task);
                                                logger.info("batchUpdate task= {} storeCode={}", task, process.getStoreCode());

                                                StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
                                                List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(store, chooseList,
                                                        StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task);
                                                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                                                    assemble.work(storeGoodsContentDTOS);
                                                }
                                            }
                                            if (CollectionUtils.isNotEmpty(nonList)) {
                                                MdmTask task = new MdmTask();
                                                task.setTaskSource(MdmTaskSourceEnum.CHOOSE_GOODS_TO_NON.getCode());
                                                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                                                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                                                task.setDetailCount(nonList.size());
                                                mdmTaskMapper.insertSelective(task);
                                                logger.info("batchUpdate task= {} storeCode={}", task, process.getStoreCode());

                                                StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.NON_MANAGE.getCode());
                                                List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(store, nonList,
                                                        StoreContentBizTypeEnum.NON_MANAGE.getCode(), userDTO, task);
                                                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                                                    assemble.work(storeGoodsContentDTOS);
                                                }
                                            }
                                        }, manageContentsConfirmExecutor);
                                        completableFutures.add(future);
//                                        });
                                        logger.info("handleAutoConfirm，批次[{}]处理完成", batchCount);
                                    } catch (Exception e) {
                                        logger.error("handleAutoConfirm，批次[" + batchCount + "]处理异常", e);
                                    }
                                }
                            } else {
                                logger.info("handleAutoConfirm，没有需要处理的数据");
                            }
                        }
                        JymlStoreSkuSuggestProcessDetail detail = new JymlStoreSkuSuggestProcessDetail();
                        detail.setGmtUpdate(new Date());
                        detail.setUpdatedBy(userDTO.getUserId());
                        detail.setUpdatedName(userDTO.getName());
                        detail.setConfirmed(INTEGER_ONE); // 设置为已确认
                        detail.setConfirmedBy(userDTO.getUserId());
                        detail.setConfirmedName(userDTO.getName());
                        detail.setStoreCode(process.getStoreCode());
                        detail.setVersion(process.getVersion());
                        JymlStoreSkuSuggestProcessDetailExample updateExample = new JymlStoreSkuSuggestProcessDetailExample();
                        updateExample.createCriteria().andStoreCodeEqualTo(process.getStoreCode()).andVersionEqualTo(process.getVersion());
                        jymlStoreSkuSuggestProcessDetailMapper.updateByExampleSelective(detail, updateExample);
                    }
                    JymlStoreSkuSuggestProcess jymlStoreSkuSuggestProcessNew = jymlStoreSkuSuggestProcessMapper.selectByPrimaryKey(process.getId());
//                    Integer nonJyCount = ExtendUtil.getExtendValue(process.getExtend(), "nonJyCount", Integer.class);
                    // 优化替换逻辑，只调用一次replace方法
                    String replacement = Objects.nonNull(nonJyCount) ? nonJyCount + "个" : "0个";
                    String originalNotice = process.getZdtEndNotice();
                    jymlStoreSkuSuggestProcessNew.setZdtEndNotice(originalNotice.replace("0个", replacement));
                    jymlStoreSkuSuggestProcessNew.setConfirmed(INTEGER_ONE);
                    if (Objects.isNull(nonJyCount) || INTEGER_ZERO.equals(nonJyCount)){
                        jymlStoreSkuSuggestProcessNew.setZdtEndNoticeFalg(INTEGER_NEGATIVE_ONE);
                    }
                    logger.info("handleAutoConfirm EndProcess  process={}", JSONObject.toJSONString(jymlStoreSkuSuggestProcessNew));
                    jymlStoreSkuSuggestProcessMapper.updateByPrimaryKeySelective(jymlStoreSkuSuggestProcessNew);
                }
                //到期结束 调整process状态 已结束
                if (Objects.nonNull(process.getEndProcessTime()) && DateUtil.isSameDay(process.getEndProcessTime(), new Date())) {
                    try {
                        //通知店长自动处理已经完成  (默认:0  0:不需要通知  1:需要通知 -1:条件异常 2:已经结束)
                        process.setZdtStartNoticeFalg(INTEGER_TWO);
                        process.setZdtEndNoticeFalg(INTEGER_TWO);
                        process.setStatus(DEL_STATUS);
                        process.setConfirmed(INTEGER_ONE);
                        logger.info("handleAutoEndProcess EndProcess  process={}", JSONObject.toJSONString(process));
                        jymlStoreSkuSuggestProcessMapper.updateByPrimaryKeySelective(process);
                    } catch (Exception e) {
                        logger.error("handleAutoEndProcess，alert 异常", e);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(completableFutures)) {
            logger.info("开始异步处理二轮选配");
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()])).whenComplete((e,v) -> {
                dealExemptionData(userDTO, dealStoreIds);
            });
        } else {
            logger.info("第一步没有数据,开始异步处理二轮选配");
            dealExemptionData(userDTO, dealStoreIds);
        }
    }

    /**
     * 处理二轮选配商品
     * @param userDTO
     */
    private void dealExemptionData(TokenUserDTO userDTO, Set<Long> dealStoreIds) {
        try {
            logger.info("二轮选配开始处理:{}", JSON.toJSONString(dealStoreIds));
            List<Long> allStoreIds = jymlStoreSkuExemptionRecordExtendMapper.getAllStoreIds();
            if (CollectionUtils.isEmpty(allStoreIds)) {
                logger.info("没有需要处理的二轮选配商品");
                return;
            }
            allStoreIds = allStoreIds.stream().filter(v -> dealStoreIds.contains(v)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allStoreIds)) {
                logger.info("没有需要处理的二轮选配门店");
                return;
            }
            ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ROOT_ORG_ID,ConfigTypeEnum.JY.getType(), null);
            if (Objects.isNull(configOrg)){
                logger.warn("selectMdmStoreFilterSelector|经营范围参数配置为空");
                return;
            }
            List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Lists.newArrayList("jymu_businesslist"));
            if(CollectionUtils.isEmpty(configOrgDetails)){
                logger.warn("selectMdmStoreFilterSelector|企业连锁为空");
                return;
            }
            List<Long> businessOrgIds = configOrgDetails.stream().map(ConfigOrgDetail::getPerprotyValue).map(Long::parseLong).collect(Collectors.toList());
            Map<Long, List<Long>> selectorStoreMap = new HashMap<>();
            for (Long storeId : allStoreIds) {
                Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(storeId);
                if (!optional.isPresent()) {
                    logger.error("门店Id不存在:{}", storeId);
                    continue;
                }
                OrgInfoBaseCache store = optional.get();
                if (!businessOrgIds.contains(store.getBusinessOrgId())) {
                    logger.error("门店:{}不在经营范围参数配置中", storeId);
                    continue;
                }
                List<Long> storeIds = selectMdmStoreIdFilterSelector(store, selectorStoreMap);
                if (CollectionUtils.isEmpty(storeIds) || !storeIds.contains(store.getOutId())) {
                    logger.error("门店:{}不在经营范围参数配置中", storeId);
                    continue;
                }
                JymlStoreSkuExemptionRecordExample example = new JymlStoreSkuExemptionRecordExample();
                example.createCriteria().andStoreIdEqualTo(storeId);
                List<JymlStoreSkuExemptionRecord> records = jymlStoreSkuExemptionRecordMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(records)) {
                    logger.info("门店:{}没有需要处理的二轮选配数据", store.getSapCode());
                    continue;
                }
                StoreGoodsContentsExample contentsExample = new StoreGoodsContentsExample();
                contentsExample.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoIn(records.stream().map(JymlStoreSkuExemptionRecord::getGoodsNo).distinct().collect(Collectors.toList())).andManageStatusNotEqualTo(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                List<StoreGoodsContents> storeGoodsContents = storeGoodsContentsMapper.selectByExample(contentsExample);
                if (CollectionUtils.isEmpty(storeGoodsContents)) {
                    logger.info("门店:{}二轮选配没有一店一目数据", store.getSapCode());
                    continue;
                }
                manageContentsConfirmExecutor.execute(() -> {
                    MdmTask task = new MdmTask();
                    task.setTaskSource(MdmTaskSourceEnum.NON_GOODS_TO_CHOOSE.getCode());
                    BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                    task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                    task.setDetailCount(storeGoodsContents.size());
                    mdmTaskMapper.insertSelective(task);
                    logger.info("batchUpdate task= {} storeCode={}", task, store.getSapCode());
                    StoreContentsAssemble assemble = factory.getAssemble(StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode());
                    List<StoreGoodsContentDTO> storeGoodsContentDTOS = buildStoreGoodsContentDTOConfirm(store, storeGoodsContents.stream().map(v -> {
                        v.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                        return v;
                    }).collect(Collectors.toList()), StoreContentBizTypeEnum.MANAGE_CHOOSE.getCode(), userDTO, task);
                    if (CollectionUtils.isNotEmpty(storeGoodsContentDTOS)) {
                        assemble.work(storeGoodsContentDTOS);
                    }
                });

            }
        } catch (Exception e) {
            logger.error("处理二轮选配商品失败", e);
        }
    }

    private List<Long> selectMdmStoreIdFilterSelector(OrgInfoBaseCache storeInfo, Map<Long, List<Long>> selectorStoreMap) {
        List<Long> storeIds = selectorStoreMap.get(storeInfo.getBusinessOrgId());
        if (CollectionUtils.isNotEmpty(storeIds)) {
            return storeIds;
        }
        RuleParam ruleParam = new RuleParam();
        ruleParam.setOrgId(ROOT_ORG_ID);
        ruleParam.setScopeCode(DicApiEnum.JYML_STORE.getCode());
        ruleParam.setConfigType(ConfigTypeEnum.JY.getType().toString());
        Map<String, List<String>> selectParamMap = bundlTaskService.buildSelectParam(ruleParam, null);
        //Map<String, List<String>> selectParamMap = buildSelectParam(ruleParam);
        if(MapUtils.isEmpty(selectParamMap)){
            logger.warn("selectMdmStoreFilterSelector|选择器入参为空");
            return new ArrayList<>();
        }
        StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
        queryParam.setScopeCode(String.valueOf(storeInfo.getBusinessOrgId()));
        queryParam.setManagstate(selectParamMap.get(DictAliasEnum.MANAGSTATE.getAlias()));
        queryParam.setStorestatus(selectParamMap.get(DictAliasEnum.STORESTATUS.getAlias()));
        queryParam.setFormat(selectParamMap.get(DictAliasEnum.FORMAT.getAlias()));
        queryParam.setStoretype(selectParamMap.get(DictAliasEnum.STORETYPE.getAlias()));
        queryParam.setOperationtype(selectParamMap.get(DictAliasEnum.OPERATIONTYPE.getAlias()));
        queryParam.setSpecialtype(selectParamMap.get(DictAliasEnum.SPECIALTYPE.getAlias()));
        queryParam.setExcludeOrgIds(selectParamMap.get(DictAliasEnum.EXCLUDEORGIDS.getAlias()));
        queryParam.setStoreattr(selectParamMap.get(DictAliasEnum.STOREATTR.getAlias()));
        List<Long> selectStoreIdList = tagService.getSelectStoreIdListByType(queryParam);
        selectorStoreMap.put(storeInfo.getBusinessOrgId(), selectStoreIdList);
        return selectStoreIdList;
    }

    private List<StoreGoodsContentDTO> buildStoreGoodsContentDTOConfirm(OrgInfoBaseCache storeInfo,  List<StoreGoodsContents> confirmList, Integer contentBizType, TokenUserDTO userDTO, MdmTask task) {
        List<StoreGoodsContentDTO> storeGoodsContentDTOS = new ArrayList<>();
        logger.info("buildStoreGoodsContentDTO storeInfo={}",storeInfo);
        SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
        spuNewParamVo.setBusinessId(storeInfo.getBusinessId());
        spuNewParamVo.setGoodsNoList(confirmList.stream().map(StoreGoodsContents::getGoodsNo).filter(Objects::nonNull).collect(Collectors.toList()));
//        Map<String, SpuListVo> newSpuMap = searchService.getSpuVOMap(spuNewParamVo.getGoodsNoList());
        for (StoreGoodsContents manageCommonDTO : confirmList) {
//            SpuListVo spuNewVo = newSpuMap.get(manageCommonDTO.getGoodsNo());
//            if (Objects.isNull(spuNewVo)){
//                continue;
//            }
            StoreGoodsContentDTO storeGoodsContentDTO = new StoreGoodsContentDTO();
            storeGoodsContentDTO.setBizType(contentBizType);
            storeGoodsContentDTO.setStoreId(storeInfo.getOutId());
            storeGoodsContentDTO.setBusinessId(storeInfo.getBusinessId());
            storeGoodsContentDTO.setPlatformOrgId(storeInfo.getPlatformOrgId());
            storeGoodsContentDTO.setGoodsNo(manageCommonDTO.getGoodsNo());
//            storeGoodsContentDTO.setBarCode(spuNewVo.getBarCode());
//            storeGoodsContentDTO.setGoodsName(spuNewVo.getName());
//            storeGoodsContentDTO.setGoodsCommonName(spuNewVo.getCurName());
//            storeGoodsContentDTO.setGoodsUnit(spuNewVo.getGoodsunit());
//            storeGoodsContentDTO.setDescription(spuNewVo.getDescription());
//            storeGoodsContentDTO.setSpecifications(spuNewVo.getJhiSpecification());
//            storeGoodsContentDTO.setDosageForm(spuNewVo.getDosageformsid());
//            storeGoodsContentDTO.setManufacturer(spuNewVo.getFactoryid());
//            storeGoodsContentDTO.setApprovalNumber(spuNewVo.getApprdocno());
            storeGoodsContentDTO.setSuggestManageStatus(manageCommonDTO.getSuggestManageStatus());
            storeGoodsContentDTO.setManageStatus(manageCommonDTO.getManageStatus());
            storeGoodsContentDTO.setSubCategoryId(manageCommonDTO.getSubCategoryId());
            storeGoodsContentDTO.setCreatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setUpdatedBy(userDTO.getUserId());
            storeGoodsContentDTO.setCreatedName(userDTO.getName());
            storeGoodsContentDTO.setUpdatedName(userDTO.getName());
            storeGoodsContentDTO.setMdmTaskId(task.getId());
//            logger.info("buildStoreGoodsContentDTO  storeGoodsContentDTO={}",storeGoodsContentDTO);
            storeGoodsContentDTOS.add(storeGoodsContentDTO);
        }
        return storeGoodsContentDTOS;
    }

}
