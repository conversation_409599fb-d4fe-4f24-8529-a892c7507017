package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.service.IAlertService;
import com.cowell.scib.service.dto.AlertContent;
import com.cowell.scib.service.dto.AlertContentForISCM;
import com.cowell.scib.service.feign.WebLogFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/17 14:14
 */
@Service
public class AlertServiceImpl implements IAlertService {
    private final Logger logger = LoggerFactory.getLogger(AlertServiceImpl.class);

    @Resource
    private WebLogFeignService webLogFeignService;

    @Override
    public void alert(AlertContent alertContent) {
        logger.info("AlertServiceImpl|sendAlert|入参为{}",JSONObject.toJSONString(alertContent));
        String result = webLogFeignService.alert(alertContent);
        logger.info("AlertServiceImpl|sendAlert|result:{}", result);
    }
    @Override
    public void alertForQYYY(AlertContentForISCM alertContent) {
        logger.info("AlertServiceImpl|alertForQYYY|入参为{}", JSONObject.toJSONString(alertContent));
        String result = webLogFeignService.alertIscm(alertContent);
        logger.info("AlertServiceImpl|alertForQYYY|result:{}", result);
    }
}
