package com.cowell.scib.service.impl;

import com.cowell.scib.enums.AsyncExportActionEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.AsyncExportFileService;
import com.cowell.scib.service.HandlerDataExportService;
import com.cowell.scib.service.dto.ListToExcelMultiSheetDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.feign.ScrmFeignClient;
import com.cowell.scib.service.feign.StoreFeignClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AsyncExportFileServiceImpl implements AsyncExportFileService {

    private final Logger logger = LoggerFactory.getLogger(AsyncExportFileServiceImpl.class);

    @Resource
    private ScrmFeignClient scrmFeignService;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Resource
    private AsyncExportContainerExecutor asyncExportContainerExecutor;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void asyncExportToCos(String fileName, AsyncExportActionEnum action, TokenUserDTO userDTO, List<ListToExcelMultiSheetDTO> dataToExcelList) {
        logger.debug("{}开始下载", fileName);
        if (CollectionUtils.isEmpty(dataToExcelList)) {
            return;
        }
        checkAction(action, userDTO);
        asyncExportContainerExecutor.submit(new AsyncExportFileCallable(fileName, action, userDTO, scrmFeignService, storeFeignClient, dataToExcelList));
    }

    @Override
    public void asyncExportToCos(String fileName, AsyncExportActionEnum action, TokenUserDTO userDTO, HandlerDataExportService handlerDataExportService) {
        logger.debug("{}开始下载", fileName);

        // 限制访问次数
        checkAction(action, userDTO);
        asyncExportContainerExecutor.submit(new AsyncExportFileCallable(fileName, action, userDTO, scrmFeignService, storeFeignClient, handlerDataExportService));
    }

    private void checkAction(AsyncExportActionEnum action, TokenUserDTO userDTO) {
        // 限制访问次数
        logger.info("checkAction|redissonClient, action, userDTO,{},{},{}.", redissonClient, action, userDTO);
        RBucket<String> bucket = redissonClient.getBucket("iscm-common-export-" + action.getAction() + userDTO.getUserId());
        String key = bucket.get();
        if (StringUtils.isNotBlank(key)) {
            throw new BusinessErrorException("请不要频繁的创建下载任务，" + bucket.remainTimeToLive()/1000 + "秒后请重试");
        }
        bucket.set(String.valueOf(userDTO.getUserId()), action.getTimeInterval(), action.getTimeUnit());
    }

}
