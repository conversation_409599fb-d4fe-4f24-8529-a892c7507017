package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.redis.RedisKeysConstant;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.dto.necessaryContents.RoleDTO;
import com.cowell.scib.service.feign.PermissionFeignClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 角色缓存服务
 */
@Slf4j
@Component
public class RoleCacheServiceImpl {
    @Autowired
    private PermissionFeignClient permissionFeignClient;
    @Autowired
    private RedissonClient redisson;

    /**
     * 根据用户名模糊搜索用户
     */
    public List<RoleDTO> findRoleByRoleNameLike(String keyword) {
        if (keyword == null || keyword.isEmpty()) {
            return new ArrayList<>();
        }
        List<RoleDTO> roleList = getCacheRoles();
        return roleList.stream()
                .filter(role -> role.getName() != null && role.getName().contains(keyword))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有缓存的角色，将JSON字符串转换回List<RoleDTO>
     */
    public List<RoleDTO> getCacheRoles() {
        String cacheKey = RedisKeysConstant.SCIB_ALL_ROLE_KEY;
        RBucket<String> bucket = redisson.getBucket(cacheKey);
        String roleJson = bucket.get();
        if (roleJson == null || roleJson.isEmpty()) {
            List<RoleDTO> roleList = getRoles();
            saveCacheRoles(roleList);
            return roleList;
        }
        try {
            List<RoleDTO> roleList = JSON.parseArray(roleJson, RoleDTO.class);
            return roleList;
        } catch (Exception e) {
            log.info("从缓存获取角色列表失败",e);
        }
        return Lists.newArrayList();

    }

    /**
     * 定时器 保存所有角色到缓存，将List<RoleDTO>转换为JSON字符串
     */
    public void saveCacheRoles(List<RoleDTO> roleList) {
        if(CollectionUtils.isEmpty(roleList)){
            log.info("调用权限查询所有的角色类型|未查询导数据");
            return;
        }
        try {
            String cacheKey = RedisKeysConstant.SCIB_ALL_ROLE_KEY;
            RBucket<String> bucket = redisson.getBucket(cacheKey);
            bucket.set(JSON.toJSONString(roleList),6, TimeUnit.HOURS);
        } catch (Exception e) {
            log.info("保存所有的角色类型到缓存失败",e);
        }
    }

    /**
     * 查询所有的角色类型
     * @return
     */
    private List<RoleDTO> getRoles(){
        try {
            ResponseEntity<List<RoleDTO>> response = permissionFeignClient.getAllRoles();
            if (response == null
                    || !(response.getStatusCode().equals(HttpStatus.OK) || response.getStatusCode().equals(HttpStatus.CREATED))
                    || response.getBody() == null) {
                throw new BusinessErrorException("调用权限查询所有的角色类型失败");
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("调用权限查询所有的角色类型失", e);
        }
        return Lists.newArrayList();
    }
}
