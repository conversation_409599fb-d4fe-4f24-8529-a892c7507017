package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.TagService;
import com.cowell.scib.service.dto.EntityBase;
import com.cowell.scib.service.dto.StoreComponentQueryParam;
import com.cowell.scib.service.feign.TagGoodsFeignClient;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/23 15:26
 */
@Slf4j
@Service
public class TagServiceImpl implements TagService {

    @Autowired
    private TagGoodsFeignClient tagGoodsFeignClient;
    @Value("${scib.tag.batch.size:1000}")
    private long batchSize;

    @Override
    public List<Long> getSelectStoreIdList(Long selectId){
        log.info("getSelectStoreIdList|selectId:{}.", selectId);
        List<Long> storeIdList = null;
        try {
            StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
            queryParam.setId(selectId);
            queryParam.setBizCode(Constants.BIZCODE);
            queryParam.setBizType(Constants.BIZTYPE);
            storeIdList = Lists.newArrayList();
            for (int i = 1 ;; i++) {
                queryParam.setPage(Long.valueOf(i));
                queryParam.setPageSize(batchSize);
                log.info("getSelectStoreIdList.storeComponent.query param:{}", queryParam);
                ResponseEntity<CommonResult<PageResult<EntityBase>>> commonRes = tagGoodsFeignClient.query(queryParam);
//                log.debug("getSelectStoreIdList|commonRes:{}.", commonRes!=null? JSON.toJSONString(commonRes):"");
                if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                        || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())){
                    throw new BusinessErrorException("查询选择器门店信息异常");
                }
                PageResult<EntityBase> pageResult = commonRes.getBody().getData();
                List<EntityBase> entityBaseList =  pageResult.getRows();
                storeIdList.addAll(entityBaseList.stream().filter(v->StringUtils.isNotBlank(v.getEntityCode())).map(v->Long.parseLong(v.getEntityCode())).distinct().collect(Collectors.toList()));
                if (pageResult.getTotal() <= Long.valueOf(queryParam.getPage() * queryParam.getPageSize())) {
                    break;
                }
            }
//            log.debug("getSelectStoreIdList|storeIdList:{}.", storeIdList);
        } catch (Exception e) {
            log.warn("getSelectStoreIdList|查询选择器门店信息异常|error.", e);
            return Lists.newArrayList();
        }
        return storeIdList;
    }

    @Override
    public List<Long> getSelectStoreIdListByType(StoreComponentQueryParam queryParam){
        log.info("getSelectStoreIdList|queryParam:{}.", JSON.toJSONString(queryParam));
        List<Long> storeIdList = null;
        try {
            storeIdList = Lists.newArrayList();
            for (int i = 1 ;; i++) {
                queryParam.setPage(Long.valueOf(i));
                queryParam.setPageSize(batchSize);
                queryParam.setBizCode(Constants.BIZCODE);
                queryParam.setBizType(Constants.BIZTYPE);
                log.info("getSelectStoreIdListByType.storeComponent.query param:{}", queryParam);
                ResponseEntity<CommonResult<PageResult<EntityBase>>> commonRes = tagGoodsFeignClient.query(queryParam);
//                log.debug("getSelectStoreIdListByType|commonRes:{}.", commonRes!=null? JSON.toJSONString(commonRes):"");
                if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                        || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())){
                    throw new BusinessErrorException("查询选择器门店信息异常");
                }
                PageResult<EntityBase> pageResult = commonRes.getBody().getData();
                List<EntityBase> entityBaseList =  pageResult.getRows();
                storeIdList.addAll(entityBaseList.stream().filter(v->StringUtils.isNotBlank(v.getEntityCode())).map(v->Long.parseLong(v.getEntityCode())).distinct().collect(Collectors.toList()));
                if (pageResult.getTotal() <= Long.valueOf(queryParam.getPage() * queryParam.getPageSize())) {
                    break;
                }
            }
//            log.debug("getSelectStoreIdListByType|storeIdList:{}.", storeIdList);
        } catch (Exception e) {
            log.warn("getSelectStoreIdListByType|查询选择器门店信息异常|error.", e);
            return Lists.newArrayList();
        }
        return storeIdList;
    }
}
