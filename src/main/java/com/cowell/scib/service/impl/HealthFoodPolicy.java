package com.cowell.scib.service.impl;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.DicApiEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.CheckService;
import com.cowell.scib.service.IRuleAddService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import com.cowell.scib.service.param.rule.RuleAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HealthFoodPolicy implements IRuleAddService {
    @Autowired
    private CheckService checkService;

    @Override
    public void check(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        RuleDetailDTO DXBEBJSP = ruleAddParam.getDetailMap().get(Constants.DXBE_BJSP);
        if (Objects.isNull(DXBEBJSP) || StringUtils.isEmpty(DXBEBJSP.getPerprotyValue())) {
            throw new BusinessErrorException(ErrorCodeEnum.INVALID_TOKEN);
        }
        List<Double> nums;
        try {
            nums = Arrays.asList(DXBEBJSP.getPerprotyValue().split(",")).stream().map(Double::parseDouble).collect(Collectors.toList());
            log.info("nums:{}",nums);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessErrorException("保健食品-店型必备厂牌标牌价要求：1位小数。");
        }
        if (nums.size()!=2){
            throw new BusinessErrorException(ErrorCodeEnum.PARAM_ERROR);
        }
        if (nums.get(0).compareTo(nums.get(1))>=0){
            throw new BusinessErrorException("保健食品-店型必备厂牌标牌价要求：1~10之间，最小值＜最大值");
        }
    }

    @Override
    public String getCode() {
        return DicApiEnum.STOMUST_BJSP.getCode();
    }
}
