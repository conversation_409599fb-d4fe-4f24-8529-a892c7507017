package com.cowell.scib.service.impl;

import com.beust.jcommander.internal.Lists;
import com.cowell.scib.entity.DevelopModuleRecordFile;
import com.cowell.scib.entity.DevelopModuleRecordFileExample;
import com.cowell.scib.mapperDgms.DevelopModuleRecordFileMapper;
import com.cowell.scib.mapperDgms.extend.DevelopModuleRecordFileExtendMapper;
import com.cowell.scib.service.DevelopModuleRecordFileService;
import com.cowell.scib.service.dto.DevelopModuleRecordFileDTO;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.utils.KeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:28
 */
@Slf4j
@Service
public class DevelopModuleRecordFileServiceImpl implements DevelopModuleRecordFileService {

    @Autowired
    private DevelopModuleRecordFileMapper recordFileMapper;
    @Autowired
    private DevelopModuleRecordFileExtendMapper developModuleRecordFileExtendMapper;

    @Override
    public List<DevelopModuleRecordFile> listFileSort(String codeVersionKey) {
        if(StringUtils.isBlank(codeVersionKey)){
            return Lists.newArrayList();
        }
        DevelopModuleRecordFileExample recordFileExample = new DevelopModuleRecordFileExample();
        recordFileExample.createCriteria().andCodeVersionKeyEqualTo(codeVersionKey);
        return recordFileMapper.selectByExample(recordFileExample);
    }

    @Override
    public Map<String, List<DevelopModuleRecordFile>> mapFileSort(List<String> codeVersionKeyList) {
        if(CollectionUtils.isEmpty(codeVersionKeyList)){
            return new HashMap<>();
        }
        DevelopModuleRecordFileExample recordFileExample = new DevelopModuleRecordFileExample();
        recordFileExample.createCriteria().andCodeVersionKeyIn(codeVersionKeyList);
        List<DevelopModuleRecordFile> recordFileList = recordFileMapper.selectByExample(recordFileExample);
        Map<String, List<DevelopModuleRecordFile>> recordFileMap = recordFileList.stream().collect(Collectors.groupingBy(v->v.getCodeVersionKey()));
        return recordFileMap;
    }

    @Override
    public void addOrEditFile(String fileVOList, DevelopRecordAddParam developRecordAddParam, boolean isCreate) {
        if(StringUtils.isBlank(fileVOList)){
            log.info("addOrEditFile|无文件变更");
            return;
        }
        String codeVersionKey = KeyUtil.delevopFileKey(developRecordAddParam.getModuleCode(), developRecordAddParam.getDevelopVersion());
        log.info("addOrEditFile|codeVersionKey:{}.", codeVersionKey);
        List<DevelopModuleRecordFile> recordFileList = Arrays.stream(fileVOList.split(",")).map(v->{
            DevelopModuleRecordFile recordFile = new DevelopModuleRecordFile();
            recordFile.setFileUrl(v);
            recordFile.setDevelopVersion(developRecordAddParam.getDevelopVersion());
            recordFile.setModuleCode(developRecordAddParam.getModuleCode());
            recordFile.setCodeVersionKey(codeVersionKey);
            return recordFile;
        }).collect(Collectors.toList());
        if(!isCreate){
            DevelopModuleRecordFileExample fileExample = new DevelopModuleRecordFileExample();
            fileExample.createCriteria().andCodeVersionKeyIn(Lists.newArrayList(codeVersionKey));
            recordFileMapper.deleteByExample(fileExample);
            developModuleRecordFileExtendMapper.batchInsertSelective(recordFileList);
        }else{
            developModuleRecordFileExtendMapper.batchInsertSelective(recordFileList);
        }
    }

    @Override
    public void deleteFileById(Integer id) {
        recordFileMapper.deleteByPrimaryKey(id);
    }

    @Override
    public DevelopModuleRecordFile recordFileById(Integer id) {
        return recordFileMapper.selectByPrimaryKey(id);
    }

    @Override
    public String fileUrlListToString(List<DevelopModuleRecordFileDTO> fileList) {
        if(CollectionUtils.isEmpty(fileList)){
            return "";
        }
        List<String> fileUrlList = fileList.stream().filter(file->StringUtils.isNotBlank(file.getFileUrl())).map(file->file.getFileUrl()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fileUrlList)){
            return "";
        }
        log.info("fileUrlListToString|fileUrlList:{}.", fileUrlList);
        return StringUtils.join(fileUrlList, ",");
    }
}
