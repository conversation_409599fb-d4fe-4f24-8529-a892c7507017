package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskDetailExtMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskStoreDetailExtendMapper;
import com.cowell.scib.mapperTidb.extend.TrackRetultAllDetailExtendMapper;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.BundlTaskWriteService;
import com.cowell.scib.service.NoSequenceService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.BundlTaskDetailDTO;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.BundlTaskAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 第一步操作
 * <AUTHOR>
 * @date 2023/3/13 22:40
 */
@Slf4j
@Component
public class BundlTaskOneStep extends BundlTaskStepHandler<BundlTaskAddParam>{

    @Autowired
    private BundlTaskWriteService bundlTaskWriteService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;
    @Autowired
    private NoSequenceService noSequenceService;
    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtMapper;
    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;
    @Autowired
    private TrackRetultAllDetailExtendMapper trackRetultAllDetailExtendMapper;

    @Override
    public void add(BundlTaskAddParam bundlTaskAddParam) {
        //添加主表信息
        addTaskInfo(bundlTaskAddParam);
        Map<String, BundlTaskDetailDTO> detailDTOMap = bundlTaskAddParam.getDetailMap();
        if(MapUtils.isNotEmpty(detailDTOMap)){
            detailDTOMap.remove(DictGoodsEnum.GOODSLINE.getCode());
            detailDTOMap.remove(DictGoodsEnum.EXCLUDEATTRIBUTE.getCode());
            detailDTOMap.remove(DictGoodsEnum.GOODSBLACKLIST.getCode());
        }
        //添加明细
        bundlTaskWriteService.addTaskDetail(bundlTaskAddParam, bundlTaskAddParam.getUserDTO());
        //添加门店信息
        bundlTaskWriteService.addMdmStore(bundlTaskAddParam, bundlTaskAddParam.getUserDTO());
    }

    @Override
    public void deleteDetail(BundlTaskAddParam bundlTaskAddParam) {
        if(Objects.isNull(bundlTaskAddParam.getTaskId())){
            log.info("BundlTaskOneStep|添加不删除");
            return;
        }
        bundlingTaskDetailExtMapper.deleteDetailByStep(bundlTaskAddParam.getTaskId(), bundlTaskAddParam.getTaskStep());
        bundlingTaskStoreDetailExtendMapper.deleteDetailByTaskId(bundlTaskAddParam.getTaskId());
    }

    @Override
    public boolean isHandler(BundlTaskAddParam bundlTaskAddParam) {
        return BundlTaskStepEnum.ONE.getStep().equals(bundlTaskAddParam.getTaskStep());
    }


    @Override
    public boolean check(BundlTaskAddParam bundlTaskAddParam) {
        if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlTaskAddParam.getTaskType())){
            if(Objects.isNull(bundlTaskAddParam.getSelectStoreId())){
                throw new BusinessErrorException(ErrorCodeEnum.SELECTED_NODE_NULL_ERROR);
            }
            if(StringUtils.isBlank(bundlTaskAddParam.getPredictOpenTime())){
                throw new BusinessErrorException(ErrorCodeEnum.OPEN_DATE_NULL_ERROR);
            }
            if(!bundlTaskAddParam.getSaveAble()){
                Optional<MdmStoreExDTO> storeExtInfoByStoreId = CacheVar.getStoreExtInfoByStoreId(bundlTaskAddParam.getSelectStoreId());
                if (!storeExtInfoByStoreId.isPresent()){
                    throw new BusinessErrorException(ErrorCodeEnum.SELECTED_NODE_NOT_FIND_ERROR);
                }
                MdmStoreExDTO mdmStoreExDTO = storeExtInfoByStoreId.get();
                if(StringUtils.isBlank(mdmStoreExDTO.getSalesLevel())){
                    throw new BusinessErrorException(ErrorCodeEnum.SELECTED_NODE_DJ_ERROR);
                }
                if(StringUtils.isBlank(mdmStoreExDTO.getStoreAttr())){
                    throw new BusinessErrorException(ErrorCodeEnum.SELECTED_NODE_NOT_FIND_ERROR);
                }
                if(StringUtils.isBlank(mdmStoreExDTO.getProvince()) ||StringUtils.isBlank(mdmStoreExDTO.getCity())||StringUtils.isBlank(mdmStoreExDTO.getArea()) ){
                    throw new BusinessErrorException(ErrorCodeEnum.SELECTED_NODE_SHENGSHIXIAN_ERROR);
                }
            }
        }
        return false;
    }

    /**
     * 添加主表
     * @param bundlTaskAddParam
     */
    private void addTaskInfo(BundlTaskAddParam bundlTaskAddParam){
        List<OrgDTO> orgDTOList = permissionService.getParentOrgByIdAndType(bundlTaskAddParam.getPlateOrgId(), OrgTypeEnum.PLATFORM.getCode());
        OrgDTO orgDTO = orgDTOList.get(0);

        Long taskId = bundlTaskAddParam.getTaskId();
        Boolean isCreate = Objects.isNull(taskId);
        BundlingTaskInfo bundlingTaskInfo = buildBaseTaskInfo(orgDTO, bundlTaskAddParam, isCreate);
        if(isCreate){
            //添加
            bundlingTaskInfoMapper.insertSelective(bundlingTaskInfo);
            bundlTaskAddParam.setTaskId(bundlingTaskInfo.getId());
            //创建表任务结果表 tidb
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlTaskAddParam.getTaskType())){
                trackRetultAllDetailExtendMapper.createNewStoreRecommendTable(Constants.NEW_STORE_RECOMMEND_DETAIL+bundlTaskAddParam.getTaskId());
                trackRetultAllDetailExtendMapper.createNewStoreRecommendRankTable(Constants.NEW_STORE_RECOMMEND_RANK_DETAIL+bundlTaskAddParam.getTaskId());
            }else {
                trackRetultAllDetailExtendMapper.createTable(Constants.TRACK_RESULT_ALL_DETAIL+bundlTaskAddParam.getTaskId());
            }
        }else{
            //更新
            bundlingTaskInfoMapper.updateByPrimaryKeySelective(bundlingTaskInfo);
        }
    }

    private BundlingTaskInfo buildBaseTaskInfo(OrgDTO orgDTO, BundlTaskAddParam bundlTaskAddParam, Boolean isCreate) {
        BundlingTaskInfo bundlingTaskInfo = new BundlingTaskInfo();
        TokenUserDTO userDTO = bundlTaskAddParam.getUserDTO();
        if(isCreate){
            String taskCode =null;
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlTaskAddParam.getTaskType())){
                taskCode = noSequenceService.taskNoSequence(OrderTypeEnum.SCIB_XDML_TASK.getType());
                bundlingTaskInfo.setExtend(JSON.toJSONString(bundlTaskAddParam));
                bundlingTaskInfo.setTaskStatus(BundlTaskStatusEnum.SAVE.getCode());
            }else {
                taskCode = noSequenceService.taskNoSequence(OrderTypeEnum.SCIB_ZH_TASK.getType());
            }
            bundlTaskAddParam.setTaskCode(taskCode);
            bundlingTaskInfo.setTaskCode(taskCode);
            bundlingTaskInfo.setTaskName(Objects.isNull(bundlTaskAddParam.getTaskName())?taskCode:bundlTaskAddParam.getTaskName());
            bundlingTaskInfo.setCreatedBy(userDTO.getUserId());
            bundlingTaskInfo.setCreatedName(userDTO.getName());
            bundlingTaskInfo.setUpdatedBy(userDTO.getUserId());
            bundlingTaskInfo.setUpdatedName(userDTO.getName());
        }else{
            BundlingTaskInfo bundlingTaskInfo1 = bundlingTaskInfoMapper.selectByPrimaryKey(bundlTaskAddParam.getTaskId());
            if(Objects.isNull(bundlingTaskInfo1)){
                throw new AmisBusinessException(ErrorCodeEnum.TASK_ID_NOTEXIT);
            }
            bundlTaskAddParam.setTaskCode(bundlingTaskInfo1.getTaskCode());
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlTaskAddParam.getTaskType())){
                bundlingTaskInfo.setExtend(JSON.toJSONString(bundlTaskAddParam));
            }
            bundlingTaskInfo.setId(bundlTaskAddParam.getTaskId());
            bundlingTaskInfo.setTaskName(bundlingTaskInfo1.getTaskName());
            bundlingTaskInfo.setUpdatedBy(userDTO.getUserId());
            bundlingTaskInfo.setUpdatedName(userDTO.getName());
            //解绑selectId
            bundlingTaskInfo.setSelectId(0L);
        }
        bundlingTaskInfo.setOrgId(orgDTO.getId());
        bundlingTaskInfo.setOrgName(orgDTO.getName());
        bundlingTaskInfo.setTaskType(bundlTaskAddParam.getTaskType());
        bundlingTaskInfo.setMemo(bundlTaskAddParam.getMemo());
        return bundlingTaskInfo;
    }
}
