package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.BundlingTaskStoreDetailMapper;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.mapperTidb.extend.TrackRetultNewStoreAllDetailExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.newStore.TrackRetultNewStoreAllDetailDTO;
import com.cowell.scib.service.param.BundlStoreConfirmParam;
import com.cowell.scib.service.param.BundlTaskListParam;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.param.TaskDictParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.SelectorResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.HutoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cowell.scib.constant.Constants.NORMAL_STATUS;

/**
 * <AUTHOR>
 * @date 2023/3/10 16:52
 */
@Slf4j
@Service
public class BundlTaskServiceImpl implements BundlTaskService {

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;
    @Autowired
    private BundlingTaskInfoExtendMapper bundlingTaskInfoExtendMapper;
    @Autowired
    private BundlingTaskDetailExtMapper bundlingTaskDetailExtendMapper;
    @Autowired
    private BundlingTaskStoreDetailMapper bundlingTaskStoreDetailMapper;
    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;
    @Autowired
    private BundlingTaskDetailExtendExtMapper bundlingTaskDetailExtendExtMapper;
    @Autowired
    private TrackRetultNewStoreAllDetailExtendMapper trackRetultNewStoreAllDetailExtendMapper;
    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;
    @Autowired
    private ConfigOrgDetailExMapper configOrgDetailExMapper;
    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;
    @Autowired
    private ConfigOrgDetailExtendExtendMapper configOrgDetailExtendExtendMapper;
    @Autowired
    private RuleService ruleService;
    @Autowired
    private CommonDictionaryExtendMapper commonDictionaryExtendMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private CheckService checkService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private TagService tagService;
    @Autowired
    private NecessaryContentsService necessaryContentsService;

    @Override
    public PageResult<BundlTaskListDTO> listBundlTask(BundlTaskListParam listParam, TokenUserDTO userDTO) {
        List<BundlTaskListDTO> bundlTaskListDTOList = Collections.emptyList();
        Long userId = userDTO.getUserId();
        if(!checkService.judgeUserRoleExit(Constants.SUPPER_ROLE, userId)){
            log.info("listBundlTask|非超级管理员，根据用户ID查寻:{}.", userId);
            listParam.setCreateBy(userId);
        }
        listParam.setOffset((listParam.getPage()-1)*listParam.getPerPage());
        resetTime(listParam);
        resetListParam(listParam);
        boolean searchResult = searchTaskIdParam(listParam);
        if(searchResult){
            return new PageResult(0L, Collections.EMPTY_LIST);
        }
        resetMapParam(listParam);
        long count = bundlingTaskInfoExtendMapper.searchBundlTaskInfoCount(listParam);
        if(count > 0){
            List<BundlingTaskInfo> taskInfoList = bundlingTaskInfoExtendMapper.searchBundlTaskInfoList(listParam);
            bundlTaskListDTOList = taskInfoList.stream().map(v->{
                BundlTaskListDTO taskListDTO = new BundlTaskListDTO();
                copyTaskBeanInfo(v, taskListDTO, listParam.getTaskDetailMap());
                return taskListDTO;
            }).collect(Collectors.toList());
        }
        return new PageResult(count, bundlTaskListDTOList);
    }

    @Override
    public SelectorResult listBundlCompany(Long plateOrgId, TokenUserDTO userDTO) {
        List<OrgSimpleDTO> orgSimpleDTOList = permissionService.getUserDataScopeChildOrgByOrgId(userDTO.getUserId(), Lists.newArrayList(plateOrgId), true);
        if(CollectionUtils.isEmpty(orgSimpleDTOList)){
            log.warn("该平台下没有子组织信息：{}.", plateOrgId);
            return null;
        }
        List<OptionDto> companyOptions = orgSimpleDTOList.stream().filter(v-> OrgTypeEnum.BUSINESS.getCode().equals(v.getType())).map(v->{
            OptionDto optionDto = new OptionDto(v.getName(), v.getId().toString());
            return optionDto;
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(companyOptions)){
            log.warn("该平台下没有子公司信息：{}.", plateOrgId);
            return null;
        }
        SelectorResult selectorResult = new SelectorResult(companyOptions);
        return selectorResult;
    }

    @Override
    public SelectorResult listBundlType(Long plateOrgId, TokenUserDTO userDTO) {
        if(checkService.fullPermByOrgId(plateOrgId, userDTO.getUserId())){
            return new SelectorResult(BundlTaskTypeEnum.listValue());
        }else{
            return new SelectorResult(BundlTaskTypeEnum.listBusinessValue());
        }
    }

    @Override
    public SelectorResult listBundlGoods(Long plateOrgId, TokenUserDTO userDTO) {
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(plateOrgId,Byte.valueOf("2"), null);
        if(Objects.isNull(configOrg)){
            log.warn("listBundlGoods|该平台没有配置必备规则：{}", plateOrgId);
            return null;
        }
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(),Lists.newArrayList("goodsType"));
        if(CollectionUtils.isEmpty(configOrgDetails)){
            log.warn("listBundlGoods|该平台必备规则没有配置组货商品，平台：{}，配置：{}。", plateOrgId, configOrg.getId());
            return null;
        }
        List<OptionDto> optionDtoList = configOrgDetails.stream().map(v->{
            return new OptionDto(v.getPerprotyValue(), v.getPerprotyValue());
        }).collect(Collectors.toList());

        return new SelectorResult(optionDtoList);
    }

    @Override
    public SelectorResult listSearch(Integer searchType, TokenUserDTO userDTO) {
        switch (BundlSearchEnum.getEnum(searchType)) {
            case TASK:
                return new SelectorResult(BundlTaskTypeEnum.listValue());
            case GOODS:
                return new SelectorResult(dicValueList(BudnlTaskDetailDicEnum.GOODS.getCode()).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList()));
            case STORE:
                return new SelectorResult(dicValueList(BudnlTaskDetailDicEnum.STORE.getCode()).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList()));
            case ZS:
                return new SelectorResult(dicValueList(BudnlTaskDetailDicEnum.ZS.getCode()).stream().map(v -> new OptionDto(v.getEnumName(), v.getEnumValue())).collect(Collectors.toList()));
            case STATUS:
                return new SelectorResult(Arrays.stream(BundlTaskStatusEnum.values()).filter(v->v.getCode()!=BundlTaskStatusEnum.CREATING.getCode()).map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList()));
            default:
                break;
        }
        return null;
    }

    @Override
    public PageResult<BundlStoreConfirmDTO> listConfirmBundlStore(BundlStoreConfirmParam confirmParam, TokenUserDTO userDTO) {
        if(Objects.isNull(confirmParam.getTaskId())){
            log.warn("任务未创建或者任务参数未来传");
            return new PageResult(0L, Collections.EMPTY_LIST);
        }
        confirmParam.setOffset((confirmParam.getOffset()-1)*confirmParam.getPerPage());
        if(StringUtils.isNotBlank(confirmParam.getStoreCode())){
            confirmParam.setStoreCodeList(Arrays.asList(confirmParam.getStoreCode().split(",")));
        }
        List<BundlStoreConfirmDTO> bundlStoreConfirmDTOList = Collections.emptyList();
        long count = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreCount(confirmParam);
        if(count > 0){
            List<BundlingTaskStoreDetail> storeDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreList(confirmParam);
            Map<String, String> dictMap = dicValueMap(Lists.newArrayList(BudnlTaskDetailDicEnum.STORE.getCode(), BudnlTaskDetailDicEnum.ZS.getCode()));
            bundlStoreConfirmDTOList = storeDetailList.stream().map(v->{
                BundlStoreConfirmDTO storeConfirmDTOt = new BundlStoreConfirmDTO();
                copyTaskStoreBeanInfo(v, storeConfirmDTOt, dictMap);
                return storeConfirmDTOt;
            }).collect(Collectors.toList());
        }
        return new PageResult(count, bundlStoreConfirmDTOList);
    }

    @Override
    public Map<String, BundlTaskDetailDTO> getTaskRuleList(TaskDictParam taskDictParam, TokenUserDTO userDTO){
        if(Objects.isNull(taskDictParam.getOrgId())){
            throw new AmisBusinessException(ErrorCodeEnum.ORG_INFO_PLATFORM_NOT_EXIST);
        }
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectByScopeCode(taskDictParam.getScopeCode());
        if (CollectionUtils.isEmpty(commonDictionaries)){
            throw new AmisBusinessException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST);
        }
        ArrayList<BundlTaskDetailDTO> nullRuleDetailDTO = new ArrayList<>();
        if (Objects.isNull(taskDictParam.getTaskId())){
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, taskDictParam.getScopeCode());
        }

        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskDictParam.getTaskId());
        if(Objects.isNull(bundlingTaskInfo) || !taskDictParam.getOrgId().equals(bundlingTaskInfo.getOrgId())){
            //平台变了，也要重新获取
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, taskDictParam.getScopeCode());
        }

        //只查询当前域得数据
        List<BundlingTaskDetail> configOrgDetails = bundlingTaskDetailExtendMapper.selectByTaskIdAndDictCodes(taskDictParam.getTaskId(),commonDictionaries.stream().map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(configOrgDetails)){
            return getStringRuleDetailDTOMap(commonDictionaries, nullRuleDetailDTO, taskDictParam.getScopeCode());
        }

        List<Long> taskDetailIdList = configOrgDetails.stream().map(v->v.getId()).collect(Collectors.toList());

        List<BundlingTaskDetailExtend> configOrgDetailExtends = bundlingTaskDetailExtendExtMapper.selectExtendByDetailId(taskDictParam.getTaskId(), taskDetailIdList);
        Map<Long, List<BundlingTaskDetailExtend>> configDetailExtendMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(configOrgDetailExtends)) {
            configDetailExtendMap = configOrgDetailExtends.stream().collect(Collectors.groupingBy(BundlingTaskDetailExtend::getTaskDetailId));
        }
        Map<Long, List<BundlingTaskDetailExtend>> finalConfigDetailExtendMap = configDetailExtendMap;

        return getTaskRuleDetailDTOMap(commonDictionaries,configOrgDetails,taskDictParam.getScopeCode(),finalConfigDetailExtendMap);
    }

    @Override
    public PageResult<CommonGoodsDTO> queryTaskGroupBlackGoods(TaskDictParam taskDictParam, TokenUserDTO userDTO) {
        Boolean isCreate = Objects.isNull(taskDictParam.getTaskId());
        if(isCreate){
            RuleParam ruleParam = new RuleParam();
            BeanUtils.copyProperties(taskDictParam, ruleParam);
            ruleParam.setOrgId(taskDictParam.getOrgId());
            ruleParam.setConfigType(ConfigTypeEnum.BB.getType().toString());
            return ruleService.queryGroupBlackGoods(ruleParam, userDTO);
        }

        List<BundlingTaskDetail> configOrgDetails = bundlingTaskDetailExtendMapper.selectByTaskIdAndDictCodes(taskDictParam.getTaskId(), Arrays.asList(Constants.GOODS_BLACK_LIST));
        if (CollectionUtils.isEmpty(configOrgDetails)){
            return null;
        }
        List<Long> detailIdList = configOrgDetails.stream().map(v->v.getId()).collect(Collectors.toList());
        Long totalCount = bundlingTaskDetailExtendExtMapper.countTaskDetailExtend(taskDictParam.getTaskId(), detailIdList);
        taskDictParam.setPage((taskDictParam.getPage()-1)* taskDictParam.getPerPage());
        List<BundlingTaskDetailExtend> configOrgDetailExtends = bundlingTaskDetailExtendExtMapper.selectDetailExtendByPage(taskDictParam.getTaskId(), detailIdList,taskDictParam.getPage(),taskDictParam.getPerPage());
        if (CollectionUtils.isEmpty(configOrgDetailExtends)){
            return null;
        }
        ArrayList<CommonGoodsDTO> commonGoodsDTOList = new ArrayList<>();
        configOrgDetailExtends.stream().forEach(v->{
            CommonGoodsDTO commonGoodsDTO = JSONObject.parseObject(v.getExtend(), CommonGoodsDTO.class);
            commonGoodsDTOList.add(commonGoodsDTO);
        });
        return new PageResult<>(totalCount,commonGoodsDTOList);
    }

    @Override
    public List<MdmStoreBaseDTO> selectMdmStoreFilterStoreType(Long plateOrgId, Long userId, List<Long> companyOrgIdList, Map<String, List<String>> dictMap){
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = selectMdmStoreByPlatformAndUser(plateOrgId, userId, companyOrgIdList);
        Map<String, Boolean> shopMap = new HashMap<>();
        if(MapUtils.isNotEmpty(dictMap)){
            dictMap.getOrDefault(BudnlTaskDetailDicEnum.STORE.getCode(), Lists.newArrayList()).stream().forEach(v->shopMap.put(v, true));
            dictMap.getOrDefault(BudnlTaskDetailDicEnum.ZS.getCode(), Lists.newArrayList()).stream().forEach(v->shopMap.put(v, true));
        }
        Iterator<MdmStoreBaseDTO> mdmStoreBaseDTOIterator = mdmStoreBaseDTOList.iterator();
        while(mdmStoreBaseDTOIterator.hasNext()){
            MdmStoreBaseDTO mdmStoreBaseDTO = mdmStoreBaseDTOIterator.next();
            MdmStoreExDTO mdmStoreExDTO = necessaryContentsService.getCopyStoreInfo(mdmStoreBaseDTO.getStoreId());
            if(MapUtils.isNotEmpty(shopMap) && mdmStoreExDTO!=null && shopMap.get(mdmStoreExDTO.getZsStoreTypeCode())==null && shopMap.get(mdmStoreExDTO.getStoreTypeCode())==null){
                mdmStoreBaseDTOIterator.remove();
            }
        }
        return mdmStoreBaseDTOList;
    }

    @Override
    public List<Long> selectMdmStoreIdFilterSelector(Long plateOrgId, Long userId, List<Long> companyOrgIdList){
        log.info("selectMdmStoreIdFilterSelector|plateOrgId:{}.userId:{}.", plateOrgId, userId);
        if(Objects.isNull(plateOrgId) || Objects.isNull(userId)){
            log.warn("selectMdmStoreFilterSelector|入参为空");
            return Collections.EMPTY_LIST;
        }
        RuleParam ruleParam = new RuleParam();
        ruleParam.setOrgId(plateOrgId);
        ruleParam.setScopeCode(DicApiEnum.ZH_STORE.getCode());
        ruleParam.setConfigType(ConfigTypeEnum.BB.getType().toString());
        Map<String, List<String>> selectParamMap = buildSelectParam(ruleParam, null);
        if(MapUtils.isEmpty(selectParamMap)){
            log.warn("selectMdmStoreFilterSelector|选择器入参为空");
            return Collections.EMPTY_LIST;
        }
        if (MapUtils.isEmpty(selectParamMap)) {
            return new ArrayList<>();
        }
        List<String> managstateList = selectParamMap.get(DictAliasEnum.MANAGSTATE.getAlias());
        if (CollectionUtils.isEmpty(managstateList) || !managstateList.stream().filter(StringUtils::isNotBlank).findAny().isPresent()) {
            return new ArrayList<>();
        }
        StoreComponentQueryParam queryParam = new StoreComponentQueryParam();
        queryParam.setManagstate(selectParamMap.get(DictAliasEnum.MANAGSTATE.getAlias()));
        queryParam.setStorestatus(selectParamMap.get(DictAliasEnum.STORESTATUS.getAlias()));
        queryParam.setFormat(selectParamMap.get(DictAliasEnum.FORMAT.getAlias()));
        queryParam.setOrgIds(selectParamMap.get(DictAliasEnum.ORGIDS.getAlias()));
        queryParam.setStoretype(selectParamMap.get(DictAliasEnum.STORETYPE.getAlias()));
        queryParam.setOperationtype(selectParamMap.get(DictAliasEnum.OPERATIONTYPE.getAlias()));
        queryParam.setSpecialtype(selectParamMap.get(DictAliasEnum.SPECIALTYPE.getAlias()));
        queryParam.setExcludeOrgIds(selectParamMap.get(DictAliasEnum.EXCLUDEORGIDS.getAlias()));
        queryParam.setStoreattr(selectParamMap.get(DictAliasEnum.STOREATTR.getAlias()));
        queryParam.setScopeCode(plateOrgId.toString());
        List<Long> selectorStoreIdList = tagService.getSelectStoreIdListByType(queryParam);

//        List<ChildOrgsDTO> childOrgsDTOS = permissionService.listChildOrgAssignedType(com.google.common.collect.Lists.newArrayList(plateOrgId), OrgTypeEnum.STORE.getCode());
//        if (CollectionUtils.isEmpty(childOrgsDTOS)) {
//            log.info("没有获取到平台下配置的门店");
//            return new ArrayList<>();
//        }
//        List<Long> children = childOrgsDTOS.get(0).getChildren().stream().filter(v->Objects.nonNull(v.getOutId())).map(OrgDTO::getOutId).collect(Collectors.toList());
//        if (org.apache.commons.collections4.CollectionUtils.isEmpty(children)) {
//            log.info("没有获取到平台下配置的门店");
//            return new ArrayList<>();
//        }
//        selectorStoreIdList = selectorStoreIdList.stream().filter(v -> children.contains(v)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selectorStoreIdList)) {
            log.info("没有获取到平台下配置的门店");
            return new ArrayList<>();
        }
        return selectorStoreIdList;
    }

    @Override
    public List<MdmStoreExDTO> selectMdmStoreByTaskId(Long taskId, Long companyOrgId, String city,List<String> platStoreTypeCodeList, List<String> storeTypeCodeList, List<String> zsStoreTypeCodeList) {
        log.info("selectMdmStoreByTaskId|taskId:{}.platStoreTypeCodeList:{}.storeTypeCodeList:{}.zsStoreTypeCodeList:{}.", taskId, platStoreTypeCodeList, storeTypeCodeList, zsStoreTypeCodeList);
        if(Objects.isNull(taskId)){
            return Collections.EMPTY_LIST;
        }
        List<Long> taskStoreIdList = bundlingTaskStoreDetailExtendMapper.searchStoreIdListByTaskIdAndStoreType(taskId, companyOrgId, city, platStoreTypeCodeList, storeTypeCodeList, zsStoreTypeCodeList);
        if(CollectionUtils.isEmpty(taskStoreIdList)){
            return Collections.EMPTY_LIST;
        }
        return taskStoreIdList.stream().map(v -> CacheVar.getStoreExtInfoByStoreId(v)).filter(v -> v.isPresent()).map(Optional::get).collect(Collectors.toList());
    }

    @Override
    public Long judgeSelectIdByTask(Long taskId, TokenUserDTO userDTO) {
        log.debug("judgeSelectIdByTask|taskId:{}.", taskId);
        if(Objects.isNull(taskId)){
            return null;
        }
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if(Objects.isNull(bundlingTaskInfo)){
            log.debug("judgeSelectIdByTask|没查到相关任务信息|taskId:{}.", taskId);
            return null;
        }
        Long selectId = bundlingTaskInfo.getSelectId();
        if(Objects.isNull(selectId) || selectId.compareTo(0L)<=0){
            log.debug("judgeSelectIdByTask|selectId不符合要求|taskId:{}.", taskId);
            return null;
        }
        return selectId;
    }

    @Override
    public Map<String, List<String>> buildSelectParam(RuleParam ruleParam, TokenUserDTO userDTO) {
        List<CommonDictionary> commonDictionaries = commonDictionaryExtendMapper.selectByScopeCode(ruleParam.getScopeCode());
        if (CollectionUtils.isEmpty(commonDictionaries)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }

        Map<String, List<String>> selectParamMap = new HashMap<>(commonDictionaries.size());
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(ruleParam.getOrgId(),Byte.valueOf(ruleParam.getConfigType()), null);
        if(Objects.isNull(configOrg)){
            buileSelectMapByBaseDict(commonDictionaries, selectParamMap);
            return selectParamMap;
        }

        //只查询当前域得数据
        List<ConfigOrgDetail> configOrgDetails = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(),commonDictionaries.stream().map(CommonDictionary::getDictCode).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(configOrgDetails)){
            buileSelectMapByBaseDict(commonDictionaries, selectParamMap);
            return selectParamMap;
        }
        Map<String, List<ConfigOrgDetail>> configOrgDetailMap = configOrgDetails.stream().filter(v->StringUtils.isNotBlank(v.getDictCode())).collect(Collectors.groupingBy(ConfigOrgDetail::getDictCode));
        List<String> dicCodeList = commonDictionaries.stream().filter(v-> StringUtils.isNotBlank(v.getDictCode())).map(v->v.getDictCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dicCodeList)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_TYPE_NOT_EXIST.getMsg());
        }
        List<CommonEnums> commonEnumsList = commonEnumsExtendMapper.selectByDicCodeList(dicCodeList);
        if (CollectionUtils.isEmpty(commonEnumsList)){
            throw new BusinessErrorException(ErrorCodeEnum.RULE_ENUM_NOT_EXIST.getMsg());
        }
        Map<String,CommonEnums> commonEnumsMap = commonEnumsList.stream().filter(v->StringUtils.isNotBlank(v.getEnumValue())).collect(Collectors.toMap(v->new StringBuilder().append(v.getPropertyCode()).append("_").append(v.getEnumValue()).toString(), Function.identity(), (k1,k2)->k1));
        commonDictionaries.forEach(dict->{
            if(StringUtils.isBlank(dict.getAlias())){
                return;
            }
            List<ConfigOrgDetail> orgDetails = configOrgDetailMap.get(dict.getDictCode());
            if(CollectionUtils.isEmpty(orgDetails)){
                return;
            }
//            //设置单属性值
            List<String> nameList = orgDetails.stream().map(v->{
                CommonEnums commonEnums = commonEnumsMap.get(v.getDictCode() + "_" + v.getPerprotyValue());
                return commonEnums!=null?commonEnums.getEnumName():"";
            }).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            selectParamMap.put(dict.getAlias(), nameList);
            //设置集合属性值
            orgDetails.forEach(v->{
                if(Constants.COLLECTION.equals(v.getPerprotyType())){
                    if(v.getId() > 0L){
                        List<ConfigOrgDetailExtend>  detailExtendList = configOrgDetailExtendExtendMapper.selectByDetailId(configOrg.getId(), v.getId());
                        selectParamMap.put(dict.getAlias(), CollectionUtils.isNotEmpty(detailExtendList)?detailExtendList.stream().map(ConfigOrgDetailExtend::getKeyword).filter(StringUtils::isNotBlank).collect(Collectors.toList()):Lists.newArrayList());
                    }else {
                        selectParamMap.put(dict.getAlias(), Lists.newArrayList());
                    }
                }
            });
        });
        log.debug("buildSelectParam|最终返回|selectParamMap:{}.", selectParamMap);
        return selectParamMap;
    }

    @Override
    public BundlTaskInfoDTO getTaskInfo(Long taskId, TokenUserDTO userDTO) {
        if(Objects.isNull(taskId)){
            return new BundlTaskInfoDTO();
        }
        BundlTaskInfoDTO bundlTaskInfoDTO = new BundlTaskInfoDTO();
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        BeanUtils.copyProperties(bundlingTaskInfo, bundlTaskInfoDTO);
        if (StringUtils.isNotBlank(bundlingTaskInfo.getExtend())) {
            bundlTaskInfoDTO.initXDMLParams(JSONObject.parseObject(bundlingTaskInfo.getExtend()));
        }
        if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
            statisticsResult(taskId,bundlTaskInfoDTO);
        }
        bundlTaskInfoDTO.setTaskId(bundlingTaskInfo.getId());
        return bundlTaskInfoDTO;
    }

    private  void statisticsResult(Long taskId, BundlTaskInfoDTO result ){
        long count = trackRetultNewStoreAllDetailExtendMapper.count(taskId);
        List<TrackRetultNewStoreAllDetail> list = new ArrayList<>();
        if (count > 0) {
            AtomicInteger index = new AtomicInteger(0);
            for (int i = 0; i <= count;  ) {
                List<TrackRetultNewStoreAllDetail> trackRetultNewStoreAllDetailDTOS = trackRetultNewStoreAllDetailExtendMapper.selectByTaskId(taskId, i, Constants.EXPORT_ONCE_QUERY_MAX)
                        .stream().filter(v -> v.getJyAble() == 1).collect(Collectors.toList());
                log.info("查询数据条数：{}", trackRetultNewStoreAllDetailDTOS.size());
                i += Constants.EXPORT_ONCE_QUERY_MAX;
                if(CollectionUtils.isNotEmpty(trackRetultNewStoreAllDetailDTOS)) {
                    list.addAll(trackRetultNewStoreAllDetailDTOS);
                }
            }
        }
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        result.setExistResult(Boolean.TRUE);
        long skuCount = list.stream()
                .map(TrackRetultNewStoreAllDetail::getGoodsId)
                .distinct()
                .count();
        result.setSkuCount(skuCount);
        // 1. 统计 classone_name = 中西成药 的 goods_id 数量作为SKU个数
        long zhongXiChengYaoCount = list.stream()
                .filter(dto -> "中西成药".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getGoodsId)
                .distinct()
                .count();
        result.setZhongXiChengYaoSkuCount(zhongXiChengYaoCount);

        // 2. 统计 classone_name = 中西成药 时 rx_otc=RX 或 rx_otc=OTC 的数量
        Map<String, Long> rxOtcCounts = list.stream()
                .filter(dto -> "中西成药".equals(dto.getClassoneName()))
                .filter(dto -> "RX".equals(dto.getRxOtc()) || "OTC".equals(dto.getRxOtc()))
                .collect(Collectors.groupingBy(TrackRetultNewStoreAllDetail::getRxOtc, Collectors.counting()));
        result.setRxCount(rxOtcCounts.getOrDefault("RX", 0L));
        result.setOtcCount(rxOtcCounts.getOrDefault("OTC", 0L));

        // 3. 统计 classone_name = 中药参茸 且 classtwo_name != 配方中药 的 goods_id 个数
        long zhongYaoCangRongCount = list.stream()
                .filter(dto -> "中药参茸".equals(dto.getClassoneName()) && !"配方中药".equals(dto.getClasstwoName()))
                .map(TrackRetultNewStoreAllDetail::getGoodsId)
                .distinct()
                .count();
        result.setZhongYaoCangRongCount(zhongYaoCangRongCount);

        // 4. 统计 classone_name = 保健食品 的 goods_id 数量作为SKU个数
        long healthFoodCount = list.stream()
                .filter(dto -> "保健食品".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getGoodsId)
                .distinct()
                .count();
        result.setHealthFoodSkuCount(healthFoodCount);

        // 5. 统计 classone_name = 医疗器械 的 goods_id 数量作为SKU个数
        long medicalDeviceCount = list.stream()
                .filter(dto -> "医疗器械".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getGoodsId)
                .distinct()
                .count();
        result.setMedicalDeviceSkuCount(medicalDeviceCount);

        // 6. 统计其他类别的 goods_id 数量作为SKU个数
        long otherCount = list.stream()
                .filter(dto -> !"中西成药".equals(dto.getClassoneName())
                        && !"中药参茸".equals(dto.getClassoneName())
                        && !"保健食品".equals(dto.getClassoneName())
                        && !"医疗器械".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getGoodsId)
                .distinct()
                .count();
        result.setOtherSkuCount(otherCount);
        // 新增：统计中西成药的成分个数
        Set<String> zhongXiChengYaoComponents = list.stream()
                .filter(dto -> "中西成药".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getComponent)
                .filter(component -> component != null && !component.isEmpty())
                .collect(Collectors.toCollection(HashSet::new));
        result.setZhongXiChengYaoComponentCount(zhongXiChengYaoComponents.size());

        // 新增：统计中药参茸（非配方中药）的成分个数
        Set<String> zhongYaoCangRongComponents = list.stream()
                .filter(dto -> "中药参茸".equals(dto.getClassoneName()) && !"配方中药".equals(dto.getClasstwoName()))
                .map(TrackRetultNewStoreAllDetail::getComponent)
                .filter(component -> component != null && !component.isEmpty())
                .collect(Collectors.toCollection(HashSet::new));
        result.setZhongYaoCangRongComponentCount(zhongYaoCangRongComponents.size());
        // 新增：统计保健食品的子类数量
        Set<String> healthFoodSubcategories = list.stream()
                .filter(dto -> "保健食品".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getClassfourName)
                .filter(subCategory -> subCategory != null && !subCategory.isEmpty())
                .collect(Collectors.toCollection(HashSet::new));
        result.setHealthFoodSubcategoryCount(healthFoodSubcategories.size());

        // 新增：统计医疗器械的子类数量
        Set<String> medicalDeviceSubcategories = list.stream()
                .filter(dto -> "医疗器械".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getClassfourName)
                .filter(subCategory -> subCategory != null && !subCategory.isEmpty())
                .collect(Collectors.toCollection(HashSet::new));
        result.setMedicalDeviceSubcategoryCount(medicalDeviceSubcategories.size());

        // 新增：统计其他类别的子类数量
        Set<String> otherSubcategories = list.stream()
                .filter(dto -> !"中西成药".equals(dto.getClassoneName())
                        && !"中药参茸".equals(dto.getClassoneName())
                        && !"保健食品".equals(dto.getClassoneName())
                        && !"医疗器械".equals(dto.getClassoneName()))
                .map(TrackRetultNewStoreAllDetail::getClassfourName)
                .filter(subCategory -> subCategory != null && !subCategory.isEmpty())
                .collect(Collectors.toCollection(HashSet::new));
        result.setOtherSubcategoryCount(otherSubcategories.size());

        // 新增：计算建议首次备货数量总和
        BigDecimal totalSuggestPhQty = list.stream()
                .map(TrackRetultNewStoreAllDetail::getSuggestPhQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalSuggestPhQty(totalSuggestPhQty);

        // 新增：计算备货库存成本金额总和
        BigDecimal totalPhCost = list.stream()
                .map(TrackRetultNewStoreAllDetail::getPhCost)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalPhCost(totalPhCost);
    }

    @Override
    public List<String> queryAlreadyBundlStoreListAndTaskType(Long orgId,List<Byte> taskTypeList,String storeSapCode){
        List<Byte> taskStatusList=new ArrayList<>();
        if(taskTypeList.contains(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode())){
            taskStatusList.add(BundlTaskStatusEnum.COMPUTING.getCode());
            List<Long> taskIdList = bundlingTaskInfoExtendMapper.searchTaskIdWithOrgIdAndTaskType(orgId,taskStatusList,taskTypeList);
            if(CollectionUtils.isEmpty(taskIdList)){
                return null;
            }
            return bundlingTaskStoreDetailExtendMapper.searchTaskCodeBytaskIdListAndStoreCode(taskIdList,storeSapCode);
        }else {
            taskStatusList.add(BundlTaskStatusEnum.COMPUTING.getCode());
            taskStatusList.add(BundlTaskStatusEnum.COMPUTED.getCode());
            List<Long> taskIdList = bundlingTaskInfoExtendMapper.searchTaskIdWithOrgIdAndTaskType(orgId,taskStatusList,taskTypeList);
            if(CollectionUtils.isEmpty(taskIdList)){
                return null;
            }
            return bundlingTaskStoreDetailExtendMapper.searchStoreCodeByTaskCode(taskIdList);
        }

    }

    /**
     * 通过字典查询值，返回map
     * @param dicCodeList
     * @return
     */
    @Override
    public Map<String, String> dicValueMap(List<String> dicCodeList){
        List<CommonEnums> commonEnumsList= commonEnumsExtendMapper.selectByDicCodeList(dicCodeList);
        if(CollectionUtils.isEmpty(commonEnumsList)){
            log.warn("dicValueList|没有查到对应的字典：{}。", dicCodeList);
            return new HashMap<>();
        }
        Map<String, String> dictMap = new HashMap<>(commonEnumsList.size());
        commonEnumsList.forEach(v->{
            dictMap.put(v.getPropertyCode() + Constants.KLINE + v.getEnumValue(), v.getEnumName());
        });
        return dictMap;
    }

    @Override
    public BundlTaskInfoDTO getTaskDetailInfo(Long taskId, TokenUserDTO userDTO) {
        BundlTaskInfoDTO taskInfo = getTaskInfo(taskId,userDTO);
        if (Objects.nonNull(taskInfo)){
            taskInfo.setTaskTypeName(BundlTaskTypeEnum.getMessageByCode(taskInfo.getTaskType()));
            List<BundlingTaskDetail> bundlingTaskDetails = bundlingTaskDetailExtendMapper.selectDetailByTaskId(taskId);
            List<BundlingTaskDetail> zsStoreList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.ZS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(zsStoreList)){
                List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(null, zsStoreList.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(commonEnums)){
                    taskInfo.setZSStoreType(commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()));
                }
            }
            List<BundlingTaskDetail> zhStoreList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.STORE.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(zhStoreList)){
                List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.STORE.getCode()), zhStoreList.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(commonEnums)){
                    taskInfo.setGroupStoreType(commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()));
                }
            }
            List<BundlingTaskDetail> zhBusinessList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.BUSINESS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(zhBusinessList)){
                ArrayList<String> zhBusinessLists = new ArrayList<>();
                for (BundlingTaskDetail bundlingTaskDetail : zhBusinessList) {
                    OrgInfoBaseCache company = CacheVar.getBusinessByOrgId(Long.valueOf(bundlingTaskDetail.getPerprotyValue())).orElseThrow(() -> new AmisBadRequestException("没有查询到企业信息,请联系管理员"));
                    zhBusinessLists.add(company.getBusinessShortName());
                }
                taskInfo.setBusinessName(zhBusinessLists);
            }
            List<BundlingTaskDetail> goodCategoryList = bundlingTaskDetails.stream().filter(v -> v.getDictCode().equals(BudnlTaskDetailDicEnum.GOODS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(goodCategoryList)){
                List<CommonEnums> commonEnums = commonEnumsExtendMapper.selectByDicCodeAndValueList(Arrays.asList(BudnlTaskDetailDicEnum.GOODS.getCode()), goodCategoryList.stream().map(BundlingTaskDetail::getPerprotyValue).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(commonEnums)){
                    taskInfo.setGoodCategory(commonEnums.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()));
                }
            }
        }
        return taskInfo;
    }

    @Override
    public void exportStores(Long taskId, HttpServletResponse response, TokenUserDTO userDTO) throws Exception {
        log.info("exportStores|taskId:{}.userId:{}.", taskId, userDTO.getUserId());
        try {
            String fileName = "组货门店清单" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            List<ListToExcelMultiSheetDTO> listDto = new ArrayList<>();
            ListToExcelMultiSheetDTO dto = new ListToExcelMultiSheetDTO();
            dto.setSheetName("sheet0");
            dto.setFieldMap(BundlStoreConfirmDTO.getCommonStoresExportMap());
            dto.setListGroup(new ArrayList(1));

            List<BundlingTaskStoreDetail> bundlingTaskStoreDetailList = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);
            if (CollectionUtils.isEmpty(bundlingTaskStoreDetailList)) {
                listDto.add(dto);
                HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
                return;
            }
            List<BundlStoreConfirmDTO> bundlStoreConfirmDTOList = null;
            Map<String, String> dictMap = dicValueMap(Lists.newArrayList(BudnlTaskDetailDicEnum.STORE.getCode(), BudnlTaskDetailDicEnum.ZS.getCode()));
            bundlStoreConfirmDTOList = bundlingTaskStoreDetailList.stream().map(v->{
                BundlStoreConfirmDTO storeConfirmDTOt = new BundlStoreConfirmDTO();
                copyTaskStoreBeanInfo(v, storeConfirmDTOt, dictMap);
                return storeConfirmDTOt;
            }).collect(Collectors.toList());
            dto.setListGroup(bundlStoreConfirmDTOList);
            listDto.add(dto);
            HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
        } catch (BusinessErrorException e) {
            log.warn("导出组货门店异常.", e);
            throw e;
        } catch (Exception e) {
            log.error("导出组货门店异常.", e);
            throw e;
        }
    }

    @Override
    public PageResult<?> queryTaskResult(TaskDictParam taskDictParam, TokenUserDTO userDTO) {
        long count =  trackRetultNewStoreAllDetailExtendMapper.count(taskDictParam.getTaskId());
        if (count <= 0L){
            return new PageResult<>(count, new ArrayList<>());
        }
        List<TrackRetultNewStoreAllDetail> trackRetultNewStoreAllDetails = trackRetultNewStoreAllDetailExtendMapper.selectByTaskId( taskDictParam.getTaskId(),(taskDictParam.getPage() - 1) * taskDictParam.getPerPage(),taskDictParam.getPerPage());
        if (CollectionUtils.isEmpty(trackRetultNewStoreAllDetails)){
            return new PageResult<>(0L, new ArrayList<>());
        }
        return new PageResult<>(count,trackRetultNewStoreAllDetails);
    }

    /**
     * 通过平台、连锁和用户，获取门店信息
     * @param plateOrgId
     * @param userId
     * @param companyOrgIdList
     * @return
     */
    public List<MdmStoreBaseDTO> selectMdmStoreByPlatformAndUser(Long plateOrgId, Long userId, List<Long> companyOrgIdList){
        log.info("selectMdmStoreByPlatform|plateOrgId:{}。userId：{}.", plateOrgId, userId);
        if(Objects.isNull(plateOrgId)){
            return Lists.newArrayList();
        }

        List<OrgSimpleDTO> businessOrgList = permissionService.listOrgInfoByPlate(userId, plateOrgId, OrgTypeEnum.BUSINESS.getCode());
        if(CollectionUtils.isEmpty(businessOrgList)){
            log.info("selectMdmStoreByPlatform|该平台下没有连锁：{}。", plateOrgId);
            return Lists.newArrayList();
        }
        if(CollectionUtils.isNotEmpty(companyOrgIdList)){
            businessOrgList = businessOrgList.stream().filter(v->companyOrgIdList.contains(v.getId())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(businessOrgList)){
            log.info("selectMdmStoreByPlatform|该平台下没有连锁交集：{}。", plateOrgId);
            return Lists.newArrayList();
        }
        //获取最终筛选之后的门店
        List<Long> permCompanyOrgIdList = businessOrgList.stream().map(OrgSimpleDTO::getId).collect(Collectors.toList());
        List<OrgSimpleDTO> storeOrgList = permissionService.getUserDataScopeChildOrgByOrgId(userId, permCompanyOrgIdList, true);
        storeOrgList = storeOrgList.stream().filter(v-> StringUtils.isNotBlank(v.getSapcode())&&OrgTypeEnum.STORE.getCode().equals(v.getType())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(storeOrgList)){
            log.info("selectMdmStoreByPlatform|该平台下没有门店信息：{}。", plateOrgId);
            return Lists.newArrayList();
        }
        List<String> storeCodeList = storeOrgList.stream().map(OrgSimpleDTO::getSapcode).distinct().collect(Collectors.toList());

        //map集合
        Map<Long, OrgSimpleDTO> businessMap =  businessOrgList.stream().filter(v->Objects.nonNull(v.getOutId())).collect(Collectors.toMap(OrgSimpleDTO::getOutId, Function.identity(),(k1, k2) -> k1));
        Map<String, OrgSimpleDTO> storeMap =  storeOrgList.stream().collect(Collectors.toMap(OrgSimpleDTO::getSapcode, Function.identity(),(k1, k2) -> k1));

        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = storeService.findStoreByStoreNosAndExtend(storeCodeList);
        if(CollectionUtils.isEmpty(mdmStoreBaseDTOList)){
            log.warn("selectMdmStoreByPlatform|MDM门店信息接口返回均为空，不能入库");
            return Lists.newArrayList();
        }
        mdmStoreBaseDTOList = mdmStoreBaseDTOList.stream().filter(v->StringUtils.isNotBlank(v.getExtend())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mdmStoreBaseDTOList)){
            log.warn("selectMdmStoreByPlatform|MDM门店扩展信息接口返回均为空，不能入库");
            return Lists.newArrayList();
        }
        mdmStoreBaseDTOList.forEach(v->{
            OrgSimpleDTO businessInfo = businessMap.get(v.getBusinessId());
            if(Objects.nonNull(businessInfo)){
                v.setCompanyOrgId(businessInfo.getId());
                v.setOrgName(businessInfo.getShortName());
            }
            OrgSimpleDTO storeInfo = storeMap.get(v.getStoreNo());
            if(Objects.nonNull(storeInfo)){
                v.setStoreOrgId(storeInfo.getId());
            }
        });
        return mdmStoreBaseDTOList;
    }


    /**
     * 组装集合参数
     * @param listParam
     */
    private void resetListParam(BundlTaskListParam listParam) {
        if(StringUtils.isNotBlank(listParam.getBundlGoodsBigKindsList())){
            listParam.setBundlGoodsBigKinds(Arrays.asList(listParam.getBundlGoodsBigKindsList().split(",")));
        }
        if(StringUtils.isNotBlank(listParam.getCommpanyOrgIdList())){
            listParam.setCommpanyOrgIds(Arrays.asList(listParam.getCommpanyOrgIdList().split(",")));
        }
        if(StringUtils.isNotBlank(listParam.getBundlStoreList())){
            listParam.setBundlStores(Arrays.asList(listParam.getBundlStoreList().split(",")));
        }
        if(StringUtils.isNotBlank(listParam.getTaskStatusList())){
            listParam.setTaskStatus(Arrays.stream(listParam.getTaskStatusList().split(",")).filter(StringUtils::isNotBlank).map(v->Byte.parseByte(StringUtils.trim(v))).collect(Collectors.toList()));
        }
        if(StringUtils.isNotBlank(listParam.getZsStoreList())){
            listParam.setZsStores(Arrays.asList(listParam.getZsStoreList().split(",")));
        }
        if(StringUtils.isNotBlank(listParam.getDisposeStoreList())){
            listParam.setDisposeStores(Arrays.asList(listParam.getDisposeStoreList().split(",")));
        }
        if(StringUtils.isNotBlank(listParam.getStoreIdList())){
            listParam.setStoreIds(Arrays.stream(listParam.getStoreIdList().split(",")).filter(StringUtils::isNotBlank).map(v->Long.parseLong(StringUtils.trim(v))).collect(Collectors.toList()));
        }
        if(StringUtils.isNotBlank(listParam.getStoreAttrList())){
            listParam.setStoreAttrs(Arrays.asList(listParam.getStoreAttrList().split(",")));
        }
        //如果taskType为空 默认组货的全部类型
        listParam.setTaskTypeList(BundlingTaskInfo.getDefaultTaskTypeListByTaskType(listParam.getTaskType()));
    }

    /**
     * 依据原始字典构建选择器参数
     * @param commonDictionaries
     * @param selectParamMap
     */
    private void buileSelectMapByBaseDict(List<CommonDictionary> commonDictionaries, Map<String, List<String>> selectParamMap){
        commonDictionaries.forEach(dict->{
            if(StringUtils.isBlank(dict.getAlias())){
                return;
            }
            selectParamMap.put(dict.getAlias(), Collections.EMPTY_LIST);
        });
    }

    /**
     * 获取规则明细
     * @param commonDictionaries
     * @param configOrgDetails
     * @param scopeCode
     * @param finalConfigDetailExtendMap
     * @return
     */
    private Map<String, BundlTaskDetailDTO> getTaskRuleDetailDTOMap(List<CommonDictionary> commonDictionaries, List<BundlingTaskDetail> configOrgDetails, String scopeCode, Map<Long, List<BundlingTaskDetailExtend>> finalConfigDetailExtendMap ) {
        Map<String, List<BundlingTaskDetail>> ruleDetailMap=new HashMap<>();
        ArrayList<BundlTaskDetailDTO> ruleDetailDTOSList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(configOrgDetails)){
            ruleDetailMap = configOrgDetails.stream().collect(Collectors.groupingBy(BundlingTaskDetail::getDictCode));
        }

        Map<String, List<BundlingTaskDetail>> finalRuleDetailMap = ruleDetailMap;
        commonDictionaries.forEach(v->{
            BundlTaskDetailDTO ruleDetailDTO = new BundlTaskDetailDTO();
            if (MapUtils.isEmpty(finalRuleDetailMap)){
                BeanUtils.copyProperties(v,ruleDetailDTO);
            }else if (CollectionUtils.isEmpty(finalRuleDetailMap.get(v.getDictCode()))){
                BeanUtils.copyProperties(v,ruleDetailDTO);
            }else {
                List<BundlingTaskDetail> ruleDetailDTOS = finalRuleDetailMap.get(v.getDictCode());
                if (CollectionUtils.isNotEmpty(ruleDetailDTOS) && ruleDetailDTOS.size() == 1) {
                    BeanUtils.copyProperties(ruleDetailDTOS.get(0), ruleDetailDTO);
                    ruleDetailDTO.setTaskDetailId(ruleDetailDTOS.get(0).getId());
                    if (ruleDetailDTO.getPerprotyType().equals(Constants.CHECKBOX)){
                        ruleDetailDTO.setPerprotyValueList(Arrays.asList(ruleDetailDTO.getPerprotyValue()));
                        ruleDetailDTO.setPerprotyValue(null);
                    }
                } else if (CollectionUtils.isNotEmpty(ruleDetailDTOS) && ruleDetailDTOS.size() > 1) {
                    List<String> collect = ruleDetailDTOS.stream().filter(c -> c.getPerprotyType().equals(Constants.CHECKBOX)).map(c -> c.getPerprotyValue()).collect(Collectors.toList());
                    BeanUtils.copyProperties(ruleDetailDTOS.get(0), ruleDetailDTO);
                    ruleDetailDTO.setTaskDetailId(ruleDetailDTOS.get(0).getId());
                    ruleDetailDTO.setPerprotyValue(null);
                    ruleDetailDTO.setPerprotyValueList(collect);
                }
                if (ruleDetailDTO.getPerprotyType().equals(Constants.COLLECTION) && MapUtils.isNotEmpty(finalConfigDetailExtendMap)) {
                    if (CollectionUtils.isNotEmpty(finalConfigDetailExtendMap.get(ruleDetailDTO.getTaskDetailId()))) {
                        ruleDetailDTO.setOrgIdList(finalConfigDetailExtendMap.get(ruleDetailDTO.getTaskDetailId()).stream().filter(c -> c.getExtendType().equals(Constants.STORE)).map(c -> Long.valueOf(c.getKeyword())).collect(Collectors.toList()));
                    }
                }
            }
            if (DicApiEnum.ZH_GOODS.getCode().equals(scopeCode)){
                ruleDetailDTO.setDisAble(DicEnableEnum.DISABLE.isValue());
            }
            ruleDetailDTO.setDictName(v.getDictName());
            ruleDetailDTOSList.add(ruleDetailDTO);
        });
        return ruleDetailDTOSList.stream().collect(Collectors.toMap(BundlTaskDetailDTO::getDictCode, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 返回值
     * @param commonDictionaries
     * @param nullRuleDetailDTO
     * @param scopCode
     * @return
     */
    private Map<String, BundlTaskDetailDTO> getStringRuleDetailDTOMap(List<CommonDictionary> commonDictionaries, ArrayList<BundlTaskDetailDTO> nullRuleDetailDTO,String scopCode) {
        commonDictionaries.forEach(v->{
            BundlTaskDetailDTO ruleDetailDTO = new BundlTaskDetailDTO();
            BeanUtils.copyProperties(v,ruleDetailDTO);
            if(DicApiEnum.ZH_GOODS.getCode().equals(scopCode)){
                ruleDetailDTO.setDisAble(DicEnableEnum.DISABLE.isValue());
            }
            nullRuleDetailDTO.add(ruleDetailDTO);
        });
        return nullRuleDetailDTO.stream().collect(Collectors.toMap(BundlTaskDetailDTO::getDictCode, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 门店字段copy
     * @param storeDetail
     * @param storeConfirmDTOt
     */
    private void copyTaskStoreBeanInfo(BundlingTaskStoreDetail storeDetail, BundlStoreConfirmDTO storeConfirmDTOt, Map<String, String> dictMap) {
        BeanUtils.copyProperties(storeDetail, storeConfirmDTOt);
        storeConfirmDTOt.setBundlAdviceAble(BundlBoolEnum.getMessageByCode(storeDetail.getBundlAdviceAble()));
        storeConfirmDTOt.setBundlConfirmAble(storeDetail.getBundlConfirmAble()==1);
        storeConfirmDTOt.setBundlConfirmAbleDesc(BundlBoolEnum.getMessageByCode(storeDetail.getBundlConfirmAble()));
        MdmStoreBaseExtendDTO mdmStoreBaseExtendDTO = JSON.parseObject(storeDetail.getExtend(), MdmStoreBaseExtendDTO.class);
        if(Objects.nonNull(mdmStoreBaseExtendDTO)) {
            storeConfirmDTOt.setZsShop(mdmStoreBaseExtendDTO.getZsShop());
            storeConfirmDTOt.setPsStore(mdmStoreBaseExtendDTO.getPsStore());
            storeConfirmDTOt.setManageState(mdmStoreBaseExtendDTO.getManageState());
            storeConfirmDTOt.setHandoverDate(mdmStoreBaseExtendDTO.getHandoverDate());
            storeConfirmDTOt.setManSubject(mdmStoreBaseExtendDTO.getManSubject());
            storeConfirmDTOt.setB2cShop(mdmStoreBaseExtendDTO.getB2cShop());
            storeConfirmDTOt.setTradingArea(mdmStoreBaseExtendDTO.getGoodTradingArea());
        }
        //店型
        if(StringUtils.isNotBlank(storeDetail.getStoreTypeCode())){
            storeConfirmDTOt.setBundlStore(dictMap.get(BudnlTaskDetailDicEnum.STORE.getCode() + Constants.KLINE + storeDetail.getStoreTypeCode()));
        }
        if(StringUtils.isNotBlank(storeDetail.getZsStoreTypeCode())){
            storeConfirmDTOt.setZsShop(dictMap.get(BudnlTaskDetailDicEnum.ZS.getCode() + Constants.KLINE + storeDetail.getZsStoreTypeCode()));
        }
    }


    /**
     * 通过字典查询值，多选类型，分号隔开
     * @param dicCode
     * @return
     */
    private List<CommonEnums> dicValueList(String dicCode){
        List<CommonEnums> commonEnumsList= commonEnumsExtendMapper.selectByDicCodeList(Lists.newArrayList(dicCode));
        if(CollectionUtils.isEmpty(commonEnumsList)){
            log.warn("dicValueList|没有查到对应的字典：{}。", dicCode);
            return Lists.newArrayList();
        }
        return commonEnumsList;
    }

    private void copyTaskBeanInfo(BundlingTaskInfo taskInfo, BundlTaskListDTO taskListDTO, Map<String, String> taskDetailMap) {
        BeanUtils.copyProperties(taskInfo, taskListDTO);
        taskListDTO.setTaskId(taskInfo.getId());
        taskListDTO.setGmtCreate(DateUtils.conventDateStrByDate(taskInfo.getGmtCreate()));
        taskListDTO.setGmtCommit(DateUtils.conventDateStrByDate(taskInfo.getGmtCommit()));
        taskListDTO.setGmtIssued(DateUtils.conventDateStrByDate(taskInfo.getGmtIssued()));
        taskListDTO.setGmtCancel(DateUtils.conventDateStrByDate(taskInfo.getGmtCancel()));
        taskListDTO.setGmtCalculated(DateUtils.conventDateStrByDate(taskInfo.getGmtCalculated()));
        taskListDTO.setTaskTypeDesc(BundlTaskTypeEnum.getMessageByCode(taskInfo.getTaskType()));
        if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(taskInfo.getTaskType())){
            //处理下文案
            taskListDTO.setTaskStatusDesc(BundlTaskStatusEnum.UPDATED.getCode()==taskInfo.getTaskStatus()?"已确认":BundlTaskStatusEnum.getMessageByCode(taskInfo.getTaskStatus()));
        }else {
            taskListDTO.setTaskStatusDesc(BundlTaskStatusEnum.getMessageByCode(taskInfo.getTaskStatus()));
        }
        //明细
        if(MapUtils.isNotEmpty(taskDetailMap)){
            taskListDTO.setBundlGoodsBigKinds(taskDetailMap.get(BudnlTaskDetailDicEnum.keyGoods(taskInfo.getTaskCode())));
            taskListDTO.setBundlBusiness(taskDetailMap.get(BudnlTaskDetailDicEnum.keyBusiness(taskInfo.getTaskCode())));
            taskListDTO.setBundlStore(taskDetailMap.get(BudnlTaskDetailDicEnum.keyStore(taskInfo.getTaskCode())));
            taskListDTO.setZsStore(taskDetailMap.get(BudnlTaskDetailDicEnum.keyZs(taskInfo.getTaskCode())));
            taskListDTO.setDisposeStore(taskDetailMap.get(BudnlTaskDetailDicEnum.keyDispose(taskInfo.getTaskCode())));
        }

        //操作
        taskListDTO.setTaskEditAble(BundlTaskStatusEnum.taskEdit(taskInfo.getTaskStatus()));
        taskListDTO.setTaskViewAble(BundlTaskStatusEnum.taskView(taskInfo.getTaskStatus()));
        taskListDTO.setTaskCancelAble(BundlTaskStatusEnum.taskCancel(taskInfo.getTaskStatus()));
        taskListDTO.setResultEditAble(BundlTaskStatusEnum.resultEdit(taskInfo.getTaskStatus()));
        taskListDTO.setResultViewAble(BundlTaskStatusEnum.resultView(taskInfo.getTaskStatus()));
        taskListDTO.setResultMdmAble(BundlTaskStatusEnum.resultMdm(taskInfo.getTaskStatus()));
        if (StringUtils.isNotBlank(taskInfo.getExtend())) {
            taskListDTO.initXDMLParams(JSONObject.parseObject(taskInfo.getExtend()));
        }

    }

    /**
     * 重新整理日期查询条件，加上时间
     * @param listParam
     */
    private void resetTime(BundlTaskListParam listParam) {
        if(StringUtils.isNotBlank(listParam.getGmtCreateStartEnd())){
            listParam.setGmtCreateStart(listParam.getGmtCreateStartEnd().split(",")[0].concat(" 00:00:00"));
            listParam.setGmtCreateEnd(listParam.getGmtCreateStartEnd().split(",")[1].concat(" 23:59:59"));
        }
        if(StringUtils.isNotBlank(listParam.getGmtCommitStartEnd())){
            listParam.setGmtCommitStart(listParam.getGmtCommitStartEnd().split(",")[0].concat(" 00:00:00"));
            listParam.setGmtCommitEnd(listParam.getGmtCommitStartEnd().split(",")[1].concat(" 23:59:59"));
        }
        if(StringUtils.isNotBlank(listParam.getGmtIssuedStartEnd())){
            listParam.setGmtIssuedStart(listParam.getGmtIssuedStartEnd().split(",")[0].concat(" 00:00:00"));
            listParam.setGmtIssuedEnd(listParam.getGmtIssuedStartEnd().split(",")[1].concat(" 23:59:59"));
        }
    }

    /**
     * 搜索taskId结果
     * @param listParam
     * @return
     */
    private boolean searchTaskIdParam(BundlTaskListParam listParam) {
        List<Long> taskIdList =new ArrayList<>();
        if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(listParam.getTaskType())){
            BundlingTaskStoreDetailExample example = new BundlingTaskStoreDetailExample();
            BundlingTaskStoreDetailExample.Criteria criteria = example.createCriteria().andStatusEqualTo(NORMAL_STATUS);
            if(StringUtils.isNotBlank(listParam.getTaskCode())){
                criteria.andTaskCodeLike(Constants.QUERY_LIKE + listParam.getTaskCode().trim() + Constants.QUERY_LIKE);
            }else {
                criteria.andTaskCodeLike(OrderTypeEnum.SCIB_XDML_TASK.getType() + Constants.QUERY_LIKE);
            }
            if(CollectionUtils.isNotEmpty(listParam.getStoreAttrs())){
                criteria.andStoreAttrIn(listParam.getStoreAttrs());
            }
            if(Objects.nonNull(listParam.getCommpanyOrgIds())){
                criteria.andCompanyOrgIdIn(listParam.getCommpanyOrgIds().stream().filter(StringUtils::isNotBlank).map(v->Long.parseLong(StringUtils.trim(v))).collect(Collectors.toList()));
            }
            if(CollectionUtils.isNotEmpty(listParam.getStoreIds())){
                criteria.andStoreIdIn(listParam.getStoreIds());
            }
            if(Objects.nonNull(listParam.getCreateBy())){
                criteria.andCreatedByEqualTo(listParam.getCreateBy());
            }
            List<BundlingTaskStoreDetail> bundlingTaskStoreDetails = bundlingTaskStoreDetailMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(bundlingTaskStoreDetails)){
                log.debug("搜索结果为空");
                return true;
            }
            taskIdList= bundlingTaskStoreDetails.stream().map(BundlingTaskStoreDetail::getTaskId).distinct().collect(Collectors.toList());
        }else {
            List<BundlingTaskDetail> taskDetailList = bundlingTaskDetailExtendMapper.listTaskDetailByStep(listParam);
            if(CollectionUtils.isEmpty(taskDetailList)){
                return true;
            }
            //goodsType
            if(CollectionUtils.isNotEmpty(listParam.getBundlGoodsBigKinds())){
                taskDetailList = taskDetailList.stream().filter(v->BudnlTaskDetailDicEnum.GOODS.getCode().equals(v.getDictCode()) && listParam.getBundlGoodsBigKinds().contains(v.getPerprotyValue())).collect(Collectors.toList());
            }
            //CompanyGroup
            if(CollectionUtils.isNotEmpty(listParam.getCommpanyOrgIds())){
                taskDetailList = taskDetailList.stream().filter(v->BudnlTaskDetailDicEnum.BUSINESS.getCode().equals(v.getDictCode()) && listParam.getCommpanyOrgIds().contains(v.getPerprotyValue())).collect(Collectors.toList());
            }
            //StoreGroup
            if(CollectionUtils.isNotEmpty(listParam.getBundlStores())){
                taskDetailList = taskDetailList.stream().filter(v->BudnlTaskDetailDicEnum.STORE.getCode().equals(v.getDictCode()) && listParam.getBundlStores().contains(v.getPerprotyValue())).collect(Collectors.toList());
            }
            //ZsStoreGroup
            if(CollectionUtils.isNotEmpty(listParam.getZsStores())){
                taskDetailList = taskDetailList.stream().filter(v->BudnlTaskDetailDicEnum.ZS.getCode().equals(v.getDictCode()) && listParam.getZsStores().contains(v.getPerprotyValue())).collect(Collectors.toList());
            }
            //暂时不用
//        if(CollectionUtils.isNotEmpty(listParam.getDisposeStores())){
//            taskDetailList = taskDetailList.stream().filter(v->listParam.getDisposeStores().contains(v.getPerprotyValue())).collect(Collectors.toList());
//        }
            if(CollectionUtils.isEmpty(taskDetailList)){
                log.debug("搜索结果为空");
                return true;
            }
            taskIdList = taskDetailList.stream().map(BundlingTaskDetail::getTaskId).distinct().collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(taskIdList)){
            log.debug("搜索任务ID结果为空");
            return true;
        }
        listParam.setTaskIdList(taskIdList);
        return false;
    }

    /**
     * 明细map
     * @param listParam
     */
    private void resetMapParam(BundlTaskListParam listParam) {
        List<BundlingTaskDetail> taskDetailList = bundlingTaskDetailExtendMapper.listTaskDetailByStep(listParam);
        if(CollectionUtils.isEmpty(taskDetailList)){
            return;
        }
        List<String> enumValueList = taskDetailList.stream().filter(v->StringUtils.isNotBlank(v.getPerprotyValue())).map(v->v.getPerprotyValue()).collect(Collectors.toList());
        List<String> dictCodeList = taskDetailList.stream().filter(v->StringUtils.isNotBlank(v.getDictCode())).map(v->v.getDictCode()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(enumValueList) || CollectionUtils.isEmpty(dictCodeList)){
            return;
        }
        List<CommonEnums> commonEnumsList = commonEnumsExtendMapper.selectByDicCodeAndValueList(dictCodeList, enumValueList);
        if(CollectionUtils.isEmpty(commonEnumsList)){
            return;
        }
        Map<String, CommonEnums> commonEnumsMap = commonEnumsList.stream().collect(Collectors.toMap(v->new StringBuilder().append(v.getPropertyCode()).append("_").append(v.getEnumValue()).toString(), Function.identity(), (k1,k2)->k1));
        Map<String, List<BundlingTaskDetail>> taskDetailEntityMap = taskDetailList.stream().filter(v->!BudnlTaskDetailDicEnum.BUSINESS.getCode().equals(v.getDictCode())).collect(Collectors.groupingBy(v->new StringBuilder().append(v.getTaskCode()).append("_").append(v.getDictCode()).toString()));
        Map<String, String> taskDetailMap = new HashMap<>();
        taskDetailEntityMap.forEach((k,list)->{
            List<String> valueList = list.stream().filter(v->StringUtils.isNotBlank(v.getPerprotyValue())).map(v->commonEnumsMap.get(new StringBuilder().append(v.getDictCode()).append("_").append(v.getPerprotyValue()).toString())!=null?commonEnumsMap.get(new StringBuilder().append(v.getDictCode()).append("_").append(v.getPerprotyValue()).toString()).getEnumName():"").collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(valueList)){
                taskDetailMap.put(k, StringUtils.join(valueList, ";"));
            }
        });
        Map<String, List<BundlingTaskDetail>> taskDetailCompanysMap = taskDetailList.stream().filter(v->BudnlTaskDetailDicEnum.BUSINESS.getCode().equals(v.getDictCode())&&StringUtils.isNotBlank(v.getPerprotyValue())).collect(Collectors.groupingBy(v->new StringBuilder().append(v.getTaskCode()).append("_").append(v.getDictCode()).toString()));
        if(MapUtils.isNotEmpty(taskDetailCompanysMap)){
            taskDetailCompanysMap.forEach((v,list)->{
                List<Long> orgIdList = list.stream().map(c->Long.parseLong(c.getPerprotyValue())).collect(Collectors.toList());
                List<String> companyNameList = Lists.newArrayList();
                orgIdList.stream().forEach(o->{
                    Optional<OrgInfoBaseCache> optionalCompany = CacheVar.getBusinessByOrgId(o);
                    if(optionalCompany.isPresent()){
                        companyNameList.add(optionalCompany.get().getName());
                    }
                });
                if(CollectionUtils.isNotEmpty(companyNameList)){
                    taskDetailMap.put(v, StringUtils.join(companyNameList, ";"));
                }
            });
        }
        listParam.setTaskDetailMap(taskDetailMap);
    }

}
