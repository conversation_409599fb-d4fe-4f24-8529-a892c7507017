package com.cowell.scib.service.impl;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.ImportDataEnum;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.IImportDataService;
import com.cowell.scib.service.NecessaryContentsService;
import com.cowell.scib.service.NecessaryContentsV2Service;
import com.cowell.scib.service.StoreService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.utils.HutoolUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ImportMdmStoreCloseDataService implements IImportDataService {

    private final Logger logger = LoggerFactory.getLogger(ImportMdmStoreCloseDataService.class);

    @Autowired
    private NecessaryContentsService necessaryContentsService;
    @Autowired
    private NecessaryContentsV2Service necessaryContentsV2Service;

    @Autowired
    private StoreService storeService;

    @Override
    public void importData(MultipartFile file, TokenUserDTO userDTO){

        try {
            List<MdmCloseStoreDTO> mdmCloseStoreDTOList = HutoolUtil.excelToList(file.getInputStream(), ExcelFileDomain.getMdmCloseStore(),MdmCloseStoreDTO.class, 1);
            if (CollectionUtils.isEmpty(mdmCloseStoreDTOList)) {
                throw new AmisBusinessException(ErrorCodeEnum.RULE_FILE_EMPTY);
            }
            List<String> storeNos = mdmCloseStoreDTOList.stream().map(MdmCloseStoreDTO::getSapCode).distinct().collect(Collectors.toList());
            List<MdmStoreBaseDTO> mdmStoreBaseDTOList = new ArrayList<>();
            List<List<String>> partition = Lists.partition(storeNos, Constants.FEIGN_ONCE_QUERY_MAX);
            for (List<String> sapCodes : partition) {
                List<MdmStoreBaseDTO> storeByStoreNos = storeService.findStoreByStoreNos(sapCodes);
                mdmStoreBaseDTOList.addAll(storeByStoreNos);
            }

            for (MdmCloseStoreDTO mdmCloseStoreDTO : mdmCloseStoreDTOList) {
                Optional<MdmStoreBaseDTO> mdmStoreBaseDTO = mdmStoreBaseDTOList.stream().filter(v -> v.getStoreNo().equals(mdmCloseStoreDTO.getSapCode())).findAny();
                if (!mdmStoreBaseDTO.isPresent()) {
                    logger.error("查询不到:{}门店信息", mdmCloseStoreDTO.getSapCode());
                    continue;
                }
                MdmStoreBaseDTO mdmStoreBaseDTO1 = mdmStoreBaseDTO.get();
                logger.info("接收mdm数据处理的门店数据为：{}", mdmStoreBaseDTO1.getId());
                TokenUserDTO tokenUserDTO = new TokenUserDTO();
                tokenUserDTO.setUserId(Constants.SYS_USER_ID);
                tokenUserDTO.setUserName(Constants.SYS_USER_NAME);
                tokenUserDTO.setName(Constants.SYS_USER_NAME);
                List<OrgInfoBaseCache> orgInfoBaseCaches = new ArrayList<>();
                OrgInfoBaseCache orgInfoBaseCache = new OrgInfoBaseCache();
                BeanUtils.copyProperties(mdmStoreBaseDTO1, orgInfoBaseCache);
                orgInfoBaseCache.setOutId(mdmStoreBaseDTO1.getStoreId());
                orgInfoBaseCache.setShortName(mdmStoreBaseDTO1.getStoreName());
                orgInfoBaseCache.setName(mdmStoreBaseDTO1.getOrgName());
                orgInfoBaseCache.setId(mdmStoreBaseDTO1.getId());
                orgInfoBaseCache.setSapCode(mdmStoreBaseDTO1.getStoreNo());
                orgInfoBaseCaches.add(orgInfoBaseCache);

                necessaryContentsV2Service.deleteStoreGoods(Arrays.asList(mdmStoreBaseDTO1.getId()), tokenUserDTO, orgInfoBaseCaches);
            }

        } catch (Exception e) {
            logger.error("importMdmStoreClose|error.", e);
            throw new BusinessErrorException(e.getMessage());
        }
    }

    @Override
    public String getCode() {
        return ImportDataEnum.MDM_STORE_CLOSE.getCode();
    }
}
