package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.necessaryComtentsV2.StoreContentsFactory;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonGoodsDTO;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.utils.HutoolUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MdmGoodsStatusUpdateServiceImpl implements MdmGoodsStatusUpdateService {

    private final Logger logger = LoggerFactory.getLogger(MdmGoodsStatusUpdateServiceImpl.class);

    @Autowired
    private NecessaryGroupGoodsMapper necessaryGroupGoodsMapper;

    @Autowired
    private NecessaryPlatformGoodsMapper necessaryPlatformGoodsMapper;

    @Autowired
    private NecessaryCompanyGoodsMapper necessaryCompanyGoodsMapper;

    @Autowired
    private NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;

    @Autowired
    private NecessaryChooseStoreTypeGoodsMapper necessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;

    @Autowired
    private NecessaryContentsService necessaryContentsService;
    @Autowired
    private NecessaryContentsV2Service necessaryContentsV2Service;
    @Autowired
    private StoreContentsFactory factory;

    @Autowired
    private StoreService storeService;

    @Autowired
    private StoreGoodsInfoMapper storeGoodsInfoMapper;

    @Autowired
    private StoreGoodsInfoExtendMapper storeGoodsInfoExtendMapper;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private MdmTaskDetailExtendMapper mdmTaskDetailExtendMapper;
    @Autowired
    private NecessaryContentsMapper necessaryContentsMapper;
    @Autowired
    private NecessaryContentsExtendMapper necessaryContentsExtendMapper;
    @Autowired
    private StoreGoodsProcessMapper storeGoodsProcessMapper;
    @Autowired
    private StoreGoodsContentsMapper storeGoodsContentsMapper;
    @Autowired
    private StoreGoodsContentsExtendMapper storeGoodsContentsExtendMapper;

    @Autowired
    @Qualifier("necessaryTaskExecutor")
    private AsyncTaskExecutor executor;

    @Autowired
    @Qualifier("mdmGoodStatusUpdateCFExecutor")
    private AsyncTaskExecutor cfExecutor;

    @Override
    public void receiveMdmGoodsUpdate(JSONObject param) {
        if (Objects.isNull(param)){
            throw new AmisBadRequestException("MDM下发数据为空");
        }
        try{
            JSONObject bdata = getJsonObject(param);
            List<MdmGoodsUpdateDTO> data = JSONObject.parseArray(bdata.get("data").toString(), MdmGoodsUpdateDTO.class);
            if (CollectionUtils.isEmpty(data)){
                throw new AmisBadRequestException("MDM下发的集团、企业商品信息为空");
            }
            logger.info("接收mdm数据处理的商品为：{}",data.get(0).getCode());
            //如果接收的数据中商品状态（集团）=作废，则直接使用商品编码查询6级必备目录，任何一级目录查到了生效的必备商品，则执行删除操作，
            // 并且取消对应一店一目层级的必备标签（同时下发mdm，mdm更新任务来源=自动化策略变更，创建人=系统管理员）
//            List<MdmGoods> group = data.get(0).getGroup();
//            List<MdmGoods> groupGoodsUpdate = group.stream().filter(v -> v.getGoodstatus().equals(GoodsStatusEnum.DISCARD.getCode())).map(v -> {
//                v.setGoodsno(data.get(0).getCode());
//                return v;
//            }).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(groupGoodsUpdate)){
//                logger.info("开始处理集团废弃数据,下发mdm的goodsNo:{}", data.get(0).getCode());
//                groupGoodsMdmUpdate(groupGoodsUpdate);
//                return;
//            }
            //如果接收的数据中商品状态（集团）<>作废，企业级属性商品状态=作废或者经营属性=淘汰(T),清场(C),作废(Z),拟淘汰,非商品(F)，
            // 首先通过sapcode查出对应的orgid，再转为管理主体的企业orgid，一并查出对应的平台orgid，及企业orgid下的门店列表，及平台orgid下的门店列表
            //不是废弃商品
//            List<MdmGoods> groupNoDiscardGoods = group.stream().filter(v -> !v.getGoodstatus().equals(GoodsStatusEnum.DISCARD.getCode())).collect(Collectors.toList());
            List<MdmGoods> companies = data.get(0).getCompanies();
            List<CommonEnums> goodslineOutlier = commonEnumsExtendMapper.selectByDicCode("goodslineOutlier");
            List<MdmGoods> companyGoodUpdate = companies.stream().filter(v -> v.getGoodstatus().equals(GoodsStatusEnum.DISCARD.getCode()) || goodslineOutlier.stream().map(CommonEnums::getEnumName).collect(Collectors.toList()).contains(v.getGoodsline())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(companyGoodUpdate)) {

                List<String> companyMdmCodes = companyGoodUpdate.stream().map(MdmGoods::getMdm_company_code).collect(Collectors.toList());
                logger.info("开始企业处理废弃或者经营属性不合法的处理的数据为：{}商品为：{}",companyMdmCodes,data.get(0).getCode());
                companyGoodsMdmUpdate(data.get(0).getCode(), companyGoodUpdate.get(0).getGoodsline(), companyMdmCodes);
            }
        }catch (Exception e) {
            logger.error("接收mdm商品数据处理出错", e);
            throw new BusinessErrorException(e.getMessage());
        }
    }

    private JSONObject getJsonObject(JSONObject param) {
        JSONArray table = param.getJSONArray("Table");
        if (CollectionUtils.isEmpty(table)) {
            throw new AmisBadRequestException("table没有数据");
        }
        MbCommonDTO mbCommonDTO = table.toJavaList(MbCommonDTO.class).get(0);
        if (Objects.isNull(mbCommonDTO.getBstatus())) {
            throw new AmisBadRequestException("bstatus状态有误");
        }
        JSONObject bdata = JSONObject.parseObject(mbCommonDTO.getBdata().toString());
        return bdata;
    }
    private void companyGoodsMdmUpdate(String goodsNo, String goodsline, List<String> companyMdmCodes) throws Exception {
        MdmCompanyTransformDTO mdmCompanyTransformDTO = new MdmCompanyTransformDTO();
        mdmCompanyTransformDTO.setComIds(companyMdmCodes);
        mdmCompanyTransformDTO.setTransFormType(2);
        MdmCompanyTransformDTO mdmCompanyTransformDTO1 = storeService.transformBusiness(mdmCompanyTransformDTO);
        if (Objects.isNull(mdmCompanyTransformDTO1)){
            logger.error("通过法人公司sapcode获取businessId 为空");
            return;
        }
        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
        Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(Arrays.asList(goodsNo));
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(Constants.SYS_USER_ID);
        userDTO.setUserName(Constants.SYS_USER_NAME);
        userDTO.setName(Constants.SYS_USER_NAME);
        // 先处理企业及以下的数据
        logger.info("处理企业及以下的数据");
        List<BusinessComIdTransform> businessMappings = mdmCompanyTransformDTO1.getBusinessMappings();
        NecessaryContentsExample example = new NecessaryContentsExample();
        example.createCriteria().andGoodsNoEqualTo(goodsNo).andBusinessIdIn(businessMappings.stream().map(BusinessComIdTransform::getBusinessId).distinct().collect(Collectors.toList())).andStatusEqualTo(Constants.NORMAL_STATUS);
        List<NecessaryContents> necessaryContents = necessaryContentsMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(necessaryContents)) {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(0);
            mdmTaskMapper.insertSelective(task);
            Map<String, String> goodslineMap = new HashMap<>();
            goodslineMap.put(goodsNo, goodsline);
            necessaryContentsV2Service.modify(userDTO, necessaryContents.stream().map(NecessaryContents::getId).collect(Collectors.toList()), Constants.DEL_STATUS, MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getMessage(), task, goodslineMap);
        }
        // 处理平台及总部的数据
        logger.info("处理平台及总部的数据");
        example.clear();
        example.createCriteria().andGoodsNoEqualTo(goodsNo).andStatusEqualTo(Constants.NORMAL_STATUS).andOrgTypeLessThan(OrgTypeEnum.BUSINESS.getCode());
        List<NecessaryContents> groupContents = necessaryContentsMapper.selectByExample(example);
        List<OrgInfoBaseCache> storeOrgs = businessMappings.stream().map(v -> CacheVar.getStoreListByBusinessId(v.getBusinessId())).flatMap(List::stream).collect(Collectors.toList());
        List<MdmStoreExDTO> storeExList = storeOrgs.stream().map(v -> CacheVar.getStoreExtInfoByStoreId(v.getOutId())).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        for (NecessaryContents contents : groupContents) {
            List<MdmStoreExDTO> stores = storeExList.stream().filter(v -> {
                Boolean check = true;
                if (StringUtils.isNotBlank(contents.getProvince()) && !contents.getProvince().equals(v.getProvince())) {
                    check = false;
                }
                if (StringUtils.isNotBlank(contents.getCity()) && !contents.getCity().equals(v.getCity())) {
                    check = false;
                }
                if (StringUtils.isNotBlank(contents.getArea()) && !contents.getArea().equals(v.getArea())) {
                    check = false;
                }
                return check;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(stores)) {
                logger.info("商品淘汰取消必备,目录:{}没有需要取消的门店", contents.getId());
                continue;
            }
            MdmTask task = new MdmTask();
            if (CollectionUtils.isNotEmpty(stores)) {
                task.setTaskSource(MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getCode());
                BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
                task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
                task.setDetailCount(0);
            }
            mdmTaskMapper.insertSelective(task);
            List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
            for (MdmStoreExDTO mdmStoreExDTO : stores) {
                StoreGoodsProcessExample processExample = new StoreGoodsProcessExample();
                processExample.createCriteria().andGoodsNoEqualTo(contents.getGoodsNo()).andStoreIdEqualTo(mdmStoreExDTO.getStoreId());
                storeGoodsProcessMapper.deleteByExample(processExample);
                StoreGoodsContentsExample contentsExample = new StoreGoodsContentsExample();
                contentsExample.createCriteria().andGoodsNoEqualTo(contents.getGoodsNo()).andStoreIdEqualTo(mdmStoreExDTO.getStoreId()).andNecessaryTagGreaterThan(0);
                StoreGoodsContents storeGoodsContents = storeGoodsContentsMapper.selectByExample(contentsExample).stream().findAny().orElse(null);
                if (storeGoodsContents == null) {
                    continue;
                }
                storeGoodsContents.setMinDisplayQuantity(BigDecimal.ZERO);
                storeGoodsContents.setNecessaryTag(0);
                storeGoodsContents.setNecessaryTagName("");
                if (ManageStatusEnum.MANAGE_NECESARY.getCode().equals(storeGoodsContents.getManageStatus())) {
                    storeGoodsContents.setManageStatus(ManageStatusEnum.MANAGE_CHOOSE.getCode());
                    storeGoodsContents.setSuggestManageStatus(SuggestManageStatusEnum.SUGGEST_MANAGE_CHOOSE.getCode());
                }
                ManageStatusEnum manageStatusEnum = ManageStatusEnum.getEnumByMessage(goodsline);
                if (null != manageStatusEnum) {
                    storeGoodsContents.setManageStatus(manageStatusEnum.getCode());
                    storeGoodsContents.setSuggestManageStatus(SuggestManageStatusEnum.NONE.getCode());
                }
                storeGoodsContents.setUpdatedBy(userDTO.getUserId());
                storeGoodsContents.setUpdatedName(userDTO.getName());
                storeGoodsContentsExtendMapper.batchUpdate(Lists.newArrayList(storeGoodsContents), storeGoodsContents.getStoreId());
                MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
                buildMdmTaskDetail(mdmTaskDetail, goodsNo, task, spuMap, storeGoodsContents);
                mdmTaskDetails.add(mdmTaskDetail);
            }
            if (CollectionUtils.isNotEmpty(mdmTaskDetails)) {
                List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(mdmTaskDetails.size());
                logger.info("mdmTaskDetailIds:{}", JSON.toJSONString(mdmTaskDetailIds));
                task.setDetailCount(mdmTaskDetails.size());
                mdmTaskMapper.updateByPrimaryKeySelective(task);
                Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
                    v.forEach(d -> d.setId(mdmTaskDetailIds.remove(0)));
                    mdmTaskDetailExtendMapper.batchInsert(v);
                    try {
                        factory.getAssemble(1).pushMdm(v);
                    } catch (Exception e) {
                        logger.info("推送失败");
                        mdmTaskDetailExtendMapper.batchUpdatePushStatus(v.get(0).getTaskId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
                        task.setTaskStatus(MdmStatusTaskEnum.FAIL.getCode());
                        mdmTaskMapper.updateByPrimaryKeySelective(task);
                    }
                });
            }
        }
    }
//    private void companyGoodsMdmUpdate(String goodsNo, List<String> companyMdmCodes, List<StoreGoodsInfo> storeGoodsInfoGroupAll, List<StoreGoodsInfo> storeGoodsInfoPlatform, int groupUpdate, int platformUpdate) throws Exception {
//        MdmTask mdmTask = new MdmTask();
//        saveMdmTask(mdmTask);
//        MdmCompanyTransformDTO mdmCompanyTransformDTO = new MdmCompanyTransformDTO();
//        mdmCompanyTransformDTO.setComIds(companyMdmCodes);
//        mdmCompanyTransformDTO.setTransFormType(2);
//        MdmCompanyTransformDTO mdmCompanyTransformDTO1 = storeService.transformBusiness(mdmCompanyTransformDTO);
//        if (Objects.isNull(mdmCompanyTransformDTO1)){
//            logger.error("通过法人公司sapcode获取businessId 为空");
//        }
//        List<BusinessComIdTransform> businessMappings = mdmCompanyTransformDTO1.getBusinessMappings();
//        List<Long> businessIds = businessMappings.stream().map(BusinessComIdTransform::getBusinessId).distinct().collect(Collectors.toList());
//
//        List<MdmTaskDetail> mdmTaskDetails = new ArrayList<>();
//        Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
//        Map<String, SpuListVo> spuMap = searchService.getSpuVOMap(Arrays.asList(goodsNo));
//        Iterator<Long> iterator = businessIds.iterator();
//        while (iterator.hasNext()) {
//            Long businessId = iterator.next();
//            List<OrgInfoBaseCache> storeListByBusinessId = CacheVar.getStoreListByBusinessId(businessId);
//            //根据连锁获取平台信息为空
//            Optional<OrgInfoBaseCache> platformBaseCache = CacheVar.getBusinessOrgByBusinessId(businessId);
//            if (!platformBaseCache.isPresent()){
//                logger.error("根据连锁获取平台信息为空,连锁为:{}",businessId);
//                iterator.remove();
//                continue;
//            }
//            //获取平台下的门店集合
//            List<OrgInfoBaseCache> storeListByPlatformOrgId = CacheVar.getStoreListByPlatformOrgId(platformBaseCache.get().getPlatformOrgId());
//            if (CollectionUtils.isEmpty(storeListByPlatformOrgId)){
//                logger.error("获取平台下的门店信息为空,平台为:{}",businessId);
//                iterator.remove();
//                continue;
//            }
//
//            if (CollectionUtils.isEmpty(storeListByBusinessId)){
//                logger.error("连锁下没有门店信息:{}",businessId);
//                iterator.remove();
//                continue;
//            }
//            Map<Integer, List<Long>> businessMap = new HashMap<>();
//            getTablesRuleByStoreId(storeListByBusinessId, businessMap);
//            logger.info("businessMap:{}",JSONObject.toJSONString(businessMap));
//            //企业orgid下的门店列表+商品编码查一店一目表必备层级=1的，存在生效的取消必备，同时做mdm更新任务。
//            updateStoreGoods(goodsNo, mdmTask, businessMap, spuMap, mdmTaskDetails,NecessaryTagEnum.GROUP_NECESSARY.getCode(), groupUpdate);
//            //企业orgid下的门店列表+商品编码查一店一目表必备层级=2的，存在生效的取消必备，同时做mdm更新任务。
//            updateStoreGoods(goodsNo, mdmTask, businessMap, spuMap, mdmTaskDetails,NecessaryTagEnum.PLATFORM_NECESSARY.getCode(), platformUpdate);
//
//
//            Map<Integer, List<Long>> platformMap = new HashMap<>();
//            getTablesRuleByStoreId(storeListByBusinessId, platformMap);
//
//            getstoreGoodsInfoGroupOrPlatform(goodsNo, storeGoodsInfoGroupAll, platformMap,NecessaryTagEnum.GROUP_NECESSARY.getCode());
//
//            getstoreGoodsInfoGroupOrPlatform(goodsNo, storeGoodsInfoPlatform, platformMap,NecessaryTagEnum.GROUP_NECESSARY.getCode());
//
//
//            //企业orgid+商品编码查询，查到删除，再对应处理一店一目和mdm更新任务。
//            Optional<OrgInfoBaseCache> businessOrgByBusinessId = CacheVar.getBusinessOrgByBusinessId(businessId);
//            if (!businessOrgByBusinessId.isPresent()){
//                logger.error("没有获取到连锁信息:{}",businessId);
//                iterator.remove();
//                continue;
//            }
//            OrgInfoBaseCache orgInfoBaseCache = businessOrgByBusinessId.get();
//            NecessaryCompanyGoodsExample necessaryCompanyGoodsExample = new NecessaryCompanyGoodsExample();
//            necessaryCompanyGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andCompanyOrgIdEqualTo(orgInfoBaseCache.getBusinessOrgId());
//            List<NecessaryCompanyGoods> necessaryCompanyGoods = necessaryCompanyGoodsMapper.selectByExample(necessaryCompanyGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryCompanyGoods)){
//                updateStoreGoodsByCompanyLevel(delGoodsStoreMap,necessaryCompanyGoodsExample,necessaryCompanyGoods);
//
//            }
//
//            NecessaryStoreTypeGoodsExample necessaryStoreTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
//            necessaryStoreTypeGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andCompanyOrgIdEqualTo(orgInfoBaseCache.getBusinessOrgId());
//            List<NecessaryStoreTypeGoods> necessaryStoreTypeGoods = necessaryStoreTypeGoodsMapper.selectByExample(necessaryStoreTypeGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryStoreTypeGoods)){
//                updateStoreGoodsByStoreTypeLevel(delGoodsStoreMap, necessaryStoreTypeGoodsExample, necessaryStoreTypeGoods);
//            }
//
//            NecessaryChooseStoreTypeGoodsExample necessaryChooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
//            necessaryChooseStoreTypeGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andCompanyOrgIdEqualTo(orgInfoBaseCache.getBusinessOrgId());
//            List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods = necessaryChooseStoreTypeGoodsMapper.selectByExample(necessaryChooseStoreTypeGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoods)){
//                updateStoreGoodsByChooseStoreTypeLevel(delGoodsStoreMap, necessaryChooseStoreTypeGoodsExample, necessaryChooseStoreTypeGoods);
//            }
//
//            NecessarySingleStoreGoodsExample necessarySingleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
//            necessarySingleStoreGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andStoreIdIn(storeListByBusinessId.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList()));
//            List<NecessarySingleStoreGoods> necessarySingleStoreGoods = necessarySingleStoreGoodsMapper.selectByExample(necessarySingleStoreGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessarySingleStoreGoods)){
//                updateStoreGoodsBySingleStoreLevel(delGoodsStoreMap, necessarySingleStoreGoodsExample, necessarySingleStoreGoods);
//            }
//            if (groupUpdate > 0 && CollectionUtils.isEmpty(storeGoodsInfoGroupAll)) {
//                NecessaryGroupGoodsExample necessaryGroupGoodsExample = new NecessaryGroupGoodsExample();
//                necessaryGroupGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andPlatformOrgIdEqualTo(platformBaseCache.get().getPlatformOrgId());
//                necessaryGroupGoodsMapper.deleteByExample(necessaryGroupGoodsExample);
//            }
//            if (platformUpdate > 0 && CollectionUtils.isEmpty(storeGoodsInfoPlatform)) {
//                NecessaryPlatformGoodsExample necessaryPlatformGoodsExample = new NecessaryPlatformGoodsExample();
//                necessaryPlatformGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andPlatformOrgIdEqualTo(platformBaseCache.get().getPlatformOrgId());;
//                necessaryPlatformGoodsMapper.deleteByExample(necessaryPlatformGoodsExample);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(mdmTaskDetails)){
//            mdmTask.setRemarks(mdmTaskDetails.get(0).getGoodsNo());
//            mdmTask.setDetailCount(mdmTaskDetails.size());
//            mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
//            Lists.partition(mdmTaskDetails, Constants.INSERT_MAX_SIZE).forEach(v -> {
//                mdmTaskDetailExtendMapper.batchInsert(v);
//                try {
//                    necessaryContentsService.pushMdm(v);
//                } catch (Exception e) {
//                    logger.info("推送失败");
//                    mdmTaskDetailExtendMapper.batchUpdatePushStatus(v.get(0).getTaskId(), v.stream().map(MdmTaskDetail::getId).collect(Collectors.toList()), MdmTaskPushStatusEnum.FAIL.getCode());
//                    mdmTask.setTaskStatus(MdmStatusTaskEnum.FAIL.getCode());
//                    mdmTaskMapper.updateByPrimaryKey(mdmTask);
//                }
//            });
//        }else {
//            mdmTaskMapper.deleteByPrimaryKey(mdmTask.getId());
//        }
//
//        CompletableFuture<Void> cf = null;
//        if (MapUtils.isNotEmpty(delGoodsStoreMap)){
//            //临时打排查问题
//            for (Map.Entry<NecessaryCommonGoodsDTO, List<Long>> entry : delGoodsStoreMap.entrySet()) {
//                NecessaryCommonGoodsDTO key = entry.getKey();
//                List<Long> storeIds = delGoodsStoreMap.get(key);
//                logger.info("商品淘汰的门店集合为key:{}",key);
//                List<List<Long>> partition = Lists.partition(storeIds, Constants.QUERY_SEARCH_PAGESIZE);
//                for (List<Long> longs : partition) {
//                    logger.info("商品淘汰的门店集合为key:{}value:{}",key,longs);
//                }
//            }
//            cf = CompletableFuture.runAsync(() -> {
//                try {
//                    delStroeGoods(delGoodsStoreMap, false);
//                } catch (Exception e) {
//                    logger.error("商品淘汰失败", e);
//                }
//            },cfExecutor);
//        }
//        if (null == cf) {
//            cf = CompletableFuture.runAsync(() -> {});
//        }
//        cf.whenCompleteAsync((res, exp) -> {
//            // 再次删除 1-8(除去7)的一店一目数据
//            Map<NecessaryCommonGoodsDTO, List<Long>> againDelGoodsStoreMap = new HashMap<>();
//            logger.info("再次删除1-8(除去7)的一店一目数据");
//            for (Long businessId : businessIds) {
//                List<OrgInfoBaseCache> storeList = CacheVar.getStoreListByBusinessId(businessId);
//                if (CollectionUtils.isEmpty(storeList)) {
//                    continue;
//                }
//                SpuListVo spuListVo = spuMap.get(goodsNo);
//                if (null == spuListVo) {
//                    logger.info("再次删除1-8(除去7)的一店一目数据,商品:{}不存在", goodsNo);
//                    continue;
//                }
//                List<Long> storeIds = storeList.stream().map(OrgInfoBaseCache::getOutId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//                OrgInfoBaseCache store = storeList.get(0);
//                for (NecessaryTagEnum tag : NecessaryTagEnum.values()) {
//                    if (NecessaryTagEnum.SEASON_GOODS.equals(tag) || NecessaryTagEnum.NONE_NECESSARY.equals(tag)) {
//                        continue;
//                    }
//                    NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
//                    goodsDTO.setPlatformOrgId(store.getPlatformOrgId());
//                    goodsDTO.setPlatformName(store.getPlatformShortName());
//                    goodsDTO.setGoodsNo(spuListVo.getGoodsNo());
//                    goodsDTO.setBarCode(spuListVo.getBarCode());
//                    goodsDTO.setGoodsCommonName(spuListVo.getCurName());
//                    goodsDTO.setGoodsName(spuListVo.getName());
//                    goodsDTO.setGoodsUnit(spuListVo.getUnit());
//                    goodsDTO.setDescription(spuListVo.getDescription());
//                    goodsDTO.setSpecifications(spuListVo.getJhiSpecification());
//                    goodsDTO.setDosageForm(spuListVo.getDosageForms());
//                    goodsDTO.setManufacturer(spuListVo.getProducter());
//                    goodsDTO.setApprovalNumber(spuListVo.getApprdocno());
//                    goodsDTO.setNecessaryTag(tag.getCode());
//                    goodsDTO.setCreatedBy(Constants.SYS_USER_ID);
//                    goodsDTO.setCreatedName(Constants.SYS_USER_NAME);
//                    goodsDTO.setUpdatedBy(Constants.SYS_USER_ID);
//                    goodsDTO.setUpdatedName(Constants.SYS_USER_NAME);
//                    againDelGoodsStoreMap.put(goodsDTO, storeIds);
//                }
//            }
//            try {
//                logger.info("再次删除1-8(除去7)的一店一目数据againDelGoodsStoreMap:{}", JSON.toJSONString(againDelGoodsStoreMap));
//                delStroeGoods(againDelGoodsStoreMap, true);
//            } catch (Exception e) {
//                logger.error("再次删除1-8(除去7)的一店一目数据失败", e);
//            }
//        }, cfExecutor);
//    }
    public void delStroeGoods(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap, boolean asynAble) throws Exception {
        try {
            MdmTask task = new MdmTask();
            task.setTaskSource(MdmTaskSourceEnum.NECESSARY_CONTENT_ADJUST.getCode());
            TokenUserDTO userDTO = new TokenUserDTO();
            userDTO.setUserId(Constants.SYS_USER_ID);
            userDTO.setName(Constants.SYS_USER_NAME);
            task.setTaskSource(MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getCode());
            BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
            List<String> goodsList = delGoodsStoreMap.keySet().stream().map(NecessaryCommonGoodsDTO::getGoodsNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(goodsList)) {
                task.setRemarks(JSONObject.toJSONString(goodsList));
            }
            task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            task.setDetailCount(0);
            mdmTaskMapper.insertSelective(task);
            if (asynAble) {
                executor.execute(() -> {
                    updateStoreGoodsInfoTag(delGoodsStoreMap, task);
                });
            } else {
                updateStoreGoodsInfoTag(delGoodsStoreMap, task);
            }
        } catch (Exception e) {
            logger.error("删除一店一目逻辑失败:", e);
            throw e;
        }
    }

    private void updateStoreGoodsInfoTag(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap, MdmTask task) {
        List<MdmTaskDetail> taskDetails = new ArrayList<>();
        for (Map.Entry<NecessaryCommonGoodsDTO, List<Long>> entry : delGoodsStoreMap.entrySet()) {
            NecessaryCommonGoodsDTO dto = entry.getKey();
            Map<Integer, List<Long>> suffixMap = new HashMap<>();
            entry.getValue().forEach(storeId -> {
                int suffix = NecessaryContentsServiceImpl.getSuffixByStoreId(storeId);
                List<Long> storeIds = Optional.ofNullable(suffixMap.get(suffix)).orElse(new ArrayList<>());
                storeIds.add(storeId);
                suffixMap.put(suffix, storeIds);
            });
            List<StoreGoodsInfo> storeGoodsInfos = new ArrayList<>();
            suffixMap.forEach((k, v) -> {
                StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                example.createCriteria().andStoreIdIn(v).andGoodsNoEqualTo(dto.getGoodsNo()).andNecessaryTagEqualTo(dto.getNecessaryTag());
                logger.info("门店:{}商品:{}必备层级:{}", JSON.toJSONString(v), dto.getGoodsNo(), dto.getNecessaryTag());
                List<StoreGoodsInfo> storeGoodsList = storeGoodsInfoMapper.selectByExample(example);
                logger.info("查询结果门店:{}商品:{}必备层级:{}", JSON.toJSONString(storeGoodsList.stream().map(StoreGoodsInfo::getStoreId).collect(Collectors.toList())), dto.getGoodsNo(), dto.getNecessaryTag());
                storeGoodsInfos.addAll(storeGoodsList);
            });
            if (CollectionUtils.isNotEmpty(storeGoodsInfos)) {
                List<Long> ids = necessaryContentsService.getStoreGoodsIds(storeGoodsInfos.size() + 1);
                List<Long> taskIds = necessaryContentsService.getMdmTaskDetailIds(storeGoodsInfos.size() + 1);
                // 先删后增
                for (int i = 0; i < storeGoodsInfos.size(); i++) {
                    StoreGoodsInfo storeGoodsInfo = storeGoodsInfos.get(i);
                    Optional<OrgInfoBaseCache> optional = CacheVar.getStoreByStoreId(storeGoodsInfo.getStoreId());
                    TokenUserDTO tokenUserDTO = new TokenUserDTO();
                    tokenUserDTO.setUserId(Constants.SYS_USER_ID);
                    tokenUserDTO.setName(Constants.SYS_USER_NAME);
                    BeanUtils.copyProperties(new CommonUserDTO(tokenUserDTO), storeGoodsInfo);
                    storeGoodsInfo.setId(ids.get(i));
                    storeGoodsInfo.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
                    storeGoodsInfo.setMinDisplayQuantity(BigDecimal.ZERO);
                    MdmTaskDetail taskDetail = new MdmTaskDetail();
                    BeanUtils.copyProperties(dto, taskDetail);
                    taskDetail.setId(taskIds.get(i));
                    taskDetail.setNecessaryTag(0);
                    taskDetail.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
                    taskDetail.setStoreName(optional.isPresent() ? optional.get().getShortName() : "");
                    taskDetail.setStoreCode(storeGoodsInfo.getStoreCode());
                    taskDetail.setStoreId(storeGoodsInfo.getStoreId());
                    taskDetail.setStoreName(CacheVar.getStoreByStoreId(storeGoodsInfo.getStoreId()).orElse(new OrgInfoBaseCache()).getShortName());
                    taskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
                    taskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
                    taskDetails.add(taskDetail);
                }
                logger.info("商品:{}门店size:{}taskSize:{}",dto.getGoodsNo(), storeGoodsInfos.size(), taskDetails.size());
                Map<Integer, List<StoreGoodsInfo>> map = new HashMap<>();

                storeGoodsInfos.forEach(v -> {
                    int suffix = NecessaryContentsServiceImpl.getSuffixByStoreId(v.getStoreId());
                    List<StoreGoodsInfo> list = Optional.ofNullable(map.get(suffix)).orElse(new ArrayList<>());
                    list.add(v);
                    map.put(suffix, list);
                });
                map.forEach((k, v) -> {
                    StoreGoodsInfoExample example = new StoreGoodsInfoExample();
                    example.createCriteria().andStoreIdIn(v.stream().map(StoreGoodsInfo::getStoreId).collect(Collectors.toList())).andGoodsNoEqualTo(dto.getGoodsNo()).andNecessaryTagEqualTo(dto.getNecessaryTag());
                    storeGoodsInfoMapper.deleteByExample(example);
                    storeGoodsInfoExtendMapper.batchInsert(v);
                });
            }
        }
        if (CollectionUtils.isNotEmpty(taskDetails)) {
            task.setDetailCount(taskDetails.size());
            task.setTaskStatus(MdmStatusTaskEnum.UPDATING.getCode());
            mdmTaskMapper.updateByPrimaryKeySelective(task);
            taskDetails.forEach(v -> v.setTaskId(task.getId()));
            List<List<MdmTaskDetail>> partition = Lists.partition(taskDetails, Constants.INSERT_MAX_SIZE);
            for (List<MdmTaskDetail> v : partition) {
                mdmTaskDetailExtendMapper.batchInsert(v);
                try {
                    necessaryContentsService.pushMdm(v);
                } catch (Exception e) {
                    logger.warn("推送mdm失败", e);
                    v.forEach(taskDetail -> {
                        taskDetail.setPushStatus(MdmTaskPushStatusEnum.FAIL.getCode());
                        taskDetail.setExtend("推送mdm失败");
                    });
                    mdmTaskDetailExtendMapper.deleteByTaskId(task.getId());
                    mdmTaskDetailExtendMapper.batchInsert(v);
                }
            }
        } else {
            mdmTaskMapper.deleteByPrimaryKey(task.getId());
        }
    }


    @Override
    public void importDiscardGoodsUpdateMdmNecessaryTag(MultipartFile file, TokenUserDTO userDTO) throws Exception {
        try {
            if (file == null || file.isEmpty()) {
                throw new BusinessErrorException("导入文件为空");
            }
            if (file.getSize() > 10 * 1024 * 1024) {
                throw new BusinessErrorException("导入文件大于" + (file.getSize() / (1024 * 1024)) + "M");
            }
            String originalFilename = file.getOriginalFilename();
            String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
            if (!".xls".equals(fileType) && !".xlsx".equals(fileType)) {
                throw new BusinessErrorException("导入的文件类型有误");
            }
            List<DiscardGoodsDTO> discardGoodsDTOList = HutoolUtil.excelToList(file.getInputStream(), ExcelFileDomain.getDiscardGoods(), DiscardGoodsDTO.class, 1);
            if (CollectionUtils.isEmpty(discardGoodsDTOList)) {
                throw new AmisBusinessException(ErrorCodeEnum.RULE_FILE_EMPTY);
            }
            List<MdmGoods> groupGoods = discardGoodsDTOList.stream().filter(v -> StringUtils.isEmpty(v.getComId())).map(v-> {
                MdmGoods mdmGoods = new MdmGoods();
                mdmGoods.setGoodsno(v.getGoodsNo());
                mdmGoods.setGoodsline(v.getGoodsline());
                return mdmGoods;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(groupGoods)){
                groupGoodsMdmUpdate(groupGoods);
            }
            List<DiscardGoodsDTO> companyGoods = discardGoodsDTOList.stream().filter(v -> StringUtils.isNotEmpty(v.getComId()) && StringUtils.isNotEmpty(v.getGoodsNo())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(companyGoods)){
                for (DiscardGoodsDTO companyGood : companyGoods) {
                    companyGoodsMdmUpdate(companyGood.getGoodsNo(), companyGood.getGoodsline(), Arrays.asList(companyGood.getComId()));
                }
            }
        } catch (Exception e) {
            logger.error("importDiscardGoodsUpdateMdmNecessaryTag|error.", e);
            throw e;
        }

    }

    /**
     * 接收mdm下发的门店闭店信息，修改mdm系统的最小陈列量
     * @param param
     */
    @Override
    public void receiveMdmStoreUpdate(JSONObject param) {
        if (Objects.isNull(param)){
            throw new AmisBadRequestException("MDM下发数据为空");
        }
        JSONObject jsonObject = getJsonObject(param);
        List<MdmStore> data = JSONObject.parseArray(jsonObject.get("data").toString(), MdmStore.class);
        if (CollectionUtils.isEmpty(data)){
            throw new AmisBadRequestException("MDM下发的集团、企业门店信息为空");
        }
        logger.info("接收mdm数据处理的门店为：{}",data.get(0).getStoreno());
        List<MdmStore> mdmStoreUpdate = data.stream().filter(v -> (MdmStoreStatusEnum.PERMANENT_CLOSURE.getCode().equals(v.getStorestatus()) || MdmStoreStatusEnum.CLOSE_HANDOVER.getCode().equals(v.getStorestatus())) || MdmStoreStatusEnum.CLOSE_STORE.getCode().equals(v.getManagstate())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mdmStoreUpdate)){
            logger.info("接收mdm门店数据不更新一点一目最小陈列量:{}",mdmStoreUpdate.stream().map(MdmStore::getStoreno).collect(Collectors.toList()));
            return;
        }

        List<MdmStoreBaseDTO> storeByStoreNos = storeService.findStoreByStoreNos(mdmStoreUpdate.stream().map(MdmStore::getStoreno).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(storeByStoreNos)) {
            logger.error("查询不到:{}门店信息", mdmStoreUpdate.stream().map(MdmStore::getStoreno).collect(Collectors.toList()));
            return;
        }
        List<Long> orgIdList = storeByStoreNos.stream().map(MdmStoreBaseDTO::getId).collect(Collectors.toList());
        logger.info("接收mdm数据处理的门店数据为：{}",orgIdList);
        TokenUserDTO tokenUserDTO = new TokenUserDTO();
        tokenUserDTO.setUserId(Constants.SYS_USER_ID);
        tokenUserDTO.setUserName(Constants.SYS_USER_NAME);
        tokenUserDTO.setName(Constants.SYS_USER_NAME);
        List<OrgInfoBaseCache> orgInfoBaseCaches = new ArrayList<>();
        for (MdmStoreBaseDTO storeByStoreNo : storeByStoreNos) {
            OrgInfoBaseCache orgInfoBaseCache = new OrgInfoBaseCache();
            BeanUtils.copyProperties(storeByStoreNo,orgInfoBaseCache);
            orgInfoBaseCache.setOutId(storeByStoreNo.getStoreId());
            orgInfoBaseCache.setShortName(storeByStoreNo.getStoreName());
            orgInfoBaseCache.setName(storeByStoreNo.getOrgName());
            orgInfoBaseCache.setId(storeByStoreNo.getId());
            orgInfoBaseCache.setSapCode(storeByStoreNo.getStoreNo());
            orgInfoBaseCaches.add(orgInfoBaseCache);
        }
        necessaryContentsV2Service.deleteStoreGoods(orgIdList, tokenUserDTO, orgInfoBaseCaches);
    }

    private void groupGoodsMdmUpdate(List<MdmGoods> mdmGoodsList) throws Exception {
        NecessaryContentsExample example = new NecessaryContentsExample();
        example.createCriteria().andGoodsNoIn(mdmGoodsList.stream().map(MdmGoods::getGoodsno).distinct().collect(Collectors.toList())).andStatusEqualTo(Constants.NORMAL_STATUS);
        List<NecessaryContents> necessaryContents = necessaryContentsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(necessaryContents)) {
            logger.info("没有商品淘汰取消必备");
            return;
        }
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(Constants.SYS_USER_ID);
        userDTO.setUserName(Constants.SYS_USER_NAME);
        userDTO.setName(Constants.SYS_USER_NAME);
        MdmTask task = new MdmTask();
        task.setTaskSource(MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getCode());
        BeanUtils.copyProperties(new CommonUserDTO(userDTO), task);
        task.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
        task.setDetailCount(0);
        mdmTaskMapper.insertSelective(task);
        Map<String, String> goodsLineMap = mdmGoodsList.stream().collect(Collectors.toMap(MdmGoods::getGoodsno, MdmGoods::getGoodsline, (k1,k2) -> k1));
        necessaryContentsV2Service.modify(userDTO, necessaryContents.stream().map(NecessaryContents::getId).collect(Collectors.toList()),Constants.DEL_STATUS, MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getMessage(), task, goodsLineMap);
    }
//    private void groupGoodsMdmUpdate(List<String> data) throws Exception {
//        for (String goodsNo : data) {
//            NecessaryGroupGoodsExample necessaryGroupGoodsExample = new NecessaryGroupGoodsExample();
//            necessaryGroupGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo).andNecessaryTagEqualTo(NecessaryTagEnum.GROUP_NECESSARY.getCode());
//            List<NecessaryGroupGoods> necessaryGroupGoods = necessaryGroupGoodsMapper.selectByExample(necessaryGroupGoodsExample);
//            Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(necessaryGroupGoods)){
//                List<Long> platformOrgIdList = necessaryGroupGoods.stream().map(NecessaryGroupGoods::getPlatformOrgId).collect(Collectors.toList());
//                List<OrgInfoBaseCache> orgInfoBaseCaches = getMdmStoreExDTOS(platformOrgIdList,null);
//                necessaryGroupGoods.stream().map(necessaryGoods -> {
//                    NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
//                    BeanUtils.copyProperties(necessaryGoods, goodsDTO);
//                    goodsDTO.setNecessaryTag(NecessaryTagEnum.GROUP_NECESSARY.getCode());
//                    return goodsDTO;
//                }).forEach(v -> delGoodsStoreMap.put(v, orgInfoBaseCaches.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList())));
//                NecessaryGroupGoods update = new NecessaryGroupGoods();
//                update.setNecessaryTag(NecessaryTagEnum.NONE_NECESSARY.getCode());
//                necessaryGroupGoodsMapper.updateByExampleSelective(update, necessaryGroupGoodsExample);
//            }
//            NecessaryPlatformGoodsExample necessaryPlatformGoodsExample = new NecessaryPlatformGoodsExample();
//            necessaryPlatformGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo);
//            List<NecessaryPlatformGoods> necessaryPlatformGoods = necessaryPlatformGoodsMapper.selectByExample(necessaryPlatformGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryPlatformGoods)){
//                List<Long> platformOrgIdList = necessaryPlatformGoods.stream().map(NecessaryPlatformGoods::getPlatformOrgId).collect(Collectors.toList());
//                List<OrgInfoBaseCache> mdmStoreExDTOS = getMdmStoreExDTOS(platformOrgIdList,null);
//                necessaryPlatformGoods.stream().map(necessaryGoods -> {
//                    NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
//                    BeanUtils.copyProperties(necessaryGoods, goodsDTO);
//                    goodsDTO.setNecessaryTag(NecessaryTagEnum.PLATFORM_NECESSARY.getCode());
//                    return goodsDTO;
//                }).forEach(v -> delGoodsStoreMap.put(v, mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList())));
//                necessaryPlatformGoodsMapper.deleteByExample(necessaryPlatformGoodsExample);
//            }
//
//            NecessaryCompanyGoodsExample necessaryCompanyGoodsExample = new NecessaryCompanyGoodsExample();
//            necessaryCompanyGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo);
//            List<NecessaryCompanyGoods> necessaryCompanyGoods = necessaryCompanyGoodsMapper.selectByExample(necessaryCompanyGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryCompanyGoods)){
//                updateStoreGoodsByCompanyLevel(delGoodsStoreMap, necessaryCompanyGoodsExample, necessaryCompanyGoods);
//            }
//
//
//            NecessaryStoreTypeGoodsExample storeTypeGoodsExample = new NecessaryStoreTypeGoodsExample();
//            storeTypeGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo);
//            List<NecessaryStoreTypeGoods> necessaryStoreTypeGoods = necessaryStoreTypeGoodsMapper.selectByExample(storeTypeGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryStoreTypeGoods)){
//                updateStoreGoodsByStoreTypeLevel(delGoodsStoreMap, storeTypeGoodsExample, necessaryStoreTypeGoods);
//            }
//
//            NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new NecessaryChooseStoreTypeGoodsExample();
//            chooseStoreTypeGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo);
//            List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods = necessaryChooseStoreTypeGoodsMapper.selectByExample(chooseStoreTypeGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoods)){
//                updateStoreGoodsByChooseStoreTypeLevel(delGoodsStoreMap, chooseStoreTypeGoodsExample, necessaryChooseStoreTypeGoods);
//            }
//            NecessarySingleStoreGoodsExample singleStoreGoodsExample = new NecessarySingleStoreGoodsExample();
//            singleStoreGoodsExample.createCriteria().andGoodsNoEqualTo(goodsNo);
//            List<NecessarySingleStoreGoods> necessarySingleStoreGoods = necessarySingleStoreGoodsMapper.selectByExample(singleStoreGoodsExample);
//            if (CollectionUtils.isNotEmpty(necessarySingleStoreGoods)){
//                updateStoreGoodsBySingleStoreLevel(delGoodsStoreMap, singleStoreGoodsExample, necessarySingleStoreGoods);
//            }
//            if (MapUtils.isNotEmpty(delGoodsStoreMap)){
//                delStroeGoods(delGoodsStoreMap, true);
//            }
//        }
//    }

    private void updateStoreGoodsBySingleStoreLevel(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap, NecessarySingleStoreGoodsExample singleStoreGoodsExample, List<NecessarySingleStoreGoods> necessarySingleStoreGoods) {
        List<Long> storeIds = necessarySingleStoreGoods.stream().map(NecessarySingleStoreGoods::getStoreId).collect(Collectors.toList());
        logger.info("单店必备门店集合：{}",storeIds);
        necessarySingleStoreGoods.stream().map(necessaryGoods -> {
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            BeanUtils.copyProperties(necessaryGoods, goodsDTO);
            goodsDTO.setNecessaryTag(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
            return goodsDTO;
        }).forEach(v -> delGoodsStoreMap.put(v, storeIds));
        necessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);
    }

    private void updateStoreGoodsByChooseStoreTypeLevel(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap, NecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample, List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods) {
        List<Long> companyOrgIds = necessaryChooseStoreTypeGoods.stream().map(NecessaryChooseStoreTypeGoods::getCompanyOrgId).collect(Collectors.toList());
        logger.info("mdm更新商品最小陈列店型选配数据企业：{}",companyOrgIds);
        List<OrgInfoBaseCache> mdmStoreExDTOS = getMdmStoreExDTOS(null,companyOrgIds);
        logger.info("mdm更新商品最小陈列店型选配数据门店：{}",mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList()));
        necessaryChooseStoreTypeGoods.stream().map(necessaryGoods -> {
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            BeanUtils.copyProperties(necessaryGoods, goodsDTO);
            goodsDTO.setNecessaryTag(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode());
            return goodsDTO;
        }).forEach(v -> delGoodsStoreMap.put(v, mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList())));
        necessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);
    }

    private void updateStoreGoodsByStoreTypeLevel(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap, NecessaryStoreTypeGoodsExample storeTypeGoodsExample, List<NecessaryStoreTypeGoods> necessaryStoreTypeGoods) {
        List<Long> companyOrgIds = necessaryStoreTypeGoods.stream().map(NecessaryStoreTypeGoods::getCompanyOrgId).collect(Collectors.toList());
        logger.info("mdm更新商品最小陈列店型必备数据企业：{}",companyOrgIds);
        List<OrgInfoBaseCache> mdmStoreExDTOS = getMdmStoreExDTOS(null,companyOrgIds);
        logger.info("mdm更新商品最小陈列店型必备数据门店：{}",mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList()));
        necessaryStoreTypeGoods.stream().map(necessaryGoods -> {
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            BeanUtils.copyProperties(necessaryGoods, goodsDTO);
            goodsDTO.setNecessaryTag(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode());
            return goodsDTO;
        }).forEach(v -> delGoodsStoreMap.put(v, mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList())));
        necessaryStoreTypeGoodsMapper.deleteByExample(storeTypeGoodsExample);
    }

    private void updateStoreGoodsByCompanyLevel(Map<NecessaryCommonGoodsDTO, List<Long>> delGoodsStoreMap, NecessaryCompanyGoodsExample necessaryCompanyGoodsExample, List<NecessaryCompanyGoods> necessaryCompanyGoods) {
        List<Long> companyOrgIds = necessaryCompanyGoods.stream().map(NecessaryCompanyGoods::getCompanyOrgId).collect(Collectors.toList());
        logger.info("mdm更新商品最小陈列企业必备数据企业：{}",companyOrgIds);
        List<OrgInfoBaseCache> mdmStoreExDTOS = getMdmStoreExDTOS(null,companyOrgIds);
        logger.info("mdm更新商品最小陈列企业必备数据企业：{}", mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList()));
        necessaryCompanyGoods.stream().map(necessaryGoods -> {
            NecessaryCommonGoodsDTO goodsDTO = new NecessaryCommonGoodsDTO();
            BeanUtils.copyProperties(necessaryGoods, goodsDTO);
            goodsDTO.setNecessaryTag(NecessaryTagEnum.COMPANY_NECESSARY.getCode());
            return goodsDTO;
        }).forEach(v -> delGoodsStoreMap.put(v, mdmStoreExDTOS.stream().map(OrgInfoBaseCache::getOutId).collect(Collectors.toList())));
        necessaryCompanyGoodsMapper.deleteByExample(necessaryCompanyGoodsExample);
    }

    private List<OrgInfoBaseCache> getMdmStoreExDTOS(List<Long> platformOrgIdList,List<Long> companyOrgIdList) {
        List<OrgInfoBaseCache> mdmStoreExDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(platformOrgIdList)){
            platformOrgIdList.stream().forEach(v -> {
                List<OrgInfoBaseCache> collect = CacheVar.getStoreListByPlatformOrgId(v).stream().filter(x -> StringUtils.isNotBlank(x.getSapCode())).collect(Collectors.toList());
                mdmStoreExDTOS.addAll(collect);
            });
        }else {
            companyOrgIdList.stream().forEach(v->{
                List<OrgInfoBaseCache> collect = CacheVar.getStoreListByBusinessOrgId(v).stream().filter(x -> StringUtils.isNotBlank(x.getSapCode())).collect(Collectors.toList());
                mdmStoreExDTOS.addAll(collect);
            });
        }
        return mdmStoreExDTOS;
    }

    private void getstoreGoodsInfoGroupOrPlatform(String goodsNo, List<StoreGoodsInfo> storeGoodsInfoAll, Map<Integer, List<Long>> platformMap,byte necessaryTagCode) {
        platformMap.forEach((k, v) -> {
            StoreGoodsInfoExample example = new StoreGoodsInfoExample();
            example.createCriteria().andStoreIdIn(v).andGoodsNoEqualTo(goodsNo).andNecessaryTagEqualTo(necessaryTagCode);
            List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoMapper.selectByExample(example);
            storeGoodsInfoAll.addAll(storeGoodsInfos);

        });
    }

//    private void updateStoreGoods(String goodsNo, MdmTask mdmTask, Map<Integer, List<Long>> businessMap, Map<String, SpuListVo> spuMap, List<MdmTaskDetail> mdmTaskDetails,byte necessaryTagCode,int groupOrPlatformUpdate) {
//        List<List<Long>> collect = businessMap.values().stream().collect(Collectors.toList());
//        for (List<Long> storeIds : collect) {
//            StoreGoodsInfoExample example = new StoreGoodsInfoExample();
//            example.createCriteria().andStoreIdIn(storeIds).andGoodsNoEqualTo(goodsNo).andNecessaryTagEqualTo(necessaryTagCode);
//            List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoMapper.selectByExample(example);
//            if (CollectionUtils.isNotEmpty(storeGoodsInfos)){
//                List<Long> mdmTaskDetailIds = necessaryContentsService.getMdmTaskDetailIds(storeGoodsInfos.size() + 1);
//                for (int i = 0; i < storeGoodsInfos.size(); i++) {
//                    StoreGoodsInfo storeGoodsInfo = storeGoodsInfos.get(i);
//                    MdmTaskDetail mdmTaskDetail = new MdmTaskDetail();
//                    buildMdmTaskDetail(mdmTaskDetail, goodsNo, mdmTask, spuMap, mdmTaskDetailIds, i, storeGoodsInfo);
//                    mdmTaskDetails.add(mdmTaskDetail);
//                }
//                //将查询出的一店一目数据修改为非必备 同时更新mdm任务
//                storeGoodsInfoExtendMapper.batchUpdateNecessaryTagByGoodsNo(storeIds,goodsNo);
//                groupOrPlatformUpdate+=storeGoodsInfos.size();
//            }
//        }
//    }

    private void getTablesRuleByStoreId(List<OrgInfoBaseCache> storeListByBusinessId, Map<Integer, List<Long>> map) {
        storeListByBusinessId.forEach(v -> {
            int suffix = NecessaryContentsServiceImpl.getSuffixByStoreId(v.getOutId());
            List<Long> list = Optional.ofNullable(map.get(suffix)).orElse(new ArrayList<>());
            list.add(v.getOutId());
            map.put(suffix, list);
        });
    }

    private MdmTaskDetail buildMdmTaskDetail(MdmTaskDetail mdmTaskDetail,String goodsNo, MdmTask mdmTask, Map<String, SpuListVo> spuMap, StoreGoodsContents storeGoodsInfo) {
        BeanUtils.copyProperties(storeGoodsInfo, mdmTaskDetail, "id");
        mdmTaskDetail.setTaskId(mdmTask.getId());
        mdmTaskDetail.setGoodsNo(goodsNo);
        if (MapUtils.isNotEmpty(spuMap) && Objects.nonNull(spuMap.get(goodsNo))){
            SpuListVo spuListVo = spuMap.get(goodsNo);
            mdmTaskDetail.setGoodsUnit(spuListVo.getGoodsunit());
            mdmTaskDetail.setApprovalNumber(spuListVo.getApprdocno());
            mdmTaskDetail.setDescription(spuListVo.getDescription());
            mdmTaskDetail.setDosageForm(spuListVo.getDosageformsid());
            mdmTaskDetail.setBarCode(spuListVo.getBarCode());
            mdmTaskDetail.setGoodsCommonName(spuListVo.getCurName());
            mdmTaskDetail.setGoodsName(spuListVo.getName());
            mdmTaskDetail.setSpecifications(spuListVo.getJhiSpecification());
            mdmTaskDetail.setManufacturer(spuListVo.getFactoryid());
        }
        mdmTaskDetail.setUpdatedBy(Constants.SYS_USER_ID);
        mdmTaskDetail.setUpdatedName(Constants.SYS_USER_NAME);
        mdmTaskDetail.setStoreOrgId(storeGoodsInfo.getStoreOrgId());
        mdmTaskDetail.setStoreId(storeGoodsInfo.getStoreId());
        mdmTaskDetail.setStoreCode(storeGoodsInfo.getStoreCode());
        mdmTaskDetail.setStoreName("");
        mdmTaskDetail.setMinDisplayQuantity(BigDecimal.ZERO);
        mdmTaskDetail.setPushStatus(MdmTaskPushStatusEnum.PUSHED.getCode());
        mdmTaskDetail.setStatus(Constants.NORMAL_STATUS);
        mdmTaskDetail.setGmtUpdate(new Date());
        return mdmTaskDetail;
    }

    private void saveMdmTask(MdmTask mdmTask) {
        mdmTask.setTaskSource(MdmTaskSourceEnum.AUTO_POLICY_CHANGE.getCode());
        mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
        mdmTask.setDetailCount(0);
        mdmTask.setStatus(Constants.NORMAL_STATUS);
        mdmTask.setUpdatedName(Constants.SYS_USER_NAME);
        mdmTask.setUpdatedBy(Constants.SYS_USER_ID);
        mdmTask.setUpdatedBy(Constants.ZERO);
        mdmTask.setGmtUpdate(new Date());
        mdmTask.setCreatedBy(Constants.ZERO);
        mdmTask.setCreatedName(Constants.SYS_USER_NAME);
        mdmTask.setCreatedBy(Constants.SYS_USER_ID);
        mdmTask.setGmtCreate(new Date());
        mdmTask.setGmtUpdate(new Date());
        mdmTaskMapper.insertSelective(mdmTask);
    }
}
