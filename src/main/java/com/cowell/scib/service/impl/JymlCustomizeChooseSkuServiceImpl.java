package com.cowell.scib.service.impl;

import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.JymlCustomizeChooseSku;
import com.cowell.scib.entityDgms.JymlCustomizeChooseSkuExample;
import com.cowell.scib.enums.AsyncExportActionEnum;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.JymlCustomizeChooseSkuMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.AsyncExportFileService;
import com.cowell.scib.service.HandlerDataExportService;
import com.cowell.scib.service.JymlCustomizeChooseSkuService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.OrgTreeSimpleDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.customize.CustomizeQueryParam;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class JymlCustomizeChooseSkuServiceImpl implements JymlCustomizeChooseSkuService {

    private final Logger logger = LoggerFactory.getLogger(IscmServiceImpl.class);
    @Resource
    private JymlCustomizeChooseSkuMapper jymlCustomizeChooseSkuMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private AsyncExportFileService asyncExportFileService;
    @Override
    public PageResult<JymlCustomizeChooseSku> getCustomizeChooseSkyList(TokenUserDTO userDTO, CustomizeQueryParam param) {
        try {
            PageResult<JymlCustomizeChooseSku> pageResult = new PageResult<>(0L, new ArrayList<>());
            JymlCustomizeChooseSkuExample example = genSelectExample(userDTO, param);
            long count = jymlCustomizeChooseSkuMapper.countByExample(example);
            if (count <= 0L) {
                return pageResult;
            }
            pageResult.setTotal(count);
            example.setOffset(Long.valueOf(param.getPage() - 1) * param.getPerPage());
            example.setLimit(param.getPerPage());
            List<JymlCustomizeChooseSku> jymlCustomizeChooseSkus = jymlCustomizeChooseSkuMapper.selectByExample(example);
            pageResult.setRows(jymlCustomizeChooseSkus);
            return pageResult;
        } catch (AmisBadRequestException e) {
            logger.error("查询自定义选配商品失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("查询自定义选配商品失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void delete(TokenUserDTO userDTO, CustomizeQueryParam param) {
        try {
            JymlCustomizeChooseSkuExample example = genSelectExample(userDTO, param);
            jymlCustomizeChooseSkuMapper.deleteByExample(example);
        } catch (AmisBadRequestException e) {
            logger.error("删除自定义选配商品失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("删除自定义选配商品失败", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @Override
    public void export(TokenUserDTO userDTO, CustomizeQueryParam param) {
        try {
            param.setToPage(false);
            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.CUSTOMIZE_CHOOSE_SKU, userDTO, new HandlerDataExportService<JymlCustomizeChooseSku>() {
                @Override
                public List<JymlCustomizeChooseSku> getDataToExport() {
                    return null;
                }

                @Override
                public List<JymlCustomizeChooseSku> getDataToExport(Integer page, Integer pageSize) {
                    param.setPage(page + 1);
                    param.setPerPage(pageSize);
                    return getCustomizeChooseSkyList(userDTO, param).getRows();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }
                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return getExportMap();
                }
            });

        } catch (Exception e) {
            logger.error("导出自定义选配商品异常", e);
            throw new AmisBadRequestException(e.getMessage());
        }

    }

    public static LinkedHashMap<String, String> getExportMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap();
        map.put("id", "主键");
        map.put("propertyDesc", "类型");
        map.put("goodsNo", "商品编码");
        map.put("platformOrgId", "区域平台ID");
        map.put("platformName", "区域平台");
        map.put("province", "省份");
        map.put("companyOrgId", "项目公司ID");
        map.put("companyName", "项目公司");
        map.put("city", "城市");
        map.put("storeTypeName", "组货店型");
        return map;
    }

    private JymlCustomizeChooseSkuExample genSelectExample(TokenUserDTO userDTO, CustomizeQueryParam param) {
        JymlCustomizeChooseSkuExample example = new JymlCustomizeChooseSkuExample();
        JymlCustomizeChooseSkuExample.Criteria criteria = example.createCriteria();
        if (null == param.getPlatformOrgId() && null != param.getCompanyOrgId()) {
            OrgInfoBaseCache orgInfoBaseCache = CacheVar.getBusinessByOrgId(param.getCompanyOrgId()).orElseThrow(() -> new AmisBadRequestException("没有查询到项目公司信息"));
            param.setPlatformOrgId(orgInfoBaseCache.getPlatformOrgId());
        }
        // 数字化商品管理员不校验权限
        if (userDTO.getRoles().contains(Constants.SUPPER_ROLE)) {
            if (null != param.getPlatformOrgId()) {
                List<OrgTreeSimpleDTO> userDataScopeStore = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), param.getPlatformOrgId(), Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
                OrgTreeSimpleDTO orgTreeSimpleDTO = userDataScopeStore.stream().findFirst().orElseThrow(() -> new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置"));
                if (CollectionUtils.isEmpty(orgTreeSimpleDTO.getChildren())) {
                    throw new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置");
                }
                List<Long> scopeCompanyOrgIds = orgTreeSimpleDTO.getChildren().stream().map(OrgTreeSimpleDTO::getId).distinct().collect(Collectors.toList());
                if (null != param.getCompanyOrgId()) {
                    if (!scopeCompanyOrgIds.contains(param.getCompanyOrgId())) {
                        throw new AmisBadRequestException("您没有所选项目公司数据权限,请联系管理员配置");
                    }
                    criteria.andCompanyOrgIdEqualTo(param.getCompanyOrgId());
                } else {
                    criteria.andCompanyOrgIdIn(scopeCompanyOrgIds);
                }
            }
        } else {
            List<OrgTreeSimpleDTO> userDataScopeStore = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), null == param.getPlatformOrgId() ? 3L : param.getPlatformOrgId(), Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            OrgTreeSimpleDTO orgTreeSimpleDTO = userDataScopeStore.stream().findFirst().orElseThrow(() -> new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置"));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTO.getChildren())) {
                throw new AmisBadRequestException("您没有项目公司数据权限,请联系管理员配置");
            }
            List<Long> scopeCompanyOrgIds = orgTreeSimpleDTO.getChildren().stream().map(OrgTreeSimpleDTO::getId).distinct().collect(Collectors.toList());
            if (null != param.getCompanyOrgId()) {
                if (!scopeCompanyOrgIds.contains(param.getCompanyOrgId())) {
                    throw new AmisBadRequestException("您没有所选项目公司数据权限,请联系管理员配置");
                }
                criteria.andCompanyOrgIdEqualTo(param.getCompanyOrgId());
            } else {
                criteria.andCompanyOrgIdIn(scopeCompanyOrgIds);
            }
        }
        if (StringUtils.isNotBlank(param.getGoodsNos())) {
            criteria.andGoodsNoIn(Arrays.stream(StringUtils.split(param.getGoodsNos(), ",")).map(String::trim).distinct().collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(param.getPropertyCode())) {
            criteria.andPropertyCodeEqualTo(param.getPropertyCode());
        }
        if (StringUtils.isNotBlank(param.getStoreType())) {
            criteria.andStoreTypeEqualTo(param.getStoreType());
        }
        if (StringUtils.isNotBlank(param.getProvince())) {
            criteria.andProvinceEqualTo(param.getProvince());
        }
        if (StringUtils.isNotBlank(param.getCity())) {
            criteria.andCityEqualTo(param.getCity());
        }
        return example;
    }
}
