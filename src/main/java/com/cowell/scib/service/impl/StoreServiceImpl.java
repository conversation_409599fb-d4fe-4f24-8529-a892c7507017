package com.cowell.scib.service.impl;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.StoreService;
import com.cowell.scib.service.dto.MdmCompanyTransformDTO;
import com.cowell.scib.service.dto.MdmLicenseBaseDTO;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.feign.StoreFeignClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/3/14 17:13
 */
@Service
public class StoreServiceImpl implements StoreService {

    private final Logger logger = LoggerFactory.getLogger(StoreServiceImpl.class);

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Override
    public Map<String, MdmStoreBaseDTO> getBusinessAndStoreIdByStoreNos(List<String> storeNos) {
        if (CollectionUtils.isEmpty(storeNos)){
            return new HashMap<>();
        }
        try {
            ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeFeignClient.findMdmStoreBaseListByStoreNoList(storeNos);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("获取连锁门店信息失败。");
            }
            return responseEntity.getBody().stream().collect(Collectors.toMap(v->v.getStoreNo(), Function.identity(), (k1, k2) -> k1));
        }catch (Exception e){
            logger.error("获取连锁门店信息失败",e);
            throw e;
        }
    }

    @Override
    public Map<Long, MdmStoreBaseDTO> findStoreByStoreIds(List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return new HashMap<>();
        }
        try {
            ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeFeignClient.findStoreByStoreIds(storeIds);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("获取连锁门店信息失败。");
            }
            return responseEntity.getBody().stream().collect(Collectors.toMap(v -> v.getStoreId(), Function.identity(), (k1, k2) -> k1));
        } catch (Exception e) {
            logger.error("获取连锁门店信息失败", e);
            throw e;
        }
    }

    @Override
    public List<MdmStoreBaseDTO> findStoreByStoreNos(List<String> storeCodeList) {
        logger.debug("findStoreByStoreNos|storeCodeList:{}.", storeCodeList);
        if(CollectionUtils.isEmpty(storeCodeList)){
            return null;
        }
        ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeFeignClient.findStoreByStoreNos(storeCodeList);
        if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || CollectionUtils.isEmpty(responseEntity.getBody())) {
            logger.warn("findStoreByStoreNos|没有获取到MDM门店信息|");
            return null;
        }
        return responseEntity.getBody();
    }

    @Override
    public List<MdmStoreBaseDTO> findStoreByStoreNosAndExtend(List<String> storeCodeList) {
        logger.debug("findStoreByStoreNosAndExtend|storeCodeList:{}.", storeCodeList);
        if(CollectionUtils.isEmpty(storeCodeList)){
            return null;
        }
        List<MdmStoreBaseDTO> result = new ArrayList<>();
        Lists.partition(storeCodeList, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE).forEach(v -> {
            try {
                ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeFeignClient.findStoreByStoreNosAndExtend(v, true);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || CollectionUtils.isEmpty(responseEntity.getBody())) {
                    logger.warn("findStoreByStoreNosAndExtend|没有获取到MDM门店信息|");
                    result.addAll(new ArrayList<>());
                }
                result.addAll(responseEntity.getBody());
            } catch (Exception e) {
                logger.error("findStoreByStoreNosAndExtend|没有获取到MDM门店信息|storeNos:", v);
            }
        });
        return result;
    }

    @Override
    public List<MdmLicenseBaseDTO> findStoreLicense(Long businessId, Long storeId) {
        logger.debug("findStoreLicense|businessId:{},storeId:{}", businessId, storeId);
        ResponseEntity<List<MdmLicenseBaseDTO>> responseEntity = storeFeignClient.findStoreLicense(businessId, storeId);
        if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
            logger.warn("findStoreByStoreNosAndExtend|没有获取到MDM门店信息|");
            return new ArrayList<>();
        }
        return responseEntity.getBody();
    }

    @Override
    public MdmCompanyTransformDTO transformBusiness(MdmCompanyTransformDTO mdmCompanyTransformDTO) {
        logger.debug("transformBusiness|mdmCompanyTransformDTO:{}", mdmCompanyTransformDTO);
        ResponseEntity<MdmCompanyTransformDTO> responseEntity = storeFeignClient.transformBusiness(mdmCompanyTransformDTO);
        if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
            logger.warn("transformBusiness|通过法人公司sapCode获取businessId为空|");
            return new MdmCompanyTransformDTO();
        }
        return responseEntity.getBody();
    }
}
