package com.cowell.scib.service.impl;

import com.cowell.scib.constant.ExtendFiledConstants;
import com.cowell.scib.entity.*;
import com.cowell.scib.enums.AlertTypeEnum;
import com.cowell.scib.enums.DevelopTypeEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.SelectMethodEnum;
import com.cowell.scib.mapperDgms.DevelopModuleMapper;
import com.cowell.scib.mapperDgms.DevelopModuleRecordMapper;
import com.cowell.scib.mapperDgms.ReachModuleMapper;
import com.cowell.scib.mapperDgms.extend.DevelopModuleRecordExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.DevelopModuleRecordFileService;
import com.cowell.scib.service.DevelopModuleRecordService;
import com.cowell.scib.service.IAlertService;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.permssion.EmployeeDetailWithWxDTO;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.service.vo.DevelopModuleRecordVO;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.ExtendUtil;
import com.cowell.scib.utils.KeyUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:28
 */
@Slf4j
@Service
public class DevelopModuleRecordServiceImpl implements DevelopModuleRecordService {

    @Value("${develop.record.days:3}")
    private int reminderDays;


    @Autowired
    private DevelopModuleRecordFileService recordFileService;
    @Autowired
    private DevelopModuleRecordMapper recordFileMapper;
    @Autowired
    private DevelopModuleRecordExtendMapper recordExtendMapper;
    @Autowired
    private DevelopModuleRecordMapper developModuleRecordMapper;
    @Autowired
    private ReachModuleMapper reachModuleMapper;
    @Autowired
    private IAlertService alertService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private DevelopModuleMapper developModuleMapper;
    @ApolloJsonValue("${develop.module.default.url:[]}")
    public List<DevelopModuleDefaultUrl> defaultUrlList;

    @Override
    public List<DevelopModuleRecordDTO> listDevelopModuleRecordByCode(String moudleCode) {
        if(StringUtils.isBlank(moudleCode)){
            return Lists.newArrayList();
        }
        DevelopModuleRecordExample fileExample = new DevelopModuleRecordExample();
        fileExample.createCriteria().andModuleCodeEqualTo(moudleCode);
        List<DevelopModuleRecord> recordList = recordFileMapper.selectByExample(fileExample);
        if(CollectionUtils.isEmpty(recordList)){
            return Lists.newArrayList();
        }
        return listRecordDTO(recordList);
    }

    @Override
    public DevelopModuleRecordDTO detailDevelopModuleRecordById(Integer recordId) {
        if(Objects.isNull(recordId)){
            return null;
        }
        DevelopModuleRecord recordDetail = recordFileMapper.selectByPrimaryKey(recordId);
        if(Objects.isNull(recordDetail)){
            return null;
        }
        return listRecordDTO(Lists.newArrayList(recordDetail)).stream().findFirst().orElse(new DevelopModuleRecordDTO());
    }

    @Override
    public PageResult<DevelopModuleRecordVO> listPageDevelopModuleRecordByCode(DevelopListParam developListParam) {

        List<DevelopModuleRecordVO> recordVOList = Collections.emptyList();
        long count = recordExtendMapper.searchModuleRecordCount(developListParam);
        if(count > 0){
            List<DevelopModuleRecord> recordList = recordExtendMapper.searchModuleRecordList(developListParam);
            recordVOList = listRecordDTO(recordList).stream().map(v->{
                DevelopModuleRecordVO recordVO = new DevelopModuleRecordVO();
                BeanUtils.copyProperties(v, recordVO);
                recordVO.setRecordId(v.getId());
                recordVO.setFileVOList(recordFileService.fileUrlListToString(v.getFileList()));
                return recordVO;
            }).collect(Collectors.toList());
        }
        log.info("recordVOList:{}.",recordVOList);
        return new PageResult(count, recordVOList);
    }

    @Override
    public Map<String, List<DevelopModuleRecordDTO>> mapDevelopModuleRecordByCode(List<String> moudleCodeList) {
        if(CollectionUtils.isEmpty(moudleCodeList)){
            return new HashMap<>();
        }
        Map<String, List<DevelopModuleRecordDTO>> modualMap = new HashMap<>();
        for(String moduleCode : moudleCodeList){
            modualMap.put(moduleCode, listDevelopModuleRecordByCode(moduleCode));
        }
        return modualMap;
    }

    @Override
    public void addModuleRecord(DevelopRecordAddParam developRecordAddParam, TokenUserDTO userDTO) {
        DevelopModuleRecord developModuleRecord = new DevelopModuleRecord();
        BeanUtils.copyProperties(developRecordAddParam, developModuleRecord);
        developModuleRecord.setId(developRecordAddParam.getRecordId());
        developModuleRecord.setDevelopTime(DateUtils.parseDateTime(developRecordAddParam.getDevelopTime(), DateUtils.DATE_MINUTE_PATTERN));
        developModuleRecord.setImageUrls(developRecordAddParam.getImageUrlList());
        developModuleRecord.setReachChannels(developRecordAddParam.getReachChannelList());
        developModuleRecord.setReachGroupids(developRecordAddParam.getReachGroupIdList());
        log.info("addModuleRecord|userDTO:{}.", userDTO);
        developModuleRecord.setCreatedBy(userDTO.getUserId());
        developModuleRecord.setCreatedName(userDTO.getName());
        developModuleRecord.setUpdatedBy(userDTO.getUserId());
        developModuleRecord.setUpdatedName(userDTO.getName());
        developModuleRecord.setExtend(ExtendUtil.putExtendValue(developModuleRecord.getExtend(), ExtendFiledConstants.SCIB_DEVELOP_URL, developRecordAddParam.getDevelopUrl()));
        developModuleRecordMapper.insertSelective(developModuleRecord);
        //文件
        recordFileService.addOrEditFile(developRecordAddParam.getFileVOList(), developRecordAddParam, true);
    }

    @Override
    public void editModuleRecord(DevelopRecordAddParam developRecordAddParam, TokenUserDTO userDTO) {
        DevelopModuleRecord developModuleRecord = new DevelopModuleRecord();
        BeanUtils.copyProperties(developRecordAddParam, developModuleRecord);
        developModuleRecord.setId(developRecordAddParam.getRecordId());
        developModuleRecord.setDevelopTime(DateUtils.parseDateTime(developRecordAddParam.getDevelopTime(), DateUtils.DATE_MINUTE_PATTERN));
        developModuleRecord.setImageUrls(developRecordAddParam.getImageUrlList());
        developModuleRecord.setReachChannels(developRecordAddParam.getReachChannelList());
        developModuleRecord.setReachGroupids(developRecordAddParam.getReachGroupIdList());
        log.info("editModuleRecord|userDTO:{}.", userDTO);
        developModuleRecord.setUpdatedBy(userDTO.getUserId());
        developModuleRecord.setUpdatedName(userDTO.getName());
        developModuleRecord.setExtend(ExtendUtil.putExtendValue(developModuleRecord.getExtend(), ExtendFiledConstants.SCIB_DEVELOP_URL, developRecordAddParam.getDevelopUrl()));
        developModuleRecordMapper.updateByPrimaryKeySelective(developModuleRecord);
        //文件
        recordFileService.addOrEditFile(developRecordAddParam.getFileVOList(), developRecordAddParam, false);
    }

    @Override
    public DevelopModuleRecordVO recentlyRecord(String moduleCode) {
        if(StringUtils.isBlank(moduleCode)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        DevelopModuleRecord developModuleRecord = recordExtendMapper.searchRencentModuleRecordList(moduleCode, reminderDays);
        if(Objects.isNull(developModuleRecord)){
            return null;
        }
        return listRecordDTO(Lists.newArrayList(developModuleRecord)).stream().map(v->{
            DevelopModuleRecordVO recordVO = new DevelopModuleRecordVO();
            BeanUtils.copyProperties(v, recordVO);
            recordVO.setDevelopTypeDesc(DevelopTypeEnum.getNameByCode(v.getDevelopType()));
            recordVO.setRecordId(v.getId());
            recordVO.setFileVOList(recordFileService.fileUrlListToString(v.getFileList()));
            if(StringUtils.isNotBlank(recordVO.getDevelopTime())){
                recordVO.setDevelopTime(recordVO.getDevelopTime().split(" ")[0]);
            }
            return recordVO;
        }).findFirst().get();
    }

    @Override
    public void sendMessage(DevelopRecordAddParam developRecordAddParam) {
        // 1. 参数校验
        if(StringUtils.isBlank(developRecordAddParam.getReachChannelList())){
            log.info("sendMessage|reachChannelList is empty.");
            return;
        }
        if(StringUtils.isBlank(developRecordAddParam.getReachGroupIdList())){
            log.info("sendMessage|reachGroupIdList is empty.");
            return;
        }
        List<Long> groupIdList = new ArrayList<>();
        for (String groupId : developRecordAddParam.getReachGroupIdList().split(",")) {
            try {
                groupIdList.add(Long.parseLong(groupId.trim()));
            } catch (NumberFormatException e) {
                log.error("DevelopModuleRecordServiceImpl|sendMessage|触达组Id存在无法解析数字groupId:{}",groupId);
            }
        }
        if(CollectionUtils.isEmpty(groupIdList)){
            log.info("sendMessage|groupIdList is empty.入参ReachGroupIdList:{}",developRecordAddParam.getReachGroupIdList());
            return;
        }
        // 2. 查询触达组信息
        List<ReachModule> reachModuleList = queryReachModules(groupIdList);
        if(CollectionUtils.isEmpty(reachModuleList)){
            log.info("sendMessage|reachModuleList is empty.入参ReachGroupIdList:{}",developRecordAddParam.getReachGroupIdList());
            return;
        }
        // 3. 收集员工工号
        Set<String> empCodeSet = collectEmployeeCodes(reachModuleList);
        if (CollectionUtils.isEmpty(empCodeSet)) {
            log.info("sendMessage|没有找到需要通知的员工");
            return;
        }
        // 4. 查询员工详细信息
        List<EmployeeDetailWithWxDTO> employeeDetailDTOList = permissionService.getUserNamesByEmpCodes(Lists.newArrayList(empCodeSet));
        if(CollectionUtils.isEmpty(employeeDetailDTOList)){
            log.info("sendMessage|employeeDetailDTOList is empty.没有合法的用户信息");
            return;
        }
        // 5. 提取联系方式并发送内容
        Set<String> wxIdSet = employeeDetailDTOList.stream().map(EmployeeDetailWithWxDTO::getWxId).filter(wxId -> wxId != null && !wxId.isEmpty()).collect(Collectors.toSet());
        Set<String> emailSet = employeeDetailDTOList.stream().map(EmployeeDetailWithWxDTO::getEmail).filter(email -> email != null && !email.isEmpty()).collect(Collectors.toSet());
        log.info("sendMessage|wxIdSet:{},emailSet:{}",wxIdSet,emailSet);
        // 检查是否有有效的联系方式
        if (CollectionUtils.isEmpty(wxIdSet) && CollectionUtils.isEmpty(emailSet)) {
            log.info("sendMessage|没有找到有效的通知渠道(微信或邮箱)");
            return;
        }
        // 6. 发送内容
        sendContend(developRecordAddParam,wxIdSet,emailSet);
    }

    /**
     * 查询触达组信息
     */
    private List<ReachModule> queryReachModules(List<Long> reachGroupIdList) {
        ReachModuleExample example = new ReachModuleExample();
        example.createCriteria().andIdIn(reachGroupIdList);
        return reachModuleMapper.selectByExample(example);
    }

    /**
     * 收集需要通知的员工工号
     */
    private Set<String> collectEmployeeCodes(List<ReachModule> reachModuleList) {
        Set<String> empCodeSet = new HashSet<>();
        Set<String> allRoleCodes = new HashSet<>();

        // 分别收集角色和指定人员
        for (ReachModule reachModule : reachModuleList) {
            // 按角色收集
            if (SelectMethodEnum.BY_ROLE.getCode().equals(reachModule.getSelectMethod())
                    && StringUtils.isNotBlank(reachModule.getReachPersonCodes())) {
                // 收集所有角色编码，稍后一次性查询
                String[] roleCodes = reachModule.getReachPersonCodes().split(",");
                Collections.addAll(allRoleCodes, roleCodes);
            }

            // 按人员收集
            if (SelectMethodEnum.SPECIFIC_PERSON.getCode().equals(reachModule.getSelectMethod())
                    && StringUtils.isNotBlank(reachModule.getReachPersons())) {
                Arrays.stream(reachModule.getReachPersons().split(","))
                        .filter(StringUtils::isNotBlank)
                        .forEach(empCodeSet::add);
            }
        }

        // 根据角色查询员工(只查询一次)
        if (!allRoleCodes.isEmpty()) {
            // 将收集到的所有角色编码转换为数组，一次性查询
            String[] roleCodeArray = allRoleCodes.toArray(new String[0]);
            for (String roleCode : roleCodeArray){
                Set<String> empCodesByRole = permissionService.getEmpCodeByRoleCode(roleCode);
                if (CollectionUtils.isNotEmpty(empCodesByRole)) {
                    empCodeSet.addAll(empCodesByRole);
                }
            }

        }

        return empCodeSet;
    }

    /**
     * 获取DTO
     * @param recordList
     * @return
     */
    private List<DevelopModuleRecordDTO> listRecordDTO(List<DevelopModuleRecord> recordList){
        List<String> codeVersionKeyList = recordList.stream().filter(v->StringUtils.isNotBlank(v.getModuleCode())).map(v-> KeyUtil.delevopFileKey(v.getModuleCode(), v.getDevelopVersion())).collect(Collectors.toList());
        Map<String, List<DevelopModuleRecordFile>> recordFileMap = recordFileService.mapFileSort(codeVersionKeyList);

        return recordList.stream().map(v->{
            return ModelDevelopDTOCover.recordToDTO(v, recordFileMap.get(KeyUtil.delevopFileKey(v.getModuleCode(), v.getDevelopVersion())));
        }).collect(Collectors.toList());
    }

    private void sendContend(DevelopRecordAddParam developRecordAddParam, Set<String> wxIdSet, Set<String> emailSet) {
        DevelopModule developMpdule = getDevelopMpdule(developRecordAddParam.getModuleCode());
        if(null == developMpdule){
            log.info("sendContend|没有找到有效的功能模块,moduleCode:{}",developRecordAddParam.getModuleCode());
            return;
        }
        String moduleName = developMpdule.getModuleName();
        String msg = buildNewFeatureNotification(moduleName,developRecordAddParam);
        List<String> channelList = new ArrayList<>(Arrays.asList(developRecordAddParam.getReachChannelList().split(",")));
        // 企业微信通知
        if (channelList.contains(AlertTypeEnum.QYWX.getAlertType())) {
            sendAlert(AlertTypeEnum.QYWX, wxIdSet, msg, "触达人员企业微信号为空");
        }
        // 邮件通知
        if (channelList.contains(AlertTypeEnum.EMAIL.getAlertType())) {
            sendAlert(AlertTypeEnum.EMAIL, emailSet, msg, "触达人员邮件地址为空");
        }
    }

    /**
     * 构建促销中台新功能上线通知内容
     * @return 格式化的通知内容
     */
    public String buildNewFeatureNotification(String moduleName,DevelopRecordAddParam developRecordAddParam) {
        ;
        Map<String, DevelopModuleDefaultUrl> partsMap = defaultUrlList.stream().collect(Collectors.toMap(k -> k.getModuleCode(), part -> part));
        DevelopModuleDefaultUrl defaultUrl = partsMap.get(developRecordAddParam.getModuleCode());
        StringBuilder content = new StringBuilder();
        // 标题
        content.append("【"+moduleName+"】新功能上线通知！").append("\n\n");
        // 功能列表
        content.append(developRecordAddParam.getDevelopContent()).append("\n");
        // 链接
        content.append("点击链接，查看详情：").append("\n");
        if(StringUtils.isNotBlank(developRecordAddParam.getDevelopUrl())){
            content.append(developRecordAddParam.getDevelopUrl());
        }else{
            content.append(defaultUrl.getDomainName());
            content.append(defaultUrl.getPath());
        }
        return content.toString();
    }

    private DevelopModule getDevelopMpdule(String code){
        DevelopModuleExample example = new DevelopModuleExample();
        example.createCriteria().andModuleCodeEqualTo(code);
        List<DevelopModule> list = developModuleMapper.selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 发送提醒
     *
     * @param alertType 提醒类型
     * @param userSet 用户集合
     * @param msg 消息内容
     * @param emptyMsg 用户集合为空时的日志消息
     */
    private void sendAlert(AlertTypeEnum alertType, Set<String> userSet, String msg, String emptyMsg) {
        if (CollectionUtils.isEmpty(userSet)) {
            log.info("{}: {}", emptyMsg, alertType.getAlertType());
            return;
        }

        String users = userSet.stream().collect(Collectors.joining("|"));
        if (StringUtils.isBlank(users)) {
            log.info("{}: {}", emptyMsg, alertType.getAlertType());
            return;
        }

        AlertContent alert = new AlertContent();
        alert.setType(alertType.getAlertType());
        alert.setFlag(true);
        alert.setToUsers(users);
        alert.setSubject("功能更新");
        alert.setMessage(msg);

        log.debug("正在发送{}通知，接收用户：{}", alertType.getAlertType(), users);
        alertService.alert(alert);
    }

}
