package com.cowell.scib.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.cowell.nyuwa.cos.util.CosService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.JymlStoreSkuLimitAdjustDetailMapper;
import com.cowell.scib.mapperDgms.JymlStoreSkuLimitAdjustEffectMapper;
import com.cowell.scib.mapperDgms.JymlStoreSkuLimitAdjustMapper;
import com.cowell.scib.mapperDgms.extend.JymlStoreSkuLimitAdjustDetailExtendMapper;
import com.cowell.scib.mapperDgms.extend.JymlStoreSkuLimitAdjustEffectExtendMapper;
import com.cowell.scib.mapperTidb.JymlSkuMaxLimitConfigureMapper;
import com.cowell.scib.mapperTidb.extend.JymlSkuMaxLimitConfigureExtendMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.skuadjust.*;
import com.cowell.scib.service.listener.LimitExcelReadListener;
import com.cowell.scib.service.vo.PageResponse;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.ExcelCheck;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SkuConfigureAdjustServiceImpl implements SkuConfigureAdjustService {
    private final Logger logger = LoggerFactory.getLogger(SkuConfigureAdjustServiceImpl.class);

    @Resource
    private JymlSkuMaxLimitConfigureMapper jymlSkuMaxLimitConfigureMapper;
    @Resource
    private JymlSkuMaxLimitConfigureExtendMapper jymlSkuMaxLimitConfigureExtendMapper;
    @Resource
    private JymlStoreSkuLimitAdjustEffectMapper jymlStoreSkuLimitAdjustEffectMapper;
    @Resource
    private JymlStoreSkuLimitAdjustEffectExtendMapper jymlStoreSkuLimitAdjustEffectExtendMapper;
    @Resource
    private JymlStoreSkuLimitAdjustMapper jymlStoreSkuLimitAdjustMapper;
    @Resource
    private JymlStoreSkuLimitAdjustDetailMapper jymlStoreSkuLimitAdjustDetailMapper;
    @Resource
    private JymlStoreSkuLimitAdjustDetailExtendMapper jymlStoreSkuLimitAdjustDetailExtendMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private NoSequenceService noSequenceService;
    @Resource
    private AsyncExportFileService asyncExportFileService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CosService cosService;
    private String IMPORT_ADJUST_RECORD = "SCIB-IMPORT-ADJUST-RECORD-";
    public static List<String> CollNameList=Lists.newArrayList("错误原因");

    @Value("${scib.easyexcel.configure.adjust.import.num:20000}")
    private Integer importNum;
    @Resource
    @Qualifier("configureAdjustAddExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;


    @Override
    public List<JymlSkuMaxLimitConfigureTreeDTO> getSkuConfigureTree() {
        try {
            List<JymlSkuMaxLimitConfigure> configures = jymlSkuMaxLimitConfigureExtendMapper.selectAllCategory(null);
            if (CollectionUtils.isEmpty(configures)) {
                return new ArrayList<>();
            }
            // 开始组装树
            // 存储每个层级的节点，使用组合键（大类id + 中类id 等）避免冲突
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> categoryMap =  new TreeMap<>();
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> middleCategoryMap = new TreeMap<>();
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> smallCategoryMap = new TreeMap<>();
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> subCategoryMap = new TreeMap<>();
            // 存储根节点
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> rootMap = new TreeMap<>();

            for (JymlSkuMaxLimitConfigure configure : configures) {
                // 处理大类
                String categoryId = configure.getCategory();
                String categoryName = configure.getCategoryName();
                JymlSkuMaxLimitConfigureTreeDTO categoryNode = categoryMap.computeIfAbsent(categoryId, k -> {
                    JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                    BeanUtils.copyProperties(configure, node);
                    node.setLevel(1);
                    rootMap.put(categoryId, node);
                    return node;
                });
                // 处理中类
                if(StringUtils.isNotBlank(configure.getMiddleCategory())) {
                    String middleCategoryId = configure.getMiddleCategory();
                    String middleCategoryName = configure.getMiddleCategoryName();
                    String middleCategoryKey = categoryId + "_" + middleCategoryId;
                    JymlSkuMaxLimitConfigureTreeDTO middleCategoryNode = middleCategoryMap.computeIfAbsent(middleCategoryKey, k -> {
                        JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                        BeanUtils.copyProperties(configure, node);
                        node.setCategory(configure.getMiddleCategory());
                        node.setCategoryName(configure.getMiddleCategoryName());
                        node.setCategoryCompleteName(configure.getCategoryName() + "|" + configure.getMiddleCategoryName());
                        node.setLevel(2);
                        categoryNode.addChild(node);
                        return node;
                    });
                    // 处理小类
                    if (StringUtils.isNotBlank(configure.getSmallCategory())) {
                        String smallCategoryId = configure.getSmallCategory();
                        String smallCategoryName = configure.getSmallCategoryName();
                        String smallCategoryKey = middleCategoryKey + "_" + smallCategoryId;
                        JymlSkuMaxLimitConfigureTreeDTO smallCategoryNode = smallCategoryMap.computeIfAbsent(smallCategoryKey, k -> {
                            JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                            BeanUtils.copyProperties(configure, node);
                            node.setLevel(3);
                            node.setCategory(configure.getSmallCategory());
                            node.setCategoryName(configure.getSmallCategoryName());
                            node.setCategoryCompleteName(configure.getCategoryName() + "|" + configure.getMiddleCategoryName() + "|" + configure.getSmallCategoryName());
                            middleCategoryNode.addChild(node);
                            return node;
                        });
                        // 处理子类
                        if (StringUtils.isNotBlank(configure.getSubCategory())) {
                            String subCategoryId = configure.getSubCategory();
                            String subCategoryName = configure.getSubCategoryName();
                            String subCategoryKey = smallCategoryKey + "_" + subCategoryId;
                            subCategoryMap.computeIfAbsent(subCategoryKey, k -> {
                                JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                                BeanUtils.copyProperties(configure, node);
                                node.setLevel(4);
                                node.setCategory(configure.getSubCategory());
                                node.setCategoryName(configure.getSubCategoryName());
                                node.setCategoryCompleteName(configure.getCategoryName() + "|" + configure.getMiddleCategoryName() + "|" + configure.getSmallCategoryName() + "|" + configure.getSubCategoryName());
                                smallCategoryNode.addChild(node);
                                return node;
                            });
                        }
                    }
                }
            }
            return new ArrayList<>(rootMap.values());
        } catch (Exception e) {
            logger.error("根据门店选择商品管控类别树失败", e);
            throw e;
        }
    }
    @Override
    public List<JymlSkuMaxLimitConfigureTreeDTO> getSkuConfigureTreeByStore(Long storeOrgId) {
        try {
            Optional<MdmStoreExDTO> storeExOpt = CacheVar.getStoreExtInfoByStoreOrgId(storeOrgId);
            if (!storeExOpt.isPresent()) {
                logger.info("没有查询到店型缓存");
                return new ArrayList<>();
            }
            if (StringUtils.isBlank(storeExOpt.get().getStoreTypeCode())) {
                throw new BusinessErrorException("该门店没有店型");
            }
            Optional<OrgInfoBaseCache> storeOpt = CacheVar.getStoreByOrgId(storeOrgId);
            if (!storeOpt.isPresent()) {
                logger.info("没有查询到门店缓存");
                return new ArrayList<>();
            }
            Long version = getNewVersion(storeOpt.get().getBusinessOrgId());
            JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
            example.createCriteria()
                    .andVersionEqualTo(version)
                    .andBusinessOrgIdEqualTo(storeOpt.get().getBusinessOrgId())
                    .andCityEqualTo(storeExOpt.get().getCity())
                    .andStatusEqualTo(Constants.NORMAL_STATUS)
                    .andStoreTypeEqualTo(storeExOpt.get().getStoreTypeCode());
            List<JymlSkuMaxLimitConfigure> configures = jymlSkuMaxLimitConfigureMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(configures)) {
                return new ArrayList<>();
            }
            // 开始组装树
            // 存储每个层级的节点，使用组合键（大类id + 中类id 等）避免冲突
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> categoryMap =  new TreeMap<>();
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> middleCategoryMap = new TreeMap<>();
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> smallCategoryMap = new TreeMap<>();
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> subCategoryMap = new TreeMap<>();
            // 存储根节点
            Map<String, JymlSkuMaxLimitConfigureTreeDTO> rootMap = new TreeMap<>();

            for (JymlSkuMaxLimitConfigure configure : configures) {
                // 处理大类
                String categoryId = configure.getCategory();
                String categoryName = configure.getCategoryName();
                JymlSkuMaxLimitConfigureTreeDTO categoryNode = categoryMap.computeIfAbsent(categoryId, k -> {
                    JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                    BeanUtils.copyProperties(configure, node);
                    node.setLevel(1);
                    node.setCategoryComplete(configure.getCategory());
                    node.setCategoryCompleteName(configure.getCategoryName());
                    rootMap.put(categoryId, node);
                    return node;
                });
                // 处理中类
                if(StringUtils.isNotBlank(configure.getMiddleCategory())) {
                    String middleCategoryId = configure.getMiddleCategory();
                    String middleCategoryName = configure.getMiddleCategoryName();
                    String middleCategoryKey = categoryId + "_" + middleCategoryId;
                    JymlSkuMaxLimitConfigureTreeDTO middleCategoryNode = middleCategoryMap.computeIfAbsent(middleCategoryKey, k -> {
                        JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                        BeanUtils.copyProperties(configure, node);
                        node.setCategory(configure.getMiddleCategory());
                        node.setCategoryName(configure.getMiddleCategoryName());
                        node.setCategoryComplete(configure.getCategory() + "|" + configure.getMiddleCategory());
                        node.setCategoryCompleteName(configure.getCategoryName() + "|" + configure.getMiddleCategoryName());
                        node.setLevel(2);
                        categoryNode.addChild(node);
                        return node;
                    });
                    // 处理小类
                    if (StringUtils.isNotBlank(configure.getSmallCategory())) {
                        String smallCategoryId = configure.getSmallCategory();
                        String smallCategoryName = configure.getSmallCategoryName();
                        String smallCategoryKey = middleCategoryKey + "_" + smallCategoryId;
                        JymlSkuMaxLimitConfigureTreeDTO smallCategoryNode = smallCategoryMap.computeIfAbsent(smallCategoryKey, k -> {
                            JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                            BeanUtils.copyProperties(configure, node);
                            node.setLevel(3);
                            node.setCategory(configure.getSmallCategory());
                            node.setCategoryName(configure.getSmallCategoryName());
                            node.setCategoryComplete(configure.getCategory() + "|" + configure.getMiddleCategory() + "|" + configure.getSmallCategory());
                            node.setCategoryCompleteName(configure.getCategoryName() + "|" + configure.getMiddleCategoryName() + "|" + configure.getSmallCategoryName());
                            middleCategoryNode.addChild(node);
                            return node;
                        });
                        // 处理子类
                        if (StringUtils.isNotBlank(configure.getSubCategory())) {
                            String subCategoryId = configure.getSubCategory();
                            String subCategoryName = configure.getSubCategoryName();
                            String subCategoryKey = smallCategoryKey + "_" + subCategoryId;
                            subCategoryMap.computeIfAbsent(subCategoryKey, k -> {
                                JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
                                BeanUtils.copyProperties(configure, node);
                                node.setLevel(4);
                                node.setCategory(configure.getSubCategory());
                                node.setCategoryName(configure.getSubCategoryName());
                                node.setCategoryComplete(configure.getCategory() + "|" + configure.getMiddleCategory() + "|" + configure.getSmallCategory() + "|" + configure.getSubCategory());
                                node.setCategoryCompleteName(configure.getCategoryName() + "|" + configure.getMiddleCategoryName() + "|" + configure.getSmallCategoryName() + "|" + configure.getSubCategoryName());
                                smallCategoryNode.addChild(node);
                                return node;
                            });
                        }
                    }
                }
            }
            return new ArrayList<>(rootMap.values());
        } catch (Exception e) {
            logger.error("根据门店选择商品管控类别树失败", e);
            throw e;
        }
    }

    @Override
    public List<JymlSkuMaxLimitConfigureTreeDTO> getOldConfigure(AdjustQueryParam param){
        if (null == param.getStoreOrgId()) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(param.getCtrlCategory())) {
            return new ArrayList<>();
        }
        MdmStoreExDTO mdmStoreExDTO = CacheVar.getStoreExtInfoByStoreOrgId(param.getStoreOrgId()).orElseThrow(() -> new BusinessErrorException("没有查询到门店所属店型"));
        OrgInfoBaseCache store = CacheVar.getStoreByOrgId(param.getStoreOrgId()).orElseThrow(() -> new BusinessErrorException("没有查询到门店"));
        if(StringUtils.isBlank(mdmStoreExDTO.getStoreTypeCode())) {
            throw new BusinessErrorException("门店店型不存在");
        }
        String[] split = StringUtils.split( param.getCtrlCategory(), "|");
        CategoryLevelEnum categoryLevelEnum = CategoryLevelEnum.getEnumByCode(split.length);
        if (null == categoryLevelEnum) {
            throw new BusinessErrorException("未定义的管控层级,请联系管理员");
        }
        JymlStoreSkuLimitAdjustEffectExample effectExample = new JymlStoreSkuLimitAdjustEffectExample();
        JymlStoreSkuLimitAdjustEffectExample.Criteria criteria = effectExample.createCriteria();
        JymlSkuMaxLimitConfigureExample configureExample = new JymlSkuMaxLimitConfigureExample();
        JymlSkuMaxLimitConfigureExample.Criteria criteria1 = configureExample.createCriteria();
        criteria.andStoreOrgIdEqualTo(param.getStoreOrgId());
        switch (categoryLevelEnum) {
            case SUB:
                criteria.andSubCategoryEqualTo(split[3]);
                criteria1.andSubCategoryEqualTo(split[3]);
                criteria.andSmallCategoryEqualTo(split[2]);
                criteria1.andSmallCategoryEqualTo(split[2]);
                criteria.andMiddleCategoryEqualTo(split[1]);
                criteria1.andMiddleCategoryEqualTo(split[1]);
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            case SMALL:
                criteria.andSubCategoryEqualTo("");
                criteria1.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo(split[2]);
                criteria1.andSmallCategoryEqualTo(split[2]);
                criteria.andMiddleCategoryEqualTo(split[1]);
                criteria1.andMiddleCategoryEqualTo(split[1]);
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            case MIDDLE:
                criteria.andSubCategoryEqualTo("");
                criteria1.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo("");
                criteria1.andSmallCategoryEqualTo("");
                criteria.andMiddleCategoryEqualTo(split[1]);
                criteria1.andMiddleCategoryEqualTo(split[1]);
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            case CATEGROY:
                criteria.andSubCategoryEqualTo("");
                criteria1.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo("");
                criteria1.andSmallCategoryEqualTo("");
                criteria.andMiddleCategoryEqualTo("");
                criteria1.andMiddleCategoryEqualTo("");
                criteria.andCategoryEqualTo(split[0]);
                criteria1.andCategoryEqualTo(split[0]);
                break;
            default:break;
        }
        List<JymlStoreSkuLimitAdjustEffect> effectList = jymlStoreSkuLimitAdjustEffectMapper.selectByExample(effectExample);
        if (CollectionUtils.isEmpty(effectList)) {
            return new ArrayList<>();
        }
        Long version = getNewVersion(store.getBusinessOrgId());
        criteria1.andVersionEqualTo(version).andCityEqualTo(mdmStoreExDTO.getCity()).andBusinessOrgIdEqualTo(store.getBusinessOrgId()).andStoreTypeEqualTo(effectList.get(0).getStoreType());
        return jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample).stream().map(configure -> {
            JymlSkuMaxLimitConfigureTreeDTO node = new JymlSkuMaxLimitConfigureTreeDTO();
            BeanUtils.copyProperties(configure, node);
            node.setLevel(categoryLevelEnum.getCode());
            node.setCategory(configure.getSubCategory());
            node.setCategoryName(configure.getSubCategoryName());
            node.setCategoryComplete(genValuesKey("|",configure.getCategory() , configure.getMiddleCategory() , configure.getSmallCategory() , configure.getSubCategory()));
            node.setCategoryCompleteName(genValuesKey("|",configure.getCategoryName() , configure.getMiddleCategoryName() , configure.getSmallCategoryName() , configure.getSubCategoryName()));
            return node;
        }).collect(Collectors.toList());
    }

    @Override
    public List<JymlSkuMaxLimitConfigure> getSkuConfigureById(Long id, Integer level, Long storeOrgId) {
        try {
            MdmStoreExDTO mdmStoreExDTO = CacheVar.getStoreExtInfoByStoreOrgId(storeOrgId).orElseThrow(() -> new BusinessErrorException("没有查询到门店的店型"));
            if (StringUtils.isBlank(mdmStoreExDTO.getStoreType())) {
                throw new BusinessErrorException("门店店型不存在");
            }
            OrgInfoBaseCache orgInfoBaseCache = CacheVar.getStoreByOrgId(storeOrgId).orElseThrow(() -> new BusinessErrorException("没有查询到门店的店型"));
            String[] split = mdmStoreExDTO.getStoreType().split("_");
            logger.info("mdmStoreExDTO.getStoreType():{}",mdmStoreExDTO.getStoreType());
            String storeTypePrefix = split[0];
            logger.info("storeTypePrefix:{}", split[0]);
            Long version = getNewVersion(orgInfoBaseCache.getBusinessOrgId());
            JymlSkuMaxLimitConfigure configure = Optional.ofNullable(jymlSkuMaxLimitConfigureMapper.selectByPrimaryKey(id)).orElseThrow(() -> new BusinessErrorException("管控类别不存在"));
            JymlSkuMaxLimitConfigureExample example = new JymlSkuMaxLimitConfigureExample();
            JymlSkuMaxLimitConfigureExample.Criteria criteria = example.createCriteria();
            criteria.andBusinessOrgIdEqualTo(configure.getBusinessOrgId()).andCityEqualTo(configure.getCity()).andVersionEqualTo(version);
            JymlStoreSkuLimitAdjustEffectExample effectExample = new JymlStoreSkuLimitAdjustEffectExample();
            JymlStoreSkuLimitAdjustEffectExample.Criteria effectCriteria = effectExample.createCriteria();
            effectCriteria.andStoreOrgIdEqualTo(storeOrgId);
            if (StringUtils.isNotBlank(configure.getSubCategory())) {
                criteria.andSubCategoryEqualTo(configure.getSubCategory());
                effectCriteria.andSubCategoryEqualTo(configure.getSubCategory());
                criteria.andSmallCategoryEqualTo(configure.getSmallCategory());
                effectCriteria.andSmallCategoryEqualTo(configure.getSmallCategory());
                criteria.andMiddleCategoryEqualTo(configure.getMiddleCategory());
                effectCriteria.andMiddleCategoryEqualTo(configure.getMiddleCategory());
                criteria.andCategoryEqualTo(configure.getCategory());
                effectCriteria.andCategoryEqualTo(configure.getCategory());
            } else if (StringUtils.isNotBlank(configure.getSmallCategory())) {
                criteria.andSubCategoryEqualTo("");
                effectCriteria.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo(configure.getSmallCategory());
                effectCriteria.andSmallCategoryEqualTo(configure.getSmallCategory());
                criteria.andMiddleCategoryEqualTo(configure.getMiddleCategory());
                effectCriteria.andMiddleCategoryEqualTo(configure.getMiddleCategory());
                criteria.andCategoryEqualTo(configure.getCategory());
                effectCriteria.andCategoryEqualTo(configure.getCategory());
            } else if (StringUtils.isNotBlank(configure.getMiddleCategory())) {
                criteria.andSubCategoryEqualTo("");
                effectCriteria.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo("");
                effectCriteria.andSmallCategoryEqualTo("");
                criteria.andMiddleCategoryEqualTo(configure.getMiddleCategory());
                effectCriteria.andMiddleCategoryEqualTo(configure.getMiddleCategory());
                criteria.andCategoryEqualTo(configure.getCategory());
                effectCriteria.andCategoryEqualTo(configure.getCategory());
            } else {
                criteria.andSubCategoryEqualTo("");
                effectCriteria.andSubCategoryEqualTo("");
                criteria.andSmallCategoryEqualTo("");
                effectCriteria.andSmallCategoryEqualTo("");
                criteria.andMiddleCategoryEqualTo("");
                effectCriteria.andMiddleCategoryEqualTo("");
                criteria.andCategoryEqualTo(configure.getCategory());
                effectCriteria.andCategoryEqualTo(configure.getCategory());
            }
            List<JymlSkuMaxLimitConfigure> result = jymlSkuMaxLimitConfigureMapper.selectByExample(example);
            List<JymlStoreSkuLimitAdjustEffect> effects = jymlStoreSkuLimitAdjustEffectMapper.selectByExample(effectExample);
            // 生效调整单存在这个门店的,那么就过滤掉生效的店型 否则过滤id对应的店型
            if (CollectionUtils.isEmpty(effects)) {
                logger.info("没有生效");
                return result.stream().filter(v -> !v.getStoreType().equals(configure.getStoreType())).filter(v -> v.getStoreTypeName().startsWith(storeTypePrefix)).collect(Collectors.toList());
            } else {
                logger.info("有生效");
                return result.stream().filter(v -> !v.getStoreType().equals(effects.get(0).getStoreType())).filter(v -> v.getStoreTypeName().startsWith(storeTypePrefix)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("根据管控类别选择新管控标准失败", e);
            throw e;
        }
    }

    @Override
    public JymlStoreSkuLimitAdjustDTO getAdjustDetails(Long adjustId){
        try {
            JymlStoreSkuLimitAdjust adjust = jymlStoreSkuLimitAdjustMapper.selectByPrimaryKey(adjustId);
            if (null == adjust) {
                throw new BusinessErrorException("单据不存在");
            }
            JymlStoreSkuLimitAdjustDetailExample detailExample = new JymlStoreSkuLimitAdjustDetailExample();
            detailExample.createCriteria().andAdjustIdEqualTo(adjustId);
            JymlStoreSkuLimitAdjustDTO adjustDTO = new JymlStoreSkuLimitAdjustDTO();
            BeanUtils.copyProperties(adjust, adjustDTO);
            adjustDTO.setAdjustStatusDesc(SkuAdjustStatusEnum.getNameByCode(adjust.getAdjustStatus()));
            if (null != adjustDTO.getApproveTime()) {
                adjustDTO.setApproveTime(DateUtils.conventDateStrByDate(adjust.getApproveTime(), DateUtils.DATETIME_PATTERN));
            }
            adjustDTO.setGmtCreate(DateUtils.conventDateStrByDate(adjust.getGmtCreate(), DateUtils.DATETIME_PATTERN));
            adjustDTO.setGmtUpdate(DateUtils.conventDateStrByDate(adjust.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
            adjustDTO.setDetailDTOList(jymlStoreSkuLimitAdjustDetailMapper.selectByExample(detailExample).stream().map(v -> {
                JymlStoreSkuLimitAdjustDetailDTO dto = new JymlStoreSkuLimitAdjustDetailDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setCtrlCategoryName(genValuesKey("|", v.getCategoryName(), v.getMiddleCategoryName(), v.getSmallCategoryName(), v.getSubCategoryName()));
                dto.setLevel(StringUtils.split(dto.getCtrlCategoryName(), "|").length);
                return dto;
            }).collect(Collectors.toList()));
            return adjustDTO;
        } catch (Exception e) {
            logger.error("获取调整明细失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAdjust(SkuAdjustAddParam param, TokenUserDTO userDTO) {
        try {
            if (null == param.getStoreOrgId()) {
                throw new BusinessErrorException("请选择门店");
            }
            if (CollectionUtils.isEmpty(param.getMappings())) {
                throw new BusinessErrorException("请选择调整标准");
            }
            Optional<AdjustConfigrueIdMapping> any = param.getMappings().stream().filter(v ->
                            Objects.isNull(v.getConfigureId())
                                    || Objects.isNull(v.getOldConfigureId())
                                    || v.getConfigureId().equals(v.getOldConfigureId())).findAny();
            if (any.isPresent()) {
                throw new BusinessErrorException("调整标准为空或调整标准与旧标准相同");
            }
            if (!Boolean.TRUE.equals(param.getSkipCheckPerm())) {
                List<OrgTreeSimpleDTO> userDataScopeStore = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), param.getStoreOrgId(), Lists.newArrayList(OrgTypeEnum.STORE.getCode()));
                if (CollectionUtils.isEmpty(userDataScopeStore)) {
                    throw new BusinessErrorException("您没有所选门店的数据权限,请重新选择");
                }
            }
            OrgInfoBaseCache store = CacheVar.getStoreByOrgId(param.getStoreOrgId()).orElseThrow(() -> new BusinessErrorException("没有查询到所选门店"));
            MdmStoreExDTO storeExDTO = CacheVar.getStoreExtInfoByStoreOrgId(param.getStoreOrgId()).orElseThrow(() -> new BusinessErrorException("没有查询到所选门店的店型"));
            JymlSkuMaxLimitConfigureExample configureExample = new JymlSkuMaxLimitConfigureExample();
            configureExample.createCriteria().andIdIn(param.getMappings().stream().map(v -> Lists.newArrayList(v.getOldConfigureId(), v.getConfigureId())).flatMap(Collection::stream).collect(Collectors.toList()));
            Map<Long, JymlSkuMaxLimitConfigure> configureMap = jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample).stream().collect(Collectors.toMap(JymlSkuMaxLimitConfigure::getId, Function.identity(), (k1, k2) -> k1));
            if (MapUtils.isEmpty(configureMap)) {
                throw new BusinessErrorException("没有查询到调整标准信息");
            }
            JymlStoreSkuLimitAdjust adjust;
            if (null == param.getAdjustId()) {
                JymlStoreSkuLimitAdjustExample example = new JymlStoreSkuLimitAdjustExample();
                example.createCriteria().andStoreOrgIdEqualTo(param.getStoreOrgId()).andAdjustStatusIn(Lists.newArrayList(SkuAdjustStatusEnum.NO_COMMIT.getCode(), SkuAdjustStatusEnum.COMMITED.getCode(), SkuAdjustStatusEnum.APPROVING.getCode()));
                if (jymlStoreSkuLimitAdjustMapper.countByExample(example) > 0L) {
                    throw new BusinessErrorException("您选择的门店，有一笔正在审批中的调整记录，请等待审批结果。");
                }
                String adjustOrderNo = noSequenceService.genOrderNo(OrderTypeEnum.SCIB_ADJUST_ORDER.getType(), store.getSapCode(), "_", 3);
                adjust = saveAdjust(adjustOrderNo, store, storeExDTO, userDTO);
            } else {
                JymlStoreSkuLimitAdjustExample example = new JymlStoreSkuLimitAdjustExample();
                example.createCriteria().andStoreOrgIdEqualTo(param.getStoreOrgId()).andIdEqualTo(param.getAdjustId()).andAdjustStatusEqualTo(SkuAdjustStatusEnum.NO_COMMIT.getCode());
                adjust = jymlStoreSkuLimitAdjustMapper.selectByExample(example).stream().findAny().orElseThrow(() -> new BusinessErrorException("您所编辑的单据不存在或已提交审批。"));
            }
            List<JymlStoreSkuLimitAdjustDetail> insertList = new ArrayList<>(param.getMappings().size());
            param.getMappings().forEach(v -> {
                JymlSkuMaxLimitConfigure newConfig = configureMap.get(v.getConfigureId());
                JymlSkuMaxLimitConfigure oldConfig = configureMap.get(v.getOldConfigureId());
                if (null == newConfig || null == oldConfig) {
                    throw new BusinessErrorException("没有查询到调整标准信息");
                }
                insertList.add(genDetail(adjust, oldConfig, newConfig));
            });
            if (null != param.getAdjustId()) {
                // 先删除旧明细
                JymlStoreSkuLimitAdjustDetailExample detailExample = new JymlStoreSkuLimitAdjustDetailExample();
                detailExample.createCriteria().andAdjustIdEqualTo(adjust.getId());
                jymlStoreSkuLimitAdjustDetailMapper.deleteByExample(detailExample);
            }
            Lists.partition(insertList, Constants.INSERT_MAX_VALUE).forEach(batch -> {
                jymlStoreSkuLimitAdjustDetailExtendMapper.batchInsert(batch);
            });
            if (Boolean.TRUE.equals(param.getApproveAble())) {
                sumbitApprove(Lists.newArrayList(adjust.getId()), userDTO);
            }
        } catch (Exception e) {
            logger.error("添加调整记录失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sumbitApprove(List<Long> ids, TokenUserDTO userDTO) {
        try {
            JymlStoreSkuLimitAdjustExample example = new JymlStoreSkuLimitAdjustExample();
            example.createCriteria().andIdIn(ids);
            List<JymlStoreSkuLimitAdjust> adjustList = jymlStoreSkuLimitAdjustMapper.selectByExample(example);
            List<JymlStoreSkuLimitAdjust> approvedList = adjustList.stream().filter(v -> !v.getAdjustStatus().equals(SkuAdjustStatusEnum.NO_COMMIT.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(approvedList)) {
                throw new BusinessErrorException("已提交审核的单据不能再次提交审核");
            }
            Map<Long, JymlStoreSkuLimitAdjust> adjustMap = adjustList.stream().collect(Collectors.toMap(JymlStoreSkuLimitAdjust::getId, Function.identity(), (k1,k2) -> k1));
            JymlStoreSkuLimitAdjustDetailExample detailExample = new JymlStoreSkuLimitAdjustDetailExample();
            detailExample.createCriteria().andAdjustIdIn(ids);
            List<JymlStoreSkuLimitAdjustEffect> effectList = jymlStoreSkuLimitAdjustDetailMapper.selectByExample(detailExample).stream().map(v -> {
                JymlStoreSkuLimitAdjustEffect effect = new JymlStoreSkuLimitAdjustEffect();
                JymlStoreSkuLimitAdjust adjust = adjustMap.get(v.getAdjustId());
                BeanUtils.copyProperties(adjust, effect);
                BeanUtils.copyProperties(v, effect);
                effect.setCreatedBy(userDTO.getUserId());
                effect.setCreatedName(userDTO.getName());
                effect.setUpdatedBy(userDTO.getUserId());
                effect.setUpdatedName(userDTO.getName());
                return effect;
            }).collect(Collectors.toList());
//            JymlStoreSkuLimitAdjustEffectExample effectExample = new JymlStoreSkuLimitAdjustEffectExample();
//            JymlStoreSkuLimitAdjustEffectExample.Criteria criteria = effectExample.createCriteria();
//            criteria.andStoreOrgIdIn(adjustList.stream().map(JymlStoreSkuLimitAdjust::getStoreOrgId).distinct().collect(Collectors.toList()));
//            Set<String> categorySet = new HashSet<>();
//            Set<String> middleCategorySet = new HashSet<>();
//            Set<String> smallCategorySet = new HashSet<>();
//            Set<String> subCategorySet = new HashSet<>();
//            effectList.forEach(v -> {
//                if (StringUtils.isNotBlank(v.getSubCategory())) {
//                    subCategorySet.add(v.getSubCategory());
//                }
//                if (StringUtils.isNotBlank(v.getSmallCategory())) {
//                    smallCategorySet.add(v.getSmallCategory());
//                }
//                if (StringUtils.isNotBlank(v.getMiddleCategory())) {
//                    middleCategorySet.add(v.getMiddleCategory());
//                }
//                categorySet.add(v.getCategory());
//            });
//            if (CollectionUtils.isNotEmpty(subCategorySet)) {
//                criteria.andSubCategoryIn(Lists.newArrayList(subCategorySet));
//            }
//            if (CollectionUtils.isNotEmpty(smallCategorySet)) {
//                criteria.andSmallCategoryIn(Lists.newArrayList(smallCategorySet));
//            }
//            if (CollectionUtils.isNotEmpty(middleCategorySet)) {
//                criteria.andMiddleCategoryIn(Lists.newArrayList(middleCategorySet));
//            }
//            if (CollectionUtils.isNotEmpty(categorySet)) {
//                criteria.andCategoryIn(Lists.newArrayList(categorySet));
//            }
            List<Long> deleteIds = jymlStoreSkuLimitAdjustEffectExtendMapper.selectWithCtrlCategory(adjustList.stream().map(JymlStoreSkuLimitAdjust::getStoreOrgId).distinct().collect(Collectors.toList()), effectList.stream().map(v -> genValuesKey("", v.getCategory(), v.getMiddleCategory(), v.getSmallCategory(), v.getSubCategory())).distinct().collect(Collectors.toList()), new ArrayList<>()).stream().map(JymlStoreSkuLimitAdjustEffect::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                JymlStoreSkuLimitAdjustEffectExample effectExample = new JymlStoreSkuLimitAdjustEffectExample();
                effectExample.createCriteria().andIdIn(deleteIds);
                jymlStoreSkuLimitAdjustEffectMapper.deleteByExample(effectExample);
            }
//            jymlStoreSkuLimitAdjustEffectMapper.deleteByExample(effectExample);
            jymlStoreSkuLimitAdjustEffectExtendMapper.batchInsert(effectList);
            JymlStoreSkuLimitAdjust update = new JymlStoreSkuLimitAdjust();
            update.setAdjustStatus(SkuAdjustStatusEnum.EFFECTED.getCode());
            update.setApproveTime(new Date());
            jymlStoreSkuLimitAdjustMapper.updateByExampleSelective(update, example);
        } catch (Exception e) {
            logger.error("提交失败", e);
            throw e;
        }
    }

    @Override
    public PageResponse<List<JymlStoreSkuLimitAdjustDTO>> getAdjustList(SkuAdjustQueryParam param, TokenUserDTO userDTO) {
        try {
            if(null == param.getPage() || param.getPage() < 0){
                param.setPage(0);
            }
            if(null == param.getPageSize() || param.getPageSize() < 0){
                param.setPageSize(10);
            }
            PageResponse<List<JymlStoreSkuLimitAdjustDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(param.getPage());
            pageResponse.setPageSize(param.getPageSize());
            pageResponse.setTotalSize(0L);
            pageResponse.setResult(new ArrayList<>());
            JymlStoreSkuLimitAdjustExample example = new JymlStoreSkuLimitAdjustExample();
            JymlStoreSkuLimitAdjustExample.Criteria criteria = example.createCriteria();
            if (null != param.getAdjustStatus()) {
                SkuAdjustStatusEnum adjustStatusEnum = SkuAdjustStatusEnum.getEnumByCode(param.getAdjustStatus());
                if (null == adjustStatusEnum) {
                    return pageResponse;
                }
                criteria.andAdjustStatusEqualTo(adjustStatusEnum.getCode());
            }
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), null == param.getPlatformOrgId() ? 3L : param.getPlatformOrgId(), Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTOS)) {
                logger.info("userId:{}, userName:{}没有企业级权限", userDTO.getUserId(), userDTO.getName());
                return pageResponse;
            }
            List<OrgTreeSimpleDTO> companys = orgTreeSimpleDTOS.get(0).getChildren();
            if (CollectionUtils.isEmpty(companys)) {
                logger.info("userId:{}, userName:{}企业级权限为空", userDTO.getUserId(), userDTO.getName());
                return pageResponse;
            }
            Map<Long, OrgTreeSimpleDTO> companyMap = companys.stream().collect(Collectors.toMap(OrgTreeSimpleDTO::getId, Function.identity(), (k1, k2) -> k1));
            if (null != param.getPlatformOrgId()) {
                criteria.andPlatformOrgIdEqualTo(param.getPlatformOrgId());
            }
            if (null != param.getCompanyOrgId()) {
                if (!companyMap.containsKey(param.getCompanyOrgId())) {
                    throw new BusinessErrorException("您没有该企业的查询权限");
                }
                criteria.andCompanyOrgIdEqualTo(param.getCompanyOrgId());
            } else {
                criteria.andCompanyOrgIdIn(Lists.newArrayList(companyMap.keySet()));
            }
            if (CollectionUtils.isNotEmpty(param.getStoreOrgIds())) {
                criteria.andStoreOrgIdIn(param.getStoreOrgIds());
            }
            long count = jymlStoreSkuLimitAdjustMapper.countByExample(example);
            if (count <= 0L) {
                return pageResponse;
            }
            pageResponse.setTotalSize(count);
            example.setLimit(param.getPageSize());
            example.setOffset(Long.valueOf(param.getPage()*param.getPageSize()));
            example.setOrderByClause(" gmt_update desc");
            List<JymlStoreSkuLimitAdjust> adjustList = jymlStoreSkuLimitAdjustMapper.selectByExample(example);
            List<JymlStoreSkuLimitAdjustDTO> dtoList = adjustList.stream().map(v -> {
                JymlStoreSkuLimitAdjustDTO dto = new JymlStoreSkuLimitAdjustDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setAdjustStatusDesc(SkuAdjustStatusEnum.getNameByCode(v.getAdjustStatus()));
                if (null != v.getApproveTime()) {
                    dto.setApproveTime(DateUtils.conventDateStrByDate(v.getApproveTime(), DateUtils.DATETIME_PATTERN));
                }
                dto.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                dto.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                return dto;
            }).collect(Collectors.toList());
            if (!Boolean.TRUE.equals(param.getExportAble())) {
                JymlStoreSkuLimitAdjustDetailExample detailExample = new JymlStoreSkuLimitAdjustDetailExample();
                detailExample.createCriteria().andAdjustIdIn(adjustList.stream().map(JymlStoreSkuLimitAdjust::getId).collect(Collectors.toList()));
                Map<Long, List<JymlStoreSkuLimitAdjustDetail>> detailMap = jymlStoreSkuLimitAdjustDetailMapper.selectByExample(detailExample).stream().collect(Collectors.groupingBy(JymlStoreSkuLimitAdjustDetail::getAdjustId));
                dtoList.forEach(v -> {
                    List<JymlStoreSkuLimitAdjustDetail> detailList = detailMap.get(v.getId());
                    if (CollectionUtils.isNotEmpty(detailList)) {
                        v.setDetailDTOList(detailList.stream().map(detail -> {
                            JymlStoreSkuLimitAdjustDetailDTO dto = new JymlStoreSkuLimitAdjustDetailDTO();
                            BeanUtils.copyProperties(detail, dto);
                            return dto;
                        }).collect(Collectors.toList()));
                    }
                });
            }
            pageResponse.setResult(dtoList);
            return pageResponse;
        } catch (Exception e) {
            logger.error("查询调整记录失败", e);
            throw e;
        }
    }

    @Override
    public void exportAdjustList(TokenUserDTO userDTO, SkuAdjustQueryParam param) {
        try {
            param.setExportAble(true);
            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.SKU_ADJUST_LIST, userDTO, new HandlerDataExportService<JymlStoreSkuLimitAdjustDTO>() {
                @Override
                public List<JymlStoreSkuLimitAdjustDTO> getDataToExport() {
                    return null;
                }

                @Override
                public List<JymlStoreSkuLimitAdjustDTO> getDataToExport(Integer page, Integer pageSize) {
                    param.setPage(page);
                    param.setPageSize(pageSize);
                    return getAdjustList(param, userDTO).getResult();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }
                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return JymlStoreSkuLimitAdjustDTO.getExportMap();
                }
            });

        } catch (Exception e) {
            logger.error("导出调整记录异常", e);
            throw e;
        }

    }

    @Override
    public PageResponse<List<JymlStoreSkuLimitAdjustEffectDTO>> getAdjustEffectList(SkuAdjustQueryParam param, TokenUserDTO userDTO) {
        try {
            PageResponse<List<JymlStoreSkuLimitAdjustEffectDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(param.getPage());
            pageResponse.setPageSize(param.getPageSize());
            pageResponse.setTotalSize(0L);
            pageResponse.setResult(new ArrayList<>());
            JymlStoreSkuLimitAdjustEffectExample example = new JymlStoreSkuLimitAdjustEffectExample();
            JymlStoreSkuLimitAdjustEffectExample.Criteria criteria = example.createCriteria();
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), null == param.getPlatformOrgId() ? 3L : param.getPlatformOrgId(), Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTOS)) {
                logger.info("userId:{}, userName:{}没有企业级权限", userDTO.getUserId(), userDTO.getName());
                return pageResponse;
            }
            List<OrgTreeSimpleDTO> companys = orgTreeSimpleDTOS.get(0).getChildren();
            if (CollectionUtils.isEmpty(companys)) {
                logger.info("userId:{}, userName:{}企业级权限为空", userDTO.getUserId(), userDTO.getName());
                return pageResponse;
            }
            Map<Long, OrgTreeSimpleDTO> companyMap = companys.stream().collect(Collectors.toMap(OrgTreeSimpleDTO::getId, Function.identity(), (k1, k2) -> k1));
            if (null != param.getPlatformOrgId()) {
                criteria.andPlatformOrgIdEqualTo(param.getPlatformOrgId());
            }
            if (null != param.getCompanyOrgId()) {
                if (!companyMap.containsKey(param.getCompanyOrgId())) {
                    throw new BusinessErrorException("您没有该企业的查询权限");
                }
                criteria.andCompanyOrgIdEqualTo(param.getCompanyOrgId());
            } else {
                criteria.andCompanyOrgIdIn(Lists.newArrayList(companyMap.keySet()));
            }
            if (CollectionUtils.isNotEmpty(param.getStoreOrgIds())) {
                criteria.andStoreOrgIdIn(param.getStoreOrgIds());
            }
            if (StringUtils.isNotBlank(param.getCategoryId()) && null != param.getLevel()) {
                if (param.getLevel().equals(4)) {
                    criteria.andSubCategoryEqualTo(param.getCategoryId());
                } else if (param.getLevel().equals(3)) {
                    criteria.andSubCategoryEqualTo("");
                    criteria.andSmallCategoryEqualTo(param.getCategoryId());
                } else if (param.getLevel().equals(2)) {
                    criteria.andSubCategoryEqualTo("");
                    criteria.andSmallCategoryEqualTo("");
                    criteria.andMiddleCategoryEqualTo(param.getCategoryId());
                } else {
                    criteria.andSubCategoryEqualTo("");
                    criteria.andSmallCategoryEqualTo("");
                    criteria.andMiddleCategoryEqualTo("");
                    criteria.andCategoryEqualTo(param.getCategoryId());
                }
            }
            long count = jymlStoreSkuLimitAdjustEffectMapper.countByExample(example);
            if (count <= 0L) {
                return pageResponse;
            }
            pageResponse.setTotalSize(count);
            example.setLimit(param.getPageSize());
            example.setOffset(Long.valueOf(param.getPage()*param.getPageSize()));
            example.setOrderByClause(" gmt_update desc");
            List<JymlStoreSkuLimitAdjustEffect> effectList = jymlStoreSkuLimitAdjustEffectMapper.selectByExample(example);
            List<JymlStoreSkuLimitAdjustEffectDTO> dtoList = genEffectDTO(effectList);
            if (CollectionUtils.isEmpty(dtoList)) {
                logger.info("SKU配置数管理没有数据");
                pageResponse.setTotalSize(0L);
                return pageResponse;
            }
            pageResponse.setResult(dtoList);
            return pageResponse;
        } catch (Exception e) {
            logger.error("查询调整生效记录失败", e);
            throw e;
        }
    }

    private List<JymlStoreSkuLimitAdjustEffectDTO> genEffectDTO(List<JymlStoreSkuLimitAdjustEffect> effectList) {
//        Long version = getNewVersion();
//        if (null == version) {
//            return new ArrayList<>();
//        }
        Set<String> storeTypes = new HashSet<>();
        storeTypes.addAll(effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getOldStoreType).collect(Collectors.toList()));
        storeTypes.addAll(effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getStoreType).collect(Collectors.toList()));
        Map<String, JymlSkuMaxLimitConfigure> configureMap = jymlSkuMaxLimitConfigureExtendMapper.selectWithCtrlCategory(null,
                effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getCompanyOrgId).distinct().collect(Collectors.toList()),
                effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getCity).distinct().collect(Collectors.toList()),
                Lists.newArrayList(storeTypes),
                new ArrayList<>(),
                effectList.stream().map(v -> genValuesKey("", v.getCategory(), v.getMiddleCategory(), v.getSmallCategory(), v.getSubCategory())).distinct().collect(Collectors.toList()),
                new ArrayList<>()).stream().map(v -> {
            if (StringUtils.isBlank(v.getSubCategory())) {
                v.setSubCategory("");
            }
            if (StringUtils.isBlank(v.getSmallCategory())) {
                v.setSmallCategory("");
            }
            if (StringUtils.isBlank(v.getMiddleCategory())) {
                v.setMiddleCategory("");
            }
            return v;
        }).collect(Collectors.toMap(v -> v.getBusinessOrgId() + "-" + v.getCity() + "-" + v.getStoreType() + "-" + v.getCategory() + "-" + v.getMiddleCategory() + "-" + v.getSmallCategory() + "-" + v.getSubCategory(), Function.identity(), (k1, k2) -> k1));
//        JymlSkuMaxLimitConfigureExample configureExample = new JymlSkuMaxLimitConfigureExample();
//        JymlSkuMaxLimitConfigureExample.Criteria criteria = configureExample.createCriteria();
//        criteria.andVersionEqualTo(version).andBusinessOrgIdIn(effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getCompanyOrgId).distinct().collect(Collectors.toList()))
//                .andCityIn(effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getCity).distinct().collect(Collectors.toList()))
//                .andStatusEqualTo(Constants.NORMAL_STATUS)
//                .andStoreTypeIn(Lists.newArrayList(storeTypes))
//                .andCategoryIn(Lists.newArrayList(effectList.stream().map(JymlStoreSkuLimitAdjustEffect::getCategory).distinct().collect(Collectors.toList())));
//        Set<String> middleCategoryList = new HashSet<>();
//        Set<String> smallCategoryList = new HashSet<>();
//        Set<String> subCategoryList = new HashSet<>();
//        effectList.forEach(v -> {
//            if (StringUtils.isNotBlank(v.getMiddleCategory())) {
//                middleCategoryList.add(v.getMiddleCategory());
//            }
//            if (StringUtils.isNotBlank(v.getSmallCategory())) {
//                smallCategoryList.add(v.getSmallCategory());
//            }
//            if (StringUtils.isNotBlank(v.getSubCategory())) {
//                subCategoryList.add(v.getSubCategory());
//            }
//        });
//        if (CollectionUtils.isNotEmpty(subCategoryList)) {
//            criteria.andSubCategoryIn(Lists.newArrayList(subCategoryList));
//        }
//        if (CollectionUtils.isNotEmpty(smallCategoryList)) {
//            criteria.andSmallCategoryIn(Lists.newArrayList(smallCategoryList));
//        }
//        if (CollectionUtils.isNotEmpty(middleCategoryList)) {
//            criteria.andMiddleCategoryIn(Lists.newArrayList(middleCategoryList));
//        }
//        Map<String, JymlSkuMaxLimitConfigure> configureMap = jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample).stream().map(v -> {
//            if (StringUtils.isBlank(v.getSubCategory())) {
//                v.setSubCategory("");
//            }
//            if (StringUtils.isBlank(v.getSmallCategory())) {
//                v.setSmallCategory("");
//            }
//            if (StringUtils.isBlank(v.getMiddleCategory())) {
//                v.setMiddleCategory("");
//            }
//            return v;
//        }).collect(Collectors.toMap(v -> v.getBusinessOrgId() + "-" + v.getCity() + "-" + v.getStoreType() + "-" + v.getCategory() + "-" + v.getMiddleCategory() + "-" + v.getSmallCategory() + "-" + v.getSubCategory(), Function.identity(), (k1, k2) -> k1));
        return effectList.stream().map(v -> {
            JymlStoreSkuLimitAdjustEffectDTO dto = new JymlStoreSkuLimitAdjustEffectDTO();
            BeanUtils.copyProperties(v, dto);
            dto.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
            dto.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
            String oldKey = v.getCompanyOrgId() + "-" + v.getCity() + "-" + v.getOldStoreType() + "-" + v.getCategory() + "-" + v.getMiddleCategory() + "-" + v.getSmallCategory() + "-" + v.getSubCategory();
            String key = v.getCompanyOrgId() + "-" + v.getCity() + "-" + v.getStoreType() + "-" + v.getCategory() + "-" + v.getMiddleCategory() + "-" + v.getSmallCategory() + "-" + v.getSubCategory();
            JymlSkuMaxLimitConfigure oldConfigure = configureMap.get(oldKey);
            JymlSkuMaxLimitConfigure configure = configureMap.get(key);
            if (null != oldConfigure) {
                dto.setOldSkuLowerLimit(oldConfigure.getSkuLowerLimit());
                dto.setOldSkuMaxLimit(oldConfigure.getSkuMaxLimit());
                dto.setOldSkuLimitDesc("(下限" + oldConfigure.getSkuLowerLimit() + "/上限" + oldConfigure.getSkuMaxLimit() + ")");
            }
            if (null != configure) {
                dto.setSkuLowerLimit(configure.getSkuLowerLimit());
                dto.setSkuMaxLimit(configure.getSkuMaxLimit());
                dto.setSkuLimitDesc("(下限" + configure.getSkuLowerLimit() + "/上限" + configure.getSkuMaxLimit() + ")");
            }
            StringBuilder sb = new StringBuilder();
            sb.append(v.getCategoryName());
            if (StringUtils.isNotBlank(v.getMiddleCategoryName())) {
                sb.append("|").append(v.getMiddleCategoryName());
            }
            if (StringUtils.isNotBlank(v.getSmallCategoryName())) {
                sb.append("|").append(v.getSmallCategoryName());
            }
            if (StringUtils.isNotBlank(v.getSubCategoryName())) {
                sb.append("|").append(v.getSubCategoryName());
            }
            dto.setCtrlCategoryName(sb.toString());
            return dto;
        }).collect(Collectors.toList());
    }

    private Long getNewVersion(Long businessOrgId){
        JymlSkuMaxLimitConfigureExample configureExample = new JymlSkuMaxLimitConfigureExample();
        configureExample.createCriteria().andStatusEqualTo(Constants.NORMAL_STATUS).andBusinessOrgIdEqualTo(businessOrgId);
        configureExample.setLimit(1);
        configureExample.setOrderByClause(" id desc");
        jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample);
        return jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample).stream().map(JymlSkuMaxLimitConfigure::getVersion).findAny().orElse(null);
    }
    @Override
    public void exportAdjustEffectList(TokenUserDTO userDTO, SkuAdjustQueryParam param) {
        try {
            param.setExportAble(true);
            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN) + "_" + userDTO.getName() + ".xls";
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.SKU_ADJUST_EFFECT_LIST, userDTO, new HandlerDataExportService<JymlStoreSkuLimitAdjustEffectDTO>() {
                @Override
                public List<JymlStoreSkuLimitAdjustEffectDTO> getDataToExport() {
                    return null;
                }

                @Override
                public List<JymlStoreSkuLimitAdjustEffectDTO> getDataToExport(Integer page, Integer pageSize) {
                    param.setPage(page);
                    param.setPageSize(pageSize);
                    return getAdjustEffectList(param, userDTO).getResult();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }
                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return JymlStoreSkuLimitAdjustEffectDTO.getExportMap();
                }
            });

        } catch (Exception e) {
            logger.error("导出调整生效记录异常", e);
            throw e;
        }
    }

    @Override
    public ImportResult importAdjust(MultipartFile file, TokenUserDTO userDTO) {
        try {
            String key = IMPORT_ADJUST_RECORD + userDTO.getUserId();
            ImportResult importResult = getOperationPermissions(key);
            if (importResult != null) {
                throw new BusinessErrorException("您当前导入任务正在进行中,请稍后重试。");
            }
            checkFile(file);
            ImportResult result = new ImportResult();
            RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
            String s = ExcelCheck.checkExcelPattern(file, new ConfigureAdjustImportData(), CollNameList);
            if (StringUtils.isNotEmpty(s)) {
                throw new BusinessErrorException(s);
            }
            result.setResult(key);
            result.setCode("9999");
            result.setMessage("任务进行中");
            rBucket.set(result, 12, TimeUnit.HOURS);
            try (InputStream in = file.getInputStream()) {
                LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
                List<ConfigureAdjustImportData> excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(ConfigureAdjustImportData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
                asyncTaskExecutor.execute(() -> {
                    dealImportData(excelList, result, key, rBucket, userDTO);
                });
            } catch (Exception e) {
                result.setCode("1");
                result.setMessage("导入门店SKU数配置调整件失败");
                rBucket.set(result, 12, TimeUnit.HOURS);
                logger.error("导入门店SKU数配置调整文件失败", e);
                if (e instanceof BusinessErrorException) {
                    throw new BusinessErrorException("导入行数不能超过" + importNum);
                } else {
                    throw new BusinessErrorException("400", e.getMessage());
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("导入门店SKU数配置调整文件失败", e);
            throw new BusinessErrorException(e.getMessage());
        }
    }

    @Override
    public MdmStoreExDTO getStoreInfoByStoreOrdIds(Long storeOrgId) {
        try {
            JymlStoreSkuLimitAdjustExample example = new JymlStoreSkuLimitAdjustExample();
            example.createCriteria().andStoreOrgIdEqualTo(storeOrgId).andAdjustStatusIn(Lists.newArrayList(SkuAdjustStatusEnum.NO_COMMIT.getCode(), SkuAdjustStatusEnum.COMMITED.getCode(), SkuAdjustStatusEnum.APPROVING.getCode()));
            if (jymlStoreSkuLimitAdjustMapper.countByExample(example) > 0L) {
                throw new BusinessErrorException("您选择的门店，有一笔正在审批中的调整记录，请等待审批结果。");
            }
            MdmStoreExDTO mdmStoreExDTO = CacheVar.getStoreExtInfoByStoreOrgId(storeOrgId).orElseThrow(() -> new BusinessErrorException("没有查询到门店店型"));
            OrgInfoBaseCache store = CacheVar.getStoreByStoreId(mdmStoreExDTO.getStoreId()).orElseThrow(() -> new BusinessErrorException("没有查询到门店"));
            mdmStoreExDTO.setBusinessName(store.getBusinessShortName());
            mdmStoreExDTO.setBusinessId(store.getBusinessId());
            mdmStoreExDTO.setCompanyOrgId(store.getBusinessOrgId());
            mdmStoreExDTO.setComId(store.getBusinessSapCode());
            mdmStoreExDTO.setPlatformOrgId(store.getPlatformOrgId());
            mdmStoreExDTO.setPlatformOrgName(store.getPlatformShortName());
            return mdmStoreExDTO;
        } catch (Exception e) {
            logger.error("获取门店详情失败", e);
            throw e;
        }
    }

    private void dealImportData(List<ConfigureAdjustImportData> excelList, ImportResult result, String key, RBucket<ImportResult> rBucket, TokenUserDTO userDTO) {
        try {
            List<ConfigureAdjustImportData> errorData = new ArrayList<>();
            if (CollectionUtils.isEmpty(excelList)) {
                throw new AmisBadRequestException("导入文件为空");
            }
            List<Long> userScopeCompanyOrgIds = new ArrayList<>();
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), 3L, Lists.newArrayList(OrgTypeEnum.BUSINESS.getCode()));
            if (CollectionUtils.isNotEmpty(orgTreeSimpleDTOS) && CollectionUtils.isNotEmpty(orgTreeSimpleDTOS.get(0).getChildren())) {
                userScopeCompanyOrgIds.addAll(orgTreeSimpleDTOS.get(0).getChildren().stream().map(OrgTreeSimpleDTO::getId).collect(Collectors.toList()));
            }
            logger.info("userScopeCompanyOrgIds:{}", JSON.toJSONString(userScopeCompanyOrgIds));
            // 查询已经存在的
            JymlStoreSkuLimitAdjustExample example = new JymlStoreSkuLimitAdjustExample();
            example.createCriteria().andStoreCodeIn(excelList.stream().map(ConfigureAdjustImportData::getStoreCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList())).andAdjustStatusIn(Lists.newArrayList(SkuAdjustStatusEnum.NO_COMMIT.getCode(), SkuAdjustStatusEnum.COMMITED.getCode(), SkuAdjustStatusEnum.APPROVING.getCode()));
            List<String> existsStoreCodes = jymlStoreSkuLimitAdjustMapper.selectByExample(example).stream().map(JymlStoreSkuLimitAdjust::getStoreCode).collect(Collectors.toList());

            Map<String, MdmStoreExDTO> storeExtMap = new HashMap<>();
            Map<String, OrgInfoBaseCache> storeMap = new HashMap<>();
            Set<String> unique = new HashSet<>();
            Iterator<ConfigureAdjustImportData> iterator = excelList.iterator();
            while (iterator.hasNext()) {
                ConfigureAdjustImportData next = iterator.next();
                if (StringUtils.isBlank(next.getStoreCode())) {
                    next.setErrorReason("门店编码不能为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                Optional<MdmStoreExDTO> storeExOptional = CacheVar.getStoreExtInfoBySapCode(next.getStoreCode());
                if (!storeExOptional.isPresent()) {
                    next.setErrorReason("门店编码所属店型不存在");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(storeExOptional.get().getStoreTypeCode())) {
                    next.setErrorReason("门店店型不存在");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                Optional<OrgInfoBaseCache> storeOpt = CacheVar.getStoreBySapCode(next.getStoreCode());
                if (!storeOpt.isPresent()) {
                    next.setErrorReason("门店编码不存在");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (existsStoreCodes.contains(next.getStoreCode())) {
                    next.setErrorReason("您选择的门店，有一笔正在审批中的调整记录，请等待审批结果。");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (!userScopeCompanyOrgIds.contains(storeOpt.get().getBusinessOrgId())) {
                    next.setErrorReason("您没有该门店权限");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getCtrlCategory())) {
                    next.setErrorReason("商品管控类别不能为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isBlank(next.getStoreType())) {
                    next.setErrorReason("新标准（店型）不能为空");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                int perSize = unique.size();
                unique.add(next.getStoreCode()+next.getCtrlCategory());
                if (unique.size() == perSize) {
                    next.setErrorReason("该行商品管控类别重复");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                storeMap.put(next.getStoreCode(), storeOpt.get());
                storeExtMap.put(next.getStoreCode(), storeExOptional.get());
            }
            if (CollectionUtils.isNotEmpty(excelList)) {
                Map<String, List<ConfigureAdjustImportData>> importMap = excelList.stream().collect(Collectors.groupingBy(ConfigureAdjustImportData::getStoreCode));
                importMap.forEach((k,v) -> {
                    try {
                        dealImportAdjustByStore(storeMap.get(k), storeExtMap.get(k),v, errorData, userDTO);
                    } catch (Exception e) {
                        logger.info("门店:" + k + "导入失败", e);
                        errorData.addAll(v.stream().map(data -> {
                            data.setErrorReason(e.getMessage());
                            return data;
                        }).collect(Collectors.toList()));
                    }
                });
            }
            String fileName = "门店SKU数配置调整导入_" + DateUtils.conventDateStrByDate(new Date()) + ExcelTypeEnum.XLSX.getValue();
            String filePath = "/tmp/" + fileName;
            String fileFileUrl = "";
            if (CollectionUtils.isNotEmpty(errorData)) {
                try {
                    EasyExcel.write(filePath, ConfigureAdjustImportData.class).sheet("sheet1").doWrite(errorData);
                    fileFileUrl = uplodaErrorData(fileName, filePath);
                } catch (Exception e) {
                    logger.error("导入门店SKU数配置调整文件失败错误文件生成文件失败,删除文件", e);
                    throw e;
                } finally {
                    delTempFile(filePath);
                }
            }
            toRedis(rBucket, errorData.size(), result, key, fileFileUrl);
        } catch (Exception e) {
            logger.error("数据处理失败",e);
            result.setCode("1");
            result.setResult(key);
            result.setMessage("数据处理失败!");
            result.setFailFileUrl(null);
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }

    /**
     *
     * @param split
     * @param values
     * @return
     */
    private static String genValuesKey(String split, String... values) {
        if (StringUtils.isBlank(split)) {
            split = "";
        }
        return Arrays.stream(values).filter(StringUtils::isNotBlank).collect(Collectors.joining(split));
    }
    private void dealImportAdjustByStore(OrgInfoBaseCache orgInfoBaseCache, MdmStoreExDTO mdmStoreExDTO, List<ConfigureAdjustImportData> importData, List<ConfigureAdjustImportData> errorData, TokenUserDTO userDTO) {
        Long version = getNewVersion(orgInfoBaseCache.getBusinessOrgId());
        if (null == version) {
            throw new BusinessErrorException("门店SKU数配置为空,请联系管理员");
        }
        String[] split = mdmStoreExDTO.getStoreType().split("_");
        logger.info("mdmStoreExDTO.getStoreType():{}",mdmStoreExDTO.getStoreType());
        String storeTypePrefix = split[0];
        Map<String, JymlStoreSkuLimitAdjustEffect> effectMap = jymlStoreSkuLimitAdjustEffectExtendMapper.selectWithCtrlCategory(Lists.newArrayList(orgInfoBaseCache.getId()), new ArrayList<>(), importData.stream().map(v -> v.getCtrlCategory().replace("|", "")).distinct().collect(Collectors.toList())).stream().collect(Collectors.toMap(v -> v.getStoreCode() + "-" + genValuesKey("|", v.getCategoryName(), v.getMiddleCategoryName() , v.getSmallCategoryName() , v.getSubCategoryName()), Function.identity(), (k1, k2) -> k1));
        logger.info("effectMap:{}", JSON.toJSONString(effectMap));
        Iterator<ConfigureAdjustImportData> iterator = importData.iterator();
        Map<String, String> oldStoreTypeMap = new HashMap<>();
        while (iterator.hasNext()) {
            ConfigureAdjustImportData next = iterator.next();
            if (!next.getStoreType().startsWith(storeTypePrefix)) {
                next.setErrorReason("新标准（店型）与门店原店型不在同一商圈");
                errorData.add(next);
                iterator.remove();
                continue;
            }
            logger.info("next.getStoreCode():{},next.getStoreType():{} next.getCtrlCategory():{}", next.getStoreCode(), next.getStoreType(), next.getCtrlCategory());
            JymlStoreSkuLimitAdjustEffect effect = effectMap.get(next.getStoreCode() + "-" + next.getCtrlCategory());
            logger.info("effect:{}", JSON.toJSONString(effect));
            if (null == effect) {
                if (next.getStoreType().equals(mdmStoreExDTO.getStoreType())) {
                    next.setErrorReason("新标准（店型）与门店原店型相同");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                oldStoreTypeMap.put(next.getCtrlCategory(), mdmStoreExDTO.getStoreType());
            } else {
                if (next.getStoreType().equals(effect.getStoreTypeName())) {
                    next.setErrorReason("新标准（店型）与门店现生效店型相同");
                    errorData.add(next);
                    iterator.remove();
                    continue;
                }
                oldStoreTypeMap.put(next.getCtrlCategory(), effect.getStoreTypeName());
            }
        }
        if (CollectionUtils.isEmpty(importData)) {
            logger.info("门店:{}没有需要处理的数据", orgInfoBaseCache.getSapCode());
            return;
        }
        List<String> storeTypes = importData.stream().map(ConfigureAdjustImportData::getStoreType).distinct().collect(Collectors.toList());
//        JymlSkuMaxLimitConfigureExample configureExample = getJymlSkuMaxLimitConfigureExample(version, orgInfoBaseCache, mdmStoreExDTO, storeTypes, categorySet, subCategorySet, smallCategorySet, middleCategorySet);
//        JymlSkuMaxLimitConfigureExample oldConfigureExample = getJymlSkuMaxLimitConfigureExample(version, orgInfoBaseCache, mdmStoreExDTO, oldStoreTypeMap.values().stream().distinct().collect(Collectors.toList()), categorySet, subCategorySet, smallCategorySet, middleCategorySet);
//        Map<String, JymlSkuMaxLimitConfigure> configureMap = jymlSkuMaxLimitConfigureMapper.selectByExample(configureExample).stream().collect(Collectors.toMap(v -> v.getBusinessOrgId() + "-" + v.getCity() + "-" + v.getStoreTypeName() + "-" + genValuesKey("|", v.getCategoryName(), v.getMiddleCategoryName() , v.getSmallCategoryName() , v.getSubCategoryName()), Function.identity(), (k1, k2) -> k1));
        Map<String, JymlSkuMaxLimitConfigure> configureMap = jymlSkuMaxLimitConfigureExtendMapper.selectWithCtrlCategory(version,
                Lists.newArrayList(orgInfoBaseCache.getBusinessOrgId()),
                Lists.newArrayList(mdmStoreExDTO.getCity()),
                new ArrayList<>(),
                storeTypes,
                new ArrayList<>(),
                importData.stream().map(v -> v.getCtrlCategory().replace("|", "")).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(v -> v.getBusinessOrgId() + "-" + v.getCity() + "-" + v.getStoreTypeName() + "-" + genValuesKey("|", v.getCategoryName(), v.getMiddleCategoryName() , v.getSmallCategoryName() , v.getSubCategoryName()), Function.identity(), (k1, k2) -> k1));
        logger.info("configureMap:{}", JSON.toJSONString(configureMap));
//        Map<String, JymlSkuMaxLimitConfigure> oldConfigureMap = jymlSkuMaxLimitConfigureMapper.selectByExample(oldConfigureExample).stream().collect(Collectors.toMap(v -> v.getBusinessOrgId() + "-" + v.getCity() + "-" + v.getStoreTypeName() + "-" + genValuesKey("|", v.getCategoryName(), v.getMiddleCategoryName() , v.getSmallCategoryName() , v.getSubCategoryName()), Function.identity(), (k1, k2) -> k1));
        Map<String, JymlSkuMaxLimitConfigure> oldConfigureMap = jymlSkuMaxLimitConfigureExtendMapper.selectWithCtrlCategory(version,
                        Lists.newArrayList(orgInfoBaseCache.getBusinessOrgId()),
                        Lists.newArrayList(mdmStoreExDTO.getCity()),
                        new ArrayList<>(),
                        oldStoreTypeMap.values().stream().distinct().collect(Collectors.toList()),
                        new ArrayList<>(),
                        importData.stream().map(v -> v.getCtrlCategory().replace("|", "")).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(v -> v.getBusinessOrgId() + "-" + v.getCity() + "-" + v.getStoreTypeName() + "-" + genValuesKey("|", v.getCategoryName(), v.getMiddleCategoryName() , v.getSmallCategoryName() , v.getSubCategoryName()), Function.identity(), (k1, k2) -> k1));

        logger.info("oldConfigureMap:{}", JSON.toJSONString(oldConfigureMap));
        List<AdjustConfigrueIdMapping> mappings = importData.stream().map(v -> {
            String oldStoreType = oldStoreTypeMap.get(v.getCtrlCategory());
            logger.info("oldConfigKey:{}", orgInfoBaseCache.getBusinessOrgId() + "-" + mdmStoreExDTO.getCity() + "-" + oldStoreType + "-" + v.getCtrlCategory());
            logger.info("configKey:{}", orgInfoBaseCache.getBusinessOrgId() + "-" + mdmStoreExDTO.getCity() + "-" + v.getStoreType() +  "-" + v.getCtrlCategory());
            JymlSkuMaxLimitConfigure oldConfig = oldConfigureMap.get(orgInfoBaseCache.getBusinessOrgId() + "-" + mdmStoreExDTO.getCity() + "-" + oldStoreType + "-" + v.getCtrlCategory());
            JymlSkuMaxLimitConfigure config = configureMap.get(orgInfoBaseCache.getBusinessOrgId() + "-" + mdmStoreExDTO.getCity() + "-" + v.getStoreType() +  "-" + v.getCtrlCategory());
            logger.info("oldConfig:{}", JSON.toJSONString(oldConfig));
            logger.info("config:{}", JSON.toJSONString(config));
            if (null == oldConfig || null == config) {
                v.setErrorReason("商品管控类别或新标准（店型）不存在");
                errorData.add(v);
                return null;
            }
            AdjustConfigrueIdMapping mapping = new AdjustConfigrueIdMapping();
            mapping.setOldConfigureId(oldConfig.getId());
            mapping.setConfigureId(config.getId());
            return mapping;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mappings)) {
            logger.info("门店:{}没有需要导入的数据", mdmStoreExDTO.getStoreNo());
            return;
        }
        SkuAdjustAddParam param = new SkuAdjustAddParam();
        param.setStoreOrgId(orgInfoBaseCache.getId());
        param.setApproveAble(false);
        param.setMappings(mappings);
        param.setSkipCheckPerm(true);
        logger.info("SkuAdjustAddParam:{}", JSON.toJSONString(param));
        createAdjust(param, userDTO);
    }

    private static JymlSkuMaxLimitConfigureExample getJymlSkuMaxLimitConfigureExample(Long version, OrgInfoBaseCache orgInfoBaseCache, MdmStoreExDTO mdmStoreExDTO, List<String> storeTypes, Set<String> categorySet, Set<String> subCategorySet, Set<String> smallCategorySet, Set<String> middleCategorySet) {
        JymlSkuMaxLimitConfigureExample configureExample = new JymlSkuMaxLimitConfigureExample();
        JymlSkuMaxLimitConfigureExample.Criteria criteria = configureExample.createCriteria();
        criteria.andVersionEqualTo(version).andBusinessOrgIdEqualTo(orgInfoBaseCache.getBusinessOrgId())
                .andCityEqualTo(mdmStoreExDTO.getCity())
                .andStatusEqualTo(Constants.NORMAL_STATUS)
                .andStoreTypeNameIn(storeTypes)
                .andCategoryNameIn(Lists.newArrayList(categorySet));
        if (CollectionUtils.isNotEmpty(subCategorySet)) {
            criteria.andSubCategoryNameIn(Lists.newArrayList(subCategorySet));
        }
        if (CollectionUtils.isNotEmpty(smallCategorySet)) {
            criteria.andSmallCategoryNameIn(Lists.newArrayList(smallCategorySet));
        }
        if (CollectionUtils.isNotEmpty(middleCategorySet)) {
            criteria.andMiddleCategoryNameIn(Lists.newArrayList(middleCategorySet));
        }
        return configureExample;
    }

    private void toRedis(RBucket<ImportResult> rBucket, int size,
                         ImportResult result, String key, String fileFileUrl) {
        if (size > 0) {
            result.setCode("1");
            result.setMessage("上传数据已处理完成，存在" + size + "行无效数据，请下载查看。");
            result.setResult(key);
            result.setFailFileUrl(fileFileUrl);
            rBucket.set(result, 12, TimeUnit.HOURS);
        } else {
            result.setCode("0");
            result.setMessage("上传数据全部处理成功。");
            rBucket.set(result, 12, TimeUnit.HOURS);
        }
    }

    private String uplodaErrorData(String fileName, String filePath) {
        File errorFile = new File(filePath);
        String key = fileName;
        String presignatureUrl = "";
        try {
            String url = cosService.multipartUploadFile(key, errorFile);
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, +7);
            presignatureUrl = cosService.getPresignatureUrl(key, calendar.getTime());
        } catch (Exception e) {
            logger.error("分片上传文件失败", e);
        }
        return presignatureUrl;
    }
    private void delTempFile(String filePath) {
        Path path = Paths.get(filePath);
        try {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
                    Files.delete(file);
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException ioException) {
            logger.error("删除文件异常", ioException);
            ioException.printStackTrace();
        }
    }

    private ImportResult getOperationPermissions(String key) {
        RBucket<ImportResult> rBucket1 = redissonClient.getBucket(key);
        if (rBucket1.isExists()) {
            ImportResult importResult = rBucket1.get();
            if (Objects.nonNull(importResult) &&importResult.getCode().equals("9999")){
                return importResult;
            }
        }
        return null;
    }

    private void checkFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }
        //获取文件名+后缀
        String filename = file.getOriginalFilename();
        if (filename != null) {
            //获取其后缀
            String extension = filename.substring(filename.lastIndexOf(".") + 1);
            if (!(extension.equals("xls") || extension.equals("xlsx"))) {
                //此处为自定义异常捕获,可使用其他方式
                throw new BusinessErrorException("文件格式有误,请检查上传文件格式!!");
            }
        }
    }

    private JymlStoreSkuLimitAdjust saveAdjust(String adjustOrderNo, OrgInfoBaseCache store, MdmStoreExDTO storeExDTO, TokenUserDTO userDTO) {
        JymlStoreSkuLimitAdjust adjust = new JymlStoreSkuLimitAdjust();
        adjust.setAdjustCode(adjustOrderNo);
        adjust.setPlatformOrgId(store.getPlatformOrgId());
        adjust.setPlatformName(store.getPlatformShortName());
        adjust.setCompanyOrgId(store.getBusinessOrgId());
        adjust.setBusinessId(store.getBusinessId());
        adjust.setCompanyCode(store.getBusinessSapCode());
        adjust.setCompanyName(store.getBusinessShortName());
        adjust.setProvince(storeExDTO.getProvince());
        adjust.setStoreOrgId(store.getId());
        adjust.setStoreId(store.getOutId());
        adjust.setStoreCode(store.getSapCode());
        adjust.setStoreName(store.getShortName());
        adjust.setCity(storeExDTO.getCity());
        adjust.setStoreType(storeExDTO.getStoreTypeCode());
        adjust.setStoreTypeName(storeExDTO.getStoreType());
        adjust.setAdjustStatus(SkuAdjustStatusEnum.NO_COMMIT.getCode());
        adjust.setCreatedBy(userDTO.getUserId());
        adjust.setCreatedName(userDTO.getName());
        adjust.setUpdatedBy(userDTO.getUserId());
        adjust.setUpdatedName(userDTO.getName());
        jymlStoreSkuLimitAdjustMapper.insertSelective(adjust);
        return adjust;
    }

    private JymlStoreSkuLimitAdjustDetail genDetail(JymlStoreSkuLimitAdjust adjust, JymlSkuMaxLimitConfigure oldConfig, JymlSkuMaxLimitConfigure newConfig){
        JymlStoreSkuLimitAdjustDetail detail = new JymlStoreSkuLimitAdjustDetail();
        BeanUtils.copyProperties(adjust, detail, "id");
        detail.setAdjustId(adjust.getId());
        detail.setCategory(newConfig.getCategory());
        detail.setCategoryName(newConfig.getCategoryName());
        detail.setMiddleCategory(newConfig.getMiddleCategory());
        detail.setMiddleCategoryName(newConfig.getMiddleCategoryName());
        detail.setSmallCategory(newConfig.getSmallCategory());
        detail.setSmallCategoryName(newConfig.getSmallCategoryName());
        detail.setSubCategory(newConfig.getSubCategory());
        detail.setSubCategoryName(newConfig.getSubCategoryName());
        detail.setOldStoreType(oldConfig.getStoreType());
        detail.setOldStoreTypeName(oldConfig.getStoreTypeName());
        detail.setOldSkuMaxLimit(oldConfig.getSkuMaxLimit());
        detail.setOldSkuLowerLimit(oldConfig.getSkuLowerLimit());
        detail.setStoreType(newConfig.getStoreType());
        detail.setStoreTypeName(newConfig.getStoreTypeName());
        detail.setSkuMaxLimit(newConfig.getSkuMaxLimit());
        detail.setSkuLowerLimit(newConfig.getSkuLowerLimit());
        detail.setOldConfigureId(oldConfig.getId());
        detail.setConfigureId(newConfig.getId());
        return detail;
    }

}
