package com.cowell.scib.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13 22:34
 */
@Slf4j
public abstract class BundlTaskStepService<P> {

    @Autowired
    private List<BundlTaskStepHandler> taskStepHandlerList;

    public void processRequest(P p){
        if(p == null){
            return;
        }
        if(CollectionUtils.isEmpty(taskStepHandlerList)){
            return;
        }

        for(BundlTaskStepHandler handler : taskStepHandlerList){

            if(handler == null){
                continue;
            }
            if(handler.isHandler(p)){
                handler.handleRequest(p);
                break;
            }
        }
    }

}
