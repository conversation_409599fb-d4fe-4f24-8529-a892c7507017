package com.cowell.scib.service.impl;

import com.cowell.scib.enums.OrderTypeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.NoSequenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/13 18:19
 */
@Slf4j
@Service
public class NoSequenceServiceImpl implements NoSequenceService {

    @Autowired
    private RedissonClient redissonClient;

    private static final String YYYYMMDD = "yyyyMMdd";

    @Override
    public String taskNoSequence(String type) {
        String date = new LocalDate().toString(YYYYMMDD);
        String redisKey = OrderTypeEnum.getRedisKeyByType(type);
        if (StringUtils.isBlank(redisKey)) {
            throw new BusinessErrorException("500", "业务编码不存在");
        }
        long maxNo = getMaxNo(redisKey, date);
        StringBuilder orderNo = new StringBuilder();
        String maxNoStr = String.format("%03d", maxNo);
        orderNo.append(type).append("-");
        orderNo.append(date).append("-");
        orderNo.append(maxNoStr);

        return orderNo.toString();
    }

    /**
     *
     * @param type
     * @param prefix 前缀
     * @param separator 分隔符
     * @param numSize 序号长度
     */
    @Override
    public String genOrderNo(String type, String prefix, String separator, Integer numSize) {
        String date = new LocalDate().toString(YYYYMMDD);
        String redisKey = OrderTypeEnum.getRedisKeyByType(type);
        if (StringUtils.isBlank(redisKey)) {
            throw new BusinessErrorException("500", "业务编码不存在");
        }
        if (StringUtils.isBlank(separator)) {
            separator = "-";
        }
        long maxNo = getMaxNo(redisKey, date);
        StringBuilder orderNo = new StringBuilder();
        String maxNoStr = String.format("%0"+ numSize + "d", maxNo);
        orderNo.append(type).append(separator);
        orderNo.append(prefix).append(separator);
        orderNo.append(date).append(separator);
        orderNo.append(maxNoStr);
        return orderNo.toString();
    }

    private long getMaxNo(String redisKey, String date) {
        StringBuilder key = new StringBuilder();
        key.append(redisKey);
        key.append("-");
        key.append(date);
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key.toString());
        long maxNo = rAtomicLong.incrementAndGet();
        rAtomicLong.expire(48, TimeUnit.HOURS);
        return maxNo;
    }

}
