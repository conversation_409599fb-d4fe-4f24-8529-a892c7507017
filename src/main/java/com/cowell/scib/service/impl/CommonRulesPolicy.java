package com.cowell.scib.service.impl;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.DicApiEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.CheckService;
import com.cowell.scib.service.IRuleAddService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import com.cowell.scib.service.param.rule.RuleAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class CommonRulesPolicy implements IRuleAddService {


    @Autowired
    private CheckService checkService;

    @Override
    public void check(RuleAddParam ruleAddParam, TokenUserDTO userDTO) {
        RuleDetailDTO ZXCYRXDTO = ruleAddParam.getDetailMap().get(Constants.ZXCY_RX);
        RuleDetailDTO ZXCYOTCDTO = ruleAddParam.getDetailMap().get(Constants.ZXCY_OTC);
        RuleDetailDTO ZYSRYSZYDTO = ruleAddParam.getDetailMap().get(Constants.ZYSR_YSZY);
        RuleDetailDTO ZYSRPFZYDTO = ruleAddParam.getDetailMap().get(Constants.ZYSR_PFZY);
        RuleDetailDTO BJSPDTO = ruleAddParam.getDetailMap().get(Constants.BJSP);
        RuleDetailDTO YLQXDTO = ruleAddParam.getDetailMap().get(Constants.YLQX);

        if (Objects.isNull(ZXCYRXDTO) || Objects.isNull(ZXCYOTCDTO)||Objects.isNull(ZYSRYSZYDTO)||
                Objects.isNull(ZYSRPFZYDTO)||Objects.isNull(ZYSRYSZYDTO)|| Objects.isNull(BJSPDTO)||Objects.isNull(YLQXDTO)) {
            throw new BusinessErrorException(ErrorCodeEnum.INVALID_TOKEN);
        }
        checkNumAdd(ZXCYRXDTO);
        checkNumAdd(ZXCYOTCDTO);
        checkNumAdd(ZYSRYSZYDTO);
        checkNumAdd(ZYSRPFZYDTO);
        checkNumAdd(BJSPDTO);
        checkNumAdd(YLQXDTO);
    }

    private void checkNumAdd(RuleDetailDTO ZXCYRXDTO){
        if (StringUtils.isEmpty(ZXCYRXDTO.getPerprotyValue())){
            throw new BusinessErrorException(ErrorCodeEnum.INVALID_TOKEN);
        }
        List<String> nums = Arrays.asList(ZXCYRXDTO.getPerprotyValue().split(","));
        if (CollectionUtils.isEmpty(nums)){
            throw new BusinessErrorException(ErrorCodeEnum.INVALID_TOKEN);
        }
        for (String num : nums) {
            Boolean upNumber = checkService.isUpNumber(num);
            if (!upNumber){
                throw  new BusinessErrorException("组货通用规则系数比例：值必须是整数");
            }
        }

        int count=10;
        int num= nums.stream().mapToInt(Integer::valueOf).sum();
        if (count!=num){
            throw  new BusinessErrorException("组货通用规则系数比例：整数，0~10之间，每行的系数加和=10");
        }
    }


    @Override
    public String getCode() {
        return DicApiEnum.ZH_NORMAL.getCode();
    }
}
