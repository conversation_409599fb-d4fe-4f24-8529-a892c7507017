package com.cowell.scib.service.impl;

import com.cowell.scib.cache.ohc.OHCDataCacheInstance;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.OHCDataConstants;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.SearchService;
import com.cowell.scib.service.dto.CommonGoodsDTO;
import com.cowell.scib.service.feign.ItemSearchEngineFeignClient;
import com.cowell.scib.service.vo.PageResponse;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewParamVo;
import com.cowell.scib.service.vo.SpuNewVo;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SearchServiceImpl implements SearchService {

    private final Logger logger = LoggerFactory.getLogger(SearchServiceImpl.class);

    private final static String bizCode="CLOUD_POS";

    @Resource
    private ItemSearchEngineFeignClient itemSearchEngineFeignClient;

    @Value("${item.group.goods.OhcCacheTime:72000000}") //默认20小时
    private Long businessGoodsOhcCacheTime;

    @Override
    public Map<String, SpuListVo> getSpuVOMap(List<String> goodsNoList) {
        Map<String, SpuListVo> results = new HashMap<>();
        List<SpuListVo> spuListVoList = new ArrayList<>();
        List<List<String>> partition = Lists.partition(goodsNoList, Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
        for (List<String> stringList : partition) {
            String preKey = OHCDataConstants.GROUP_SPU_GOODS_CACHE_KEY;
            List<SpuListVo> resultList = Lists.newArrayList();
            List<String> unCacheGoodsNos = Lists.newArrayList();
            for (String goodsNo : stringList) {
                String key = preKey + "_" + goodsNo;
                SpuListVo spuCache = OHCDataCacheInstance.groupGoodsOHCDataCache.getCache(key,SpuListVo.class);
                if (Objects.isNull(spuCache)) {
                    unCacheGoodsNos.add(goodsNo);
                }else {
                    resultList.add(spuCache);
                }
            }
            if (CollectionUtils.isNotEmpty(unCacheGoodsNos)) {
                logger.info("集团商品信息 unCacheGoodsNos:{}",unCacheGoodsNos);
                CommonResult<PageResult<SpuListVo>> responseEntity = itemSearchEngineFeignClient.getSupAllList(String.join(",", unCacheGoodsNos),bizCode,Constants.FEIGN_START_PAGE,Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
                if (responseEntity == null || !(responseEntity.getStatus().equals(Constants.INTEGER_ZERO) ||  null == responseEntity.getData()) || CollectionUtils.isEmpty(responseEntity.getData().getRows())){
                    continue;
                }else {
                    resultList.addAll(responseEntity.getData().getRows());
                    for (SpuListVo spuListVo : resultList) {
                        if (Objects.nonNull(spuListVo)) {
                            String key = preKey + "_" + spuListVo.getGoodsNo();
                            OHCDataCacheInstance.groupGoodsOHCDataCache.setCache(key,spuListVo,businessGoodsOhcCacheTime);
                        }
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(resultList)) {
                resultList.forEach(v->{
                    Map<String, String>  comPvMap= comPvToMap(v.getComPv());
                    if (MapUtils.isNotEmpty(comPvMap)){
                        v.setComponent(comPvMap.get("13"));
                    }
                    v.setCategoryId(v.getCategoryId() != null ? v.getCategoryId() : "");
                    spuListVoList.add(v);
                });
                results.putAll(spuListVoList.stream().collect(Collectors.toMap(SpuListVo::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
            }
        }
        return results;
    }
    @Override
    public PageResponse<List<SpuNewVo>> getNewSpuList(SpuNewParamVo spuNewParamVo) {
        try {
            List<SpuNewVo> result = new ArrayList<>();
            PageResponse<List<SpuNewVo>> pageResponse = new PageResponse();
            if (CollectionUtils.isEmpty(spuNewParamVo.getGoodsNoList())){
                pageResponse.setTotalSize(0);
                pageResponse.setResult(result);
                return pageResponse;
            }
            List<List<String>> partition = Lists.partition(spuNewParamVo.getGoodsNoList(), Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
            int totalSize=0;
            ArrayList<SpuNewVo> spuNewVos = new ArrayList<>();
            for (List<String> goodsNoList : partition) {
                String businessId = null;
                if (CollectionUtils.isNotEmpty(spuNewParamVo.getBusinessIdList())) {
                    businessId = spuNewParamVo.getBusinessIdList().stream().map(Object::toString).collect(Collectors.joining(","));
                }else {
                    businessId = Objects.isNull(spuNewParamVo.getBusinessId()) ? null : String.valueOf(spuNewParamVo.getBusinessId());
                }
                String preKey = OHCDataConstants.BUSINESS_GOODS_CACHE_KEY + "_" + businessId;
                List<SpuNewVo> resultList = Lists.newArrayList();
                List<String> unCacheGoodsNos = Lists.newArrayList();
                for (String goodsNo : goodsNoList) {
                    String key = preKey + "_" + goodsNo;
                    SpuNewVo spuNewVoCache = OHCDataCacheInstance.businessGoodsOHCDataCache.getCache(key,SpuNewVo.class);
                    if (Objects.isNull(spuNewVoCache)) {
                        unCacheGoodsNos.add(goodsNo);
                    }else {
                       // logger.info("调用ItemSearchEngine按连锁获取商品信息 命中缓存 ->businessId:{},goodsNo:{}",businessId,goodsNo);
                        resultList.add(spuNewVoCache);
                    }
                }
                if (CollectionUtils.isNotEmpty(unCacheGoodsNos)) {
                    //logger.info("连锁获取商品信息 param ->businessId:{},unCacheGoodsNos:{}",businessId,unCacheGoodsNos);
                    String goodsNo = String.join(",", unCacheGoodsNos);
                    CommonResult<PageResult<SpuNewVo>> responseEntity = itemSearchEngineFeignClient.getNewSpuList4Post(businessId,goodsNo,bizCode,Constants.FEIGN_START_PAGE,Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
                    if (Objects.isNull(responseEntity)  || Objects.isNull(responseEntity.getData()) || CollectionUtils.isEmpty(responseEntity.getData().getRows())){
                        logger.warn("查询商品失败 responseEntity={}",responseEntity.getData());
                        continue;
                    }else {
                        resultList.addAll(responseEntity.getData().getRows());
                        for (SpuNewVo spuNewVo : resultList) {
                            if (Objects.nonNull(spuNewVo)) {
                                String key = preKey + "_" + spuNewVo.getGoodsNo();
                                OHCDataCacheInstance.businessGoodsOHCDataCache.setCache(key,spuNewVo,businessGoodsOhcCacheTime);
                            }
                        }
                    }
                }
                totalSize+=resultList.size();
                spuNewVos.addAll(resultList);
            }
            if(CollectionUtils.isNotEmpty(spuNewVos)) {
                Set<String> resultGoodsNo = spuNewVos.stream().map(SpuNewVo::getGoodsNo).collect(Collectors.toSet());
                List<String> notFindGoodsNo = spuNewParamVo.getGoodsNoList().stream().filter(v -> !resultGoodsNo.contains(v)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(notFindGoodsNo)){
                    logger.warn("连锁获取商品信息 结果 ->businessId:{}, notFindGoodsNo:{} ",spuNewParamVo.getBusinessId(),notFindGoodsNo);
                }
            }else {
                logger.warn("连锁获取商品信息 结果 ->businessId:{}, 所有都没查询到:{} ",spuNewParamVo.getBusinessId(),spuNewParamVo.getGoodsNoList());
            }
            pageResponse.setResult(spuNewVos);
            logger.warn("连锁获取商品信息 结果 ->businessId:{}, spuNewVos.size:{} ",spuNewParamVo.getBusinessId(),spuNewVos.size());
            pageResponse.setTotalSize(totalSize);
            return pageResponse;
        } catch (BusinessErrorException e){
            logger.warn("调用searchapi按连锁获取商品信息失败:", e);
            throw e;
        } catch (Exception e){
            logger.error("调用searchapi按连锁获取商品信息失败:", e);
            throw e;
        }
    }
    /**
     * 获取连锁商品信息 ,商品编码列表必传
     * @param spuNewParamVo
     * @return
     */
    @Override
    public Map<String, SpuNewVo> getNewSpuMap(SpuNewParamVo spuNewParamVo) {
        try {
            Map<String, SpuNewVo> resultMap = new HashMap<>();
            if (Objects.isNull(spuNewParamVo.getPageSize()) || spuNewParamVo.getPageSize() == 10) {
                spuNewParamVo.setPageSize(Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
            }
            List<List<String>> goodsNosPartition = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(spuNewParamVo.getGoodsNoList())){
                goodsNosPartition = Lists.partition(spuNewParamVo.getGoodsNoList(), Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
            }else {
                return new HashMap<>();
            }
            for (List<String> stringList : goodsNosPartition) {
                spuNewParamVo.setTypeData(1);
                spuNewParamVo.setPage(1);
                spuNewParamVo.setGoodsNoList(stringList);
                PageResponse<List<SpuNewVo>> pageResponse = getNewSpuList(spuNewParamVo);
                if(CollectionUtils.isEmpty(pageResponse.getResult())){
                    continue;
                }
                resultMap.putAll(pageResponse.getResult().stream().map(spuNewVo -> {
                    Map<String, String> comPvMap = comPvToMap(spuNewVo.getComPv());
                    spuNewVo.setShortFactoryid(comPvMap.get("61"));
                    return spuNewVo;
                }).collect(Collectors.toMap(SpuNewVo::getGoodsNo, Function.identity(), (k1, k2) -> k1)));
            }
            return resultMap;
        } catch (BusinessErrorException e){
            logger.warn("调用searchapi按连锁获取商品信息失败:", e);
            throw e;
        } catch (Exception e){
            logger.error("调用searchapi按连锁获取商品信息失败:", e);
            throw e;
        }
    }

    @Override
    public CommonGoodsDTO getCommonGoods(SpuListVo spuListVo) {
        CommonGoodsDTO commonGoodsDTO = new CommonGoodsDTO();
        commonGoodsDTO.setGoodsNo(spuListVo.getGoodsNo());
        commonGoodsDTO.setGoodsCommonName(spuListVo.getCurName());
        commonGoodsDTO.setManufacturer(spuListVo.getFactoryid());
        commonGoodsDTO.setDosageForm(spuListVo.getDosageformsid());
        commonGoodsDTO.setGoodsUnit(spuListVo.getGoodsunit());
        commonGoodsDTO.setSpecifications(spuListVo.getJhiSpecification());
        commonGoodsDTO.setBarCode(spuListVo.getBarCode());
        commonGoodsDTO.setGoodsName(spuListVo.getName());
        commonGoodsDTO.setDescription(spuListVo.getDescription());
        commonGoodsDTO.setApprovalNumber(spuListVo.getApprdocno());
        return commonGoodsDTO;
    }


    /**
     * comPv文本转换为map
     * @param comPv
     * @return
     */
    private Map<String, String> comPvToMap(String comPv) {
        Map<String, String> itemMap = new HashMap<>();
        if (StringUtils.isNotBlank(comPv)) {
            String[] items = comPv.split(";");
            Lists.newArrayList(items).stream().filter(Objects::nonNull).forEach(item -> {
                String[] itemArr = item.split(":");
                if (itemArr!=null && itemArr.length==2) {
                    itemMap.put(itemArr[0], itemArr[1]);
                }
            });
        }
        return itemMap;
    }


}
