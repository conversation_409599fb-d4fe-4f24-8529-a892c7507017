package com.cowell.scib.service.impl;

import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.AmisBusinessException;
import com.cowell.scib.service.TocService;
import com.cowell.scib.service.feign.TocFeignClient;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/30 14:37
 */
@Slf4j
@Service
public class TocServiceImpl implements TocService {
    @Resource
    private TocFeignClient tocFeignClient;

    @Override
    public List<Long> getDistributedIDList(String biz, int count) {

        if (count < 1) {
            log.error("<===[TocServiceImpl.getDistributedIDList]  count不能小于1");
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR);
        }
        List<Long> allIds = new ArrayList<>();
        //每次调用toc的最大个数
        int pageSize = 5000;
        List<Long> pageDistributeIds = Collections.emptyList();
        int hasObtainCount = 0;
        while(hasObtainCount < count) {
            int toObtainCount = hasObtainCount + pageSize <= count ? pageSize : count - hasObtainCount ;
            ResponseEntity<List<Long>> responseEntity;
            try {
                responseEntity = tocFeignClient.getNextIdBatch(biz, toObtainCount);
            } catch (Exception e) {
                log.error("<===[TocServiceImpl.getDistributedIDList] error:{} ", Throwables.getStackTraceAsString(e));
                throw new AmisBadRequestException(ErrorCodeEnum.CALL_TOC_ERROR);
            }
            if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new AmisBusinessException(ErrorCodeEnum.CALL_TOC_ERROR);
            }
            pageDistributeIds = responseEntity.getBody();
            if (CollectionUtils.isNotEmpty(pageDistributeIds)) {
                hasObtainCount += pageDistributeIds.size();
                allIds.addAll(pageDistributeIds);
            }
        }

        log.info("TocServiceImpl|getDistributedIDList|biz:{}, count:{} min:{}, max:{}", biz, count,
                Collections.min(allIds), Collections.max(allIds));
        return allIds;
    }
}
