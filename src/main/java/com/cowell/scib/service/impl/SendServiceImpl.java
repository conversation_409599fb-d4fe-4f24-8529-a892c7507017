package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.service.SendService;
import com.cowell.scib.service.dto.BdpResponseDTO;
import com.cowell.scib.service.dto.BundlTaskBdpDTO;
import com.cowell.scib.service.dto.rule.RuleDataBaseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/20 18:20
 */
@Slf4j
@Service
public class SendServiceImpl implements SendService {

    @Autowired
    @Qualifier("vanillaRestTemplate")
    private RestTemplate restTemplate;

    @Value("${scib.task.bdp.url:}")
    private String taskUrl;

    @Value("${scib.adjust.task.bdp.url:}")
    private String adjustTaskUrl;
    @Value("${scib.defective.task.bdp.url:https://bdp-stage.gaojihealth.cn/v1/groupCargoTask/analysisGroupCargoInfoRaw}")
    private String defectiveTaskUrl;

    @Override
    public ResponseEntity<BdpResponseDTO> sendBdp(BundlTaskBdpDTO bundlTaskBdpDTO) {
        if(Objects.isNull(bundlTaskBdpDTO)){
            return null;
        }
        return send(JSON.toJSONString(bundlTaskBdpDTO), taskUrl);
    }
    @Override
    public ResponseEntity<BdpResponseDTO> sendAdjustBdp(BundlTaskBdpDTO bundlTaskBdpDTO) {
        if(Objects.isNull(bundlTaskBdpDTO)){
            return null;
        }
        return send(JSON.toJSONString(bundlTaskBdpDTO), adjustTaskUrl);
    }

    @Override
    public ResponseEntity<BdpResponseDTO> sendDefectiveBdp(RuleDataBaseDTO ruleDataBaseDTO) {
        if(Objects.isNull(ruleDataBaseDTO)){
            return null;
        }
        return send(JSON.toJSONString(ruleDataBaseDTO), defectiveTaskUrl);
    }

    private ResponseEntity<BdpResponseDTO> send(String requestParam, String url) {
        try {
            log.info("SendServiceImpl|requestParam:{}.", requestParam);
            HttpHeaders requestHeaders = new HttpHeaders();

            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            requestHeaders.setContentType(type);
            requestHeaders.add("Accept", MediaType.APPLICATION_JSON.toString());

            HttpEntity<JSONObject> requestEntity = new HttpEntity(requestParam, requestHeaders);
            log.info("SendServiceImpl|url:{}.", url);
            ResponseEntity<BdpResponseDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, BdpResponseDTO.class);
            log.info("SendServiceImpl|responseEntity:{}.", responseEntity);
            return responseEntity;
        }catch (Exception e){
            log.warn("SendServiceImpl|warn.", e);
            throw e;
        }
    }
}
