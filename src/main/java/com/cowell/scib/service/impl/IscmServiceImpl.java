package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.IscmService;
import com.cowell.scib.service.dto.CommonCategoryDTO;
import com.cowell.scib.service.dto.IscmGoodsCategoryDTO;
import com.cowell.scib.service.dto.SensitiveWarnSaveDTO;
import com.cowell.scib.service.dto.iscm.DailySalesParam;
import com.cowell.scib.service.dto.iscm.DailySalesResponse;
import com.cowell.scib.service.dto.iscm.GoodsInfoParams;
import com.cowell.scib.service.dto.iscm.GoodsInfoResponse;
import com.cowell.scib.service.feign.IscmFeignClient;
import com.cowell.scib.service.vo.CommonResponse;
import com.cowell.scib.service.vo.amis.PageResult;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class IscmServiceImpl implements IscmService {

    private final Logger logger = LoggerFactory.getLogger(IscmServiceImpl.class);

    @Autowired
    private IscmFeignClient iscmFeignClient;
    @Override
    public List<IscmGoodsCategoryDTO> getIscmGoodsCategoryListByIds(List<Long> categoryIds) {
        try {
            logger.info("调用iscm根据商品分类id获取商品分类详情 goodsNoList -> {}", JSON.toJSONString(categoryIds));
            ResponseEntity<List<IscmGoodsCategoryDTO>> responseEntity = iscmFeignClient.getIscmGoodsCategoryListByIds(categoryIds);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                    throw new BusinessErrorException("没有获取到商品分类详情信息");
                }
            return responseEntity.getBody();
        } catch (BusinessErrorException e){
            logger.warn("调用iscm根据商品分类id获取商品分类详情失败:", e);
            throw e;
        } catch (Exception e){
            logger.error("调用iscm根据商品分类id获取商品分类详情失败:", e);
            throw e;
        }

    }

    /**
     * 根据子类id获取四级类目信息
     * @param subCategoryIds
     * @return
     */
    @Override
    public Map<Long, CommonCategoryDTO> getCategoryBySubIds(List<Long> subCategoryIds) {
        logger.info("subCategoryIds:{}", JSON.toJSONString(subCategoryIds));
        Set<Long> categoryIdSet = new HashSet<>();
        // key 子类Id value 父级分类id
        Map<Long, List<Long>> categoryIdMap = new HashMap<>();
        subCategoryIds.forEach(v -> {
            Set<Long> set = new HashSet<>();
            String cateStr = v.toString();
            set.add(Long.valueOf(cateStr.substring(0,2)));
            set.add(Long.valueOf(cateStr.substring(0,4)));
            set.add(Long.valueOf(cateStr.substring(0,6)));
            set.add(v);
            categoryIdSet.addAll(set);
            categoryIdMap.put(v, Lists.newArrayList(set).stream().sorted().collect(Collectors.toList()));
        });
        Map<Long, IscmGoodsCategoryDTO> categoryMap = getIscmGoodsCategoryListByIds(Lists.newArrayList(categoryIdSet)).stream().collect(Collectors.toMap(v -> v.getCategoryId(), Function.identity(), (k1, k2) -> k1));
        Map<Long, CommonCategoryDTO> resultMap = new HashMap<>();
        categoryIdMap.forEach((k,v) -> {
            v = v.stream().sorted().collect(Collectors.toList());
            CommonCategoryDTO commonCategoryDTO = new CommonCategoryDTO();
            IscmGoodsCategoryDTO category = categoryMap.get(v.get(0));
            IscmGoodsCategoryDTO middleCategory = categoryMap.get(v.get(1));
            IscmGoodsCategoryDTO smallCategory = categoryMap.get(v.get(2));
            IscmGoodsCategoryDTO subCategory = categoryMap.get(v.get(3));
            // 返回所有类目信息才加入
            if (Objects.nonNull(category) && Objects.nonNull(middleCategory) && Objects.nonNull(smallCategory) && Objects.nonNull(subCategory)) {
                commonCategoryDTO.setCategoryId(category.getCategoryId());
                commonCategoryDTO.setCategoryName(category.getCategoryName());
                commonCategoryDTO.setMiddleCategoryId(middleCategory.getCategoryId());
                commonCategoryDTO.setMiddleCategoryName(middleCategory.getCategoryName());
                commonCategoryDTO.setSmallCategoryId(smallCategory.getCategoryId());
                commonCategoryDTO.setSmallCategoryName(smallCategory.getCategoryName());
                commonCategoryDTO.setSubCategoryId(subCategory.getCategoryId());
                commonCategoryDTO.setSubCategoryName(subCategory.getCategoryName());
                resultMap.put(k, commonCategoryDTO);
            }
        });
        logger.info("resultMap:{}", JSON.toJSONString(resultMap));
        return resultMap;
    }

    @Override
    public SensitiveWarnSaveDTO getSensitivewarnConfig(String paramUniqueMark) {
        try {
            logger.info("调用iscm根据参数唯一编码获取参数 paramUniqueMark -> {}", paramUniqueMark);
            ResponseEntity<SensitiveWarnSaveDTO> responseEntity = iscmFeignClient.getSensitivewarnConfig(paramUniqueMark);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException("没有获取到参数详情信息");
            }
            return responseEntity.getBody();
        } catch (Exception e) {
            logger.error("调用iscm根据参数唯一编码获取参数失败:", e);
            throw e;
        }
    }


    @Override
    public GoodsInfoResponse getParamGoodsInfos(GoodsInfoParams params) {
        try {
            logger.info("调用iscm根据商品分类id获取商品分类详情 goodsNoList -> {}", JSON.toJSONString(params));
            ResponseEntity<CommonResponse<GoodsInfoResponse>> responseEntity = iscmFeignClient.getParamGoodsInfos(params);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.BAD_REQUEST)) {
                   return null;
                }
                throw new BusinessErrorException("没有获取到商品分类详情信息");
            }

            return responseEntity.getBody().getResult();
        } catch (BusinessErrorException e){
            logger.warn("调用iscm根据商品分类id获取商品分类详情失败:", e);
            throw e;
        } catch (Exception e){
            logger.error("调用iscm根据商品分类id获取商品分类详情失败:", e);
            throw e;
        }

    }

    /**
     * 根据门店与商品列表获取日销列表
     * @param dailySalesParam
     * @return
     */
    @Override
    public Map<String, DailySalesResponse> getAvgSalesList(DailySalesParam dailySalesParam) {
        try {
            logger.info("根据门店与商品列表获取日销列表 dailySalesParam -> {}", JSON.toJSONString(dailySalesParam));
            ResponseEntity<List<DailySalesResponse>> responseEntity = iscmFeignClient.getAvgSalesList(dailySalesParam);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException("根据门店与商品列表获取日销列表");
            }
            return responseEntity.getBody().stream().collect(Collectors.toMap(DailySalesResponse::getGoodsNo, Function.identity(), (k1, k2) -> k1));
        } catch (BusinessErrorException e){
            logger.warn("根据门店与商品列表获取日销列表:", e);
        } catch (Exception e){
            logger.error("根据门店与商品列表获取日销列表:", e);
        }
        return new HashMap<>();
    }
}
