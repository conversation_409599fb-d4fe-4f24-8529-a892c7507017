package com.cowell.scib.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.*;
import com.cowell.scib.entityTidb.*;
import com.cowell.scib.enums.*;
import com.cowell.scib.mapperDgms.*;
import com.cowell.scib.mapperDgms.extend.*;
import com.cowell.scib.mapperTidb.*;
import com.cowell.scib.mapperTidb.extend.*;
import com.cowell.scib.mq.producer.SixLevelNecessaryCheckProducer;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.service.*;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.TrackResult.*;
import com.cowell.scib.service.listener.LimitExcelReadListener;
import com.cowell.scib.service.param.BundlTaskListParam;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.param.TaskNecessaryPlatformParam;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.ExcelCheck;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class TrackResultAdjustServiceImpl implements TrackResultAdjustService {

    private final Logger logger = LoggerFactory.getLogger(TrackResultAdjustServiceImpl.class);


    @Autowired
    private TrackRetultAllDetailExtendMapper trackRetultAllDetailExtendMapper;


    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;

    @Autowired
    private TaskNecessaryPlatformGoodsExtendMapper taskNecessaryPlatformGoodsExtendMapper;


    @Autowired
    private TaskNecessaryStoreTypeGoodsMapper taskNecessaryStoreTypeGoodsMapper;

    @Autowired
    private TaskNecessarySingleStoreGoodsMapper taskNecessarySingleStoreGoodsMapper;

    @Autowired
    private TaskNecessaryChooseStoreTypeGoodsMapper taskNecessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private TaskNecessaryChooseStoreTypeGoodsExtendMapper taskNecessaryChooseStoreTypeGoodsExtendMapper;

    @Autowired
    private ForestService forestService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private ConfigOrgExtendMapper configOrgExtendMapper;

    @Autowired
    private ConfigOrgDetailExMapper configOrgDetailExMapper;

    @Autowired
    private TrackResultFileMapper trackResultFileMapper;

    @Autowired
    private TaskNecessaryCompanyGoodsExtendMapper taskNecessaryCompanyGoodsExtendMapper;

    @Autowired
    private TaskNecessaryStoreTypeGoodsExtendMapper taskNecessaryStoreTypeGoodsExtendMapper;

    @Autowired
    private TaskNecessaryPlatformGoodsMapper taskNecessaryPlatformGoodsMapper;

    @Autowired
    private TaskNecessaryCompanyGoodsMapper taskNecessaryCompanyGoodsMapper;

    @Autowired
    private TaskNecessarySingleStoreGoodsExtendMapper taskNecessarySingleStoreGoodsExtendMapper;

    @Autowired
    private SixLevelNecessaryCheckProducer sixLevelNecessaryCheckProducer;

    @Autowired
    private AsyncImportTrackResultGoodsConfig asyncImportTrackResultGoodsConfig;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private BundlingTaskStoreDetailExtendMapper bundlingTaskStoreDetailExtendMapper;

    @Autowired
    private StoreService storeService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private NecessaryPlatformGoodsMapper necessaryPlatformGoodsMapper;

    @Autowired
    private NecessaryPlatformGoodsExtendMapper necessaryPlatformGoodsExtendMapper;

    @Autowired
    private NecessaryCompanyGoodsMapper necessaryCompanyGoodsMapper;

    @Autowired
    private NecessaryCompanyGoodsExtendMapper necessaryCompanyGoodsExtendMapper;

    @Autowired
    private NecessaryStoreTypeGoodsMapper necessaryStoreTypeGoodsMapper;

    @Autowired
    private NecessaryStoreTypeGoodsExtendMapper necessaryStoreTypeGoodsExtendMapper;

    @Autowired
    private NecessaryChooseStoreTypeGoodsMapper necessaryChooseStoreTypeGoodsMapper;

    @Autowired
    private NecessaryChooseStoreTypeGoodsExtendMapper necessaryChooseStoreTypeGoodsExtendMapper;

    @Autowired
    private NecessarySingleStoreGoodsMapper necessarySingleStoreGoodsMapper;

    @Autowired
    private NecessarySingleStoreGoodsExtendMapper necessarySingleStoreGoodsExtendMapper;

    @Autowired
    private BundlingTaskInfoExtendMapper bundlingTaskInfoExtendMapper;

    @Autowired
    private TrackRetultLevelReviewMapper trackRetultLevelReviewMapper;

    @Autowired
    private TrackRetultCompositionReviewMapper trackRetultCompositionReviewMapper;

    @Autowired
    private TrackRetultEfficiencyAnalyseMapper trackRetultEfficiencyAnalyseMapper;

    @Autowired
    private TrackRetultTop4levelStoregroupExtendMapper trackRetultTop4levelStoregroupExtendMapper;

    @Autowired
    private TrackRetultTop4levelStoregroupMapper trackRetultTop4levelStoregroupMapper;

    @Autowired
    private TrackResultLevelNecessaryMapper trackResultLevelNecessaryMapper;

    @Autowired
    private TrackResultLevelNecessaryExtendMapper trackResultLevelNecessaryExtendMapper;

    @Autowired
    private TrackRetultAllDetailMapper trackRetultAllDetailMapper;

    private static final String BUSINESS_SCOPE_CACHE = "BUSINESS-SCOPE-CACHE-";

    @Autowired
    @Qualifier("trackResultTaskExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Value("${scib.easyexcel.import.num:10000}")
    private Integer importNum;

    @Autowired
    private SendService sendService;

    @Value("${scib.bundltask.max.version:2}")
    private Integer maxVersion;

    @Value("${scib.task.bdp.environment:300}")
    private int environment;

    /**
     * Excel导入商品时使用的key
     */
    public static final String STORE_TYPE_GOODS_DEL_IMPORT ="SCIB_STORE_TYPE_GOODS_DEL_IMPORT:%s" ;

    /**
     * Excel导入商品时使用的key
     */
    public static final String NEW_STORE_GOODS_IMPORT ="SCIB_NEW_STORE_GOODS_IMPORT:%s" ;

    public static List<String> CollNameList=Lists.newArrayList("错误原因");

    @Override
    public ImportResult importStoreTypeDelGoods(MultipartFile file, Long taskId, TokenUserDTO userDTO) throws Exception {
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }

        checkFile(file);

        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new StoreTypeNecessaryData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }

        BundlingTaskInfo bundlingTaskInfo = getBundlingTaskInfo(taskId);
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result, 12, TimeUnit.HOURS);
        List<StoreTypeNecessaryData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(StoreTypeNecessaryData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.delStoreTypeGoods(excelList, taskId, bundlingTaskInfo, finalResult, key, rBucket);
            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            logger.error("异常",e);
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
         return result;
    }

    private ImportResult getOperationPermissions(String key) {
        RBucket<ImportResult> rBucket1 = redissonClient.getBucket(key);
        if (rBucket1.isExists()) {
            ImportResult importResult = rBucket1.get();
            if (Objects.nonNull(importResult) &&importResult.getCode().equals("9999")){
                return importResult;
            }
        }
        return null;
    }

    private BundlingTaskInfo getBundlingTaskInfo(Long taskId) {
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(bundlingTaskInfo)) {
            throw new BusinessErrorException(ErrorCodeEnum.TASK_ID_NOTEXIT.getMsg());
        }
        return bundlingTaskInfo;
    }

    @Override
    public ImportResult importSingleStoreDelGoods(MultipartFile file, Long taskId, TokenUserDTO userDTO) throws Exception {
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        checkFile(file);
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new SingleStoreNecessaryData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result,12, TimeUnit.HOURS);
        List<SingleStoreNecessaryData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(SingleStoreNecessaryData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.delSingleStoreGoods(excelList,taskId, key, rBucket, finalResult);
            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }


    @Override
    public ImportResult importPlatformGoods(MultipartFile file, Long taskId, Byte taskType, TokenUserDTO userDTO) throws Exception {
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        checkFile(file);
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new PlatformGoodsData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result,12, TimeUnit.HOURS);
        if (BundlTaskTypeEnum.STORE_BUNDL.getCode().equals(taskType) || BundlTaskTypeEnum.BUSINESS_BUNDL.getCode().equals(taskType)) {
            result.setCode("1");
            result.setMessage("导入文件类型和导入数据不符");
            rBucket.set(result,12, TimeUnit.HOURS);
            return getImportResult();
        }
        List<PlatformGoodsData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(PlatformGoodsData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.platformGoods(excelList,taskId, finalResult, key, rBucket);
            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }

    @Override
    public ImportResult importCompanyGoods(MultipartFile file, Long taskId, Byte taskType, TokenUserDTO userDTO) throws Exception {
        checkFile(file);
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new CompanyGoodsData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result,12, TimeUnit.HOURS);
        if (BundlTaskTypeEnum.STORE_BUNDL.getCode().equals(taskType)) {
            return getImportResult();
        }
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(bundlingTaskInfo)){
            throw new BusinessErrorException(ErrorCodeEnum.TASK_ID_NOTEXIT.getMsg());
        }
        List<CompanyGoodsData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(CompanyGoodsData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.companyGoods(excelList,taskId, bundlingTaskInfo, finalResult, key, rBucket);

            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }


    private ImportResult getImportResult() {
        ImportResult importResult = new ImportResult();
        importResult.setCode("1");
        importResult.setMessage("导入文件类型和导入数据不符");
        return new ImportResult();
    }



    @Override
    public ImportResult importStoreTypeGoods(MultipartFile file, Long taskId, TokenUserDTO userDTO) throws Exception {
        checkFile(file);
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new StoreTypeGoodsData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result,12, TimeUnit.HOURS);
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        List<StoreTypeGoodsData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(StoreTypeGoodsData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.storeTypeGoods(excelList,taskId, bundlingTaskInfo, finalResult, key, rBucket);

            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }


    @Override
    public ImportResult importSingleStoreGoods(MultipartFile file, Long taskId, TokenUserDTO userDTO) throws Exception {
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        checkFile(file);
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new SingleStoreNecessaryData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result,12, TimeUnit.HOURS);
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        List<SingleStoreNecessaryData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(SingleStoreNecessaryData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.singleStoreGoods(excelList,taskId, bundlingTaskInfo, finalResult, key, rBucket);

            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                logger.info("数据行数是超过10000行");
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }

    @Override
    public void updateMDMData(Long taskId,Byte taskType,Integer version, TokenUserDTO userDTO) {
        BundlingTaskInfoExample example = new BundlingTaskInfoExample();
        example.createCriteria().andIdEqualTo(taskId).andVersionEqualTo(version);
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByExample(example).stream().findAny().orElseThrow(() -> new BusinessErrorException("任务版本不匹配"));
        if (!bundlingTaskInfo.getTaskStatus().equals(BundlTaskStatusEnum.COMPUTED.getCode())){
            throw new BusinessErrorException("任务状态已变化,请返回列表查看");
        }
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        ImportResult result = new ImportResult();
        result.setResult(key);
        result.setCode("9999");
        result.setMessage("任务进行中");
        RBucket<ImportResult> rBucket1 = redissonClient.getBucket(key);
        rBucket1.set(result, 12, TimeUnit.HOURS);

        asyncTaskExecutor.execute(() -> {
            try {
                String finishedKey = RedisConstant.NECESSARY_ADD_STORE_GOODS_CACHE_KEY +taskId;
                RAtomicLong atomicLong = redissonClient.getAtomicLong(finishedKey);
                atomicLong.set(Constants.ZERO);
                String redisKey = RedisConstant.NECESSARY_FINISHED_ADD_STORE_GOODS_CACHE_KEY + taskId;
                RBucket<String> finishBucket = redissonClient.getBucket(redisKey);
                finishBucket.set(Constants.NECESSARY_DATA_VERSION_ZERO,1,TimeUnit.DAYS);
                logger.info("atomicLong初始值:{}",atomicLong.get());
                List<BundlingTaskStoreDetail> bundlingTaskStoreDetails = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);
                List<String> storeCodeList = bundlingTaskStoreDetails.stream().filter(v->v.getBundlAdviceAble().equals(BundlBoolEnum.YES.getCode())).map(BundlingTaskStoreDetail::getStoreCode).collect(Collectors.toList());
                if (taskType.equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())){
                    //平台必备
                    platformGoods(taskId, userDTO);
                    //企业必备
                    necessaryCompanyGoods(bundlingTaskInfo,userDTO);
                    //店型必备
                    necessaryStoreTypeGoods(taskId, bundlingTaskInfo,userDTO);
                    //店型选配
                    necessaryChooseStoreTypeGoods(taskId, bundlingTaskInfo,userDTO);
                    //单店必备
                    necessarySingleStoreGoods(taskId, bundlingTaskInfo,userDTO);

                    //一店一目
                    for (String storeCode : storeCodeList) {
                        TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
                        trackRetultDetailParam.setTaskId(bundlingTaskInfo.getId());
                        trackRetultDetailParam.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
                        trackRetultDetailParam.setOrgNoList(Arrays.asList(storeCode));
                        trackRetultDetailParam.setLevelList(Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()),String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()),
                                String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()),String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()),String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode())));
                        trackRetultDetailParam.setStatus(Constants.NORMAL_STATUS);
                        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
                        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(storeCode);
                        if (Objects.nonNull(mdmStoreExDTO)){
                            List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, mdmStoreExDTO.getBusinessId(), trackRetultAllDetails.stream().map(TrackRetultAllDetail::getGoodsId).collect(Collectors.toList()),mdmStoreExDTO.getStoreId());
                            getStoreIdNotOperatingGoods(trackRetultAllDetails, mdmStoreExDTO, removeBusGoods);
                            pushTsakMdmStoreData(taskId, trackRetultAllDetails,removeBusGoods,userDTO,atomicLong);
                        }else {
                            logger.warn("下发一店一目 storeCode 查询门店信息为空={} 跳过",storeCode);
                            //pushTsakMdmStoreData(taskId, trackRetultAllDetails,null,userDTO,atomicLong);
                        }
                        trackRetultAllDetails.clear();
                    }
                    finishBucket.set(Constants.NECESSARY_DATA_VERSION_ONE,1,TimeUnit.DAYS);
                    logger.info("全层级组货发完mq更新redisSCIB_NECESSARY_FINISHED_ADD_STORE_GOODS_{}的值为1",taskId);

                }else if (taskType.equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())){
                    //企业必备
                    necessaryCompanyGoods(bundlingTaskInfo,userDTO);
                    //店型必备
                    necessaryStoreTypeGoods(taskId, bundlingTaskInfo,userDTO);
                    //店型选配
                    necessaryChooseStoreTypeGoods(taskId, bundlingTaskInfo,userDTO);
                    //单店必备
                    necessarySingleStoreGoods(taskId, bundlingTaskInfo,userDTO);

                    for (String storeCode : storeCodeList) {
                        TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
                        trackRetultDetailParam.setTaskId(bundlingTaskInfo.getId());
                        trackRetultDetailParam.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
                        trackRetultDetailParam.setOrgNoList(Arrays.asList(storeCode));
                        trackRetultDetailParam.setLevelList(Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()),String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()),String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode())));
                        trackRetultDetailParam.setStatus(Constants.NORMAL_STATUS);
                        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
                        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(storeCode);
                        if (Objects.nonNull(mdmStoreExDTO)){
                            List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, mdmStoreExDTO.getBusinessId(), trackRetultAllDetails.stream().map(TrackRetultAllDetail::getGoodsId).collect(Collectors.toList()),mdmStoreExDTO.getStoreId());
                            getStoreIdNotOperatingGoods(trackRetultAllDetails, mdmStoreExDTO, removeBusGoods);
                            pushTsakMdmStoreData(taskId, trackRetultAllDetails,removeBusGoods, userDTO, atomicLong);
                        }else {
                            logger.warn("下发一店一目 storeCode 查询门店信息为空={} 跳过",storeCode);
                            //pushTsakMdmStoreData(taskId, trackRetultAllDetails,null,userDTO,atomicLong);
                        }
                        trackRetultAllDetails.clear();
                    }
                    finishBucket.set(Constants.NECESSARY_DATA_VERSION_ONE,1,TimeUnit.DAYS);
                    logger.info("企业级组货发完mq更新redisSCIB_NECESSARY_FINISHED_ADD_STORE_GOODS_{}的值为1",taskId);

                }else if (taskType.equals(BundlTaskTypeEnum.STORE_BUNDL.getCode())){
                    //店型必备
                    necessaryStoreTypeGoods(taskId, bundlingTaskInfo,userDTO);
                    //店型选配
                    necessaryChooseStoreTypeGoods(taskId, bundlingTaskInfo,userDTO);
                    //单店必备
                    necessarySingleStoreGoods(taskId, bundlingTaskInfo,userDTO);
                    for (String storeCode : storeCodeList) {
                        TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
                        trackRetultDetailParam.setTaskId(bundlingTaskInfo.getId());
                        trackRetultDetailParam.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
                        trackRetultDetailParam.setOrgNoList(Arrays.asList(storeCode));
                        trackRetultDetailParam.setLevelList(Arrays.asList( String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()),String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()),String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode())));
                        trackRetultDetailParam.setStatus(Constants.NORMAL_STATUS);
                        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
                        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(storeCode);
                        if (Objects.nonNull(mdmStoreExDTO)){
                            List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, mdmStoreExDTO.getBusinessId(), trackRetultAllDetails.stream().map(TrackRetultAllDetail::getGoodsId).collect(Collectors.toList()),mdmStoreExDTO.getStoreId());
                            getStoreIdNotOperatingGoods(trackRetultAllDetails, mdmStoreExDTO, removeBusGoods);
                            pushTsakMdmStoreData(taskId, trackRetultAllDetails,removeBusGoods,userDTO,atomicLong);
                        }else {
                            logger.warn("下发一店一目 storeCode 查询门店信息为空={} 跳过",storeCode);
                            //pushTsakMdmStoreData(taskId, trackRetultAllDetails,null,userDTO,atomicLong);
                        }
                        trackRetultAllDetails.clear();
                    }
                    finishBucket.set(Constants.NECESSARY_DATA_VERSION_ONE,1,TimeUnit.DAYS);
                    logger.info("店型级组货发完mq更新redisSCIB_NECESSARY_FINISHED_ADD_STORE_GOODS_{}的值为1",taskId);
                }else {
                    logger.error("下发一店一目 未知的任务类型:{}", taskType);
                    return;
                }
            }catch (Exception e){
                logger.error("下发一店一目逻辑失败:", e);
               // rBucket.delete();
                result.setCode("1");
                result.setMessage("");
                rBucket1.set(result, 12, TimeUnit.HOURS);
            }

        });
    }

    private void getStoreIdNotOperatingGoods(List<TrackRetultAllDetail> trackRetultAllDetails, MdmStoreExDTO mdmStoreExDTO, List<String> removeBusGoods) {
        if (CollectionUtils.isNotEmpty(trackRetultAllDetails)){
            Map<Long, List<String>> longListMap = ruleService.checkExistsUnmanage(Arrays.asList(mdmStoreExDTO.getStoreId()), trackRetultAllDetails.stream().map(TrackRetultAllDetail::getSubCategoryId).collect(Collectors.toList()));
            if (MapUtils.isNotEmpty(longListMap)) {
                List<TrackRetultAllDetail> collect = trackRetultAllDetails.stream().filter(v -> longListMap.get(mdmStoreExDTO.getStoreId()).contains(v.getSubCategoryId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)){
                    List<String> storeIdNotOperatingGoods = collect.stream().map(TrackRetultAllDetail::getGoodsId).distinct().collect(Collectors.toList());
                    logger.info("storeId:{}检查传入的门店和分类, 是否存在于不经营目录:{}", mdmStoreExDTO.getStoreId(),storeIdNotOperatingGoods);
                    removeBusGoods.addAll(storeIdNotOperatingGoods);
                }
            }
        }
    }

    @Override
    public void updateOneStoreOneData(Long taskId,String orgNo, TokenUserDTO userDTO) {
        String finishedKey = RedisConstant.NECESSARY_ADD_STORE_GOODS_CACHE_KEY +taskId;
        RAtomicLong atomicLong = redissonClient.getAtomicLong(finishedKey);
        atomicLong.set(Constants.ZERO);
        String redisKey = RedisConstant.NECESSARY_FINISHED_ADD_STORE_GOODS_CACHE_KEY + taskId;
        RBucket<String> finishBucket = redissonClient.getBucket(redisKey);
        finishBucket.set(Constants.NECESSARY_DATA_VERSION_ZERO,1,TimeUnit.DAYS);
        BundlingTaskInfo bundlingTaskInfo = getBundlingTaskInfo(taskId);
        List<BundlingTaskStoreDetail> bundlingTaskStoreDetails = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId);
        List<String> storeCodeList = bundlingTaskStoreDetails.stream().filter(v->v.getBundlAdviceAble().equals(BundlBoolEnum.YES.getCode())).map(BundlingTaskStoreDetail::getStoreCode).collect(Collectors.toList());
        if (!storeCodeList.contains(orgNo)) {
            throw new BusinessErrorException("组货任务" + taskId + "下" + orgNo + "门店不是组货门店");
        }
        //一店一目
        List<String> levelList = new ArrayList<>();
        if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.PLATE_BUNDL.getCode())) {
            levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.SECEND.getCode()), String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode())
                    , String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()), String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode()));
        } else if (bundlingTaskInfo.getTaskType().equals(BundlTaskTypeEnum.BUSINESS_BUNDL.getCode())) {
            levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.THIRD.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()),
                    String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()), String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode()));
        } else {
            levelList = Arrays.asList(String.valueOf(NecessaryGoodsLevelEnum.FOURTH.getCode()), String.valueOf(NecessaryGoodsLevelEnum.FIFTH.getCode()), String.valueOf(NecessaryGoodsLevelEnum.SIXTH.getCode()));
        }
        TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
        trackRetultDetailParam.setTaskId(taskId);
        trackRetultDetailParam.setPlatOrgid(String.valueOf(bundlingTaskInfo.getOrgId()));
        trackRetultDetailParam.setOrgNoList(Arrays.asList(orgNo));
        trackRetultDetailParam.setLevelList(levelList);
        trackRetultDetailParam.setStatus(Constants.NORMAL_STATUS);
        List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
        MdmStoreExDTO mdmStoreExDTO = CacheVar.storeExMap.get(orgNo);
        if (Objects.nonNull(mdmStoreExDTO)) {
            List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, mdmStoreExDTO.getBusinessId(), trackRetultAllDetails.stream().map(TrackRetultAllDetail::getGoodsId).collect(Collectors.toList()), mdmStoreExDTO.getStoreId());
            getStoreIdNotOperatingGoods(trackRetultAllDetails, mdmStoreExDTO, removeBusGoods);
            pushTsakMdmStoreData(taskId, trackRetultAllDetails, removeBusGoods, userDTO, atomicLong);
        } else {
            pushTsakMdmStoreData(taskId, trackRetultAllDetails, null, userDTO, atomicLong);
        }
        finishBucket.set(Constants.NECESSARY_DATA_VERSION_ONE,1,TimeUnit.DAYS);
        logger.info("全层级组货发完mq更新redisSCIB_NECESSARY_FINISHED_ADD_STORE_GOODS_{}的值为1",taskId);
    }

    private void platformGoods(Long taskId, TokenUserDTO userDTO) {
        String platformFinishedKey = RedisConstant.PUSH_PLATFORM_GOODS +taskId;
        RAtomicLong platformAtomicLong = redissonClient.getAtomicLong(platformFinishedKey);

        String finishedKey = RedisConstant.PUSH_FINISH_PLATFORM_GOODS +taskId;
        RBucket<Object> bucket = redissonClient.getBucket(finishedKey);
        bucket.set(1,3,TimeUnit.HOURS);
        try{
            TaskNecessaryPlatformParam taskNecessaryPlatformParam = new TaskNecessaryPlatformParam();
            taskNecessaryPlatformParam.setTaskId(taskId);
            List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods = taskNecessaryPlatformGoodsExtendMapper.queryNecessaryPlatformGoodList(taskNecessaryPlatformParam);
            List<List<TaskNecessaryPlatformGoods>> tempTaskNecessaryPlatformGoods = Lists.partition(taskNecessaryPlatformGoods, Constants.SEND_NECESSARY_DATA);
            for (int i=0;i<tempTaskNecessaryPlatformGoods.size();i++){
                List<TaskNecessaryPlatformGoods> taskNecessaryPlatformGoods1 = tempTaskNecessaryPlatformGoods.get(i);
                int finalI = i;
                taskNecessaryPlatformGoods1.stream().forEach(v->{
                    v.setNecessaryTag(String.valueOf(NecessaryTagEnum.PLATFORM_NECESSARY.getCode()));
                    if (finalI ==taskNecessaryPlatformGoods1.size()-1){
                        v.setFinishFlag(1);
                    }
                    v.setUpdatedBy(userDTO.getUserId());
                    v.setUpdatedName(userDTO.getName());
                });
                NecessaryGoodsDTO necessaryGoodsDTO = new NecessaryGoodsDTO();
                necessaryGoodsDTO.setNecessaryTag(String.valueOf(NecessaryTagEnum.PLATFORM_NECESSARY.getCode()));
                necessaryGoodsDTO.setTaskNecessaryPlatformGoods(taskNecessaryPlatformGoods1);
                long l = platformAtomicLong.incrementAndGet();
                logger.info("平台锁得值为：{}",l);
                platformAtomicLong.expire(3,TimeUnit.HOURS);
                sixLevelNecessaryCheckProducer.send(JSONObject.toJSONString(necessaryGoodsDTO));
            }

        }catch (Exception e){
            bucket.delete();
            platformAtomicLong.delete();
            logger.error("下发平台数据报错",e);
        }finally {
            platformAtomicLong.set(0);
            bucket.set(0,3,TimeUnit.HOURS);
        }
    }

    private void pushTsakMdmStoreData(Long taskId, List<TrackRetultAllDetail> trackRetultAllDetails,List<String> removeBusGoods,TokenUserDTO userDTO,RAtomicLong atomicLong) {
        try{
            ArrayList<PushStoreMdmDTO> pushStoreMdmDTOList = new ArrayList<>();
            Iterator<TrackRetultAllDetail> it = trackRetultAllDetails.iterator();
            List<Long> idList = new ArrayList<>();
            while (it.hasNext()){
                TrackRetultAllDetail trackRetultAllDetail = it.next();
                if (CollectionUtils.isNotEmpty(removeBusGoods)&& removeBusGoods.contains(trackRetultAllDetail.getGoodsId())){
                    idList.add(trackRetultAllDetail.getId());
                    it.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(idList)){
                trackRetultAllDetailExtendMapper.delTrackRetultAllDetailById(taskId,idList);
            }
            for (TrackRetultAllDetail trackRetultAllDetail : trackRetultAllDetails) {
                PushStoreMdmDTO pushStoreMdmDTO = new PushStoreMdmDTO();
                BeanUtils.copyProperties(trackRetultAllDetail,pushStoreMdmDTO);
                pushStoreMdmDTO.setEffectStatus(StoreGoodsEffectStatusEnum.YES.getCode());
                pushStoreMdmDTO.setUserId(userDTO.getUserId());
                pushStoreMdmDTO.setUserName(userDTO.getName());
                pushStoreMdmDTOList.add(pushStoreMdmDTO);
            }
            saveMdmTask(taskId,pushStoreMdmDTOList, userDTO);
            List<List<PushStoreMdmDTO>> partition = Lists.partition(pushStoreMdmDTOList, Constants.SEND_NECESSARY_DATA_STORE_GOOD);
            for (List<PushStoreMdmDTO> PushStoreMdmDTOS : partition) {
                sendTrackRetultAllDetail(PushStoreMdmDTOS,atomicLong);
            }
            pushStoreMdmDTOList.clear();
        }catch (Exception e){
            logger.info("下发一店一目数据错误",e);
            atomicLong.delete();
        }

    }

    @Override
    public Boolean uploadOperationalPermission(Long taskId, Integer version, TokenUserDTO userDTO) {
        String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            logger.info("组货任务结果异步处理中 taskId={}",taskId);
            return false;
        }
        BundlingTaskInfoExample example = new BundlingTaskInfoExample();
        example.createCriteria().andIdEqualTo(taskId).andVersionEqualTo(version);
        Optional<BundlingTaskInfo> optional = bundlingTaskInfoMapper.selectByExample(example).stream().findAny();
        if (optional.isPresent() && optional.get().getTaskStatus().equals(BundlTaskStatusEnum.COMPUTED.getCode())) {
            return true;
        }
        return false;
    }

    private void saveMdmTask(Long taskId,List<PushStoreMdmDTO> pushStoreMdmDTOList,TokenUserDTO userDTO) {
        MdmTaskExample mdmTaskExample = new MdmTaskExample();
        mdmTaskExample.createCriteria().andBundlingTaskIdEqualTo(taskId);
        List<MdmTask> mdmTasks = mdmTaskMapper.selectByExample(mdmTaskExample);
        if (CollectionUtils.isEmpty(mdmTasks)){
            MdmTask mdmTask = new MdmTask();
            mdmTask.setTaskSource(Byte.valueOf("2"));
            mdmTask.setTaskStatus(MdmStatusTaskEnum.CREATEING.getCode());
            mdmTask.setDetailCount(0);
            mdmTask.setStatus(Constants.NORMAL_STATUS);
            mdmTask.setDetailCount(pushStoreMdmDTOList.size());
            mdmTask.setUpdatedName(userDTO.getName());
            mdmTask.setUpdatedBy(userDTO.getUserId());
            mdmTask.setGmtUpdate(new Date());
            mdmTask.setCreatedBy(userDTO.getUserId());
            mdmTask.setCreatedName(userDTO.getName());
            mdmTask.setGmtCreate(new Date());
            mdmTask.setBundlingTaskId(taskId);
            mdmTaskMapper.insertSelective(mdmTask);
        }else {
            MdmTask mdmTask = mdmTasks.get(0);
            mdmTask.setDetailCount(0);
            //mdmTask.setDetailCount(mdmTask.getDetailCount()+pushStoreMdmDTOList.size());
            mdmTaskMapper.updateByPrimaryKey(mdmTask);
        }
    }

    private void sendTrackRetultAllDetail(List<PushStoreMdmDTO> pushStoreMdmDTOList,RAtomicLong atomicLong) {
        try {
            NecessaryGoodsDTO necessaryGoodsDTO = new NecessaryGoodsDTO();
            necessaryGoodsDTO.setNecessaryTag(String.valueOf(NecessaryTagEnum.NONE_NECESSARY.getCode()));
            necessaryGoodsDTO.setPushStoreMdmDTOList(pushStoreMdmDTOList);
            SendResult send = sixLevelNecessaryCheckProducer.send(JSONObject.toJSONString(necessaryGoodsDTO));
            if (SendStatus.SEND_OK.equals(send.getSendStatus())) {
                long count = atomicLong.addAndGet(pushStoreMdmDTOList.size());
                logger.info("计数器：{},messageId:{}",count,send.getMsgId());
            }else {
                logger.info("发送一店一目必备目录消费者失败：{},messageId:{}",send.getMsgId());
            }
        } catch (Exception e) {
            logger.error("一店一目推送mq错误", e);
        }
    }

    private void necessarySingleStoreGoods(Long taskId, BundlingTaskInfo bundlingTaskInfo,TokenUserDTO userDTO) {
        String singleStoreFinishedKey = RedisConstant.PUSH_CHOOSE_TYPE_GOODS +bundlingTaskInfo.getId();
        RAtomicLong singleStoreAtomicLong = redissonClient.getAtomicLong(singleStoreFinishedKey);
        //singleStoreAtomicLong.set(0);
        String finishedKey = RedisConstant.PUSH_FINISH_SINGLE_STORE_GOODS +bundlingTaskInfo.getId();
        RBucket<Object> bucket = redissonClient.getBucket(finishedKey);
        bucket.set(1,3,TimeUnit.HOURS);
        try{
            List<NecessaryBusinessIdDTO> necessaryBusinessIdDTOS = taskNecessarySingleStoreGoodsExtendMapper.queryNecessarySingleStoreGoodsBusinessIdList(taskId);
            if (CollectionUtils.isEmpty(necessaryBusinessIdDTOS)){
                return;
            }

            for (NecessaryBusinessIdDTO necessaryBusinessIdDTO : necessaryBusinessIdDTOS) {
                TaskNecessarySingleStoreGoodsExample taskNecessarySingleStoreGoodsExample = new TaskNecessarySingleStoreGoodsExample();
                taskNecessarySingleStoreGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andBusinessidEqualTo(necessaryBusinessIdDTO.getBusinessId());
                List<TaskNecessarySingleStoreGoods> taskNecessarySingleStoreGoods = taskNecessarySingleStoreGoodsMapper.selectByExample(taskNecessarySingleStoreGoodsExample);
                List<String> goodsNoList = taskNecessarySingleStoreGoods.stream().map(TaskNecessarySingleStoreGoods::getGoodsNo).distinct().collect(Collectors.toList());
                List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, necessaryBusinessIdDTO.getBusinessId(), goodsNoList,null);
                Iterator<TaskNecessarySingleStoreGoods> it = taskNecessarySingleStoreGoods.iterator();
                while (it.hasNext()){
                    TaskNecessarySingleStoreGoods next = it.next();
                    if (CollectionUtils.isNotEmpty(removeBusGoods) && removeBusGoods.contains(next.getGoodsNo())){
                        it.remove();
                    }
                }
                List<List<TaskNecessarySingleStoreGoods>> collect1 = Lists.partition(taskNecessarySingleStoreGoods,Constants.SEND_NECESSARY_DATA);
                for (int i = 0; i < collect1.size(); i++) {
                    List<TaskNecessarySingleStoreGoods> singleStoreGood = collect1.get(i);
                    int finalI = i;
                    singleStoreGood.stream().forEach(v -> {
                        if (finalI==collect1.size()-1){
                            v.setFinishFlag(1);
                        }
                        v.setNecessaryTag(String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode()));
                        v.setUpdatedBy(userDTO.getUserId());
                        v.setUpdatedName(userDTO.getName());
                    });
                    NecessaryGoodsDTO necessaryGoodsDTO = new NecessaryGoodsDTO();
                    necessaryGoodsDTO.setNecessaryTag(String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode()));
                    necessaryGoodsDTO.setTaskNecessarySingleStoreGoods(singleStoreGood);
                    long l = singleStoreAtomicLong.incrementAndGet();
                    logger.info("单店锁得值为：{}",l);
                    singleStoreAtomicLong.expire(3,TimeUnit.HOURS);
                    sixLevelNecessaryCheckProducer.send(JSONObject.toJSONString(necessaryGoodsDTO));
                }
            }
        }catch (Exception e){
            bucket.delete();
            singleStoreAtomicLong.delete();
            logger.error("下发单店必备数据报错",e);
        }finally {
            singleStoreAtomicLong.set(0);
            singleStoreAtomicLong.expire(3,TimeUnit.HOURS);
            bucket.set(0,3,TimeUnit.HOURS);
        }

    }

    private void necessaryChooseStoreTypeGoods(Long taskId, BundlingTaskInfo bundlingTaskInfo,TokenUserDTO userDTO) {
        String chooseStoreFinishedKey = RedisConstant.PUSH_CHOOSE_TYPE_GOODS +bundlingTaskInfo.getId();
        RAtomicLong chooseStoreAtomicLong = redissonClient.getAtomicLong(chooseStoreFinishedKey);
       // chooseStoreAtomicLong.set(0);
        String finishedKey = RedisConstant.PUSH_FINISH_CHOOSE_TYPE_GOODS +bundlingTaskInfo.getId();
        RBucket<Object> bucket = redissonClient.getBucket(finishedKey);
        bucket.set(1,3,TimeUnit.HOURS);

        try{
            List<NecessaryBusinessIdDTO> necessaryBusinessIdDTOS = taskNecessaryChooseStoreTypeGoodsExtendMapper.queryNecessaryChooseStoreTypeGoodsBusinessIdList(taskId);
            if (CollectionUtils.isEmpty(necessaryBusinessIdDTOS)){
                return;
            }

            for (NecessaryBusinessIdDTO necessaryBusinessIdDTO : necessaryBusinessIdDTOS) {
                TaskNecessaryChooseStoreTypeGoodsExample taskNecessaryChooseStoreTypeGoodsExample = new TaskNecessaryChooseStoreTypeGoodsExample();
                taskNecessaryChooseStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(taskId).andBusinessidEqualTo(necessaryBusinessIdDTO.getBusinessId());
                List<TaskNecessaryChooseStoreTypeGoods> taskNecessaryChooseStoreTypeGoods = taskNecessaryChooseStoreTypeGoodsMapper.selectByExample(taskNecessaryChooseStoreTypeGoodsExample);
                List<String> goodsNoList = taskNecessaryChooseStoreTypeGoods.stream().map(TaskNecessaryChooseStoreTypeGoods::getGoodsNo).collect(Collectors.toList());
                List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, necessaryBusinessIdDTO.getBusinessId(), goodsNoList,null);
                Iterator<TaskNecessaryChooseStoreTypeGoods> it = taskNecessaryChooseStoreTypeGoods.iterator();
                while (it.hasNext()){
                    TaskNecessaryChooseStoreTypeGoods next = it.next();
                    if (CollectionUtils.isNotEmpty(removeBusGoods) && removeBusGoods.contains(next.getGoodsNo())){
                        it.remove();
                    }
                }
                List<List<TaskNecessaryChooseStoreTypeGoods>> collect1 = Lists.partition(taskNecessaryChooseStoreTypeGoods,Constants.SEND_NECESSARY_DATA);
                for (int i = 0; i < collect1.size(); i++) {
                    List<TaskNecessaryChooseStoreTypeGoods> chooseStoreTypeGood = collect1.get(i);
                    int finalI = i;
                    chooseStoreTypeGood.stream().forEach(v->{
                        if (finalI ==collect1.size()-1){
                            v.setFinishFlag(1);
                        }
                        v.setNecessaryTag(String.valueOf(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()));
                        v.setUpdatedBy(userDTO.getUserId());
                        v.setUpdatedName(userDTO.getName());
                    });
                    NecessaryGoodsDTO necessaryGoodsDTO = new NecessaryGoodsDTO();
                    necessaryGoodsDTO.setNecessaryTag(String.valueOf(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()));
                    necessaryGoodsDTO.setTaskNecessaryChooseStoreTypeGoods(chooseStoreTypeGood);
                    long l = chooseStoreAtomicLong.incrementAndGet();
                    logger.info("选配锁得值为：{}",l );
                    chooseStoreAtomicLong.expire(3,TimeUnit.HOURS);
                    sixLevelNecessaryCheckProducer.send(JSONObject.toJSONString(necessaryGoodsDTO));
                }
            }
        }catch (Exception e){
            bucket.delete();
            chooseStoreAtomicLong.delete();
            logger.error("下发店型选配数据报错",e);
        }finally {
            chooseStoreAtomicLong.set(0);
            chooseStoreAtomicLong.expire(3,TimeUnit.HOURS);
            bucket.set(0,3,TimeUnit.HOURS);
        }
    }

    private void necessaryStoreTypeGoods(Long taskId, BundlingTaskInfo bundlingTaskInfo,TokenUserDTO userDTO) {
        String storeTypeFinishedKey = RedisConstant.PUSH_STORE_TYPE_GOODS +bundlingTaskInfo.getId();
        RAtomicLong storeTypeAtomicLong = redissonClient.getAtomicLong(storeTypeFinishedKey);
        //storeTypeAtomicLong.set(0);
        String finishedKey = RedisConstant.PUSH_FINISH_STORE_TYPE_GOODS +bundlingTaskInfo.getId();
        RBucket<Object> bucket = redissonClient.getBucket(finishedKey);
        bucket.set(1,3,TimeUnit.HOURS);
        try{
            List<NecessaryBusinessIdDTO> necessaryBusinessIdDTOS = taskNecessaryStoreTypeGoodsExtendMapper.queryTaskNecessaryStoreTypeGoodBusinessIdList(taskId);
            if (CollectionUtils.isEmpty(necessaryBusinessIdDTOS)){
                return;
            }
            for (NecessaryBusinessIdDTO necessaryBusinessIdDTO : necessaryBusinessIdDTOS) {
                TaskNecessaryStoreTypeGoodsExample example = new TaskNecessaryStoreTypeGoodsExample();
                example.createCriteria().andTaskIdEqualTo(taskId).andBusinessidEqualTo(necessaryBusinessIdDTO.getBusinessId());
                List<TaskNecessaryStoreTypeGoods> taskNecessaryStoreTypeGoods = taskNecessaryStoreTypeGoodsMapper.selectByExample(example);
                List<String> goodsNoList = taskNecessaryStoreTypeGoods.stream().map(TaskNecessaryStoreTypeGoods::getGoodsNo).collect(Collectors.toList());
                List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, necessaryBusinessIdDTO.getBusinessId(), goodsNoList,null);
                Iterator<TaskNecessaryStoreTypeGoods> it = taskNecessaryStoreTypeGoods.iterator();
                while (it.hasNext()){
                    TaskNecessaryStoreTypeGoods next = it.next();
                    if (CollectionUtils.isNotEmpty(removeBusGoods) && removeBusGoods.contains(next.getGoodsNo())){
                        it.remove();
                    }
                }
                List<List<TaskNecessaryStoreTypeGoods>> collect1 = Lists.partition(taskNecessaryStoreTypeGoods,Constants.SEND_NECESSARY_DATA);
                for (int i = 0; i < collect1.size(); i++) {
                    List<TaskNecessaryStoreTypeGoods> necessaryStoreTypeGoods = collect1.get(i);
                    int finalI = i;
                    necessaryStoreTypeGoods.stream().forEach(v->{
                        v.setNecessaryTag(String.valueOf(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode()));
                        if (finalI ==collect1.size()-1){
                            v.setFinishFlag(1);
                        }
                        v.setUpdatedBy(userDTO.getUserId());
                        v.setUpdatedName(userDTO.getName());
                    });
                    NecessaryGoodsDTO necessaryGoodsDTO = new NecessaryGoodsDTO();
                    necessaryGoodsDTO.setNecessaryTag(String.valueOf(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode()));
                    necessaryGoodsDTO.setTaskNecessaryStoreTypeGoods(necessaryStoreTypeGoods);
                    long l = storeTypeAtomicLong.incrementAndGet();
                    logger.info("店型锁得值为：{}",l );
                    storeTypeAtomicLong.expire(3,TimeUnit.HOURS);
                    sixLevelNecessaryCheckProducer.send(JSONObject.toJSONString(necessaryGoodsDTO));
                }
            }
        }catch (Exception e){
            bucket.delete();
            storeTypeAtomicLong.delete();
            logger.error("下发店型必备数据报错",e);
        }finally {
            storeTypeAtomicLong.set(0);
            storeTypeAtomicLong.expire(3,TimeUnit.HOURS);
            bucket.set(0,3,TimeUnit.HOURS);
        }
    }

    private void necessaryCompanyGoods(BundlingTaskInfo bundlingTaskInfo,TokenUserDTO userDTO) {

        String companyFinishedKey = RedisConstant.PUSH_COMPANY_GOODS +bundlingTaskInfo.getId();
        RAtomicLong companyAtomicLong = redissonClient.getAtomicLong(companyFinishedKey);
        //companyAtomicLong.set(0);
        String finishedKey = RedisConstant.PUSH_FINISH_COMPANY_GOODS +bundlingTaskInfo.getId();
        RBucket<Object> bucket = redissonClient.getBucket(finishedKey);
        bucket.set(1,3,TimeUnit.HOURS);
        try{
            Long aLong = taskNecessaryCompanyGoodsExtendMapper.countNecessaryCompanyGoods(bundlingTaskInfo.getId());
            if (aLong<=0){
                return;
            }
            List<NecessaryBusinessIdDTO> necessaryBusinessIdDTOS = taskNecessaryCompanyGoodsExtendMapper.queryNecessaryCompanyGoodsBusinessIdList(bundlingTaskInfo.getId());
            for (NecessaryBusinessIdDTO necessaryBusinessIdDTO : necessaryBusinessIdDTOS) {
                TaskNecessaryCompanyGoodsExample example = new TaskNecessaryCompanyGoodsExample();
                example.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId()).andBusinessidEqualTo(necessaryBusinessIdDTO.getBusinessId());
                List<TaskNecessaryCompanyGoods> taskNecessaryCompanyGoods = taskNecessaryCompanyGoodsMapper.selectByExample(example);

                List<String> goodsNoList = taskNecessaryCompanyGoods.stream().map(TaskNecessaryCompanyGoods::getGoodsNo).collect(Collectors.toList());
                List<String> removeBusGoods = getRemoveBusGoods(bundlingTaskInfo, necessaryBusinessIdDTO.getBusinessId(), goodsNoList,null);
                Iterator<TaskNecessaryCompanyGoods> it = taskNecessaryCompanyGoods.iterator();
                while (it.hasNext()){
                    TaskNecessaryCompanyGoods next = it.next();
                    if (CollectionUtils.isNotEmpty(removeBusGoods) && removeBusGoods.contains(next.getGoodsNo())){
                        it.remove();
                    }
                }
                List<List<TaskNecessaryCompanyGoods>> collect1 = Lists.partition(taskNecessaryCompanyGoods, Constants.SEND_NECESSARY_DATA);
                for (int i = 0; i < collect1.size(); i++) {
                    List<TaskNecessaryCompanyGoods> necessaryCompanyGoods = collect1.get(i);
                    int finalI = i;
                    necessaryCompanyGoods.stream().forEach(v->{
                        v.setNecessaryTag(String.valueOf(NecessaryTagEnum.COMPANY_NECESSARY.getCode()));
                        if (finalI ==collect1.size()-1){
                            v.setFinishFlag(1);
                        }
                        v.setUpdatedBy(userDTO.getUserId());
                        v.setUpdatedName(userDTO.getName());
                    });
                    NecessaryGoodsDTO necessaryGoodsDTO = new NecessaryGoodsDTO();
                    necessaryGoodsDTO.setNecessaryTag(String.valueOf(NecessaryTagEnum.COMPANY_NECESSARY.getCode()));
                    necessaryGoodsDTO.setTaskNecessaryCompanyGoods(necessaryCompanyGoods);
                    long l = companyAtomicLong.incrementAndGet();
                    logger.info("企业锁得值为：{}",l);
                    companyAtomicLong.expire(3,TimeUnit.HOURS);
                    sixLevelNecessaryCheckProducer.send(JSONObject.toJSONString(necessaryGoodsDTO));
                }
            }
        }catch (Exception e){
            bucket.delete();
            companyAtomicLong.delete();
            logger.error("下发企业必备数据报错",e);
        }finally {
            companyAtomicLong.set(0);
            companyAtomicLong.expire(3,TimeUnit.HOURS);
            bucket.set(0,3,TimeUnit.HOURS);
        }

    }

    public List<String> getStoreLicense(Long businessId, Long storeId) {
        try {
            RBucket<List<String>> rBucket = redissonClient.getBucket(BUSINESS_SCOPE_CACHE + storeId.toString());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(rBucket.get())) {
                List<MdmLicenseBaseDTO> storeLicense = storeService.findStoreLicense(businessId, storeId);
                rBucket.set(storeLicense.stream().map(MdmLicenseBaseDTO::getBusinessScope).distinct().collect(Collectors.toList()), 30L, TimeUnit.MINUTES);
            }
            return rBucket.get();
        } catch (Exception e) {
            try {
                logger.warn("查询storeService.findStoreLicense报错 businessId:{}, storeId:{}", businessId, storeId);
                return storeService.findStoreLicense(businessId, storeId).stream().map(MdmLicenseBaseDTO::getBusinessScope).distinct().collect(Collectors.toList());
            } catch (Exception e1) {
                logger.warn("查询storeService.findStoreLicense报错返回空 businessId:{}, storeId:{}", businessId, storeId);
                return new ArrayList<>();
            }
        }
    }

    private List<String> getRemoveBusGoods (BundlingTaskInfo bundlingTaskInfo, Long businessId, List<String> goodNos,Long storeId){
        List<String> removeGoodsNo= new ArrayList<>();
        // 取平台的经营属性 商品表企业级属性：经营属性=（核心必备、一般、新品）-》从参数BB_2/goodsline中取值
        RuleParam ruleParam = new RuleParam();
        ruleParam.setScopeCode("BB_2");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
        Map<String, String> goodslineMap = ruleEnum.get("goodsline").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));

        // 平台必备店型
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(bundlingTaskInfo.getOrgId(), ConfigTypeEnum.BB.getType(), null);
        List<String> goodslineList = configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Lists.newArrayList("goodsline")).stream().map(v -> goodslineMap.get(v.getPerprotyValue())).distinct().collect(Collectors.toList());

        //商品是否完成首营： 商品经营属性合法性校验：
        List<String> storeLicense=new ArrayList<>();
        if (null != storeId) {
            storeLicense =getStoreLicense(businessId, storeId);
        }
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        List<AuctionSpuBaseInfo> auctionSpuBaseInfoList = forestService.batchFindSpuProperty(goodNos, businessId);
        for (AuctionSpuBaseInfo auctionSpuBaseInfo : auctionSpuBaseInfoList) {
            if (!goodslineList.contains(auctionSpuBaseInfo.getGoodsline())) {
                removeGoodsNo.add(auctionSpuBaseInfo.getGoodsNo());
                logger.info("businessId:{}商品经营合法性removeGoodsNo:{}",businessId,removeGoodsNo);
            }
            if (null != storeId && (CollectionUtils.isEmpty(storeLicense) || CollectionUtils.isNotEmpty(storeLicense) && !storeLicense.contains(auctionSpuBaseInfo.getBusiscopetag()))) {
                removeGoodsNo.add(auctionSpuBaseInfo.getGoodsNo());
                logger.info("businessId:{}门店经营范围和商品经营范围不匹配removeGoodsNo:{}", businessId, removeGoodsNo);
            }
        }
        return removeGoodsNo;
    }

    @Override
    public String getRedisKeyByTaskId(Long taskId, TokenUserDTO userDTO) {
        RBucket<ImportResult> bucket = redissonClient.getBucket(String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId));
        if (bucket.isExists()){
            if (bucket.get().getCode().equals(Constants.TASK_EXECUTION)){
                return String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId);
            }
        }
        return "";
    }

    @Override
    public String updateRedisKeyByTaskId(Long taskId, TokenUserDTO userDTO) {
        RBucket<ImportResult> bucket = redissonClient.getBucket(String.format(STORE_TYPE_GOODS_DEL_IMPORT, taskId));
        if (bucket.isExists()){
            ImportResult importResult = bucket.get();
            importResult.setCode("0");
            importResult.setMessage("处理成功");
            bucket.set(importResult,12,TimeUnit.HOURS);
            return "处理成功";
        }
        return "";
    }

    @Override
    public void deleteFiveData(Long taskId ,Integer type) {
        if (null==taskId){
            throw new BusinessErrorException("任务ID不能为空");
        }
        try{
            if (type.equals(Constants.DEL_STATUS)){
                //删除大数据推来的数据
                TaskNecessaryPlatformGoodsExample example = new TaskNecessaryPlatformGoodsExample();
                example.createCriteria().andTaskIdEqualTo(taskId);
                taskNecessaryPlatformGoodsMapper.deleteByExample(example);
                TaskNecessaryCompanyGoodsExample example1 = new TaskNecessaryCompanyGoodsExample();
                example1.createCriteria().andTaskIdEqualTo(taskId);
                taskNecessaryCompanyGoodsMapper.deleteByExample(example1);
                TaskNecessaryStoreTypeGoodsExample example2 = new TaskNecessaryStoreTypeGoodsExample();
                example2.createCriteria().andTaskIdEqualTo(taskId);
                taskNecessaryStoreTypeGoodsMapper.deleteByExample(example2);
                TaskNecessaryChooseStoreTypeGoodsExample example3 = new TaskNecessaryChooseStoreTypeGoodsExample();
                example3.createCriteria().andTaskIdEqualTo(taskId);
                taskNecessaryChooseStoreTypeGoodsMapper.deleteByExample(example3);
                TaskNecessarySingleStoreGoodsExample example4 = new TaskNecessarySingleStoreGoodsExample();
                example4.createCriteria().andTaskIdEqualTo(taskId);
                taskNecessarySingleStoreGoodsMapper.deleteByExample(example4);
                TrackResultFileExample trackResultFileExample = new TrackResultFileExample();
                trackResultFileExample.createCriteria().andTaskIdEqualTo(taskId);
                trackResultFileMapper.deleteByExample(trackResultFileExample);
            }else {
                //修改平台必备目录数据
                NecessaryPlatformGoodsExample example = new NecessaryPlatformGoodsExample();
                example.createCriteria().andVersionEqualTo(taskId);
                List<NecessaryPlatformGoods> necessaryPlatformGoods = necessaryPlatformGoodsMapper.selectByExample(example);
                Map<String, SpuListVo> spuMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(necessaryPlatformGoods) && CollectionUtils.isNotEmpty(necessaryPlatformGoods.stream().filter(v -> StringUtils.isEmpty(v.getComposition())).collect(Collectors.toList()))) {
                    List<String> goodsNoList = necessaryPlatformGoods.stream().map(NecessaryPlatformGoods::getGoodsNo).distinct().collect(Collectors.toList());
                    Lists.partition(goodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
                        spuMap.putAll(searchService.getSpuVOMap(v));
                        //防止接口限流
                        try {
                            Thread.sleep(1000L);
                        } catch (Exception e) {
                            logger.error("补数据出错");
                        }
                    });
                    for (NecessaryPlatformGoods necessaryPlatformGood : necessaryPlatformGoods) {
                        if (Objects.nonNull(spuMap.get(necessaryPlatformGood.getGoodsNo()))) {
                            necessaryPlatformGood.setComposition(spuMap.get(necessaryPlatformGood.getGoodsNo()).getComponent());
                        }
                        Optional<OrgInfoBaseCache> platformByOrgId = CacheVar.getPlatformByOrgId(necessaryPlatformGood.getPlatformOrgId());
                        if (platformByOrgId.isPresent()) {
                            necessaryPlatformGood.setPlatformName(platformByOrgId.get().getPlatformShortName());
                        }
                    }
                    List<List<NecessaryPlatformGoods>> partition = Lists.partition(necessaryPlatformGoods, Constants.INSERT_MAX_SIZE);
                    for (List<NecessaryPlatformGoods> necessaryPlatformGoodsList : partition) {
                        necessaryPlatformGoodsExtendMapper.batchDel(necessaryPlatformGoodsList.stream().map(NecessaryPlatformGoods::getId).collect(Collectors.toList()));
                        necessaryPlatformGoodsList.stream().forEach(v -> {
                            v.setId(null);
                        });
                        necessaryPlatformGoodsExtendMapper.batchInsert(necessaryPlatformGoodsList);
                    }
                    spuMap.clear();
                }
                //修改企业必备目录数据
                NecessaryCompanyGoodsExample example1 = new NecessaryCompanyGoodsExample();
                example1.createCriteria().andVersionEqualTo(taskId);
                List<NecessaryCompanyGoods> necessaryCompanyGoods = necessaryCompanyGoodsMapper.selectByExample(example1);
                if (CollectionUtils.isNotEmpty(necessaryCompanyGoods) && CollectionUtils.isNotEmpty(necessaryCompanyGoods.stream().filter(v -> StringUtils.isEmpty(v.getComposition())).collect(Collectors.toList()))) {
                    List<String> companyGoodsNoList = necessaryCompanyGoods.stream().map(NecessaryCompanyGoods::getGoodsNo).distinct().collect(Collectors.toList());
                    Lists.partition(companyGoodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
                        spuMap.putAll(searchService.getSpuVOMap(v));
                        //防止接口限流
                        try {
                            Thread.sleep(1000L);
                        } catch (Exception e) {
                            logger.error("补数据出错");
                        }
                    });
                    for (NecessaryCompanyGoods necessaryCompanyGoods1 : necessaryCompanyGoods) {
                        if (Objects.nonNull(spuMap.get(necessaryCompanyGoods1.getGoodsNo()))) {
                            necessaryCompanyGoods1.setComposition(spuMap.get(necessaryCompanyGoods1.getGoodsNo()).getComponent());
                        }
                        Optional<OrgInfoBaseCache> platformByOrgId = CacheVar.getPlatformByOrgId(necessaryCompanyGoods1.getPlatformOrgId());
                        if (platformByOrgId.isPresent()) {
                            necessaryCompanyGoods1.setPlatformName(platformByOrgId.get().getPlatformShortName());
                        }
                    }
                    List<List<NecessaryCompanyGoods>> partition1 = Lists.partition(necessaryCompanyGoods, Constants.INSERT_MAX_SIZE);
                    for (List<NecessaryCompanyGoods> necessaryCompanyGoodsList : partition1) {
                        necessaryCompanyGoodsExtendMapper.batchDel(necessaryCompanyGoodsList.stream().map(NecessaryCompanyGoods::getId).collect(Collectors.toList()));
                        necessaryCompanyGoodsList.stream().forEach(v -> {
                            v.setId(null);
                        });
                        necessaryCompanyGoodsExtendMapper.batchInsert(necessaryCompanyGoodsList);
                    }
                    spuMap.clear();
                }

                //修改店型必备目录数据
                NecessaryStoreTypeGoodsExample example2 = new NecessaryStoreTypeGoodsExample();
                example2.createCriteria().andVersionEqualTo(taskId);
                List<NecessaryStoreTypeGoods> necessaryStoreTypeGoods = necessaryStoreTypeGoodsMapper.selectByExample(example2);
                if (CollectionUtils.isNotEmpty(necessaryStoreTypeGoods) && CollectionUtils.isNotEmpty(necessaryStoreTypeGoods.stream().filter(v -> StringUtils.isEmpty(v.getComposition())).collect(Collectors.toList()))) {
                    List<String> storeTypeGoodsNoList = necessaryStoreTypeGoods.stream().map(NecessaryStoreTypeGoods::getGoodsNo).distinct().collect(Collectors.toList());
                    Lists.partition(storeTypeGoodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
                        spuMap.putAll(searchService.getSpuVOMap(v));
                        //防止接口限流
                        try {
                            Thread.sleep(1000L);
                        } catch (Exception e) {
                            logger.error("补数据出错");
                        }
                    });
                    for (NecessaryStoreTypeGoods necessaryStoreTypeGoods1 : necessaryStoreTypeGoods) {
                        if (Objects.nonNull(spuMap.get(necessaryStoreTypeGoods1.getGoodsNo()))) {
                            necessaryStoreTypeGoods1.setComposition(spuMap.get(necessaryStoreTypeGoods1.getGoodsNo()).getComponent());
                        }
                        Optional<OrgInfoBaseCache> platformByOrgId = CacheVar.getPlatformByOrgId(necessaryStoreTypeGoods1.getPlatformOrgId());
                        if (platformByOrgId.isPresent()) {
                            necessaryStoreTypeGoods1.setPlatformName(platformByOrgId.get().getPlatformShortName());
                        }
                    }
                    List<List<NecessaryStoreTypeGoods>> partition2 = Lists.partition(necessaryStoreTypeGoods, Constants.INSERT_MAX_SIZE);
                    for (List<NecessaryStoreTypeGoods> necessaryStoreTypeGoods1 : partition2) {
                        necessaryStoreTypeGoodsExtendMapper.batchDel(necessaryStoreTypeGoods1.stream().map(NecessaryStoreTypeGoods::getId).collect(Collectors.toList()));
                        necessaryStoreTypeGoods1.stream().forEach(v -> {
                            v.setId(null);
                        });
                        necessaryStoreTypeGoodsExtendMapper.batchInsert(necessaryStoreTypeGoods1);
                    }
                    spuMap.clear();
                }

                //修改店型选配目录数据
                NecessaryChooseStoreTypeGoodsExample example3 = new NecessaryChooseStoreTypeGoodsExample();
                example3.createCriteria().andVersionEqualTo(taskId);
                List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoods = necessaryChooseStoreTypeGoodsMapper.selectByExample(example3);
                if (CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoods) && CollectionUtils.isNotEmpty(necessaryChooseStoreTypeGoods.stream().filter(v -> StringUtils.isEmpty(v.getComposition())).collect(Collectors.toList()))) {
                    List<String> chooseStoreTypeNoList = necessaryChooseStoreTypeGoods.stream().map(NecessaryChooseStoreTypeGoods::getGoodsNo).distinct().collect(Collectors.toList());
                    Lists.partition(chooseStoreTypeNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
                        spuMap.putAll(searchService.getSpuVOMap(v));
                        //防止接口限流
                        try {
                            Thread.sleep(1000L);
                        } catch (Exception e) {
                            logger.error("补数据出错");
                        }
                    });
                    for (NecessaryChooseStoreTypeGoods necessaryChooseStoreTypeGood : necessaryChooseStoreTypeGoods) {
                        if (Objects.nonNull(spuMap.get(necessaryChooseStoreTypeGood.getGoodsNo()))) {
                            necessaryChooseStoreTypeGood.setComposition(spuMap.get(necessaryChooseStoreTypeGood.getGoodsNo()).getComponent());
                        }
                        Optional<OrgInfoBaseCache> platformByOrgId = CacheVar.getPlatformByOrgId(necessaryChooseStoreTypeGood.getPlatformOrgId());
                        if (platformByOrgId.isPresent()) {
                            necessaryChooseStoreTypeGood.setPlatformName(platformByOrgId.get().getPlatformShortName());
                        }
                    }
                    List<List<NecessaryChooseStoreTypeGoods>> partition3 = Lists.partition(necessaryChooseStoreTypeGoods, Constants.INSERT_MAX_SIZE);
                    for (List<NecessaryChooseStoreTypeGoods> necessaryChooseStoreTypeGoodsList : partition3) {
                        necessaryChooseStoreTypeGoodsExtendMapper.batchDel(necessaryChooseStoreTypeGoodsList.stream().map(NecessaryChooseStoreTypeGoods::getId).collect(Collectors.toList()));
                        necessaryChooseStoreTypeGoodsList.stream().forEach(v -> {
                            v.setId(null);
                        });
                        necessaryChooseStoreTypeGoodsExtendMapper.batchInsert(necessaryChooseStoreTypeGoodsList);
                    }
                    spuMap.clear();
                }

                //修改单店目录数据
                NecessarySingleStoreGoodsExample example4 = new NecessarySingleStoreGoodsExample();
                example4.createCriteria().andVersionEqualTo(taskId);
                List<NecessarySingleStoreGoods> necessarySingleStoreGoods = necessarySingleStoreGoodsMapper.selectByExample(example4);
                if (CollectionUtils.isNotEmpty(necessarySingleStoreGoods) && CollectionUtils.isNotEmpty(necessarySingleStoreGoods.stream().filter(v -> StringUtils.isEmpty(v.getComposition())).collect(Collectors.toList()))) {
                    List<String> singleStoreGoodsNoList = necessarySingleStoreGoods.stream().map(NecessarySingleStoreGoods::getGoodsNo).distinct().collect(Collectors.toList());
                    Lists.partition(singleStoreGoodsNoList, Constants.QUERY_SEARCH_PAGESIZE).forEach(v -> {
                        spuMap.putAll(searchService.getSpuVOMap(v));
                        //防止接口限流
                        try {
                            Thread.sleep(1000L);
                        } catch (Exception e) {
                            logger.error("补数据出错");
                        }
                    });
                    for (NecessarySingleStoreGoods necessarySingleStoreGood : necessarySingleStoreGoods) {
                        if (Objects.nonNull(spuMap.get(necessarySingleStoreGood.getGoodsNo()))) {
                            necessarySingleStoreGood.setComposition(spuMap.get(necessarySingleStoreGood.getGoodsNo()).getComponent());
                        }
                        Optional<OrgInfoBaseCache> platformByOrgId = CacheVar.getPlatformByOrgId(necessarySingleStoreGood.getPlatformOrgId());
                        if (platformByOrgId.isPresent()) {
                            necessarySingleStoreGood.setPlatformName(platformByOrgId.get().getPlatformShortName());
                        }
                    }
                    List<List<NecessarySingleStoreGoods>> partition4 = Lists.partition(necessarySingleStoreGoods, Constants.INSERT_MAX_SIZE);
                    for (List<NecessarySingleStoreGoods> singleStoreGoods : partition4) {
                        necessarySingleStoreGoodsExtendMapper.batchDel(singleStoreGoods.stream().map(NecessarySingleStoreGoods::getId).collect(Collectors.toList()));
                        singleStoreGoods.stream().forEach(v -> {
                            v.setId(null);
                        });
                        necessarySingleStoreGoodsExtendMapper.batchInsert(singleStoreGoods);
                    }
                    spuMap.clear();
                }
            }

        }catch (Exception e){
            logger.error("删除出错任务Id:{}",taskId);
            throw new BusinessErrorException("删除出错",e.getMessage());
        }
    }

    @Override
    public void cleanGroupData(String date) {
        Date date1;
        if (StringUtils.isEmpty(date)){
            date1 = DateUtils.parse(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN));
        }else {
            date1= DateUtils.parse(date);
        }
        if (Objects.isNull(date1)){
            throw new BusinessErrorException("时间格式异常");
        }
        List<BundlingTaskInfo> bundlingTaskInfos = getTaskInfos(Arrays.asList(BundlTaskStatusEnum.CANCEL.getCode()));
        if (CollectionUtils.isEmpty(bundlingTaskInfos)){
            return;
        }
        Date date2 = DateUtils.parse(DateUtils.conventDateStrByDate(DateUtils.getFetureDateBydays(date1, -14), DateUtils.DATE_PATTERN));
        logger.info("累加14天后的date2:{}",date2);
        Date date3 = DateUtils.parse(DateUtils.conventDateStrByDate(DateUtils.getFetureDateBydays(date1, -180), DateUtils.DATE_PATTERN));
        logger.info("累加180天后的date3:{}",date3);
        logger.info("获取组货任务信息：{}",JSONObject.toJSONString(bundlingTaskInfos));
        List<BundlingTaskInfo> cleanBundTaskList = bundlingTaskInfos.stream().filter(v -> DateUtils.parse(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATE_PATTERN)).before(date2)).collect(Collectors.toList());
        logger.info("14天前的任务集合：{}",JSONObject.toJSONString(cleanBundTaskList));
        List<BundlingTaskInfo> computedBundlingTaskInfos = getTaskInfos(Arrays.asList(BundlTaskStatusEnum.COMPUTED.getCode()));
        List<BundlingTaskInfo> CalculationCompletedCleanBundTaskList = computedBundlingTaskInfos.stream().filter(v -> DateUtils.parse(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATE_PATTERN)).before(date3)).collect(Collectors.toList());
        logger.info("180天前的任务集合：{}",JSONObject.toJSONString(CalculationCompletedCleanBundTaskList));
        if (CollectionUtils.isEmpty(cleanBundTaskList)){
            return;
        }
        if (CollectionUtils.isNotEmpty(CalculationCompletedCleanBundTaskList)){
            for (BundlingTaskInfo bundlingTaskInfo : CalculationCompletedCleanBundTaskList) {
                BundlingTaskInfo bundlingTaskInfo1 = new BundlingTaskInfo();
                bundlingTaskInfo1.setTaskStatus(BundlTaskStatusEnum.CANCEL.getCode());
                bundlingTaskInfo1.setId(bundlingTaskInfo.getId());
                bundlingTaskInfoMapper.updateByPrimaryKeySelective(bundlingTaskInfo1);
            }
        }
        cleanBundTaskList.addAll(CalculationCompletedCleanBundTaskList);
        TrackResultFileExample trackResultFileExample = new TrackResultFileExample();
        trackResultFileExample.createCriteria().andTaskIdIn(cleanBundTaskList.stream().map(BundlingTaskInfo::getId).collect(Collectors.toList()));
        trackResultFileMapper.selectByExample(trackResultFileExample);

        TrackRetultLevelReviewExample trackRetultLevelReviewExample = new TrackRetultLevelReviewExample();
        trackRetultLevelReviewExample.createCriteria().andTaskIdIn(cleanBundTaskList.stream().map(BundlingTaskInfo::getId).collect(Collectors.toList()));
        trackRetultLevelReviewMapper.deleteByExample(trackRetultLevelReviewExample);

        TrackRetultEfficiencyAnalyseExample trackRetultEfficiencyAnalyseExample = new TrackRetultEfficiencyAnalyseExample();
        trackRetultEfficiencyAnalyseExample.createCriteria().andTaskIdIn(cleanBundTaskList.stream().map(BundlingTaskInfo::getId).collect(Collectors.toList()));
        trackRetultEfficiencyAnalyseMapper.deleteByExample(trackRetultEfficiencyAnalyseExample);

        for (BundlingTaskInfo bundlingTaskInfo : cleanBundTaskList) {
            for (int i = 0; ; i++) {
                TrackRetultCompositionReviewExample trackRetultCompositionReviewExample = new TrackRetultCompositionReviewExample();
                trackRetultCompositionReviewExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                trackRetultCompositionReviewExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount= trackRetultCompositionReviewMapper.deleteByExample(trackRetultCompositionReviewExample);
                if (delCount<=0){
                    break;
                }
            }
            for (int i = 0; ; i++) {
                TrackRetultTop4levelStoregroupExample trackRetultTop4levelStoregroupExample = new TrackRetultTop4levelStoregroupExample();
                trackRetultTop4levelStoregroupExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                trackRetultTop4levelStoregroupExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int top4levelStoregroupCount = trackRetultTop4levelStoregroupMapper.deleteByExample(trackRetultTop4levelStoregroupExample);
                if (top4levelStoregroupCount<=0){
                    break;
                }
            }

            for (int i = 0; ; i++) {
                TrackResultLevelNecessaryExample trackResultLevelNecessaryExample = new TrackResultLevelNecessaryExample();
                trackResultLevelNecessaryExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                trackResultLevelNecessaryExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount= trackResultLevelNecessaryMapper.deleteByExample(trackResultLevelNecessaryExample);
                if (delCount<=0){
                    break;
                }
            }

            for (int i = 0; ; i++) {
                TaskNecessaryPlatformGoodsExample platformGoodsExample = new TaskNecessaryPlatformGoodsExample();
                platformGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                platformGoodsExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount = taskNecessaryPlatformGoodsMapper.deleteByExample(platformGoodsExample);
                if (delCount<=0){
                    break;
                }
            }
            for (int i = 0; ; i++) {
                TaskNecessaryCompanyGoodsExample companyGoodsExample = new TaskNecessaryCompanyGoodsExample();
                companyGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                companyGoodsExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount = taskNecessaryCompanyGoodsMapper.deleteByExample(companyGoodsExample);
                if (delCount<=0){
                    break;
                }
            }

            for (int i = 0; ; i++) {
                TaskNecessaryStoreTypeGoodsExample storeTypeGoodsExample = new TaskNecessaryStoreTypeGoodsExample();
                storeTypeGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                storeTypeGoodsExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount = taskNecessaryStoreTypeGoodsMapper.deleteByExample(storeTypeGoodsExample);
                if (delCount<=0){
                    break;
                }
            }

            for (int i = 0; ; i++) {
                TaskNecessaryChooseStoreTypeGoodsExample chooseStoreTypeGoodsExample = new TaskNecessaryChooseStoreTypeGoodsExample();
                chooseStoreTypeGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                chooseStoreTypeGoodsExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount = taskNecessaryChooseStoreTypeGoodsMapper.deleteByExample(chooseStoreTypeGoodsExample);
                if (delCount<=0){
                    break;
                }
            }

            for (int i = 0; ; i++) {
                TaskNecessarySingleStoreGoodsExample singleStoreGoodsExample = new TaskNecessarySingleStoreGoodsExample();
                singleStoreGoodsExample.createCriteria().andTaskIdEqualTo(bundlingTaskInfo.getId());
                singleStoreGoodsExample.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                int delCount = taskNecessarySingleStoreGoodsMapper.deleteByExample(singleStoreGoodsExample);
                if (delCount<=0){
                    break;
                }
            }

            for (int i = 0; ; i++) {
                TrackRetultDetailParam trackRetultDetailParam = new TrackRetultDetailParam();
                trackRetultDetailParam.setTaskId(bundlingTaskInfo.getId());
                trackRetultDetailParam.setLimit(Constants.EXPORT_ONCE_QUERY_MAX);
                List<TrackRetultAllDetail> trackRetultAllDetails = trackRetultAllDetailExtendMapper.queryTrackRetultDetailByGoods(trackRetultDetailParam);
                int delCount = trackRetultAllDetailExtendMapper.batchDel(bundlingTaskInfo.getId(),trackRetultAllDetails.stream().map(TrackRetultAllDetail::getId).collect(Collectors.toList()));
                if (delCount<=0){
                    break;
                }
            }
        }
    }

    @Override
    public BdpResponseDTO adjustBdpTrackResult(Long taskId, Integer version) {
        BundlingTaskInfoExample example = new BundlingTaskInfoExample();
        example.createCriteria().andIdEqualTo(taskId).andVersionEqualTo(version);
        BundlingTaskInfo taskInfo = bundlingTaskInfoMapper.selectByExample(example).stream().findAny().orElseThrow(() -> new AmisBadRequestException("没有找到组货任务"));
        if (!taskInfo.getTaskStatus().equals(BundlTaskStatusEnum.COMPUTED.getCode())){
            throw new BusinessErrorException("任务状态已变化,请返回列表查看");
        }
        if (maxVersion <= taskInfo.getVersion()) {
            throw new AmisBadRequestException("只能调整" + maxVersion + "次");
        }
        taskInfo.setVersion(version + 1);
        TaskHeadDTO taskHeadDTO = new TaskHeadDTO();
        taskHeadDTO.setTaskId(taskId);
        taskHeadDTO.setVersion(taskInfo.getVersion()+"");
        taskHeadDTO.setEnvironment(environment);
        BundlTaskBdpDTO bundlTaskBdpDTO = new BundlTaskBdpDTO();
        bundlTaskBdpDTO.setHead(taskHeadDTO);
        ResponseEntity<BdpResponseDTO> responseEntity = sendService.sendAdjustBdp(bundlTaskBdpDTO);
        if(Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody())){
            return null;
        }
        BdpResponseDTO bdpResponseDTO = responseEntity.getBody();
        logger.info("adjustBdpTrackResult|bdpResponseDTO:{}.", JSONObject.toJSONString(bdpResponseDTO));
        if(ResponseCodeEnum.ERROR.getCode().equals(bdpResponseDTO.getCode())){
            throw new BusinessErrorException(ErrorCodeEnum.TASK_BDP_ERROR);
        }
        if(ResponseCodeEnum.SUCCESS.getCode().equals(bdpResponseDTO.getCode())){
            bdpResponseDTO.setMsg("成功提交大数据平台复盘，预计1小时可返回复盘结果，请耐心等待。");
            taskInfo.setTaskStatus(BundlTaskStatusEnum.COMPUTING.getCode());
            bundlingTaskInfoMapper.updateByPrimaryKeySelective(taskInfo);
        }else {
            bdpResponseDTO.setMsg(String.format(ResponseCodeEnum.COMPUTING.getMessage(), taskInfo.getTaskCode()));
        }
        return bdpResponseDTO;
    }

    private List<BundlingTaskInfo> getTaskInfos(List<Byte> taskStatus) {
        BundlTaskListParam bundlTaskListParam = new BundlTaskListParam();
        bundlTaskListParam.setTaskStatus(taskStatus);
        List<BundlingTaskInfo> bundlingTaskInfos = bundlingTaskInfoExtendMapper.searchBundlTaskInfoList(bundlTaskListParam);
        return bundlingTaskInfos;
    }

    /**
     * 校验文件格式
     * @param file
     */
    private void checkFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessErrorException("导入文件为空");
        }
        //获取文件名+后缀
        String filename = file.getOriginalFilename();
        if (filename != null) {
            //获取其后缀
            String extension = filename.substring(filename.lastIndexOf(".") + 1);
            if (!(extension.equals("xls") || extension.equals("xlsx"))) {
                //此处为自定义异常捕获,可使用其他方式
                throw new BusinessErrorException("文件格式有误,请检查上传文件格式!!");
            }
        }
    }

    @Override
    public ImportResult importAdjustNewStoreSuggest(MultipartFile file, Long taskId, Byte taskType, TokenUserDTO userDTO) throws Exception {
        checkFile(file);
        String key = String.format(NEW_STORE_GOODS_IMPORT, taskId);
        ImportResult importResult = getOperationPermissions(key);
        if (importResult != null){
            throw new BusinessErrorException("任务正在进行中,请稍后重试。");
        }
        ImportResult result = new ImportResult();
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        String s = ExcelCheck.checkExcelPattern(file, new NewStoreGoodsData(),CollNameList);
        if (StringUtils.isNotEmpty(s)){
            throw new BusinessErrorException(s);
        }
        result.setCode("9999");
        result.setMessage("任务进行中");
        rBucket.set(result,12, TimeUnit.HOURS);
        if (!BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(taskType)) {
            return getImportResult();
        }
        BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(bundlingTaskInfo) || !BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
            throw new BusinessErrorException(ErrorCodeEnum.TASK_ID_NOTEXIT.getMsg());
        }
        if (BundlTaskStatusEnum.COMPUTED.getCode() != bundlingTaskInfo.getTaskStatus().byteValue()){
            throw new BusinessErrorException("只有完成状态才可以调整");
        }
        BundlingTaskStoreDetail detail = bundlingTaskStoreDetailExtendMapper.searchBundlTaskStoreListByTaskId(taskId).stream().findFirst().orElseThrow(() -> new BusinessErrorException("没有查询到新开门店任务信息"));
        List<String> storeLicense = getStoreLicense(detail.getBusinessId(), detail.getStoreId());
        // 取平台的经营属性
        RuleParam ruleParam = new RuleParam();
        ruleParam.setScopeCode("BB_2");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(ruleParam, null);
        Map<String, String> goodslineMap = ruleEnum.get("goodsline").stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        ConfigOrg configOrg = configOrgExtendMapper.selectConfigByOrgId(bundlingTaskInfo.getOrgId(), ConfigTypeEnum.BB.getType(), null);
        List<String> goodslineList = new ArrayList<>();
        if (Objects.nonNull(configOrg)) {
            goodslineList.addAll(configOrgDetailExMapper.selectByConfigIdAndDictCodes(configOrg.getId(), Lists.newArrayList("goodsline")).stream().map(v -> goodslineMap.get(v.getPerprotyValue())).distinct().collect(Collectors.toList()));
        }

        List<NewStoreGoodsData> excelList;
        try (InputStream in = file.getInputStream()) {
            ImportResult finalResult = result;
            LimitExcelReadListener limitExcelReadListener = new LimitExcelReadListener(importNum);
            excelList = EasyExcel.read(in).headRowNumber(2).autoTrim(true).head(NewStoreGoodsData.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            asyncImportTrackResultGoodsConfig.newStoreGoods(excelList,taskId, bundlingTaskInfo, detail, storeLicense, goodslineList, finalResult, key, rBucket);

            result = ImportResult.buildDefaultImportResult(key);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage("导入文件失败");
            rBucket.set(result, 12, TimeUnit.HOURS);
            logger.error("导入文件失败", e);
            if (e instanceof BusinessErrorException) {
                throw new BusinessErrorException("导入行数不能超过"+importNum);
            }else {
                throw new BusinessErrorException("400",e.getMessage());
            }
        }
        return result;
    }

}
