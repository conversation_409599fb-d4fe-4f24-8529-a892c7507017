package com.cowell.scib.service.impl;

import com.beust.jcommander.internal.Lists;
import com.cowell.permission.vo.RoleInfoDetailsVO;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.ExtendFiledConstants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entity.ReachModule;
import com.cowell.scib.enums.DevelopTypeEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.SelectApiEnum;
import com.cowell.scib.enums.UseStatusEnum;
import com.cowell.scib.mapperDgms.ReachModuleMapper;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.service.DevelopModuleReadService;
import com.cowell.scib.service.DevelopModuleRecordFileService;
import com.cowell.scib.service.DevelopModuleRecordService;
import com.cowell.scib.service.DevelopModuleService;
import com.cowell.scib.service.dto.DevelopModuleDTO;
import com.cowell.scib.service.dto.DevelopModuleRecordDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.feign.PermissionFeignClient;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.param.DevelopSelectParam;
import com.cowell.scib.service.vo.DevelopModuleRecordVO;
import com.cowell.scib.service.vo.DevelopModuleVO;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.TabResult;
import com.cowell.scib.utils.DateUtils;
import com.cowell.scib.utils.ExtendUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/29 15:09
 */
@Slf4j
@Service
public class DevelopModuleReadServiceImpl extends DevelopChechService implements DevelopModuleReadService {

    @Value("${develop.reminder.limit:3}")
    private int reminderLimit;
    @Value("${develop.edit.role:}")
    private String editRole;
    @Value("${develop.module.sort:}")
    private String moduleSort;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PermissionFeignClient permissionService;
    @Autowired
    private DevelopModuleService developModuleService;
    @Autowired
    private DevelopModuleRecordService developModuleRecordService;
    @Autowired
    private DevelopModuleRecordFileService recordFileService;
    @Autowired
    private ReachModuleMapper reachModuleMapper;

    @Override
    public List<TabResult> listModuleVO(Integer useStatus) {
        List<DevelopModuleDTO> moduleDTOList=new ArrayList<>();
        if (null != useStatus) {
            moduleDTOList = developModuleService.listDevelopModuleByCondition(null, UseStatusEnum.START.getCode());
        } else {
            moduleDTOList = developModuleService.listDevelopModuleByCondition(null,null);
        }
        if(CollectionUtils.isEmpty(moduleDTOList)){
            return Lists.newArrayList();
        }
        return moduleDTOList.stream().map(v->{
            TabResult moduleVO = new TabResult();
            moduleVO.setId(Long.valueOf(v.getId()));
            moduleVO.setContent(v.getModuleCode());
            moduleVO.setTitle(v.getModuleName());
            moduleVO.setUseStatus(UseStatusEnum.getUseStatusName(v.getUseStatus()));
            moduleVO.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate()));
            BeanUtils.copyProperties(v, moduleVO);
            return moduleVO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<DevelopModuleRecordVO> listPageModuleRecordVO(DevelopListParam developListParam) {
        checkModuleDetailParam(developListParam);
        return developModuleRecordService.listPageDevelopModuleRecordByCode(developListParam);
    }

    @Override
    public DevelopModuleVO moduleDetail(DevelopListParam developListParam) {
        checkModuleDetailParam(developListParam);
        List<DevelopModuleDTO> developModuleDTOList = developModuleService.listDevelopModuleByCondition(Lists.newArrayList(developListParam.getModuleCode()), null);
        if(CollectionUtils.isEmpty(developModuleDTOList)){
            throw new AmisBadRequestException(ErrorCodeEnum.DEVELOP_MODULE_DATA_NULL);
        }

        return developModuleDTOList.stream().map(v->{
            DevelopModuleVO moduleVO = new DevelopModuleVO();
            BeanUtils.copyProperties(v, moduleVO);
            return moduleVO;
        }).findFirst().get();
    }

    @Override
    public DevelopModuleRecordVO recordDetail(DevelopListParam developListParam) {
        checkRecordDetailParam(developListParam);
        DevelopModuleRecordDTO developModuleRecordDTO = developModuleRecordService.detailDevelopModuleRecordById(developListParam.getRecordId());
        DevelopModuleRecordVO recordVO = new DevelopModuleRecordVO();
        if(Objects.nonNull(developModuleRecordDTO)){
            BeanUtils.copyProperties(developModuleRecordDTO, recordVO);
            recordVO.setDevelopTime(String.valueOf(DateUtils.transeMilliSecond(developModuleRecordDTO.getDevelopTime(), DateUtils.DATE_PATTERN)));
            recordVO.setRecordId(developModuleRecordDTO.getId());
            recordVO.setFileVOList(recordFileService.fileUrlListToString(developModuleRecordDTO.getFileList()));
            if(StringUtils.isNotBlank(developModuleRecordDTO.getExtend())){
                recordVO.setDevelopUrl(ExtendUtil.getExtendValue(developModuleRecordDTO.getExtend(), ExtendFiledConstants.SCIB_DEVELOP_URL, String.class));
            }
            if(StringUtils.isNotBlank(developModuleRecordDTO.getReachGroupIdList())){
                String groupId = developModuleRecordDTO.getReachGroupIdList().split(",")[0];
                ReachModule reachModule = reachModuleMapper.selectByPrimaryKey(Long.valueOf(groupId));
                if(null != reachModule){
                    recordVO.setSelectMethod(reachModule.getSelectMethod());
                }
            }
        }
        return recordVO;
    }

    @Override
    public List<OptionDto> developSelectUnifyList(DevelopSelectParam param) {
        switch (SelectApiEnum.getEnum(param.getApiCode())) {
            case DEVELOP_TYPE:
                return Arrays.stream(DevelopTypeEnum.values()).map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
            case MODULE:
                return listModuleVO(Integer.valueOf(UseStatusEnum.START.getCode())).stream().map(typeEnum -> new OptionDto(typeEnum.getTitle(), String.valueOf(typeEnum.getContent()))).collect(Collectors.toList());
            default:
                break;
        }
        return null;
    }

    @Override
    public DevelopModuleRecordVO recordDetailReminder(String moduleCode, TokenUserDTO userDTO) {
        if(StringUtils.isBlank(moduleCode)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        String redisKey = new StringBuilder().append(RedisConstant.DEVELOP_RECORD_REMINDER_KEY).append(userDTO.getUserId()).append(Constants.KLINE).append(moduleCode).append(Constants.KLINE).append(DateUtils.conventDateStrByPattern(new Date(), DateUtils.DATE_PATTERN)).toString();
        RBucket<Integer> remindCount = redissonClient.getBucket(redisKey);
        log.info("recordDetailReminder|redisKey:{}.remindCount:{}.", redisKey, remindCount!=null?remindCount.get():0);
        if (remindCount.get() != null && remindCount.get() >= reminderLimit) {
            log.info("不提醒");
            return null;
        }
        DevelopModuleRecordVO developModuleRecordVO = developModuleRecordService.recentlyRecord(moduleCode);
        if(Objects.nonNull(developModuleRecordVO)){
            RBucket<Integer> exitRemindCount = redissonClient.getBucket(redisKey);
            exitRemindCount.setAsync(Optional.ofNullable(exitRemindCount.get()).orElse(0) + 1, RedisConstant.DEVELOP_RECORD_REMINDER_TIME, TimeUnit.DAYS);
            return developModuleRecordVO;
        }
        return null;
    }

    @Override
    public Boolean editAblePermission() {
        ResponseEntity<List<RoleInfoDetailsVO>> responseEntity = null;
        try {
            responseEntity = permissionService.getUserRole();
        } catch (Exception e) {
            log.error("DevelopModuleReadServiceImpl|editAblePermission|error",e);
            return false;
        }
        if (responseEntity == null) {
            log.warn("返回值为空|responseEntity");
            return false;
        }

        if (responseEntity.getStatusCodeValue() != HttpStatus.OK.value() && responseEntity.getStatusCodeValue() != HttpStatus.CREATED.value()) {
            log.warn("返回值状态有误");
            return false;
        }
        List<RoleInfoDetailsVO> roleInfoDetailsVOS =responseEntity.getBody();
        if(CollectionUtils.isEmpty(roleInfoDetailsVOS)){
            log.warn("返回内容为空");
            return false;
        }
        roleInfoDetailsVOS = roleInfoDetailsVOS.stream().filter(v->editRole.equals(v.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleInfoDetailsVOS)){
            log.warn("返回内容没有相关角色：{}", editRole);
            return false;
        }
        return true;
    }

    @Override
    public void delReminderKey(String redisKey) {
        RBucket<Integer> remindCount = redissonClient.getBucket(redisKey);
        log.info("delReminderKey|redisKey:{}.remindCount:{}.", redisKey, remindCount!=null?remindCount.get():0);
        if (remindCount.get() != null) {
            log.info("清除缓存");
            redissonClient.getBucket(redisKey).getAndDelete();
        }
        RBucket<Integer> remindCount2 = redissonClient.getBucket(redisKey);
        log.info("delReminderKey|remindCount2:{}.", remindCount2!=null?remindCount2.get():0);
    }
}
