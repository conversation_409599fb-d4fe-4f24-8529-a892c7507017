package com.cowell.scib.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.cache.CacheService;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityTidb.DgmsSingleStoreRecommend;
import com.cowell.scib.entityTidb.DgmsSingleStoreRecommendExample;
import com.cowell.scib.mapperTidb.DgmsSingleStoreRecommendMapper;
import com.cowell.scib.mq.producer.GoodsCommissionProducer;
import com.cowell.scib.service.DgmsSingleStoreRecommendService;
import com.cowell.scib.service.GoodsCommissionService;
import com.cowell.scib.service.StoreService;
import com.cowell.scib.service.dto.IscmGoodsCategoryDTO;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.singleStoreSuggest.PerformanceRuleGoodsDTO;
import com.google.common.collect.Lists;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsCommissionServiceImpl implements GoodsCommissionService {

    @Autowired
    private GoodsCommissionProducer goodsCommissionProducer;
    @Autowired
    @Qualifier("trackResultFileUploadExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Autowired
    private DgmsSingleStoreRecommendMapper dgmsSingleStoreRecommendMapper;

    @Override
    public void queryGoodsCommionService() {
        try {
            DgmsSingleStoreRecommendExample dgmsSingleStoreRecommendExample = new DgmsSingleStoreRecommendExample();
            dgmsSingleStoreRecommendExample.createCriteria();
            Long countNum = dgmsSingleStoreRecommendMapper.countByExample(dgmsSingleStoreRecommendExample);
            if(countNum == 0){
                log.info("获取提成数据为空");
                return ;
            }
            asyncTaskExecutor.execute(() -> {
                try {
                    AtomicLong count=new AtomicLong();
                    int num = (countNum.intValue()-1) / Constants.SELECT_MAX_SIZE + 1;
                    for(int page = 0; page < num; page++){
                        log.info("queryGoodsCommionService 第{}页",page);
                        dgmsSingleStoreRecommendExample.setLimit(Constants.SELECT_MAX_SIZE);
                        dgmsSingleStoreRecommendExample.setOffset(Long.valueOf(page) * Constants.SELECT_MAX_SIZE);
                        List<DgmsSingleStoreRecommend>  dgmsSingleStoreRecommends = dgmsSingleStoreRecommendMapper.selectByExample(dgmsSingleStoreRecommendExample);
                        if(CollectionUtils.isEmpty(dgmsSingleStoreRecommends)){
                            log.info("查询为空处理完成");
                            break;
                        }
                        List<String> codeList = dgmsSingleStoreRecommends.parallelStream().map(v -> v.getStoreCode()).distinct().collect(Collectors.toList());
                        Map<String, OrgInfoBaseCache> finalMdmStoreBaseDTOHashMap = CacheVar.getStoreByStoreCodeList(codeList).get().stream().collect(Collectors.toMap(v -> v.getSapCode(), Function.identity(), (k1, k2) -> k1));
                        List<PerformanceRuleGoodsDTO> performanceRuleGoodsDTOS = new ArrayList<>();
                        dgmsSingleStoreRecommends.stream().forEach(v->{
                            if (MapUtils.isNotEmpty(finalMdmStoreBaseDTOHashMap) && Objects.nonNull(finalMdmStoreBaseDTOHashMap.get(v.getStoreCode()))) {
                                PerformanceRuleGoodsDTO performanceRuleGoodsDTO = new PerformanceRuleGoodsDTO();
                                performanceRuleGoodsDTO.setGoodsNo(v.getGoodsNo());
                                OrgInfoBaseCache mdmStoreBaseDTO = finalMdmStoreBaseDTOHashMap.get(v.getStoreCode());
                                performanceRuleGoodsDTO.setStoreId(mdmStoreBaseDTO.getOutId());
                                performanceRuleGoodsDTO.setBusinessId(mdmStoreBaseDTO.getBusinessId());
                                performanceRuleGoodsDTO.setDataSource(1);
                                performanceRuleGoodsDTOS.add(performanceRuleGoodsDTO);
                                count.incrementAndGet();

                            }
                        });
                        ThreadUtil.sleep(500);
                        List<List<PerformanceRuleGoodsDTO>> partition1 = Lists.partition(performanceRuleGoodsDTOS, 5);
                        for (List<PerformanceRuleGoodsDTO> ruleGoodsDTOS : partition1) {
                            if(CollectionUtils.isNotEmpty(ruleGoodsDTOS)){
                                goodsCommissionProducer.send(JSON.toJSONString(ruleGoodsDTOS));
                            }
                        }
                        log.info("queryGoodsCommionService 第{}页 处理完成  查询数量={} 发送数量={}",page, dgmsSingleStoreRecommends.size(),performanceRuleGoodsDTOS.size());
                        performanceRuleGoodsDTOS.clear();
                        dgmsSingleStoreRecommends.clear();
                    }
                    log.info("queryGoodsCommionService 处理总数量count={}",count.get());
                }catch (Exception e){
                    log.info("获取和推送提成数据失败!");
                    throw e;
                }
            });
        } catch (Exception e) {
            log.info("处理提成数据失败!");
            throw e;
        }
    }
}
