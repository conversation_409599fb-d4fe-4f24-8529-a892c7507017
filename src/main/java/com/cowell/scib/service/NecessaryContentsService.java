package com.cowell.scib.service;

import com.alibaba.fastjson.JSONObject;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.entityDgms.MdmTaskDetail;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.necessaryContents.*;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.SelectorResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface NecessaryContentsService {

    /**
     * 手工添加商品key
     */
    String NECESSARY_ADD_GOODS_CACHE_KEY = "SCIB_NECESSARY-ADD-GOODS-";

    /**
     * 判断添加是否完成key
     */
    String NECESSARY_FINISHED_GOODS_CACHE_KEY = "SCIB_NECESSARY-FINISHED-GOODS-";

    /**
     * 判断添加是否完成key
     */
    String COPY_STORE_INFO = "SCIB_COPY-STORE-INFO-";

    /**
     * 批量门店复制key
     */
    String BATCH_IMPORT_COPY_STORE = "SCIB_BATCH-IMPORT-COPY-STORE-";


    /**
     * 分页查询六级必备目录
     * @param userDTO
     * @return
     * @throws Exception
     */
    PageResult<NecessaryCommonDTO> getContents(TokenUserDTO userDTO, NecessaryQueryParam param);

    String addNecessaryGoods(TokenUserDTO userDTO, NecessaryAddParam param);

    void modifyNecessaryGroup(TokenUserDTO userDTO, NecessaryAddParam param);

    /**
     * 删除必备目录
     * @param userDTO
     * @param param
     * @throws Exception
     */
    String delNecessaryGoods(TokenUserDTO userDTO, NecessaryDelParam param);

    void consumerAddNecessary(NecessaryAddMqDTO addMqDTO) throws Exception;

    List<MdmStoreExDTO> getPlatformStoreInfo(Long platformOrgId, List<Long> companyOrgIds, Long userId);

    List<Long> getStoreGoodsIds(Integer count);

    List<Long> getMdmTaskDetailIds(Integer count);

    List<Long> getTrackResultAllDetailIds(Integer count);

    void pushMdm(List<MdmTaskDetail> taskDetails) throws Exception;

    void exportNecessaryContent(TokenUserDTO userDTO, NecessaryQueryParam para);

    MdmStoreExDTO getCopyStoreInfo(Long storeId);

    PageResult<StoreGoodsInfoDTO> getSourceStoreInfos(StoreGoodsQueryParam param, TokenUserDTO userDTO);

    String copyStoreGoods(TokenUserDTO userDTO, NecessaryCopyParam param);

    PageResult<MdmTaskDTO> getMdmTaskList(MdmTaskQueryParam queryParam, TokenUserDTO userDTO);

    PageResult<MdmTaskDetailDTO> getMdmTaskErrorList(MdmTaskErrorQueryParam queryParam, TokenUserDTO userDTO);

    void exportStoreGoodsInfo(TokenUserDTO userDTO, NecessaryQueryParam param);

    String listGroupGoods(Long platFormOrgId, Long taskId);

    List<Map<String, String>> listPlatformGoods(Long platFormOrgId, Long taskId, List<TaskStoreDTO> taskStoreDTOList);

    List<Map<String, List<Map<String, String>>>> listCompanyGoods(Long platFormOrgId, List<String> companyOrgIdList);


    /**
     * 判断用户是否拥有传入机构所有权限
     *
     * @param orgIds 传入机构
     * @param userId 当前用户
     * @return 有所有权限的机构id
     */
    List<Long> isFullScopeByOrgId(List<Long> orgIds, Long userId);

    NecessaryCityDTO getCityByCompany(TokenUserDTO userDTO, Long businessId);

    String refushUpdate(Long taskId, Integer taskSource, TokenUserDTO userDTO);

    SelectorResult listSearch(Integer searchType);

    void permOrgCacheInit();

    void receiveMdmStoreGoodsInfo(JSONObject param);

    void manualPushMdm(Long taskId);

    void updateCopyStoreGoodsStatus(Long sourceStoreId, String goodsNo, Byte effectStatus, TokenUserDTO userDTO);

    List<OrgDTO> getStoreByStoreType(Long companyOrgId, String storeType);

    List<InitImportNecessaryDTO> initNecessary(TokenUserDTO userDTO, Byte necessaryTag, MultipartFile file);

    /**
     * 校验该平台是否可以进行增删改查
     *
     * @param platformOrgId
     * @return
     */
    boolean checkModifyAble(Long platformOrgId);

    void receiveInitMdmPush(JSONObject param);

    Long refreshStoreGoods(NecessaryRefreshParam param, TokenUserDTO userDTO) throws Exception;

    void importRefreshStoreGoods(MultipartFile file, Integer necessaryTag, String necessaryTagName, Integer sourceNecessaryTag, Boolean pushMdmAble, TokenUserDTO userDTO) throws Exception;

    NecessaryDirectionsDTO getDirections(NecessaryDirectionsParam param, TokenUserDTO userDTO);

    List<OrgInfoBaseCache> getPlatformBusinessInfo(String storeIds);

    List<String> importGoods(Long businessId, Byte necessaryTag, MultipartFile file);

    // 获取店型
    SelectorResult getStoreTypeList(Long platformId, Byte necessaryTag, Long companyOrgId, String citys, TokenUserDTO userDTO);

    void deleteStoreGoodsByStoreCode(List<Long> orgIds, TokenUserDTO userDTO,List<OrgInfoBaseCache> orgInfoBaseCaches);

    String importCopyStore(MultipartFile file, TokenUserDTO userDTO);

    CommonProcessDTO<List<ImportStoreDTO>> getImportProcess(TokenUserDTO userDTO);

    String batchImportGoods(MultipartFile file, Byte necessaryTag, Long platformId, Long companyOrgId, String city, TokenUserDTO userDTO);

    CommonProcessDTO getBatchImportProcess(Long platformId, Long companyOrgId, String city, Byte necessaryTag, TokenUserDTO userDTO);
}
