package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 必备目录管理表
 */
public class NecessaryContents implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 必备标签
     */
    private String necessaryTag;

    /**
     * 必备标签名称
     */
    private String necessaryTagName;

    /**
     * orgid
     */
    private Long orgId;

    /**
     * org名称
     */
    private String orgName;

    /**
     * 组织层级
     */
    private Integer orgType;

    /**
     * 平台orgid
     */
    private Long platformOrgId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 公司orgId
     */
    private Long companyOrgId;

    /**
     * 连锁id
     */
    private Long businessId;

    /**
     * 公司MDM编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 门店orgID
     */
    private Long storeOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 店型
     */
    private String storeType;

    /**
     * 店型名称
     */
    private String storeTypeName;

    /**
     * 店型对应的属性
     */
    private String storeTypeProp;

    private String storeTypePropName;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String area;

    /**
     * 大类id
     */
    private Long categoryId;

    /**
     * 大类名称
     */
    private String categoryName;

    /**
     * 中类id
     */
    private Long middleCategoryId;

    /**
     * 中类名称
     */
    private String middleCategoryName;

    /**
     * 小类编码
     */
    private Long smallCategoryId;

    /**
     * 小类名称
     */
    private String smallCategoryName;

    /**
     * 子类编码
     */
    private Long subCategoryId;

    /**
     * 子类名称
     */
    private String subCategoryName;

    /**
     * 成分
     */
    private String composition;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品通用名
     */
    private String goodsCommonName;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 采购属性
     */
    private String purchaseAttr;

    /**
     * 入选原因
     */
    private String chooseReason;

    /**
     * 作废原因
     */
    private String invalidReason;

    /**
     * 门店动销率
     */
    private BigDecimal storeSalesRate;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(String necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public String getNecessaryTagName() {
        return necessaryTagName;
    }

    public void setNecessaryTagName(String necessaryTagName) {
        this.necessaryTagName = necessaryTagName;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getStoreTypeName() {
        return storeTypeName;
    }

    public void setStoreTypeName(String storeTypeName) {
        this.storeTypeName = storeTypeName;
    }

    public String getStoreTypeProp() {
        return storeTypeProp;
    }

    public void setStoreTypeProp(String storeTypeProp) {
        this.storeTypeProp = storeTypeProp;
    }

    public String getStoreTypePropName() {
        return storeTypePropName;
    }

    public void setStoreTypePropName(String storeTypePropName) {
        this.storeTypePropName = storeTypePropName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getMiddleCategoryId() {
        return middleCategoryId;
    }

    public void setMiddleCategoryId(Long middleCategoryId) {
        this.middleCategoryId = middleCategoryId;
    }

    public String getMiddleCategoryName() {
        return middleCategoryName;
    }

    public void setMiddleCategoryName(String middleCategoryName) {
        this.middleCategoryName = middleCategoryName;
    }

    public Long getSmallCategoryId() {
        return smallCategoryId;
    }

    public void setSmallCategoryId(Long smallCategoryId) {
        this.smallCategoryId = smallCategoryId;
    }

    public String getSmallCategoryName() {
        return smallCategoryName;
    }

    public void setSmallCategoryName(String smallCategoryName) {
        this.smallCategoryName = smallCategoryName;
    }

    public Long getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Long subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(String subCategoryName) {
        this.subCategoryName = subCategoryName;
    }

    public String getComposition() {
        return composition;
    }

    public void setComposition(String composition) {
        this.composition = composition;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getPurchaseAttr() {
        return purchaseAttr;
    }

    public void setPurchaseAttr(String purchaseAttr) {
        this.purchaseAttr = purchaseAttr;
    }

    public String getChooseReason() {
        return chooseReason;
    }

    public void setChooseReason(String chooseReason) {
        this.chooseReason = chooseReason;
    }

    public String getInvalidReason() {
        return invalidReason;
    }

    public void setInvalidReason(String invalidReason) {
        this.invalidReason = invalidReason;
    }

    public BigDecimal getStoreSalesRate() {
        return storeSalesRate;
    }

    public void setStoreSalesRate(BigDecimal storeSalesRate) {
        this.storeSalesRate = storeSalesRate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        NecessaryContents other = (NecessaryContents) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getNecessaryTag() == null ? other.getNecessaryTag() == null : this.getNecessaryTag().equals(other.getNecessaryTag()))
            && (this.getNecessaryTagName() == null ? other.getNecessaryTagName() == null : this.getNecessaryTagName().equals(other.getNecessaryTagName()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getOrgType() == null ? other.getOrgType() == null : this.getOrgType().equals(other.getOrgType()))
            && (this.getPlatformOrgId() == null ? other.getPlatformOrgId() == null : this.getPlatformOrgId().equals(other.getPlatformOrgId()))
            && (this.getPlatformName() == null ? other.getPlatformName() == null : this.getPlatformName().equals(other.getPlatformName()))
            && (this.getCompanyOrgId() == null ? other.getCompanyOrgId() == null : this.getCompanyOrgId().equals(other.getCompanyOrgId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getStoreOrgId() == null ? other.getStoreOrgId() == null : this.getStoreOrgId().equals(other.getStoreOrgId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getStoreType() == null ? other.getStoreType() == null : this.getStoreType().equals(other.getStoreType()))
            && (this.getStoreTypeName() == null ? other.getStoreTypeName() == null : this.getStoreTypeName().equals(other.getStoreTypeName()))
            && (this.getStoreTypeProp() == null ? other.getStoreTypeProp() == null : this.getStoreTypeProp().equals(other.getStoreTypeProp()))
            && (this.getStoreTypePropName() == null ? other.getStoreTypePropName() == null : this.getStoreTypePropName().equals(other.getStoreTypePropName()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getArea() == null ? other.getArea() == null : this.getArea().equals(other.getArea()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()))
            && (this.getCategoryName() == null ? other.getCategoryName() == null : this.getCategoryName().equals(other.getCategoryName()))
            && (this.getMiddleCategoryId() == null ? other.getMiddleCategoryId() == null : this.getMiddleCategoryId().equals(other.getMiddleCategoryId()))
            && (this.getMiddleCategoryName() == null ? other.getMiddleCategoryName() == null : this.getMiddleCategoryName().equals(other.getMiddleCategoryName()))
            && (this.getSmallCategoryId() == null ? other.getSmallCategoryId() == null : this.getSmallCategoryId().equals(other.getSmallCategoryId()))
            && (this.getSmallCategoryName() == null ? other.getSmallCategoryName() == null : this.getSmallCategoryName().equals(other.getSmallCategoryName()))
            && (this.getSubCategoryId() == null ? other.getSubCategoryId() == null : this.getSubCategoryId().equals(other.getSubCategoryId()))
            && (this.getSubCategoryName() == null ? other.getSubCategoryName() == null : this.getSubCategoryName().equals(other.getSubCategoryName()))
            && (this.getComposition() == null ? other.getComposition() == null : this.getComposition().equals(other.getComposition()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getBarCode() == null ? other.getBarCode() == null : this.getBarCode().equals(other.getBarCode()))
            && (this.getGoodsCommonName() == null ? other.getGoodsCommonName() == null : this.getGoodsCommonName().equals(other.getGoodsCommonName()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getGoodsUnit() == null ? other.getGoodsUnit() == null : this.getGoodsUnit().equals(other.getGoodsUnit()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getSpecifications() == null ? other.getSpecifications() == null : this.getSpecifications().equals(other.getSpecifications()))
            && (this.getDosageForm() == null ? other.getDosageForm() == null : this.getDosageForm().equals(other.getDosageForm()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getApprovalNumber() == null ? other.getApprovalNumber() == null : this.getApprovalNumber().equals(other.getApprovalNumber()))
            && (this.getPurchaseAttr() == null ? other.getPurchaseAttr() == null : this.getPurchaseAttr().equals(other.getPurchaseAttr()))
            && (this.getChooseReason() == null ? other.getChooseReason() == null : this.getChooseReason().equals(other.getChooseReason()))
            && (this.getInvalidReason() == null ? other.getInvalidReason() == null : this.getInvalidReason().equals(other.getInvalidReason()))
            && (this.getStoreSalesRate() == null ? other.getStoreSalesRate() == null : this.getStoreSalesRate().equals(other.getStoreSalesRate()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNecessaryTag() == null) ? 0 : getNecessaryTag().hashCode());
        result = prime * result + ((getNecessaryTagName() == null) ? 0 : getNecessaryTagName().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getOrgType() == null) ? 0 : getOrgType().hashCode());
        result = prime * result + ((getPlatformOrgId() == null) ? 0 : getPlatformOrgId().hashCode());
        result = prime * result + ((getPlatformName() == null) ? 0 : getPlatformName().hashCode());
        result = prime * result + ((getCompanyOrgId() == null) ? 0 : getCompanyOrgId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getStoreOrgId() == null) ? 0 : getStoreOrgId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getStoreType() == null) ? 0 : getStoreType().hashCode());
        result = prime * result + ((getStoreTypeName() == null) ? 0 : getStoreTypeName().hashCode());
        result = prime * result + ((getStoreTypeProp() == null) ? 0 : getStoreTypeProp().hashCode());
        result = prime * result + ((getStoreTypePropName() == null) ? 0 : getStoreTypePropName().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getArea() == null) ? 0 : getArea().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getCategoryName() == null) ? 0 : getCategoryName().hashCode());
        result = prime * result + ((getMiddleCategoryId() == null) ? 0 : getMiddleCategoryId().hashCode());
        result = prime * result + ((getMiddleCategoryName() == null) ? 0 : getMiddleCategoryName().hashCode());
        result = prime * result + ((getSmallCategoryId() == null) ? 0 : getSmallCategoryId().hashCode());
        result = prime * result + ((getSmallCategoryName() == null) ? 0 : getSmallCategoryName().hashCode());
        result = prime * result + ((getSubCategoryId() == null) ? 0 : getSubCategoryId().hashCode());
        result = prime * result + ((getSubCategoryName() == null) ? 0 : getSubCategoryName().hashCode());
        result = prime * result + ((getComposition() == null) ? 0 : getComposition().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getBarCode() == null) ? 0 : getBarCode().hashCode());
        result = prime * result + ((getGoodsCommonName() == null) ? 0 : getGoodsCommonName().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getGoodsUnit() == null) ? 0 : getGoodsUnit().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getSpecifications() == null) ? 0 : getSpecifications().hashCode());
        result = prime * result + ((getDosageForm() == null) ? 0 : getDosageForm().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getApprovalNumber() == null) ? 0 : getApprovalNumber().hashCode());
        result = prime * result + ((getPurchaseAttr() == null) ? 0 : getPurchaseAttr().hashCode());
        result = prime * result + ((getChooseReason() == null) ? 0 : getChooseReason().hashCode());
        result = prime * result + ((getInvalidReason() == null) ? 0 : getInvalidReason().hashCode());
        result = prime * result + ((getStoreSalesRate() == null) ? 0 : getStoreSalesRate().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", necessaryTag=").append(necessaryTag);
        sb.append(", necessaryTagName=").append(necessaryTagName);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", orgType=").append(orgType);
        sb.append(", platformOrgId=").append(platformOrgId);
        sb.append(", platformName=").append(platformName);
        sb.append(", companyOrgId=").append(companyOrgId);
        sb.append(", businessId=").append(businessId);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", storeOrgId=").append(storeOrgId);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeName=").append(storeName);
        sb.append(", storeType=").append(storeType);
        sb.append(", storeTypeName=").append(storeTypeName);
        sb.append(", storeTypeProp=").append(storeTypeProp);
        sb.append(", storeTypePropName=").append(storeTypePropName);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", area=").append(area);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", categoryName=").append(categoryName);
        sb.append(", middleCategoryId=").append(middleCategoryId);
        sb.append(", middleCategoryName=").append(middleCategoryName);
        sb.append(", smallCategoryId=").append(smallCategoryId);
        sb.append(", smallCategoryName=").append(smallCategoryName);
        sb.append(", subCategoryId=").append(subCategoryId);
        sb.append(", subCategoryName=").append(subCategoryName);
        sb.append(", composition=").append(composition);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", barCode=").append(barCode);
        sb.append(", goodsCommonName=").append(goodsCommonName);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsUnit=").append(goodsUnit);
        sb.append(", description=").append(description);
        sb.append(", specifications=").append(specifications);
        sb.append(", dosageForm=").append(dosageForm);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", approvalNumber=").append(approvalNumber);
        sb.append(", purchaseAttr=").append(purchaseAttr);
        sb.append(", chooseReason=").append(chooseReason);
        sb.append(", invalidReason=").append(invalidReason);
        sb.append(", storeSalesRate=").append(storeSalesRate);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}