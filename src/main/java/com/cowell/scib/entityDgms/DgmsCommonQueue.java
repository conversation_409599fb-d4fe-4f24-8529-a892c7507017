package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 数字化商品通用队列表表
 */
public class DgmsCommonQueue implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * key
     */
    private String queueKey;

    /**
     * 线程id
     */
    private String queueValue;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQueueKey() {
        return queueKey;
    }

    public void setQueueKey(String queueKey) {
        this.queueKey = queueKey;
    }

    public String getQueueValue() {
        return queueValue;
    }

    public void setQueueValue(String queueValue) {
        this.queueValue = queueValue;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DgmsCommonQueue other = (DgmsCommonQueue) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getQueueKey() == null ? other.getQueueKey() == null : this.getQueueKey().equals(other.getQueueKey()))
            && (this.getQueueValue() == null ? other.getQueueValue() == null : this.getQueueValue().equals(other.getQueueValue()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getQueueKey() == null) ? 0 : getQueueKey().hashCode());
        result = prime * result + ((getQueueValue() == null) ? 0 : getQueueValue().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", queueKey=").append(queueKey);
        sb.append(", queueValue=").append(queueValue);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}