package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlStoreSkuLimitAdjustEffectExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlStoreSkuLimitAdjustEffectExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAdjustIdIsNull() {
            addCriterion("adjust_id is null");
            return (Criteria) this;
        }

        public Criteria andAdjustIdIsNotNull() {
            addCriterion("adjust_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustIdEqualTo(Long value) {
            addCriterion("adjust_id =", value, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdNotEqualTo(Long value) {
            addCriterion("adjust_id <>", value, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdGreaterThan(Long value) {
            addCriterion("adjust_id >", value, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdGreaterThanOrEqualTo(Long value) {
            addCriterion("adjust_id >=", value, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdLessThan(Long value) {
            addCriterion("adjust_id <", value, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdLessThanOrEqualTo(Long value) {
            addCriterion("adjust_id <=", value, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdIn(List<Long> values) {
            addCriterion("adjust_id in", values, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdNotIn(List<Long> values) {
            addCriterion("adjust_id not in", values, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdBetween(Long value1, Long value2) {
            addCriterion("adjust_id between", value1, value2, "adjustId");
            return (Criteria) this;
        }

        public Criteria andAdjustIdNotBetween(Long value1, Long value2) {
            addCriterion("adjust_id not between", value1, value2, "adjustId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNull() {
            addCriterion("platform_org_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNotNull() {
            addCriterion("platform_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdEqualTo(Long value) {
            addCriterion("platform_org_id =", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotEqualTo(Long value) {
            addCriterion("platform_org_id <>", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThan(Long value) {
            addCriterion("platform_org_id >", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("platform_org_id >=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThan(Long value) {
            addCriterion("platform_org_id <", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("platform_org_id <=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIn(List<Long> values) {
            addCriterion("platform_org_id in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotIn(List<Long> values) {
            addCriterion("platform_org_id not in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdBetween(Long value1, Long value2) {
            addCriterion("platform_org_id between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("platform_org_id not between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIsNull() {
            addCriterion("company_org_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIsNotNull() {
            addCriterion("company_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdEqualTo(Long value) {
            addCriterion("company_org_id =", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotEqualTo(Long value) {
            addCriterion("company_org_id <>", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdGreaterThan(Long value) {
            addCriterion("company_org_id >", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_org_id >=", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdLessThan(Long value) {
            addCriterion("company_org_id <", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("company_org_id <=", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIn(List<Long> values) {
            addCriterion("company_org_id in", values, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotIn(List<Long> values) {
            addCriterion("company_org_id not in", values, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdBetween(Long value1, Long value2) {
            addCriterion("company_org_id between", value1, value2, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("company_org_id not between", value1, value2, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNull() {
            addCriterion("store_org_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNotNull() {
            addCriterion("store_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdEqualTo(Long value) {
            addCriterion("store_org_id =", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotEqualTo(Long value) {
            addCriterion("store_org_id <>", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThan(Long value) {
            addCriterion("store_org_id >", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_org_id >=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThan(Long value) {
            addCriterion("store_org_id <", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("store_org_id <=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIn(List<Long> values) {
            addCriterion("store_org_id in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotIn(List<Long> values) {
            addCriterion("store_org_id not in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdBetween(Long value1, Long value2) {
            addCriterion("store_org_id between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("store_org_id not between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNull() {
            addCriterion("middle_category is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNotNull() {
            addCriterion("middle_category is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryEqualTo(String value) {
            addCriterion("middle_category =", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotEqualTo(String value) {
            addCriterion("middle_category <>", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThan(String value) {
            addCriterion("middle_category >", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category >=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThan(String value) {
            addCriterion("middle_category <", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThanOrEqualTo(String value) {
            addCriterion("middle_category <=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLike(String value) {
            addCriterion("middle_category like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotLike(String value) {
            addCriterion("middle_category not like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIn(List<String> values) {
            addCriterion("middle_category in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotIn(List<String> values) {
            addCriterion("middle_category not in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryBetween(String value1, String value2) {
            addCriterion("middle_category between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotBetween(String value1, String value2) {
            addCriterion("middle_category not between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNull() {
            addCriterion("middle_category_name is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNotNull() {
            addCriterion("middle_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameEqualTo(String value) {
            addCriterion("middle_category_name =", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotEqualTo(String value) {
            addCriterion("middle_category_name <>", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThan(String value) {
            addCriterion("middle_category_name >", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category_name >=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThan(String value) {
            addCriterion("middle_category_name <", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("middle_category_name <=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLike(String value) {
            addCriterion("middle_category_name like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotLike(String value) {
            addCriterion("middle_category_name not like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIn(List<String> values) {
            addCriterion("middle_category_name in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotIn(List<String> values) {
            addCriterion("middle_category_name not in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameBetween(String value1, String value2) {
            addCriterion("middle_category_name between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotBetween(String value1, String value2) {
            addCriterion("middle_category_name not between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNull() {
            addCriterion("small_category is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNotNull() {
            addCriterion("small_category is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryEqualTo(String value) {
            addCriterion("small_category =", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotEqualTo(String value) {
            addCriterion("small_category <>", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThan(String value) {
            addCriterion("small_category >", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("small_category >=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThan(String value) {
            addCriterion("small_category <", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThanOrEqualTo(String value) {
            addCriterion("small_category <=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLike(String value) {
            addCriterion("small_category like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotLike(String value) {
            addCriterion("small_category not like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIn(List<String> values) {
            addCriterion("small_category in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotIn(List<String> values) {
            addCriterion("small_category not in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryBetween(String value1, String value2) {
            addCriterion("small_category between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotBetween(String value1, String value2) {
            addCriterion("small_category not between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNull() {
            addCriterion("small_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNotNull() {
            addCriterion("small_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameEqualTo(String value) {
            addCriterion("small_category_name =", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotEqualTo(String value) {
            addCriterion("small_category_name <>", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThan(String value) {
            addCriterion("small_category_name >", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("small_category_name >=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThan(String value) {
            addCriterion("small_category_name <", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("small_category_name <=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLike(String value) {
            addCriterion("small_category_name like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotLike(String value) {
            addCriterion("small_category_name not like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIn(List<String> values) {
            addCriterion("small_category_name in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotIn(List<String> values) {
            addCriterion("small_category_name not in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameBetween(String value1, String value2) {
            addCriterion("small_category_name between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotBetween(String value1, String value2) {
            addCriterion("small_category_name not between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNull() {
            addCriterion("sub_category is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNotNull() {
            addCriterion("sub_category is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryEqualTo(String value) {
            addCriterion("sub_category =", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotEqualTo(String value) {
            addCriterion("sub_category <>", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThan(String value) {
            addCriterion("sub_category >", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category >=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThan(String value) {
            addCriterion("sub_category <", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThanOrEqualTo(String value) {
            addCriterion("sub_category <=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLike(String value) {
            addCriterion("sub_category like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotLike(String value) {
            addCriterion("sub_category not like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIn(List<String> values) {
            addCriterion("sub_category in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotIn(List<String> values) {
            addCriterion("sub_category not in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryBetween(String value1, String value2) {
            addCriterion("sub_category between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotBetween(String value1, String value2) {
            addCriterion("sub_category not between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNull() {
            addCriterion("sub_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNotNull() {
            addCriterion("sub_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameEqualTo(String value) {
            addCriterion("sub_category_name =", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotEqualTo(String value) {
            addCriterion("sub_category_name <>", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThan(String value) {
            addCriterion("sub_category_name >", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_name >=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThan(String value) {
            addCriterion("sub_category_name <", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("sub_category_name <=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLike(String value) {
            addCriterion("sub_category_name like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotLike(String value) {
            addCriterion("sub_category_name not like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIn(List<String> values) {
            addCriterion("sub_category_name in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotIn(List<String> values) {
            addCriterion("sub_category_name not in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameBetween(String value1, String value2) {
            addCriterion("sub_category_name between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotBetween(String value1, String value2) {
            addCriterion("sub_category_name not between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeIsNull() {
            addCriterion("old_store_type is null");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeIsNotNull() {
            addCriterion("old_store_type is not null");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeEqualTo(String value) {
            addCriterion("old_store_type =", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNotEqualTo(String value) {
            addCriterion("old_store_type <>", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeGreaterThan(String value) {
            addCriterion("old_store_type >", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeGreaterThanOrEqualTo(String value) {
            addCriterion("old_store_type >=", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeLessThan(String value) {
            addCriterion("old_store_type <", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeLessThanOrEqualTo(String value) {
            addCriterion("old_store_type <=", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeLike(String value) {
            addCriterion("old_store_type like", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNotLike(String value) {
            addCriterion("old_store_type not like", value, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeIn(List<String> values) {
            addCriterion("old_store_type in", values, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNotIn(List<String> values) {
            addCriterion("old_store_type not in", values, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeBetween(String value1, String value2) {
            addCriterion("old_store_type between", value1, value2, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNotBetween(String value1, String value2) {
            addCriterion("old_store_type not between", value1, value2, "oldStoreType");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameIsNull() {
            addCriterion("old_store_type_name is null");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameIsNotNull() {
            addCriterion("old_store_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameEqualTo(String value) {
            addCriterion("old_store_type_name =", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameNotEqualTo(String value) {
            addCriterion("old_store_type_name <>", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameGreaterThan(String value) {
            addCriterion("old_store_type_name >", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("old_store_type_name >=", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameLessThan(String value) {
            addCriterion("old_store_type_name <", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameLessThanOrEqualTo(String value) {
            addCriterion("old_store_type_name <=", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameLike(String value) {
            addCriterion("old_store_type_name like", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameNotLike(String value) {
            addCriterion("old_store_type_name not like", value, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameIn(List<String> values) {
            addCriterion("old_store_type_name in", values, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameNotIn(List<String> values) {
            addCriterion("old_store_type_name not in", values, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameBetween(String value1, String value2) {
            addCriterion("old_store_type_name between", value1, value2, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andOldStoreTypeNameNotBetween(String value1, String value2) {
            addCriterion("old_store_type_name not between", value1, value2, "oldStoreTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNull() {
            addCriterion("store_type is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNotNull() {
            addCriterion("store_type is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeEqualTo(String value) {
            addCriterion("store_type =", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotEqualTo(String value) {
            addCriterion("store_type <>", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThan(String value) {
            addCriterion("store_type >", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThanOrEqualTo(String value) {
            addCriterion("store_type >=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThan(String value) {
            addCriterion("store_type <", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThanOrEqualTo(String value) {
            addCriterion("store_type <=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLike(String value) {
            addCriterion("store_type like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotLike(String value) {
            addCriterion("store_type not like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIn(List<String> values) {
            addCriterion("store_type in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotIn(List<String> values) {
            addCriterion("store_type not in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeBetween(String value1, String value2) {
            addCriterion("store_type between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotBetween(String value1, String value2) {
            addCriterion("store_type not between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameIsNull() {
            addCriterion("store_type_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameIsNotNull() {
            addCriterion("store_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameEqualTo(String value) {
            addCriterion("store_type_name =", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotEqualTo(String value) {
            addCriterion("store_type_name <>", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameGreaterThan(String value) {
            addCriterion("store_type_name >", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_type_name >=", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameLessThan(String value) {
            addCriterion("store_type_name <", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameLessThanOrEqualTo(String value) {
            addCriterion("store_type_name <=", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameLike(String value) {
            addCriterion("store_type_name like", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotLike(String value) {
            addCriterion("store_type_name not like", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameIn(List<String> values) {
            addCriterion("store_type_name in", values, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotIn(List<String> values) {
            addCriterion("store_type_name not in", values, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameBetween(String value1, String value2) {
            addCriterion("store_type_name between", value1, value2, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotBetween(String value1, String value2) {
            addCriterion("store_type_name not between", value1, value2, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}