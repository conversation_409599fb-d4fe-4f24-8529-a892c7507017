package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class JymlAdjustmentCycleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlAdjustmentCycleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Long value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Long value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Long value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Long value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Long> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Long> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Long value1, Long value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andStartMonthIsNull() {
            addCriterion("start_month is null");
            return (Criteria) this;
        }

        public Criteria andStartMonthIsNotNull() {
            addCriterion("start_month is not null");
            return (Criteria) this;
        }

        public Criteria andStartMonthEqualTo(Date value) {
            addCriterion("start_month =", value, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthNotEqualTo(Date value) {
            addCriterion("start_month <>", value, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthGreaterThan(Date value) {
            addCriterion("start_month >", value, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthGreaterThanOrEqualTo(Date value) {
            addCriterion("start_month >=", value, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthLessThan(Date value) {
            addCriterion("start_month <", value, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthLessThanOrEqualTo(Date value) {
            addCriterion("start_month <=", value, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthIn(List<Date> values) {
            addCriterion("start_month in", values, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthNotIn(List<Date> values) {
            addCriterion("start_month not in", values, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthBetween(Date value1, Date value2) {
            addCriterion("start_month between", value1, value2, "startMonth");
            return (Criteria) this;
        }

        public Criteria andStartMonthNotBetween(Date value1, Date value2) {
            addCriterion("start_month not between", value1, value2, "startMonth");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateIsNull() {
            addCriterion("adjust_channel_open_date is null");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateIsNotNull() {
            addCriterion("adjust_channel_open_date is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateEqualTo(Date value) {
            addCriterionForJDBCDate("adjust_channel_open_date =", value, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("adjust_channel_open_date <>", value, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateGreaterThan(Date value) {
            addCriterionForJDBCDate("adjust_channel_open_date >", value, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("adjust_channel_open_date >=", value, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateLessThan(Date value) {
            addCriterionForJDBCDate("adjust_channel_open_date <", value, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("adjust_channel_open_date <=", value, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateIn(List<Date> values) {
            addCriterionForJDBCDate("adjust_channel_open_date in", values, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("adjust_channel_open_date not in", values, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("adjust_channel_open_date between", value1, value2, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andAdjustChannelOpenDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("adjust_channel_open_date not between", value1, value2, "adjustChannelOpenDate");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectIsNull() {
            addCriterion("days_after_start_date_forbid_effect is null");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectIsNotNull() {
            addCriterion("days_after_start_date_forbid_effect is not null");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectEqualTo(Integer value) {
            addCriterion("days_after_start_date_forbid_effect =", value, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectNotEqualTo(Integer value) {
            addCriterion("days_after_start_date_forbid_effect <>", value, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectGreaterThan(Integer value) {
            addCriterion("days_after_start_date_forbid_effect >", value, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectGreaterThanOrEqualTo(Integer value) {
            addCriterion("days_after_start_date_forbid_effect >=", value, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectLessThan(Integer value) {
            addCriterion("days_after_start_date_forbid_effect <", value, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectLessThanOrEqualTo(Integer value) {
            addCriterion("days_after_start_date_forbid_effect <=", value, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectIn(List<Integer> values) {
            addCriterion("days_after_start_date_forbid_effect in", values, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectNotIn(List<Integer> values) {
            addCriterion("days_after_start_date_forbid_effect not in", values, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectBetween(Integer value1, Integer value2) {
            addCriterion("days_after_start_date_forbid_effect between", value1, value2, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterStartDateForbidEffectNotBetween(Integer value1, Integer value2) {
            addCriterion("days_after_start_date_forbid_effect not between", value1, value2, "daysAfterStartDateForbidEffect");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelIsNull() {
            addCriterion("days_after_effect_close_channel is null");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelIsNotNull() {
            addCriterion("days_after_effect_close_channel is not null");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelEqualTo(Integer value) {
            addCriterion("days_after_effect_close_channel =", value, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelNotEqualTo(Integer value) {
            addCriterion("days_after_effect_close_channel <>", value, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelGreaterThan(Integer value) {
            addCriterion("days_after_effect_close_channel >", value, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelGreaterThanOrEqualTo(Integer value) {
            addCriterion("days_after_effect_close_channel >=", value, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelLessThan(Integer value) {
            addCriterion("days_after_effect_close_channel <", value, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelLessThanOrEqualTo(Integer value) {
            addCriterion("days_after_effect_close_channel <=", value, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelIn(List<Integer> values) {
            addCriterion("days_after_effect_close_channel in", values, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelNotIn(List<Integer> values) {
            addCriterion("days_after_effect_close_channel not in", values, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelBetween(Integer value1, Integer value2) {
            addCriterion("days_after_effect_close_channel between", value1, value2, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andDaysAfterEffectCloseChannelNotBetween(Integer value1, Integer value2) {
            addCriterion("days_after_effect_close_channel not between", value1, value2, "daysAfterEffectCloseChannel");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyIsNull() {
            addCriterion("adjust_frequency is null");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyIsNotNull() {
            addCriterion("adjust_frequency is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyEqualTo(String value) {
            addCriterion("adjust_frequency =", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyNotEqualTo(String value) {
            addCriterion("adjust_frequency <>", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyGreaterThan(String value) {
            addCriterion("adjust_frequency >", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("adjust_frequency >=", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyLessThan(String value) {
            addCriterion("adjust_frequency <", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyLessThanOrEqualTo(String value) {
            addCriterion("adjust_frequency <=", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyLike(String value) {
            addCriterion("adjust_frequency like", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyNotLike(String value) {
            addCriterion("adjust_frequency not like", value, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyIn(List<String> values) {
            addCriterion("adjust_frequency in", values, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyNotIn(List<String> values) {
            addCriterion("adjust_frequency not in", values, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyBetween(String value1, String value2) {
            addCriterion("adjust_frequency between", value1, value2, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andAdjustFrequencyNotBetween(String value1, String value2) {
            addCriterion("adjust_frequency not between", value1, value2, "adjustFrequency");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmIsNull() {
            addCriterion("need_confirm is null");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmIsNotNull() {
            addCriterion("need_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmEqualTo(Integer value) {
            addCriterion("need_confirm =", value, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmNotEqualTo(Integer value) {
            addCriterion("need_confirm <>", value, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmGreaterThan(Integer value) {
            addCriterion("need_confirm >", value, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmGreaterThanOrEqualTo(Integer value) {
            addCriterion("need_confirm >=", value, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmLessThan(Integer value) {
            addCriterion("need_confirm <", value, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmLessThanOrEqualTo(Integer value) {
            addCriterion("need_confirm <=", value, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmIn(List<Integer> values) {
            addCriterion("need_confirm in", values, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmNotIn(List<Integer> values) {
            addCriterion("need_confirm not in", values, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmBetween(Integer value1, Integer value2) {
            addCriterion("need_confirm between", value1, value2, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andNeedConfirmNotBetween(Integer value1, Integer value2) {
            addCriterion("need_confirm not between", value1, value2, "needConfirm");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeIsNull() {
            addCriterion("send_wx_notice is null");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeIsNotNull() {
            addCriterion("send_wx_notice is not null");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeEqualTo(Integer value) {
            addCriterion("send_wx_notice =", value, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeNotEqualTo(Integer value) {
            addCriterion("send_wx_notice <>", value, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeGreaterThan(Integer value) {
            addCriterion("send_wx_notice >", value, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeGreaterThanOrEqualTo(Integer value) {
            addCriterion("send_wx_notice >=", value, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeLessThan(Integer value) {
            addCriterion("send_wx_notice <", value, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeLessThanOrEqualTo(Integer value) {
            addCriterion("send_wx_notice <=", value, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeIn(List<Integer> values) {
            addCriterion("send_wx_notice in", values, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeNotIn(List<Integer> values) {
            addCriterion("send_wx_notice not in", values, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeBetween(Integer value1, Integer value2) {
            addCriterion("send_wx_notice between", value1, value2, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andSendWxNoticeNotBetween(Integer value1, Integer value2) {
            addCriterion("send_wx_notice not between", value1, value2, "sendWxNotice");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNull() {
            addCriterion("create_by_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNotNull() {
            addCriterion("create_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdEqualTo(Long value) {
            addCriterion("create_by_id =", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotEqualTo(Long value) {
            addCriterion("create_by_id <>", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThan(Long value) {
            addCriterion("create_by_id >", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by_id >=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThan(Long value) {
            addCriterion("create_by_id <", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThanOrEqualTo(Long value) {
            addCriterion("create_by_id <=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIn(List<Long> values) {
            addCriterion("create_by_id in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotIn(List<Long> values) {
            addCriterion("create_by_id not in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdBetween(Long value1, Long value2) {
            addCriterion("create_by_id between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotBetween(Long value1, Long value2) {
            addCriterion("create_by_id not between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNull() {
            addCriterion("update_by_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNotNull() {
            addCriterion("update_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdEqualTo(Long value) {
            addCriterion("update_by_id =", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotEqualTo(Long value) {
            addCriterion("update_by_id <>", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThan(Long value) {
            addCriterion("update_by_id >", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by_id >=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThan(Long value) {
            addCriterion("update_by_id <", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThanOrEqualTo(Long value) {
            addCriterion("update_by_id <=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIn(List<Long> values) {
            addCriterion("update_by_id in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotIn(List<Long> values) {
            addCriterion("update_by_id not in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdBetween(Long value1, Long value2) {
            addCriterion("update_by_id between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotBetween(Long value1, Long value2) {
            addCriterion("update_by_id not between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}