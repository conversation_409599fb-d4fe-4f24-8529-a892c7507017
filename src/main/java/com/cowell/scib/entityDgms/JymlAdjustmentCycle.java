package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-调整周期
 */
@Data
public class JymlAdjustmentCycle implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 下次开始时间
     */
    private Date startMonth;

    /**
     * 调整通道开启日期
     */
    private Date adjustChannelOpenDate;

    /**
     * 开始日期后几天禁止请货生效
     */
    private Integer daysAfterStartDateForbidEffect;

    /**
     * 生效后几天关闭调整通道
     */
    private Integer daysAfterEffectCloseChannel;

    /**
     * 调整频率
     */
    private String adjustFrequency;

    /**
     * 是否需要二次确认
     */
    private Integer needConfirm;

    /**
     * 是否发送店长企微消息
     */
    private Integer sendWxNotice;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 更新人
     */
    private String updateBy;

    private static final long serialVersionUID = 1L;
}