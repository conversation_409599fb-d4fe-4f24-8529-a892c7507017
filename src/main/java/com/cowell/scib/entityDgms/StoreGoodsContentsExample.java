package com.cowell.scib.entityDgms;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StoreGoodsContentsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public StoreGoodsContentsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNull() {
            addCriterion("store_org_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNotNull() {
            addCriterion("store_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdEqualTo(Long value) {
            addCriterion("store_org_id =", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotEqualTo(Long value) {
            addCriterion("store_org_id <>", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThan(Long value) {
            addCriterion("store_org_id >", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_org_id >=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThan(Long value) {
            addCriterion("store_org_id <", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("store_org_id <=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIn(List<Long> values) {
            addCriterion("store_org_id in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotIn(List<Long> values) {
            addCriterion("store_org_id not in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdBetween(Long value1, Long value2) {
            addCriterion("store_org_id between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("store_org_id not between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNull() {
            addCriterion("sub_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNotNull() {
            addCriterion("sub_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdEqualTo(Long value) {
            addCriterion("sub_category_id =", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotEqualTo(Long value) {
            addCriterion("sub_category_id <>", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThan(Long value) {
            addCriterion("sub_category_id >", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sub_category_id >=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThan(Long value) {
            addCriterion("sub_category_id <", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("sub_category_id <=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIn(List<Long> values) {
            addCriterion("sub_category_id in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotIn(List<Long> values) {
            addCriterion("sub_category_id not in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdBetween(Long value1, Long value2) {
            addCriterion("sub_category_id between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("sub_category_id not between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagIsNull() {
            addCriterion("necessary_tag is null");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagIsNotNull() {
            addCriterion("necessary_tag is not null");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagEqualTo(Integer value) {
            addCriterion("necessary_tag =", value, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNotEqualTo(Integer value) {
            addCriterion("necessary_tag <>", value, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagGreaterThan(Integer value) {
            addCriterion("necessary_tag >", value, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("necessary_tag >=", value, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagLessThan(Integer value) {
            addCriterion("necessary_tag <", value, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagLessThanOrEqualTo(Integer value) {
            addCriterion("necessary_tag <=", value, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagIn(List<Integer> values) {
            addCriterion("necessary_tag in", values, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNotIn(List<Integer> values) {
            addCriterion("necessary_tag not in", values, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagBetween(Integer value1, Integer value2) {
            addCriterion("necessary_tag between", value1, value2, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNotBetween(Integer value1, Integer value2) {
            addCriterion("necessary_tag not between", value1, value2, "necessaryTag");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameIsNull() {
            addCriterion("necessary_tag_name is null");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameIsNotNull() {
            addCriterion("necessary_tag_name is not null");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameEqualTo(String value) {
            addCriterion("necessary_tag_name =", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameNotEqualTo(String value) {
            addCriterion("necessary_tag_name <>", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameGreaterThan(String value) {
            addCriterion("necessary_tag_name >", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameGreaterThanOrEqualTo(String value) {
            addCriterion("necessary_tag_name >=", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameLessThan(String value) {
            addCriterion("necessary_tag_name <", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameLessThanOrEqualTo(String value) {
            addCriterion("necessary_tag_name <=", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameLike(String value) {
            addCriterion("necessary_tag_name like", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameNotLike(String value) {
            addCriterion("necessary_tag_name not like", value, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameIn(List<String> values) {
            addCriterion("necessary_tag_name in", values, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameNotIn(List<String> values) {
            addCriterion("necessary_tag_name not in", values, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameBetween(String value1, String value2) {
            addCriterion("necessary_tag_name between", value1, value2, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andNecessaryTagNameNotBetween(String value1, String value2) {
            addCriterion("necessary_tag_name not between", value1, value2, "necessaryTagName");
            return (Criteria) this;
        }

        public Criteria andManageStatusIsNull() {
            addCriterion("manage_status is null");
            return (Criteria) this;
        }

        public Criteria andManageStatusIsNotNull() {
            addCriterion("manage_status is not null");
            return (Criteria) this;
        }

        public Criteria andManageStatusEqualTo(Integer value) {
            addCriterion("manage_status =", value, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusNotEqualTo(Integer value) {
            addCriterion("manage_status <>", value, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusGreaterThan(Integer value) {
            addCriterion("manage_status >", value, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("manage_status >=", value, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusLessThan(Integer value) {
            addCriterion("manage_status <", value, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusLessThanOrEqualTo(Integer value) {
            addCriterion("manage_status <=", value, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusIn(List<Integer> values) {
            addCriterion("manage_status in", values, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusNotIn(List<Integer> values) {
            addCriterion("manage_status not in", values, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusBetween(Integer value1, Integer value2) {
            addCriterion("manage_status between", value1, value2, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andManageStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("manage_status not between", value1, value2, "manageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusIsNull() {
            addCriterion("suggest_manage_status is null");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusIsNotNull() {
            addCriterion("suggest_manage_status is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusEqualTo(Integer value) {
            addCriterion("suggest_manage_status =", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotEqualTo(Integer value) {
            addCriterion("suggest_manage_status <>", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusGreaterThan(Integer value) {
            addCriterion("suggest_manage_status >", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("suggest_manage_status >=", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusLessThan(Integer value) {
            addCriterion("suggest_manage_status <", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusLessThanOrEqualTo(Integer value) {
            addCriterion("suggest_manage_status <=", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusIn(List<Integer> values) {
            addCriterion("suggest_manage_status in", values, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotIn(List<Integer> values) {
            addCriterion("suggest_manage_status not in", values, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusBetween(Integer value1, Integer value2) {
            addCriterion("suggest_manage_status between", value1, value2, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("suggest_manage_status not between", value1, value2, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andForbidApplyIsNull() {
            addCriterion("forbid_apply is null");
            return (Criteria) this;
        }

        public Criteria andForbidApplyIsNotNull() {
            addCriterion("forbid_apply is not null");
            return (Criteria) this;
        }

        public Criteria andForbidApplyEqualTo(String value) {
            addCriterion("forbid_apply =", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotEqualTo(String value) {
            addCriterion("forbid_apply <>", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyGreaterThan(String value) {
            addCriterion("forbid_apply >", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_apply >=", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyLessThan(String value) {
            addCriterion("forbid_apply <", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyLessThanOrEqualTo(String value) {
            addCriterion("forbid_apply <=", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyLike(String value) {
            addCriterion("forbid_apply like", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotLike(String value) {
            addCriterion("forbid_apply not like", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyIn(List<String> values) {
            addCriterion("forbid_apply in", values, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotIn(List<String> values) {
            addCriterion("forbid_apply not in", values, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyBetween(String value1, String value2) {
            addCriterion("forbid_apply between", value1, value2, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotBetween(String value1, String value2) {
            addCriterion("forbid_apply not between", value1, value2, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidDistrIsNull() {
            addCriterion("forbid_distr is null");
            return (Criteria) this;
        }

        public Criteria andForbidDistrIsNotNull() {
            addCriterion("forbid_distr is not null");
            return (Criteria) this;
        }

        public Criteria andForbidDistrEqualTo(String value) {
            addCriterion("forbid_distr =", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrNotEqualTo(String value) {
            addCriterion("forbid_distr <>", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrGreaterThan(String value) {
            addCriterion("forbid_distr >", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_distr >=", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrLessThan(String value) {
            addCriterion("forbid_distr <", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrLessThanOrEqualTo(String value) {
            addCriterion("forbid_distr <=", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrLike(String value) {
            addCriterion("forbid_distr like", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrNotLike(String value) {
            addCriterion("forbid_distr not like", value, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrIn(List<String> values) {
            addCriterion("forbid_distr in", values, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrNotIn(List<String> values) {
            addCriterion("forbid_distr not in", values, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrBetween(String value1, String value2) {
            addCriterion("forbid_distr between", value1, value2, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidDistrNotBetween(String value1, String value2) {
            addCriterion("forbid_distr not between", value1, value2, "forbidDistr");
            return (Criteria) this;
        }

        public Criteria andForbidSaleIsNull() {
            addCriterion("forbid_sale is null");
            return (Criteria) this;
        }

        public Criteria andForbidSaleIsNotNull() {
            addCriterion("forbid_sale is not null");
            return (Criteria) this;
        }

        public Criteria andForbidSaleEqualTo(String value) {
            addCriterion("forbid_sale =", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleNotEqualTo(String value) {
            addCriterion("forbid_sale <>", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleGreaterThan(String value) {
            addCriterion("forbid_sale >", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_sale >=", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleLessThan(String value) {
            addCriterion("forbid_sale <", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleLessThanOrEqualTo(String value) {
            addCriterion("forbid_sale <=", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleLike(String value) {
            addCriterion("forbid_sale like", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleNotLike(String value) {
            addCriterion("forbid_sale not like", value, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleIn(List<String> values) {
            addCriterion("forbid_sale in", values, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleNotIn(List<String> values) {
            addCriterion("forbid_sale not in", values, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleBetween(String value1, String value2) {
            addCriterion("forbid_sale between", value1, value2, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidSaleNotBetween(String value1, String value2) {
            addCriterion("forbid_sale not between", value1, value2, "forbidSale");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseIsNull() {
            addCriterion("forbid_return_warehouse is null");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseIsNotNull() {
            addCriterion("forbid_return_warehouse is not null");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseEqualTo(String value) {
            addCriterion("forbid_return_warehouse =", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotEqualTo(String value) {
            addCriterion("forbid_return_warehouse <>", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseGreaterThan(String value) {
            addCriterion("forbid_return_warehouse >", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_return_warehouse >=", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseLessThan(String value) {
            addCriterion("forbid_return_warehouse <", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseLessThanOrEqualTo(String value) {
            addCriterion("forbid_return_warehouse <=", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseLike(String value) {
            addCriterion("forbid_return_warehouse like", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotLike(String value) {
            addCriterion("forbid_return_warehouse not like", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseIn(List<String> values) {
            addCriterion("forbid_return_warehouse in", values, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotIn(List<String> values) {
            addCriterion("forbid_return_warehouse not in", values, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseBetween(String value1, String value2) {
            addCriterion("forbid_return_warehouse between", value1, value2, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotBetween(String value1, String value2) {
            addCriterion("forbid_return_warehouse not between", value1, value2, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotIsNull() {
            addCriterion("forbid_return_allot is null");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotIsNotNull() {
            addCriterion("forbid_return_allot is not null");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotEqualTo(String value) {
            addCriterion("forbid_return_allot =", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotNotEqualTo(String value) {
            addCriterion("forbid_return_allot <>", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotGreaterThan(String value) {
            addCriterion("forbid_return_allot >", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_return_allot >=", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotLessThan(String value) {
            addCriterion("forbid_return_allot <", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotLessThanOrEqualTo(String value) {
            addCriterion("forbid_return_allot <=", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotLike(String value) {
            addCriterion("forbid_return_allot like", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotNotLike(String value) {
            addCriterion("forbid_return_allot not like", value, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotIn(List<String> values) {
            addCriterion("forbid_return_allot in", values, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotNotIn(List<String> values) {
            addCriterion("forbid_return_allot not in", values, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotBetween(String value1, String value2) {
            addCriterion("forbid_return_allot between", value1, value2, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andForbidReturnAllotNotBetween(String value1, String value2) {
            addCriterion("forbid_return_allot not between", value1, value2, "forbidReturnAllot");
            return (Criteria) this;
        }

        public Criteria andSensitiveIsNull() {
            addCriterion("`sensitive` is null");
            return (Criteria) this;
        }

        public Criteria andSensitiveIsNotNull() {
            addCriterion("`sensitive` is not null");
            return (Criteria) this;
        }

        public Criteria andSensitiveEqualTo(String value) {
            addCriterion("`sensitive` =", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveNotEqualTo(String value) {
            addCriterion("`sensitive` <>", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveGreaterThan(String value) {
            addCriterion("`sensitive` >", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveGreaterThanOrEqualTo(String value) {
            addCriterion("`sensitive` >=", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveLessThan(String value) {
            addCriterion("`sensitive` <", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveLessThanOrEqualTo(String value) {
            addCriterion("`sensitive` <=", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveLike(String value) {
            addCriterion("`sensitive` like", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveNotLike(String value) {
            addCriterion("`sensitive` not like", value, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveIn(List<String> values) {
            addCriterion("`sensitive` in", values, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveNotIn(List<String> values) {
            addCriterion("`sensitive` not in", values, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveBetween(String value1, String value2) {
            addCriterion("`sensitive` between", value1, value2, "sensitive");
            return (Criteria) this;
        }

        public Criteria andSensitiveNotBetween(String value1, String value2) {
            addCriterion("`sensitive` not between", value1, value2, "sensitive");
            return (Criteria) this;
        }

        public Criteria andPushLevelIsNull() {
            addCriterion("push_level is null");
            return (Criteria) this;
        }

        public Criteria andPushLevelIsNotNull() {
            addCriterion("push_level is not null");
            return (Criteria) this;
        }

        public Criteria andPushLevelEqualTo(String value) {
            addCriterion("push_level =", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotEqualTo(String value) {
            addCriterion("push_level <>", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelGreaterThan(String value) {
            addCriterion("push_level >", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelGreaterThanOrEqualTo(String value) {
            addCriterion("push_level >=", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLessThan(String value) {
            addCriterion("push_level <", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLessThanOrEqualTo(String value) {
            addCriterion("push_level <=", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLike(String value) {
            addCriterion("push_level like", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotLike(String value) {
            addCriterion("push_level not like", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelIn(List<String> values) {
            addCriterion("push_level in", values, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotIn(List<String> values) {
            addCriterion("push_level not in", values, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelBetween(String value1, String value2) {
            addCriterion("push_level between", value1, value2, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotBetween(String value1, String value2) {
            addCriterion("push_level not between", value1, value2, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIsNull() {
            addCriterion("min_display_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIsNotNull() {
            addCriterion("min_display_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity =", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity <>", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityGreaterThan(BigDecimal value) {
            addCriterion("min_display_quantity >", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity >=", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityLessThan(BigDecimal value) {
            addCriterion("min_display_quantity <", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity <=", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIn(List<BigDecimal> values) {
            addCriterion("min_display_quantity in", values, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotIn(List<BigDecimal> values) {
            addCriterion("min_display_quantity not in", values, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_quantity between", value1, value2, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_quantity not between", value1, value2, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperIsNull() {
            addCriterion("apply_limit_upper is null");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperIsNotNull() {
            addCriterion("apply_limit_upper is not null");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperEqualTo(BigDecimal value) {
            addCriterion("apply_limit_upper =", value, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperNotEqualTo(BigDecimal value) {
            addCriterion("apply_limit_upper <>", value, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperGreaterThan(BigDecimal value) {
            addCriterion("apply_limit_upper >", value, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_limit_upper >=", value, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperLessThan(BigDecimal value) {
            addCriterion("apply_limit_upper <", value, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_limit_upper <=", value, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperIn(List<BigDecimal> values) {
            addCriterion("apply_limit_upper in", values, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperNotIn(List<BigDecimal> values) {
            addCriterion("apply_limit_upper not in", values, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_limit_upper between", value1, value2, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andApplyLimitUpperNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_limit_upper not between", value1, value2, "applyLimitUpper");
            return (Criteria) this;
        }

        public Criteria andEffectStatusIsNull() {
            addCriterion("effect_status is null");
            return (Criteria) this;
        }

        public Criteria andEffectStatusIsNotNull() {
            addCriterion("effect_status is not null");
            return (Criteria) this;
        }

        public Criteria andEffectStatusEqualTo(Byte value) {
            addCriterion("effect_status =", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusNotEqualTo(Byte value) {
            addCriterion("effect_status <>", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusGreaterThan(Byte value) {
            addCriterion("effect_status >", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("effect_status >=", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusLessThan(Byte value) {
            addCriterion("effect_status <", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusLessThanOrEqualTo(Byte value) {
            addCriterion("effect_status <=", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusIn(List<Byte> values) {
            addCriterion("effect_status in", values, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusNotIn(List<Byte> values) {
            addCriterion("effect_status not in", values, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusBetween(Byte value1, Byte value2) {
            addCriterion("effect_status between", value1, value2, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("effect_status not between", value1, value2, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}