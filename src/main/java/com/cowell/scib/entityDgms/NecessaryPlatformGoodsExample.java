package com.cowell.scib.entityDgms;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NecessaryPlatformGoodsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public NecessaryPlatformGoodsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNull() {
            addCriterion("platform_org_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNotNull() {
            addCriterion("platform_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdEqualTo(Long value) {
            addCriterion("platform_org_id =", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotEqualTo(Long value) {
            addCriterion("platform_org_id <>", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThan(Long value) {
            addCriterion("platform_org_id >", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("platform_org_id >=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThan(Long value) {
            addCriterion("platform_org_id <", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("platform_org_id <=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIn(List<Long> values) {
            addCriterion("platform_org_id in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotIn(List<Long> values) {
            addCriterion("platform_org_id not in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdBetween(Long value1, Long value2) {
            addCriterion("platform_org_id between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("platform_org_id not between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNull() {
            addCriterion("store_type is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNotNull() {
            addCriterion("store_type is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeEqualTo(String value) {
            addCriterion("store_type =", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotEqualTo(String value) {
            addCriterion("store_type <>", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThan(String value) {
            addCriterion("store_type >", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThanOrEqualTo(String value) {
            addCriterion("store_type >=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThan(String value) {
            addCriterion("store_type <", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThanOrEqualTo(String value) {
            addCriterion("store_type <=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLike(String value) {
            addCriterion("store_type like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotLike(String value) {
            addCriterion("store_type not like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIn(List<String> values) {
            addCriterion("store_type in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotIn(List<String> values) {
            addCriterion("store_type not in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeBetween(String value1, String value2) {
            addCriterion("store_type between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotBetween(String value1, String value2) {
            addCriterion("store_type not between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Long value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Long value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Long value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Long value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Long> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Long> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Long value1, Long value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdIsNull() {
            addCriterion("middle_category_id is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdIsNotNull() {
            addCriterion("middle_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdEqualTo(Long value) {
            addCriterion("middle_category_id =", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdNotEqualTo(Long value) {
            addCriterion("middle_category_id <>", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdGreaterThan(Long value) {
            addCriterion("middle_category_id >", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("middle_category_id >=", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdLessThan(Long value) {
            addCriterion("middle_category_id <", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("middle_category_id <=", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdIn(List<Long> values) {
            addCriterion("middle_category_id in", values, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdNotIn(List<Long> values) {
            addCriterion("middle_category_id not in", values, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdBetween(Long value1, Long value2) {
            addCriterion("middle_category_id between", value1, value2, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("middle_category_id not between", value1, value2, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNull() {
            addCriterion("middle_category_name is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNotNull() {
            addCriterion("middle_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameEqualTo(String value) {
            addCriterion("middle_category_name =", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotEqualTo(String value) {
            addCriterion("middle_category_name <>", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThan(String value) {
            addCriterion("middle_category_name >", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category_name >=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThan(String value) {
            addCriterion("middle_category_name <", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("middle_category_name <=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLike(String value) {
            addCriterion("middle_category_name like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotLike(String value) {
            addCriterion("middle_category_name not like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIn(List<String> values) {
            addCriterion("middle_category_name in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotIn(List<String> values) {
            addCriterion("middle_category_name not in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameBetween(String value1, String value2) {
            addCriterion("middle_category_name between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotBetween(String value1, String value2) {
            addCriterion("middle_category_name not between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdIsNull() {
            addCriterion("small_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdIsNotNull() {
            addCriterion("small_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdEqualTo(Long value) {
            addCriterion("small_category_id =", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdNotEqualTo(Long value) {
            addCriterion("small_category_id <>", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdGreaterThan(Long value) {
            addCriterion("small_category_id >", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("small_category_id >=", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdLessThan(Long value) {
            addCriterion("small_category_id <", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("small_category_id <=", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdIn(List<Long> values) {
            addCriterion("small_category_id in", values, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdNotIn(List<Long> values) {
            addCriterion("small_category_id not in", values, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdBetween(Long value1, Long value2) {
            addCriterion("small_category_id between", value1, value2, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("small_category_id not between", value1, value2, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNull() {
            addCriterion("small_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNotNull() {
            addCriterion("small_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameEqualTo(String value) {
            addCriterion("small_category_name =", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotEqualTo(String value) {
            addCriterion("small_category_name <>", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThan(String value) {
            addCriterion("small_category_name >", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("small_category_name >=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThan(String value) {
            addCriterion("small_category_name <", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("small_category_name <=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLike(String value) {
            addCriterion("small_category_name like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotLike(String value) {
            addCriterion("small_category_name not like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIn(List<String> values) {
            addCriterion("small_category_name in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotIn(List<String> values) {
            addCriterion("small_category_name not in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameBetween(String value1, String value2) {
            addCriterion("small_category_name between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotBetween(String value1, String value2) {
            addCriterion("small_category_name not between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNull() {
            addCriterion("sub_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNotNull() {
            addCriterion("sub_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdEqualTo(Long value) {
            addCriterion("sub_category_id =", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotEqualTo(Long value) {
            addCriterion("sub_category_id <>", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThan(Long value) {
            addCriterion("sub_category_id >", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sub_category_id >=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThan(Long value) {
            addCriterion("sub_category_id <", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("sub_category_id <=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIn(List<Long> values) {
            addCriterion("sub_category_id in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotIn(List<Long> values) {
            addCriterion("sub_category_id not in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdBetween(Long value1, Long value2) {
            addCriterion("sub_category_id between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("sub_category_id not between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNull() {
            addCriterion("sub_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNotNull() {
            addCriterion("sub_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameEqualTo(String value) {
            addCriterion("sub_category_name =", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotEqualTo(String value) {
            addCriterion("sub_category_name <>", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThan(String value) {
            addCriterion("sub_category_name >", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_name >=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThan(String value) {
            addCriterion("sub_category_name <", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("sub_category_name <=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLike(String value) {
            addCriterion("sub_category_name like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotLike(String value) {
            addCriterion("sub_category_name not like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIn(List<String> values) {
            addCriterion("sub_category_name in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotIn(List<String> values) {
            addCriterion("sub_category_name not in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameBetween(String value1, String value2) {
            addCriterion("sub_category_name between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotBetween(String value1, String value2) {
            addCriterion("sub_category_name not between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNull() {
            addCriterion("composition is null");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNotNull() {
            addCriterion("composition is not null");
            return (Criteria) this;
        }

        public Criteria andCompositionEqualTo(String value) {
            addCriterion("composition =", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotEqualTo(String value) {
            addCriterion("composition <>", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThan(String value) {
            addCriterion("composition >", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThanOrEqualTo(String value) {
            addCriterion("composition >=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThan(String value) {
            addCriterion("composition <", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThanOrEqualTo(String value) {
            addCriterion("composition <=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLike(String value) {
            addCriterion("composition like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotLike(String value) {
            addCriterion("composition not like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionIn(List<String> values) {
            addCriterion("composition in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotIn(List<String> values) {
            addCriterion("composition not in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionBetween(String value1, String value2) {
            addCriterion("composition between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotBetween(String value1, String value2) {
            addCriterion("composition not between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNull() {
            addCriterion("bar_code is null");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNotNull() {
            addCriterion("bar_code is not null");
            return (Criteria) this;
        }

        public Criteria andBarCodeEqualTo(String value) {
            addCriterion("bar_code =", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotEqualTo(String value) {
            addCriterion("bar_code <>", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThan(String value) {
            addCriterion("bar_code >", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bar_code >=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThan(String value) {
            addCriterion("bar_code <", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThanOrEqualTo(String value) {
            addCriterion("bar_code <=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLike(String value) {
            addCriterion("bar_code like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotLike(String value) {
            addCriterion("bar_code not like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeIn(List<String> values) {
            addCriterion("bar_code in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotIn(List<String> values) {
            addCriterion("bar_code not in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeBetween(String value1, String value2) {
            addCriterion("bar_code between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotBetween(String value1, String value2) {
            addCriterion("bar_code not between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameIsNull() {
            addCriterion("goods_common_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameIsNotNull() {
            addCriterion("goods_common_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameEqualTo(String value) {
            addCriterion("goods_common_name =", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotEqualTo(String value) {
            addCriterion("goods_common_name <>", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameGreaterThan(String value) {
            addCriterion("goods_common_name >", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_common_name >=", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameLessThan(String value) {
            addCriterion("goods_common_name <", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameLessThanOrEqualTo(String value) {
            addCriterion("goods_common_name <=", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameLike(String value) {
            addCriterion("goods_common_name like", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotLike(String value) {
            addCriterion("goods_common_name not like", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameIn(List<String> values) {
            addCriterion("goods_common_name in", values, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotIn(List<String> values) {
            addCriterion("goods_common_name not in", values, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameBetween(String value1, String value2) {
            addCriterion("goods_common_name between", value1, value2, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotBetween(String value1, String value2) {
            addCriterion("goods_common_name not between", value1, value2, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitIsNull() {
            addCriterion("goods_unit is null");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitIsNotNull() {
            addCriterion("goods_unit is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitEqualTo(String value) {
            addCriterion("goods_unit =", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotEqualTo(String value) {
            addCriterion("goods_unit <>", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitGreaterThan(String value) {
            addCriterion("goods_unit >", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitGreaterThanOrEqualTo(String value) {
            addCriterion("goods_unit >=", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitLessThan(String value) {
            addCriterion("goods_unit <", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitLessThanOrEqualTo(String value) {
            addCriterion("goods_unit <=", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitLike(String value) {
            addCriterion("goods_unit like", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotLike(String value) {
            addCriterion("goods_unit not like", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitIn(List<String> values) {
            addCriterion("goods_unit in", values, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotIn(List<String> values) {
            addCriterion("goods_unit not in", values, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitBetween(String value1, String value2) {
            addCriterion("goods_unit between", value1, value2, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotBetween(String value1, String value2) {
            addCriterion("goods_unit not between", value1, value2, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNull() {
            addCriterion("specifications is null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNotNull() {
            addCriterion("specifications is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsEqualTo(String value) {
            addCriterion("specifications =", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotEqualTo(String value) {
            addCriterion("specifications <>", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThan(String value) {
            addCriterion("specifications >", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThanOrEqualTo(String value) {
            addCriterion("specifications >=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThan(String value) {
            addCriterion("specifications <", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThanOrEqualTo(String value) {
            addCriterion("specifications <=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLike(String value) {
            addCriterion("specifications like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotLike(String value) {
            addCriterion("specifications not like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIn(List<String> values) {
            addCriterion("specifications in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotIn(List<String> values) {
            addCriterion("specifications not in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsBetween(String value1, String value2) {
            addCriterion("specifications between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotBetween(String value1, String value2) {
            addCriterion("specifications not between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andDosageFormIsNull() {
            addCriterion("dosage_form is null");
            return (Criteria) this;
        }

        public Criteria andDosageFormIsNotNull() {
            addCriterion("dosage_form is not null");
            return (Criteria) this;
        }

        public Criteria andDosageFormEqualTo(String value) {
            addCriterion("dosage_form =", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotEqualTo(String value) {
            addCriterion("dosage_form <>", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormGreaterThan(String value) {
            addCriterion("dosage_form >", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormGreaterThanOrEqualTo(String value) {
            addCriterion("dosage_form >=", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormLessThan(String value) {
            addCriterion("dosage_form <", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormLessThanOrEqualTo(String value) {
            addCriterion("dosage_form <=", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormLike(String value) {
            addCriterion("dosage_form like", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotLike(String value) {
            addCriterion("dosage_form not like", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormIn(List<String> values) {
            addCriterion("dosage_form in", values, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotIn(List<String> values) {
            addCriterion("dosage_form not in", values, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormBetween(String value1, String value2) {
            addCriterion("dosage_form between", value1, value2, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotBetween(String value1, String value2) {
            addCriterion("dosage_form not between", value1, value2, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberIsNull() {
            addCriterion("approval_number is null");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberIsNotNull() {
            addCriterion("approval_number is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberEqualTo(String value) {
            addCriterion("approval_number =", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotEqualTo(String value) {
            addCriterion("approval_number <>", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberGreaterThan(String value) {
            addCriterion("approval_number >", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberGreaterThanOrEqualTo(String value) {
            addCriterion("approval_number >=", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberLessThan(String value) {
            addCriterion("approval_number <", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberLessThanOrEqualTo(String value) {
            addCriterion("approval_number <=", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberLike(String value) {
            addCriterion("approval_number like", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotLike(String value) {
            addCriterion("approval_number not like", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberIn(List<String> values) {
            addCriterion("approval_number in", values, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotIn(List<String> values) {
            addCriterion("approval_number not in", values, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberBetween(String value1, String value2) {
            addCriterion("approval_number between", value1, value2, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotBetween(String value1, String value2) {
            addCriterion("approval_number not between", value1, value2, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrIsNull() {
            addCriterion("purchase_attr is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrIsNotNull() {
            addCriterion("purchase_attr is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrEqualTo(String value) {
            addCriterion("purchase_attr =", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrNotEqualTo(String value) {
            addCriterion("purchase_attr <>", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrGreaterThan(String value) {
            addCriterion("purchase_attr >", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_attr >=", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrLessThan(String value) {
            addCriterion("purchase_attr <", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrLessThanOrEqualTo(String value) {
            addCriterion("purchase_attr <=", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrLike(String value) {
            addCriterion("purchase_attr like", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrNotLike(String value) {
            addCriterion("purchase_attr not like", value, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrIn(List<String> values) {
            addCriterion("purchase_attr in", values, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrNotIn(List<String> values) {
            addCriterion("purchase_attr not in", values, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrBetween(String value1, String value2) {
            addCriterion("purchase_attr between", value1, value2, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseAttrNotBetween(String value1, String value2) {
            addCriterion("purchase_attr not between", value1, value2, "purchaseAttr");
            return (Criteria) this;
        }

        public Criteria andChooseReasonIsNull() {
            addCriterion("choose_reason is null");
            return (Criteria) this;
        }

        public Criteria andChooseReasonIsNotNull() {
            addCriterion("choose_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChooseReasonEqualTo(String value) {
            addCriterion("choose_reason =", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonNotEqualTo(String value) {
            addCriterion("choose_reason <>", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonGreaterThan(String value) {
            addCriterion("choose_reason >", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonGreaterThanOrEqualTo(String value) {
            addCriterion("choose_reason >=", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonLessThan(String value) {
            addCriterion("choose_reason <", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonLessThanOrEqualTo(String value) {
            addCriterion("choose_reason <=", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonLike(String value) {
            addCriterion("choose_reason like", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonNotLike(String value) {
            addCriterion("choose_reason not like", value, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonIn(List<String> values) {
            addCriterion("choose_reason in", values, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonNotIn(List<String> values) {
            addCriterion("choose_reason not in", values, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonBetween(String value1, String value2) {
            addCriterion("choose_reason between", value1, value2, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andChooseReasonNotBetween(String value1, String value2) {
            addCriterion("choose_reason not between", value1, value2, "chooseReason");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateIsNull() {
            addCriterion("store_sales_rate is null");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateIsNotNull() {
            addCriterion("store_sales_rate is not null");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateEqualTo(BigDecimal value) {
            addCriterion("store_sales_rate =", value, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateNotEqualTo(BigDecimal value) {
            addCriterion("store_sales_rate <>", value, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateGreaterThan(BigDecimal value) {
            addCriterion("store_sales_rate >", value, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_sales_rate >=", value, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateLessThan(BigDecimal value) {
            addCriterion("store_sales_rate <", value, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_sales_rate <=", value, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateIn(List<BigDecimal> values) {
            addCriterion("store_sales_rate in", values, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateNotIn(List<BigDecimal> values) {
            addCriterion("store_sales_rate not in", values, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_sales_rate between", value1, value2, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStoreSalesRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_sales_rate not between", value1, value2, "storeSalesRate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}