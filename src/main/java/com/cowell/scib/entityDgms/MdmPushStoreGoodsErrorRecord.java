package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> mdm一店一目下发失败记录
 */
public class MdmPushStoreGoodsErrorRecord implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 数据id
     */
    private String autoCode;

    /**
     * 最小陈列量
     */
    private String minDisplayQuantity;

    /**
     * 必备层级
     */
    private String necessaryTagDesc;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getAutoCode() {
        return autoCode;
    }

    public void setAutoCode(String autoCode) {
        this.autoCode = autoCode;
    }

    public String getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(String minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public String getNecessaryTagDesc() {
        return necessaryTagDesc;
    }

    public void setNecessaryTagDesc(String necessaryTagDesc) {
        this.necessaryTagDesc = necessaryTagDesc;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MdmPushStoreGoodsErrorRecord other = (MdmPushStoreGoodsErrorRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getAutoCode() == null ? other.getAutoCode() == null : this.getAutoCode().equals(other.getAutoCode()))
            && (this.getMinDisplayQuantity() == null ? other.getMinDisplayQuantity() == null : this.getMinDisplayQuantity().equals(other.getMinDisplayQuantity()))
            && (this.getNecessaryTagDesc() == null ? other.getNecessaryTagDesc() == null : this.getNecessaryTagDesc().equals(other.getNecessaryTagDesc()))
            && (this.getErrorMsg() == null ? other.getErrorMsg() == null : this.getErrorMsg().equals(other.getErrorMsg()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getAutoCode() == null) ? 0 : getAutoCode().hashCode());
        result = prime * result + ((getMinDisplayQuantity() == null) ? 0 : getMinDisplayQuantity().hashCode());
        result = prime * result + ((getNecessaryTagDesc() == null) ? 0 : getNecessaryTagDesc().hashCode());
        result = prime * result + ((getErrorMsg() == null) ? 0 : getErrorMsg().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", autoCode=").append(autoCode);
        sb.append(", minDisplayQuantity=").append(minDisplayQuantity);
        sb.append(", necessaryTagDesc=").append(necessaryTagDesc);
        sb.append(", errorMsg=").append(errorMsg);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}