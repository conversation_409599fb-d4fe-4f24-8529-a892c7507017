package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 一店一目表
 */
public class StoreGoodsContents implements Serializable {
    /**
     * 分布式主键
     */
    private Long id;

    /**
     * 门店orgID
     */
    private Long storeOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 子类id
     */
    private Long subCategoryId;

    /**
     * 必备标识(0非必备)
     */
    private Integer necessaryTag;

    /**
     * 必备标识名
     */
    private String necessaryTagName;

    /**
     * 经营状态
     */
    private Integer manageStatus;

    /**
     * 建议经营状态
     */
    private Integer suggestManageStatus;

    /**
     * 禁止请货
     */
    private String forbidApply;

    /**
     * 禁止配送
     */
    private String forbidDistr;

    /**
     * 禁止销售
     */
    private String forbidSale;

    /**
     * 禁止返仓
     */
    private String forbidReturnWarehouse;

    /**
     * 禁止调拨
     */
    private String forbidReturnAllot;

    /**
     * 是否敏感品
     */
    private String sensitive;

    /**
     * 销售属性
     */
    private String pushLevel;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 请货最大上线
     */
    private BigDecimal applyLimitUpper;

    /**
     * 是否有效(0 否 1 是)
     */
    private Byte effectStatus;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Long getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Long subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public Integer getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(Integer necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public String getNecessaryTagName() {
        return necessaryTagName;
    }

    public void setNecessaryTagName(String necessaryTagName) {
        this.necessaryTagName = necessaryTagName;
    }

    public Integer getManageStatus() {
        return manageStatus;
    }

    public void setManageStatus(Integer manageStatus) {
        this.manageStatus = manageStatus;
    }

    public Integer getSuggestManageStatus() {
        return suggestManageStatus;
    }

    public void setSuggestManageStatus(Integer suggestManageStatus) {
        this.suggestManageStatus = suggestManageStatus;
    }

    public String getForbidApply() {
        return forbidApply;
    }

    public void setForbidApply(String forbidApply) {
        this.forbidApply = forbidApply;
    }

    public String getForbidDistr() {
        return forbidDistr;
    }

    public void setForbidDistr(String forbidDistr) {
        this.forbidDistr = forbidDistr;
    }

    public String getForbidSale() {
        return forbidSale;
    }

    public void setForbidSale(String forbidSale) {
        this.forbidSale = forbidSale;
    }

    public String getForbidReturnWarehouse() {
        return forbidReturnWarehouse;
    }

    public void setForbidReturnWarehouse(String forbidReturnWarehouse) {
        this.forbidReturnWarehouse = forbidReturnWarehouse;
    }

    public String getForbidReturnAllot() {
        return forbidReturnAllot;
    }

    public void setForbidReturnAllot(String forbidReturnAllot) {
        this.forbidReturnAllot = forbidReturnAllot;
    }

    public String getSensitive() {
        return sensitive;
    }

    public void setSensitive(String sensitive) {
        this.sensitive = sensitive;
    }

    public String getPushLevel() {
        return pushLevel;
    }

    public void setPushLevel(String pushLevel) {
        this.pushLevel = pushLevel;
    }

    public BigDecimal getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(BigDecimal minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public BigDecimal getApplyLimitUpper() {
        return applyLimitUpper;
    }

    public void setApplyLimitUpper(BigDecimal applyLimitUpper) {
        this.applyLimitUpper = applyLimitUpper;
    }

    public Byte getEffectStatus() {
        return effectStatus;
    }

    public void setEffectStatus(Byte effectStatus) {
        this.effectStatus = effectStatus;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StoreGoodsContents other = (StoreGoodsContents) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getStoreOrgId() == null ? other.getStoreOrgId() == null : this.getStoreOrgId().equals(other.getStoreOrgId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getSubCategoryId() == null ? other.getSubCategoryId() == null : this.getSubCategoryId().equals(other.getSubCategoryId()))
            && (this.getNecessaryTag() == null ? other.getNecessaryTag() == null : this.getNecessaryTag().equals(other.getNecessaryTag()))
            && (this.getNecessaryTagName() == null ? other.getNecessaryTagName() == null : this.getNecessaryTagName().equals(other.getNecessaryTagName()))
            && (this.getManageStatus() == null ? other.getManageStatus() == null : this.getManageStatus().equals(other.getManageStatus()))
            && (this.getSuggestManageStatus() == null ? other.getSuggestManageStatus() == null : this.getSuggestManageStatus().equals(other.getSuggestManageStatus()))
            && (this.getForbidApply() == null ? other.getForbidApply() == null : this.getForbidApply().equals(other.getForbidApply()))
            && (this.getForbidDistr() == null ? other.getForbidDistr() == null : this.getForbidDistr().equals(other.getForbidDistr()))
            && (this.getForbidSale() == null ? other.getForbidSale() == null : this.getForbidSale().equals(other.getForbidSale()))
            && (this.getForbidReturnWarehouse() == null ? other.getForbidReturnWarehouse() == null : this.getForbidReturnWarehouse().equals(other.getForbidReturnWarehouse()))
            && (this.getForbidReturnAllot() == null ? other.getForbidReturnAllot() == null : this.getForbidReturnAllot().equals(other.getForbidReturnAllot()))
            && (this.getSensitive() == null ? other.getSensitive() == null : this.getSensitive().equals(other.getSensitive()))
            && (this.getPushLevel() == null ? other.getPushLevel() == null : this.getPushLevel().equals(other.getPushLevel()))
            && (this.getMinDisplayQuantity() == null ? other.getMinDisplayQuantity() == null : this.getMinDisplayQuantity().equals(other.getMinDisplayQuantity()))
            && (this.getApplyLimitUpper() == null ? other.getApplyLimitUpper() == null : this.getApplyLimitUpper().equals(other.getApplyLimitUpper()))
            && (this.getEffectStatus() == null ? other.getEffectStatus() == null : this.getEffectStatus().equals(other.getEffectStatus()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getStoreOrgId() == null) ? 0 : getStoreOrgId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getSubCategoryId() == null) ? 0 : getSubCategoryId().hashCode());
        result = prime * result + ((getNecessaryTag() == null) ? 0 : getNecessaryTag().hashCode());
        result = prime * result + ((getNecessaryTagName() == null) ? 0 : getNecessaryTagName().hashCode());
        result = prime * result + ((getManageStatus() == null) ? 0 : getManageStatus().hashCode());
        result = prime * result + ((getSuggestManageStatus() == null) ? 0 : getSuggestManageStatus().hashCode());
        result = prime * result + ((getForbidApply() == null) ? 0 : getForbidApply().hashCode());
        result = prime * result + ((getForbidDistr() == null) ? 0 : getForbidDistr().hashCode());
        result = prime * result + ((getForbidSale() == null) ? 0 : getForbidSale().hashCode());
        result = prime * result + ((getForbidReturnWarehouse() == null) ? 0 : getForbidReturnWarehouse().hashCode());
        result = prime * result + ((getForbidReturnAllot() == null) ? 0 : getForbidReturnAllot().hashCode());
        result = prime * result + ((getSensitive() == null) ? 0 : getSensitive().hashCode());
        result = prime * result + ((getPushLevel() == null) ? 0 : getPushLevel().hashCode());
        result = prime * result + ((getMinDisplayQuantity() == null) ? 0 : getMinDisplayQuantity().hashCode());
        result = prime * result + ((getApplyLimitUpper() == null) ? 0 : getApplyLimitUpper().hashCode());
        result = prime * result + ((getEffectStatus() == null) ? 0 : getEffectStatus().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", storeOrgId=").append(storeOrgId);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", subCategoryId=").append(subCategoryId);
        sb.append(", necessaryTag=").append(necessaryTag);
        sb.append(", necessaryTagName=").append(necessaryTagName);
        sb.append(", manageStatus=").append(manageStatus);
        sb.append(", suggestManageStatus=").append(suggestManageStatus);
        sb.append(", forbidApply=").append(forbidApply);
        sb.append(", forbidDistr=").append(forbidDistr);
        sb.append(", forbidSale=").append(forbidSale);
        sb.append(", forbidReturnWarehouse=").append(forbidReturnWarehouse);
        sb.append(", forbidReturnAllot=").append(forbidReturnAllot);
        sb.append(", sensitive=").append(sensitive);
        sb.append(", pushLevel=").append(pushLevel);
        sb.append(", minDisplayQuantity=").append(minDisplayQuantity);
        sb.append(", applyLimitUpper=").append(applyLimitUpper);
        sb.append(", effectStatus=").append(effectStatus);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}