package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OnlineHotGoodsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public OnlineHotGoodsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceIsNull() {
            addCriterion("goods_source is null");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceIsNotNull() {
            addCriterion("goods_source is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceEqualTo(String value) {
            addCriterion("goods_source =", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceNotEqualTo(String value) {
            addCriterion("goods_source <>", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceGreaterThan(String value) {
            addCriterion("goods_source >", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceGreaterThanOrEqualTo(String value) {
            addCriterion("goods_source >=", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceLessThan(String value) {
            addCriterion("goods_source <", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceLessThanOrEqualTo(String value) {
            addCriterion("goods_source <=", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceLike(String value) {
            addCriterion("goods_source like", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceNotLike(String value) {
            addCriterion("goods_source not like", value, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceIn(List<String> values) {
            addCriterion("goods_source in", values, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceNotIn(List<String> values) {
            addCriterion("goods_source not in", values, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceBetween(String value1, String value2) {
            addCriterion("goods_source between", value1, value2, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsSourceNotBetween(String value1, String value2) {
            addCriterion("goods_source not between", value1, value2, "goodsSource");
            return (Criteria) this;
        }

        public Criteria andGoodsYearIsNull() {
            addCriterion("goods_year is null");
            return (Criteria) this;
        }

        public Criteria andGoodsYearIsNotNull() {
            addCriterion("goods_year is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsYearEqualTo(String value) {
            addCriterion("goods_year =", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearNotEqualTo(String value) {
            addCriterion("goods_year <>", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearGreaterThan(String value) {
            addCriterion("goods_year >", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearGreaterThanOrEqualTo(String value) {
            addCriterion("goods_year >=", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearLessThan(String value) {
            addCriterion("goods_year <", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearLessThanOrEqualTo(String value) {
            addCriterion("goods_year <=", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearLike(String value) {
            addCriterion("goods_year like", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearNotLike(String value) {
            addCriterion("goods_year not like", value, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearIn(List<String> values) {
            addCriterion("goods_year in", values, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearNotIn(List<String> values) {
            addCriterion("goods_year not in", values, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearBetween(String value1, String value2) {
            addCriterion("goods_year between", value1, value2, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsYearNotBetween(String value1, String value2) {
            addCriterion("goods_year not between", value1, value2, "goodsYear");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionIsNull() {
            addCriterion("goods_time_dimension is null");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionIsNotNull() {
            addCriterion("goods_time_dimension is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionEqualTo(String value) {
            addCriterion("goods_time_dimension =", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionNotEqualTo(String value) {
            addCriterion("goods_time_dimension <>", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionGreaterThan(String value) {
            addCriterion("goods_time_dimension >", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionGreaterThanOrEqualTo(String value) {
            addCriterion("goods_time_dimension >=", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionLessThan(String value) {
            addCriterion("goods_time_dimension <", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionLessThanOrEqualTo(String value) {
            addCriterion("goods_time_dimension <=", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionLike(String value) {
            addCriterion("goods_time_dimension like", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionNotLike(String value) {
            addCriterion("goods_time_dimension not like", value, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionIn(List<String> values) {
            addCriterion("goods_time_dimension in", values, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionNotIn(List<String> values) {
            addCriterion("goods_time_dimension not in", values, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionBetween(String value1, String value2) {
            addCriterion("goods_time_dimension between", value1, value2, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeDimensionNotBetween(String value1, String value2) {
            addCriterion("goods_time_dimension not between", value1, value2, "goodsTimeDimension");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameIsNull() {
            addCriterion("goods_time_frame is null");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameIsNotNull() {
            addCriterion("goods_time_frame is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameEqualTo(String value) {
            addCriterion("goods_time_frame =", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameNotEqualTo(String value) {
            addCriterion("goods_time_frame <>", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameGreaterThan(String value) {
            addCriterion("goods_time_frame >", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_time_frame >=", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameLessThan(String value) {
            addCriterion("goods_time_frame <", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameLessThanOrEqualTo(String value) {
            addCriterion("goods_time_frame <=", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameLike(String value) {
            addCriterion("goods_time_frame like", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameNotLike(String value) {
            addCriterion("goods_time_frame not like", value, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameIn(List<String> values) {
            addCriterion("goods_time_frame in", values, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameNotIn(List<String> values) {
            addCriterion("goods_time_frame not in", values, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameBetween(String value1, String value2) {
            addCriterion("goods_time_frame between", value1, value2, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsTimeFrameNotBetween(String value1, String value2) {
            addCriterion("goods_time_frame not between", value1, value2, "goodsTimeFrame");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceIsNull() {
            addCriterion("goods_province is null");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceIsNotNull() {
            addCriterion("goods_province is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceEqualTo(String value) {
            addCriterion("goods_province =", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceNotEqualTo(String value) {
            addCriterion("goods_province <>", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceGreaterThan(String value) {
            addCriterion("goods_province >", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("goods_province >=", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceLessThan(String value) {
            addCriterion("goods_province <", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceLessThanOrEqualTo(String value) {
            addCriterion("goods_province <=", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceLike(String value) {
            addCriterion("goods_province like", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceNotLike(String value) {
            addCriterion("goods_province not like", value, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceIn(List<String> values) {
            addCriterion("goods_province in", values, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceNotIn(List<String> values) {
            addCriterion("goods_province not in", values, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceBetween(String value1, String value2) {
            addCriterion("goods_province between", value1, value2, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsProvinceNotBetween(String value1, String value2) {
            addCriterion("goods_province not between", value1, value2, "goodsProvince");
            return (Criteria) this;
        }

        public Criteria andGoodsCityIsNull() {
            addCriterion("goods_city is null");
            return (Criteria) this;
        }

        public Criteria andGoodsCityIsNotNull() {
            addCriterion("goods_city is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsCityEqualTo(String value) {
            addCriterion("goods_city =", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityNotEqualTo(String value) {
            addCriterion("goods_city <>", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityGreaterThan(String value) {
            addCriterion("goods_city >", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityGreaterThanOrEqualTo(String value) {
            addCriterion("goods_city >=", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityLessThan(String value) {
            addCriterion("goods_city <", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityLessThanOrEqualTo(String value) {
            addCriterion("goods_city <=", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityLike(String value) {
            addCriterion("goods_city like", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityNotLike(String value) {
            addCriterion("goods_city not like", value, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityIn(List<String> values) {
            addCriterion("goods_city in", values, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityNotIn(List<String> values) {
            addCriterion("goods_city not in", values, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityBetween(String value1, String value2) {
            addCriterion("goods_city between", value1, value2, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andGoodsCityNotBetween(String value1, String value2) {
            addCriterion("goods_city not between", value1, value2, "goodsCity");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNull() {
            addCriterion("bar_code is null");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNotNull() {
            addCriterion("bar_code is not null");
            return (Criteria) this;
        }

        public Criteria andBarCodeEqualTo(String value) {
            addCriterion("bar_code =", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotEqualTo(String value) {
            addCriterion("bar_code <>", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThan(String value) {
            addCriterion("bar_code >", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bar_code >=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThan(String value) {
            addCriterion("bar_code <", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThanOrEqualTo(String value) {
            addCriterion("bar_code <=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLike(String value) {
            addCriterion("bar_code like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotLike(String value) {
            addCriterion("bar_code not like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeIn(List<String> values) {
            addCriterion("bar_code in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotIn(List<String> values) {
            addCriterion("bar_code not in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeBetween(String value1, String value2) {
            addCriterion("bar_code between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotBetween(String value1, String value2) {
            addCriterion("bar_code not between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsRankIsNull() {
            addCriterion("goods_rank is null");
            return (Criteria) this;
        }

        public Criteria andGoodsRankIsNotNull() {
            addCriterion("goods_rank is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsRankEqualTo(String value) {
            addCriterion("goods_rank =", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankNotEqualTo(String value) {
            addCriterion("goods_rank <>", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankGreaterThan(String value) {
            addCriterion("goods_rank >", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankGreaterThanOrEqualTo(String value) {
            addCriterion("goods_rank >=", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankLessThan(String value) {
            addCriterion("goods_rank <", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankLessThanOrEqualTo(String value) {
            addCriterion("goods_rank <=", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankLike(String value) {
            addCriterion("goods_rank like", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankNotLike(String value) {
            addCriterion("goods_rank not like", value, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankIn(List<String> values) {
            addCriterion("goods_rank in", values, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankNotIn(List<String> values) {
            addCriterion("goods_rank not in", values, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankBetween(String value1, String value2) {
            addCriterion("goods_rank between", value1, value2, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andGoodsRankNotBetween(String value1, String value2) {
            addCriterion("goods_rank not between", value1, value2, "goodsRank");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNull() {
            addCriterion("create_by_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNotNull() {
            addCriterion("create_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdEqualTo(Long value) {
            addCriterion("create_by_id =", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotEqualTo(Long value) {
            addCriterion("create_by_id <>", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThan(Long value) {
            addCriterion("create_by_id >", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by_id >=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThan(Long value) {
            addCriterion("create_by_id <", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThanOrEqualTo(Long value) {
            addCriterion("create_by_id <=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIn(List<Long> values) {
            addCriterion("create_by_id in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotIn(List<Long> values) {
            addCriterion("create_by_id not in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdBetween(Long value1, Long value2) {
            addCriterion("create_by_id between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotBetween(Long value1, Long value2) {
            addCriterion("create_by_id not between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNull() {
            addCriterion("update_by_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNotNull() {
            addCriterion("update_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdEqualTo(Long value) {
            addCriterion("update_by_id =", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotEqualTo(Long value) {
            addCriterion("update_by_id <>", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThan(Long value) {
            addCriterion("update_by_id >", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by_id >=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThan(Long value) {
            addCriterion("update_by_id <", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThanOrEqualTo(Long value) {
            addCriterion("update_by_id <=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIn(List<Long> values) {
            addCriterion("update_by_id in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotIn(List<Long> values) {
            addCriterion("update_by_id not in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdBetween(Long value1, Long value2) {
            addCriterion("update_by_id between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotBetween(Long value1, Long value2) {
            addCriterion("update_by_id not between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}