package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.util.Date;

/**
 * bundling_task_store_detail
 * <AUTHOR>
public class BundlingTaskStoreDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 组货对应店型，大店/中店/小店
     */
    private String bundlStore;

    /**
     * 是否参与组货系统建议 0-否 1-是
     */
    private Byte bundlAdviceAble;

    /**
     * 是否参与组货业务确认 0-否 1-是
     */
    private Byte bundlConfirmAble;

    /**
     * 高济连锁id
     */
    private Long businessId;

    /**
     * 高济连锁门店id
     */
    private Long storeId;

    /**
     * 高济连锁id
     */
    private Long storeOrgId;

    /**
     * 高济连锁id
     */
    private Long companyOrgId;

    /**
     * 连锁企业MDM编码
     */
    private String companyCode;

    /**
     * 门店编码：SAP编码，编码段A000-ZZZZ
     */
    private String storeCode;

    /**
     * 组织机构名称
     */
    private String companyName;

    /**
     * 门店简称: 连锁企业+地级市/县级市+核心地名+店
     */
    private String storeName;

    /**
     * 月销售额等级
     */
    private String salesLevel;

    /**
     * 选址商圈店型
     */
    private String tradingArea;

    /**
     * 省: 门店所在省份
     */
    private String province;

    /**
     * 城市: 门店所在城市
     */
    private String city;

    /**
     * 区/县
     */
    private String area;

    /**
     * 详细地址: 详细地址到门牌号，乡镇店
     */
    private String address;

    /**
     * 门店状态: 营业、永久性闭店、暂时性闭店
     */
    private String storeStatus;

    /**
     * 开业日期: 正式营业的开始日期，对于收购门店，以收购店实际开业日期为准；如原系统没有记录或不能确认收购门店的开业日期，则以交割日期为准
     */
    private String openDate;

    /**
     * 关店日期: 如果门店状态为闭店，此字段必输
     */
    private String closeDate;

    /**
     * 门店属性: 直营-自建、加盟、托管、直营-并购
     */
    private String storeAttr;

    /**
     * 业态 商超/批发
     */
    private String format;

    /**
     * 特殊业态店型
     */
    private String operationType;

    /**
     * 特殊业务类型 DTP/慢病
     */
    private String specialType;

    /**
     * dtp门店
     */
    private String dtp;

    /**
     * 平台店型编码
     */
    private String platStoreTypeCode;

    /**
     * 组货店型编码
     */
    private String storeTypeCode;

    /**
     * 中参店型编码
     */
    private String zsStoreTypeCode;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getBundlStore() {
        return bundlStore;
    }

    public void setBundlStore(String bundlStore) {
        this.bundlStore = bundlStore;
    }

    public Byte getBundlAdviceAble() {
        return bundlAdviceAble;
    }

    public void setBundlAdviceAble(Byte bundlAdviceAble) {
        this.bundlAdviceAble = bundlAdviceAble;
    }

    public Byte getBundlConfirmAble() {
        return bundlConfirmAble;
    }

    public void setBundlConfirmAble(Byte bundlConfirmAble) {
        this.bundlConfirmAble = bundlConfirmAble;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSalesLevel() {
        return salesLevel;
    }

    public void setSalesLevel(String salesLevel) {
        this.salesLevel = salesLevel;
    }

    public String getTradingArea() {
        return tradingArea;
    }

    public void setTradingArea(String tradingArea) {
        this.tradingArea = tradingArea;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getStoreStatus() {
        return storeStatus;
    }

    public void setStoreStatus(String storeStatus) {
        this.storeStatus = storeStatus;
    }

    public String getOpenDate() {
        return openDate;
    }

    public void setOpenDate(String openDate) {
        this.openDate = openDate;
    }

    public String getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(String closeDate) {
        this.closeDate = closeDate;
    }

    public String getStoreAttr() {
        return storeAttr;
    }

    public void setStoreAttr(String storeAttr) {
        this.storeAttr = storeAttr;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getSpecialType() {
        return specialType;
    }

    public void setSpecialType(String specialType) {
        this.specialType = specialType;
    }

    public String getDtp() {
        return dtp;
    }

    public void setDtp(String dtp) {
        this.dtp = dtp;
    }

    public String getPlatStoreTypeCode() {
        return platStoreTypeCode;
    }

    public void setPlatStoreTypeCode(String platStoreTypeCode) {
        this.platStoreTypeCode = platStoreTypeCode;
    }

    public String getStoreTypeCode() {
        return storeTypeCode;
    }

    public void setStoreTypeCode(String storeTypeCode) {
        this.storeTypeCode = storeTypeCode;
    }

    public String getZsStoreTypeCode() {
        return zsStoreTypeCode;
    }

    public void setZsStoreTypeCode(String zsStoreTypeCode) {
        this.zsStoreTypeCode = zsStoreTypeCode;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BundlingTaskStoreDetail other = (BundlingTaskStoreDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getTaskCode() == null ? other.getTaskCode() == null : this.getTaskCode().equals(other.getTaskCode()))
            && (this.getBundlStore() == null ? other.getBundlStore() == null : this.getBundlStore().equals(other.getBundlStore()))
            && (this.getBundlAdviceAble() == null ? other.getBundlAdviceAble() == null : this.getBundlAdviceAble().equals(other.getBundlAdviceAble()))
            && (this.getBundlConfirmAble() == null ? other.getBundlConfirmAble() == null : this.getBundlConfirmAble().equals(other.getBundlConfirmAble()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreOrgId() == null ? other.getStoreOrgId() == null : this.getStoreOrgId().equals(other.getStoreOrgId()))
            && (this.getCompanyOrgId() == null ? other.getCompanyOrgId() == null : this.getCompanyOrgId().equals(other.getCompanyOrgId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getSalesLevel() == null ? other.getSalesLevel() == null : this.getSalesLevel().equals(other.getSalesLevel()))
            && (this.getTradingArea() == null ? other.getTradingArea() == null : this.getTradingArea().equals(other.getTradingArea()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getArea() == null ? other.getArea() == null : this.getArea().equals(other.getArea()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getStoreStatus() == null ? other.getStoreStatus() == null : this.getStoreStatus().equals(other.getStoreStatus()))
            && (this.getOpenDate() == null ? other.getOpenDate() == null : this.getOpenDate().equals(other.getOpenDate()))
            && (this.getCloseDate() == null ? other.getCloseDate() == null : this.getCloseDate().equals(other.getCloseDate()))
            && (this.getStoreAttr() == null ? other.getStoreAttr() == null : this.getStoreAttr().equals(other.getStoreAttr()))
            && (this.getFormat() == null ? other.getFormat() == null : this.getFormat().equals(other.getFormat()))
            && (this.getOperationType() == null ? other.getOperationType() == null : this.getOperationType().equals(other.getOperationType()))
            && (this.getSpecialType() == null ? other.getSpecialType() == null : this.getSpecialType().equals(other.getSpecialType()))
            && (this.getDtp() == null ? other.getDtp() == null : this.getDtp().equals(other.getDtp()))
            && (this.getPlatStoreTypeCode() == null ? other.getPlatStoreTypeCode() == null : this.getPlatStoreTypeCode().equals(other.getPlatStoreTypeCode()))
            && (this.getStoreTypeCode() == null ? other.getStoreTypeCode() == null : this.getStoreTypeCode().equals(other.getStoreTypeCode()))
            && (this.getZsStoreTypeCode() == null ? other.getZsStoreTypeCode() == null : this.getZsStoreTypeCode().equals(other.getZsStoreTypeCode()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getTaskCode() == null) ? 0 : getTaskCode().hashCode());
        result = prime * result + ((getBundlStore() == null) ? 0 : getBundlStore().hashCode());
        result = prime * result + ((getBundlAdviceAble() == null) ? 0 : getBundlAdviceAble().hashCode());
        result = prime * result + ((getBundlConfirmAble() == null) ? 0 : getBundlConfirmAble().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreOrgId() == null) ? 0 : getStoreOrgId().hashCode());
        result = prime * result + ((getCompanyOrgId() == null) ? 0 : getCompanyOrgId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getSalesLevel() == null) ? 0 : getSalesLevel().hashCode());
        result = prime * result + ((getTradingArea() == null) ? 0 : getTradingArea().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getArea() == null) ? 0 : getArea().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getStoreStatus() == null) ? 0 : getStoreStatus().hashCode());
        result = prime * result + ((getOpenDate() == null) ? 0 : getOpenDate().hashCode());
        result = prime * result + ((getCloseDate() == null) ? 0 : getCloseDate().hashCode());
        result = prime * result + ((getStoreAttr() == null) ? 0 : getStoreAttr().hashCode());
        result = prime * result + ((getFormat() == null) ? 0 : getFormat().hashCode());
        result = prime * result + ((getOperationType() == null) ? 0 : getOperationType().hashCode());
        result = prime * result + ((getSpecialType() == null) ? 0 : getSpecialType().hashCode());
        result = prime * result + ((getDtp() == null) ? 0 : getDtp().hashCode());
        result = prime * result + ((getPlatStoreTypeCode() == null) ? 0 : getPlatStoreTypeCode().hashCode());
        result = prime * result + ((getStoreTypeCode() == null) ? 0 : getStoreTypeCode().hashCode());
        result = prime * result + ((getZsStoreTypeCode() == null) ? 0 : getZsStoreTypeCode().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", taskCode=").append(taskCode);
        sb.append(", bundlStore=").append(bundlStore);
        sb.append(", bundlAdviceAble=").append(bundlAdviceAble);
        sb.append(", bundlConfirmAble=").append(bundlConfirmAble);
        sb.append(", businessId=").append(businessId);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeOrgId=").append(storeOrgId);
        sb.append(", companyOrgId=").append(companyOrgId);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", storeName=").append(storeName);
        sb.append(", salesLevel=").append(salesLevel);
        sb.append(", tradingArea=").append(tradingArea);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", area=").append(area);
        sb.append(", address=").append(address);
        sb.append(", storeStatus=").append(storeStatus);
        sb.append(", openDate=").append(openDate);
        sb.append(", closeDate=").append(closeDate);
        sb.append(", storeAttr=").append(storeAttr);
        sb.append(", format=").append(format);
        sb.append(", operationType=").append(operationType);
        sb.append(", specialType=").append(specialType);
        sb.append(", dtp=").append(dtp);
        sb.append(", platStoreTypeCode=").append(platStoreTypeCode);
        sb.append(", storeTypeCode=").append(storeTypeCode);
        sb.append(", zsStoreTypeCode=").append(zsStoreTypeCode);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}