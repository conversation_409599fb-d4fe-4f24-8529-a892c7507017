package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlSkuLimitExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlSkuLimitExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeIsNull() {
            addCriterion("property_code is null");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeIsNotNull() {
            addCriterion("property_code is not null");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeEqualTo(String value) {
            addCriterion("property_code =", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeNotEqualTo(String value) {
            addCriterion("property_code <>", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeGreaterThan(String value) {
            addCriterion("property_code >", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("property_code >=", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeLessThan(String value) {
            addCriterion("property_code <", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeLessThanOrEqualTo(String value) {
            addCriterion("property_code <=", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeLike(String value) {
            addCriterion("property_code like", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeNotLike(String value) {
            addCriterion("property_code not like", value, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeIn(List<String> values) {
            addCriterion("property_code in", values, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeNotIn(List<String> values) {
            addCriterion("property_code not in", values, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeBetween(String value1, String value2) {
            addCriterion("property_code between", value1, value2, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyCodeNotBetween(String value1, String value2) {
            addCriterion("property_code not between", value1, value2, "propertyCode");
            return (Criteria) this;
        }

        public Criteria andPropertyDescIsNull() {
            addCriterion("property_desc is null");
            return (Criteria) this;
        }

        public Criteria andPropertyDescIsNotNull() {
            addCriterion("property_desc is not null");
            return (Criteria) this;
        }

        public Criteria andPropertyDescEqualTo(String value) {
            addCriterion("property_desc =", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescNotEqualTo(String value) {
            addCriterion("property_desc <>", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescGreaterThan(String value) {
            addCriterion("property_desc >", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescGreaterThanOrEqualTo(String value) {
            addCriterion("property_desc >=", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescLessThan(String value) {
            addCriterion("property_desc <", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescLessThanOrEqualTo(String value) {
            addCriterion("property_desc <=", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescLike(String value) {
            addCriterion("property_desc like", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescNotLike(String value) {
            addCriterion("property_desc not like", value, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescIn(List<String> values) {
            addCriterion("property_desc in", values, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescNotIn(List<String> values) {
            addCriterion("property_desc not in", values, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescBetween(String value1, String value2) {
            addCriterion("property_desc between", value1, value2, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPropertyDescNotBetween(String value1, String value2) {
            addCriterion("property_desc not between", value1, value2, "propertyDesc");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNull() {
            addCriterion("platform_org_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNotNull() {
            addCriterion("platform_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdEqualTo(Long value) {
            addCriterion("platform_org_id =", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotEqualTo(Long value) {
            addCriterion("platform_org_id <>", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThan(Long value) {
            addCriterion("platform_org_id >", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("platform_org_id >=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThan(Long value) {
            addCriterion("platform_org_id <", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("platform_org_id <=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIn(List<Long> values) {
            addCriterion("platform_org_id in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotIn(List<Long> values) {
            addCriterion("platform_org_id not in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdBetween(Long value1, Long value2) {
            addCriterion("platform_org_id between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("platform_org_id not between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIsNull() {
            addCriterion("company_org_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIsNotNull() {
            addCriterion("company_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdEqualTo(Long value) {
            addCriterion("company_org_id =", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotEqualTo(Long value) {
            addCriterion("company_org_id <>", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdGreaterThan(Long value) {
            addCriterion("company_org_id >", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_org_id >=", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdLessThan(Long value) {
            addCriterion("company_org_id <", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("company_org_id <=", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIn(List<Long> values) {
            addCriterion("company_org_id in", values, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotIn(List<Long> values) {
            addCriterion("company_org_id not in", values, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdBetween(Long value1, Long value2) {
            addCriterion("company_org_id between", value1, value2, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("company_org_id not between", value1, value2, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeIsNull() {
            addCriterion("store_group_type is null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeIsNotNull() {
            addCriterion("store_group_type is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeEqualTo(String value) {
            addCriterion("store_group_type =", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeNotEqualTo(String value) {
            addCriterion("store_group_type <>", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeGreaterThan(String value) {
            addCriterion("store_group_type >", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeGreaterThanOrEqualTo(String value) {
            addCriterion("store_group_type >=", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeLessThan(String value) {
            addCriterion("store_group_type <", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeLessThanOrEqualTo(String value) {
            addCriterion("store_group_type <=", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeLike(String value) {
            addCriterion("store_group_type like", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeNotLike(String value) {
            addCriterion("store_group_type not like", value, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeIn(List<String> values) {
            addCriterion("store_group_type in", values, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeNotIn(List<String> values) {
            addCriterion("store_group_type not in", values, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeBetween(String value1, String value2) {
            addCriterion("store_group_type between", value1, value2, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupTypeNotBetween(String value1, String value2) {
            addCriterion("store_group_type not between", value1, value2, "storeGroupType");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeIsNull() {
            addCriterion("store_group_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeIsNotNull() {
            addCriterion("store_group_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeEqualTo(String value) {
            addCriterion("store_group_code =", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeNotEqualTo(String value) {
            addCriterion("store_group_code <>", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeGreaterThan(String value) {
            addCriterion("store_group_code >", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_group_code >=", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeLessThan(String value) {
            addCriterion("store_group_code <", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeLessThanOrEqualTo(String value) {
            addCriterion("store_group_code <=", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeLike(String value) {
            addCriterion("store_group_code like", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeNotLike(String value) {
            addCriterion("store_group_code not like", value, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeIn(List<String> values) {
            addCriterion("store_group_code in", values, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeNotIn(List<String> values) {
            addCriterion("store_group_code not in", values, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeBetween(String value1, String value2) {
            addCriterion("store_group_code between", value1, value2, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andStoreGroupCodeNotBetween(String value1, String value2) {
            addCriterion("store_group_code not between", value1, value2, "storeGroupCode");
            return (Criteria) this;
        }

        public Criteria andTotalSkuIsNull() {
            addCriterion("total_sku is null");
            return (Criteria) this;
        }

        public Criteria andTotalSkuIsNotNull() {
            addCriterion("total_sku is not null");
            return (Criteria) this;
        }

        public Criteria andTotalSkuEqualTo(Integer value) {
            addCriterion("total_sku =", value, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuNotEqualTo(Integer value) {
            addCriterion("total_sku <>", value, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuGreaterThan(Integer value) {
            addCriterion("total_sku >", value, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_sku >=", value, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuLessThan(Integer value) {
            addCriterion("total_sku <", value, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuLessThanOrEqualTo(Integer value) {
            addCriterion("total_sku <=", value, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuIn(List<Integer> values) {
            addCriterion("total_sku in", values, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuNotIn(List<Integer> values) {
            addCriterion("total_sku not in", values, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuBetween(Integer value1, Integer value2) {
            addCriterion("total_sku between", value1, value2, "totalSku");
            return (Criteria) this;
        }

        public Criteria andTotalSkuNotBetween(Integer value1, Integer value2) {
            addCriterion("total_sku not between", value1, value2, "totalSku");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNull() {
            addCriterion("create_by_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNotNull() {
            addCriterion("create_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdEqualTo(Long value) {
            addCriterion("create_by_id =", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotEqualTo(Long value) {
            addCriterion("create_by_id <>", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThan(Long value) {
            addCriterion("create_by_id >", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by_id >=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThan(Long value) {
            addCriterion("create_by_id <", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThanOrEqualTo(Long value) {
            addCriterion("create_by_id <=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIn(List<Long> values) {
            addCriterion("create_by_id in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotIn(List<Long> values) {
            addCriterion("create_by_id not in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdBetween(Long value1, Long value2) {
            addCriterion("create_by_id between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotBetween(Long value1, Long value2) {
            addCriterion("create_by_id not between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNull() {
            addCriterion("update_by_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNotNull() {
            addCriterion("update_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdEqualTo(Long value) {
            addCriterion("update_by_id =", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotEqualTo(Long value) {
            addCriterion("update_by_id <>", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThan(Long value) {
            addCriterion("update_by_id >", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by_id >=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThan(Long value) {
            addCriterion("update_by_id <", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThanOrEqualTo(Long value) {
            addCriterion("update_by_id <=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIn(List<Long> values) {
            addCriterion("update_by_id in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotIn(List<Long> values) {
            addCriterion("update_by_id not in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdBetween(Long value1, Long value2) {
            addCriterion("update_by_id between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotBetween(Long value1, Long value2) {
            addCriterion("update_by_id not between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}