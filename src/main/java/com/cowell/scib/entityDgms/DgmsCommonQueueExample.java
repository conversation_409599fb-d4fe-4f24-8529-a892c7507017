package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DgmsCommonQueueExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public DgmsCommonQueueExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andQueueKeyIsNull() {
            addCriterion("queue_key is null");
            return (Criteria) this;
        }

        public Criteria andQueueKeyIsNotNull() {
            addCriterion("queue_key is not null");
            return (Criteria) this;
        }

        public Criteria andQueueKeyEqualTo(String value) {
            addCriterion("queue_key =", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyNotEqualTo(String value) {
            addCriterion("queue_key <>", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyGreaterThan(String value) {
            addCriterion("queue_key >", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyGreaterThanOrEqualTo(String value) {
            addCriterion("queue_key >=", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyLessThan(String value) {
            addCriterion("queue_key <", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyLessThanOrEqualTo(String value) {
            addCriterion("queue_key <=", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyLike(String value) {
            addCriterion("queue_key like", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyNotLike(String value) {
            addCriterion("queue_key not like", value, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyIn(List<String> values) {
            addCriterion("queue_key in", values, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyNotIn(List<String> values) {
            addCriterion("queue_key not in", values, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyBetween(String value1, String value2) {
            addCriterion("queue_key between", value1, value2, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueKeyNotBetween(String value1, String value2) {
            addCriterion("queue_key not between", value1, value2, "queueKey");
            return (Criteria) this;
        }

        public Criteria andQueueValueIsNull() {
            addCriterion("queue_value is null");
            return (Criteria) this;
        }

        public Criteria andQueueValueIsNotNull() {
            addCriterion("queue_value is not null");
            return (Criteria) this;
        }

        public Criteria andQueueValueEqualTo(String value) {
            addCriterion("queue_value =", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueNotEqualTo(String value) {
            addCriterion("queue_value <>", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueGreaterThan(String value) {
            addCriterion("queue_value >", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueGreaterThanOrEqualTo(String value) {
            addCriterion("queue_value >=", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueLessThan(String value) {
            addCriterion("queue_value <", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueLessThanOrEqualTo(String value) {
            addCriterion("queue_value <=", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueLike(String value) {
            addCriterion("queue_value like", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueNotLike(String value) {
            addCriterion("queue_value not like", value, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueIn(List<String> values) {
            addCriterion("queue_value in", values, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueNotIn(List<String> values) {
            addCriterion("queue_value not in", values, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueBetween(String value1, String value2) {
            addCriterion("queue_value between", value1, value2, "queueValue");
            return (Criteria) this;
        }

        public Criteria andQueueValueNotBetween(String value1, String value2) {
            addCriterion("queue_value not between", value1, value2, "queueValue");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}