package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BundlingTaskStoreDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public BundlingTaskStoreDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskCodeIsNull() {
            addCriterion("task_code is null");
            return (Criteria) this;
        }

        public Criteria andTaskCodeIsNotNull() {
            addCriterion("task_code is not null");
            return (Criteria) this;
        }

        public Criteria andTaskCodeEqualTo(String value) {
            addCriterion("task_code =", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotEqualTo(String value) {
            addCriterion("task_code <>", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeGreaterThan(String value) {
            addCriterion("task_code >", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeGreaterThanOrEqualTo(String value) {
            addCriterion("task_code >=", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeLessThan(String value) {
            addCriterion("task_code <", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeLessThanOrEqualTo(String value) {
            addCriterion("task_code <=", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeLike(String value) {
            addCriterion("task_code like", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotLike(String value) {
            addCriterion("task_code not like", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeIn(List<String> values) {
            addCriterion("task_code in", values, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotIn(List<String> values) {
            addCriterion("task_code not in", values, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeBetween(String value1, String value2) {
            addCriterion("task_code between", value1, value2, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotBetween(String value1, String value2) {
            addCriterion("task_code not between", value1, value2, "taskCode");
            return (Criteria) this;
        }

        public Criteria andBundlStoreIsNull() {
            addCriterion("bundl_store is null");
            return (Criteria) this;
        }

        public Criteria andBundlStoreIsNotNull() {
            addCriterion("bundl_store is not null");
            return (Criteria) this;
        }

        public Criteria andBundlStoreEqualTo(String value) {
            addCriterion("bundl_store =", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreNotEqualTo(String value) {
            addCriterion("bundl_store <>", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreGreaterThan(String value) {
            addCriterion("bundl_store >", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreGreaterThanOrEqualTo(String value) {
            addCriterion("bundl_store >=", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreLessThan(String value) {
            addCriterion("bundl_store <", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreLessThanOrEqualTo(String value) {
            addCriterion("bundl_store <=", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreLike(String value) {
            addCriterion("bundl_store like", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreNotLike(String value) {
            addCriterion("bundl_store not like", value, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreIn(List<String> values) {
            addCriterion("bundl_store in", values, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreNotIn(List<String> values) {
            addCriterion("bundl_store not in", values, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreBetween(String value1, String value2) {
            addCriterion("bundl_store between", value1, value2, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlStoreNotBetween(String value1, String value2) {
            addCriterion("bundl_store not between", value1, value2, "bundlStore");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleIsNull() {
            addCriterion("bundl_advice_able is null");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleIsNotNull() {
            addCriterion("bundl_advice_able is not null");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleEqualTo(Byte value) {
            addCriterion("bundl_advice_able =", value, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleNotEqualTo(Byte value) {
            addCriterion("bundl_advice_able <>", value, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleGreaterThan(Byte value) {
            addCriterion("bundl_advice_able >", value, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("bundl_advice_able >=", value, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleLessThan(Byte value) {
            addCriterion("bundl_advice_able <", value, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleLessThanOrEqualTo(Byte value) {
            addCriterion("bundl_advice_able <=", value, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleIn(List<Byte> values) {
            addCriterion("bundl_advice_able in", values, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleNotIn(List<Byte> values) {
            addCriterion("bundl_advice_able not in", values, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleBetween(Byte value1, Byte value2) {
            addCriterion("bundl_advice_able between", value1, value2, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlAdviceAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("bundl_advice_able not between", value1, value2, "bundlAdviceAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleIsNull() {
            addCriterion("bundl_confirm_able is null");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleIsNotNull() {
            addCriterion("bundl_confirm_able is not null");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleEqualTo(Byte value) {
            addCriterion("bundl_confirm_able =", value, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleNotEqualTo(Byte value) {
            addCriterion("bundl_confirm_able <>", value, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleGreaterThan(Byte value) {
            addCriterion("bundl_confirm_able >", value, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("bundl_confirm_able >=", value, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleLessThan(Byte value) {
            addCriterion("bundl_confirm_able <", value, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleLessThanOrEqualTo(Byte value) {
            addCriterion("bundl_confirm_able <=", value, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleIn(List<Byte> values) {
            addCriterion("bundl_confirm_able in", values, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleNotIn(List<Byte> values) {
            addCriterion("bundl_confirm_able not in", values, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleBetween(Byte value1, Byte value2) {
            addCriterion("bundl_confirm_able between", value1, value2, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBundlConfirmAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("bundl_confirm_able not between", value1, value2, "bundlConfirmAble");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNull() {
            addCriterion("store_org_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNotNull() {
            addCriterion("store_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdEqualTo(Long value) {
            addCriterion("store_org_id =", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotEqualTo(Long value) {
            addCriterion("store_org_id <>", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThan(Long value) {
            addCriterion("store_org_id >", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_org_id >=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThan(Long value) {
            addCriterion("store_org_id <", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("store_org_id <=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIn(List<Long> values) {
            addCriterion("store_org_id in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotIn(List<Long> values) {
            addCriterion("store_org_id not in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdBetween(Long value1, Long value2) {
            addCriterion("store_org_id between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("store_org_id not between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIsNull() {
            addCriterion("company_org_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIsNotNull() {
            addCriterion("company_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdEqualTo(Long value) {
            addCriterion("company_org_id =", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotEqualTo(Long value) {
            addCriterion("company_org_id <>", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdGreaterThan(Long value) {
            addCriterion("company_org_id >", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_org_id >=", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdLessThan(Long value) {
            addCriterion("company_org_id <", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("company_org_id <=", value, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdIn(List<Long> values) {
            addCriterion("company_org_id in", values, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotIn(List<Long> values) {
            addCriterion("company_org_id not in", values, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdBetween(Long value1, Long value2) {
            addCriterion("company_org_id between", value1, value2, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("company_org_id not between", value1, value2, "companyOrgId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andSalesLevelIsNull() {
            addCriterion("sales_level is null");
            return (Criteria) this;
        }

        public Criteria andSalesLevelIsNotNull() {
            addCriterion("sales_level is not null");
            return (Criteria) this;
        }

        public Criteria andSalesLevelEqualTo(String value) {
            addCriterion("sales_level =", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelNotEqualTo(String value) {
            addCriterion("sales_level <>", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelGreaterThan(String value) {
            addCriterion("sales_level >", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelGreaterThanOrEqualTo(String value) {
            addCriterion("sales_level >=", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelLessThan(String value) {
            addCriterion("sales_level <", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelLessThanOrEqualTo(String value) {
            addCriterion("sales_level <=", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelLike(String value) {
            addCriterion("sales_level like", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelNotLike(String value) {
            addCriterion("sales_level not like", value, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelIn(List<String> values) {
            addCriterion("sales_level in", values, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelNotIn(List<String> values) {
            addCriterion("sales_level not in", values, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelBetween(String value1, String value2) {
            addCriterion("sales_level between", value1, value2, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andSalesLevelNotBetween(String value1, String value2) {
            addCriterion("sales_level not between", value1, value2, "salesLevel");
            return (Criteria) this;
        }

        public Criteria andTradingAreaIsNull() {
            addCriterion("trading_area is null");
            return (Criteria) this;
        }

        public Criteria andTradingAreaIsNotNull() {
            addCriterion("trading_area is not null");
            return (Criteria) this;
        }

        public Criteria andTradingAreaEqualTo(String value) {
            addCriterion("trading_area =", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaNotEqualTo(String value) {
            addCriterion("trading_area <>", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaGreaterThan(String value) {
            addCriterion("trading_area >", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaGreaterThanOrEqualTo(String value) {
            addCriterion("trading_area >=", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaLessThan(String value) {
            addCriterion("trading_area <", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaLessThanOrEqualTo(String value) {
            addCriterion("trading_area <=", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaLike(String value) {
            addCriterion("trading_area like", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaNotLike(String value) {
            addCriterion("trading_area not like", value, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaIn(List<String> values) {
            addCriterion("trading_area in", values, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaNotIn(List<String> values) {
            addCriterion("trading_area not in", values, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaBetween(String value1, String value2) {
            addCriterion("trading_area between", value1, value2, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andTradingAreaNotBetween(String value1, String value2) {
            addCriterion("trading_area not between", value1, value2, "tradingArea");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andAreaIsNull() {
            addCriterion("area is null");
            return (Criteria) this;
        }

        public Criteria andAreaIsNotNull() {
            addCriterion("area is not null");
            return (Criteria) this;
        }

        public Criteria andAreaEqualTo(String value) {
            addCriterion("area =", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotEqualTo(String value) {
            addCriterion("area <>", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThan(String value) {
            addCriterion("area >", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThanOrEqualTo(String value) {
            addCriterion("area >=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThan(String value) {
            addCriterion("area <", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThanOrEqualTo(String value) {
            addCriterion("area <=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLike(String value) {
            addCriterion("area like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotLike(String value) {
            addCriterion("area not like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaIn(List<String> values) {
            addCriterion("area in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotIn(List<String> values) {
            addCriterion("area not in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaBetween(String value1, String value2) {
            addCriterion("area between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotBetween(String value1, String value2) {
            addCriterion("area not between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andStoreStatusIsNull() {
            addCriterion("store_status is null");
            return (Criteria) this;
        }

        public Criteria andStoreStatusIsNotNull() {
            addCriterion("store_status is not null");
            return (Criteria) this;
        }

        public Criteria andStoreStatusEqualTo(String value) {
            addCriterion("store_status =", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotEqualTo(String value) {
            addCriterion("store_status <>", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusGreaterThan(String value) {
            addCriterion("store_status >", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusGreaterThanOrEqualTo(String value) {
            addCriterion("store_status >=", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusLessThan(String value) {
            addCriterion("store_status <", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusLessThanOrEqualTo(String value) {
            addCriterion("store_status <=", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusLike(String value) {
            addCriterion("store_status like", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotLike(String value) {
            addCriterion("store_status not like", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusIn(List<String> values) {
            addCriterion("store_status in", values, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotIn(List<String> values) {
            addCriterion("store_status not in", values, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusBetween(String value1, String value2) {
            addCriterion("store_status between", value1, value2, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotBetween(String value1, String value2) {
            addCriterion("store_status not between", value1, value2, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNull() {
            addCriterion("open_date is null");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNotNull() {
            addCriterion("open_date is not null");
            return (Criteria) this;
        }

        public Criteria andOpenDateEqualTo(String value) {
            addCriterion("open_date =", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotEqualTo(String value) {
            addCriterion("open_date <>", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThan(String value) {
            addCriterion("open_date >", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThanOrEqualTo(String value) {
            addCriterion("open_date >=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThan(String value) {
            addCriterion("open_date <", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThanOrEqualTo(String value) {
            addCriterion("open_date <=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLike(String value) {
            addCriterion("open_date like", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotLike(String value) {
            addCriterion("open_date not like", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateIn(List<String> values) {
            addCriterion("open_date in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotIn(List<String> values) {
            addCriterion("open_date not in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateBetween(String value1, String value2) {
            addCriterion("open_date between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotBetween(String value1, String value2) {
            addCriterion("open_date not between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateIsNull() {
            addCriterion("close_date is null");
            return (Criteria) this;
        }

        public Criteria andCloseDateIsNotNull() {
            addCriterion("close_date is not null");
            return (Criteria) this;
        }

        public Criteria andCloseDateEqualTo(String value) {
            addCriterion("close_date =", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateNotEqualTo(String value) {
            addCriterion("close_date <>", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateGreaterThan(String value) {
            addCriterion("close_date >", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateGreaterThanOrEqualTo(String value) {
            addCriterion("close_date >=", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateLessThan(String value) {
            addCriterion("close_date <", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateLessThanOrEqualTo(String value) {
            addCriterion("close_date <=", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateLike(String value) {
            addCriterion("close_date like", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateNotLike(String value) {
            addCriterion("close_date not like", value, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateIn(List<String> values) {
            addCriterion("close_date in", values, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateNotIn(List<String> values) {
            addCriterion("close_date not in", values, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateBetween(String value1, String value2) {
            addCriterion("close_date between", value1, value2, "closeDate");
            return (Criteria) this;
        }

        public Criteria andCloseDateNotBetween(String value1, String value2) {
            addCriterion("close_date not between", value1, value2, "closeDate");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNull() {
            addCriterion("store_attr is null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNotNull() {
            addCriterion("store_attr is not null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrEqualTo(String value) {
            addCriterion("store_attr =", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotEqualTo(String value) {
            addCriterion("store_attr <>", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThan(String value) {
            addCriterion("store_attr >", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThanOrEqualTo(String value) {
            addCriterion("store_attr >=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThan(String value) {
            addCriterion("store_attr <", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThanOrEqualTo(String value) {
            addCriterion("store_attr <=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLike(String value) {
            addCriterion("store_attr like", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotLike(String value) {
            addCriterion("store_attr not like", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIn(List<String> values) {
            addCriterion("store_attr in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotIn(List<String> values) {
            addCriterion("store_attr not in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrBetween(String value1, String value2) {
            addCriterion("store_attr between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotBetween(String value1, String value2) {
            addCriterion("store_attr not between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andFormatIsNull() {
            addCriterion("format is null");
            return (Criteria) this;
        }

        public Criteria andFormatIsNotNull() {
            addCriterion("format is not null");
            return (Criteria) this;
        }

        public Criteria andFormatEqualTo(String value) {
            addCriterion("format =", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatNotEqualTo(String value) {
            addCriterion("format <>", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatGreaterThan(String value) {
            addCriterion("format >", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatGreaterThanOrEqualTo(String value) {
            addCriterion("format >=", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatLessThan(String value) {
            addCriterion("format <", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatLessThanOrEqualTo(String value) {
            addCriterion("format <=", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatLike(String value) {
            addCriterion("format like", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatNotLike(String value) {
            addCriterion("format not like", value, "format");
            return (Criteria) this;
        }

        public Criteria andFormatIn(List<String> values) {
            addCriterion("format in", values, "format");
            return (Criteria) this;
        }

        public Criteria andFormatNotIn(List<String> values) {
            addCriterion("format not in", values, "format");
            return (Criteria) this;
        }

        public Criteria andFormatBetween(String value1, String value2) {
            addCriterion("format between", value1, value2, "format");
            return (Criteria) this;
        }

        public Criteria andFormatNotBetween(String value1, String value2) {
            addCriterion("format not between", value1, value2, "format");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIsNull() {
            addCriterion("operation_type is null");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIsNotNull() {
            addCriterion("operation_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperationTypeEqualTo(String value) {
            addCriterion("operation_type =", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotEqualTo(String value) {
            addCriterion("operation_type <>", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeGreaterThan(String value) {
            addCriterion("operation_type >", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("operation_type >=", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLessThan(String value) {
            addCriterion("operation_type <", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLessThanOrEqualTo(String value) {
            addCriterion("operation_type <=", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLike(String value) {
            addCriterion("operation_type like", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotLike(String value) {
            addCriterion("operation_type not like", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIn(List<String> values) {
            addCriterion("operation_type in", values, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotIn(List<String> values) {
            addCriterion("operation_type not in", values, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeBetween(String value1, String value2) {
            addCriterion("operation_type between", value1, value2, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotBetween(String value1, String value2) {
            addCriterion("operation_type not between", value1, value2, "operationType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeIsNull() {
            addCriterion("special_type is null");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeIsNotNull() {
            addCriterion("special_type is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeEqualTo(String value) {
            addCriterion("special_type =", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeNotEqualTo(String value) {
            addCriterion("special_type <>", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeGreaterThan(String value) {
            addCriterion("special_type >", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeGreaterThanOrEqualTo(String value) {
            addCriterion("special_type >=", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeLessThan(String value) {
            addCriterion("special_type <", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeLessThanOrEqualTo(String value) {
            addCriterion("special_type <=", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeLike(String value) {
            addCriterion("special_type like", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeNotLike(String value) {
            addCriterion("special_type not like", value, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeIn(List<String> values) {
            addCriterion("special_type in", values, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeNotIn(List<String> values) {
            addCriterion("special_type not in", values, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeBetween(String value1, String value2) {
            addCriterion("special_type between", value1, value2, "specialType");
            return (Criteria) this;
        }

        public Criteria andSpecialTypeNotBetween(String value1, String value2) {
            addCriterion("special_type not between", value1, value2, "specialType");
            return (Criteria) this;
        }

        public Criteria andDtpIsNull() {
            addCriterion("dtp is null");
            return (Criteria) this;
        }

        public Criteria andDtpIsNotNull() {
            addCriterion("dtp is not null");
            return (Criteria) this;
        }

        public Criteria andDtpEqualTo(String value) {
            addCriterion("dtp =", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpNotEqualTo(String value) {
            addCriterion("dtp <>", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpGreaterThan(String value) {
            addCriterion("dtp >", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpGreaterThanOrEqualTo(String value) {
            addCriterion("dtp >=", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpLessThan(String value) {
            addCriterion("dtp <", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpLessThanOrEqualTo(String value) {
            addCriterion("dtp <=", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpLike(String value) {
            addCriterion("dtp like", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpNotLike(String value) {
            addCriterion("dtp not like", value, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpIn(List<String> values) {
            addCriterion("dtp in", values, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpNotIn(List<String> values) {
            addCriterion("dtp not in", values, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpBetween(String value1, String value2) {
            addCriterion("dtp between", value1, value2, "dtp");
            return (Criteria) this;
        }

        public Criteria andDtpNotBetween(String value1, String value2) {
            addCriterion("dtp not between", value1, value2, "dtp");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeIsNull() {
            addCriterion("plat_store_type_code is null");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeIsNotNull() {
            addCriterion("plat_store_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeEqualTo(String value) {
            addCriterion("plat_store_type_code =", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeNotEqualTo(String value) {
            addCriterion("plat_store_type_code <>", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeGreaterThan(String value) {
            addCriterion("plat_store_type_code >", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("plat_store_type_code >=", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeLessThan(String value) {
            addCriterion("plat_store_type_code <", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("plat_store_type_code <=", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeLike(String value) {
            addCriterion("plat_store_type_code like", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeNotLike(String value) {
            addCriterion("plat_store_type_code not like", value, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeIn(List<String> values) {
            addCriterion("plat_store_type_code in", values, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeNotIn(List<String> values) {
            addCriterion("plat_store_type_code not in", values, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeBetween(String value1, String value2) {
            addCriterion("plat_store_type_code between", value1, value2, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andPlatStoreTypeCodeNotBetween(String value1, String value2) {
            addCriterion("plat_store_type_code not between", value1, value2, "platStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeIsNull() {
            addCriterion("store_type_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeIsNotNull() {
            addCriterion("store_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeEqualTo(String value) {
            addCriterion("store_type_code =", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeNotEqualTo(String value) {
            addCriterion("store_type_code <>", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeGreaterThan(String value) {
            addCriterion("store_type_code >", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_type_code >=", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeLessThan(String value) {
            addCriterion("store_type_code <", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("store_type_code <=", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeLike(String value) {
            addCriterion("store_type_code like", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeNotLike(String value) {
            addCriterion("store_type_code not like", value, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeIn(List<String> values) {
            addCriterion("store_type_code in", values, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeNotIn(List<String> values) {
            addCriterion("store_type_code not in", values, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeBetween(String value1, String value2) {
            addCriterion("store_type_code between", value1, value2, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andStoreTypeCodeNotBetween(String value1, String value2) {
            addCriterion("store_type_code not between", value1, value2, "storeTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeIsNull() {
            addCriterion("zs_store_type_code is null");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeIsNotNull() {
            addCriterion("zs_store_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeEqualTo(String value) {
            addCriterion("zs_store_type_code =", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeNotEqualTo(String value) {
            addCriterion("zs_store_type_code <>", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeGreaterThan(String value) {
            addCriterion("zs_store_type_code >", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("zs_store_type_code >=", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeLessThan(String value) {
            addCriterion("zs_store_type_code <", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("zs_store_type_code <=", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeLike(String value) {
            addCriterion("zs_store_type_code like", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeNotLike(String value) {
            addCriterion("zs_store_type_code not like", value, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeIn(List<String> values) {
            addCriterion("zs_store_type_code in", values, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeNotIn(List<String> values) {
            addCriterion("zs_store_type_code not in", values, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeBetween(String value1, String value2) {
            addCriterion("zs_store_type_code between", value1, value2, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andZsStoreTypeCodeNotBetween(String value1, String value2) {
            addCriterion("zs_store_type_code not between", value1, value2, "zsStoreTypeCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}