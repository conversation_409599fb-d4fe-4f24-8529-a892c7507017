package com.cowell.scib.entityDgms;

import java.io.Serializable;
import java.util.Date;

/**
 * online_hot_goods
 * <AUTHOR>
public class OnlineHotGoods implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商品目录来源
     */
    private String goodsSource;

    /**
     * 年份
     */
    private String goodsYear;

    /**
     * 时间维度
     */
    private String goodsTimeDimension;

    /**
     * 时间范围
     */
    private String goodsTimeFrame;

    /**
     * 省份
     */
    private String goodsProvince;

    /**
     * 城市
     */
    private String goodsCity;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 排名
     */
    private String goodsRank;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间（操作时间）
     */
    private Date gmtUpdate;

    /**
     * 逻辑删除
     */
    private Integer isDelete;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 创建人名字，对应登录返回的name字段
     */
    private String createBy;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 更新人名字，对应登录返回的name字段
     */
    private String updateBy;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGoodsSource() {
        return goodsSource;
    }

    public void setGoodsSource(String goodsSource) {
        this.goodsSource = goodsSource;
    }

    public String getGoodsYear() {
        return goodsYear;
    }

    public void setGoodsYear(String goodsYear) {
        this.goodsYear = goodsYear;
    }

    public String getGoodsTimeDimension() {
        return goodsTimeDimension;
    }

    public void setGoodsTimeDimension(String goodsTimeDimension) {
        this.goodsTimeDimension = goodsTimeDimension;
    }

    public String getGoodsTimeFrame() {
        return goodsTimeFrame;
    }

    public void setGoodsTimeFrame(String goodsTimeFrame) {
        this.goodsTimeFrame = goodsTimeFrame;
    }

    public String getGoodsProvince() {
        return goodsProvince;
    }

    public void setGoodsProvince(String goodsProvince) {
        this.goodsProvince = goodsProvince;
    }

    public String getGoodsCity() {
        return goodsCity;
    }

    public void setGoodsCity(String goodsCity) {
        this.goodsCity = goodsCity;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsRank() {
        return goodsRank;
    }

    public void setGoodsRank(String goodsRank) {
        this.goodsRank = goodsRank;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Long getCreateById() {
        return createById;
    }

    public void setCreateById(Long createById) {
        this.createById = createById;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateById() {
        return updateById;
    }

    public void setUpdateById(Long updateById) {
        this.updateById = updateById;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OnlineHotGoods other = (OnlineHotGoods) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGoodsSource() == null ? other.getGoodsSource() == null : this.getGoodsSource().equals(other.getGoodsSource()))
            && (this.getGoodsYear() == null ? other.getGoodsYear() == null : this.getGoodsYear().equals(other.getGoodsYear()))
            && (this.getGoodsTimeDimension() == null ? other.getGoodsTimeDimension() == null : this.getGoodsTimeDimension().equals(other.getGoodsTimeDimension()))
            && (this.getGoodsTimeFrame() == null ? other.getGoodsTimeFrame() == null : this.getGoodsTimeFrame().equals(other.getGoodsTimeFrame()))
            && (this.getGoodsProvince() == null ? other.getGoodsProvince() == null : this.getGoodsProvince().equals(other.getGoodsProvince()))
            && (this.getGoodsCity() == null ? other.getGoodsCity() == null : this.getGoodsCity().equals(other.getGoodsCity()))
            && (this.getBarCode() == null ? other.getBarCode() == null : this.getBarCode().equals(other.getBarCode()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getGoodsRank() == null ? other.getGoodsRank() == null : this.getGoodsRank().equals(other.getGoodsRank()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getCreateById() == null ? other.getCreateById() == null : this.getCreateById().equals(other.getCreateById()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getUpdateById() == null ? other.getUpdateById() == null : this.getUpdateById().equals(other.getUpdateById()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGoodsSource() == null) ? 0 : getGoodsSource().hashCode());
        result = prime * result + ((getGoodsYear() == null) ? 0 : getGoodsYear().hashCode());
        result = prime * result + ((getGoodsTimeDimension() == null) ? 0 : getGoodsTimeDimension().hashCode());
        result = prime * result + ((getGoodsTimeFrame() == null) ? 0 : getGoodsTimeFrame().hashCode());
        result = prime * result + ((getGoodsProvince() == null) ? 0 : getGoodsProvince().hashCode());
        result = prime * result + ((getGoodsCity() == null) ? 0 : getGoodsCity().hashCode());
        result = prime * result + ((getBarCode() == null) ? 0 : getBarCode().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getGoodsRank() == null) ? 0 : getGoodsRank().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getCreateById() == null) ? 0 : getCreateById().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getUpdateById() == null) ? 0 : getUpdateById().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", goodsSource=").append(goodsSource);
        sb.append(", goodsYear=").append(goodsYear);
        sb.append(", goodsTimeDimension=").append(goodsTimeDimension);
        sb.append(", goodsTimeFrame=").append(goodsTimeFrame);
        sb.append(", goodsProvince=").append(goodsProvince);
        sb.append(", goodsCity=").append(goodsCity);
        sb.append(", barCode=").append(barCode);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsRank=").append(goodsRank);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", createById=").append(createById);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateById=").append(updateById);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}