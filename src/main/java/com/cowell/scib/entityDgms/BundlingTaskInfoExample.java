package com.cowell.scib.entityDgms;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BundlingTaskInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public BundlingTaskInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskCodeIsNull() {
            addCriterion("task_code is null");
            return (Criteria) this;
        }

        public Criteria andTaskCodeIsNotNull() {
            addCriterion("task_code is not null");
            return (Criteria) this;
        }

        public Criteria andTaskCodeEqualTo(String value) {
            addCriterion("task_code =", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotEqualTo(String value) {
            addCriterion("task_code <>", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeGreaterThan(String value) {
            addCriterion("task_code >", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeGreaterThanOrEqualTo(String value) {
            addCriterion("task_code >=", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeLessThan(String value) {
            addCriterion("task_code <", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeLessThanOrEqualTo(String value) {
            addCriterion("task_code <=", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeLike(String value) {
            addCriterion("task_code like", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotLike(String value) {
            addCriterion("task_code not like", value, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeIn(List<String> values) {
            addCriterion("task_code in", values, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotIn(List<String> values) {
            addCriterion("task_code not in", values, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeBetween(String value1, String value2) {
            addCriterion("task_code between", value1, value2, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskCodeNotBetween(String value1, String value2) {
            addCriterion("task_code not between", value1, value2, "taskCode");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNull() {
            addCriterion("task_name is null");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNotNull() {
            addCriterion("task_name is not null");
            return (Criteria) this;
        }

        public Criteria andTaskNameEqualTo(String value) {
            addCriterion("task_name =", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotEqualTo(String value) {
            addCriterion("task_name <>", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThan(String value) {
            addCriterion("task_name >", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThanOrEqualTo(String value) {
            addCriterion("task_name >=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThan(String value) {
            addCriterion("task_name <", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThanOrEqualTo(String value) {
            addCriterion("task_name <=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLike(String value) {
            addCriterion("task_name like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotLike(String value) {
            addCriterion("task_name not like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameIn(List<String> values) {
            addCriterion("task_name in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotIn(List<String> values) {
            addCriterion("task_name not in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameBetween(String value1, String value2) {
            addCriterion("task_name between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotBetween(String value1, String value2) {
            addCriterion("task_name not between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(Byte value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(Byte value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(Byte value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(Byte value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(Byte value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<Byte> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<Byte> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(Byte value1, Byte value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andSelectIdIsNull() {
            addCriterion("select_id is null");
            return (Criteria) this;
        }

        public Criteria andSelectIdIsNotNull() {
            addCriterion("select_id is not null");
            return (Criteria) this;
        }

        public Criteria andSelectIdEqualTo(Long value) {
            addCriterion("select_id =", value, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdNotEqualTo(Long value) {
            addCriterion("select_id <>", value, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdGreaterThan(Long value) {
            addCriterion("select_id >", value, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("select_id >=", value, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdLessThan(Long value) {
            addCriterion("select_id <", value, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdLessThanOrEqualTo(Long value) {
            addCriterion("select_id <=", value, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdIn(List<Long> values) {
            addCriterion("select_id in", values, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdNotIn(List<Long> values) {
            addCriterion("select_id not in", values, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdBetween(Long value1, Long value2) {
            addCriterion("select_id between", value1, value2, "selectId");
            return (Criteria) this;
        }

        public Criteria andSelectIdNotBetween(Long value1, Long value2) {
            addCriterion("select_id not between", value1, value2, "selectId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Long value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Long value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Long value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Long value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Long> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Long> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Long value1, Long value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andMemoIsNull() {
            addCriterion("memo is null");
            return (Criteria) this;
        }

        public Criteria andMemoIsNotNull() {
            addCriterion("memo is not null");
            return (Criteria) this;
        }

        public Criteria andMemoEqualTo(String value) {
            addCriterion("memo =", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotEqualTo(String value) {
            addCriterion("memo <>", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThan(String value) {
            addCriterion("memo >", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThanOrEqualTo(String value) {
            addCriterion("memo >=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThan(String value) {
            addCriterion("memo <", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThanOrEqualTo(String value) {
            addCriterion("memo <=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLike(String value) {
            addCriterion("memo like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotLike(String value) {
            addCriterion("memo not like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoIn(List<String> values) {
            addCriterion("memo in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotIn(List<String> values) {
            addCriterion("memo not in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoBetween(String value1, String value2) {
            addCriterion("memo between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotBetween(String value1, String value2) {
            addCriterion("memo not between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andCommitByIsNull() {
            addCriterion("commit_by is null");
            return (Criteria) this;
        }

        public Criteria andCommitByIsNotNull() {
            addCriterion("commit_by is not null");
            return (Criteria) this;
        }

        public Criteria andCommitByEqualTo(Long value) {
            addCriterion("commit_by =", value, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByNotEqualTo(Long value) {
            addCriterion("commit_by <>", value, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByGreaterThan(Long value) {
            addCriterion("commit_by >", value, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByGreaterThanOrEqualTo(Long value) {
            addCriterion("commit_by >=", value, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByLessThan(Long value) {
            addCriterion("commit_by <", value, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByLessThanOrEqualTo(Long value) {
            addCriterion("commit_by <=", value, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByIn(List<Long> values) {
            addCriterion("commit_by in", values, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByNotIn(List<Long> values) {
            addCriterion("commit_by not in", values, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByBetween(Long value1, Long value2) {
            addCriterion("commit_by between", value1, value2, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitByNotBetween(Long value1, Long value2) {
            addCriterion("commit_by not between", value1, value2, "commitBy");
            return (Criteria) this;
        }

        public Criteria andCommitNameIsNull() {
            addCriterion("commit_name is null");
            return (Criteria) this;
        }

        public Criteria andCommitNameIsNotNull() {
            addCriterion("commit_name is not null");
            return (Criteria) this;
        }

        public Criteria andCommitNameEqualTo(String value) {
            addCriterion("commit_name =", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameNotEqualTo(String value) {
            addCriterion("commit_name <>", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameGreaterThan(String value) {
            addCriterion("commit_name >", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameGreaterThanOrEqualTo(String value) {
            addCriterion("commit_name >=", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameLessThan(String value) {
            addCriterion("commit_name <", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameLessThanOrEqualTo(String value) {
            addCriterion("commit_name <=", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameLike(String value) {
            addCriterion("commit_name like", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameNotLike(String value) {
            addCriterion("commit_name not like", value, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameIn(List<String> values) {
            addCriterion("commit_name in", values, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameNotIn(List<String> values) {
            addCriterion("commit_name not in", values, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameBetween(String value1, String value2) {
            addCriterion("commit_name between", value1, value2, "commitName");
            return (Criteria) this;
        }

        public Criteria andCommitNameNotBetween(String value1, String value2) {
            addCriterion("commit_name not between", value1, value2, "commitName");
            return (Criteria) this;
        }

        public Criteria andIssuedByIsNull() {
            addCriterion("issued_by is null");
            return (Criteria) this;
        }

        public Criteria andIssuedByIsNotNull() {
            addCriterion("issued_by is not null");
            return (Criteria) this;
        }

        public Criteria andIssuedByEqualTo(Long value) {
            addCriterion("issued_by =", value, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByNotEqualTo(Long value) {
            addCriterion("issued_by <>", value, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByGreaterThan(Long value) {
            addCriterion("issued_by >", value, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByGreaterThanOrEqualTo(Long value) {
            addCriterion("issued_by >=", value, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByLessThan(Long value) {
            addCriterion("issued_by <", value, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByLessThanOrEqualTo(Long value) {
            addCriterion("issued_by <=", value, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByIn(List<Long> values) {
            addCriterion("issued_by in", values, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByNotIn(List<Long> values) {
            addCriterion("issued_by not in", values, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByBetween(Long value1, Long value2) {
            addCriterion("issued_by between", value1, value2, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedByNotBetween(Long value1, Long value2) {
            addCriterion("issued_by not between", value1, value2, "issuedBy");
            return (Criteria) this;
        }

        public Criteria andIssuedNameIsNull() {
            addCriterion("issued_name is null");
            return (Criteria) this;
        }

        public Criteria andIssuedNameIsNotNull() {
            addCriterion("issued_name is not null");
            return (Criteria) this;
        }

        public Criteria andIssuedNameEqualTo(String value) {
            addCriterion("issued_name =", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameNotEqualTo(String value) {
            addCriterion("issued_name <>", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameGreaterThan(String value) {
            addCriterion("issued_name >", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameGreaterThanOrEqualTo(String value) {
            addCriterion("issued_name >=", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameLessThan(String value) {
            addCriterion("issued_name <", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameLessThanOrEqualTo(String value) {
            addCriterion("issued_name <=", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameLike(String value) {
            addCriterion("issued_name like", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameNotLike(String value) {
            addCriterion("issued_name not like", value, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameIn(List<String> values) {
            addCriterion("issued_name in", values, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameNotIn(List<String> values) {
            addCriterion("issued_name not in", values, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameBetween(String value1, String value2) {
            addCriterion("issued_name between", value1, value2, "issuedName");
            return (Criteria) this;
        }

        public Criteria andIssuedNameNotBetween(String value1, String value2) {
            addCriterion("issued_name not between", value1, value2, "issuedName");
            return (Criteria) this;
        }

        public Criteria andCancelByIsNull() {
            addCriterion("cancel_by is null");
            return (Criteria) this;
        }

        public Criteria andCancelByIsNotNull() {
            addCriterion("cancel_by is not null");
            return (Criteria) this;
        }

        public Criteria andCancelByEqualTo(Long value) {
            addCriterion("cancel_by =", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotEqualTo(Long value) {
            addCriterion("cancel_by <>", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByGreaterThan(Long value) {
            addCriterion("cancel_by >", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByGreaterThanOrEqualTo(Long value) {
            addCriterion("cancel_by >=", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLessThan(Long value) {
            addCriterion("cancel_by <", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLessThanOrEqualTo(Long value) {
            addCriterion("cancel_by <=", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByIn(List<Long> values) {
            addCriterion("cancel_by in", values, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotIn(List<Long> values) {
            addCriterion("cancel_by not in", values, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByBetween(Long value1, Long value2) {
            addCriterion("cancel_by between", value1, value2, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotBetween(Long value1, Long value2) {
            addCriterion("cancel_by not between", value1, value2, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelNameIsNull() {
            addCriterion("cancel_name is null");
            return (Criteria) this;
        }

        public Criteria andCancelNameIsNotNull() {
            addCriterion("cancel_name is not null");
            return (Criteria) this;
        }

        public Criteria andCancelNameEqualTo(String value) {
            addCriterion("cancel_name =", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameNotEqualTo(String value) {
            addCriterion("cancel_name <>", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameGreaterThan(String value) {
            addCriterion("cancel_name >", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameGreaterThanOrEqualTo(String value) {
            addCriterion("cancel_name >=", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameLessThan(String value) {
            addCriterion("cancel_name <", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameLessThanOrEqualTo(String value) {
            addCriterion("cancel_name <=", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameLike(String value) {
            addCriterion("cancel_name like", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameNotLike(String value) {
            addCriterion("cancel_name not like", value, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameIn(List<String> values) {
            addCriterion("cancel_name in", values, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameNotIn(List<String> values) {
            addCriterion("cancel_name not in", values, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameBetween(String value1, String value2) {
            addCriterion("cancel_name between", value1, value2, "cancelName");
            return (Criteria) this;
        }

        public Criteria andCancelNameNotBetween(String value1, String value2) {
            addCriterion("cancel_name not between", value1, value2, "cancelName");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(Byte value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(Byte value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(Byte value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(Byte value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(Byte value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<Byte> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<Byte> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(Byte value1, Byte value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andGmtCommitIsNull() {
            addCriterion("gmt_commit is null");
            return (Criteria) this;
        }

        public Criteria andGmtCommitIsNotNull() {
            addCriterion("gmt_commit is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCommitEqualTo(Date value) {
            addCriterion("gmt_commit =", value, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitNotEqualTo(Date value) {
            addCriterion("gmt_commit <>", value, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitGreaterThan(Date value) {
            addCriterion("gmt_commit >", value, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_commit >=", value, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitLessThan(Date value) {
            addCriterion("gmt_commit <", value, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitLessThanOrEqualTo(Date value) {
            addCriterion("gmt_commit <=", value, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitIn(List<Date> values) {
            addCriterion("gmt_commit in", values, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitNotIn(List<Date> values) {
            addCriterion("gmt_commit not in", values, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitBetween(Date value1, Date value2) {
            addCriterion("gmt_commit between", value1, value2, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCommitNotBetween(Date value1, Date value2) {
            addCriterion("gmt_commit not between", value1, value2, "gmtCommit");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedIsNull() {
            addCriterion("gmt_calculated is null");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedIsNotNull() {
            addCriterion("gmt_calculated is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedEqualTo(Date value) {
            addCriterion("gmt_calculated =", value, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedNotEqualTo(Date value) {
            addCriterion("gmt_calculated <>", value, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedGreaterThan(Date value) {
            addCriterion("gmt_calculated >", value, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_calculated >=", value, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedLessThan(Date value) {
            addCriterion("gmt_calculated <", value, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_calculated <=", value, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedIn(List<Date> values) {
            addCriterion("gmt_calculated in", values, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedNotIn(List<Date> values) {
            addCriterion("gmt_calculated not in", values, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedBetween(Date value1, Date value2) {
            addCriterion("gmt_calculated between", value1, value2, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtCalculatedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_calculated not between", value1, value2, "gmtCalculated");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedIsNull() {
            addCriterion("gmt_issued is null");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedIsNotNull() {
            addCriterion("gmt_issued is not null");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedEqualTo(Date value) {
            addCriterion("gmt_issued =", value, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedNotEqualTo(Date value) {
            addCriterion("gmt_issued <>", value, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedGreaterThan(Date value) {
            addCriterion("gmt_issued >", value, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_issued >=", value, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedLessThan(Date value) {
            addCriterion("gmt_issued <", value, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_issued <=", value, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedIn(List<Date> values) {
            addCriterion("gmt_issued in", values, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedNotIn(List<Date> values) {
            addCriterion("gmt_issued not in", values, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedBetween(Date value1, Date value2) {
            addCriterion("gmt_issued between", value1, value2, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtIssuedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_issued not between", value1, value2, "gmtIssued");
            return (Criteria) this;
        }

        public Criteria andGmtCancelIsNull() {
            addCriterion("gmt_cancel is null");
            return (Criteria) this;
        }

        public Criteria andGmtCancelIsNotNull() {
            addCriterion("gmt_cancel is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCancelEqualTo(Date value) {
            addCriterion("gmt_cancel =", value, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelNotEqualTo(Date value) {
            addCriterion("gmt_cancel <>", value, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelGreaterThan(Date value) {
            addCriterion("gmt_cancel >", value, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_cancel >=", value, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelLessThan(Date value) {
            addCriterion("gmt_cancel <", value, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelLessThanOrEqualTo(Date value) {
            addCriterion("gmt_cancel <=", value, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelIn(List<Date> values) {
            addCriterion("gmt_cancel in", values, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelNotIn(List<Date> values) {
            addCriterion("gmt_cancel not in", values, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelBetween(Date value1, Date value2) {
            addCriterion("gmt_cancel between", value1, value2, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andGmtCancelNotBetween(Date value1, Date value2) {
            addCriterion("gmt_cancel not between", value1, value2, "gmtCancel");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}