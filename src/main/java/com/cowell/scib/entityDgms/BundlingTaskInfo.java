package com.cowell.scib.entityDgms;

import com.cowell.scib.enums.BundlTaskTypeEnum;

import java.io.Serializable;
import java.util.*;

/**
 * bundling_task_info
 * <AUTHOR>
public class BundlingTaskInfo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 组货任务类型 1.全必备层级组货 2.企业必备组货 3.店型必备组货 4 O2O重点门店组货
     */
    private Byte taskType;

    /**
     * 选择器ID
     */
    private Long selectId;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 备注
     */
    private String memo;

    /**
     * 提交人ID
     */
    private Long commitBy;

    /**
     * 提交人
     */
    private String commitName;

    /**
     * 下发人ID
     */
    private Long issuedBy;

    /**
     * 下发人
     */
    private String issuedName;

    /**
     * 作废人ID
     */
    private Long cancelBy;

    /**
     * 作废人
     */
    private String cancelName;

    /**
     * 0 创建中  1 暂存 2 计算中 3 计算完成 4已作废 5已更新
     */
    private Byte taskStatus;

    /**
     * 提交时间
     */
    private Date gmtCommit;

    /**
     * 计算完成时间
     */
    private Date gmtCalculated;

    /**
     * 任务下发时间
     */
    private Date gmtIssued;

    /**
     * 任务作废时间
     */
    private Date gmtCancel;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Byte getTaskType() {
        return taskType;
    }

    public void setTaskType(Byte taskType) {
        this.taskType = taskType;
    }

    public Long getSelectId() {
        return selectId;
    }

    public void setSelectId(Long selectId) {
        this.selectId = selectId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Long getCommitBy() {
        return commitBy;
    }

    public void setCommitBy(Long commitBy) {
        this.commitBy = commitBy;
    }

    public String getCommitName() {
        return commitName;
    }

    public void setCommitName(String commitName) {
        this.commitName = commitName;
    }

    public Long getIssuedBy() {
        return issuedBy;
    }

    public void setIssuedBy(Long issuedBy) {
        this.issuedBy = issuedBy;
    }

    public String getIssuedName() {
        return issuedName;
    }

    public void setIssuedName(String issuedName) {
        this.issuedName = issuedName;
    }

    public Long getCancelBy() {
        return cancelBy;
    }

    public void setCancelBy(Long cancelBy) {
        this.cancelBy = cancelBy;
    }

    public String getCancelName() {
        return cancelName;
    }

    public void setCancelName(String cancelName) {
        this.cancelName = cancelName;
    }

    public Byte getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Byte taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Date getGmtCommit() {
        return gmtCommit;
    }

    public void setGmtCommit(Date gmtCommit) {
        this.gmtCommit = gmtCommit;
    }

    public Date getGmtCalculated() {
        return gmtCalculated;
    }

    public void setGmtCalculated(Date gmtCalculated) {
        this.gmtCalculated = gmtCalculated;
    }

    public Date getGmtIssued() {
        return gmtIssued;
    }

    public void setGmtIssued(Date gmtIssued) {
        this.gmtIssued = gmtIssued;
    }

    public Date getGmtCancel() {
        return gmtCancel;
    }

    public void setGmtCancel(Date gmtCancel) {
        this.gmtCancel = gmtCancel;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }


    public static List<Byte> getDefaultTaskTypeListByTaskType(Byte taskType) {
        if(Objects.isNull(taskType)){
            return  Arrays.asList(BundlTaskTypeEnum.PLATE_BUNDL.getCode(), BundlTaskTypeEnum.BUSINESS_BUNDL.getCode(),BundlTaskTypeEnum.STORE_BUNDL.getCode(), BundlTaskTypeEnum.KEY_STORE_O2O_BUNDL.getCode());
        }else {
            return Collections.singletonList(taskType);
        }
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BundlingTaskInfo other = (BundlingTaskInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskCode() == null ? other.getTaskCode() == null : this.getTaskCode().equals(other.getTaskCode()))
            && (this.getTaskName() == null ? other.getTaskName() == null : this.getTaskName().equals(other.getTaskName()))
            && (this.getTaskType() == null ? other.getTaskType() == null : this.getTaskType().equals(other.getTaskType()))
            && (this.getSelectId() == null ? other.getSelectId() == null : this.getSelectId().equals(other.getSelectId()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getMemo() == null ? other.getMemo() == null : this.getMemo().equals(other.getMemo()))
            && (this.getCommitBy() == null ? other.getCommitBy() == null : this.getCommitBy().equals(other.getCommitBy()))
            && (this.getCommitName() == null ? other.getCommitName() == null : this.getCommitName().equals(other.getCommitName()))
            && (this.getIssuedBy() == null ? other.getIssuedBy() == null : this.getIssuedBy().equals(other.getIssuedBy()))
            && (this.getIssuedName() == null ? other.getIssuedName() == null : this.getIssuedName().equals(other.getIssuedName()))
            && (this.getCancelBy() == null ? other.getCancelBy() == null : this.getCancelBy().equals(other.getCancelBy()))
            && (this.getCancelName() == null ? other.getCancelName() == null : this.getCancelName().equals(other.getCancelName()))
            && (this.getTaskStatus() == null ? other.getTaskStatus() == null : this.getTaskStatus().equals(other.getTaskStatus()))
            && (this.getGmtCommit() == null ? other.getGmtCommit() == null : this.getGmtCommit().equals(other.getGmtCommit()))
            && (this.getGmtCalculated() == null ? other.getGmtCalculated() == null : this.getGmtCalculated().equals(other.getGmtCalculated()))
            && (this.getGmtIssued() == null ? other.getGmtIssued() == null : this.getGmtIssued().equals(other.getGmtIssued()))
            && (this.getGmtCancel() == null ? other.getGmtCancel() == null : this.getGmtCancel().equals(other.getGmtCancel()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskCode() == null) ? 0 : getTaskCode().hashCode());
        result = prime * result + ((getTaskName() == null) ? 0 : getTaskName().hashCode());
        result = prime * result + ((getTaskType() == null) ? 0 : getTaskType().hashCode());
        result = prime * result + ((getSelectId() == null) ? 0 : getSelectId().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getMemo() == null) ? 0 : getMemo().hashCode());
        result = prime * result + ((getCommitBy() == null) ? 0 : getCommitBy().hashCode());
        result = prime * result + ((getCommitName() == null) ? 0 : getCommitName().hashCode());
        result = prime * result + ((getIssuedBy() == null) ? 0 : getIssuedBy().hashCode());
        result = prime * result + ((getIssuedName() == null) ? 0 : getIssuedName().hashCode());
        result = prime * result + ((getCancelBy() == null) ? 0 : getCancelBy().hashCode());
        result = prime * result + ((getCancelName() == null) ? 0 : getCancelName().hashCode());
        result = prime * result + ((getTaskStatus() == null) ? 0 : getTaskStatus().hashCode());
        result = prime * result + ((getGmtCommit() == null) ? 0 : getGmtCommit().hashCode());
        result = prime * result + ((getGmtCalculated() == null) ? 0 : getGmtCalculated().hashCode());
        result = prime * result + ((getGmtIssued() == null) ? 0 : getGmtIssued().hashCode());
        result = prime * result + ((getGmtCancel() == null) ? 0 : getGmtCancel().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskCode=").append(taskCode);
        sb.append(", taskName=").append(taskName);
        sb.append(", taskType=").append(taskType);
        sb.append(", selectId=").append(selectId);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", memo=").append(memo);
        sb.append(", commitBy=").append(commitBy);
        sb.append(", commitName=").append(commitName);
        sb.append(", issuedBy=").append(issuedBy);
        sb.append(", issuedName=").append(issuedName);
        sb.append(", cancelBy=").append(cancelBy);
        sb.append(", cancelName=").append(cancelName);
        sb.append(", taskStatus=").append(taskStatus);
        sb.append(", gmtCommit=").append(gmtCommit);
        sb.append(", gmtCalculated=").append(gmtCalculated);
        sb.append(", gmtIssued=").append(gmtIssued);
        sb.append(", gmtCancel=").append(gmtCancel);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}