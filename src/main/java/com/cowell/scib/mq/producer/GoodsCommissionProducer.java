package com.cowell.scib.mq.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Component
public class GoodsCommissionProducer  {

    private static final Logger logger = LoggerFactory.getLogger(GoodsCommissionProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String nameServerAdr;

    @Value("${apache.rocketmq.scib.goods-commission-calculate.producer-topic}")
    private String topic;

    @Value("${apache.rocketmq.scib.goods-commission-calculate.producer-group}")
    private String group;

    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;


    /**
     * 初始化
     */
    @PostConstruct
    public DefaultMQProducer start() {
        try {
            producer.setProducerGroup(group);
            producer.setNamesrvAddr(nameServerAdr);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[GoodsCommissionProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
        return producer;
    }

    public SendResult send(String data) {
        logger.info("GoodsCommissionProducer: 生产者发送消息 :{}", data);
        SendResult result;
        try {
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message mqMsg = new Message(topic,"",String.valueOf(System.currentTimeMillis()) , messageBody);

            logger.info("GoodsCommissionProducer| 发送消息为：{}",JSONObject.toJSONString(mqMsg));
            result = producer.send(mqMsg);
            if (SendStatus.SEND_OK.equals(result.getSendStatus())) {
                logger.info("GoodsCommissionProducer 发送消息成功,result:{}", result);
            } else {
                logger.info("GoodsCommissionProducer 发送消息失败,result:{}", result);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BusinessErrorException(e.getMessage());

        }
        return result;
    }


    @PreDestroy
    public void stop() {
        producer.shutdown();
        logger.info("MQ：关闭生产者[GoodsCommissionProducer]");
    }
}
