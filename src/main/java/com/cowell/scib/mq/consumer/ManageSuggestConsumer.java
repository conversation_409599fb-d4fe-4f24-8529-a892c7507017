package com.cowell.scib.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.mq.ConsumerAdapter;
import com.cowell.scib.service.ManageContentsService;
import com.cowell.scib.service.dto.manageContents.ManageSuggestMqDTO;
import com.cowell.scib.utils.TracerBean;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Objects;

/**
 * 经营目录管理推送按门店处理
 */
@Component
public class ManageSuggestConsumer implements MessageListenerConcurrently, ConsumerAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManageSuggestConsumer.class);
    @Value("${apache.rocketmq.namesrvAddr}")
    private String nameServerAdr;

    @Value("${apache.rocketmq.scib.manage-suggest.topic}")
    private String topic;

    @Value("${apache.rocketmq.scib.manage-suggest.group}")
    private String group;

    @Autowired
    private ManageContentsService manageContentsService;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();

    @Autowired
    private TracerBean tracerBean;
    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            LOGGER.info("MQ：启动消费者[ManageSuggestConsumer] namesrvAddr:"+ nameServerAdr);
            consumer.setNamesrvAddr(nameServerAdr);
            consumer.setConsumerGroup(group);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(this);
            consumer.setPullThresholdForQueue(20);
            consumer.setPullThresholdForTopic(20);
            consumer.setConsumeThreadMin(2);
            consumer.setConsumeThreadMax(2);
            // 启动消费端
            consumer.start();
        } catch (MQClientException e) {
            LOGGER.error("MQ：启动消费者[ManageSuggestConsumer] 失败，异常信息 ：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        Span span = tracerBean.startSpan();
        try {
            LOGGER.info("消费者 [ManageSuggestConsumer]开始消费");
            for (MessageExt messageExt : msgs) {
                byte[] body = messageExt.getBody();
                String messageBody = new String(body, RemotingHelper.DEFAULT_CHARSET);
                ManageSuggestMqDTO  suggestMqDTO = JSON.parseObject(messageBody, ManageSuggestMqDTO.class);
                LOGGER.info("消费者 [ManageSuggestConsumer] 传过来的信息: {}", JSONObject.toJSONString(suggestMqDTO));
                if (Objects.nonNull(suggestMqDTO)){
                    boolean handled = manageContentsService.handleSuggest(suggestMqDTO);
                    if (!handled){
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                }
            }
        }  catch (Exception e) {
            LOGGER.warn("ManageSuggestConsumer消费经营目录数据失败: ", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }finally {
            tracerBean.close(span);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    @PreDestroy
    public void stop() {
        consumer.shutdown();
        LOGGER.warn("MQ：stop ManageSuggestConsumer consumer!");
    }
}
