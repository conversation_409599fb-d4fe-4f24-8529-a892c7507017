/*
package com.cowell.scib.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.mq.ConsumerAdapter;
import com.cowell.scib.service.MinDisplayService;
import com.cowell.scib.service.dto.SeasonalGoodsPushDTO;
import com.cowell.scib.utils.TracerBean;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;

*/
/**
 * 接收季节品推送的数据
 *//*

@Component
public class SeasonalGoodsPushConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(SeasonalGoodsPushConsumer.class);
    //@Value("${apache.rocketmq.namesrvAddr2}")
    private String nameServerAdr;

    //@Value("${apache.rocketmq.scib.season-goods.topic}")
    private String topic;

    //@Value("${apache.rocketmq.scib.season-goods.group}")
    private String group;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();

    @Autowired
    private TracerBean tracerBean;

    @Autowired
    private MinDisplayService minDisplayService;

    @Override
    public void start() {
        try {
            LOGGER.info("MQ：启动消费者[SeasonalGoodsPushConsumer] namesrvAddr:"+ nameServerAdr);
            consumer.setNamesrvAddr(nameServerAdr);
            consumer.setConsumerGroup(group);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(this);
            consumer.setPullThresholdForQueue(20);
            consumer.setPullThresholdForTopic(20);
            // 启动消费端
            consumer.start();
            LOGGER.info("MQ：启动消费者[SeasonalGoodsPushConsumer] consumer={} 成功 ", JSONObject.toJSONString(consumer));
        } catch (MQClientException e) {
            LOGGER.error("MQ：启动消费者[SeasonalGoodsPushConsumer] 失败，异常信息 ：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        Span span = tracerBean.startSpan();
        try {
            LOGGER.info("消费者 [SeasonalGoodsPushConsumer]开始消费");
            for (MessageExt messageExt : msgs) {
                byte[] body = messageExt.getBody();
                String messageBody = new String(body, RemotingHelper.DEFAULT_CHARSET);
                SeasonalGoodsPushDTO seasonalGoodsDTO = JSON.parseObject(messageBody, SeasonalGoodsPushDTO.class);
                LOGGER.info("消费者 [SeasonalGoodsPushConsumer] 传过来的信息: {}", JSON.toJSONString(seasonalGoodsDTO));
                //minDisplayService.getMinDisplayFromMdm(seasonalGoodsDTO);
            }
        }  catch (Exception e) {
            LOGGER.warn("SeasonalGoodsPushConsumer: ", e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }finally {
            tracerBean.close(span);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


    @PreDestroy
    public void stop() {
        consumer.shutdown();
        LOGGER.warn("MQ：stop SeasonalGoodsPushConsumer consumer!");
    }
}
*/
