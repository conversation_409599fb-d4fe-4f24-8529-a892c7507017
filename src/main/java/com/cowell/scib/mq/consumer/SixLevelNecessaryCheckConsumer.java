package com.cowell.scib.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.MdmTask;
import com.cowell.scib.entityDgms.MdmTaskDetailExample;
import com.cowell.scib.entityDgms.MdmTaskExample;
import com.cowell.scib.enums.NecessaryTagEnum;
import com.cowell.scib.enums.TaskStatusChangeEnum;
import com.cowell.scib.mapperDgms.MdmTaskDetailMapper;
import com.cowell.scib.mapperDgms.MdmTaskMapper;
import com.cowell.scib.mq.ConsumerAdapter;
import com.cowell.scib.service.BundlTaskWriteService;
import com.cowell.scib.service.dto.NecessaryGoodsDTO;
import com.cowell.scib.service.dto.PushStoreMdmDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.handle.*;
import com.cowell.scib.utils.TracerBean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class SixLevelNecessaryCheckConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(SixLevelNecessaryCheckConsumer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String nameServerAdr;

    @Value("${apache.rocketmq.scib.six-level-necessary-check.topic}")
    private String topic;

    @Value("${apache.rocketmq.scib.six-level-necessary-check.group}")
    private String group;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();

    @Autowired
    private TracerBean tracerBean;

    public static final String STORE_TYPE_GOODS_DEL_IMPORT ="SCIB_STORE_TYPE_GOODS_DEL_IMPORT:%s" ;

    @Autowired
    private NecessaryPlatformGoodsHandler necessaryPlatformGoodsHandler;
    @Autowired
    private NecessaryCompanyGoodsHandler necessaryCompanyGoodsHandler;
    @Autowired
    private NecessaryStoreTypeGoodsHandler necessaryStoreTypeGoodsHandler;
    @Autowired
    private NecessaryChooseStoreTypeGoodsHandler necessaryChooseStoreTypeGoodsHandler;
    @Autowired
    private NecessarySingleStoreGoodsHandler necessarySingleStoreGoodsHandler;
    @Autowired
    private StoreGoodsHandler storeGoodsHandler;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private BundlTaskWriteService bundlTaskWriteService;

    @Autowired
    private MdmTaskMapper mdmTaskMapper;

    @Autowired
    private MdmTaskDetailMapper mdmTaskDetailMapper;
    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            LOGGER.info("MQ：启动消费者[SixLevelNecessaryCheckConsumer] namesrvAddr:"+ nameServerAdr);
            consumer.setNamesrvAddr(nameServerAdr);
            consumer.setConsumerGroup(group);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(this);
            consumer.setPullThresholdForQueue(20);
            consumer.setPullThresholdForTopic(20);
            consumer.setConsumeThreadMax(15);
            consumer.setConsumeThreadMin(1);
            // 启动消费端
            consumer.start();
        } catch (MQClientException e) {
            LOGGER.error("MQ：启动消费者[SixLevelNecessaryCheckConsumer] 失败，异常信息 ：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        Span span = tracerBean.startSpan();
        MessageExt messageExt = msgs.get(0);
        try {
            LOGGER.info("消费者 [SixLevelNecessaryCheckConsumer]开始消费");
            byte[] body = messageExt.getBody();
            LOGGER.info("消费次数:{},messageId：{}",messageExt.getReconsumeTimes(),messageExt.getMsgId());
            String messageBody = new String(body, RemotingHelper.DEFAULT_CHARSET);
            NecessaryGoodsDTO necessaryGoodsDTO = JSON.parseObject(messageBody, NecessaryGoodsDTO.class);

            if (necessaryGoodsDTO.getNecessaryTag().equals(String.valueOf(NecessaryTagEnum.PLATFORM_NECESSARY.getCode()))){
                necessaryPlatformGoodsHandler.check(necessaryGoodsDTO);
            }else if (necessaryGoodsDTO.getNecessaryTag().equals(String.valueOf(NecessaryTagEnum.COMPANY_NECESSARY.getCode()))){
                necessaryCompanyGoodsHandler.check(necessaryGoodsDTO);
            }else if (necessaryGoodsDTO.getNecessaryTag().equals(String.valueOf(NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode()))){
                necessaryStoreTypeGoodsHandler.check(necessaryGoodsDTO);
            }else if (necessaryGoodsDTO.getNecessaryTag().equals(String.valueOf(NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode()))){
                necessaryChooseStoreTypeGoodsHandler.check(necessaryGoodsDTO);
            }else if (necessaryGoodsDTO.getNecessaryTag().equals(String.valueOf(NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode()))){
                necessarySingleStoreGoodsHandler.check(necessaryGoodsDTO);
            }else if (necessaryGoodsDTO.getNecessaryTag().equals(String.valueOf(NecessaryTagEnum.NONE_NECESSARY.getCode()))){
                if (CollectionUtils.isNotEmpty(necessaryGoodsDTO.getPushStoreMdmDTOList())){
                    PushStoreMdmDTO pushStoreMdmDTO = necessaryGoodsDTO.getPushStoreMdmDTOList().get(0);
                    String redisKey = RedisConstant.NECESSARY_FINISHED_ADD_STORE_GOODS_CACHE_KEY + pushStoreMdmDTO.getTaskId();
                    RBucket<String> sendFinished = redissonClient.getBucket(redisKey);
                    String finishedKey = RedisConstant.NECESSARY_ADD_STORE_GOODS_CACHE_KEY +pushStoreMdmDTO.getTaskId();
                    try{
                        storeGoodsHandler.check(necessaryGoodsDTO);
                        if (CollectionUtils.isNotEmpty(necessaryGoodsDTO.getPushStoreMdmDTOList())){
                            long decrement = -1L * necessaryGoodsDTO.getPushStoreMdmDTOList().size();
                            RAtomicLong atomicLong = redissonClient.getAtomicLong(finishedKey);
                            long last = atomicLong.addAndGet(decrement);
                            LOGGER.info("一店一目redis 本次减:{} 剩余值：{}",decrement,last );
                            if (last<=0 && Constants.NECESSARY_DATA_VERSION_ONE.equals(sendFinished.get())){
                                LOGGER.info("一店一目 最后更新 sendFinished={}",sendFinished.get() );
                                MdmTaskExample mdmTaskExample = new MdmTaskExample();
                                mdmTaskExample.createCriteria().andBundlingTaskIdEqualTo(pushStoreMdmDTO.getTaskId());
                                List<MdmTask> mdmTasks = mdmTaskMapper.selectByExample(mdmTaskExample);
                                if (CollectionUtils.isNotEmpty(mdmTasks)){
                                    MdmTask mdmTask = mdmTasks.get(0);
                                    MdmTaskDetailExample mdmTaskDetailExample = new MdmTaskDetailExample();
                                    mdmTaskDetailExample.createCriteria().andTaskIdEqualTo(mdmTask.getId());
                                    Long count = mdmTaskDetailMapper.countByExample(mdmTaskDetailExample);
                                    mdmTask.setDetailCount(count.intValue());
                                    mdmTaskMapper.updateByPrimaryKeySelective(mdmTask);
                                }
                                String key = String.format(STORE_TYPE_GOODS_DEL_IMPORT, necessaryGoodsDTO.getPushStoreMdmDTOList().get(0).getTaskId());
                                RBucket<ImportResult> bucket1 = redissonClient.getBucket(key);
                                ImportResult importResult = ImportResult.buildDefaultImportResult(key);
                                bucket1.set(importResult, 12, TimeUnit.HOURS);
                                TokenUserDTO tokenUserDTO = new TokenUserDTO();
                                tokenUserDTO.setUserId(pushStoreMdmDTO.getUserId());
                                tokenUserDTO.setUserName(pushStoreMdmDTO.getUserName());
                                bundlTaskWriteService.unifyUpdateStasktatus(pushStoreMdmDTO.getTaskId(), TaskStatusChangeEnum.UPDATED,tokenUserDTO);
                                atomicLong.delete();
                                sendFinished.delete();
                            }
                            LOGGER.info("一店一目 发送状态 sendFinished={} ",sendFinished.get());
                        }
                    }catch (Exception e){
                        LOGGER.warn("处理一店一目数据报错: ", e);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                }
            }else{
                LOGGER.warn("处理一店一目数据未知的 NecessaryTag={}  messageExt.getMsgId ={}", necessaryGoodsDTO.getNecessaryTag(), messageExt.getMsgId());
            }
        }  catch (Exception e) {
            LOGGER.warn("SixLevelNecessaryCheckConsumer获取6级必备数据异常: ", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }finally {
            tracerBean.close(span);
        }
        LOGGER.info("消费完成 messageId：{}",messageExt.getMsgId());
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    @PreDestroy
    public void stop() {
        consumer.shutdown();
        LOGGER.warn("MQ：stop SixLevelNecessaryCheckConsumer consumer!");
    }
}
