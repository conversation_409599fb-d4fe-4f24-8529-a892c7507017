package com.cowell.scib.cache;

import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class CacheVar {

    // 带店型的缓存 key sapcode
    public static final Map<String, MdmStoreExDTO> storeExMap = new ConcurrentHashMap();

    /**
     * 平台缓存map key是platformOrgId
     */
    public static final Map<Long, OrgInfoBaseCache> platformCacheMap = Maps.newConcurrentMap();
    /**
     * 连锁缓存map key是businessSapCode
     */
    public static final Map<String, OrgInfoBaseCache> businessCacheMap = Maps.newConcurrentMap();

    /**
     * 门店缓存map key是storeSapCode
     */
    public static final Map<String, OrgInfoBaseCache> storeCacheMap = Maps.newConcurrentMap();

    /**
     * 公司org_id和sapCode 映射关系   根据 orgId 查询 sapCode  可在查OrgInfo
     */
    public static final Map<Long, String> businessOrgIdAndSapCodeMapping = Maps.newConcurrentMap();
    /**
     * 公司outid(businessId) 和sapCode 映射关系  根据businessId 查询 sapCode  可在查OrgInfo
     */
    public static final Map<Long, String> businessIdAndSapCodeMapping = Maps.newConcurrentMap();

    /**
     * 门店org_id和sapCode 映射关系   根据 orgId 查询 sapCode  可在查OrgInfo
     */
    public static final Map<Long, String> storeOrgIdAndSapCodeMapping = Maps.newConcurrentMap();
    /**
     * 门店outid(storeId) 和sapCode 映射关系  根据storeId  查询 sapCode  可在查OrgInfo
     */
    public static final Map<Long, String> storeIdAndSapCodeMapping = Maps.newConcurrentMap();

    // store里的省市缓存 key 省
    public static final Map<String, Set<String>> provinceMap = new ConcurrentHashMap();
    // store里的市区县缓存 key 市
    public static final Map<String, Set<String>> cityMap = new ConcurrentHashMap();

    /**
     * 根据storeSapCode 获取门店全部信息 包含店型信息
     * @param sapCode
     * @return
     */
    public static Optional<MdmStoreExDTO> getStoreExtInfoBySapCode(String sapCode) {
        return  sapCode == null ? Optional.empty() : Optional.ofNullable(CacheVar.storeExMap.get(sapCode));
    }

    /**
     * 根据storeId 获取门店全部信息 包含店型信息
     * @param storeId
     * @return
     */
    public static Optional<MdmStoreExDTO> getStoreExtInfoByStoreId(Long storeId) {
        Optional<OrgInfoBaseCache> store = getStoreByStoreId(storeId);
        return store.map(orgInfoBaseCache -> CacheVar.storeExMap.get(orgInfoBaseCache.getSapCode()));
    }

    /**
     * 根据storeId 获取门店全部信息 包含店型信息
     * @param storeOrgId
     * @return
     */
    public static Optional<MdmStoreExDTO> getStoreExtInfoByStoreOrgId(Long storeOrgId) {
        Optional<OrgInfoBaseCache> store = getStoreByOrgId(storeOrgId);
        return store.map(orgInfoBaseCache -> CacheVar.storeExMap.get(orgInfoBaseCache.getSapCode()));
    }


    /**
     * 根据storeId 获取门店基本信息 包含连锁和平台简单信息
     * @param storeId
     * @return
     */
    public static Optional<OrgInfoBaseCache> getStoreByStoreId(Long storeId) {
        String sapCode = storeId == null? null: storeIdAndSapCodeMapping.get(storeId);
        return  getStoreBySapCode(sapCode);
    }
    /**
     * 根据storeIdList  批量获取门店基本信息 包含连锁和平台简单信息
     * @param storeIdList
     * @return
     */
    public static Optional<List<OrgInfoBaseCache>> getStoreByStoreIdList(List<Long> storeIdList) {
        List<OrgInfoBaseCache> collect = storeIdList.stream()
                .map(key -> getStoreByStoreId(key).orElse(null)).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Optional.of(collect);
    }
    /**
     * 根据storeOrgId 获取门店基本信息 包含连锁和平台简单信息
     * @param storeOrgId
     * @return
     */
    public static Optional<OrgInfoBaseCache> getStoreByOrgId(Long storeOrgId) {
        String sapCode = storeOrgId == null? null: storeOrgIdAndSapCodeMapping.get(storeOrgId);
        return  getStoreBySapCode(sapCode);
    }
    /**
     * 根据storeOrgIdList 获取门店基本信息 包含连锁和平台简单信息
     * @param storeOrgIdList
     * @return
     */
    public static Optional<List<OrgInfoBaseCache>> getStoreByStoreOrgIdList(List<Long> storeOrgIdList) {
        List<OrgInfoBaseCache> collect = storeOrgIdList.stream()
                .map(key -> getStoreByOrgId(key).orElse(null)).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Optional.ofNullable(collect);
    }
    /**
     * 根据storeSapCode 获取门店基本信息 包含连锁和平台简单信息
     * @param sapCode
     * @return
     */
    public static Optional<OrgInfoBaseCache> getStoreBySapCode(String sapCode) {
        return  sapCode == null ? Optional.empty() : Optional.ofNullable(CacheVar.storeCacheMap.get(sapCode));
    }

    /**
     * 根据storeSapCode 获取门店基本信息 包含连锁和平台简单信息
     * @param storeCodes
     * @return
     */
    public static Optional<List<OrgInfoBaseCache>> getStoreByStoreCodeList(List<String> storeCodes) {
        List<OrgInfoBaseCache> collect = storeCodes.stream()
                .map(key -> getStoreBySapCode(key).orElse(null)).filter(v -> Objects.nonNull(v))
                .collect(Collectors.toList());
        return Optional.ofNullable(collect);
    }

    /**
     * 根据businessId 获取连锁基本信息 包含连锁和平台简单信息
     * @param businessId
     * @return
     */
    public static Optional<OrgInfoBaseCache> getBusinessOrgByBusinessId(Long businessId) {
        String sapCode = businessId == null? null: businessIdAndSapCodeMapping.get(businessId);
        return  getBusinessBySapCode(sapCode);
    }
    /**
     * 根据businessIdList  批量获取连锁基本信息 包含连锁和平台简单信息
     * @param businessIdList
     * @return
     */
    public static Optional<List<OrgInfoBaseCache>> getBusinessOrgByBusinessIdList(List<Long> businessIdList) {
        List<OrgInfoBaseCache> collect = businessIdList.stream()
                .map(key -> getBusinessOrgByBusinessId(key).orElse(null)).filter(v -> Objects.nonNull(v))
                .collect(Collectors.toList());
        return Optional.ofNullable(collect);
    }
    /**
     * 根据businessOrgId 获取连锁基本信息 包含连锁和平台简单信息
     * @param businessOrgId
     * @return
     */
    public static Optional<OrgInfoBaseCache> getBusinessByOrgId(Long businessOrgId) {
        String sapCode = businessOrgId == null? null: businessOrgIdAndSapCodeMapping.get(businessOrgId);
        return  getBusinessBySapCode(sapCode);
    }

    /**
     * 根据orgIdList  批量获取连锁基本信息 包含连锁和平台简单信息
     * @param businessOrgIdList
     * @return
     */
    public static Optional<List<OrgInfoBaseCache>> getBusinessOrgByOrgIdList(List<Long> businessOrgIdList) {
        List<OrgInfoBaseCache> collect = businessOrgIdList.stream()
                .map(key -> getBusinessByOrgId(key).orElse(null)).filter(v -> Objects.nonNull(v))
                .collect(Collectors.toList());
        return Optional.ofNullable(collect);
    }
    /**
     * 根据sapCode 获取连锁基本信息 包含连锁和平台简单信息
     * @param sapCode
     * @return
     */
    public static Optional<OrgInfoBaseCache> getBusinessBySapCode(String sapCode) {
        return  sapCode == null ? Optional.empty() : Optional.ofNullable(CacheVar.businessCacheMap.get(sapCode));
    }


    /**
     * 根据sapCodeList 获取连锁基本信息 包含连锁和平台简单信息
     * @param sapCodeList
     * @return
     */
    public static Optional<List<OrgInfoBaseCache>> getBusinessBySapCodeList(List<String> sapCodeList) {
        List<OrgInfoBaseCache> collect = sapCodeList.stream()
                .map(key -> getBusinessBySapCode(key).orElse(null)).filter(v -> Objects.nonNull(v))
                .collect(Collectors.toList());
        return  Optional.ofNullable(collect);
    }


    /**
     * 根据platformOrgId 获取平台基本信息
     * @param platformOrgId
     * @return
     */
    public static Optional<OrgInfoBaseCache> getPlatformByOrgId(Long platformOrgId) {
        return platformOrgId == null ? Optional.empty() : Optional.ofNullable(CacheVar.platformCacheMap.get(platformOrgId));
    }

    /**
     * 根据连锁businessId 获取连锁下门店集合
     * @param businessId
     * @return
     */
    public static List<OrgInfoBaseCache> getStoreListByBusinessId(Long businessId) {
        if (businessId == null){
            return  Lists.newArrayList();
        }
        return  storeCacheMap.values().stream().filter(v-> null!=v.getId() && businessId.equals(v.getBusinessId())).collect(Collectors.toList());
    }

    /**
     * 根据连锁businessOrgId 获取连锁下门店集合
     * @param businessOrgId
     * @return
     */
    public static List<OrgInfoBaseCache> getStoreListByBusinessOrgId(Long businessOrgId) {
        if (businessOrgId == null){
            return  Lists.newArrayList();
        }
        return  storeCacheMap.values().stream().filter(v-> null!=v.getId() && businessOrgId.equals(v.getBusinessOrgId())).collect(Collectors.toList());
    }

    /**
     * 根据连锁businessSapCode 获取连锁下门店集合
     * @param businessSapCode
     * @return
     */
    public static List<OrgInfoBaseCache> getStoreListByBusinessSapCode(String businessSapCode) {
        if (StringUtils.isBlank(businessSapCode)){
            return  Lists.newArrayList();
        }
        return  storeCacheMap.values().stream().filter(v-> null!=v.getId() && businessSapCode.equals(v.getBusinessSapCode())).collect(Collectors.toList());
    }

    /**
     * 根据平台platformOrgId 获取平台下门店集合
     * @param platformOrgId
     * @return
     */
    public static List<OrgInfoBaseCache> getStoreListByPlatformOrgId(Long platformOrgId) {
        if (platformOrgId == null){
            return  Lists.newArrayList();
        }
        return  storeCacheMap.values().stream().filter(v-> null!=v.getId() && platformOrgId.equals(v.getPlatformOrgId())).collect(Collectors.toList());
    }

    /**
     * 组货店型转平台店型
     * @param storeTypeCode
     * @return
     */
    public static String storeTypeConvertPtStoreType (String storeTypeCode){
        if ("14".equals(storeTypeCode) || "24".equals(storeTypeCode) || "34".equals(storeTypeCode)) {
            return "PT01";
        }
        if ("10".equals(storeTypeCode) || "20".equals(storeTypeCode) || "30".equals(storeTypeCode)) {
            return "PT05";
        }
        if ("11".equals(storeTypeCode) || "21".equals(storeTypeCode) ||"31".equals(storeTypeCode)) {
            return "PT02";
        }
        if ("12".equals(storeTypeCode) || "22".equals(storeTypeCode) ||"32".equals(storeTypeCode)) {
            return "PT03";
        }
        if ("13".equals(storeTypeCode) || "23".equals(storeTypeCode) ||"33".equals(storeTypeCode)) {
            return "PT04";
        }
        if ("ZS01".equals(storeTypeCode)) {
            return storeTypeCode;
        }
        if ("ZS02".equals(storeTypeCode)) {
            return storeTypeCode;
        }
        if ("ZS03".equals(storeTypeCode)) {
            return storeTypeCode;
        }
        if ("ZS04".equals(storeTypeCode)) {
            return storeTypeCode;
        }
        return null;
    }
}
