package com.cowell.scib.cache.ohc;


import com.cowell.cstore.cache.manager.IDataCacheManager;
import com.cowell.cstore.cache.manager.OHCDataCacheManagerImpl;
import com.cowell.scib.service.vo.SpuListVo;
import com.cowell.scib.service.vo.SpuNewVo;

/**
 * 功    能：堆外缓存
 * 作    者：王代军
 * 时    间：2025年03月13日
 */
public class OHCDataCacheInstance {

    /**
     * 根据连锁和商品编码 缓存SpuNew堆外缓存
     */
    public static IDataCacheManager<String, SpuNewVo> businessGoodsOHCDataCache = new OHCDataCacheManagerImpl();


    /**
     * 根据集团商品编码 缓存SpuNew堆外缓存
     */
    public static IDataCacheManager<String, SpuListVo> groupGoodsOHCDataCache = new OHCDataCacheManagerImpl();

}
