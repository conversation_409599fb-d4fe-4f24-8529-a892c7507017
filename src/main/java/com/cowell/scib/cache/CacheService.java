package com.cowell.scib.cache;

import com.alibaba.fastjson.JSONObject;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.CommonEnums;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.mapperDgms.extend.CommonEnumsExtendMapper;
import com.cowell.scib.service.PermissionService;
import com.cowell.scib.service.RuleService;
import com.cowell.scib.service.StoreService;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CacheService {

    private final Logger log = LoggerFactory.getLogger(CacheService.class);

    @Autowired
    private StoreService storeService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private CommonEnumsExtendMapper commonEnumsExtendMapper;

    @PostConstruct
    public void permOrgCacheInit() {
        try {

            // 初始化平台缓存
            initPlatformCache();
            // 初始化连锁缓存
            initBusinessCache();
            // 初始化门店缓存
            initStoreCache();
            //加载门店全部数据
            refushMdmStoreExCache();
        } catch (Exception e) {
            log.error("加载缓存异常", e);
        }
    }

    public void refushMdmStoreExCache(){
        RuleParam param = new RuleParam();
        param.setScopeCode("TaskCreate");
        Map<String, List<OptionDto>> ruleEnum = ruleService.getRuleEnumList(param, null);
        // 平台必备店型
        Map<String, String> platStoreGroup = Optional.ofNullable(ruleEnum.get("PlatStoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 中参必备店型
        Map<String, String> zsStoreGroup = Optional.ofNullable(ruleEnum.get("ZsStoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 配方必备店型
        Map<String, String> pfStoreGroup = Optional.ofNullable(ruleEnum.get("PfStoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1));
        // 组货必备店型 (城市店_A类店:11)
        Map<String, String> storeGroup = Optional.ofNullable(ruleEnum.get("StoreGroup")).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getLabel, OptionDto::getValue, (k1, k2) -> k1));
        // 门店配送周期
        Map<String, String> deliveryCycleGroup =  commonEnumsExtendMapper.selectByDicCode("delivery_code").stream().collect(Collectors.toMap(CommonEnums::getEnumValue, CommonEnums::getEnumName, (k1, k2) -> k1));

        ArrayList<String> storeCodes = Lists.newArrayList(CacheVar.storeCacheMap.keySet());
        Lists.partition(storeCodes , Constants.INSERT_MAX_SIZE).forEach(v -> {
            storeService.findStoreByStoreNosAndExtend(v).forEach(store -> {
                MdmStoreExDTO exDTO = new MdmStoreExDTO();
                BeanUtils.copyProperties(store, exDTO);
                JSONObject extend = JSONObject.parseObject(exDTO.getExtend());
                log.info("门店store={} exDTO={} ",store.getStoreNo(),JSONObject.toJSONString(exDTO));
                if (StringUtils.isNotBlank(exDTO.getSalesLevel())) {
                    // 平台必备
                    String platStoreCode = salesLevelPlatStoreTypeMapping.get(exDTO.getSalesLevel());
                    if (StringUtils.isNotBlank(platStoreCode) && platStoreGroup.containsKey(platStoreCode)) {
                        exDTO.setPlatStoreTypeCode(platStoreCode);
                        exDTO.setPlatStoreType(platStoreGroup.get(platStoreCode));
                    }
                    //组货必备店型 (城市店_A类店:11)
                    if (Objects.nonNull(extend) && extend.containsKey("divisionType")) {
                        String storeGroupLabel = extend.getString("divisionType")+"_"+exDTO.getSalesLevel();
                        //log.info("门店store={} storeGroupLabel={} ",store.getStoreNo(),storeGroupLabel);
                        if (storeGroup.containsKey(storeGroupLabel)) {
                            //log.info("门店store={} storeGroupLabel={} code={}",store.getStoreNo(),storeGroupLabel,storeGroup.get(storeGroupLabel));
                            exDTO.setStoreTypeCode(storeGroup.get(storeGroupLabel));
                            exDTO.setStoreType(storeGroupLabel);
                        }
                    }
                }
                //选址商圈类型
                if (Objects.nonNull(extend) && extend.containsKey("goodTradingArea")) {
                    String goodTradingArea = extend.getString("goodTradingArea");
                    if(StringUtils.isNotBlank(goodTradingArea)) {
                        exDTO.setGoodTradingArea(goodTradingArea);
                    }
                }
                // 中参必备
                if (Objects.nonNull(extend) && extend.containsKey("zsShop")) {
                    String zsShop = extend.getString("zsShop");
                    if (StringUtils.isNotBlank(zsShop) && zsStoreTypeMapping.containsKey(zsShop)) {
                        exDTO.setZsStoreTypeCode(zsStoreTypeMapping.get(zsShop));
                        exDTO.setZsStoreType(zsStoreGroup.get(exDTO.getZsStoreTypeCode()));
                    }
                }
                // 配方
                if (Objects.nonNull(extend) && extend.containsKey("psStore")) {
                    String pfStore = extend.getString("psStore");
                    if (StringUtils.isNotBlank(pfStore) && pfStoreTypeMapping.containsKey(pfStore)) {
                        exDTO.setPfStoreTypeCode(pfStoreTypeMapping.get(pfStore));
                        exDTO.setPfStoreType(pfStoreGroup.get(exDTO.getPfStoreTypeCode()));
                    }
                }
                // 中参必备
                if (Objects.nonNull(extend) && extend.containsKey("zsShop")) {
                    String zsShop = extend.getString("zsShop");
                    if (StringUtils.isNotBlank(zsShop) && zsStoreTypeMapping.containsKey(zsShop)) {
                        exDTO.setZsStoreTypeCode(zsStoreTypeMapping.get(zsShop));
                        exDTO.setZsStoreType(zsStoreGroup.get(exDTO.getZsStoreTypeCode()));
                    }
                }
                // 配送周期
                if (Objects.nonNull(extend) && extend.containsKey("deliveryCycle")) {
                    String deliveryCycle = extend.getString("deliveryCycle");
                    if (StringUtils.isNotBlank(deliveryCycle) && deliveryCycleGroup.containsKey(deliveryCycle)) {
                        exDTO.setDeliveryDate(deliveryCycleGroup.get(deliveryCycle));
                    }
                }
                //log.info("门店store={} exDTO={} 完成 ",store.getStoreNo(),exDTO);
                //为OrgInfoBaseCache 对象补不分扩展属性
                buildStoreCacheExtendInfo(store.getStoreNo(), store);
                CacheVar.storeExMap.put(exDTO.getStoreNo(), exDTO);
               // 处理省份 - 城市映射
                String province = exDTO.getProvince();
                if (province != null) {
                    Set<String> citys = Optional.ofNullable(CacheVar.provinceMap.get(province)).orElse(new HashSet<>());
                    String city = exDTO.getCity();
                    if (StringUtils.isNotBlank(city)) {
                        citys.add(city);
                    }
                    CacheVar.provinceMap.put(province, citys);
                }
                // 处理城市 - 区县映射
                String city = exDTO.getCity();
                if (city != null) {
                    Set<String> areas = Optional.ofNullable(CacheVar.cityMap.get(city)).orElse(new HashSet<>());
                    String area = exDTO.getArea();
                    if (StringUtils.isNotBlank(area)) {
                        areas.add(area);
                    }
                    CacheVar.cityMap.put(city, areas);
                }
            });
        });
        log.info("refushMdmStoreExCache is ok  size={}",CacheVar.storeExMap.size());
    }

    /**
     * 补充缓存中门店 一些额外属性  如果需要可以在补充部分属性,不建议太多影响传输效率
     * @param storeNo
     * @param store
     */
    private void buildStoreCacheExtendInfo(String storeNo, MdmStoreBaseDTO store) {
        OrgInfoBaseCache orgInfoBaseCache = CacheVar.storeCacheMap.get(storeNo);
        if(null!=orgInfoBaseCache){
            JSONObject orgInfoBaseCacheExtend=new JSONObject();
            if(StringUtils.isNotEmpty(store.getExtend())) {
                JSONObject jsonObject = JSONObject.parseObject(store.getExtend());
                String deliveryCycle = jsonObject.getString("deliveryCycle");
                if(StringUtils.isNotEmpty(deliveryCycle)){
                    orgInfoBaseCacheExtend.put("deliveryCycle",deliveryCycle);
                }
            }
            if(StringUtils.isNotEmpty(store.getComId())){
                orgInfoBaseCacheExtend.put("comId",store.getComId());
            }
            if(StringUtils.isNotEmpty(store.getSalesLevel())){
                orgInfoBaseCacheExtend.put("salesLevel",store.getSalesLevel());
            }
            orgInfoBaseCache.setExtend(JSONObject.toJSONString(orgInfoBaseCacheExtend));
        }
    }

    public void initPlatformCache() {
        List<OrgDTO> platformOrgDTOS = permissionService.listChildOrgByOrgTypeAndOrgPath(OrgTypeEnum.PLATFORM.getCode(), "1/3/");
        // 增加预发测试平台
        platformOrgDTOS.addAll(permissionService.listChildOrgByOrgTypeAndOrgPath(OrgTypeEnum.PLATFORM.getCode(), "1/790/34927"));
        // 初始化平台缓存
        if (CollectionUtils.isNotEmpty(platformOrgDTOS)) {
            platformOrgDTOS.stream().filter(v->Objects.nonNull(v.getId())).forEach(v -> {
                OrgInfoBaseCache orgInfoBaseCache = new OrgInfoBaseCache();
                BeanUtils.copyProperties(v, orgInfoBaseCache);
                orgInfoBaseCache.setSapCode(v.getSapcode());
                orgInfoBaseCache.setPlatformOrgId(v.getId());
                orgInfoBaseCache.setPlatformShortName(v.getShortName());
                if (OrgTypeEnum.PLATFORM.getCode().equals(v.getType()) && null != v.getId()) {
                    CacheVar.platformCacheMap.put(v.getId(), orgInfoBaseCache);
                }
            });
        }
        log.info("initPlatformCache is ok  size={}",CacheVar.platformCacheMap.size());
    }


    public void initBusinessCache() {
        List<OrgDTO> businessOrgDTOS = permissionService.listChildOrgByOrgTypeAndOrgPath(OrgTypeEnum.BUSINESS.getCode(), "1/3/");
        // 增加预发测试连锁
        businessOrgDTOS.addAll(permissionService.listChildOrgByOrgTypeAndOrgPath(OrgTypeEnum.BUSINESS.getCode(), "1/790/34927/"));
        if (CollectionUtils.isNotEmpty(businessOrgDTOS)) {
            businessOrgDTOS.stream().filter(v->StringUtils.isNotBlank(v.getSapcode())).forEach(v -> {
                OrgInfoBaseCache orgInfoBaseCache = new OrgInfoBaseCache();
                BeanUtils.copyProperties(v, orgInfoBaseCache);
                orgInfoBaseCache.setSapCode(v.getSapcode());
                if (OrgTypeEnum.BUSINESS.getCode().equals(v.getType()) && null != v.getId()) {
                    orgInfoBaseCache.setBusinessOrgId(v.getId());
                    orgInfoBaseCache.setBusinessId(v.getOutId());
                    orgInfoBaseCache.setBusinessSapCode(v.getSapcode());
                    orgInfoBaseCache.setBusinessShortName(v.getShortName());
                    Optional<OrgInfoBaseCache> platformOrgOptional = getPlatformIdByPath(v.getOrgPath());
                    if (platformOrgOptional.isPresent()) {
                        OrgInfoBaseCache platform = platformOrgOptional.get();
                        orgInfoBaseCache.setPlatformOrgId(platform.getId());
                        orgInfoBaseCache.setPlatformShortName(platform.getShortName());
                    }
                    CacheVar.businessCacheMap.put(v.getSapcode(), orgInfoBaseCache);
                    if (orgInfoBaseCache.getId() != null && orgInfoBaseCache.getOutId() != null) {
                        CacheVar.businessOrgIdAndSapCodeMapping.put(v.getId(), v.getSapcode());
                        CacheVar.businessIdAndSapCodeMapping.put(v.getOutId(),v.getSapcode());
                    }
                }
            });
        }
        log.info("initBusinessCache is ok  size={}",CacheVar.businessCacheMap.size());
    }

    public void initStoreCache() {
        List<OrgDTO> storeOrgDTOS = permissionService.listChildOrgByOrgTypeAndOrgPath(OrgTypeEnum.STORE.getCode(), "1/3/");
        // 增加预发测试连锁门店
        storeOrgDTOS.addAll(permissionService.listChildOrgByOrgTypeAndOrgPath(OrgTypeEnum.STORE.getCode(), "1/790/34927/"));
        if (CollectionUtils.isNotEmpty(storeOrgDTOS)) {
            storeOrgDTOS = storeOrgDTOS.stream().filter(storeOrgDTO -> Objects.nonNull(storeOrgDTO.getOutId())).collect(Collectors.toList());
            List<Long> storeOrgIdList = storeOrgDTOS.stream().map(OrgDTO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeOrgIdList)) {
                storeOrgDTOS.stream().filter(v->StringUtils.isNotBlank(v.getSapcode())).forEach(v -> {
                    OrgInfoBaseCache orgInfoBaseCache = new OrgInfoBaseCache();
                    BeanUtils.copyProperties(v, orgInfoBaseCache);
                    orgInfoBaseCache.setSapCode(v.getSapcode());
                    Optional<OrgInfoBaseCache> businessOrgOptional = getBusinessSapCodeByPath(v.getOrgPath());
                    if (businessOrgOptional.isPresent()) {
                        OrgInfoBaseCache business = businessOrgOptional.get();
                        orgInfoBaseCache.setBusinessOrgId(business.getId());
                        orgInfoBaseCache.setBusinessId(business.getOutId());
                        orgInfoBaseCache.setBusinessSapCode(business.getSapCode());
                        orgInfoBaseCache.setBusinessShortName(business.getBusinessShortName());
                        orgInfoBaseCache.setPlatformShortName(business.getPlatformShortName());
                        orgInfoBaseCache.setPlatformOrgId(business.getPlatformOrgId());
                    }
                    CacheVar.storeCacheMap.put(v.getSapcode(), orgInfoBaseCache);
                    if (orgInfoBaseCache.getId() != null && orgInfoBaseCache.getOutId() != null) {
                        CacheVar.storeOrgIdAndSapCodeMapping.put(v.getId(), v.getSapcode());
                        CacheVar.storeIdAndSapCodeMapping.put(v.getOutId(),v.getSapcode());
                    }
                });
            }
        }
        log.info("initStoreCache is ok  size={}",CacheVar.storeCacheMap.size());
    }

    private Optional<OrgInfoBaseCache> getBusinessSapCodeByPath(String orgPath) {
        if (orgPath == null) {
            return Optional.empty();
        }
        String [] orgIds = StringUtils.split(orgPath,"/");
        return Arrays.stream(orgIds)
                .map(Long::parseLong)
                .filter(orgId -> CacheVar.getBusinessBySapCode(CacheVar.businessOrgIdAndSapCodeMapping.get(orgId)).isPresent())
                .map(orgId-> CacheVar.getBusinessBySapCode(CacheVar.businessOrgIdAndSapCodeMapping.get(orgId)).get()).findAny();
    }

    private Optional<OrgInfoBaseCache> getPlatformIdByPath(String orgPath) {
        if (orgPath == null) {
            return Optional.empty();
        }
        String [] orgIds = StringUtils.split(orgPath,"/");
        return Arrays.stream(orgIds)
                .map(Long::parseLong)
                .filter(orgId ->CacheVar.platformCacheMap.containsKey(orgId))
                .map(orgId->CacheVar.platformCacheMap.get(orgId)).findAny();
    }



    // 月销等级与平台必备店型对应关系
    public static final Map<String, String> salesLevelPlatStoreTypeMapping = new HashMap<>();

    // 月销等级商圈与组货必备店型对应关系
    public static final Map<String, String> salesLevelCricleStoreTypeMapping = new HashMap<>();

    // 中参店型映射关系
    public static final Map<String, String> zsStoreTypeMapping = new HashMap<>();

    // 配方店型映射关系
    public static final Map<String, String> pfStoreTypeMapping = new HashMap<>();

    static {
        salesLevelPlatStoreTypeMapping.put("AA类店", "PT01");
        salesLevelPlatStoreTypeMapping.put("A类店", "PT01");
        salesLevelPlatStoreTypeMapping.put("B类店", "PT05");
        salesLevelPlatStoreTypeMapping.put("C类店", "PT02");
        salesLevelPlatStoreTypeMapping.put("D类店", "PT03");
        salesLevelPlatStoreTypeMapping.put("D1类店", "PT03");
        salesLevelPlatStoreTypeMapping.put("D2类店", "PT03");
        salesLevelPlatStoreTypeMapping.put("E类店", "PT03");
        salesLevelPlatStoreTypeMapping.put("F类店", "PT04");
        salesLevelPlatStoreTypeMapping.put("F1类店", "PT04");
        salesLevelPlatStoreTypeMapping.put("F2类店", "PT04");
        salesLevelPlatStoreTypeMapping.put("F3类店", "PT04");

        salesLevelCricleStoreTypeMapping.put("AA类店商业店", "14");
        salesLevelCricleStoreTypeMapping.put("AA类店社区及商住店", "14");
        salesLevelCricleStoreTypeMapping.put("AA类店乡镇店", "24");
        salesLevelCricleStoreTypeMapping.put("AA类店院边店", "34");
        salesLevelCricleStoreTypeMapping.put("A类店商业店", "14");
        salesLevelCricleStoreTypeMapping.put("A类店社区及商住店", "14");
        salesLevelCricleStoreTypeMapping.put("A类店乡镇店", "24");
        salesLevelCricleStoreTypeMapping.put("A类店院边店", "34");
        salesLevelCricleStoreTypeMapping.put("B类店商业店", "10");
        salesLevelCricleStoreTypeMapping.put("B类店社区及商住店", "10");
        salesLevelCricleStoreTypeMapping.put("B类店乡镇店", "20");
        salesLevelCricleStoreTypeMapping.put("B类店院边店", "30");
        salesLevelCricleStoreTypeMapping.put("C类店商业店", "11");
        salesLevelCricleStoreTypeMapping.put("C类店社区及商住店", "11");
        salesLevelCricleStoreTypeMapping.put("C类店乡镇店", "21");
        salesLevelCricleStoreTypeMapping.put("C类店院边店", "31");
        salesLevelCricleStoreTypeMapping.put("D类店商业店", "12");
        salesLevelCricleStoreTypeMapping.put("D类店社区及商住店", "12");
        salesLevelCricleStoreTypeMapping.put("D类店乡镇店", "22");
        salesLevelCricleStoreTypeMapping.put("D类店院边店", "32");
        salesLevelCricleStoreTypeMapping.put("D1类店商业店", "12");
        salesLevelCricleStoreTypeMapping.put("D1类店社区及商住店", "12");
        salesLevelCricleStoreTypeMapping.put("D1类店乡镇店", "22");
        salesLevelCricleStoreTypeMapping.put("D1类店院边店", "32");
        salesLevelCricleStoreTypeMapping.put("D2类店商业店", "12");
        salesLevelCricleStoreTypeMapping.put("D2类店社区及商住店", "12");
        salesLevelCricleStoreTypeMapping.put("D2类店乡镇店", "22");
        salesLevelCricleStoreTypeMapping.put("D2类店院边店", "32");
        salesLevelCricleStoreTypeMapping.put("E类店商业店", "12");
        salesLevelCricleStoreTypeMapping.put("E类店社区及商住店", "12");
        salesLevelCricleStoreTypeMapping.put("E类店乡镇店", "22");
        salesLevelCricleStoreTypeMapping.put("E类店院边店", "32");
        salesLevelCricleStoreTypeMapping.put("F类店商业店", "13");
        salesLevelCricleStoreTypeMapping.put("F类店社区及商住店", "13");
        salesLevelCricleStoreTypeMapping.put("F类店乡镇店", "23");
        salesLevelCricleStoreTypeMapping.put("F类店院边店", "33");
        salesLevelCricleStoreTypeMapping.put("F1类店商业店", "13");
        salesLevelCricleStoreTypeMapping.put("F1类店社区及商住店", "13");
        salesLevelCricleStoreTypeMapping.put("F1类店乡镇店", "23");
        salesLevelCricleStoreTypeMapping.put("F1类店院边店", "33");
        salesLevelCricleStoreTypeMapping.put("F2类店商业店", "13");
        salesLevelCricleStoreTypeMapping.put("F2类店社区及商住店", "13");
        salesLevelCricleStoreTypeMapping.put("F2类店乡镇店", "23");
        salesLevelCricleStoreTypeMapping.put("F2类店院边店", "33");
        salesLevelCricleStoreTypeMapping.put("F3类店商业店", "13");
        salesLevelCricleStoreTypeMapping.put("F3类店社区及商住店", "13");
        salesLevelCricleStoreTypeMapping.put("F3类店乡镇店", "23");
        salesLevelCricleStoreTypeMapping.put("F3类店院边店", "33");

        zsStoreTypeMapping.put("ZS-大店", "ZS01");
        zsStoreTypeMapping.put("ZS-中店", "ZS02");
        zsStoreTypeMapping.put("ZS-小店", "ZS03");
        zsStoreTypeMapping.put("ZS-微店", "ZS04");

        pfStoreTypeMapping.put("配方饮片-大店", "PF01");
        pfStoreTypeMapping.put("配方饮片-中店", "PF02");
        pfStoreTypeMapping.put("配方饮片-小店", "PF03");
    }

}
