package com.cowell.scib.config.event;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 *
 */
@Aspect
@Component
@Slf4j
public class ScibEventListenerConfig {

    //@Autowired
    /**
     * 发送处理消息 - 全量处理
     *
     * @param event
     */
    @EventListener(classes = JymlSkuLimitConfigureUpdateEvent.class)
    public void sendMsgProcesse(JymlSkuLimitConfigureUpdateEvent event) {
      log.info("event={}",event);
    }

}
