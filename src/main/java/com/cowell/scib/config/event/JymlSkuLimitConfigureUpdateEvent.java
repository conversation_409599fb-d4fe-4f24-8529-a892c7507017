package com.cowell.scib.config.event;

import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.enums.ScibCommonEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 *
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JymlSkuLimitConfigureUpdateEvent {

    /**
     * 促销单号
     */
    private ScibCommonEnums.MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM eventEnum;

    /**
     * 触发方法
     */
    private JymlSkuMaxLimitConfigure configure;

    /**
     * 获取当前方法名
     *
     * @return
     */
    public static String getCurrentMethodName(String name) {
        log.info("name={}",name);
        return "";
    }

}
