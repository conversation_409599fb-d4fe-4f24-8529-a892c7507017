package com.cowell.scib.config;

import io.github.jhipster.async.ExceptionHandlingAsyncTaskExecutor;
import io.github.jhipster.config.JHipsterProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.instrument.async.LazyTraceThreadPoolTaskExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
@EnableScheduling
public class AsyncConfiguration implements AsyncConfigurer {

    private final Logger log = LoggerFactory.getLogger(AsyncConfiguration.class);

    private final JHipsterProperties jHipsterProperties;
    @Autowired
    private BeanFactory beanFactory;

    public AsyncConfiguration(JHipsterProperties jHipsterProperties) {
        this.jHipsterProperties = jHipsterProperties;
    }

    @Override
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        log.debug("Creating Async Task Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(jHipsterProperties.getAsync().getCorePoolSize());
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("scib-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "taskExecutorTrace")
    public AsyncTaskExecutor getAsyncExecutorTrace() {
        log.debug("Creating Async Task Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("bam-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }


    @Bean(name = "storeCommissionRecommendExecutor")
    public AsyncTaskExecutor getAsyncStoreCommissionRecommend() {
        log.debug("Creating storeCommissionRecommend Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("storeCommission-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "trackResultTaskExecutor")
    public AsyncTaskExecutor getTrackResultTaskExecutor() {
        log.debug("Creating trackResultFileExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("trackResultTask-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "necessaryTaskExecutor")
    public AsyncTaskExecutor getNecessaryTaskExecutor() {
        log.debug("Creating getNecessaryTaskExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("necessaryTask-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "trackResultFileUploadExecutor")
    public AsyncTaskExecutor getTrackResultFileUploadExecutor() {
        log.debug("Creating trackResultFileUploadExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("trackResultFileUploadTask-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "mdmGoodStatusUpdateSixNecessaryExecutor")
    public AsyncTaskExecutor getMdmGoodStatusUpdateSixNecessaryExecutor() {
        log.debug("Creating mdmGoodStatusUpdateSixNecessaryExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("mdmGoodStatusUpdateSixNecessaryExecutor-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "mdmGoodStatusUpdateCFExecutor")
    public AsyncTaskExecutor getdmGoodStatusUpdateCFExecutor() {
        log.debug("Creating mdmGoodStatusUpdateCFExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("mdmGoodStatusUpdateCFExecutor-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "manageContentsConfirmExecutor")
    public AsyncTaskExecutor manageContentsConfirmExecutor() {
        log.debug("Creating manageContentsConfirmExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("manageContentsConfirmExecutor-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Bean(name = "configureAdjustAddExecutor")
    public AsyncTaskExecutor configureAdjustAddExecutor() {
        log.debug("Creating configureAdjustAddExecutor Executor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(jHipsterProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(jHipsterProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("configureAdjustAddExecutor-Executor-");
        LazyTraceThreadPoolTaskExecutor traceThreadPoolTaskExecutor = new LazyTraceThreadPoolTaskExecutor(this.beanFactory, executor);
        return new ExceptionHandlingAsyncTaskExecutor(traceThreadPoolTaskExecutor);
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }
}
