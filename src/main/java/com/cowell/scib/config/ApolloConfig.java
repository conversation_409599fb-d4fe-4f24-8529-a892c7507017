package com.cowell.scib.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @description: ${description}
 * @author: zzy
 * @create: 2018-07-09 14:00
 **/
@ConfigurationProperties("apollo")
@Component
@RefreshScope
public class ApolloConfig {
    private String env;
    private String appId;

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String toString() {
        return "ApolloConfig{" +
            "env='" + env + '\'' +
            ", appId='" + appId + '\'' +
            '}';
    }
}
