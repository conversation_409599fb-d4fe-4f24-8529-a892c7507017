package com.cowell.scib.config.sharding;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.cowell.scib.config.sharding.algorithm.*;
import io.shardingsphere.api.config.ShardingRuleConfiguration;
import io.shardingsphere.api.config.TableRuleConfiguration;
import io.shardingsphere.api.config.strategy.ComplexShardingStrategyConfiguration;
import io.shardingsphere.api.config.strategy.NoneShardingStrategyConfiguration;
import io.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.health.CompositeHealthIndicator;
import org.springframework.boot.actuate.health.DataSourceHealthIndicator;
import org.springframework.boot.actuate.health.HealthAggregator;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Scib 数据源配置
 */
@Configuration
@MapperScan(basePackages = {"com.cowell.scib.mapperDgms"}, sqlSessionTemplateRef = "DgmsSqlSessionTemplate")
public class DataSourceDgmsConfig {

    @Autowired
    private HealthAggregator healthAggregator;

    @Autowired
    private Environment environment;

    /**
     * 配置Scib数据源
     *
     * @return
     */
    @Bean(name = "dgms_0")
    @ConfigurationProperties(prefix = "spring.datasource-dgms0")
    @Qualifier("dgms_0")
    public DataSource dataSource0() {
        return DruidDataSourceBuilder.create().build(environment, "spring.datasource-dgms0.");
    }

    @Bean(name = "dgms_1")
    @ConfigurationProperties(prefix = "spring.datasource-dgms1")
    @Qualifier("dgms_1")
    public DataSource dataSource1() {
        return DruidDataSourceBuilder.create().build(environment, "spring.datasource-dgms1.");
    }

    @Bean(name = "dataSource")
    public DataSource getShardingDataSource() throws SQLException {

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.getTableRuleConfigs().add(getStoreGoodsInfoRule("store_goods_info", "store_id"));
        shardingRuleConfig.getTableRuleConfigs().add(getMdmTaskDetailRule("mdm_task_detail", "task_id"));
        shardingRuleConfig.getTableRuleConfigs().add(getStoreGoodsContentsRule("store_goods_contents", "store_id"));
        shardingRuleConfig.getTableRuleConfigs().add(getStoreGoodsProcessRule("store_goods_process", "store_id"));

        shardingRuleConfig.setDefaultDataSourceName("dgms_0");
        shardingRuleConfig.setDefaultDatabaseShardingStrategyConfig(new NoneShardingStrategyConfiguration());
        shardingRuleConfig.setDefaultTableShardingStrategyConfig(new NoneShardingStrategyConfiguration());
        Properties p =  new Properties() ;
        p.put("sql.show", true) ;
        return ShardingDataSourceFactory.createDataSource(createDataSourceMap(),
                shardingRuleConfig, new ConcurrentHashMap<>(), p);
    }

    TableRuleConfiguration getMdmTaskDetailRule(String tableName, String shardingColumns) {
        TableRuleConfiguration result = new TableRuleConfiguration();
        result.setLogicTable(tableName);
        result.setActualDataNodes("dgms_${0..1}.mdm_task_detail_${0..999}");
        // 分库策略
        result.setDatabaseShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new MdmTaskDetailShardingDatabaseAlgorithm()));
        // 分表策略
        result.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new MdmTaskDetailShardingTableAlgorithm()));
        return result;
    }

    TableRuleConfiguration getStoreGoodsInfoRule(String tableName, String shardingColumns) {
        TableRuleConfiguration result = new TableRuleConfiguration();
        result.setLogicTable(tableName);
        result.setActualDataNodes("dgms_${0..1}.store_goods_info_${0..255}");
        // 分库策略
        result.setDatabaseShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new StoreGoodsInfoShardingDatabaseAlgorithm()));
        // 分表策略
        result.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new StoreGoodsInfoShardingTableAlgorithm()));
        return result;
    }
    TableRuleConfiguration getStoreGoodsContentsRule(String tableName, String shardingColumns) {
        TableRuleConfiguration result = new TableRuleConfiguration();
        result.setLogicTable(tableName);
        result.setActualDataNodes("dgms_${0..1}.store_goods_contents_${0..255}");
        // 分库策略
        result.setDatabaseShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new StoreGoodsContentsShardingDatabaseAlgorithm()));
        // 分表策略
        result.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new StoreGoodsContentsShardingTableAlgorithm()));
        return result;
    }
    TableRuleConfiguration getStoreGoodsProcessRule(String tableName, String shardingColumns) {
        TableRuleConfiguration result = new TableRuleConfiguration();
        result.setLogicTable(tableName);
        result.setActualDataNodes("dgms_${0..1}.store_goods_process_${0..255}");
        // 分库策略
        result.setDatabaseShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new StoreGoodsProcessShardingDatabaseAlgorithm()));
        // 分表策略
        result.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration(shardingColumns, new StoreGoodsProcessShardingTableAlgorithm()));
        return result;
    }

    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> result = new HashMap<>();
        result.put("dgms_0", dataSource0());
        result.put("dgms_1", dataSource1());
        return result;
    }

    @Bean(name = "DgmsSqlSessionFactory")
    @Primary
    public SqlSessionFactory getSqlSessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper-dgms/**/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "DgmsSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate getSqlSessionTemplate(@Qualifier("DgmsSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "dbHealthIndicator")
    public HealthIndicator dbHealthIndicator() {

        Map<String, DataSource> dataSourceMap = new HashMap<>(4);
        dataSourceMap.put("dgms_0", dataSource0());
        dataSourceMap.put("dgms_1", dataSource1());
        if (dataSourceMap.size() == 1) {
            return new DataSourceHealthIndicator(dataSourceMap.values().iterator().next());
        } else {
            CompositeHealthIndicator composite = new CompositeHealthIndicator(this.healthAggregator);
            Iterator var3 = dataSourceMap.entrySet().iterator();

            while (var3.hasNext()) {
                Map.Entry<String, DataSource> entry = (Map.Entry) var3.next();
                composite.addHealthIndicator((String) entry.getKey(), new DataSourceHealthIndicator(entry.getValue()));
            }

            return composite;

        }
    }

}
