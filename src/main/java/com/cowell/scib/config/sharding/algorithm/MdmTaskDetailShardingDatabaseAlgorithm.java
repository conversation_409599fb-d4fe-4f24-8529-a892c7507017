package com.cowell.scib.config.sharding.algorithm;

import com.cowell.framework.utils.IdUtils;
import io.shardingsphere.api.algorithm.sharding.ListShardingValue;
import io.shardingsphere.api.algorithm.sharding.ShardingValue;
import io.shardingsphere.api.algorithm.sharding.complex.ComplexKeysShardingAlgorithm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

public class MdmTaskDetailShardingDatabaseAlgorithm implements ComplexKeysShardingAlgorithm {
    private static final int DB_COUNT = 2;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, Collection<ShardingValue> shardingValues) {
        Collection<Long> taskIdValues = getShardingValue(shardingValues, "task_id");
        List<String> shardingSuffix = new ArrayList<>();
        if (taskIdValues != null && taskIdValues.size() > 0) {
            // 1 id对DB_COUNT取余
            // 2 千位对2取余 判断奇偶性 偶数不变 奇数库反转 不满千位默认为0
            for (Long val : taskIdValues) {
                long dbNum = val % DB_COUNT;
                Integer lastFourNum;
                if (val >= 1000) {
                    lastFourNum = Integer.parseInt(IdUtils.getFirstNumber(IdUtils.getLastNumber(val,4), 1));
                } else {
                    lastFourNum = 0;
                }
                int oddEven = lastFourNum % 2;
                String suffix = "_" + (oddEven == 0 ? dbNum : Math.negateExact(dbNum - 1)) + "";
                availableTargetNames.forEach(x -> {
                    if (x.endsWith(suffix)) {
                        shardingSuffix.add(x);
                    }
                });
            }
        }

        return shardingSuffix;
    }

    private Collection<Long> getShardingValue(Collection<ShardingValue> shardingValues, final String key) {
        Collection<Long> valueSet = new ArrayList<>();
        Iterator<ShardingValue> iterator = shardingValues.iterator();
        while (iterator.hasNext()) {
            ShardingValue next = iterator.next();
            if (next instanceof ListShardingValue) {
                ListShardingValue value = (ListShardingValue) next;
                if (value.getColumnName().equals(key)) {
                    return value.getValues();
                }
            }
        }
        return valueSet;
    }

}
