package com.cowell.scib.config.sharding.algorithm;

import com.cowell.scib.rest.errors.BusinessErrorException;
import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;

public class JymlStoreSkuSuggestShardingTableAlgorithm implements PreciseShardingAlgorithm<Long> {

    private static final int TABLE_COUNT = 13;
    private static final Logger logger = LoggerFactory.getLogger(JymlStoreSkuSuggestShardingTableAlgorithm.class);

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {
        Integer year = 0;
        Integer month = 0;
        if(Objects.isNull(shardingValue.getValue())){
            logger.error("JymlStoreSkuSugges没有传入分表键");
            throw new BusinessErrorException("JymlStoreSkuSugges没有传入分表键");
        } else {
            Long businessOrgId = shardingValue.getValue();
            return shardingValue.getLogicTableName() + "_" + businessOrgId;
        }
    }

}
