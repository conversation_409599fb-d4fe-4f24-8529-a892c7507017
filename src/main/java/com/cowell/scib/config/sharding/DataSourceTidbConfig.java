package com.cowell.scib.config.sharding;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.cowell.scib.config.sharding.algorithm.JymlStoreSkuSuggestShardingTableAlgorithm;
import com.cowell.scib.config.sharding.algorithm.StoreGoodsContentsShardingDatabaseAlgorithm;
import com.cowell.scib.config.sharding.algorithm.StoreGoodsContentsShardingTableAlgorithm;
import io.shardingsphere.api.config.ShardingRuleConfiguration;
import io.shardingsphere.api.config.TableRuleConfiguration;
import io.shardingsphere.api.config.strategy.ComplexShardingStrategyConfiguration;
import io.shardingsphere.api.config.strategy.NoneShardingStrategyConfiguration;
import io.shardingsphere.api.config.strategy.StandardShardingStrategyConfiguration;
import io.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源及分表配置
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.cowell.scib.mapperTidb"}, sqlSessionTemplateRef = "TidbSqlSessionTemplate")
public class DataSourceTidbConfig {

    /**
     * 配置Tidb数据源
     *
     * @return
     */
    @Bean(name = "tidb")
//    @Bean(name = "TidbDatasource")
    @ConfigurationProperties(prefix = "spring.datasource-Tidb")
//    @Qualifier("TidbDatasource")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

//    /**
//     * 需要手动配置事务管理器
//     *
//     * @param dataSource
//     * @return
//     */
//    @Bean
//    public DataSourceTransactionManager transactitonManager(@Qualifier("TidbDatasource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }

    @Bean(name = "TidbDatasource")
    @Qualifier("TidbDatasource")
    public DataSource getShardingDataSource() throws SQLException {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.setDefaultDataSourceName("TidbDatasource");
        shardingRuleConfig.setDefaultDatabaseShardingStrategyConfig(new NoneShardingStrategyConfiguration());
        shardingRuleConfig.setDefaultTableShardingStrategyConfig(new NoneShardingStrategyConfiguration());
        shardingRuleConfig.getTableRuleConfigs().add(getJymlStoreSkuSuggestRule("jyml_store_sku_suggest", "business_org_id"));
        Properties p =  new Properties() ;
        p.put("sql.show", true) ;
        return ShardingDataSourceFactory.createDataSource(createDataSourceMap(),
                shardingRuleConfig, new ConcurrentHashMap<>(), p);
    }

    private TableRuleConfiguration getJymlStoreSkuSuggestRule(String tableName, String shardingColumns) {
        TableRuleConfiguration result = new TableRuleConfiguration();
        result.setLogicTable(tableName);
        // 分表策略 按企业orgId分表
        result.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(shardingColumns, new JymlStoreSkuSuggestShardingTableAlgorithm()));
        return result;

    }

    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> result = new HashMap<>();
        result.put("TidbDatasource", dataSource());
        return result;
    }

    @Bean(name = "TidbSqlSessionFactory")
    @Primary
    public SqlSessionFactory testSqlSessionFactory(@Qualifier("TidbDatasource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper-tidb/**/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "TidbSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("TidbSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
