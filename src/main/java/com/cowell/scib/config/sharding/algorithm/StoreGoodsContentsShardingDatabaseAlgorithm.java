package com.cowell.scib.config.sharding.algorithm;

import com.cowell.framework.utils.IdUtils;
import io.shardingsphere.api.algorithm.sharding.ListShardingValue;
import io.shardingsphere.api.algorithm.sharding.ShardingValue;
import io.shardingsphere.api.algorithm.sharding.complex.ComplexKeysShardingAlgorithm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

public class StoreGoodsContentsShardingDatabaseAlgorithm implements ComplexKeysShardingAlgorithm {
    private static final int DB_COUNT = 2;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, Collection<ShardingValue> shardingValues) {
        Collection<Long> storeIdValues = getShardingValue(shardingValues, "store_id");
        List<String> shardingSuffix = new ArrayList<>();
        if (storeIdValues != null && storeIdValues.size() > 0) {
            for (Long val : storeIdValues) {
                // 后10位前5位的前2位分库
                int lastFiveNum = Integer.parseInt(IdUtils.getFirstNumber(IdUtils.getLastNumber(val,10),5));
                int lastThreeNum = Integer.parseInt(IdUtils.getLastNumber(String.valueOf(lastFiveNum),3));
                Integer dbNum = (lastFiveNum - lastThreeNum) / 1000;
                String suffix = "_" + dbNum % DB_COUNT + "";
                availableTargetNames.forEach(x -> {
                    if (x.endsWith(suffix)) {
                        shardingSuffix.add(x);
                    }
                });
            }
        }

        return shardingSuffix;
    }
    private Collection<Long> getShardingValue(Collection<ShardingValue> shardingValues, final String key) {
        Collection<Long> valueSet = new ArrayList<>();
        Iterator<ShardingValue> iterator = shardingValues.iterator();
        while (iterator.hasNext()) {
            ShardingValue next = iterator.next();
            if (next instanceof ListShardingValue) {
                ListShardingValue value = (ListShardingValue) next;
                if (value.getColumnName().equals(key)) {
                    return value.getValues();
                }
            }
        }
        return valueSet;
    }

}
