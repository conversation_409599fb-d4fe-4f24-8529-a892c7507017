package com.cowell.scib.config;

import com.cowell.scib.mq.ConsumerAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version MqConsumerApplicationRunner.java, 2020/8/19 11:37
 */
@Component
public class MqConsumerApplicationRunner implements ApplicationRunner,ApplicationContextAware {


    List<ConsumerAdapter> consumers ;
    @Override
    public void run(ApplicationArguments var1) throws Exception{
        if(CollectionUtils.isNotEmpty(consumers)){
            consumers.forEach(ConsumerAdapter::start);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ConsumerAdapter> map = applicationContext.getBeansOfType(ConsumerAdapter.class);
        consumers = new ArrayList<>();
        map.forEach((key, value) -> consumers.add(value) );
    }
}
