package com.cowell.scib.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;

@ConfigurationProperties(prefix = "spring.redisson")
@Configuration
public class RedissonConfig extends CachingConfigurerSupport {
    private final Logger log = LoggerFactory.getLogger(RedissonConfig.class);
	private List<String> address;
    @Value("${spring.redisson.read-mode:MASTER_SLAVE}")
    private String readMode;

    @Bean
    RedissonClient redisson() {

        Config config = new Config();
        // 新增配置
        config.setCodec(new JsonJacksonCodec());
        ClusterServersConfig clusterConfig = config.useClusterServers()
                .setScanInterval(2000).setKeepAlive(true)
                .setMasterConnectionPoolSize(10).setMasterConnectionMinimumIdleSize(10)
                .setSlaveConnectionPoolSize(10).setSlaveConnectionMinimumIdleSize(10)
                .setReadMode(ReadMode.valueOf(readMode));
        clusterConfig.setPingConnectionInterval(3000);
        for (String str : address) {
            clusterConfig.addNodeAddress(str);
            log.info("cluster address========"+str);
        }
        log.info("cluster redisson readMode======="+readMode);
        return Redisson.create(config);
    }

	public List<String> getAddress() {
		return address;
	}

	public void setAddress(List<String> address) {
		this.address = address;
	}

}
