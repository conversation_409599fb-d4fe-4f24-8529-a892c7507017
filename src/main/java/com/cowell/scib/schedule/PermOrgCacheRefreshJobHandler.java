package com.cowell.scib.schedule;

import com.cowell.scib.cache.CacheService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 定时刷新权限机构缓存
 */
@JobHandler(value = "permOrgRefreshJobHandler")
@Slf4j
@Component
public class PermOrgCacheRefreshJobHandler extends IJobHandler {

    @Autowired
    private CacheService cacheService;

    @Override
    @NewSpan
    public ReturnT<String> execute(String s) {
        XxlJobLogger.log("PermOrgCacheRefreshJobHandler|XXL-JOB, start-----------------");
        try {
            cacheService.permOrgCacheInit();
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("PermOrgCacheRefreshJobHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
