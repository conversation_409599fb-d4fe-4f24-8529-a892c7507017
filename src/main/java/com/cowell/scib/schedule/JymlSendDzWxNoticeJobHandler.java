package com.cowell.scib.schedule;

import com.cowell.scib.cache.CacheService;
import com.cowell.scib.service.impl.jyml.ManageContentsAutoHandlerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 每日检查需要发送店长 微信提醒 且 兼容 0点自动确认逻辑
 */
@JobHandler(value = "jymlSendDzWxNoticeJobHandler")
@Slf4j
@Component
public class JymlSendDzWxNoticeJobHandler extends IJobHandler {

    @Autowired
    private ManageContentsAutoHandlerService manageContentsAutoHandlerService;

    @Override
    @NewSpan
    public ReturnT<String> execute(String s) {
        XxlJobLogger.log("PermOrgCacheRefreshJobHandler|XXL-JOB, start-----------------");
        try {
            manageContentsAutoHandlerService.handleProcess(s);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("PermOrgCacheRefreshJobHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
