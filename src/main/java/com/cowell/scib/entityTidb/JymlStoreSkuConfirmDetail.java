package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-商品确认明细表
 */
@Data
public class JymlStoreSkuConfirmDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 连锁orgid
     */
    private Long businessOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 关联的进度明细表ID
     */
    private Long processDetailId;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品大类id
     */
    private String category;

    /**
     * 商品中类id
     */
    private String middleCategory;

    /**
     * 商品小类id
     */
    private String smallCategory;

    /**
     * 商品子类id
     */
    private String subCategory;

    /**
     * 分类全路径
     */
    private String categoryPath;

    /**
     * 系统建议状态
     */
    private Integer systemSuggest;

    /**
     * 上次审核结果
     */
    private Integer previousReviewResult;

    /**
     * 本次我的确认
     */
    private Integer myConfirm;

    /**
     * 本次审核结果
     */
    private Integer reviewResult;

    /**
     * 提交人ID
     */
    private Long submitBy;

    /**
     * 提交人
     */
    private String submitName;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核人ID
     */
    private Long reviewBy;

    /**
     * 审核人
     */
    private String reviewName;

    /**
     * 审核时间
     */
    private Date reviewTime;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;
}