package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 单店推荐表
 */
public class DgmsSingleStoreRecommend implements Serializable {
    /**
     * 无业务逻辑主键
     */
    private Long id;

    /**
     * 推荐日期
     */
    private Date recommendDate;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 是否有提成 0否 1是
     */
    private Byte promotionAble;

    /**
     * 提成规则名称
     */
    private String promotionName;

    /**
     * 奖励方式
     */
    private String promotionWay;

    /**
     * 奖励类型
     */
    private String thresholdInfo;

    /**
     * 奖励
     */
    private String favInfo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getRecommendDate() {
        return recommendDate;
    }

    public void setRecommendDate(Date recommendDate) {
        this.recommendDate = recommendDate;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Byte getPromotionAble() {
        return promotionAble;
    }

    public void setPromotionAble(Byte promotionAble) {
        this.promotionAble = promotionAble;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionWay() {
        return promotionWay;
    }

    public void setPromotionWay(String promotionWay) {
        this.promotionWay = promotionWay;
    }

    public String getThresholdInfo() {
        return thresholdInfo;
    }

    public void setThresholdInfo(String thresholdInfo) {
        this.thresholdInfo = thresholdInfo;
    }

    public String getFavInfo() {
        return favInfo;
    }

    public void setFavInfo(String favInfo) {
        this.favInfo = favInfo;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DgmsSingleStoreRecommend other = (DgmsSingleStoreRecommend) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getRecommendDate() == null ? other.getRecommendDate() == null : this.getRecommendDate().equals(other.getRecommendDate()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getPromotionAble() == null ? other.getPromotionAble() == null : this.getPromotionAble().equals(other.getPromotionAble()))
            && (this.getPromotionName() == null ? other.getPromotionName() == null : this.getPromotionName().equals(other.getPromotionName()))
            && (this.getPromotionWay() == null ? other.getPromotionWay() == null : this.getPromotionWay().equals(other.getPromotionWay()))
            && (this.getThresholdInfo() == null ? other.getThresholdInfo() == null : this.getThresholdInfo().equals(other.getThresholdInfo()))
            && (this.getFavInfo() == null ? other.getFavInfo() == null : this.getFavInfo().equals(other.getFavInfo()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getRecommendDate() == null) ? 0 : getRecommendDate().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getPromotionAble() == null) ? 0 : getPromotionAble().hashCode());
        result = prime * result + ((getPromotionName() == null) ? 0 : getPromotionName().hashCode());
        result = prime * result + ((getPromotionWay() == null) ? 0 : getPromotionWay().hashCode());
        result = prime * result + ((getThresholdInfo() == null) ? 0 : getThresholdInfo().hashCode());
        result = prime * result + ((getFavInfo() == null) ? 0 : getFavInfo().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", recommendDate=").append(recommendDate);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", promotionAble=").append(promotionAble);
        sb.append(", promotionName=").append(promotionName);
        sb.append(", promotionWay=").append(promotionWay);
        sb.append(", thresholdInfo=").append(thresholdInfo);
        sb.append(", favInfo=").append(favInfo);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}