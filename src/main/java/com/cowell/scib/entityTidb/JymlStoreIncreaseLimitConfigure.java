package com.cowell.scib.entityTidb;

import java.io.Serializable;

/**
 * <AUTHOR> 经营目录-门店在营商品上浮管理
 */
public class JymlStoreIncreaseLimitConfigure implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 项目公司id
     */
    private Long businessOrgId;

    /**
     * 项目公司
     */
    private String businessName;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 上浮额度
     */
    private Integer increaseLimit;

    /**
     * 二轮选配商品个数下限
     */
    private Integer increaseLow;

    /**
     * 生效状态
     */
    private Byte status;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessOrgId() {
        return businessOrgId;
    }

    public void setBusinessOrgId(Long businessOrgId) {
        this.businessOrgId = businessOrgId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Integer getIncreaseLimit() {
        return increaseLimit;
    }

    public void setIncreaseLimit(Integer increaseLimit) {
        this.increaseLimit = increaseLimit;
    }

    public Integer getIncreaseLow() {
        return increaseLow;
    }

    public void setIncreaseLow(Integer increaseLow) {
        this.increaseLow = increaseLow;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        JymlStoreIncreaseLimitConfigure other = (JymlStoreIncreaseLimitConfigure) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessOrgId() == null ? other.getBusinessOrgId() == null : this.getBusinessOrgId().equals(other.getBusinessOrgId()))
            && (this.getBusinessName() == null ? other.getBusinessName() == null : this.getBusinessName().equals(other.getBusinessName()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getIncreaseLimit() == null ? other.getIncreaseLimit() == null : this.getIncreaseLimit().equals(other.getIncreaseLimit()))
            && (this.getIncreaseLow() == null ? other.getIncreaseLow() == null : this.getIncreaseLow().equals(other.getIncreaseLow()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessOrgId() == null) ? 0 : getBusinessOrgId().hashCode());
        result = prime * result + ((getBusinessName() == null) ? 0 : getBusinessName().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getIncreaseLimit() == null) ? 0 : getIncreaseLimit().hashCode());
        result = prime * result + ((getIncreaseLow() == null) ? 0 : getIncreaseLow().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessOrgId=").append(businessOrgId);
        sb.append(", businessName=").append(businessName);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", increaseLimit=").append(increaseLimit);
        sb.append(", increaseLow=").append(increaseLow);
        sb.append(", status=").append(status);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}