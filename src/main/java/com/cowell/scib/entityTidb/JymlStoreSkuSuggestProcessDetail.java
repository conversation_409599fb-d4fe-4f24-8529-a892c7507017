package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-一店一目经营建议确认进度表分类级别
 */
@Data
public class JymlStoreSkuSuggestProcessDetail implements Serializable {
    /**
     * 分布式主键
     */
    private Long id;

    /**
     * 连锁orgid
     */
    private Long businessOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 商品大类id(11_rx/或11_otc/12)
     */
    private String category;

    /**
     * 商品大类
     */
    private String categoryName;

    /**
     * 商品大类是rx/otc
     */
    private String rxOtc;

    /**
     * 商品中类id
     */
    private String middleCategory;

    /**
     * 商品中类
     */
    private String middleCategoryName;

    /**
     * 商品小类id
     */
    private String smallCategory;

    /**
     * 商品小类
     */
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    private String subCategory;

    /**
     * 商品子类
     */
    private String subCategoryName;

    /**
     * 经营商品数是否超sku上限(默认:0  0:未超 1:以超上限,感叹号)
     */
    private Integer upperLimit;

    /**
     * 分类sku配置数下限
     */
    private Integer skuLowerLimit;

    /**
     * SKU配置数上限
     */
    private Integer skuMaxLimit;

    /**
     * 当前分类上确认经营sku数量
     */
    private Integer skuCount;

    /**
     * 是否确认完成(默认:0  0:未确认 1:已确认)
     */
    private Integer confirmed;

    /**
     * 确认人ID
     */
    private Long confirmedBy;

    /**
     * 确认人
     */
    private String confirmedName;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;
}