package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 数字化商品bdp任务表
 */
public class DgmsBdpTask implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务类型 1:不经营商品
     */
    private Integer bizType;

    /**
     * 项目公司id
     */
    private Long companyOrgId;

    /**
     * businesId
     */
    private Long businesId;

    /**
     * 项目公司
     */
    private String companyName;

    /**
     * 门店orgID
     */
    private Long storeOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 子类id
     */
    private Long subCategory;

    /**
     * 必备标识(0非必备)
     */
    private Integer necessaryTag;

    /**
     * 必备标识名
     */
    private String necessaryTagName;

    /**
     * 经营状态
     */
    private Integer manageStatus;

    /**
     * 建议经营状态
     */
    private Integer suggestManageStatus;

    /**
     * 禁止请货
     */
    private String forbidApply;

    /**
     * 禁止配送
     */
    private String forbidDistr;

    /**
     * 禁止销售
     */
    private String forbidSale;

    /**
     * 禁止返仓
     */
    private String forbidReturnWarehouse;

    /**
     * 禁止调拨
     */
    private String forbidReturnAllot;

    /**
     * 是否敏感品
     */
    private String sensitive;

    /**
     * 销售属性
     */
    private String pushLevel;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 请货最大上线
     */
    private BigDecimal applyLimitUpper;

    /**
     * 生效状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtUpdate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public Long getBusinesId() {
        return businesId;
    }

    public void setBusinesId(Long businesId) {
        this.businesId = businesId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Long getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(Long subCategory) {
        this.subCategory = subCategory;
    }

    public Integer getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(Integer necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    public String getNecessaryTagName() {
        return necessaryTagName;
    }

    public void setNecessaryTagName(String necessaryTagName) {
        this.necessaryTagName = necessaryTagName;
    }

    public Integer getManageStatus() {
        return manageStatus;
    }

    public void setManageStatus(Integer manageStatus) {
        this.manageStatus = manageStatus;
    }

    public Integer getSuggestManageStatus() {
        return suggestManageStatus;
    }

    public void setSuggestManageStatus(Integer suggestManageStatus) {
        this.suggestManageStatus = suggestManageStatus;
    }

    public String getForbidApply() {
        return forbidApply;
    }

    public void setForbidApply(String forbidApply) {
        this.forbidApply = forbidApply;
    }

    public String getForbidDistr() {
        return forbidDistr;
    }

    public void setForbidDistr(String forbidDistr) {
        this.forbidDistr = forbidDistr;
    }

    public String getForbidSale() {
        return forbidSale;
    }

    public void setForbidSale(String forbidSale) {
        this.forbidSale = forbidSale;
    }

    public String getForbidReturnWarehouse() {
        return forbidReturnWarehouse;
    }

    public void setForbidReturnWarehouse(String forbidReturnWarehouse) {
        this.forbidReturnWarehouse = forbidReturnWarehouse;
    }

    public String getForbidReturnAllot() {
        return forbidReturnAllot;
    }

    public void setForbidReturnAllot(String forbidReturnAllot) {
        this.forbidReturnAllot = forbidReturnAllot;
    }

    public String getSensitive() {
        return sensitive;
    }

    public void setSensitive(String sensitive) {
        this.sensitive = sensitive;
    }

    public String getPushLevel() {
        return pushLevel;
    }

    public void setPushLevel(String pushLevel) {
        this.pushLevel = pushLevel;
    }

    public BigDecimal getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(BigDecimal minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public BigDecimal getApplyLimitUpper() {
        return applyLimitUpper;
    }

    public void setApplyLimitUpper(BigDecimal applyLimitUpper) {
        this.applyLimitUpper = applyLimitUpper;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DgmsBdpTask other = (DgmsBdpTask) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBizType() == null ? other.getBizType() == null : this.getBizType().equals(other.getBizType()))
            && (this.getCompanyOrgId() == null ? other.getCompanyOrgId() == null : this.getCompanyOrgId().equals(other.getCompanyOrgId()))
            && (this.getBusinesId() == null ? other.getBusinesId() == null : this.getBusinesId().equals(other.getBusinesId()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getStoreOrgId() == null ? other.getStoreOrgId() == null : this.getStoreOrgId().equals(other.getStoreOrgId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getSubCategory() == null ? other.getSubCategory() == null : this.getSubCategory().equals(other.getSubCategory()))
            && (this.getNecessaryTag() == null ? other.getNecessaryTag() == null : this.getNecessaryTag().equals(other.getNecessaryTag()))
            && (this.getNecessaryTagName() == null ? other.getNecessaryTagName() == null : this.getNecessaryTagName().equals(other.getNecessaryTagName()))
            && (this.getManageStatus() == null ? other.getManageStatus() == null : this.getManageStatus().equals(other.getManageStatus()))
            && (this.getSuggestManageStatus() == null ? other.getSuggestManageStatus() == null : this.getSuggestManageStatus().equals(other.getSuggestManageStatus()))
            && (this.getForbidApply() == null ? other.getForbidApply() == null : this.getForbidApply().equals(other.getForbidApply()))
            && (this.getForbidDistr() == null ? other.getForbidDistr() == null : this.getForbidDistr().equals(other.getForbidDistr()))
            && (this.getForbidSale() == null ? other.getForbidSale() == null : this.getForbidSale().equals(other.getForbidSale()))
            && (this.getForbidReturnWarehouse() == null ? other.getForbidReturnWarehouse() == null : this.getForbidReturnWarehouse().equals(other.getForbidReturnWarehouse()))
            && (this.getForbidReturnAllot() == null ? other.getForbidReturnAllot() == null : this.getForbidReturnAllot().equals(other.getForbidReturnAllot()))
            && (this.getSensitive() == null ? other.getSensitive() == null : this.getSensitive().equals(other.getSensitive()))
            && (this.getPushLevel() == null ? other.getPushLevel() == null : this.getPushLevel().equals(other.getPushLevel()))
            && (this.getMinDisplayQuantity() == null ? other.getMinDisplayQuantity() == null : this.getMinDisplayQuantity().equals(other.getMinDisplayQuantity()))
            && (this.getApplyLimitUpper() == null ? other.getApplyLimitUpper() == null : this.getApplyLimitUpper().equals(other.getApplyLimitUpper()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBizType() == null) ? 0 : getBizType().hashCode());
        result = prime * result + ((getCompanyOrgId() == null) ? 0 : getCompanyOrgId().hashCode());
        result = prime * result + ((getBusinesId() == null) ? 0 : getBusinesId().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getStoreOrgId() == null) ? 0 : getStoreOrgId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getSubCategory() == null) ? 0 : getSubCategory().hashCode());
        result = prime * result + ((getNecessaryTag() == null) ? 0 : getNecessaryTag().hashCode());
        result = prime * result + ((getNecessaryTagName() == null) ? 0 : getNecessaryTagName().hashCode());
        result = prime * result + ((getManageStatus() == null) ? 0 : getManageStatus().hashCode());
        result = prime * result + ((getSuggestManageStatus() == null) ? 0 : getSuggestManageStatus().hashCode());
        result = prime * result + ((getForbidApply() == null) ? 0 : getForbidApply().hashCode());
        result = prime * result + ((getForbidDistr() == null) ? 0 : getForbidDistr().hashCode());
        result = prime * result + ((getForbidSale() == null) ? 0 : getForbidSale().hashCode());
        result = prime * result + ((getForbidReturnWarehouse() == null) ? 0 : getForbidReturnWarehouse().hashCode());
        result = prime * result + ((getForbidReturnAllot() == null) ? 0 : getForbidReturnAllot().hashCode());
        result = prime * result + ((getSensitive() == null) ? 0 : getSensitive().hashCode());
        result = prime * result + ((getPushLevel() == null) ? 0 : getPushLevel().hashCode());
        result = prime * result + ((getMinDisplayQuantity() == null) ? 0 : getMinDisplayQuantity().hashCode());
        result = prime * result + ((getApplyLimitUpper() == null) ? 0 : getApplyLimitUpper().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bizType=").append(bizType);
        sb.append(", companyOrgId=").append(companyOrgId);
        sb.append(", businesId=").append(businesId);
        sb.append(", companyName=").append(companyName);
        sb.append(", storeOrgId=").append(storeOrgId);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeName=").append(storeName);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", subCategory=").append(subCategory);
        sb.append(", necessaryTag=").append(necessaryTag);
        sb.append(", necessaryTagName=").append(necessaryTagName);
        sb.append(", manageStatus=").append(manageStatus);
        sb.append(", suggestManageStatus=").append(suggestManageStatus);
        sb.append(", forbidApply=").append(forbidApply);
        sb.append(", forbidDistr=").append(forbidDistr);
        sb.append(", forbidSale=").append(forbidSale);
        sb.append(", forbidReturnWarehouse=").append(forbidReturnWarehouse);
        sb.append(", forbidReturnAllot=").append(forbidReturnAllot);
        sb.append(", sensitive=").append(sensitive);
        sb.append(", pushLevel=").append(pushLevel);
        sb.append(", minDisplayQuantity=").append(minDisplayQuantity);
        sb.append(", applyLimitUpper=").append(applyLimitUpper);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}