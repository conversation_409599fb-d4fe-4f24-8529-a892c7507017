package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class DgmsSingleStoreRecommendExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public DgmsSingleStoreRecommendExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRecommendDateIsNull() {
            addCriterion("recommend_date is null");
            return (Criteria) this;
        }

        public Criteria andRecommendDateIsNotNull() {
            addCriterion("recommend_date is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendDateEqualTo(Date value) {
            addCriterionForJDBCDate("recommend_date =", value, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("recommend_date <>", value, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateGreaterThan(Date value) {
            addCriterionForJDBCDate("recommend_date >", value, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("recommend_date >=", value, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateLessThan(Date value) {
            addCriterionForJDBCDate("recommend_date <", value, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("recommend_date <=", value, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateIn(List<Date> values) {
            addCriterionForJDBCDate("recommend_date in", values, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("recommend_date not in", values, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("recommend_date between", value1, value2, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andRecommendDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("recommend_date not between", value1, value2, "recommendDate");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleIsNull() {
            addCriterion("promotion_able is null");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleIsNotNull() {
            addCriterion("promotion_able is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleEqualTo(Byte value) {
            addCriterion("promotion_able =", value, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleNotEqualTo(Byte value) {
            addCriterion("promotion_able <>", value, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleGreaterThan(Byte value) {
            addCriterion("promotion_able >", value, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("promotion_able >=", value, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleLessThan(Byte value) {
            addCriterion("promotion_able <", value, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleLessThanOrEqualTo(Byte value) {
            addCriterion("promotion_able <=", value, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleIn(List<Byte> values) {
            addCriterion("promotion_able in", values, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleNotIn(List<Byte> values) {
            addCriterion("promotion_able not in", values, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleBetween(Byte value1, Byte value2) {
            addCriterion("promotion_able between", value1, value2, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("promotion_able not between", value1, value2, "promotionAble");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIsNull() {
            addCriterion("promotion_name is null");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIsNotNull() {
            addCriterion("promotion_name is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionNameEqualTo(String value) {
            addCriterion("promotion_name =", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotEqualTo(String value) {
            addCriterion("promotion_name <>", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameGreaterThan(String value) {
            addCriterion("promotion_name >", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_name >=", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLessThan(String value) {
            addCriterion("promotion_name <", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLessThanOrEqualTo(String value) {
            addCriterion("promotion_name <=", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLike(String value) {
            addCriterion("promotion_name like", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotLike(String value) {
            addCriterion("promotion_name not like", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIn(List<String> values) {
            addCriterion("promotion_name in", values, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotIn(List<String> values) {
            addCriterion("promotion_name not in", values, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameBetween(String value1, String value2) {
            addCriterion("promotion_name between", value1, value2, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotBetween(String value1, String value2) {
            addCriterion("promotion_name not between", value1, value2, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIsNull() {
            addCriterion("promotion_way is null");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIsNotNull() {
            addCriterion("promotion_way is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionWayEqualTo(String value) {
            addCriterion("promotion_way =", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotEqualTo(String value) {
            addCriterion("promotion_way <>", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayGreaterThan(String value) {
            addCriterion("promotion_way >", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_way >=", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLessThan(String value) {
            addCriterion("promotion_way <", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLessThanOrEqualTo(String value) {
            addCriterion("promotion_way <=", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLike(String value) {
            addCriterion("promotion_way like", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotLike(String value) {
            addCriterion("promotion_way not like", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIn(List<String> values) {
            addCriterion("promotion_way in", values, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotIn(List<String> values) {
            addCriterion("promotion_way not in", values, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayBetween(String value1, String value2) {
            addCriterion("promotion_way between", value1, value2, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotBetween(String value1, String value2) {
            addCriterion("promotion_way not between", value1, value2, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIsNull() {
            addCriterion("threshold_info is null");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIsNotNull() {
            addCriterion("threshold_info is not null");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoEqualTo(String value) {
            addCriterion("threshold_info =", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotEqualTo(String value) {
            addCriterion("threshold_info <>", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoGreaterThan(String value) {
            addCriterion("threshold_info >", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoGreaterThanOrEqualTo(String value) {
            addCriterion("threshold_info >=", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLessThan(String value) {
            addCriterion("threshold_info <", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLessThanOrEqualTo(String value) {
            addCriterion("threshold_info <=", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLike(String value) {
            addCriterion("threshold_info like", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotLike(String value) {
            addCriterion("threshold_info not like", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIn(List<String> values) {
            addCriterion("threshold_info in", values, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotIn(List<String> values) {
            addCriterion("threshold_info not in", values, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoBetween(String value1, String value2) {
            addCriterion("threshold_info between", value1, value2, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotBetween(String value1, String value2) {
            addCriterion("threshold_info not between", value1, value2, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoIsNull() {
            addCriterion("fav_info is null");
            return (Criteria) this;
        }

        public Criteria andFavInfoIsNotNull() {
            addCriterion("fav_info is not null");
            return (Criteria) this;
        }

        public Criteria andFavInfoEqualTo(String value) {
            addCriterion("fav_info =", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotEqualTo(String value) {
            addCriterion("fav_info <>", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoGreaterThan(String value) {
            addCriterion("fav_info >", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoGreaterThanOrEqualTo(String value) {
            addCriterion("fav_info >=", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLessThan(String value) {
            addCriterion("fav_info <", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLessThanOrEqualTo(String value) {
            addCriterion("fav_info <=", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLike(String value) {
            addCriterion("fav_info like", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotLike(String value) {
            addCriterion("fav_info not like", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoIn(List<String> values) {
            addCriterion("fav_info in", values, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotIn(List<String> values) {
            addCriterion("fav_info not in", values, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoBetween(String value1, String value2) {
            addCriterion("fav_info between", value1, value2, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotBetween(String value1, String value2) {
            addCriterion("fav_info not between", value1, value2, "favInfo");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}