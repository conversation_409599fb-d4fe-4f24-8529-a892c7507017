package com.cowell.scib.entityTidb;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.converters.string.StringNumberConverter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import java.io.Serializable;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR> 企业店型级复盘表
 */
public class TrackRetultLevelReview implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 任务ID
     */
    @ExcelIgnore
    private Long taskId;

    /**
     * 平台
     */
    @ExcelProperty({"平台","平台"})
    private String zoneNew;

    /**
     * 企业
     */
    @ExcelProperty({"项目公司","项目公司"})
    private String chainName;

    /**
     * 城市
     */
    @ExcelProperty({"城市","城市"})
    private String city;

    /**
     * 店型组
     */
    @ExcelProperty({"店型组","店型组"})
    private String reviseStoreGroup;

    /**
     * 品类
     */
    @ExcelProperty({"品类","品类"})
    private String classoneName;

    /**
     * 门店数
     */
    @ExcelProperty(value = {"门店数","门店数"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String orgCntV3;

    /**
     * 商品数总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCnt;

    /**
     * 商品集团必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntJt;

    /**
     * 商品平台必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntPt;

    /**
     * 商品企业必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntQy;

    /**
     * 商品店型必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntDxbb;

    /**
     * 商品部分店必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntDxxp;

    /**
     * 商品单店必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntDd;

    /**
     * 商品集团必备+企业必备+店型必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntJtQyDx;

    /**
     * 商品集团必备+平台必备企业必备+店型必备+部分店必备总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntNoDd;

    /**
     * 商品新增必备条目数（原来不是必备，现在是必备）总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","新增必备条目数（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntXz;

    /**
     * 商品必备减少条目数（原来是必备，现在不是必备）总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","必备减少条目数（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String skuCntJs;

    /**
     * 商品必备SKU占比（前四类必备/当前正常经营且在营）总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","必备SKU占比（前四类必备/当前正常经营且在营）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String skuRateTop4;

    /**
     * 商品必备选配SKU占比（前五类必备/当前正常经营且在营）总计
     */
    @ExcelProperty(value = {"商品数（每个层级都取SKU去重后的个数，不取平均值）","必备选配SKU占比（前五类必备/当前正常经营且在营）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String skuRateNoDd;

    /**
     * 成分数总计
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCnt;

    /**
     * 成分数集团必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntJt;

    /**
     * 成分数平台必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntPt;

    /**
     * 成分数企业必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntQy;

    /**
     * 成分数店型必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntDxbb;

    /**
     * 成分数部分店必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntDxxp;

    /**
     * 成分数单店必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntDd;

    /**
     * 成分数集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntJtQyDx;

    /**
     * 成分数集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntNoDd;

    /**
     * 成分数新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntXz;

    /**
     * 成分数减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String compCntJs;

    /**
     * 成分数 必备成分数占比
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","必备成分数占比"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String compRateTop4;

    /**
     * 成分数 必备+选配成分数占比
     */
    @ExcelProperty(value = {"成分数（中西成药+中药参茸）","必备+选配成分数占比"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String compRateNoDd;

    /**
     * 子类总计（中西成药+中药参茸）
     */
    @ExcelProperty(value = {"子类（保健+器械）","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCnt;

    /**
     * 子类集团必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntJt;

    /**
     * 子类平台必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntPt;

    /**
     * 子类企业必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntQy;

    /**
     * 子类店型必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntDxbb;

    /**
     * 子类部分店必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntDxxp;

    /**
     * 子类单店必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntDd;

    /**
     * 子类集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntJtQyDx;

    /**
     * 子类集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"子类（保健+器械）","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntNoDd;

    /**
     * 子类新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"子类（保健+器械）","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntXz;

    /**
     * 子类减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"子类（保健+器械）","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String classCntJs;

    /**
     * 销售额总计
     */
    @ExcelProperty(value = {"销售额","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30;

    /**
     * 销售额集团必备
     */
    @ExcelProperty(value = {"销售额","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Jt;

    /**
     * 销售额平台必备
     */
    @ExcelProperty(value = {"销售额","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Pt;

    /**
     * 销售额企业必备
     */
    @ExcelProperty(value = {"销售额","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Qy;

    /**
     * 销售额店型必备
     */
    @ExcelProperty(value = {"销售额","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Dxbb;

    /**
     * 销售额部分店必备
     */
    @ExcelProperty(value = {"销售额","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Dxxp;

    /**
     * 销售额单店必备
     */
    @ExcelProperty(value = {"销售额","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Dd;

    /**
     * 销售额集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"销售额","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30JtQyDx;

    /**
     * 销售额集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"销售额","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30NoDd;

    /**
     * 销售额新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"销售额","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Xz;

    /**
     * 销售额减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"销售额","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Js;

    /**
     * 毛利额总计
     */
    @ExcelProperty(value = {"毛利额","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30;

    /**
     * 毛利额集团必备
     */
    @ExcelProperty(value = {"毛利额","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Jt;

    /**
     * 毛利额平台必备
     */
    @ExcelProperty(value = {"毛利额","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Pt;

    /**
     * 毛利额企业必备
     */
    @ExcelProperty(value = {"毛利额","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Qy;

    /**
     * 毛利额店型必备
     */
    @ExcelProperty(value = {"毛利额","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Dxbb;

    /**
     * 毛利额部分店必备
     */
    @ExcelProperty(value = {"毛利额","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Dxxp;

    /**
     * 毛利额单店必备
     */
    @ExcelProperty(value = {"毛利额","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Dd;

    /**
     * 毛利额集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"毛利额","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30JtQyDx;

    /**
     * 毛利额集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"毛利额","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30NoDd;

    /**
     * 毛利额新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"毛利额","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Xz;

    /**
     * 毛利额减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"毛利额","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Js;

    /**
     * 毛利率总计
     */
    @ExcelProperty(value = {"毛利率","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30Rate;

    /**
     * 毛利率集团必备
     */
    @ExcelProperty(value = {"毛利率","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateJt;

    /**
     * 毛利率平台必备
     */
    @ExcelProperty(value = {"毛利率","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RatePt;

    /**
     * 毛利率企业必备
     */
    @ExcelProperty(value = {"毛利率","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateQy;

    /**
     * 毛利率店型必备
     */
    @ExcelProperty(value = {"毛利率","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateDxbb;

    /**
     * 毛利率部分店必备
     */
    @ExcelProperty(value = {"毛利率","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateDxxp;

    /**
     * 毛利率单店必备
     */
    @ExcelProperty(value = {"毛利率","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateDd;

    /**
     * 毛利率集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"毛利率","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateJtQyDx;

    /**
     * 毛利率集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"毛利率","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateNoDd;

    /**
     * 毛利率新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"毛利率","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateXz;

    /**
     * 毛利率减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"毛利率","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateJs;

    /**
     * 销售额(全量销售，不排非正常经营和品类）
     */
    @ExcelProperty(value = {"销售额(全量销售，不排非正常经营和品类）","销售额(全量销售，不排非正常经营和品类）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Month;

    /**
     * 销售额(全量销售剔DTP，不排非正常经营和品类）
     */
    @ExcelProperty(value = {"销售额(全量销售剔DTP，不排非正常经营和品类）","销售额(全量销售剔DTP，不排非正常经营和品类）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30MonthNodtp;

    /**
     * 销售额(全量销售剔DTP&中药饮片，不排非正常经营和品类
     */
    @ExcelProperty(value = {"销售额(全量销售剔DTP&中药饮片，不排非正常经营和品类)","销售额(全量销售剔DTP&中药饮片，不排非正常经营和品类)"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30MonthNozy;

    /**
     * 销售占比(分母为全量销售）总计
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Rate;

    /**
     * 销售占比集团必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJt;

    /**
     * 销售占比平台必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RatePt;

    /**
     * 销售占比企业必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateQt;

    /**
     * 销售占比店型必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDxbb;

    /**
     * 销售占比部分店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDxxp;

    /**
     * 销售占比单店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDd;

    /**
     * 销售占比集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJtQyDx;

    /**
     * 销售占比集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNoDd;

    /**
     * 销售占比新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateXz;

    /**
     * 销售占比减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售）","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJs;

    /**
     * 销售占比(分母为全量销售只剔除DTP)总计
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNodtp;

    /**
     * 销售占比集团必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJtNodtp;

    /**
     * 销售占比平台必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RatePtNodtp;

    /**
     * 销售占比企业必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateQtNodtp;

    /**
     * 销售占比店型必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDxbbNodtp;

    /**
     * 销售占比部分店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDxxpNodtp;

    /**
     * 销售占比单店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDdNodtp;

    /**
     * 销售占比集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJtQyDxNodtp;

    /**
     * 销售占比集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNoDdNodtp;

    /**
     * 销售占比新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateXzNodtp;

    /**
     * 销售占比减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售只剔除DTP)","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJsNodtp;

    /**
     * 销售占比(分母为全量销售剔DTP&中药饮片)总计
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNozy;

    /**
     * 销售占比集团必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","集团必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJtNozy;

    /**
     * 销售占比平台必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RatePtNozy;

    /**
     * 销售占比企业必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateQtNozy;

    /**
     * 销售占比店型必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDxbbNozy;

    /**
     * 销售占比部分店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDxxpNozy;

    /**
     * 销售占比单店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateDdNozy;

    /**
     * 销售占比集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJtQyDxNozy;

    /**
     * 销售占比集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNoDdNozy;

    /**
     * 销售占比新增（原来不是必备，现在是必备）
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","新增（原来不是必备，现在是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateXzNozy;

    /**
     * 销售占比减少（原来是必备，现在不是必备）
     */
    @ExcelProperty(value = {"销售占比(分母为全量销售剔DTP&中药饮片)","减少（原来是必备，现在不是必备）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateJsNozy;

    /**
     * 库存额总计
     */
    @ExcelProperty(value = {"库存额","总计"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30;

    /**
     * 库存额集团必备（当前门店实际库存金额之和）
     */
    @ExcelProperty(value = {"库存额","集团必备（当前门店实际库存金额之和）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Jt;

    /**
     * 库存额平台必备
     */
    @ExcelProperty(value = {"库存额","平台必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Pt;

    /**
     * 库存额企业必备
     */
    @ExcelProperty(value = {"库存额","企业必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Qy;

    /**
     * 库存额店型必备
     */
    @ExcelProperty(value = {"库存额","店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Dxbb;

    /**
     * 库存额部分店必备
     */
    @ExcelProperty(value = {"库存额","部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Dxxp;

    /**
     * 库存额单店必备
     */
    @ExcelProperty(value = {"库存额","单店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Dd;

    /**
     * 库存额 集团必备+企业必备+店型必备
     */
    @ExcelProperty(value = {"库存额","集团必备+企业必备+店型必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30JtQyDx;

    /**
     * 库存额 集团必备+平台必备企业必备+店型必备+部分店必备
     */
    @ExcelProperty(value = {"库存额","集团必备+平台必备企业必备+店型必备+部分店必备"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30NoDd;

    /**
     * 库存额 新增库存额（一店一目级纯新增，1盒*配送仓价格）
     */
    @ExcelProperty(value = {"库存额","新增库存额（一店一目级纯新增，1盒*配送仓价格）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30New;

    /**
     * 库存额 减少总库存（原来是必备，现在不是必备。1盒*配送仓价格）
     */
    @ExcelProperty(value = {"库存额","减少总库存（原来是必备，现在不是必备。1盒*配送仓价格）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String stockCum30Js;

    /**
     * 店均库存额预估、店均新增库存额（DS/D列）
     */
    @ExcelProperty(value = {"店均库存额预估（元）","店均新增库存额（DS/D列）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgStockCum30New;

    /**
     * 店均库存额预估、店均减少库存额（DT/D列）
     */
    @ExcelProperty(value = {"店均库存额预估（元）","店均减少库存额（DT/D列"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgStockCum30Js;

    /**
     * 店均SKU、店均新增SKU数（N/D列）
     */
    @ExcelProperty(value = {"店均SKU","店均新增SKU数（N/D列）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntXz;

    /**
     * 店均SKU、店均减少SKU数（O/D列）
     */
    @ExcelProperty(value = {"店均SKU","店均减少SKU数（O/D列）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntJs;

    /**
     * 纯新增品未来30天预估销售（新成分0.15+旧成分*0.115） 店均净增SKU
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","店均净增SKU"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgCnt;

    /**
     * 纯新增品未来30天预估销售 无法预估销售的门店*SKU占比
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","无法预估销售的门店*SKU占比"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Null;

    /**
     * 纯新增品未来30天预估销售 无法预估销售的店均SKU
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","无法预估销售的店均SKU"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgAmtCum30Null;

    /**
     * 纯新增品未来30天预估销售 新增品预估销售额
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品预估销售额"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30New;

    /**
     * 纯新增品未来30天预估销售 新增品店均销售额
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品店均销售额"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String avgAmtCum30New;

    /**
     * 纯新增品未来30天预估销售 新增品毛利额
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品毛利额"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30New;

    /**
     * 纯新增品未来30天预估销售 新增品毛利率
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品毛利率"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateNew;

    /**
     * 纯新增品未来30天预估销售 新增品销售占比（全量销售）
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品销售占比（全量销售）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNew;

    /**
     * 纯新增品未来30天预估销售 新增品销售占比（全量销售-DTP）
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品销售占比（全量销售-DTP）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNewNodtp;

    /**
     * 纯新增品未来30天预估销售 新增品销售占比（全量销售-DTP-配方饮片）
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.15+旧成分*0.115）","新增品销售占比（全量销售-DTP-配方饮片）"},converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNewNozy;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date gmtCreate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getZoneNew() {
        return zoneNew;
    }

    public void setZoneNew(String zoneNew) {
        this.zoneNew = zoneNew;
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getReviseStoreGroup() {
        return reviseStoreGroup;
    }

    public void setReviseStoreGroup(String reviseStoreGroup) {
        this.reviseStoreGroup = reviseStoreGroup;
    }

    public String getClassoneName() {
        return classoneName;
    }

    public void setClassoneName(String classoneName) {
        this.classoneName = classoneName;
    }

    public String getOrgCntV3() {
        return orgCntV3;
    }

    public void setOrgCntV3(String orgCntV3) {
        this.orgCntV3 = orgCntV3;
    }

    public String getSkuCnt() {
        return skuCnt;
    }

    public void setSkuCnt(String skuCnt) {
        this.skuCnt = skuCnt;
    }

    public String getSkuCntJt() {
        return skuCntJt;
    }

    public void setSkuCntJt(String skuCntJt) {
        this.skuCntJt = skuCntJt;
    }

    public String getSkuCntPt() {
        return skuCntPt;
    }

    public void setSkuCntPt(String skuCntPt) {
        this.skuCntPt = skuCntPt;
    }

    public String getSkuCntQy() {
        return skuCntQy;
    }

    public void setSkuCntQy(String skuCntQy) {
        this.skuCntQy = skuCntQy;
    }

    public String getSkuCntDxbb() {
        return skuCntDxbb;
    }

    public void setSkuCntDxbb(String skuCntDxbb) {
        this.skuCntDxbb = skuCntDxbb;
    }

    public String getSkuCntDxxp() {
        return skuCntDxxp;
    }

    public void setSkuCntDxxp(String skuCntDxxp) {
        this.skuCntDxxp = skuCntDxxp;
    }

    public String getSkuCntDd() {
        return skuCntDd;
    }

    public void setSkuCntDd(String skuCntDd) {
        this.skuCntDd = skuCntDd;
    }

    public String getSkuCntJtQyDx() {
        return skuCntJtQyDx;
    }

    public void setSkuCntJtQyDx(String skuCntJtQyDx) {
        this.skuCntJtQyDx = skuCntJtQyDx;
    }

    public String getSkuCntNoDd() {
        return skuCntNoDd;
    }

    public void setSkuCntNoDd(String skuCntNoDd) {
        this.skuCntNoDd = skuCntNoDd;
    }

    public String getSkuCntXz() {
        return skuCntXz;
    }

    public void setSkuCntXz(String skuCntXz) {
        this.skuCntXz = skuCntXz;
    }

    public String getSkuCntJs() {
        return skuCntJs;
    }

    public void setSkuCntJs(String skuCntJs) {
        this.skuCntJs = skuCntJs;
    }

    public String getSkuRateTop4() {
        return skuRateTop4;
    }

    public void setSkuRateTop4(String skuRateTop4) {
        this.skuRateTop4 = skuRateTop4;
    }

    public String getSkuRateNoDd() {
        return skuRateNoDd;
    }

    public void setSkuRateNoDd(String skuRateNoDd) {
        this.skuRateNoDd = skuRateNoDd;
    }

    public String getCompCnt() {
        return compCnt;
    }

    public void setCompCnt(String compCnt) {
        this.compCnt = compCnt;
    }

    public String getCompCntJt() {
        return compCntJt;
    }

    public void setCompCntJt(String compCntJt) {
        this.compCntJt = compCntJt;
    }

    public String getCompCntPt() {
        return compCntPt;
    }

    public void setCompCntPt(String compCntPt) {
        this.compCntPt = compCntPt;
    }

    public String getCompCntQy() {
        return compCntQy;
    }

    public void setCompCntQy(String compCntQy) {
        this.compCntQy = compCntQy;
    }

    public String getCompCntDxbb() {
        return compCntDxbb;
    }

    public void setCompCntDxbb(String compCntDxbb) {
        this.compCntDxbb = compCntDxbb;
    }

    public String getCompCntDxxp() {
        return compCntDxxp;
    }

    public void setCompCntDxxp(String compCntDxxp) {
        this.compCntDxxp = compCntDxxp;
    }

    public String getCompCntDd() {
        return compCntDd;
    }

    public void setCompCntDd(String compCntDd) {
        this.compCntDd = compCntDd;
    }

    public String getCompCntJtQyDx() {
        return compCntJtQyDx;
    }

    public void setCompCntJtQyDx(String compCntJtQyDx) {
        this.compCntJtQyDx = compCntJtQyDx;
    }

    public String getCompCntNoDd() {
        return compCntNoDd;
    }

    public void setCompCntNoDd(String compCntNoDd) {
        this.compCntNoDd = compCntNoDd;
    }

    public String getCompCntXz() {
        return compCntXz;
    }

    public void setCompCntXz(String compCntXz) {
        this.compCntXz = compCntXz;
    }

    public String getCompCntJs() {
        return compCntJs;
    }

    public void setCompCntJs(String compCntJs) {
        this.compCntJs = compCntJs;
    }

    public String getCompRateTop4() {
        return compRateTop4;
    }

    public void setCompRateTop4(String compRateTop4) {
        this.compRateTop4 = compRateTop4;
    }

    public String getCompRateNoDd() {
        return compRateNoDd;
    }

    public void setCompRateNoDd(String compRateNoDd) {
        this.compRateNoDd = compRateNoDd;
    }

    public String getClassCnt() {
        return classCnt;
    }

    public void setClassCnt(String classCnt) {
        this.classCnt = classCnt;
    }

    public String getClassCntJt() {
        return classCntJt;
    }

    public void setClassCntJt(String classCntJt) {
        this.classCntJt = classCntJt;
    }

    public String getClassCntPt() {
        return classCntPt;
    }

    public void setClassCntPt(String classCntPt) {
        this.classCntPt = classCntPt;
    }

    public String getClassCntQy() {
        return classCntQy;
    }

    public void setClassCntQy(String classCntQy) {
        this.classCntQy = classCntQy;
    }

    public String getClassCntDxbb() {
        return classCntDxbb;
    }

    public void setClassCntDxbb(String classCntDxbb) {
        this.classCntDxbb = classCntDxbb;
    }

    public String getClassCntDxxp() {
        return classCntDxxp;
    }

    public void setClassCntDxxp(String classCntDxxp) {
        this.classCntDxxp = classCntDxxp;
    }

    public String getClassCntDd() {
        return classCntDd;
    }

    public void setClassCntDd(String classCntDd) {
        this.classCntDd = classCntDd;
    }

    public String getClassCntJtQyDx() {
        return classCntJtQyDx;
    }

    public void setClassCntJtQyDx(String classCntJtQyDx) {
        this.classCntJtQyDx = classCntJtQyDx;
    }

    public String getClassCntNoDd() {
        return classCntNoDd;
    }

    public void setClassCntNoDd(String classCntNoDd) {
        this.classCntNoDd = classCntNoDd;
    }

    public String getClassCntXz() {
        return classCntXz;
    }

    public void setClassCntXz(String classCntXz) {
        this.classCntXz = classCntXz;
    }

    public String getClassCntJs() {
        return classCntJs;
    }

    public void setClassCntJs(String classCntJs) {
        this.classCntJs = classCntJs;
    }

    public String getAmtCum30() {
        return amtCum30;
    }

    public void setAmtCum30(String amtCum30) {
        this.amtCum30 = amtCum30;
    }

    public String getAmtCum30Jt() {
        return amtCum30Jt;
    }

    public void setAmtCum30Jt(String amtCum30Jt) {
        this.amtCum30Jt = amtCum30Jt;
    }

    public String getAmtCum30Pt() {
        return amtCum30Pt;
    }

    public void setAmtCum30Pt(String amtCum30Pt) {
        this.amtCum30Pt = amtCum30Pt;
    }

    public String getAmtCum30Qy() {
        return amtCum30Qy;
    }

    public void setAmtCum30Qy(String amtCum30Qy) {
        this.amtCum30Qy = amtCum30Qy;
    }

    public String getAmtCum30Dxbb() {
        return amtCum30Dxbb;
    }

    public void setAmtCum30Dxbb(String amtCum30Dxbb) {
        this.amtCum30Dxbb = amtCum30Dxbb;
    }

    public String getAmtCum30Dxxp() {
        return amtCum30Dxxp;
    }

    public void setAmtCum30Dxxp(String amtCum30Dxxp) {
        this.amtCum30Dxxp = amtCum30Dxxp;
    }

    public String getAmtCum30Dd() {
        return amtCum30Dd;
    }

    public void setAmtCum30Dd(String amtCum30Dd) {
        this.amtCum30Dd = amtCum30Dd;
    }

    public String getAmtCum30JtQyDx() {
        return amtCum30JtQyDx;
    }

    public void setAmtCum30JtQyDx(String amtCum30JtQyDx) {
        this.amtCum30JtQyDx = amtCum30JtQyDx;
    }

    public String getAmtCum30NoDd() {
        return amtCum30NoDd;
    }

    public void setAmtCum30NoDd(String amtCum30NoDd) {
        this.amtCum30NoDd = amtCum30NoDd;
    }

    public String getAmtCum30Xz() {
        return amtCum30Xz;
    }

    public void setAmtCum30Xz(String amtCum30Xz) {
        this.amtCum30Xz = amtCum30Xz;
    }

    public String getAmtCum30Js() {
        return amtCum30Js;
    }

    public void setAmtCum30Js(String amtCum30Js) {
        this.amtCum30Js = amtCum30Js;
    }

    public String getProfitCum30() {
        return profitCum30;
    }

    public void setProfitCum30(String profitCum30) {
        this.profitCum30 = profitCum30;
    }

    public String getProfitCum30Jt() {
        return profitCum30Jt;
    }

    public void setProfitCum30Jt(String profitCum30Jt) {
        this.profitCum30Jt = profitCum30Jt;
    }

    public String getProfitCum30Pt() {
        return profitCum30Pt;
    }

    public void setProfitCum30Pt(String profitCum30Pt) {
        this.profitCum30Pt = profitCum30Pt;
    }

    public String getProfitCum30Qy() {
        return profitCum30Qy;
    }

    public void setProfitCum30Qy(String profitCum30Qy) {
        this.profitCum30Qy = profitCum30Qy;
    }

    public String getProfitCum30Dxbb() {
        return profitCum30Dxbb;
    }

    public void setProfitCum30Dxbb(String profitCum30Dxbb) {
        this.profitCum30Dxbb = profitCum30Dxbb;
    }

    public String getProfitCum30Dxxp() {
        return profitCum30Dxxp;
    }

    public void setProfitCum30Dxxp(String profitCum30Dxxp) {
        this.profitCum30Dxxp = profitCum30Dxxp;
    }

    public String getProfitCum30Dd() {
        return profitCum30Dd;
    }

    public void setProfitCum30Dd(String profitCum30Dd) {
        this.profitCum30Dd = profitCum30Dd;
    }

    public String getProfitCum30JtQyDx() {
        return profitCum30JtQyDx;
    }

    public void setProfitCum30JtQyDx(String profitCum30JtQyDx) {
        this.profitCum30JtQyDx = profitCum30JtQyDx;
    }

    public String getProfitCum30NoDd() {
        return profitCum30NoDd;
    }

    public void setProfitCum30NoDd(String profitCum30NoDd) {
        this.profitCum30NoDd = profitCum30NoDd;
    }

    public String getProfitCum30Xz() {
        return profitCum30Xz;
    }

    public void setProfitCum30Xz(String profitCum30Xz) {
        this.profitCum30Xz = profitCum30Xz;
    }

    public String getProfitCum30Js() {
        return profitCum30Js;
    }

    public void setProfitCum30Js(String profitCum30Js) {
        this.profitCum30Js = profitCum30Js;
    }

    public String getProfitCum30Rate() {
        return profitCum30Rate;
    }

    public void setProfitCum30Rate(String profitCum30Rate) {
        this.profitCum30Rate = profitCum30Rate;
    }

    public String getProfitCum30RateJt() {
        return profitCum30RateJt;
    }

    public void setProfitCum30RateJt(String profitCum30RateJt) {
        this.profitCum30RateJt = profitCum30RateJt;
    }

    public String getProfitCum30RatePt() {
        return profitCum30RatePt;
    }

    public void setProfitCum30RatePt(String profitCum30RatePt) {
        this.profitCum30RatePt = profitCum30RatePt;
    }

    public String getProfitCum30RateQy() {
        return profitCum30RateQy;
    }

    public void setProfitCum30RateQy(String profitCum30RateQy) {
        this.profitCum30RateQy = profitCum30RateQy;
    }

    public String getProfitCum30RateDxbb() {
        return profitCum30RateDxbb;
    }

    public void setProfitCum30RateDxbb(String profitCum30RateDxbb) {
        this.profitCum30RateDxbb = profitCum30RateDxbb;
    }

    public String getProfitCum30RateDxxp() {
        return profitCum30RateDxxp;
    }

    public void setProfitCum30RateDxxp(String profitCum30RateDxxp) {
        this.profitCum30RateDxxp = profitCum30RateDxxp;
    }

    public String getProfitCum30RateDd() {
        return profitCum30RateDd;
    }

    public void setProfitCum30RateDd(String profitCum30RateDd) {
        this.profitCum30RateDd = profitCum30RateDd;
    }

    public String getProfitCum30RateJtQyDx() {
        return profitCum30RateJtQyDx;
    }

    public void setProfitCum30RateJtQyDx(String profitCum30RateJtQyDx) {
        this.profitCum30RateJtQyDx = profitCum30RateJtQyDx;
    }

    public String getProfitCum30RateNoDd() {
        return profitCum30RateNoDd;
    }

    public void setProfitCum30RateNoDd(String profitCum30RateNoDd) {
        this.profitCum30RateNoDd = profitCum30RateNoDd;
    }

    public String getProfitCum30RateXz() {
        return profitCum30RateXz;
    }

    public void setProfitCum30RateXz(String profitCum30RateXz) {
        this.profitCum30RateXz = profitCum30RateXz;
    }

    public String getProfitCum30RateJs() {
        return profitCum30RateJs;
    }

    public void setProfitCum30RateJs(String profitCum30RateJs) {
        this.profitCum30RateJs = profitCum30RateJs;
    }

    public String getAmtCum30Month() {
        return amtCum30Month;
    }

    public void setAmtCum30Month(String amtCum30Month) {
        this.amtCum30Month = amtCum30Month;
    }

    public String getAmtCum30MonthNodtp() {
        return amtCum30MonthNodtp;
    }

    public void setAmtCum30MonthNodtp(String amtCum30MonthNodtp) {
        this.amtCum30MonthNodtp = amtCum30MonthNodtp;
    }

    public String getAmtCum30MonthNozy() {
        return amtCum30MonthNozy;
    }

    public void setAmtCum30MonthNozy(String amtCum30MonthNozy) {
        this.amtCum30MonthNozy = amtCum30MonthNozy;
    }

    public String getAmtCum30Rate() {
        return amtCum30Rate;
    }

    public void setAmtCum30Rate(String amtCum30Rate) {
        this.amtCum30Rate = amtCum30Rate;
    }

    public String getAmtCum30RateJt() {
        return amtCum30RateJt;
    }

    public void setAmtCum30RateJt(String amtCum30RateJt) {
        this.amtCum30RateJt = amtCum30RateJt;
    }

    public String getAmtCum30RatePt() {
        return amtCum30RatePt;
    }

    public void setAmtCum30RatePt(String amtCum30RatePt) {
        this.amtCum30RatePt = amtCum30RatePt;
    }

    public String getAmtCum30RateQt() {
        return amtCum30RateQt;
    }

    public void setAmtCum30RateQt(String amtCum30RateQt) {
        this.amtCum30RateQt = amtCum30RateQt;
    }

    public String getAmtCum30RateDxbb() {
        return amtCum30RateDxbb;
    }

    public void setAmtCum30RateDxbb(String amtCum30RateDxbb) {
        this.amtCum30RateDxbb = amtCum30RateDxbb;
    }

    public String getAmtCum30RateDxxp() {
        return amtCum30RateDxxp;
    }

    public void setAmtCum30RateDxxp(String amtCum30RateDxxp) {
        this.amtCum30RateDxxp = amtCum30RateDxxp;
    }

    public String getAmtCum30RateDd() {
        return amtCum30RateDd;
    }

    public void setAmtCum30RateDd(String amtCum30RateDd) {
        this.amtCum30RateDd = amtCum30RateDd;
    }

    public String getAmtCum30RateJtQyDx() {
        return amtCum30RateJtQyDx;
    }

    public void setAmtCum30RateJtQyDx(String amtCum30RateJtQyDx) {
        this.amtCum30RateJtQyDx = amtCum30RateJtQyDx;
    }

    public String getAmtCum30RateNoDd() {
        return amtCum30RateNoDd;
    }

    public void setAmtCum30RateNoDd(String amtCum30RateNoDd) {
        this.amtCum30RateNoDd = amtCum30RateNoDd;
    }

    public String getAmtCum30RateXz() {
        return amtCum30RateXz;
    }

    public void setAmtCum30RateXz(String amtCum30RateXz) {
        this.amtCum30RateXz = amtCum30RateXz;
    }

    public String getAmtCum30RateJs() {
        return amtCum30RateJs;
    }

    public void setAmtCum30RateJs(String amtCum30RateJs) {
        this.amtCum30RateJs = amtCum30RateJs;
    }

    public String getAmtCum30RateNodtp() {
        return amtCum30RateNodtp;
    }

    public void setAmtCum30RateNodtp(String amtCum30RateNodtp) {
        this.amtCum30RateNodtp = amtCum30RateNodtp;
    }

    public String getAmtCum30RateJtNodtp() {
        return amtCum30RateJtNodtp;
    }

    public void setAmtCum30RateJtNodtp(String amtCum30RateJtNodtp) {
        this.amtCum30RateJtNodtp = amtCum30RateJtNodtp;
    }

    public String getAmtCum30RatePtNodtp() {
        return amtCum30RatePtNodtp;
    }

    public void setAmtCum30RatePtNodtp(String amtCum30RatePtNodtp) {
        this.amtCum30RatePtNodtp = amtCum30RatePtNodtp;
    }

    public String getAmtCum30RateQtNodtp() {
        return amtCum30RateQtNodtp;
    }

    public void setAmtCum30RateQtNodtp(String amtCum30RateQtNodtp) {
        this.amtCum30RateQtNodtp = amtCum30RateQtNodtp;
    }

    public String getAmtCum30RateDxbbNodtp() {
        return amtCum30RateDxbbNodtp;
    }

    public void setAmtCum30RateDxbbNodtp(String amtCum30RateDxbbNodtp) {
        this.amtCum30RateDxbbNodtp = amtCum30RateDxbbNodtp;
    }

    public String getAmtCum30RateDxxpNodtp() {
        return amtCum30RateDxxpNodtp;
    }

    public void setAmtCum30RateDxxpNodtp(String amtCum30RateDxxpNodtp) {
        this.amtCum30RateDxxpNodtp = amtCum30RateDxxpNodtp;
    }

    public String getAmtCum30RateDdNodtp() {
        return amtCum30RateDdNodtp;
    }

    public void setAmtCum30RateDdNodtp(String amtCum30RateDdNodtp) {
        this.amtCum30RateDdNodtp = amtCum30RateDdNodtp;
    }

    public String getAmtCum30RateJtQyDxNodtp() {
        return amtCum30RateJtQyDxNodtp;
    }

    public void setAmtCum30RateJtQyDxNodtp(String amtCum30RateJtQyDxNodtp) {
        this.amtCum30RateJtQyDxNodtp = amtCum30RateJtQyDxNodtp;
    }

    public String getAmtCum30RateNoDdNodtp() {
        return amtCum30RateNoDdNodtp;
    }

    public void setAmtCum30RateNoDdNodtp(String amtCum30RateNoDdNodtp) {
        this.amtCum30RateNoDdNodtp = amtCum30RateNoDdNodtp;
    }

    public String getAmtCum30RateXzNodtp() {
        return amtCum30RateXzNodtp;
    }

    public void setAmtCum30RateXzNodtp(String amtCum30RateXzNodtp) {
        this.amtCum30RateXzNodtp = amtCum30RateXzNodtp;
    }

    public String getAmtCum30RateJsNodtp() {
        return amtCum30RateJsNodtp;
    }

    public void setAmtCum30RateJsNodtp(String amtCum30RateJsNodtp) {
        this.amtCum30RateJsNodtp = amtCum30RateJsNodtp;
    }

    public String getAmtCum30RateNozy() {
        return amtCum30RateNozy;
    }

    public void setAmtCum30RateNozy(String amtCum30RateNozy) {
        this.amtCum30RateNozy = amtCum30RateNozy;
    }

    public String getAmtCum30RateJtNozy() {
        return amtCum30RateJtNozy;
    }

    public void setAmtCum30RateJtNozy(String amtCum30RateJtNozy) {
        this.amtCum30RateJtNozy = amtCum30RateJtNozy;
    }

    public String getAmtCum30RatePtNozy() {
        return amtCum30RatePtNozy;
    }

    public void setAmtCum30RatePtNozy(String amtCum30RatePtNozy) {
        this.amtCum30RatePtNozy = amtCum30RatePtNozy;
    }

    public String getAmtCum30RateQtNozy() {
        return amtCum30RateQtNozy;
    }

    public void setAmtCum30RateQtNozy(String amtCum30RateQtNozy) {
        this.amtCum30RateQtNozy = amtCum30RateQtNozy;
    }

    public String getAmtCum30RateDxbbNozy() {
        return amtCum30RateDxbbNozy;
    }

    public void setAmtCum30RateDxbbNozy(String amtCum30RateDxbbNozy) {
        this.amtCum30RateDxbbNozy = amtCum30RateDxbbNozy;
    }

    public String getAmtCum30RateDxxpNozy() {
        return amtCum30RateDxxpNozy;
    }

    public void setAmtCum30RateDxxpNozy(String amtCum30RateDxxpNozy) {
        this.amtCum30RateDxxpNozy = amtCum30RateDxxpNozy;
    }

    public String getAmtCum30RateDdNozy() {
        return amtCum30RateDdNozy;
    }

    public void setAmtCum30RateDdNozy(String amtCum30RateDdNozy) {
        this.amtCum30RateDdNozy = amtCum30RateDdNozy;
    }

    public String getAmtCum30RateJtQyDxNozy() {
        return amtCum30RateJtQyDxNozy;
    }

    public void setAmtCum30RateJtQyDxNozy(String amtCum30RateJtQyDxNozy) {
        this.amtCum30RateJtQyDxNozy = amtCum30RateJtQyDxNozy;
    }

    public String getAmtCum30RateNoDdNozy() {
        return amtCum30RateNoDdNozy;
    }

    public void setAmtCum30RateNoDdNozy(String amtCum30RateNoDdNozy) {
        this.amtCum30RateNoDdNozy = amtCum30RateNoDdNozy;
    }

    public String getAmtCum30RateXzNozy() {
        return amtCum30RateXzNozy;
    }

    public void setAmtCum30RateXzNozy(String amtCum30RateXzNozy) {
        this.amtCum30RateXzNozy = amtCum30RateXzNozy;
    }

    public String getAmtCum30RateJsNozy() {
        return amtCum30RateJsNozy;
    }

    public void setAmtCum30RateJsNozy(String amtCum30RateJsNozy) {
        this.amtCum30RateJsNozy = amtCum30RateJsNozy;
    }

    public String getStockCum30() {
        return stockCum30;
    }

    public void setStockCum30(String stockCum30) {
        this.stockCum30 = stockCum30;
    }

    public String getStockCum30Jt() {
        return stockCum30Jt;
    }

    public void setStockCum30Jt(String stockCum30Jt) {
        this.stockCum30Jt = stockCum30Jt;
    }

    public String getStockCum30Pt() {
        return stockCum30Pt;
    }

    public void setStockCum30Pt(String stockCum30Pt) {
        this.stockCum30Pt = stockCum30Pt;
    }

    public String getStockCum30Qy() {
        return stockCum30Qy;
    }

    public void setStockCum30Qy(String stockCum30Qy) {
        this.stockCum30Qy = stockCum30Qy;
    }

    public String getStockCum30Dxbb() {
        return stockCum30Dxbb;
    }

    public void setStockCum30Dxbb(String stockCum30Dxbb) {
        this.stockCum30Dxbb = stockCum30Dxbb;
    }

    public String getStockCum30Dxxp() {
        return stockCum30Dxxp;
    }

    public void setStockCum30Dxxp(String stockCum30Dxxp) {
        this.stockCum30Dxxp = stockCum30Dxxp;
    }

    public String getStockCum30Dd() {
        return stockCum30Dd;
    }

    public void setStockCum30Dd(String stockCum30Dd) {
        this.stockCum30Dd = stockCum30Dd;
    }

    public String getStockCum30JtQyDx() {
        return stockCum30JtQyDx;
    }

    public void setStockCum30JtQyDx(String stockCum30JtQyDx) {
        this.stockCum30JtQyDx = stockCum30JtQyDx;
    }

    public String getStockCum30NoDd() {
        return stockCum30NoDd;
    }

    public void setStockCum30NoDd(String stockCum30NoDd) {
        this.stockCum30NoDd = stockCum30NoDd;
    }

    public String getStockCum30New() {
        return stockCum30New;
    }

    public void setStockCum30New(String stockCum30New) {
        this.stockCum30New = stockCum30New;
    }

    public String getStockCum30Js() {
        return stockCum30Js;
    }

    public void setStockCum30Js(String stockCum30Js) {
        this.stockCum30Js = stockCum30Js;
    }

    public String getAvgStockCum30New() {
        return avgStockCum30New;
    }

    public void setAvgStockCum30New(String avgStockCum30New) {
        this.avgStockCum30New = avgStockCum30New;
    }

    public String getAvgStockCum30Js() {
        return avgStockCum30Js;
    }

    public void setAvgStockCum30Js(String avgStockCum30Js) {
        this.avgStockCum30Js = avgStockCum30Js;
    }

    public String getAvgSkuCntXz() {
        return avgSkuCntXz;
    }

    public void setAvgSkuCntXz(String avgSkuCntXz) {
        this.avgSkuCntXz = avgSkuCntXz;
    }

    public String getAvgSkuCntJs() {
        return avgSkuCntJs;
    }

    public void setAvgSkuCntJs(String avgSkuCntJs) {
        this.avgSkuCntJs = avgSkuCntJs;
    }

    public String getAvgCnt() {
        return avgCnt;
    }

    public void setAvgCnt(String avgCnt) {
        this.avgCnt = avgCnt;
    }

    public String getAmtCum30Null() {
        return amtCum30Null;
    }

    public void setAmtCum30Null(String amtCum30Null) {
        this.amtCum30Null = amtCum30Null;
    }

    public String getAvgAmtCum30Null() {
        return avgAmtCum30Null;
    }

    public void setAvgAmtCum30Null(String avgAmtCum30Null) {
        this.avgAmtCum30Null = avgAmtCum30Null;
    }

    public String getAmtCum30New() {
        return amtCum30New;
    }

    public void setAmtCum30New(String amtCum30New) {
        this.amtCum30New = amtCum30New;
    }

    public String getAvgAmtCum30New() {
        return avgAmtCum30New;
    }

    public void setAvgAmtCum30New(String avgAmtCum30New) {
        this.avgAmtCum30New = avgAmtCum30New;
    }

    public String getProfitCum30New() {
        return profitCum30New;
    }

    public void setProfitCum30New(String profitCum30New) {
        this.profitCum30New = profitCum30New;
    }

    public String getProfitCum30RateNew() {
        return profitCum30RateNew;
    }

    public void setProfitCum30RateNew(String profitCum30RateNew) {
        this.profitCum30RateNew = profitCum30RateNew;
    }

    public String getAmtCum30RateNew() {
        return amtCum30RateNew;
    }

    public void setAmtCum30RateNew(String amtCum30RateNew) {
        this.amtCum30RateNew = amtCum30RateNew;
    }

    public String getAmtCum30RateNewNodtp() {
        return amtCum30RateNewNodtp;
    }

    public void setAmtCum30RateNewNodtp(String amtCum30RateNewNodtp) {
        this.amtCum30RateNewNodtp = amtCum30RateNewNodtp;
    }

    public String getAmtCum30RateNewNozy() {
        return amtCum30RateNewNozy;
    }

    public void setAmtCum30RateNewNozy(String amtCum30RateNewNozy) {
        this.amtCum30RateNewNozy = amtCum30RateNewNozy;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TrackRetultLevelReview other = (TrackRetultLevelReview) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
                && (this.getZoneNew() == null ? other.getZoneNew() == null : this.getZoneNew().equals(other.getZoneNew()))
                && (this.getChainName() == null ? other.getChainName() == null : this.getChainName().equals(other.getChainName()))
                && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
                && (this.getReviseStoreGroup() == null ? other.getReviseStoreGroup() == null : this.getReviseStoreGroup().equals(other.getReviseStoreGroup()))
                && (this.getClassoneName() == null ? other.getClassoneName() == null : this.getClassoneName().equals(other.getClassoneName()))
                && (this.getOrgCntV3() == null ? other.getOrgCntV3() == null : this.getOrgCntV3().equals(other.getOrgCntV3()))
                && (this.getSkuCnt() == null ? other.getSkuCnt() == null : this.getSkuCnt().equals(other.getSkuCnt()))
                && (this.getSkuCntJt() == null ? other.getSkuCntJt() == null : this.getSkuCntJt().equals(other.getSkuCntJt()))
                && (this.getSkuCntPt() == null ? other.getSkuCntPt() == null : this.getSkuCntPt().equals(other.getSkuCntPt()))
                && (this.getSkuCntQy() == null ? other.getSkuCntQy() == null : this.getSkuCntQy().equals(other.getSkuCntQy()))
                && (this.getSkuCntDxbb() == null ? other.getSkuCntDxbb() == null : this.getSkuCntDxbb().equals(other.getSkuCntDxbb()))
                && (this.getSkuCntDxxp() == null ? other.getSkuCntDxxp() == null : this.getSkuCntDxxp().equals(other.getSkuCntDxxp()))
                && (this.getSkuCntDd() == null ? other.getSkuCntDd() == null : this.getSkuCntDd().equals(other.getSkuCntDd()))
                && (this.getSkuCntJtQyDx() == null ? other.getSkuCntJtQyDx() == null : this.getSkuCntJtQyDx().equals(other.getSkuCntJtQyDx()))
                && (this.getSkuCntNoDd() == null ? other.getSkuCntNoDd() == null : this.getSkuCntNoDd().equals(other.getSkuCntNoDd()))
                && (this.getSkuCntXz() == null ? other.getSkuCntXz() == null : this.getSkuCntXz().equals(other.getSkuCntXz()))
                && (this.getSkuCntJs() == null ? other.getSkuCntJs() == null : this.getSkuCntJs().equals(other.getSkuCntJs()))
                && (this.getSkuRateTop4() == null ? other.getSkuRateTop4() == null : this.getSkuRateTop4().equals(other.getSkuRateTop4()))
                && (this.getSkuRateNoDd() == null ? other.getSkuRateNoDd() == null : this.getSkuRateNoDd().equals(other.getSkuRateNoDd()))
                && (this.getCompCnt() == null ? other.getCompCnt() == null : this.getCompCnt().equals(other.getCompCnt()))
                && (this.getCompCntJt() == null ? other.getCompCntJt() == null : this.getCompCntJt().equals(other.getCompCntJt()))
                && (this.getCompCntPt() == null ? other.getCompCntPt() == null : this.getCompCntPt().equals(other.getCompCntPt()))
                && (this.getCompCntQy() == null ? other.getCompCntQy() == null : this.getCompCntQy().equals(other.getCompCntQy()))
                && (this.getCompCntDxbb() == null ? other.getCompCntDxbb() == null : this.getCompCntDxbb().equals(other.getCompCntDxbb()))
                && (this.getCompCntDxxp() == null ? other.getCompCntDxxp() == null : this.getCompCntDxxp().equals(other.getCompCntDxxp()))
                && (this.getCompCntDd() == null ? other.getCompCntDd() == null : this.getCompCntDd().equals(other.getCompCntDd()))
                && (this.getCompCntJtQyDx() == null ? other.getCompCntJtQyDx() == null : this.getCompCntJtQyDx().equals(other.getCompCntJtQyDx()))
                && (this.getCompCntNoDd() == null ? other.getCompCntNoDd() == null : this.getCompCntNoDd().equals(other.getCompCntNoDd()))
                && (this.getCompCntXz() == null ? other.getCompCntXz() == null : this.getCompCntXz().equals(other.getCompCntXz()))
                && (this.getCompCntJs() == null ? other.getCompCntJs() == null : this.getCompCntJs().equals(other.getCompCntJs()))
                && (this.getCompRateTop4() == null ? other.getCompRateTop4() == null : this.getCompRateTop4().equals(other.getCompRateTop4()))
                && (this.getCompRateNoDd() == null ? other.getCompRateNoDd() == null : this.getCompRateNoDd().equals(other.getCompRateNoDd()))
                && (this.getClassCnt() == null ? other.getClassCnt() == null : this.getClassCnt().equals(other.getClassCnt()))
                && (this.getClassCntJt() == null ? other.getClassCntJt() == null : this.getClassCntJt().equals(other.getClassCntJt()))
                && (this.getClassCntPt() == null ? other.getClassCntPt() == null : this.getClassCntPt().equals(other.getClassCntPt()))
                && (this.getClassCntQy() == null ? other.getClassCntQy() == null : this.getClassCntQy().equals(other.getClassCntQy()))
                && (this.getClassCntDxbb() == null ? other.getClassCntDxbb() == null : this.getClassCntDxbb().equals(other.getClassCntDxbb()))
                && (this.getClassCntDxxp() == null ? other.getClassCntDxxp() == null : this.getClassCntDxxp().equals(other.getClassCntDxxp()))
                && (this.getClassCntDd() == null ? other.getClassCntDd() == null : this.getClassCntDd().equals(other.getClassCntDd()))
                && (this.getClassCntJtQyDx() == null ? other.getClassCntJtQyDx() == null : this.getClassCntJtQyDx().equals(other.getClassCntJtQyDx()))
                && (this.getClassCntNoDd() == null ? other.getClassCntNoDd() == null : this.getClassCntNoDd().equals(other.getClassCntNoDd()))
                && (this.getClassCntXz() == null ? other.getClassCntXz() == null : this.getClassCntXz().equals(other.getClassCntXz()))
                && (this.getClassCntJs() == null ? other.getClassCntJs() == null : this.getClassCntJs().equals(other.getClassCntJs()))
                && (this.getAmtCum30() == null ? other.getAmtCum30() == null : this.getAmtCum30().equals(other.getAmtCum30()))
                && (this.getAmtCum30Jt() == null ? other.getAmtCum30Jt() == null : this.getAmtCum30Jt().equals(other.getAmtCum30Jt()))
                && (this.getAmtCum30Pt() == null ? other.getAmtCum30Pt() == null : this.getAmtCum30Pt().equals(other.getAmtCum30Pt()))
                && (this.getAmtCum30Qy() == null ? other.getAmtCum30Qy() == null : this.getAmtCum30Qy().equals(other.getAmtCum30Qy()))
                && (this.getAmtCum30Dxbb() == null ? other.getAmtCum30Dxbb() == null : this.getAmtCum30Dxbb().equals(other.getAmtCum30Dxbb()))
                && (this.getAmtCum30Dxxp() == null ? other.getAmtCum30Dxxp() == null : this.getAmtCum30Dxxp().equals(other.getAmtCum30Dxxp()))
                && (this.getAmtCum30Dd() == null ? other.getAmtCum30Dd() == null : this.getAmtCum30Dd().equals(other.getAmtCum30Dd()))
                && (this.getAmtCum30JtQyDx() == null ? other.getAmtCum30JtQyDx() == null : this.getAmtCum30JtQyDx().equals(other.getAmtCum30JtQyDx()))
                && (this.getAmtCum30NoDd() == null ? other.getAmtCum30NoDd() == null : this.getAmtCum30NoDd().equals(other.getAmtCum30NoDd()))
                && (this.getAmtCum30Xz() == null ? other.getAmtCum30Xz() == null : this.getAmtCum30Xz().equals(other.getAmtCum30Xz()))
                && (this.getAmtCum30Js() == null ? other.getAmtCum30Js() == null : this.getAmtCum30Js().equals(other.getAmtCum30Js()))
                && (this.getProfitCum30() == null ? other.getProfitCum30() == null : this.getProfitCum30().equals(other.getProfitCum30()))
                && (this.getProfitCum30Jt() == null ? other.getProfitCum30Jt() == null : this.getProfitCum30Jt().equals(other.getProfitCum30Jt()))
                && (this.getProfitCum30Pt() == null ? other.getProfitCum30Pt() == null : this.getProfitCum30Pt().equals(other.getProfitCum30Pt()))
                && (this.getProfitCum30Qy() == null ? other.getProfitCum30Qy() == null : this.getProfitCum30Qy().equals(other.getProfitCum30Qy()))
                && (this.getProfitCum30Dxbb() == null ? other.getProfitCum30Dxbb() == null : this.getProfitCum30Dxbb().equals(other.getProfitCum30Dxbb()))
                && (this.getProfitCum30Dxxp() == null ? other.getProfitCum30Dxxp() == null : this.getProfitCum30Dxxp().equals(other.getProfitCum30Dxxp()))
                && (this.getProfitCum30Dd() == null ? other.getProfitCum30Dd() == null : this.getProfitCum30Dd().equals(other.getProfitCum30Dd()))
                && (this.getProfitCum30JtQyDx() == null ? other.getProfitCum30JtQyDx() == null : this.getProfitCum30JtQyDx().equals(other.getProfitCum30JtQyDx()))
                && (this.getProfitCum30NoDd() == null ? other.getProfitCum30NoDd() == null : this.getProfitCum30NoDd().equals(other.getProfitCum30NoDd()))
                && (this.getProfitCum30Xz() == null ? other.getProfitCum30Xz() == null : this.getProfitCum30Xz().equals(other.getProfitCum30Xz()))
                && (this.getProfitCum30Js() == null ? other.getProfitCum30Js() == null : this.getProfitCum30Js().equals(other.getProfitCum30Js()))
                && (this.getProfitCum30Rate() == null ? other.getProfitCum30Rate() == null : this.getProfitCum30Rate().equals(other.getProfitCum30Rate()))
                && (this.getProfitCum30RateJt() == null ? other.getProfitCum30RateJt() == null : this.getProfitCum30RateJt().equals(other.getProfitCum30RateJt()))
                && (this.getProfitCum30RatePt() == null ? other.getProfitCum30RatePt() == null : this.getProfitCum30RatePt().equals(other.getProfitCum30RatePt()))
                && (this.getProfitCum30RateQy() == null ? other.getProfitCum30RateQy() == null : this.getProfitCum30RateQy().equals(other.getProfitCum30RateQy()))
                && (this.getProfitCum30RateDxbb() == null ? other.getProfitCum30RateDxbb() == null : this.getProfitCum30RateDxbb().equals(other.getProfitCum30RateDxbb()))
                && (this.getProfitCum30RateDxxp() == null ? other.getProfitCum30RateDxxp() == null : this.getProfitCum30RateDxxp().equals(other.getProfitCum30RateDxxp()))
                && (this.getProfitCum30RateDd() == null ? other.getProfitCum30RateDd() == null : this.getProfitCum30RateDd().equals(other.getProfitCum30RateDd()))
                && (this.getProfitCum30RateJtQyDx() == null ? other.getProfitCum30RateJtQyDx() == null : this.getProfitCum30RateJtQyDx().equals(other.getProfitCum30RateJtQyDx()))
                && (this.getProfitCum30RateNoDd() == null ? other.getProfitCum30RateNoDd() == null : this.getProfitCum30RateNoDd().equals(other.getProfitCum30RateNoDd()))
                && (this.getProfitCum30RateXz() == null ? other.getProfitCum30RateXz() == null : this.getProfitCum30RateXz().equals(other.getProfitCum30RateXz()))
                && (this.getProfitCum30RateJs() == null ? other.getProfitCum30RateJs() == null : this.getProfitCum30RateJs().equals(other.getProfitCum30RateJs()))
                && (this.getAmtCum30Month() == null ? other.getAmtCum30Month() == null : this.getAmtCum30Month().equals(other.getAmtCum30Month()))
                && (this.getAmtCum30MonthNodtp() == null ? other.getAmtCum30MonthNodtp() == null : this.getAmtCum30MonthNodtp().equals(other.getAmtCum30MonthNodtp()))
                && (this.getAmtCum30MonthNozy() == null ? other.getAmtCum30MonthNozy() == null : this.getAmtCum30MonthNozy().equals(other.getAmtCum30MonthNozy()))
                && (this.getAmtCum30Rate() == null ? other.getAmtCum30Rate() == null : this.getAmtCum30Rate().equals(other.getAmtCum30Rate()))
                && (this.getAmtCum30RateJt() == null ? other.getAmtCum30RateJt() == null : this.getAmtCum30RateJt().equals(other.getAmtCum30RateJt()))
                && (this.getAmtCum30RatePt() == null ? other.getAmtCum30RatePt() == null : this.getAmtCum30RatePt().equals(other.getAmtCum30RatePt()))
                && (this.getAmtCum30RateQt() == null ? other.getAmtCum30RateQt() == null : this.getAmtCum30RateQt().equals(other.getAmtCum30RateQt()))
                && (this.getAmtCum30RateDxbb() == null ? other.getAmtCum30RateDxbb() == null : this.getAmtCum30RateDxbb().equals(other.getAmtCum30RateDxbb()))
                && (this.getAmtCum30RateDxxp() == null ? other.getAmtCum30RateDxxp() == null : this.getAmtCum30RateDxxp().equals(other.getAmtCum30RateDxxp()))
                && (this.getAmtCum30RateDd() == null ? other.getAmtCum30RateDd() == null : this.getAmtCum30RateDd().equals(other.getAmtCum30RateDd()))
                && (this.getAmtCum30RateJtQyDx() == null ? other.getAmtCum30RateJtQyDx() == null : this.getAmtCum30RateJtQyDx().equals(other.getAmtCum30RateJtQyDx()))
                && (this.getAmtCum30RateNoDd() == null ? other.getAmtCum30RateNoDd() == null : this.getAmtCum30RateNoDd().equals(other.getAmtCum30RateNoDd()))
                && (this.getAmtCum30RateXz() == null ? other.getAmtCum30RateXz() == null : this.getAmtCum30RateXz().equals(other.getAmtCum30RateXz()))
                && (this.getAmtCum30RateJs() == null ? other.getAmtCum30RateJs() == null : this.getAmtCum30RateJs().equals(other.getAmtCum30RateJs()))
                && (this.getAmtCum30RateNodtp() == null ? other.getAmtCum30RateNodtp() == null : this.getAmtCum30RateNodtp().equals(other.getAmtCum30RateNodtp()))
                && (this.getAmtCum30RateJtNodtp() == null ? other.getAmtCum30RateJtNodtp() == null : this.getAmtCum30RateJtNodtp().equals(other.getAmtCum30RateJtNodtp()))
                && (this.getAmtCum30RatePtNodtp() == null ? other.getAmtCum30RatePtNodtp() == null : this.getAmtCum30RatePtNodtp().equals(other.getAmtCum30RatePtNodtp()))
                && (this.getAmtCum30RateQtNodtp() == null ? other.getAmtCum30RateQtNodtp() == null : this.getAmtCum30RateQtNodtp().equals(other.getAmtCum30RateQtNodtp()))
                && (this.getAmtCum30RateDxbbNodtp() == null ? other.getAmtCum30RateDxbbNodtp() == null : this.getAmtCum30RateDxbbNodtp().equals(other.getAmtCum30RateDxbbNodtp()))
                && (this.getAmtCum30RateDxxpNodtp() == null ? other.getAmtCum30RateDxxpNodtp() == null : this.getAmtCum30RateDxxpNodtp().equals(other.getAmtCum30RateDxxpNodtp()))
                && (this.getAmtCum30RateDdNodtp() == null ? other.getAmtCum30RateDdNodtp() == null : this.getAmtCum30RateDdNodtp().equals(other.getAmtCum30RateDdNodtp()))
                && (this.getAmtCum30RateJtQyDxNodtp() == null ? other.getAmtCum30RateJtQyDxNodtp() == null : this.getAmtCum30RateJtQyDxNodtp().equals(other.getAmtCum30RateJtQyDxNodtp()))
                && (this.getAmtCum30RateNoDdNodtp() == null ? other.getAmtCum30RateNoDdNodtp() == null : this.getAmtCum30RateNoDdNodtp().equals(other.getAmtCum30RateNoDdNodtp()))
                && (this.getAmtCum30RateXzNodtp() == null ? other.getAmtCum30RateXzNodtp() == null : this.getAmtCum30RateXzNodtp().equals(other.getAmtCum30RateXzNodtp()))
                && (this.getAmtCum30RateJsNodtp() == null ? other.getAmtCum30RateJsNodtp() == null : this.getAmtCum30RateJsNodtp().equals(other.getAmtCum30RateJsNodtp()))
                && (this.getAmtCum30RateNozy() == null ? other.getAmtCum30RateNozy() == null : this.getAmtCum30RateNozy().equals(other.getAmtCum30RateNozy()))
                && (this.getAmtCum30RateJtNozy() == null ? other.getAmtCum30RateJtNozy() == null : this.getAmtCum30RateJtNozy().equals(other.getAmtCum30RateJtNozy()))
                && (this.getAmtCum30RatePtNozy() == null ? other.getAmtCum30RatePtNozy() == null : this.getAmtCum30RatePtNozy().equals(other.getAmtCum30RatePtNozy()))
                && (this.getAmtCum30RateQtNozy() == null ? other.getAmtCum30RateQtNozy() == null : this.getAmtCum30RateQtNozy().equals(other.getAmtCum30RateQtNozy()))
                && (this.getAmtCum30RateDxbbNozy() == null ? other.getAmtCum30RateDxbbNozy() == null : this.getAmtCum30RateDxbbNozy().equals(other.getAmtCum30RateDxbbNozy()))
                && (this.getAmtCum30RateDxxpNozy() == null ? other.getAmtCum30RateDxxpNozy() == null : this.getAmtCum30RateDxxpNozy().equals(other.getAmtCum30RateDxxpNozy()))
                && (this.getAmtCum30RateDdNozy() == null ? other.getAmtCum30RateDdNozy() == null : this.getAmtCum30RateDdNozy().equals(other.getAmtCum30RateDdNozy()))
                && (this.getAmtCum30RateJtQyDxNozy() == null ? other.getAmtCum30RateJtQyDxNozy() == null : this.getAmtCum30RateJtQyDxNozy().equals(other.getAmtCum30RateJtQyDxNozy()))
                && (this.getAmtCum30RateNoDdNozy() == null ? other.getAmtCum30RateNoDdNozy() == null : this.getAmtCum30RateNoDdNozy().equals(other.getAmtCum30RateNoDdNozy()))
                && (this.getAmtCum30RateXzNozy() == null ? other.getAmtCum30RateXzNozy() == null : this.getAmtCum30RateXzNozy().equals(other.getAmtCum30RateXzNozy()))
                && (this.getAmtCum30RateJsNozy() == null ? other.getAmtCum30RateJsNozy() == null : this.getAmtCum30RateJsNozy().equals(other.getAmtCum30RateJsNozy()))
                && (this.getStockCum30() == null ? other.getStockCum30() == null : this.getStockCum30().equals(other.getStockCum30()))
                && (this.getStockCum30Jt() == null ? other.getStockCum30Jt() == null : this.getStockCum30Jt().equals(other.getStockCum30Jt()))
                && (this.getStockCum30Pt() == null ? other.getStockCum30Pt() == null : this.getStockCum30Pt().equals(other.getStockCum30Pt()))
                && (this.getStockCum30Qy() == null ? other.getStockCum30Qy() == null : this.getStockCum30Qy().equals(other.getStockCum30Qy()))
                && (this.getStockCum30Dxbb() == null ? other.getStockCum30Dxbb() == null : this.getStockCum30Dxbb().equals(other.getStockCum30Dxbb()))
                && (this.getStockCum30Dxxp() == null ? other.getStockCum30Dxxp() == null : this.getStockCum30Dxxp().equals(other.getStockCum30Dxxp()))
                && (this.getStockCum30Dd() == null ? other.getStockCum30Dd() == null : this.getStockCum30Dd().equals(other.getStockCum30Dd()))
                && (this.getStockCum30JtQyDx() == null ? other.getStockCum30JtQyDx() == null : this.getStockCum30JtQyDx().equals(other.getStockCum30JtQyDx()))
                && (this.getStockCum30NoDd() == null ? other.getStockCum30NoDd() == null : this.getStockCum30NoDd().equals(other.getStockCum30NoDd()))
                && (this.getStockCum30New() == null ? other.getStockCum30New() == null : this.getStockCum30New().equals(other.getStockCum30New()))
                && (this.getStockCum30Js() == null ? other.getStockCum30Js() == null : this.getStockCum30Js().equals(other.getStockCum30Js()))
                && (this.getAvgStockCum30New() == null ? other.getAvgStockCum30New() == null : this.getAvgStockCum30New().equals(other.getAvgStockCum30New()))
                && (this.getAvgStockCum30Js() == null ? other.getAvgStockCum30Js() == null : this.getAvgStockCum30Js().equals(other.getAvgStockCum30Js()))
                && (this.getAvgSkuCntXz() == null ? other.getAvgSkuCntXz() == null : this.getAvgSkuCntXz().equals(other.getAvgSkuCntXz()))
                && (this.getAvgSkuCntJs() == null ? other.getAvgSkuCntJs() == null : this.getAvgSkuCntJs().equals(other.getAvgSkuCntJs()))
                && (this.getAvgCnt() == null ? other.getAvgCnt() == null : this.getAvgCnt().equals(other.getAvgCnt()))
                && (this.getAmtCum30Null() == null ? other.getAmtCum30Null() == null : this.getAmtCum30Null().equals(other.getAmtCum30Null()))
                && (this.getAvgAmtCum30Null() == null ? other.getAvgAmtCum30Null() == null : this.getAvgAmtCum30Null().equals(other.getAvgAmtCum30Null()))
                && (this.getAmtCum30New() == null ? other.getAmtCum30New() == null : this.getAmtCum30New().equals(other.getAmtCum30New()))
                && (this.getAvgAmtCum30New() == null ? other.getAvgAmtCum30New() == null : this.getAvgAmtCum30New().equals(other.getAvgAmtCum30New()))
                && (this.getProfitCum30New() == null ? other.getProfitCum30New() == null : this.getProfitCum30New().equals(other.getProfitCum30New()))
                && (this.getProfitCum30RateNew() == null ? other.getProfitCum30RateNew() == null : this.getProfitCum30RateNew().equals(other.getProfitCum30RateNew()))
                && (this.getAmtCum30RateNew() == null ? other.getAmtCum30RateNew() == null : this.getAmtCum30RateNew().equals(other.getAmtCum30RateNew()))
                && (this.getAmtCum30RateNewNodtp() == null ? other.getAmtCum30RateNewNodtp() == null : this.getAmtCum30RateNewNodtp().equals(other.getAmtCum30RateNewNodtp()))
                && (this.getAmtCum30RateNewNozy() == null ? other.getAmtCum30RateNewNozy() == null : this.getAmtCum30RateNewNozy().equals(other.getAmtCum30RateNewNozy()))
                && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getZoneNew() == null) ? 0 : getZoneNew().hashCode());
        result = prime * result + ((getChainName() == null) ? 0 : getChainName().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getReviseStoreGroup() == null) ? 0 : getReviseStoreGroup().hashCode());
        result = prime * result + ((getClassoneName() == null) ? 0 : getClassoneName().hashCode());
        result = prime * result + ((getOrgCntV3() == null) ? 0 : getOrgCntV3().hashCode());
        result = prime * result + ((getSkuCnt() == null) ? 0 : getSkuCnt().hashCode());
        result = prime * result + ((getSkuCntJt() == null) ? 0 : getSkuCntJt().hashCode());
        result = prime * result + ((getSkuCntPt() == null) ? 0 : getSkuCntPt().hashCode());
        result = prime * result + ((getSkuCntQy() == null) ? 0 : getSkuCntQy().hashCode());
        result = prime * result + ((getSkuCntDxbb() == null) ? 0 : getSkuCntDxbb().hashCode());
        result = prime * result + ((getSkuCntDxxp() == null) ? 0 : getSkuCntDxxp().hashCode());
        result = prime * result + ((getSkuCntDd() == null) ? 0 : getSkuCntDd().hashCode());
        result = prime * result + ((getSkuCntJtQyDx() == null) ? 0 : getSkuCntJtQyDx().hashCode());
        result = prime * result + ((getSkuCntNoDd() == null) ? 0 : getSkuCntNoDd().hashCode());
        result = prime * result + ((getSkuCntXz() == null) ? 0 : getSkuCntXz().hashCode());
        result = prime * result + ((getSkuCntJs() == null) ? 0 : getSkuCntJs().hashCode());
        result = prime * result + ((getSkuRateTop4() == null) ? 0 : getSkuRateTop4().hashCode());
        result = prime * result + ((getSkuRateNoDd() == null) ? 0 : getSkuRateNoDd().hashCode());
        result = prime * result + ((getCompCnt() == null) ? 0 : getCompCnt().hashCode());
        result = prime * result + ((getCompCntJt() == null) ? 0 : getCompCntJt().hashCode());
        result = prime * result + ((getCompCntPt() == null) ? 0 : getCompCntPt().hashCode());
        result = prime * result + ((getCompCntQy() == null) ? 0 : getCompCntQy().hashCode());
        result = prime * result + ((getCompCntDxbb() == null) ? 0 : getCompCntDxbb().hashCode());
        result = prime * result + ((getCompCntDxxp() == null) ? 0 : getCompCntDxxp().hashCode());
        result = prime * result + ((getCompCntDd() == null) ? 0 : getCompCntDd().hashCode());
        result = prime * result + ((getCompCntJtQyDx() == null) ? 0 : getCompCntJtQyDx().hashCode());
        result = prime * result + ((getCompCntNoDd() == null) ? 0 : getCompCntNoDd().hashCode());
        result = prime * result + ((getCompCntXz() == null) ? 0 : getCompCntXz().hashCode());
        result = prime * result + ((getCompCntJs() == null) ? 0 : getCompCntJs().hashCode());
        result = prime * result + ((getCompRateTop4() == null) ? 0 : getCompRateTop4().hashCode());
        result = prime * result + ((getCompRateNoDd() == null) ? 0 : getCompRateNoDd().hashCode());
        result = prime * result + ((getClassCnt() == null) ? 0 : getClassCnt().hashCode());
        result = prime * result + ((getClassCntJt() == null) ? 0 : getClassCntJt().hashCode());
        result = prime * result + ((getClassCntPt() == null) ? 0 : getClassCntPt().hashCode());
        result = prime * result + ((getClassCntQy() == null) ? 0 : getClassCntQy().hashCode());
        result = prime * result + ((getClassCntDxbb() == null) ? 0 : getClassCntDxbb().hashCode());
        result = prime * result + ((getClassCntDxxp() == null) ? 0 : getClassCntDxxp().hashCode());
        result = prime * result + ((getClassCntDd() == null) ? 0 : getClassCntDd().hashCode());
        result = prime * result + ((getClassCntJtQyDx() == null) ? 0 : getClassCntJtQyDx().hashCode());
        result = prime * result + ((getClassCntNoDd() == null) ? 0 : getClassCntNoDd().hashCode());
        result = prime * result + ((getClassCntXz() == null) ? 0 : getClassCntXz().hashCode());
        result = prime * result + ((getClassCntJs() == null) ? 0 : getClassCntJs().hashCode());
        result = prime * result + ((getAmtCum30() == null) ? 0 : getAmtCum30().hashCode());
        result = prime * result + ((getAmtCum30Jt() == null) ? 0 : getAmtCum30Jt().hashCode());
        result = prime * result + ((getAmtCum30Pt() == null) ? 0 : getAmtCum30Pt().hashCode());
        result = prime * result + ((getAmtCum30Qy() == null) ? 0 : getAmtCum30Qy().hashCode());
        result = prime * result + ((getAmtCum30Dxbb() == null) ? 0 : getAmtCum30Dxbb().hashCode());
        result = prime * result + ((getAmtCum30Dxxp() == null) ? 0 : getAmtCum30Dxxp().hashCode());
        result = prime * result + ((getAmtCum30Dd() == null) ? 0 : getAmtCum30Dd().hashCode());
        result = prime * result + ((getAmtCum30JtQyDx() == null) ? 0 : getAmtCum30JtQyDx().hashCode());
        result = prime * result + ((getAmtCum30NoDd() == null) ? 0 : getAmtCum30NoDd().hashCode());
        result = prime * result + ((getAmtCum30Xz() == null) ? 0 : getAmtCum30Xz().hashCode());
        result = prime * result + ((getAmtCum30Js() == null) ? 0 : getAmtCum30Js().hashCode());
        result = prime * result + ((getProfitCum30() == null) ? 0 : getProfitCum30().hashCode());
        result = prime * result + ((getProfitCum30Jt() == null) ? 0 : getProfitCum30Jt().hashCode());
        result = prime * result + ((getProfitCum30Pt() == null) ? 0 : getProfitCum30Pt().hashCode());
        result = prime * result + ((getProfitCum30Qy() == null) ? 0 : getProfitCum30Qy().hashCode());
        result = prime * result + ((getProfitCum30Dxbb() == null) ? 0 : getProfitCum30Dxbb().hashCode());
        result = prime * result + ((getProfitCum30Dxxp() == null) ? 0 : getProfitCum30Dxxp().hashCode());
        result = prime * result + ((getProfitCum30Dd() == null) ? 0 : getProfitCum30Dd().hashCode());
        result = prime * result + ((getProfitCum30JtQyDx() == null) ? 0 : getProfitCum30JtQyDx().hashCode());
        result = prime * result + ((getProfitCum30NoDd() == null) ? 0 : getProfitCum30NoDd().hashCode());
        result = prime * result + ((getProfitCum30Xz() == null) ? 0 : getProfitCum30Xz().hashCode());
        result = prime * result + ((getProfitCum30Js() == null) ? 0 : getProfitCum30Js().hashCode());
        result = prime * result + ((getProfitCum30Rate() == null) ? 0 : getProfitCum30Rate().hashCode());
        result = prime * result + ((getProfitCum30RateJt() == null) ? 0 : getProfitCum30RateJt().hashCode());
        result = prime * result + ((getProfitCum30RatePt() == null) ? 0 : getProfitCum30RatePt().hashCode());
        result = prime * result + ((getProfitCum30RateQy() == null) ? 0 : getProfitCum30RateQy().hashCode());
        result = prime * result + ((getProfitCum30RateDxbb() == null) ? 0 : getProfitCum30RateDxbb().hashCode());
        result = prime * result + ((getProfitCum30RateDxxp() == null) ? 0 : getProfitCum30RateDxxp().hashCode());
        result = prime * result + ((getProfitCum30RateDd() == null) ? 0 : getProfitCum30RateDd().hashCode());
        result = prime * result + ((getProfitCum30RateJtQyDx() == null) ? 0 : getProfitCum30RateJtQyDx().hashCode());
        result = prime * result + ((getProfitCum30RateNoDd() == null) ? 0 : getProfitCum30RateNoDd().hashCode());
        result = prime * result + ((getProfitCum30RateXz() == null) ? 0 : getProfitCum30RateXz().hashCode());
        result = prime * result + ((getProfitCum30RateJs() == null) ? 0 : getProfitCum30RateJs().hashCode());
        result = prime * result + ((getAmtCum30Month() == null) ? 0 : getAmtCum30Month().hashCode());
        result = prime * result + ((getAmtCum30MonthNodtp() == null) ? 0 : getAmtCum30MonthNodtp().hashCode());
        result = prime * result + ((getAmtCum30MonthNozy() == null) ? 0 : getAmtCum30MonthNozy().hashCode());
        result = prime * result + ((getAmtCum30Rate() == null) ? 0 : getAmtCum30Rate().hashCode());
        result = prime * result + ((getAmtCum30RateJt() == null) ? 0 : getAmtCum30RateJt().hashCode());
        result = prime * result + ((getAmtCum30RatePt() == null) ? 0 : getAmtCum30RatePt().hashCode());
        result = prime * result + ((getAmtCum30RateQt() == null) ? 0 : getAmtCum30RateQt().hashCode());
        result = prime * result + ((getAmtCum30RateDxbb() == null) ? 0 : getAmtCum30RateDxbb().hashCode());
        result = prime * result + ((getAmtCum30RateDxxp() == null) ? 0 : getAmtCum30RateDxxp().hashCode());
        result = prime * result + ((getAmtCum30RateDd() == null) ? 0 : getAmtCum30RateDd().hashCode());
        result = prime * result + ((getAmtCum30RateJtQyDx() == null) ? 0 : getAmtCum30RateJtQyDx().hashCode());
        result = prime * result + ((getAmtCum30RateNoDd() == null) ? 0 : getAmtCum30RateNoDd().hashCode());
        result = prime * result + ((getAmtCum30RateXz() == null) ? 0 : getAmtCum30RateXz().hashCode());
        result = prime * result + ((getAmtCum30RateJs() == null) ? 0 : getAmtCum30RateJs().hashCode());
        result = prime * result + ((getAmtCum30RateNodtp() == null) ? 0 : getAmtCum30RateNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateJtNodtp() == null) ? 0 : getAmtCum30RateJtNodtp().hashCode());
        result = prime * result + ((getAmtCum30RatePtNodtp() == null) ? 0 : getAmtCum30RatePtNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateQtNodtp() == null) ? 0 : getAmtCum30RateQtNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateDxbbNodtp() == null) ? 0 : getAmtCum30RateDxbbNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateDxxpNodtp() == null) ? 0 : getAmtCum30RateDxxpNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateDdNodtp() == null) ? 0 : getAmtCum30RateDdNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateJtQyDxNodtp() == null) ? 0 : getAmtCum30RateJtQyDxNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateNoDdNodtp() == null) ? 0 : getAmtCum30RateNoDdNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateXzNodtp() == null) ? 0 : getAmtCum30RateXzNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateJsNodtp() == null) ? 0 : getAmtCum30RateJsNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateNozy() == null) ? 0 : getAmtCum30RateNozy().hashCode());
        result = prime * result + ((getAmtCum30RateJtNozy() == null) ? 0 : getAmtCum30RateJtNozy().hashCode());
        result = prime * result + ((getAmtCum30RatePtNozy() == null) ? 0 : getAmtCum30RatePtNozy().hashCode());
        result = prime * result + ((getAmtCum30RateQtNozy() == null) ? 0 : getAmtCum30RateQtNozy().hashCode());
        result = prime * result + ((getAmtCum30RateDxbbNozy() == null) ? 0 : getAmtCum30RateDxbbNozy().hashCode());
        result = prime * result + ((getAmtCum30RateDxxpNozy() == null) ? 0 : getAmtCum30RateDxxpNozy().hashCode());
        result = prime * result + ((getAmtCum30RateDdNozy() == null) ? 0 : getAmtCum30RateDdNozy().hashCode());
        result = prime * result + ((getAmtCum30RateJtQyDxNozy() == null) ? 0 : getAmtCum30RateJtQyDxNozy().hashCode());
        result = prime * result + ((getAmtCum30RateNoDdNozy() == null) ? 0 : getAmtCum30RateNoDdNozy().hashCode());
        result = prime * result + ((getAmtCum30RateXzNozy() == null) ? 0 : getAmtCum30RateXzNozy().hashCode());
        result = prime * result + ((getAmtCum30RateJsNozy() == null) ? 0 : getAmtCum30RateJsNozy().hashCode());
        result = prime * result + ((getStockCum30() == null) ? 0 : getStockCum30().hashCode());
        result = prime * result + ((getStockCum30Jt() == null) ? 0 : getStockCum30Jt().hashCode());
        result = prime * result + ((getStockCum30Pt() == null) ? 0 : getStockCum30Pt().hashCode());
        result = prime * result + ((getStockCum30Qy() == null) ? 0 : getStockCum30Qy().hashCode());
        result = prime * result + ((getStockCum30Dxbb() == null) ? 0 : getStockCum30Dxbb().hashCode());
        result = prime * result + ((getStockCum30Dxxp() == null) ? 0 : getStockCum30Dxxp().hashCode());
        result = prime * result + ((getStockCum30Dd() == null) ? 0 : getStockCum30Dd().hashCode());
        result = prime * result + ((getStockCum30JtQyDx() == null) ? 0 : getStockCum30JtQyDx().hashCode());
        result = prime * result + ((getStockCum30NoDd() == null) ? 0 : getStockCum30NoDd().hashCode());
        result = prime * result + ((getStockCum30New() == null) ? 0 : getStockCum30New().hashCode());
        result = prime * result + ((getStockCum30Js() == null) ? 0 : getStockCum30Js().hashCode());
        result = prime * result + ((getAvgStockCum30New() == null) ? 0 : getAvgStockCum30New().hashCode());
        result = prime * result + ((getAvgStockCum30Js() == null) ? 0 : getAvgStockCum30Js().hashCode());
        result = prime * result + ((getAvgSkuCntXz() == null) ? 0 : getAvgSkuCntXz().hashCode());
        result = prime * result + ((getAvgSkuCntJs() == null) ? 0 : getAvgSkuCntJs().hashCode());
        result = prime * result + ((getAvgCnt() == null) ? 0 : getAvgCnt().hashCode());
        result = prime * result + ((getAmtCum30Null() == null) ? 0 : getAmtCum30Null().hashCode());
        result = prime * result + ((getAvgAmtCum30Null() == null) ? 0 : getAvgAmtCum30Null().hashCode());
        result = prime * result + ((getAmtCum30New() == null) ? 0 : getAmtCum30New().hashCode());
        result = prime * result + ((getAvgAmtCum30New() == null) ? 0 : getAvgAmtCum30New().hashCode());
        result = prime * result + ((getProfitCum30New() == null) ? 0 : getProfitCum30New().hashCode());
        result = prime * result + ((getProfitCum30RateNew() == null) ? 0 : getProfitCum30RateNew().hashCode());
        result = prime * result + ((getAmtCum30RateNew() == null) ? 0 : getAmtCum30RateNew().hashCode());
        result = prime * result + ((getAmtCum30RateNewNodtp() == null) ? 0 : getAmtCum30RateNewNodtp().hashCode());
        result = prime * result + ((getAmtCum30RateNewNozy() == null) ? 0 : getAmtCum30RateNewNozy().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", zoneNew=").append(zoneNew);
        sb.append(", chainName=").append(chainName);
        sb.append(", city=").append(city);
        sb.append(", reviseStoreGroup=").append(reviseStoreGroup);
        sb.append(", classoneName=").append(classoneName);
        sb.append(", orgCntV3=").append(orgCntV3);
        sb.append(", skuCnt=").append(skuCnt);
        sb.append(", skuCntJt=").append(skuCntJt);
        sb.append(", skuCntPt=").append(skuCntPt);
        sb.append(", skuCntQy=").append(skuCntQy);
        sb.append(", skuCntDxbb=").append(skuCntDxbb);
        sb.append(", skuCntDxxp=").append(skuCntDxxp);
        sb.append(", skuCntDd=").append(skuCntDd);
        sb.append(", skuCntJtQyDx=").append(skuCntJtQyDx);
        sb.append(", skuCntNoDd=").append(skuCntNoDd);
        sb.append(", skuCntXz=").append(skuCntXz);
        sb.append(", skuCntJs=").append(skuCntJs);
        sb.append(", skuRateTop4=").append(skuRateTop4);
        sb.append(", skuRateNoDd=").append(skuRateNoDd);
        sb.append(", compCnt=").append(compCnt);
        sb.append(", compCntJt=").append(compCntJt);
        sb.append(", compCntPt=").append(compCntPt);
        sb.append(", compCntQy=").append(compCntQy);
        sb.append(", compCntDxbb=").append(compCntDxbb);
        sb.append(", compCntDxxp=").append(compCntDxxp);
        sb.append(", compCntDd=").append(compCntDd);
        sb.append(", compCntJtQyDx=").append(compCntJtQyDx);
        sb.append(", compCntNoDd=").append(compCntNoDd);
        sb.append(", compCntXz=").append(compCntXz);
        sb.append(", compCntJs=").append(compCntJs);
        sb.append(", compRateTop4=").append(compRateTop4);
        sb.append(", compRateNoDd=").append(compRateNoDd);
        sb.append(", classCnt=").append(classCnt);
        sb.append(", classCntJt=").append(classCntJt);
        sb.append(", classCntPt=").append(classCntPt);
        sb.append(", classCntQy=").append(classCntQy);
        sb.append(", classCntDxbb=").append(classCntDxbb);
        sb.append(", classCntDxxp=").append(classCntDxxp);
        sb.append(", classCntDd=").append(classCntDd);
        sb.append(", classCntJtQyDx=").append(classCntJtQyDx);
        sb.append(", classCntNoDd=").append(classCntNoDd);
        sb.append(", classCntXz=").append(classCntXz);
        sb.append(", classCntJs=").append(classCntJs);
        sb.append(", amtCum30=").append(amtCum30);
        sb.append(", amtCum30Jt=").append(amtCum30Jt);
        sb.append(", amtCum30Pt=").append(amtCum30Pt);
        sb.append(", amtCum30Qy=").append(amtCum30Qy);
        sb.append(", amtCum30Dxbb=").append(amtCum30Dxbb);
        sb.append(", amtCum30Dxxp=").append(amtCum30Dxxp);
        sb.append(", amtCum30Dd=").append(amtCum30Dd);
        sb.append(", amtCum30JtQyDx=").append(amtCum30JtQyDx);
        sb.append(", amtCum30NoDd=").append(amtCum30NoDd);
        sb.append(", amtCum30Xz=").append(amtCum30Xz);
        sb.append(", amtCum30Js=").append(amtCum30Js);
        sb.append(", profitCum30=").append(profitCum30);
        sb.append(", profitCum30Jt=").append(profitCum30Jt);
        sb.append(", profitCum30Pt=").append(profitCum30Pt);
        sb.append(", profitCum30Qy=").append(profitCum30Qy);
        sb.append(", profitCum30Dxbb=").append(profitCum30Dxbb);
        sb.append(", profitCum30Dxxp=").append(profitCum30Dxxp);
        sb.append(", profitCum30Dd=").append(profitCum30Dd);
        sb.append(", profitCum30JtQyDx=").append(profitCum30JtQyDx);
        sb.append(", profitCum30NoDd=").append(profitCum30NoDd);
        sb.append(", profitCum30Xz=").append(profitCum30Xz);
        sb.append(", profitCum30Js=").append(profitCum30Js);
        sb.append(", profitCum30Rate=").append(profitCum30Rate);
        sb.append(", profitCum30RateJt=").append(profitCum30RateJt);
        sb.append(", profitCum30RatePt=").append(profitCum30RatePt);
        sb.append(", profitCum30RateQy=").append(profitCum30RateQy);
        sb.append(", profitCum30RateDxbb=").append(profitCum30RateDxbb);
        sb.append(", profitCum30RateDxxp=").append(profitCum30RateDxxp);
        sb.append(", profitCum30RateDd=").append(profitCum30RateDd);
        sb.append(", profitCum30RateJtQyDx=").append(profitCum30RateJtQyDx);
        sb.append(", profitCum30RateNoDd=").append(profitCum30RateNoDd);
        sb.append(", profitCum30RateXz=").append(profitCum30RateXz);
        sb.append(", profitCum30RateJs=").append(profitCum30RateJs);
        sb.append(", amtCum30Month=").append(amtCum30Month);
        sb.append(", amtCum30MonthNodtp=").append(amtCum30MonthNodtp);
        sb.append(", amtCum30MonthNozy=").append(amtCum30MonthNozy);
        sb.append(", amtCum30Rate=").append(amtCum30Rate);
        sb.append(", amtCum30RateJt=").append(amtCum30RateJt);
        sb.append(", amtCum30RatePt=").append(amtCum30RatePt);
        sb.append(", amtCum30RateQt=").append(amtCum30RateQt);
        sb.append(", amtCum30RateDxbb=").append(amtCum30RateDxbb);
        sb.append(", amtCum30RateDxxp=").append(amtCum30RateDxxp);
        sb.append(", amtCum30RateDd=").append(amtCum30RateDd);
        sb.append(", amtCum30RateJtQyDx=").append(amtCum30RateJtQyDx);
        sb.append(", amtCum30RateNoDd=").append(amtCum30RateNoDd);
        sb.append(", amtCum30RateXz=").append(amtCum30RateXz);
        sb.append(", amtCum30RateJs=").append(amtCum30RateJs);
        sb.append(", amtCum30RateNodtp=").append(amtCum30RateNodtp);
        sb.append(", amtCum30RateJtNodtp=").append(amtCum30RateJtNodtp);
        sb.append(", amtCum30RatePtNodtp=").append(amtCum30RatePtNodtp);
        sb.append(", amtCum30RateQtNodtp=").append(amtCum30RateQtNodtp);
        sb.append(", amtCum30RateDxbbNodtp=").append(amtCum30RateDxbbNodtp);
        sb.append(", amtCum30RateDxxpNodtp=").append(amtCum30RateDxxpNodtp);
        sb.append(", amtCum30RateDdNodtp=").append(amtCum30RateDdNodtp);
        sb.append(", amtCum30RateJtQyDxNodtp=").append(amtCum30RateJtQyDxNodtp);
        sb.append(", amtCum30RateNoDdNodtp=").append(amtCum30RateNoDdNodtp);
        sb.append(", amtCum30RateXzNodtp=").append(amtCum30RateXzNodtp);
        sb.append(", amtCum30RateJsNodtp=").append(amtCum30RateJsNodtp);
        sb.append(", amtCum30RateNozy=").append(amtCum30RateNozy);
        sb.append(", amtCum30RateJtNozy=").append(amtCum30RateJtNozy);
        sb.append(", amtCum30RatePtNozy=").append(amtCum30RatePtNozy);
        sb.append(", amtCum30RateQtNozy=").append(amtCum30RateQtNozy);
        sb.append(", amtCum30RateDxbbNozy=").append(amtCum30RateDxbbNozy);
        sb.append(", amtCum30RateDxxpNozy=").append(amtCum30RateDxxpNozy);
        sb.append(", amtCum30RateDdNozy=").append(amtCum30RateDdNozy);
        sb.append(", amtCum30RateJtQyDxNozy=").append(amtCum30RateJtQyDxNozy);
        sb.append(", amtCum30RateNoDdNozy=").append(amtCum30RateNoDdNozy);
        sb.append(", amtCum30RateXzNozy=").append(amtCum30RateXzNozy);
        sb.append(", amtCum30RateJsNozy=").append(amtCum30RateJsNozy);
        sb.append(", stockCum30=").append(stockCum30);
        sb.append(", stockCum30Jt=").append(stockCum30Jt);
        sb.append(", stockCum30Pt=").append(stockCum30Pt);
        sb.append(", stockCum30Qy=").append(stockCum30Qy);
        sb.append(", stockCum30Dxbb=").append(stockCum30Dxbb);
        sb.append(", stockCum30Dxxp=").append(stockCum30Dxxp);
        sb.append(", stockCum30Dd=").append(stockCum30Dd);
        sb.append(", stockCum30JtQyDx=").append(stockCum30JtQyDx);
        sb.append(", stockCum30NoDd=").append(stockCum30NoDd);
        sb.append(", stockCum30New=").append(stockCum30New);
        sb.append(", stockCum30Js=").append(stockCum30Js);
        sb.append(", avgStockCum30New=").append(avgStockCum30New);
        sb.append(", avgStockCum30Js=").append(avgStockCum30Js);
        sb.append(", avgSkuCntXz=").append(avgSkuCntXz);
        sb.append(", avgSkuCntJs=").append(avgSkuCntJs);
        sb.append(", avgCnt=").append(avgCnt);
        sb.append(", amtCum30Null=").append(amtCum30Null);
        sb.append(", avgAmtCum30Null=").append(avgAmtCum30Null);
        sb.append(", amtCum30New=").append(amtCum30New);
        sb.append(", avgAmtCum30New=").append(avgAmtCum30New);
        sb.append(", profitCum30New=").append(profitCum30New);
        sb.append(", profitCum30RateNew=").append(profitCum30RateNew);
        sb.append(", amtCum30RateNew=").append(amtCum30RateNew);
        sb.append(", amtCum30RateNewNodtp=").append(amtCum30RateNewNodtp);
        sb.append(", amtCum30RateNewNozy=").append(amtCum30RateNewNozy);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}