package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-一店一目经营建议确认进度表
 */
@Data
public class JymlStoreSkuSuggestProcess implements Serializable {
    /**
     * 分布式主键
     */
    private Long id;

    /**
     * 连锁org id
     */
    private Long businessOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 通知店长,需要确认分类数据
     */
    private String zdtStartNotice;

    /**
     * 通知店长,需要确认分类数据  (默认:0  0:不需要通知  1:需要通知 -1:条件异常)
     */
    private Integer zdtStartNoticeFalg;

    /**
     * 本次调整周期(开始)
     */
    private Date beginProcessTime;

    /**
     * 定时任务自动操作确认时间
     */
    private Date scheduledProcessTime;

    /**
     * 本次调整周期(结束)
     */
    private Date endProcessTime;

    /**
     * 全部分类确认完成(默认:0  0:未确认 1:已确认)
     */
    private Integer confirmed;

    /**
     * 通知店长,自动处理已经完成 notice
     */
    private String zdtEndNotice;

    /**
     * 通知店长自动处理已经完成  (默认:0  0:不需要通知  1:需要通知 -1:条件异常 2:已经结束)
     */
    private Integer zdtEndNoticeFalg;

    /**
     * 下次调整周期(开始)
     */
    private Date nextBeginProcessTime;

    /**
     * 下次调整周期(结束)
     */
    private Date nextEndProcessTime;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;
}