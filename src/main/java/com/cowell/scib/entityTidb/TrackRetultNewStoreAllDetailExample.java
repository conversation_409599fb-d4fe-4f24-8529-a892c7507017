package com.cowell.scib.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrackRetultNewStoreAllDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TrackRetultNewStoreAllDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andOrgNoIsNull() {
            addCriterion("org_no is null");
            return (Criteria) this;
        }

        public Criteria andOrgNoIsNotNull() {
            addCriterion("org_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNoEqualTo(String value) {
            addCriterion("org_no =", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotEqualTo(String value) {
            addCriterion("org_no <>", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoGreaterThan(String value) {
            addCriterion("org_no >", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoGreaterThanOrEqualTo(String value) {
            addCriterion("org_no >=", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLessThan(String value) {
            addCriterion("org_no <", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLessThanOrEqualTo(String value) {
            addCriterion("org_no <=", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLike(String value) {
            addCriterion("org_no like", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotLike(String value) {
            addCriterion("org_no not like", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoIn(List<String> values) {
            addCriterion("org_no in", values, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotIn(List<String> values) {
            addCriterion("org_no not in", values, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoBetween(String value1, String value2) {
            addCriterion("org_no between", value1, value2, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotBetween(String value1, String value2) {
            addCriterion("org_no not between", value1, value2, "orgNo");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("goods_id is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("goods_id is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(String value) {
            addCriterion("goods_id =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(String value) {
            addCriterion("goods_id <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(String value) {
            addCriterion("goods_id >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(String value) {
            addCriterion("goods_id >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(String value) {
            addCriterion("goods_id <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(String value) {
            addCriterion("goods_id <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLike(String value) {
            addCriterion("goods_id like", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotLike(String value) {
            addCriterion("goods_id not like", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<String> values) {
            addCriterion("goods_id in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<String> values) {
            addCriterion("goods_id not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(String value1, String value2) {
            addCriterion("goods_id between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(String value1, String value2) {
            addCriterion("goods_id not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsnameIsNull() {
            addCriterion("goodsname is null");
            return (Criteria) this;
        }

        public Criteria andGoodsnameIsNotNull() {
            addCriterion("goodsname is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsnameEqualTo(String value) {
            addCriterion("goodsname =", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotEqualTo(String value) {
            addCriterion("goodsname <>", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameGreaterThan(String value) {
            addCriterion("goodsname >", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameGreaterThanOrEqualTo(String value) {
            addCriterion("goodsname >=", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameLessThan(String value) {
            addCriterion("goodsname <", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameLessThanOrEqualTo(String value) {
            addCriterion("goodsname <=", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameLike(String value) {
            addCriterion("goodsname like", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotLike(String value) {
            addCriterion("goodsname not like", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameIn(List<String> values) {
            addCriterion("goodsname in", values, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotIn(List<String> values) {
            addCriterion("goodsname not in", values, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameBetween(String value1, String value2) {
            addCriterion("goodsname between", value1, value2, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotBetween(String value1, String value2) {
            addCriterion("goodsname not between", value1, value2, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsspecIsNull() {
            addCriterion("goodsspec is null");
            return (Criteria) this;
        }

        public Criteria andGoodsspecIsNotNull() {
            addCriterion("goodsspec is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsspecEqualTo(String value) {
            addCriterion("goodsspec =", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotEqualTo(String value) {
            addCriterion("goodsspec <>", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecGreaterThan(String value) {
            addCriterion("goodsspec >", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecGreaterThanOrEqualTo(String value) {
            addCriterion("goodsspec >=", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecLessThan(String value) {
            addCriterion("goodsspec <", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecLessThanOrEqualTo(String value) {
            addCriterion("goodsspec <=", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecLike(String value) {
            addCriterion("goodsspec like", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotLike(String value) {
            addCriterion("goodsspec not like", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecIn(List<String> values) {
            addCriterion("goodsspec in", values, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotIn(List<String> values) {
            addCriterion("goodsspec not in", values, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecBetween(String value1, String value2) {
            addCriterion("goodsspec between", value1, value2, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotBetween(String value1, String value2) {
            addCriterion("goodsspec not between", value1, value2, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameIsNull() {
            addCriterion("jx_cate1_name is null");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameIsNotNull() {
            addCriterion("jx_cate1_name is not null");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameEqualTo(String value) {
            addCriterion("jx_cate1_name =", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotEqualTo(String value) {
            addCriterion("jx_cate1_name <>", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameGreaterThan(String value) {
            addCriterion("jx_cate1_name >", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameGreaterThanOrEqualTo(String value) {
            addCriterion("jx_cate1_name >=", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameLessThan(String value) {
            addCriterion("jx_cate1_name <", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameLessThanOrEqualTo(String value) {
            addCriterion("jx_cate1_name <=", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameLike(String value) {
            addCriterion("jx_cate1_name like", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotLike(String value) {
            addCriterion("jx_cate1_name not like", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameIn(List<String> values) {
            addCriterion("jx_cate1_name in", values, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotIn(List<String> values) {
            addCriterion("jx_cate1_name not in", values, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameBetween(String value1, String value2) {
            addCriterion("jx_cate1_name between", value1, value2, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotBetween(String value1, String value2) {
            addCriterion("jx_cate1_name not between", value1, value2, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andGoodsunitIsNull() {
            addCriterion("goodsunit is null");
            return (Criteria) this;
        }

        public Criteria andGoodsunitIsNotNull() {
            addCriterion("goodsunit is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsunitEqualTo(String value) {
            addCriterion("goodsunit =", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotEqualTo(String value) {
            addCriterion("goodsunit <>", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitGreaterThan(String value) {
            addCriterion("goodsunit >", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitGreaterThanOrEqualTo(String value) {
            addCriterion("goodsunit >=", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitLessThan(String value) {
            addCriterion("goodsunit <", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitLessThanOrEqualTo(String value) {
            addCriterion("goodsunit <=", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitLike(String value) {
            addCriterion("goodsunit like", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotLike(String value) {
            addCriterion("goodsunit not like", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitIn(List<String> values) {
            addCriterion("goodsunit in", values, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotIn(List<String> values) {
            addCriterion("goodsunit not in", values, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitBetween(String value1, String value2) {
            addCriterion("goodsunit between", value1, value2, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotBetween(String value1, String value2) {
            addCriterion("goodsunit not between", value1, value2, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("`level` is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("`level` is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(String value) {
            addCriterion("`level` =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(String value) {
            addCriterion("`level` <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(String value) {
            addCriterion("`level` >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(String value) {
            addCriterion("`level` >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(String value) {
            addCriterion("`level` <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(String value) {
            addCriterion("`level` <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLike(String value) {
            addCriterion("`level` like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotLike(String value) {
            addCriterion("`level` not like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<String> values) {
            addCriterion("`level` in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<String> values) {
            addCriterion("`level` not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(String value1, String value2) {
            addCriterion("`level` between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(String value1, String value2) {
            addCriterion("`level` not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyIsNull() {
            addCriterion("suggest_ph_qty is null");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyIsNotNull() {
            addCriterion("suggest_ph_qty is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyEqualTo(BigDecimal value) {
            addCriterion("suggest_ph_qty =", value, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyNotEqualTo(BigDecimal value) {
            addCriterion("suggest_ph_qty <>", value, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyGreaterThan(BigDecimal value) {
            addCriterion("suggest_ph_qty >", value, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_ph_qty >=", value, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyLessThan(BigDecimal value) {
            addCriterion("suggest_ph_qty <", value, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_ph_qty <=", value, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyIn(List<BigDecimal> values) {
            addCriterion("suggest_ph_qty in", values, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyNotIn(List<BigDecimal> values) {
            addCriterion("suggest_ph_qty not in", values, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_ph_qty between", value1, value2, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andSuggestPhQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_ph_qty not between", value1, value2, "suggestPhQty");
            return (Criteria) this;
        }

        public Criteria andPhCostIsNull() {
            addCriterion("ph_cost is null");
            return (Criteria) this;
        }

        public Criteria andPhCostIsNotNull() {
            addCriterion("ph_cost is not null");
            return (Criteria) this;
        }

        public Criteria andPhCostEqualTo(BigDecimal value) {
            addCriterion("ph_cost =", value, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostNotEqualTo(BigDecimal value) {
            addCriterion("ph_cost <>", value, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostGreaterThan(BigDecimal value) {
            addCriterion("ph_cost >", value, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ph_cost >=", value, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostLessThan(BigDecimal value) {
            addCriterion("ph_cost <", value, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ph_cost <=", value, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostIn(List<BigDecimal> values) {
            addCriterion("ph_cost in", values, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostNotIn(List<BigDecimal> values) {
            addCriterion("ph_cost not in", values, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ph_cost between", value1, value2, "phCost");
            return (Criteria) this;
        }

        public Criteria andPhCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ph_cost not between", value1, value2, "phCost");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeIsNull() {
            addCriterion("taotai_type is null");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeIsNotNull() {
            addCriterion("taotai_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeEqualTo(String value) {
            addCriterion("taotai_type =", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotEqualTo(String value) {
            addCriterion("taotai_type <>", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeGreaterThan(String value) {
            addCriterion("taotai_type >", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeGreaterThanOrEqualTo(String value) {
            addCriterion("taotai_type >=", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeLessThan(String value) {
            addCriterion("taotai_type <", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeLessThanOrEqualTo(String value) {
            addCriterion("taotai_type <=", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeLike(String value) {
            addCriterion("taotai_type like", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotLike(String value) {
            addCriterion("taotai_type not like", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeIn(List<String> values) {
            addCriterion("taotai_type in", values, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotIn(List<String> values) {
            addCriterion("taotai_type not in", values, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeBetween(String value1, String value2) {
            addCriterion("taotai_type between", value1, value2, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotBetween(String value1, String value2) {
            addCriterion("taotai_type not between", value1, value2, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andStjbIsNull() {
            addCriterion("stjb is null");
            return (Criteria) this;
        }

        public Criteria andStjbIsNotNull() {
            addCriterion("stjb is not null");
            return (Criteria) this;
        }

        public Criteria andStjbEqualTo(String value) {
            addCriterion("stjb =", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotEqualTo(String value) {
            addCriterion("stjb <>", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbGreaterThan(String value) {
            addCriterion("stjb >", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbGreaterThanOrEqualTo(String value) {
            addCriterion("stjb >=", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbLessThan(String value) {
            addCriterion("stjb <", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbLessThanOrEqualTo(String value) {
            addCriterion("stjb <=", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbLike(String value) {
            addCriterion("stjb like", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotLike(String value) {
            addCriterion("stjb not like", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbIn(List<String> values) {
            addCriterion("stjb in", values, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotIn(List<String> values) {
            addCriterion("stjb not in", values, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbBetween(String value1, String value2) {
            addCriterion("stjb between", value1, value2, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotBetween(String value1, String value2) {
            addCriterion("stjb not between", value1, value2, "stjb");
            return (Criteria) this;
        }

        public Criteria andGrossprofitIsNull() {
            addCriterion("grossprofit is null");
            return (Criteria) this;
        }

        public Criteria andGrossprofitIsNotNull() {
            addCriterion("grossprofit is not null");
            return (Criteria) this;
        }

        public Criteria andGrossprofitEqualTo(String value) {
            addCriterion("grossprofit =", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotEqualTo(String value) {
            addCriterion("grossprofit <>", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitGreaterThan(String value) {
            addCriterion("grossprofit >", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitGreaterThanOrEqualTo(String value) {
            addCriterion("grossprofit >=", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitLessThan(String value) {
            addCriterion("grossprofit <", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitLessThanOrEqualTo(String value) {
            addCriterion("grossprofit <=", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitLike(String value) {
            addCriterion("grossprofit like", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotLike(String value) {
            addCriterion("grossprofit not like", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitIn(List<String> values) {
            addCriterion("grossprofit in", values, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotIn(List<String> values) {
            addCriterion("grossprofit not in", values, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitBetween(String value1, String value2) {
            addCriterion("grossprofit between", value1, value2, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotBetween(String value1, String value2) {
            addCriterion("grossprofit not between", value1, value2, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNull() {
            addCriterion("sub_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNotNull() {
            addCriterion("sub_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdEqualTo(String value) {
            addCriterion("sub_category_id =", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotEqualTo(String value) {
            addCriterion("sub_category_id <>", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThan(String value) {
            addCriterion("sub_category_id >", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_id >=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThan(String value) {
            addCriterion("sub_category_id <", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("sub_category_id <=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLike(String value) {
            addCriterion("sub_category_id like", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotLike(String value) {
            addCriterion("sub_category_id not like", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIn(List<String> values) {
            addCriterion("sub_category_id in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotIn(List<String> values) {
            addCriterion("sub_category_id not in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdBetween(String value1, String value2) {
            addCriterion("sub_category_id between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotBetween(String value1, String value2) {
            addCriterion("sub_category_id not between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNull() {
            addCriterion("classone_name is null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNotNull() {
            addCriterion("classone_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameEqualTo(String value) {
            addCriterion("classone_name =", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotEqualTo(String value) {
            addCriterion("classone_name <>", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThan(String value) {
            addCriterion("classone_name >", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("classone_name >=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThan(String value) {
            addCriterion("classone_name <", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThanOrEqualTo(String value) {
            addCriterion("classone_name <=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLike(String value) {
            addCriterion("classone_name like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotLike(String value) {
            addCriterion("classone_name not like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIn(List<String> values) {
            addCriterion("classone_name in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotIn(List<String> values) {
            addCriterion("classone_name not in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameBetween(String value1, String value2) {
            addCriterion("classone_name between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotBetween(String value1, String value2) {
            addCriterion("classone_name not between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIsNull() {
            addCriterion("classtwo_name is null");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIsNotNull() {
            addCriterion("classtwo_name is not null");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameEqualTo(String value) {
            addCriterion("classtwo_name =", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotEqualTo(String value) {
            addCriterion("classtwo_name <>", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameGreaterThan(String value) {
            addCriterion("classtwo_name >", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameGreaterThanOrEqualTo(String value) {
            addCriterion("classtwo_name >=", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLessThan(String value) {
            addCriterion("classtwo_name <", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLessThanOrEqualTo(String value) {
            addCriterion("classtwo_name <=", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLike(String value) {
            addCriterion("classtwo_name like", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotLike(String value) {
            addCriterion("classtwo_name not like", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIn(List<String> values) {
            addCriterion("classtwo_name in", values, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotIn(List<String> values) {
            addCriterion("classtwo_name not in", values, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameBetween(String value1, String value2) {
            addCriterion("classtwo_name between", value1, value2, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotBetween(String value1, String value2) {
            addCriterion("classtwo_name not between", value1, value2, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIsNull() {
            addCriterion("classthree_name is null");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIsNotNull() {
            addCriterion("classthree_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameEqualTo(String value) {
            addCriterion("classthree_name =", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotEqualTo(String value) {
            addCriterion("classthree_name <>", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameGreaterThan(String value) {
            addCriterion("classthree_name >", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameGreaterThanOrEqualTo(String value) {
            addCriterion("classthree_name >=", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLessThan(String value) {
            addCriterion("classthree_name <", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLessThanOrEqualTo(String value) {
            addCriterion("classthree_name <=", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLike(String value) {
            addCriterion("classthree_name like", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotLike(String value) {
            addCriterion("classthree_name not like", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIn(List<String> values) {
            addCriterion("classthree_name in", values, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotIn(List<String> values) {
            addCriterion("classthree_name not in", values, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameBetween(String value1, String value2) {
            addCriterion("classthree_name between", value1, value2, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotBetween(String value1, String value2) {
            addCriterion("classthree_name not between", value1, value2, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIsNull() {
            addCriterion("classfour_name is null");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIsNotNull() {
            addCriterion("classfour_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassfourNameEqualTo(String value) {
            addCriterion("classfour_name =", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotEqualTo(String value) {
            addCriterion("classfour_name <>", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameGreaterThan(String value) {
            addCriterion("classfour_name >", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameGreaterThanOrEqualTo(String value) {
            addCriterion("classfour_name >=", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLessThan(String value) {
            addCriterion("classfour_name <", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLessThanOrEqualTo(String value) {
            addCriterion("classfour_name <=", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLike(String value) {
            addCriterion("classfour_name like", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotLike(String value) {
            addCriterion("classfour_name not like", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIn(List<String> values) {
            addCriterion("classfour_name in", values, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotIn(List<String> values) {
            addCriterion("classfour_name not in", values, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameBetween(String value1, String value2) {
            addCriterion("classfour_name between", value1, value2, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotBetween(String value1, String value2) {
            addCriterion("classfour_name not between", value1, value2, "classfourName");
            return (Criteria) this;
        }

        public Criteria andComponentIsNull() {
            addCriterion("component is null");
            return (Criteria) this;
        }

        public Criteria andComponentIsNotNull() {
            addCriterion("component is not null");
            return (Criteria) this;
        }

        public Criteria andComponentEqualTo(String value) {
            addCriterion("component =", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotEqualTo(String value) {
            addCriterion("component <>", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThan(String value) {
            addCriterion("component >", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThanOrEqualTo(String value) {
            addCriterion("component >=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThan(String value) {
            addCriterion("component <", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThanOrEqualTo(String value) {
            addCriterion("component <=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLike(String value) {
            addCriterion("component like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotLike(String value) {
            addCriterion("component not like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentIn(List<String> values) {
            addCriterion("component in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotIn(List<String> values) {
            addCriterion("component not in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentBetween(String value1, String value2) {
            addCriterion("component between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotBetween(String value1, String value2) {
            addCriterion("component not between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andDtpgoodIsNull() {
            addCriterion("dtpgood is null");
            return (Criteria) this;
        }

        public Criteria andDtpgoodIsNotNull() {
            addCriterion("dtpgood is not null");
            return (Criteria) this;
        }

        public Criteria andDtpgoodEqualTo(String value) {
            addCriterion("dtpgood =", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodNotEqualTo(String value) {
            addCriterion("dtpgood <>", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodGreaterThan(String value) {
            addCriterion("dtpgood >", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodGreaterThanOrEqualTo(String value) {
            addCriterion("dtpgood >=", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodLessThan(String value) {
            addCriterion("dtpgood <", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodLessThanOrEqualTo(String value) {
            addCriterion("dtpgood <=", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodLike(String value) {
            addCriterion("dtpgood like", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodNotLike(String value) {
            addCriterion("dtpgood not like", value, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodIn(List<String> values) {
            addCriterion("dtpgood in", values, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodNotIn(List<String> values) {
            addCriterion("dtpgood not in", values, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodBetween(String value1, String value2) {
            addCriterion("dtpgood between", value1, value2, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andDtpgoodNotBetween(String value1, String value2) {
            addCriterion("dtpgood not between", value1, value2, "dtpgood");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseIsNull() {
            addCriterion("flag_disease is null");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseIsNotNull() {
            addCriterion("flag_disease is not null");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseEqualTo(String value) {
            addCriterion("flag_disease =", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotEqualTo(String value) {
            addCriterion("flag_disease <>", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseGreaterThan(String value) {
            addCriterion("flag_disease >", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseGreaterThanOrEqualTo(String value) {
            addCriterion("flag_disease >=", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseLessThan(String value) {
            addCriterion("flag_disease <", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseLessThanOrEqualTo(String value) {
            addCriterion("flag_disease <=", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseLike(String value) {
            addCriterion("flag_disease like", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotLike(String value) {
            addCriterion("flag_disease not like", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseIn(List<String> values) {
            addCriterion("flag_disease in", values, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotIn(List<String> values) {
            addCriterion("flag_disease not in", values, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseBetween(String value1, String value2) {
            addCriterion("flag_disease between", value1, value2, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotBetween(String value1, String value2) {
            addCriterion("flag_disease not between", value1, value2, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDistribindIsNull() {
            addCriterion("distribind is null");
            return (Criteria) this;
        }

        public Criteria andDistribindIsNotNull() {
            addCriterion("distribind is not null");
            return (Criteria) this;
        }

        public Criteria andDistribindEqualTo(String value) {
            addCriterion("distribind =", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindNotEqualTo(String value) {
            addCriterion("distribind <>", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindGreaterThan(String value) {
            addCriterion("distribind >", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindGreaterThanOrEqualTo(String value) {
            addCriterion("distribind >=", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindLessThan(String value) {
            addCriterion("distribind <", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindLessThanOrEqualTo(String value) {
            addCriterion("distribind <=", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindLike(String value) {
            addCriterion("distribind like", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindNotLike(String value) {
            addCriterion("distribind not like", value, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindIn(List<String> values) {
            addCriterion("distribind in", values, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindNotIn(List<String> values) {
            addCriterion("distribind not in", values, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindBetween(String value1, String value2) {
            addCriterion("distribind between", value1, value2, "distribind");
            return (Criteria) this;
        }

        public Criteria andDistribindNotBetween(String value1, String value2) {
            addCriterion("distribind not between", value1, value2, "distribind");
            return (Criteria) this;
        }

        public Criteria andPreciousIsNull() {
            addCriterion("precious is null");
            return (Criteria) this;
        }

        public Criteria andPreciousIsNotNull() {
            addCriterion("precious is not null");
            return (Criteria) this;
        }

        public Criteria andPreciousEqualTo(String value) {
            addCriterion("precious =", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousNotEqualTo(String value) {
            addCriterion("precious <>", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousGreaterThan(String value) {
            addCriterion("precious >", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousGreaterThanOrEqualTo(String value) {
            addCriterion("precious >=", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousLessThan(String value) {
            addCriterion("precious <", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousLessThanOrEqualTo(String value) {
            addCriterion("precious <=", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousLike(String value) {
            addCriterion("precious like", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousNotLike(String value) {
            addCriterion("precious not like", value, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousIn(List<String> values) {
            addCriterion("precious in", values, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousNotIn(List<String> values) {
            addCriterion("precious not in", values, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousBetween(String value1, String value2) {
            addCriterion("precious between", value1, value2, "precious");
            return (Criteria) this;
        }

        public Criteria andPreciousNotBetween(String value1, String value2) {
            addCriterion("precious not between", value1, value2, "precious");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceIsNull() {
            addCriterion("refretailprice is null");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceIsNotNull() {
            addCriterion("refretailprice is not null");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceEqualTo(String value) {
            addCriterion("refretailprice =", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceNotEqualTo(String value) {
            addCriterion("refretailprice <>", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceGreaterThan(String value) {
            addCriterion("refretailprice >", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceGreaterThanOrEqualTo(String value) {
            addCriterion("refretailprice >=", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceLessThan(String value) {
            addCriterion("refretailprice <", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceLessThanOrEqualTo(String value) {
            addCriterion("refretailprice <=", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceLike(String value) {
            addCriterion("refretailprice like", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceNotLike(String value) {
            addCriterion("refretailprice not like", value, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceIn(List<String> values) {
            addCriterion("refretailprice in", values, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceNotIn(List<String> values) {
            addCriterion("refretailprice not in", values, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceBetween(String value1, String value2) {
            addCriterion("refretailprice between", value1, value2, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andRefretailpriceNotBetween(String value1, String value2) {
            addCriterion("refretailprice not between", value1, value2, "refretailprice");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxIsNull() {
            addCriterion("in_stock_rate_dx is null");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxIsNotNull() {
            addCriterion("in_stock_rate_dx is not null");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxEqualTo(String value) {
            addCriterion("in_stock_rate_dx =", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxNotEqualTo(String value) {
            addCriterion("in_stock_rate_dx <>", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxGreaterThan(String value) {
            addCriterion("in_stock_rate_dx >", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxGreaterThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_dx >=", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxLessThan(String value) {
            addCriterion("in_stock_rate_dx <", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxLessThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_dx <=", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxLike(String value) {
            addCriterion("in_stock_rate_dx like", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxNotLike(String value) {
            addCriterion("in_stock_rate_dx not like", value, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxIn(List<String> values) {
            addCriterion("in_stock_rate_dx in", values, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxNotIn(List<String> values) {
            addCriterion("in_stock_rate_dx not in", values, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxBetween(String value1, String value2) {
            addCriterion("in_stock_rate_dx between", value1, value2, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInStockRateDxNotBetween(String value1, String value2) {
            addCriterion("in_stock_rate_dx not between", value1, value2, "inStockRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxIsNull() {
            addCriterion("in_sales_rate_dx is null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxIsNotNull() {
            addCriterion("in_sales_rate_dx is not null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxEqualTo(String value) {
            addCriterion("in_sales_rate_dx =", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxNotEqualTo(String value) {
            addCriterion("in_sales_rate_dx <>", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxGreaterThan(String value) {
            addCriterion("in_sales_rate_dx >", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxGreaterThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_dx >=", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxLessThan(String value) {
            addCriterion("in_sales_rate_dx <", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxLessThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_dx <=", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxLike(String value) {
            addCriterion("in_sales_rate_dx like", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxNotLike(String value) {
            addCriterion("in_sales_rate_dx not like", value, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxIn(List<String> values) {
            addCriterion("in_sales_rate_dx in", values, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxNotIn(List<String> values) {
            addCriterion("in_sales_rate_dx not in", values, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxBetween(String value1, String value2) {
            addCriterion("in_sales_rate_dx between", value1, value2, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andInSalesRateDxNotBetween(String value1, String value2) {
            addCriterion("in_sales_rate_dx not between", value1, value2, "inSalesRateDx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxIsNull() {
            addCriterion("num_cum_90_dx is null");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxIsNotNull() {
            addCriterion("num_cum_90_dx is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxEqualTo(String value) {
            addCriterion("num_cum_90_dx =", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxNotEqualTo(String value) {
            addCriterion("num_cum_90_dx <>", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxGreaterThan(String value) {
            addCriterion("num_cum_90_dx >", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxGreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_90_dx >=", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxLessThan(String value) {
            addCriterion("num_cum_90_dx <", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxLessThanOrEqualTo(String value) {
            addCriterion("num_cum_90_dx <=", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxLike(String value) {
            addCriterion("num_cum_90_dx like", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxNotLike(String value) {
            addCriterion("num_cum_90_dx not like", value, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxIn(List<String> values) {
            addCriterion("num_cum_90_dx in", values, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxNotIn(List<String> values) {
            addCriterion("num_cum_90_dx not in", values, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxBetween(String value1, String value2) {
            addCriterion("num_cum_90_dx between", value1, value2, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andNumCum90DxNotBetween(String value1, String value2) {
            addCriterion("num_cum_90_dx not between", value1, value2, "numCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxIsNull() {
            addCriterion("bill_cnts_cum_90_dx is null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxIsNotNull() {
            addCriterion("bill_cnts_cum_90_dx is not null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_dx =", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxNotEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_dx <>", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxGreaterThan(String value) {
            addCriterion("bill_cnts_cum_90_dx >", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxGreaterThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_dx >=", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxLessThan(String value) {
            addCriterion("bill_cnts_cum_90_dx <", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxLessThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_dx <=", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxLike(String value) {
            addCriterion("bill_cnts_cum_90_dx like", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxNotLike(String value) {
            addCriterion("bill_cnts_cum_90_dx not like", value, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_dx in", values, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxNotIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_dx not in", values, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_dx between", value1, value2, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90DxNotBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_dx not between", value1, value2, "billCntsCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxIsNull() {
            addCriterion("amt_cum_90_dx is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxIsNotNull() {
            addCriterion("amt_cum_90_dx is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxEqualTo(String value) {
            addCriterion("amt_cum_90_dx =", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxNotEqualTo(String value) {
            addCriterion("amt_cum_90_dx <>", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxGreaterThan(String value) {
            addCriterion("amt_cum_90_dx >", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_dx >=", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxLessThan(String value) {
            addCriterion("amt_cum_90_dx <", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_dx <=", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxLike(String value) {
            addCriterion("amt_cum_90_dx like", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxNotLike(String value) {
            addCriterion("amt_cum_90_dx not like", value, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxIn(List<String> values) {
            addCriterion("amt_cum_90_dx in", values, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxNotIn(List<String> values) {
            addCriterion("amt_cum_90_dx not in", values, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxBetween(String value1, String value2) {
            addCriterion("amt_cum_90_dx between", value1, value2, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andAmtCum90DxNotBetween(String value1, String value2) {
            addCriterion("amt_cum_90_dx not between", value1, value2, "amtCum90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxIsNull() {
            addCriterion("profit_rate_90_dx is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxIsNotNull() {
            addCriterion("profit_rate_90_dx is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxEqualTo(String value) {
            addCriterion("profit_rate_90_dx =", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxNotEqualTo(String value) {
            addCriterion("profit_rate_90_dx <>", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxGreaterThan(String value) {
            addCriterion("profit_rate_90_dx >", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxGreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_dx >=", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxLessThan(String value) {
            addCriterion("profit_rate_90_dx <", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxLessThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_dx <=", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxLike(String value) {
            addCriterion("profit_rate_90_dx like", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxNotLike(String value) {
            addCriterion("profit_rate_90_dx not like", value, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxIn(List<String> values) {
            addCriterion("profit_rate_90_dx in", values, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxNotIn(List<String> values) {
            addCriterion("profit_rate_90_dx not in", values, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxBetween(String value1, String value2) {
            addCriterion("profit_rate_90_dx between", value1, value2, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andProfitRate90DxNotBetween(String value1, String value2) {
            addCriterion("profit_rate_90_dx not between", value1, value2, "profitRate90Dx");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityIsNull() {
            addCriterion("in_stock_rate_city is null");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityIsNotNull() {
            addCriterion("in_stock_rate_city is not null");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityEqualTo(String value) {
            addCriterion("in_stock_rate_city =", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityNotEqualTo(String value) {
            addCriterion("in_stock_rate_city <>", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityGreaterThan(String value) {
            addCriterion("in_stock_rate_city >", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityGreaterThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_city >=", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityLessThan(String value) {
            addCriterion("in_stock_rate_city <", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityLessThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_city <=", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityLike(String value) {
            addCriterion("in_stock_rate_city like", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityNotLike(String value) {
            addCriterion("in_stock_rate_city not like", value, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityIn(List<String> values) {
            addCriterion("in_stock_rate_city in", values, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityNotIn(List<String> values) {
            addCriterion("in_stock_rate_city not in", values, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityBetween(String value1, String value2) {
            addCriterion("in_stock_rate_city between", value1, value2, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInStockRateCityNotBetween(String value1, String value2) {
            addCriterion("in_stock_rate_city not between", value1, value2, "inStockRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityIsNull() {
            addCriterion("in_sales_rate_city is null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityIsNotNull() {
            addCriterion("in_sales_rate_city is not null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityEqualTo(String value) {
            addCriterion("in_sales_rate_city =", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityNotEqualTo(String value) {
            addCriterion("in_sales_rate_city <>", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityGreaterThan(String value) {
            addCriterion("in_sales_rate_city >", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityGreaterThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_city >=", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityLessThan(String value) {
            addCriterion("in_sales_rate_city <", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityLessThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_city <=", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityLike(String value) {
            addCriterion("in_sales_rate_city like", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityNotLike(String value) {
            addCriterion("in_sales_rate_city not like", value, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityIn(List<String> values) {
            addCriterion("in_sales_rate_city in", values, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityNotIn(List<String> values) {
            addCriterion("in_sales_rate_city not in", values, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityBetween(String value1, String value2) {
            addCriterion("in_sales_rate_city between", value1, value2, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andInSalesRateCityNotBetween(String value1, String value2) {
            addCriterion("in_sales_rate_city not between", value1, value2, "inSalesRateCity");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityIsNull() {
            addCriterion("num_cum_90_city is null");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityIsNotNull() {
            addCriterion("num_cum_90_city is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityEqualTo(String value) {
            addCriterion("num_cum_90_city =", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityNotEqualTo(String value) {
            addCriterion("num_cum_90_city <>", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityGreaterThan(String value) {
            addCriterion("num_cum_90_city >", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityGreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_90_city >=", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityLessThan(String value) {
            addCriterion("num_cum_90_city <", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityLessThanOrEqualTo(String value) {
            addCriterion("num_cum_90_city <=", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityLike(String value) {
            addCriterion("num_cum_90_city like", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityNotLike(String value) {
            addCriterion("num_cum_90_city not like", value, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityIn(List<String> values) {
            addCriterion("num_cum_90_city in", values, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityNotIn(List<String> values) {
            addCriterion("num_cum_90_city not in", values, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityBetween(String value1, String value2) {
            addCriterion("num_cum_90_city between", value1, value2, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andNumCum90CityNotBetween(String value1, String value2) {
            addCriterion("num_cum_90_city not between", value1, value2, "numCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityIsNull() {
            addCriterion("bill_cnts_cum_90_city is null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityIsNotNull() {
            addCriterion("bill_cnts_cum_90_city is not null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_city =", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityNotEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_city <>", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityGreaterThan(String value) {
            addCriterion("bill_cnts_cum_90_city >", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityGreaterThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_city >=", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityLessThan(String value) {
            addCriterion("bill_cnts_cum_90_city <", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityLessThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_city <=", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityLike(String value) {
            addCriterion("bill_cnts_cum_90_city like", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityNotLike(String value) {
            addCriterion("bill_cnts_cum_90_city not like", value, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_city in", values, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityNotIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_city not in", values, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_city between", value1, value2, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90CityNotBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_city not between", value1, value2, "billCntsCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityIsNull() {
            addCriterion("amt_cum_90_city is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityIsNotNull() {
            addCriterion("amt_cum_90_city is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityEqualTo(String value) {
            addCriterion("amt_cum_90_city =", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityNotEqualTo(String value) {
            addCriterion("amt_cum_90_city <>", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityGreaterThan(String value) {
            addCriterion("amt_cum_90_city >", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_city >=", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityLessThan(String value) {
            addCriterion("amt_cum_90_city <", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_city <=", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityLike(String value) {
            addCriterion("amt_cum_90_city like", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityNotLike(String value) {
            addCriterion("amt_cum_90_city not like", value, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityIn(List<String> values) {
            addCriterion("amt_cum_90_city in", values, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityNotIn(List<String> values) {
            addCriterion("amt_cum_90_city not in", values, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityBetween(String value1, String value2) {
            addCriterion("amt_cum_90_city between", value1, value2, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andAmtCum90CityNotBetween(String value1, String value2) {
            addCriterion("amt_cum_90_city not between", value1, value2, "amtCum90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityIsNull() {
            addCriterion("profit_rate_90_city is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityIsNotNull() {
            addCriterion("profit_rate_90_city is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityEqualTo(String value) {
            addCriterion("profit_rate_90_city =", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityNotEqualTo(String value) {
            addCriterion("profit_rate_90_city <>", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityGreaterThan(String value) {
            addCriterion("profit_rate_90_city >", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityGreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_city >=", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityLessThan(String value) {
            addCriterion("profit_rate_90_city <", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityLessThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_city <=", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityLike(String value) {
            addCriterion("profit_rate_90_city like", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityNotLike(String value) {
            addCriterion("profit_rate_90_city not like", value, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityIn(List<String> values) {
            addCriterion("profit_rate_90_city in", values, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityNotIn(List<String> values) {
            addCriterion("profit_rate_90_city not in", values, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityBetween(String value1, String value2) {
            addCriterion("profit_rate_90_city between", value1, value2, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andProfitRate90CityNotBetween(String value1, String value2) {
            addCriterion("profit_rate_90_city not between", value1, value2, "profitRate90City");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyIsNull() {
            addCriterion("in_stock_rate_qy is null");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyIsNotNull() {
            addCriterion("in_stock_rate_qy is not null");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyEqualTo(String value) {
            addCriterion("in_stock_rate_qy =", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyNotEqualTo(String value) {
            addCriterion("in_stock_rate_qy <>", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyGreaterThan(String value) {
            addCriterion("in_stock_rate_qy >", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyGreaterThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_qy >=", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyLessThan(String value) {
            addCriterion("in_stock_rate_qy <", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyLessThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_qy <=", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyLike(String value) {
            addCriterion("in_stock_rate_qy like", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyNotLike(String value) {
            addCriterion("in_stock_rate_qy not like", value, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyIn(List<String> values) {
            addCriterion("in_stock_rate_qy in", values, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyNotIn(List<String> values) {
            addCriterion("in_stock_rate_qy not in", values, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyBetween(String value1, String value2) {
            addCriterion("in_stock_rate_qy between", value1, value2, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInStockRateQyNotBetween(String value1, String value2) {
            addCriterion("in_stock_rate_qy not between", value1, value2, "inStockRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyIsNull() {
            addCriterion("in_sales_rate_qy is null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyIsNotNull() {
            addCriterion("in_sales_rate_qy is not null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyEqualTo(String value) {
            addCriterion("in_sales_rate_qy =", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyNotEqualTo(String value) {
            addCriterion("in_sales_rate_qy <>", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyGreaterThan(String value) {
            addCriterion("in_sales_rate_qy >", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyGreaterThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_qy >=", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyLessThan(String value) {
            addCriterion("in_sales_rate_qy <", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyLessThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_qy <=", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyLike(String value) {
            addCriterion("in_sales_rate_qy like", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyNotLike(String value) {
            addCriterion("in_sales_rate_qy not like", value, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyIn(List<String> values) {
            addCriterion("in_sales_rate_qy in", values, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyNotIn(List<String> values) {
            addCriterion("in_sales_rate_qy not in", values, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyBetween(String value1, String value2) {
            addCriterion("in_sales_rate_qy between", value1, value2, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andInSalesRateQyNotBetween(String value1, String value2) {
            addCriterion("in_sales_rate_qy not between", value1, value2, "inSalesRateQy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyIsNull() {
            addCriterion("num_cum_90_qy is null");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyIsNotNull() {
            addCriterion("num_cum_90_qy is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyEqualTo(String value) {
            addCriterion("num_cum_90_qy =", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyNotEqualTo(String value) {
            addCriterion("num_cum_90_qy <>", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyGreaterThan(String value) {
            addCriterion("num_cum_90_qy >", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyGreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_90_qy >=", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyLessThan(String value) {
            addCriterion("num_cum_90_qy <", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyLessThanOrEqualTo(String value) {
            addCriterion("num_cum_90_qy <=", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyLike(String value) {
            addCriterion("num_cum_90_qy like", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyNotLike(String value) {
            addCriterion("num_cum_90_qy not like", value, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyIn(List<String> values) {
            addCriterion("num_cum_90_qy in", values, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyNotIn(List<String> values) {
            addCriterion("num_cum_90_qy not in", values, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyBetween(String value1, String value2) {
            addCriterion("num_cum_90_qy between", value1, value2, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andNumCum90QyNotBetween(String value1, String value2) {
            addCriterion("num_cum_90_qy not between", value1, value2, "numCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyIsNull() {
            addCriterion("bill_cnts_cum_90_qy is null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyIsNotNull() {
            addCriterion("bill_cnts_cum_90_qy is not null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_qy =", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyNotEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_qy <>", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyGreaterThan(String value) {
            addCriterion("bill_cnts_cum_90_qy >", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyGreaterThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_qy >=", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyLessThan(String value) {
            addCriterion("bill_cnts_cum_90_qy <", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyLessThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_qy <=", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyLike(String value) {
            addCriterion("bill_cnts_cum_90_qy like", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyNotLike(String value) {
            addCriterion("bill_cnts_cum_90_qy not like", value, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_qy in", values, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyNotIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_qy not in", values, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_qy between", value1, value2, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90QyNotBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_qy not between", value1, value2, "billCntsCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyIsNull() {
            addCriterion("amt_cum_90_qy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyIsNotNull() {
            addCriterion("amt_cum_90_qy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyEqualTo(String value) {
            addCriterion("amt_cum_90_qy =", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyNotEqualTo(String value) {
            addCriterion("amt_cum_90_qy <>", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyGreaterThan(String value) {
            addCriterion("amt_cum_90_qy >", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_qy >=", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyLessThan(String value) {
            addCriterion("amt_cum_90_qy <", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_qy <=", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyLike(String value) {
            addCriterion("amt_cum_90_qy like", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyNotLike(String value) {
            addCriterion("amt_cum_90_qy not like", value, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyIn(List<String> values) {
            addCriterion("amt_cum_90_qy in", values, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyNotIn(List<String> values) {
            addCriterion("amt_cum_90_qy not in", values, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyBetween(String value1, String value2) {
            addCriterion("amt_cum_90_qy between", value1, value2, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum90QyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_90_qy not between", value1, value2, "amtCum90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyIsNull() {
            addCriterion("profit_rate_90_qy is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyIsNotNull() {
            addCriterion("profit_rate_90_qy is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyEqualTo(String value) {
            addCriterion("profit_rate_90_qy =", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyNotEqualTo(String value) {
            addCriterion("profit_rate_90_qy <>", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyGreaterThan(String value) {
            addCriterion("profit_rate_90_qy >", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyGreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_qy >=", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyLessThan(String value) {
            addCriterion("profit_rate_90_qy <", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyLessThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_qy <=", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyLike(String value) {
            addCriterion("profit_rate_90_qy like", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyNotLike(String value) {
            addCriterion("profit_rate_90_qy not like", value, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyIn(List<String> values) {
            addCriterion("profit_rate_90_qy in", values, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyNotIn(List<String> values) {
            addCriterion("profit_rate_90_qy not in", values, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyBetween(String value1, String value2) {
            addCriterion("profit_rate_90_qy between", value1, value2, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andProfitRate90QyNotBetween(String value1, String value2) {
            addCriterion("profit_rate_90_qy not between", value1, value2, "profitRate90Qy");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdIsNull() {
            addCriterion("in_stock_rate_md is null");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdIsNotNull() {
            addCriterion("in_stock_rate_md is not null");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdEqualTo(String value) {
            addCriterion("in_stock_rate_md =", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdNotEqualTo(String value) {
            addCriterion("in_stock_rate_md <>", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdGreaterThan(String value) {
            addCriterion("in_stock_rate_md >", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdGreaterThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_md >=", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdLessThan(String value) {
            addCriterion("in_stock_rate_md <", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdLessThanOrEqualTo(String value) {
            addCriterion("in_stock_rate_md <=", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdLike(String value) {
            addCriterion("in_stock_rate_md like", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdNotLike(String value) {
            addCriterion("in_stock_rate_md not like", value, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdIn(List<String> values) {
            addCriterion("in_stock_rate_md in", values, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdNotIn(List<String> values) {
            addCriterion("in_stock_rate_md not in", values, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdBetween(String value1, String value2) {
            addCriterion("in_stock_rate_md between", value1, value2, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInStockRateMdNotBetween(String value1, String value2) {
            addCriterion("in_stock_rate_md not between", value1, value2, "inStockRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdIsNull() {
            addCriterion("in_sales_rate_md is null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdIsNotNull() {
            addCriterion("in_sales_rate_md is not null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdEqualTo(String value) {
            addCriterion("in_sales_rate_md =", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdNotEqualTo(String value) {
            addCriterion("in_sales_rate_md <>", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdGreaterThan(String value) {
            addCriterion("in_sales_rate_md >", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdGreaterThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_md >=", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdLessThan(String value) {
            addCriterion("in_sales_rate_md <", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdLessThanOrEqualTo(String value) {
            addCriterion("in_sales_rate_md <=", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdLike(String value) {
            addCriterion("in_sales_rate_md like", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdNotLike(String value) {
            addCriterion("in_sales_rate_md not like", value, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdIn(List<String> values) {
            addCriterion("in_sales_rate_md in", values, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdNotIn(List<String> values) {
            addCriterion("in_sales_rate_md not in", values, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdBetween(String value1, String value2) {
            addCriterion("in_sales_rate_md between", value1, value2, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andInSalesRateMdNotBetween(String value1, String value2) {
            addCriterion("in_sales_rate_md not between", value1, value2, "inSalesRateMd");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdIsNull() {
            addCriterion("num_cum_90_md is null");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdIsNotNull() {
            addCriterion("num_cum_90_md is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdEqualTo(String value) {
            addCriterion("num_cum_90_md =", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdNotEqualTo(String value) {
            addCriterion("num_cum_90_md <>", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdGreaterThan(String value) {
            addCriterion("num_cum_90_md >", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdGreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_90_md >=", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdLessThan(String value) {
            addCriterion("num_cum_90_md <", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdLessThanOrEqualTo(String value) {
            addCriterion("num_cum_90_md <=", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdLike(String value) {
            addCriterion("num_cum_90_md like", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdNotLike(String value) {
            addCriterion("num_cum_90_md not like", value, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdIn(List<String> values) {
            addCriterion("num_cum_90_md in", values, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdNotIn(List<String> values) {
            addCriterion("num_cum_90_md not in", values, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdBetween(String value1, String value2) {
            addCriterion("num_cum_90_md between", value1, value2, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andNumCum90MdNotBetween(String value1, String value2) {
            addCriterion("num_cum_90_md not between", value1, value2, "numCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdIsNull() {
            addCriterion("bill_cnts_cum_90_md is null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdIsNotNull() {
            addCriterion("bill_cnts_cum_90_md is not null");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_md =", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdNotEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_md <>", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdGreaterThan(String value) {
            addCriterion("bill_cnts_cum_90_md >", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdGreaterThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_md >=", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdLessThan(String value) {
            addCriterion("bill_cnts_cum_90_md <", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdLessThanOrEqualTo(String value) {
            addCriterion("bill_cnts_cum_90_md <=", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdLike(String value) {
            addCriterion("bill_cnts_cum_90_md like", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdNotLike(String value) {
            addCriterion("bill_cnts_cum_90_md not like", value, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_md in", values, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdNotIn(List<String> values) {
            addCriterion("bill_cnts_cum_90_md not in", values, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_md between", value1, value2, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andBillCntsCum90MdNotBetween(String value1, String value2) {
            addCriterion("bill_cnts_cum_90_md not between", value1, value2, "billCntsCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdIsNull() {
            addCriterion("amt_cum_90_md is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdIsNotNull() {
            addCriterion("amt_cum_90_md is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdEqualTo(String value) {
            addCriterion("amt_cum_90_md =", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdNotEqualTo(String value) {
            addCriterion("amt_cum_90_md <>", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdGreaterThan(String value) {
            addCriterion("amt_cum_90_md >", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_md >=", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdLessThan(String value) {
            addCriterion("amt_cum_90_md <", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_90_md <=", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdLike(String value) {
            addCriterion("amt_cum_90_md like", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdNotLike(String value) {
            addCriterion("amt_cum_90_md not like", value, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdIn(List<String> values) {
            addCriterion("amt_cum_90_md in", values, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdNotIn(List<String> values) {
            addCriterion("amt_cum_90_md not in", values, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdBetween(String value1, String value2) {
            addCriterion("amt_cum_90_md between", value1, value2, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andAmtCum90MdNotBetween(String value1, String value2) {
            addCriterion("amt_cum_90_md not between", value1, value2, "amtCum90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdIsNull() {
            addCriterion("profit_rate_90_md is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdIsNotNull() {
            addCriterion("profit_rate_90_md is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdEqualTo(String value) {
            addCriterion("profit_rate_90_md =", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdNotEqualTo(String value) {
            addCriterion("profit_rate_90_md <>", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdGreaterThan(String value) {
            addCriterion("profit_rate_90_md >", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdGreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_md >=", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdLessThan(String value) {
            addCriterion("profit_rate_90_md <", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdLessThanOrEqualTo(String value) {
            addCriterion("profit_rate_90_md <=", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdLike(String value) {
            addCriterion("profit_rate_90_md like", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdNotLike(String value) {
            addCriterion("profit_rate_90_md not like", value, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdIn(List<String> values) {
            addCriterion("profit_rate_90_md in", values, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdNotIn(List<String> values) {
            addCriterion("profit_rate_90_md not in", values, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdBetween(String value1, String value2) {
            addCriterion("profit_rate_90_md between", value1, value2, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andProfitRate90MdNotBetween(String value1, String value2) {
            addCriterion("profit_rate_90_md not between", value1, value2, "profitRate90Md");
            return (Criteria) this;
        }

        public Criteria andJyAbleIsNull() {
            addCriterion("jy_able is null");
            return (Criteria) this;
        }

        public Criteria andJyAbleIsNotNull() {
            addCriterion("jy_able is not null");
            return (Criteria) this;
        }

        public Criteria andJyAbleEqualTo(Byte value) {
            addCriterion("jy_able =", value, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleNotEqualTo(Byte value) {
            addCriterion("jy_able <>", value, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleGreaterThan(Byte value) {
            addCriterion("jy_able >", value, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("jy_able >=", value, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleLessThan(Byte value) {
            addCriterion("jy_able <", value, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleLessThanOrEqualTo(Byte value) {
            addCriterion("jy_able <=", value, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleIn(List<Byte> values) {
            addCriterion("jy_able in", values, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleNotIn(List<Byte> values) {
            addCriterion("jy_able not in", values, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleBetween(Byte value1, Byte value2) {
            addCriterion("jy_able between", value1, value2, "jyAble");
            return (Criteria) this;
        }

        public Criteria andJyAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("jy_able not between", value1, value2, "jyAble");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNull() {
            addCriterion("rx_otc is null");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNotNull() {
            addCriterion("rx_otc is not null");
            return (Criteria) this;
        }

        public Criteria andRxOtcEqualTo(String value) {
            addCriterion("rx_otc =", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotEqualTo(String value) {
            addCriterion("rx_otc <>", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThan(String value) {
            addCriterion("rx_otc >", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThanOrEqualTo(String value) {
            addCriterion("rx_otc >=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThan(String value) {
            addCriterion("rx_otc <", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThanOrEqualTo(String value) {
            addCriterion("rx_otc <=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLike(String value) {
            addCriterion("rx_otc like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotLike(String value) {
            addCriterion("rx_otc not like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcIn(List<String> values) {
            addCriterion("rx_otc in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotIn(List<String> values) {
            addCriterion("rx_otc not in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcBetween(String value1, String value2) {
            addCriterion("rx_otc between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotBetween(String value1, String value2) {
            addCriterion("rx_otc not between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andBak1IsNull() {
            addCriterion("bak1 is null");
            return (Criteria) this;
        }

        public Criteria andBak1IsNotNull() {
            addCriterion("bak1 is not null");
            return (Criteria) this;
        }

        public Criteria andBak1EqualTo(String value) {
            addCriterion("bak1 =", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1NotEqualTo(String value) {
            addCriterion("bak1 <>", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1GreaterThan(String value) {
            addCriterion("bak1 >", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1GreaterThanOrEqualTo(String value) {
            addCriterion("bak1 >=", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1LessThan(String value) {
            addCriterion("bak1 <", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1LessThanOrEqualTo(String value) {
            addCriterion("bak1 <=", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1Like(String value) {
            addCriterion("bak1 like", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1NotLike(String value) {
            addCriterion("bak1 not like", value, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1In(List<String> values) {
            addCriterion("bak1 in", values, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1NotIn(List<String> values) {
            addCriterion("bak1 not in", values, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1Between(String value1, String value2) {
            addCriterion("bak1 between", value1, value2, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak1NotBetween(String value1, String value2) {
            addCriterion("bak1 not between", value1, value2, "bak1");
            return (Criteria) this;
        }

        public Criteria andBak2IsNull() {
            addCriterion("bak2 is null");
            return (Criteria) this;
        }

        public Criteria andBak2IsNotNull() {
            addCriterion("bak2 is not null");
            return (Criteria) this;
        }

        public Criteria andBak2EqualTo(String value) {
            addCriterion("bak2 =", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2NotEqualTo(String value) {
            addCriterion("bak2 <>", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2GreaterThan(String value) {
            addCriterion("bak2 >", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2GreaterThanOrEqualTo(String value) {
            addCriterion("bak2 >=", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2LessThan(String value) {
            addCriterion("bak2 <", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2LessThanOrEqualTo(String value) {
            addCriterion("bak2 <=", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2Like(String value) {
            addCriterion("bak2 like", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2NotLike(String value) {
            addCriterion("bak2 not like", value, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2In(List<String> values) {
            addCriterion("bak2 in", values, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2NotIn(List<String> values) {
            addCriterion("bak2 not in", values, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2Between(String value1, String value2) {
            addCriterion("bak2 between", value1, value2, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak2NotBetween(String value1, String value2) {
            addCriterion("bak2 not between", value1, value2, "bak2");
            return (Criteria) this;
        }

        public Criteria andBak3IsNull() {
            addCriterion("bak3 is null");
            return (Criteria) this;
        }

        public Criteria andBak3IsNotNull() {
            addCriterion("bak3 is not null");
            return (Criteria) this;
        }

        public Criteria andBak3EqualTo(String value) {
            addCriterion("bak3 =", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3NotEqualTo(String value) {
            addCriterion("bak3 <>", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3GreaterThan(String value) {
            addCriterion("bak3 >", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3GreaterThanOrEqualTo(String value) {
            addCriterion("bak3 >=", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3LessThan(String value) {
            addCriterion("bak3 <", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3LessThanOrEqualTo(String value) {
            addCriterion("bak3 <=", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3Like(String value) {
            addCriterion("bak3 like", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3NotLike(String value) {
            addCriterion("bak3 not like", value, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3In(List<String> values) {
            addCriterion("bak3 in", values, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3NotIn(List<String> values) {
            addCriterion("bak3 not in", values, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3Between(String value1, String value2) {
            addCriterion("bak3 between", value1, value2, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak3NotBetween(String value1, String value2) {
            addCriterion("bak3 not between", value1, value2, "bak3");
            return (Criteria) this;
        }

        public Criteria andBak4IsNull() {
            addCriterion("bak4 is null");
            return (Criteria) this;
        }

        public Criteria andBak4IsNotNull() {
            addCriterion("bak4 is not null");
            return (Criteria) this;
        }

        public Criteria andBak4EqualTo(String value) {
            addCriterion("bak4 =", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4NotEqualTo(String value) {
            addCriterion("bak4 <>", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4GreaterThan(String value) {
            addCriterion("bak4 >", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4GreaterThanOrEqualTo(String value) {
            addCriterion("bak4 >=", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4LessThan(String value) {
            addCriterion("bak4 <", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4LessThanOrEqualTo(String value) {
            addCriterion("bak4 <=", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4Like(String value) {
            addCriterion("bak4 like", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4NotLike(String value) {
            addCriterion("bak4 not like", value, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4In(List<String> values) {
            addCriterion("bak4 in", values, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4NotIn(List<String> values) {
            addCriterion("bak4 not in", values, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4Between(String value1, String value2) {
            addCriterion("bak4 between", value1, value2, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak4NotBetween(String value1, String value2) {
            addCriterion("bak4 not between", value1, value2, "bak4");
            return (Criteria) this;
        }

        public Criteria andBak5IsNull() {
            addCriterion("bak5 is null");
            return (Criteria) this;
        }

        public Criteria andBak5IsNotNull() {
            addCriterion("bak5 is not null");
            return (Criteria) this;
        }

        public Criteria andBak5EqualTo(String value) {
            addCriterion("bak5 =", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5NotEqualTo(String value) {
            addCriterion("bak5 <>", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5GreaterThan(String value) {
            addCriterion("bak5 >", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5GreaterThanOrEqualTo(String value) {
            addCriterion("bak5 >=", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5LessThan(String value) {
            addCriterion("bak5 <", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5LessThanOrEqualTo(String value) {
            addCriterion("bak5 <=", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5Like(String value) {
            addCriterion("bak5 like", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5NotLike(String value) {
            addCriterion("bak5 not like", value, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5In(List<String> values) {
            addCriterion("bak5 in", values, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5NotIn(List<String> values) {
            addCriterion("bak5 not in", values, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5Between(String value1, String value2) {
            addCriterion("bak5 between", value1, value2, "bak5");
            return (Criteria) this;
        }

        public Criteria andBak5NotBetween(String value1, String value2) {
            addCriterion("bak5 not between", value1, value2, "bak5");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}