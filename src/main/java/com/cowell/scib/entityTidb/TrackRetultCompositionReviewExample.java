package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrackRetultCompositionReviewExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TrackRetultCompositionReviewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNull() {
            addCriterion("zone_new is null");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNotNull() {
            addCriterion("zone_new is not null");
            return (Criteria) this;
        }

        public Criteria andZoneNewEqualTo(String value) {
            addCriterion("zone_new =", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotEqualTo(String value) {
            addCriterion("zone_new <>", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThan(String value) {
            addCriterion("zone_new >", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThanOrEqualTo(String value) {
            addCriterion("zone_new >=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThan(String value) {
            addCriterion("zone_new <", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThanOrEqualTo(String value) {
            addCriterion("zone_new <=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLike(String value) {
            addCriterion("zone_new like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotLike(String value) {
            addCriterion("zone_new not like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewIn(List<String> values) {
            addCriterion("zone_new in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotIn(List<String> values) {
            addCriterion("zone_new not in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewBetween(String value1, String value2) {
            addCriterion("zone_new between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotBetween(String value1, String value2) {
            addCriterion("zone_new not between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andChainNameIsNull() {
            addCriterion("chain_name is null");
            return (Criteria) this;
        }

        public Criteria andChainNameIsNotNull() {
            addCriterion("chain_name is not null");
            return (Criteria) this;
        }

        public Criteria andChainNameEqualTo(String value) {
            addCriterion("chain_name =", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotEqualTo(String value) {
            addCriterion("chain_name <>", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameGreaterThan(String value) {
            addCriterion("chain_name >", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameGreaterThanOrEqualTo(String value) {
            addCriterion("chain_name >=", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLessThan(String value) {
            addCriterion("chain_name <", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLessThanOrEqualTo(String value) {
            addCriterion("chain_name <=", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLike(String value) {
            addCriterion("chain_name like", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotLike(String value) {
            addCriterion("chain_name not like", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameIn(List<String> values) {
            addCriterion("chain_name in", values, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotIn(List<String> values) {
            addCriterion("chain_name not in", values, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameBetween(String value1, String value2) {
            addCriterion("chain_name between", value1, value2, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotBetween(String value1, String value2) {
            addCriterion("chain_name not between", value1, value2, "chainName");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andStoreGroupIsNull() {
            addCriterion("store_group is null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupIsNotNull() {
            addCriterion("store_group is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupEqualTo(String value) {
            addCriterion("store_group =", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotEqualTo(String value) {
            addCriterion("store_group <>", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupGreaterThan(String value) {
            addCriterion("store_group >", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupGreaterThanOrEqualTo(String value) {
            addCriterion("store_group >=", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupLessThan(String value) {
            addCriterion("store_group <", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupLessThanOrEqualTo(String value) {
            addCriterion("store_group <=", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupLike(String value) {
            addCriterion("store_group like", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotLike(String value) {
            addCriterion("store_group not like", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupIn(List<String> values) {
            addCriterion("store_group in", values, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotIn(List<String> values) {
            addCriterion("store_group not in", values, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupBetween(String value1, String value2) {
            addCriterion("store_group between", value1, value2, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotBetween(String value1, String value2) {
            addCriterion("store_group not between", value1, value2, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNull() {
            addCriterion("classone_name is null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNotNull() {
            addCriterion("classone_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameEqualTo(String value) {
            addCriterion("classone_name =", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotEqualTo(String value) {
            addCriterion("classone_name <>", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThan(String value) {
            addCriterion("classone_name >", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("classone_name >=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThan(String value) {
            addCriterion("classone_name <", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThanOrEqualTo(String value) {
            addCriterion("classone_name <=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLike(String value) {
            addCriterion("classone_name like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotLike(String value) {
            addCriterion("classone_name not like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIn(List<String> values) {
            addCriterion("classone_name in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotIn(List<String> values) {
            addCriterion("classone_name not in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameBetween(String value1, String value2) {
            addCriterion("classone_name between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotBetween(String value1, String value2) {
            addCriterion("classone_name not between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIsNull() {
            addCriterion("classtwo_name is null");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIsNotNull() {
            addCriterion("classtwo_name is not null");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameEqualTo(String value) {
            addCriterion("classtwo_name =", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotEqualTo(String value) {
            addCriterion("classtwo_name <>", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameGreaterThan(String value) {
            addCriterion("classtwo_name >", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameGreaterThanOrEqualTo(String value) {
            addCriterion("classtwo_name >=", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLessThan(String value) {
            addCriterion("classtwo_name <", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLessThanOrEqualTo(String value) {
            addCriterion("classtwo_name <=", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLike(String value) {
            addCriterion("classtwo_name like", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotLike(String value) {
            addCriterion("classtwo_name not like", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIn(List<String> values) {
            addCriterion("classtwo_name in", values, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotIn(List<String> values) {
            addCriterion("classtwo_name not in", values, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameBetween(String value1, String value2) {
            addCriterion("classtwo_name between", value1, value2, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotBetween(String value1, String value2) {
            addCriterion("classtwo_name not between", value1, value2, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIsNull() {
            addCriterion("classthree_name is null");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIsNotNull() {
            addCriterion("classthree_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameEqualTo(String value) {
            addCriterion("classthree_name =", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotEqualTo(String value) {
            addCriterion("classthree_name <>", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameGreaterThan(String value) {
            addCriterion("classthree_name >", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameGreaterThanOrEqualTo(String value) {
            addCriterion("classthree_name >=", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLessThan(String value) {
            addCriterion("classthree_name <", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLessThanOrEqualTo(String value) {
            addCriterion("classthree_name <=", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLike(String value) {
            addCriterion("classthree_name like", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotLike(String value) {
            addCriterion("classthree_name not like", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIn(List<String> values) {
            addCriterion("classthree_name in", values, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotIn(List<String> values) {
            addCriterion("classthree_name not in", values, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameBetween(String value1, String value2) {
            addCriterion("classthree_name between", value1, value2, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotBetween(String value1, String value2) {
            addCriterion("classthree_name not between", value1, value2, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIsNull() {
            addCriterion("classfour_name is null");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIsNotNull() {
            addCriterion("classfour_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassfourNameEqualTo(String value) {
            addCriterion("classfour_name =", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotEqualTo(String value) {
            addCriterion("classfour_name <>", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameGreaterThan(String value) {
            addCriterion("classfour_name >", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameGreaterThanOrEqualTo(String value) {
            addCriterion("classfour_name >=", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLessThan(String value) {
            addCriterion("classfour_name <", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLessThanOrEqualTo(String value) {
            addCriterion("classfour_name <=", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLike(String value) {
            addCriterion("classfour_name like", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotLike(String value) {
            addCriterion("classfour_name not like", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIn(List<String> values) {
            addCriterion("classfour_name in", values, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotIn(List<String> values) {
            addCriterion("classfour_name not in", values, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameBetween(String value1, String value2) {
            addCriterion("classfour_name between", value1, value2, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotBetween(String value1, String value2) {
            addCriterion("classfour_name not between", value1, value2, "classfourName");
            return (Criteria) this;
        }

        public Criteria andComponentIsNull() {
            addCriterion("component is null");
            return (Criteria) this;
        }

        public Criteria andComponentIsNotNull() {
            addCriterion("component is not null");
            return (Criteria) this;
        }

        public Criteria andComponentEqualTo(String value) {
            addCriterion("component =", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotEqualTo(String value) {
            addCriterion("component <>", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThan(String value) {
            addCriterion("component >", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThanOrEqualTo(String value) {
            addCriterion("component >=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThan(String value) {
            addCriterion("component <", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThanOrEqualTo(String value) {
            addCriterion("component <=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLike(String value) {
            addCriterion("component like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotLike(String value) {
            addCriterion("component not like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentIn(List<String> values) {
            addCriterion("component in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotIn(List<String> values) {
            addCriterion("component not in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentBetween(String value1, String value2) {
            addCriterion("component between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotBetween(String value1, String value2) {
            addCriterion("component not between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andSkuCntIsNull() {
            addCriterion("sku_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntIsNotNull() {
            addCriterion("sku_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntEqualTo(String value) {
            addCriterion("sku_cnt =", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotEqualTo(String value) {
            addCriterion("sku_cnt <>", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntGreaterThan(String value) {
            addCriterion("sku_cnt >", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt >=", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntLessThan(String value) {
            addCriterion("sku_cnt <", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt <=", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntLike(String value) {
            addCriterion("sku_cnt like", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotLike(String value) {
            addCriterion("sku_cnt not like", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntIn(List<String> values) {
            addCriterion("sku_cnt in", values, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotIn(List<String> values) {
            addCriterion("sku_cnt not in", values, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntBetween(String value1, String value2) {
            addCriterion("sku_cnt between", value1, value2, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotBetween(String value1, String value2) {
            addCriterion("sku_cnt not between", value1, value2, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2IsNull() {
            addCriterion("sku_cnt_total_2 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2IsNotNull() {
            addCriterion("sku_cnt_total_2 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2EqualTo(String value) {
            addCriterion("sku_cnt_total_2 =", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2NotEqualTo(String value) {
            addCriterion("sku_cnt_total_2 <>", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2GreaterThan(String value) {
            addCriterion("sku_cnt_total_2 >", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_2 >=", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2LessThan(String value) {
            addCriterion("sku_cnt_total_2 <", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_2 <=", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2Like(String value) {
            addCriterion("sku_cnt_total_2 like", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2NotLike(String value) {
            addCriterion("sku_cnt_total_2 not like", value, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2In(List<String> values) {
            addCriterion("sku_cnt_total_2 in", values, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2NotIn(List<String> values) {
            addCriterion("sku_cnt_total_2 not in", values, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2Between(String value1, String value2) {
            addCriterion("sku_cnt_total_2 between", value1, value2, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal2NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_2 not between", value1, value2, "skuCntTotal2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2IsNull() {
            addCriterion("sku_cnt_2 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2IsNotNull() {
            addCriterion("sku_cnt_2 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2EqualTo(String value) {
            addCriterion("sku_cnt_2 =", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2NotEqualTo(String value) {
            addCriterion("sku_cnt_2 <>", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2GreaterThan(String value) {
            addCriterion("sku_cnt_2 >", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_2 >=", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2LessThan(String value) {
            addCriterion("sku_cnt_2 <", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_2 <=", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2Like(String value) {
            addCriterion("sku_cnt_2 like", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2NotLike(String value) {
            addCriterion("sku_cnt_2 not like", value, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2In(List<String> values) {
            addCriterion("sku_cnt_2 in", values, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2NotIn(List<String> values) {
            addCriterion("sku_cnt_2 not in", values, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2Between(String value1, String value2) {
            addCriterion("sku_cnt_2 between", value1, value2, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCnt2NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_2 not between", value1, value2, "skuCnt2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2IsNull() {
            addCriterion("sku_cnt_new_2 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2IsNotNull() {
            addCriterion("sku_cnt_new_2 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2EqualTo(String value) {
            addCriterion("sku_cnt_new_2 =", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2NotEqualTo(String value) {
            addCriterion("sku_cnt_new_2 <>", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2GreaterThan(String value) {
            addCriterion("sku_cnt_new_2 >", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_2 >=", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2LessThan(String value) {
            addCriterion("sku_cnt_new_2 <", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_2 <=", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2Like(String value) {
            addCriterion("sku_cnt_new_2 like", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2NotLike(String value) {
            addCriterion("sku_cnt_new_2 not like", value, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2In(List<String> values) {
            addCriterion("sku_cnt_new_2 in", values, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2NotIn(List<String> values) {
            addCriterion("sku_cnt_new_2 not in", values, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2Between(String value1, String value2) {
            addCriterion("sku_cnt_new_2 between", value1, value2, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew2NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_2 not between", value1, value2, "skuCntNew2");
            return (Criteria) this;
        }

        public Criteria andNumCum302IsNull() {
            addCriterion("num_cum_30_2 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum302IsNotNull() {
            addCriterion("num_cum_30_2 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum302EqualTo(String value) {
            addCriterion("num_cum_30_2 =", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302NotEqualTo(String value) {
            addCriterion("num_cum_30_2 <>", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302GreaterThan(String value) {
            addCriterion("num_cum_30_2 >", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_2 >=", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302LessThan(String value) {
            addCriterion("num_cum_30_2 <", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_2 <=", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302Like(String value) {
            addCriterion("num_cum_30_2 like", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302NotLike(String value) {
            addCriterion("num_cum_30_2 not like", value, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302In(List<String> values) {
            addCriterion("num_cum_30_2 in", values, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302NotIn(List<String> values) {
            addCriterion("num_cum_30_2 not in", values, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302Between(String value1, String value2) {
            addCriterion("num_cum_30_2 between", value1, value2, "numCum302");
            return (Criteria) this;
        }

        public Criteria andNumCum302NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_2 not between", value1, value2, "numCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302IsNull() {
            addCriterion("amt_cum_30_2 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum302IsNotNull() {
            addCriterion("amt_cum_30_2 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum302EqualTo(String value) {
            addCriterion("amt_cum_30_2 =", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302NotEqualTo(String value) {
            addCriterion("amt_cum_30_2 <>", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302GreaterThan(String value) {
            addCriterion("amt_cum_30_2 >", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_2 >=", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302LessThan(String value) {
            addCriterion("amt_cum_30_2 <", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_2 <=", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302Like(String value) {
            addCriterion("amt_cum_30_2 like", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302NotLike(String value) {
            addCriterion("amt_cum_30_2 not like", value, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302In(List<String> values) {
            addCriterion("amt_cum_30_2 in", values, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302NotIn(List<String> values) {
            addCriterion("amt_cum_30_2 not in", values, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302Between(String value1, String value2) {
            addCriterion("amt_cum_30_2 between", value1, value2, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtCum302NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_2 not between", value1, value2, "amtCum302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302IsNull() {
            addCriterion("amt_rate_30_2 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate302IsNotNull() {
            addCriterion("amt_rate_30_2 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate302EqualTo(String value) {
            addCriterion("amt_rate_30_2 =", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302NotEqualTo(String value) {
            addCriterion("amt_rate_30_2 <>", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302GreaterThan(String value) {
            addCriterion("amt_rate_30_2 >", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_2 >=", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302LessThan(String value) {
            addCriterion("amt_rate_30_2 <", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_2 <=", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302Like(String value) {
            addCriterion("amt_rate_30_2 like", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302NotLike(String value) {
            addCriterion("amt_rate_30_2 not like", value, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302In(List<String> values) {
            addCriterion("amt_rate_30_2 in", values, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302NotIn(List<String> values) {
            addCriterion("amt_rate_30_2 not in", values, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302Between(String value1, String value2) {
            addCriterion("amt_rate_30_2 between", value1, value2, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andAmtRate302NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_2 not between", value1, value2, "amtRate302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302IsNull() {
            addCriterion("profit_amt_cum_30_2 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302IsNotNull() {
            addCriterion("profit_amt_cum_30_2 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302EqualTo(String value) {
            addCriterion("profit_amt_cum_30_2 =", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_2 <>", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_2 >", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_2 >=", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302LessThan(String value) {
            addCriterion("profit_amt_cum_30_2 <", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_2 <=", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302Like(String value) {
            addCriterion("profit_amt_cum_30_2 like", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302NotLike(String value) {
            addCriterion("profit_amt_cum_30_2 not like", value, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302In(List<String> values) {
            addCriterion("profit_amt_cum_30_2 in", values, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_2 not in", values, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_2 between", value1, value2, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum302NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_2 not between", value1, value2, "profitAmtCum302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302IsNull() {
            addCriterion("profit_rate_30_2 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate302IsNotNull() {
            addCriterion("profit_rate_30_2 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate302EqualTo(String value) {
            addCriterion("profit_rate_30_2 =", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302NotEqualTo(String value) {
            addCriterion("profit_rate_30_2 <>", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302GreaterThan(String value) {
            addCriterion("profit_rate_30_2 >", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_2 >=", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302LessThan(String value) {
            addCriterion("profit_rate_30_2 <", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_2 <=", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302Like(String value) {
            addCriterion("profit_rate_30_2 like", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302NotLike(String value) {
            addCriterion("profit_rate_30_2 not like", value, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302In(List<String> values) {
            addCriterion("profit_rate_30_2 in", values, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302NotIn(List<String> values) {
            addCriterion("profit_rate_30_2 not in", values, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302Between(String value1, String value2) {
            addCriterion("profit_rate_30_2 between", value1, value2, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andProfitRate302NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_2 not between", value1, value2, "profitRate302");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2IsNull() {
            addCriterion("amt_inv_new_2 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2IsNotNull() {
            addCriterion("amt_inv_new_2 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2EqualTo(String value) {
            addCriterion("amt_inv_new_2 =", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2NotEqualTo(String value) {
            addCriterion("amt_inv_new_2 <>", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2GreaterThan(String value) {
            addCriterion("amt_inv_new_2 >", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_2 >=", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2LessThan(String value) {
            addCriterion("amt_inv_new_2 <", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_2 <=", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2Like(String value) {
            addCriterion("amt_inv_new_2 like", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2NotLike(String value) {
            addCriterion("amt_inv_new_2 not like", value, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2In(List<String> values) {
            addCriterion("amt_inv_new_2 in", values, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2NotIn(List<String> values) {
            addCriterion("amt_inv_new_2 not in", values, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2Between(String value1, String value2) {
            addCriterion("amt_inv_new_2 between", value1, value2, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew2NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_2 not between", value1, value2, "amtInvNew2");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3IsNull() {
            addCriterion("sku_cnt_total_3 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3IsNotNull() {
            addCriterion("sku_cnt_total_3 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3EqualTo(String value) {
            addCriterion("sku_cnt_total_3 =", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3NotEqualTo(String value) {
            addCriterion("sku_cnt_total_3 <>", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3GreaterThan(String value) {
            addCriterion("sku_cnt_total_3 >", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_3 >=", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3LessThan(String value) {
            addCriterion("sku_cnt_total_3 <", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_3 <=", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3Like(String value) {
            addCriterion("sku_cnt_total_3 like", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3NotLike(String value) {
            addCriterion("sku_cnt_total_3 not like", value, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3In(List<String> values) {
            addCriterion("sku_cnt_total_3 in", values, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3NotIn(List<String> values) {
            addCriterion("sku_cnt_total_3 not in", values, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3Between(String value1, String value2) {
            addCriterion("sku_cnt_total_3 between", value1, value2, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal3NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_3 not between", value1, value2, "skuCntTotal3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3IsNull() {
            addCriterion("sku_cnt_3 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3IsNotNull() {
            addCriterion("sku_cnt_3 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3EqualTo(String value) {
            addCriterion("sku_cnt_3 =", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3NotEqualTo(String value) {
            addCriterion("sku_cnt_3 <>", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3GreaterThan(String value) {
            addCriterion("sku_cnt_3 >", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_3 >=", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3LessThan(String value) {
            addCriterion("sku_cnt_3 <", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_3 <=", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3Like(String value) {
            addCriterion("sku_cnt_3 like", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3NotLike(String value) {
            addCriterion("sku_cnt_3 not like", value, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3In(List<String> values) {
            addCriterion("sku_cnt_3 in", values, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3NotIn(List<String> values) {
            addCriterion("sku_cnt_3 not in", values, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3Between(String value1, String value2) {
            addCriterion("sku_cnt_3 between", value1, value2, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCnt3NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_3 not between", value1, value2, "skuCnt3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3IsNull() {
            addCriterion("sku_cnt_new_3 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3IsNotNull() {
            addCriterion("sku_cnt_new_3 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3EqualTo(String value) {
            addCriterion("sku_cnt_new_3 =", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3NotEqualTo(String value) {
            addCriterion("sku_cnt_new_3 <>", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3GreaterThan(String value) {
            addCriterion("sku_cnt_new_3 >", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_3 >=", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3LessThan(String value) {
            addCriterion("sku_cnt_new_3 <", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_3 <=", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3Like(String value) {
            addCriterion("sku_cnt_new_3 like", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3NotLike(String value) {
            addCriterion("sku_cnt_new_3 not like", value, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3In(List<String> values) {
            addCriterion("sku_cnt_new_3 in", values, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3NotIn(List<String> values) {
            addCriterion("sku_cnt_new_3 not in", values, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3Between(String value1, String value2) {
            addCriterion("sku_cnt_new_3 between", value1, value2, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew3NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_3 not between", value1, value2, "skuCntNew3");
            return (Criteria) this;
        }

        public Criteria andNumCum303IsNull() {
            addCriterion("num_cum_30_3 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum303IsNotNull() {
            addCriterion("num_cum_30_3 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum303EqualTo(String value) {
            addCriterion("num_cum_30_3 =", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303NotEqualTo(String value) {
            addCriterion("num_cum_30_3 <>", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303GreaterThan(String value) {
            addCriterion("num_cum_30_3 >", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_3 >=", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303LessThan(String value) {
            addCriterion("num_cum_30_3 <", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_3 <=", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303Like(String value) {
            addCriterion("num_cum_30_3 like", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303NotLike(String value) {
            addCriterion("num_cum_30_3 not like", value, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303In(List<String> values) {
            addCriterion("num_cum_30_3 in", values, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303NotIn(List<String> values) {
            addCriterion("num_cum_30_3 not in", values, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303Between(String value1, String value2) {
            addCriterion("num_cum_30_3 between", value1, value2, "numCum303");
            return (Criteria) this;
        }

        public Criteria andNumCum303NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_3 not between", value1, value2, "numCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303IsNull() {
            addCriterion("amt_cum_30_3 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum303IsNotNull() {
            addCriterion("amt_cum_30_3 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum303EqualTo(String value) {
            addCriterion("amt_cum_30_3 =", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303NotEqualTo(String value) {
            addCriterion("amt_cum_30_3 <>", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303GreaterThan(String value) {
            addCriterion("amt_cum_30_3 >", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_3 >=", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303LessThan(String value) {
            addCriterion("amt_cum_30_3 <", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_3 <=", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303Like(String value) {
            addCriterion("amt_cum_30_3 like", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303NotLike(String value) {
            addCriterion("amt_cum_30_3 not like", value, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303In(List<String> values) {
            addCriterion("amt_cum_30_3 in", values, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303NotIn(List<String> values) {
            addCriterion("amt_cum_30_3 not in", values, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303Between(String value1, String value2) {
            addCriterion("amt_cum_30_3 between", value1, value2, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtCum303NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_3 not between", value1, value2, "amtCum303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303IsNull() {
            addCriterion("amt_rate_30_3 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate303IsNotNull() {
            addCriterion("amt_rate_30_3 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate303EqualTo(String value) {
            addCriterion("amt_rate_30_3 =", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303NotEqualTo(String value) {
            addCriterion("amt_rate_30_3 <>", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303GreaterThan(String value) {
            addCriterion("amt_rate_30_3 >", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_3 >=", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303LessThan(String value) {
            addCriterion("amt_rate_30_3 <", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_3 <=", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303Like(String value) {
            addCriterion("amt_rate_30_3 like", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303NotLike(String value) {
            addCriterion("amt_rate_30_3 not like", value, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303In(List<String> values) {
            addCriterion("amt_rate_30_3 in", values, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303NotIn(List<String> values) {
            addCriterion("amt_rate_30_3 not in", values, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303Between(String value1, String value2) {
            addCriterion("amt_rate_30_3 between", value1, value2, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andAmtRate303NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_3 not between", value1, value2, "amtRate303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303IsNull() {
            addCriterion("profit_amt_cum_30_3 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303IsNotNull() {
            addCriterion("profit_amt_cum_30_3 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303EqualTo(String value) {
            addCriterion("profit_amt_cum_30_3 =", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_3 <>", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_3 >", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_3 >=", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303LessThan(String value) {
            addCriterion("profit_amt_cum_30_3 <", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_3 <=", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303Like(String value) {
            addCriterion("profit_amt_cum_30_3 like", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303NotLike(String value) {
            addCriterion("profit_amt_cum_30_3 not like", value, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303In(List<String> values) {
            addCriterion("profit_amt_cum_30_3 in", values, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_3 not in", values, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_3 between", value1, value2, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum303NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_3 not between", value1, value2, "profitAmtCum303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303IsNull() {
            addCriterion("profit_rate_30_3 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate303IsNotNull() {
            addCriterion("profit_rate_30_3 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate303EqualTo(String value) {
            addCriterion("profit_rate_30_3 =", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303NotEqualTo(String value) {
            addCriterion("profit_rate_30_3 <>", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303GreaterThan(String value) {
            addCriterion("profit_rate_30_3 >", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_3 >=", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303LessThan(String value) {
            addCriterion("profit_rate_30_3 <", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_3 <=", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303Like(String value) {
            addCriterion("profit_rate_30_3 like", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303NotLike(String value) {
            addCriterion("profit_rate_30_3 not like", value, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303In(List<String> values) {
            addCriterion("profit_rate_30_3 in", values, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303NotIn(List<String> values) {
            addCriterion("profit_rate_30_3 not in", values, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303Between(String value1, String value2) {
            addCriterion("profit_rate_30_3 between", value1, value2, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andProfitRate303NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_3 not between", value1, value2, "profitRate303");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3IsNull() {
            addCriterion("amt_inv_new_3 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3IsNotNull() {
            addCriterion("amt_inv_new_3 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3EqualTo(String value) {
            addCriterion("amt_inv_new_3 =", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3NotEqualTo(String value) {
            addCriterion("amt_inv_new_3 <>", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3GreaterThan(String value) {
            addCriterion("amt_inv_new_3 >", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_3 >=", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3LessThan(String value) {
            addCriterion("amt_inv_new_3 <", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_3 <=", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3Like(String value) {
            addCriterion("amt_inv_new_3 like", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3NotLike(String value) {
            addCriterion("amt_inv_new_3 not like", value, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3In(List<String> values) {
            addCriterion("amt_inv_new_3 in", values, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3NotIn(List<String> values) {
            addCriterion("amt_inv_new_3 not in", values, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3Between(String value1, String value2) {
            addCriterion("amt_inv_new_3 between", value1, value2, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew3NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_3 not between", value1, value2, "amtInvNew3");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4IsNull() {
            addCriterion("sku_cnt_total_4 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4IsNotNull() {
            addCriterion("sku_cnt_total_4 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4EqualTo(String value) {
            addCriterion("sku_cnt_total_4 =", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4NotEqualTo(String value) {
            addCriterion("sku_cnt_total_4 <>", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4GreaterThan(String value) {
            addCriterion("sku_cnt_total_4 >", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_4 >=", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4LessThan(String value) {
            addCriterion("sku_cnt_total_4 <", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_4 <=", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4Like(String value) {
            addCriterion("sku_cnt_total_4 like", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4NotLike(String value) {
            addCriterion("sku_cnt_total_4 not like", value, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4In(List<String> values) {
            addCriterion("sku_cnt_total_4 in", values, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4NotIn(List<String> values) {
            addCriterion("sku_cnt_total_4 not in", values, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4Between(String value1, String value2) {
            addCriterion("sku_cnt_total_4 between", value1, value2, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal4NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_4 not between", value1, value2, "skuCntTotal4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4IsNull() {
            addCriterion("sku_cnt_4 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4IsNotNull() {
            addCriterion("sku_cnt_4 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4EqualTo(String value) {
            addCriterion("sku_cnt_4 =", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4NotEqualTo(String value) {
            addCriterion("sku_cnt_4 <>", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4GreaterThan(String value) {
            addCriterion("sku_cnt_4 >", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_4 >=", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4LessThan(String value) {
            addCriterion("sku_cnt_4 <", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_4 <=", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4Like(String value) {
            addCriterion("sku_cnt_4 like", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4NotLike(String value) {
            addCriterion("sku_cnt_4 not like", value, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4In(List<String> values) {
            addCriterion("sku_cnt_4 in", values, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4NotIn(List<String> values) {
            addCriterion("sku_cnt_4 not in", values, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4Between(String value1, String value2) {
            addCriterion("sku_cnt_4 between", value1, value2, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCnt4NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_4 not between", value1, value2, "skuCnt4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4IsNull() {
            addCriterion("sku_cnt_new_4 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4IsNotNull() {
            addCriterion("sku_cnt_new_4 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4EqualTo(String value) {
            addCriterion("sku_cnt_new_4 =", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4NotEqualTo(String value) {
            addCriterion("sku_cnt_new_4 <>", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4GreaterThan(String value) {
            addCriterion("sku_cnt_new_4 >", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_4 >=", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4LessThan(String value) {
            addCriterion("sku_cnt_new_4 <", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_4 <=", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4Like(String value) {
            addCriterion("sku_cnt_new_4 like", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4NotLike(String value) {
            addCriterion("sku_cnt_new_4 not like", value, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4In(List<String> values) {
            addCriterion("sku_cnt_new_4 in", values, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4NotIn(List<String> values) {
            addCriterion("sku_cnt_new_4 not in", values, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4Between(String value1, String value2) {
            addCriterion("sku_cnt_new_4 between", value1, value2, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew4NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_4 not between", value1, value2, "skuCntNew4");
            return (Criteria) this;
        }

        public Criteria andNumCum304IsNull() {
            addCriterion("num_cum_30_4 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum304IsNotNull() {
            addCriterion("num_cum_30_4 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum304EqualTo(String value) {
            addCriterion("num_cum_30_4 =", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304NotEqualTo(String value) {
            addCriterion("num_cum_30_4 <>", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304GreaterThan(String value) {
            addCriterion("num_cum_30_4 >", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_4 >=", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304LessThan(String value) {
            addCriterion("num_cum_30_4 <", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_4 <=", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304Like(String value) {
            addCriterion("num_cum_30_4 like", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304NotLike(String value) {
            addCriterion("num_cum_30_4 not like", value, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304In(List<String> values) {
            addCriterion("num_cum_30_4 in", values, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304NotIn(List<String> values) {
            addCriterion("num_cum_30_4 not in", values, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304Between(String value1, String value2) {
            addCriterion("num_cum_30_4 between", value1, value2, "numCum304");
            return (Criteria) this;
        }

        public Criteria andNumCum304NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_4 not between", value1, value2, "numCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304IsNull() {
            addCriterion("amt_cum_30_4 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum304IsNotNull() {
            addCriterion("amt_cum_30_4 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum304EqualTo(String value) {
            addCriterion("amt_cum_30_4 =", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304NotEqualTo(String value) {
            addCriterion("amt_cum_30_4 <>", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304GreaterThan(String value) {
            addCriterion("amt_cum_30_4 >", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_4 >=", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304LessThan(String value) {
            addCriterion("amt_cum_30_4 <", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_4 <=", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304Like(String value) {
            addCriterion("amt_cum_30_4 like", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304NotLike(String value) {
            addCriterion("amt_cum_30_4 not like", value, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304In(List<String> values) {
            addCriterion("amt_cum_30_4 in", values, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304NotIn(List<String> values) {
            addCriterion("amt_cum_30_4 not in", values, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304Between(String value1, String value2) {
            addCriterion("amt_cum_30_4 between", value1, value2, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtCum304NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_4 not between", value1, value2, "amtCum304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304IsNull() {
            addCriterion("amt_rate_30_4 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate304IsNotNull() {
            addCriterion("amt_rate_30_4 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate304EqualTo(String value) {
            addCriterion("amt_rate_30_4 =", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304NotEqualTo(String value) {
            addCriterion("amt_rate_30_4 <>", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304GreaterThan(String value) {
            addCriterion("amt_rate_30_4 >", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_4 >=", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304LessThan(String value) {
            addCriterion("amt_rate_30_4 <", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_4 <=", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304Like(String value) {
            addCriterion("amt_rate_30_4 like", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304NotLike(String value) {
            addCriterion("amt_rate_30_4 not like", value, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304In(List<String> values) {
            addCriterion("amt_rate_30_4 in", values, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304NotIn(List<String> values) {
            addCriterion("amt_rate_30_4 not in", values, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304Between(String value1, String value2) {
            addCriterion("amt_rate_30_4 between", value1, value2, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andAmtRate304NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_4 not between", value1, value2, "amtRate304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304IsNull() {
            addCriterion("profit_amt_cum_30_4 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304IsNotNull() {
            addCriterion("profit_amt_cum_30_4 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304EqualTo(String value) {
            addCriterion("profit_amt_cum_30_4 =", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_4 <>", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_4 >", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_4 >=", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304LessThan(String value) {
            addCriterion("profit_amt_cum_30_4 <", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_4 <=", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304Like(String value) {
            addCriterion("profit_amt_cum_30_4 like", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304NotLike(String value) {
            addCriterion("profit_amt_cum_30_4 not like", value, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304In(List<String> values) {
            addCriterion("profit_amt_cum_30_4 in", values, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_4 not in", values, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_4 between", value1, value2, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum304NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_4 not between", value1, value2, "profitAmtCum304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304IsNull() {
            addCriterion("profit_rate_30_4 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate304IsNotNull() {
            addCriterion("profit_rate_30_4 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate304EqualTo(String value) {
            addCriterion("profit_rate_30_4 =", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304NotEqualTo(String value) {
            addCriterion("profit_rate_30_4 <>", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304GreaterThan(String value) {
            addCriterion("profit_rate_30_4 >", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_4 >=", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304LessThan(String value) {
            addCriterion("profit_rate_30_4 <", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_4 <=", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304Like(String value) {
            addCriterion("profit_rate_30_4 like", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304NotLike(String value) {
            addCriterion("profit_rate_30_4 not like", value, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304In(List<String> values) {
            addCriterion("profit_rate_30_4 in", values, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304NotIn(List<String> values) {
            addCriterion("profit_rate_30_4 not in", values, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304Between(String value1, String value2) {
            addCriterion("profit_rate_30_4 between", value1, value2, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andProfitRate304NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_4 not between", value1, value2, "profitRate304");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4IsNull() {
            addCriterion("amt_inv_new_4 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4IsNotNull() {
            addCriterion("amt_inv_new_4 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4EqualTo(String value) {
            addCriterion("amt_inv_new_4 =", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4NotEqualTo(String value) {
            addCriterion("amt_inv_new_4 <>", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4GreaterThan(String value) {
            addCriterion("amt_inv_new_4 >", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_4 >=", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4LessThan(String value) {
            addCriterion("amt_inv_new_4 <", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_4 <=", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4Like(String value) {
            addCriterion("amt_inv_new_4 like", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4NotLike(String value) {
            addCriterion("amt_inv_new_4 not like", value, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4In(List<String> values) {
            addCriterion("amt_inv_new_4 in", values, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4NotIn(List<String> values) {
            addCriterion("amt_inv_new_4 not in", values, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4Between(String value1, String value2) {
            addCriterion("amt_inv_new_4 between", value1, value2, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew4NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_4 not between", value1, value2, "amtInvNew4");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5IsNull() {
            addCriterion("sku_cnt_total_5 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5IsNotNull() {
            addCriterion("sku_cnt_total_5 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5EqualTo(String value) {
            addCriterion("sku_cnt_total_5 =", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5NotEqualTo(String value) {
            addCriterion("sku_cnt_total_5 <>", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5GreaterThan(String value) {
            addCriterion("sku_cnt_total_5 >", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_5 >=", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5LessThan(String value) {
            addCriterion("sku_cnt_total_5 <", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_5 <=", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5Like(String value) {
            addCriterion("sku_cnt_total_5 like", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5NotLike(String value) {
            addCriterion("sku_cnt_total_5 not like", value, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5In(List<String> values) {
            addCriterion("sku_cnt_total_5 in", values, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5NotIn(List<String> values) {
            addCriterion("sku_cnt_total_5 not in", values, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5Between(String value1, String value2) {
            addCriterion("sku_cnt_total_5 between", value1, value2, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal5NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_5 not between", value1, value2, "skuCntTotal5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5IsNull() {
            addCriterion("sku_cnt_5 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5IsNotNull() {
            addCriterion("sku_cnt_5 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5EqualTo(String value) {
            addCriterion("sku_cnt_5 =", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5NotEqualTo(String value) {
            addCriterion("sku_cnt_5 <>", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5GreaterThan(String value) {
            addCriterion("sku_cnt_5 >", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_5 >=", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5LessThan(String value) {
            addCriterion("sku_cnt_5 <", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_5 <=", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5Like(String value) {
            addCriterion("sku_cnt_5 like", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5NotLike(String value) {
            addCriterion("sku_cnt_5 not like", value, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5In(List<String> values) {
            addCriterion("sku_cnt_5 in", values, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5NotIn(List<String> values) {
            addCriterion("sku_cnt_5 not in", values, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5Between(String value1, String value2) {
            addCriterion("sku_cnt_5 between", value1, value2, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCnt5NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_5 not between", value1, value2, "skuCnt5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5IsNull() {
            addCriterion("sku_cnt_new_5 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5IsNotNull() {
            addCriterion("sku_cnt_new_5 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5EqualTo(String value) {
            addCriterion("sku_cnt_new_5 =", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5NotEqualTo(String value) {
            addCriterion("sku_cnt_new_5 <>", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5GreaterThan(String value) {
            addCriterion("sku_cnt_new_5 >", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_5 >=", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5LessThan(String value) {
            addCriterion("sku_cnt_new_5 <", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_5 <=", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5Like(String value) {
            addCriterion("sku_cnt_new_5 like", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5NotLike(String value) {
            addCriterion("sku_cnt_new_5 not like", value, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5In(List<String> values) {
            addCriterion("sku_cnt_new_5 in", values, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5NotIn(List<String> values) {
            addCriterion("sku_cnt_new_5 not in", values, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5Between(String value1, String value2) {
            addCriterion("sku_cnt_new_5 between", value1, value2, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew5NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_5 not between", value1, value2, "skuCntNew5");
            return (Criteria) this;
        }

        public Criteria andNumCum305IsNull() {
            addCriterion("num_cum_30_5 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum305IsNotNull() {
            addCriterion("num_cum_30_5 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum305EqualTo(String value) {
            addCriterion("num_cum_30_5 =", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305NotEqualTo(String value) {
            addCriterion("num_cum_30_5 <>", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305GreaterThan(String value) {
            addCriterion("num_cum_30_5 >", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_5 >=", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305LessThan(String value) {
            addCriterion("num_cum_30_5 <", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_5 <=", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305Like(String value) {
            addCriterion("num_cum_30_5 like", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305NotLike(String value) {
            addCriterion("num_cum_30_5 not like", value, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305In(List<String> values) {
            addCriterion("num_cum_30_5 in", values, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305NotIn(List<String> values) {
            addCriterion("num_cum_30_5 not in", values, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305Between(String value1, String value2) {
            addCriterion("num_cum_30_5 between", value1, value2, "numCum305");
            return (Criteria) this;
        }

        public Criteria andNumCum305NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_5 not between", value1, value2, "numCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305IsNull() {
            addCriterion("amt_cum_30_5 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum305IsNotNull() {
            addCriterion("amt_cum_30_5 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum305EqualTo(String value) {
            addCriterion("amt_cum_30_5 =", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305NotEqualTo(String value) {
            addCriterion("amt_cum_30_5 <>", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305GreaterThan(String value) {
            addCriterion("amt_cum_30_5 >", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_5 >=", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305LessThan(String value) {
            addCriterion("amt_cum_30_5 <", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_5 <=", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305Like(String value) {
            addCriterion("amt_cum_30_5 like", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305NotLike(String value) {
            addCriterion("amt_cum_30_5 not like", value, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305In(List<String> values) {
            addCriterion("amt_cum_30_5 in", values, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305NotIn(List<String> values) {
            addCriterion("amt_cum_30_5 not in", values, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305Between(String value1, String value2) {
            addCriterion("amt_cum_30_5 between", value1, value2, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtCum305NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_5 not between", value1, value2, "amtCum305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305IsNull() {
            addCriterion("amt_rate_30_5 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate305IsNotNull() {
            addCriterion("amt_rate_30_5 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate305EqualTo(String value) {
            addCriterion("amt_rate_30_5 =", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305NotEqualTo(String value) {
            addCriterion("amt_rate_30_5 <>", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305GreaterThan(String value) {
            addCriterion("amt_rate_30_5 >", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_5 >=", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305LessThan(String value) {
            addCriterion("amt_rate_30_5 <", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_5 <=", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305Like(String value) {
            addCriterion("amt_rate_30_5 like", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305NotLike(String value) {
            addCriterion("amt_rate_30_5 not like", value, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305In(List<String> values) {
            addCriterion("amt_rate_30_5 in", values, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305NotIn(List<String> values) {
            addCriterion("amt_rate_30_5 not in", values, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305Between(String value1, String value2) {
            addCriterion("amt_rate_30_5 between", value1, value2, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andAmtRate305NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_5 not between", value1, value2, "amtRate305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305IsNull() {
            addCriterion("profit_amt_cum_30_5 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305IsNotNull() {
            addCriterion("profit_amt_cum_30_5 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305EqualTo(String value) {
            addCriterion("profit_amt_cum_30_5 =", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_5 <>", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_5 >", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_5 >=", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305LessThan(String value) {
            addCriterion("profit_amt_cum_30_5 <", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_5 <=", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305Like(String value) {
            addCriterion("profit_amt_cum_30_5 like", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305NotLike(String value) {
            addCriterion("profit_amt_cum_30_5 not like", value, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305In(List<String> values) {
            addCriterion("profit_amt_cum_30_5 in", values, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_5 not in", values, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_5 between", value1, value2, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum305NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_5 not between", value1, value2, "profitAmtCum305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305IsNull() {
            addCriterion("profit_rate_30_5 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate305IsNotNull() {
            addCriterion("profit_rate_30_5 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate305EqualTo(String value) {
            addCriterion("profit_rate_30_5 =", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305NotEqualTo(String value) {
            addCriterion("profit_rate_30_5 <>", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305GreaterThan(String value) {
            addCriterion("profit_rate_30_5 >", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_5 >=", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305LessThan(String value) {
            addCriterion("profit_rate_30_5 <", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_5 <=", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305Like(String value) {
            addCriterion("profit_rate_30_5 like", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305NotLike(String value) {
            addCriterion("profit_rate_30_5 not like", value, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305In(List<String> values) {
            addCriterion("profit_rate_30_5 in", values, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305NotIn(List<String> values) {
            addCriterion("profit_rate_30_5 not in", values, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305Between(String value1, String value2) {
            addCriterion("profit_rate_30_5 between", value1, value2, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andProfitRate305NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_5 not between", value1, value2, "profitRate305");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5IsNull() {
            addCriterion("amt_inv_new_5 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5IsNotNull() {
            addCriterion("amt_inv_new_5 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5EqualTo(String value) {
            addCriterion("amt_inv_new_5 =", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5NotEqualTo(String value) {
            addCriterion("amt_inv_new_5 <>", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5GreaterThan(String value) {
            addCriterion("amt_inv_new_5 >", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_5 >=", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5LessThan(String value) {
            addCriterion("amt_inv_new_5 <", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_5 <=", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5Like(String value) {
            addCriterion("amt_inv_new_5 like", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5NotLike(String value) {
            addCriterion("amt_inv_new_5 not like", value, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5In(List<String> values) {
            addCriterion("amt_inv_new_5 in", values, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5NotIn(List<String> values) {
            addCriterion("amt_inv_new_5 not in", values, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5Between(String value1, String value2) {
            addCriterion("amt_inv_new_5 between", value1, value2, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew5NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_5 not between", value1, value2, "amtInvNew5");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6IsNull() {
            addCriterion("sku_cnt_total_6 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6IsNotNull() {
            addCriterion("sku_cnt_total_6 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6EqualTo(String value) {
            addCriterion("sku_cnt_total_6 =", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6NotEqualTo(String value) {
            addCriterion("sku_cnt_total_6 <>", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6GreaterThan(String value) {
            addCriterion("sku_cnt_total_6 >", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_6 >=", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6LessThan(String value) {
            addCriterion("sku_cnt_total_6 <", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_6 <=", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6Like(String value) {
            addCriterion("sku_cnt_total_6 like", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6NotLike(String value) {
            addCriterion("sku_cnt_total_6 not like", value, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6In(List<String> values) {
            addCriterion("sku_cnt_total_6 in", values, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6NotIn(List<String> values) {
            addCriterion("sku_cnt_total_6 not in", values, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6Between(String value1, String value2) {
            addCriterion("sku_cnt_total_6 between", value1, value2, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal6NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_6 not between", value1, value2, "skuCntTotal6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6IsNull() {
            addCriterion("sku_cnt_6 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6IsNotNull() {
            addCriterion("sku_cnt_6 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6EqualTo(String value) {
            addCriterion("sku_cnt_6 =", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6NotEqualTo(String value) {
            addCriterion("sku_cnt_6 <>", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6GreaterThan(String value) {
            addCriterion("sku_cnt_6 >", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_6 >=", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6LessThan(String value) {
            addCriterion("sku_cnt_6 <", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_6 <=", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6Like(String value) {
            addCriterion("sku_cnt_6 like", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6NotLike(String value) {
            addCriterion("sku_cnt_6 not like", value, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6In(List<String> values) {
            addCriterion("sku_cnt_6 in", values, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6NotIn(List<String> values) {
            addCriterion("sku_cnt_6 not in", values, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6Between(String value1, String value2) {
            addCriterion("sku_cnt_6 between", value1, value2, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCnt6NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_6 not between", value1, value2, "skuCnt6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6IsNull() {
            addCriterion("sku_cnt_new_6 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6IsNotNull() {
            addCriterion("sku_cnt_new_6 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6EqualTo(String value) {
            addCriterion("sku_cnt_new_6 =", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6NotEqualTo(String value) {
            addCriterion("sku_cnt_new_6 <>", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6GreaterThan(String value) {
            addCriterion("sku_cnt_new_6 >", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_6 >=", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6LessThan(String value) {
            addCriterion("sku_cnt_new_6 <", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_6 <=", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6Like(String value) {
            addCriterion("sku_cnt_new_6 like", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6NotLike(String value) {
            addCriterion("sku_cnt_new_6 not like", value, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6In(List<String> values) {
            addCriterion("sku_cnt_new_6 in", values, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6NotIn(List<String> values) {
            addCriterion("sku_cnt_new_6 not in", values, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6Between(String value1, String value2) {
            addCriterion("sku_cnt_new_6 between", value1, value2, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew6NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_6 not between", value1, value2, "skuCntNew6");
            return (Criteria) this;
        }

        public Criteria andNumCum306IsNull() {
            addCriterion("num_cum_30_6 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum306IsNotNull() {
            addCriterion("num_cum_30_6 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum306EqualTo(String value) {
            addCriterion("num_cum_30_6 =", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306NotEqualTo(String value) {
            addCriterion("num_cum_30_6 <>", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306GreaterThan(String value) {
            addCriterion("num_cum_30_6 >", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_6 >=", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306LessThan(String value) {
            addCriterion("num_cum_30_6 <", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_6 <=", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306Like(String value) {
            addCriterion("num_cum_30_6 like", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306NotLike(String value) {
            addCriterion("num_cum_30_6 not like", value, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306In(List<String> values) {
            addCriterion("num_cum_30_6 in", values, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306NotIn(List<String> values) {
            addCriterion("num_cum_30_6 not in", values, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306Between(String value1, String value2) {
            addCriterion("num_cum_30_6 between", value1, value2, "numCum306");
            return (Criteria) this;
        }

        public Criteria andNumCum306NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_6 not between", value1, value2, "numCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306IsNull() {
            addCriterion("amt_cum_30_6 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum306IsNotNull() {
            addCriterion("amt_cum_30_6 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum306EqualTo(String value) {
            addCriterion("amt_cum_30_6 =", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306NotEqualTo(String value) {
            addCriterion("amt_cum_30_6 <>", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306GreaterThan(String value) {
            addCriterion("amt_cum_30_6 >", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_6 >=", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306LessThan(String value) {
            addCriterion("amt_cum_30_6 <", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_6 <=", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306Like(String value) {
            addCriterion("amt_cum_30_6 like", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306NotLike(String value) {
            addCriterion("amt_cum_30_6 not like", value, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306In(List<String> values) {
            addCriterion("amt_cum_30_6 in", values, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306NotIn(List<String> values) {
            addCriterion("amt_cum_30_6 not in", values, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306Between(String value1, String value2) {
            addCriterion("amt_cum_30_6 between", value1, value2, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtCum306NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_6 not between", value1, value2, "amtCum306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306IsNull() {
            addCriterion("amt_rate_30_6 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate306IsNotNull() {
            addCriterion("amt_rate_30_6 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate306EqualTo(String value) {
            addCriterion("amt_rate_30_6 =", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306NotEqualTo(String value) {
            addCriterion("amt_rate_30_6 <>", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306GreaterThan(String value) {
            addCriterion("amt_rate_30_6 >", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_6 >=", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306LessThan(String value) {
            addCriterion("amt_rate_30_6 <", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_6 <=", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306Like(String value) {
            addCriterion("amt_rate_30_6 like", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306NotLike(String value) {
            addCriterion("amt_rate_30_6 not like", value, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306In(List<String> values) {
            addCriterion("amt_rate_30_6 in", values, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306NotIn(List<String> values) {
            addCriterion("amt_rate_30_6 not in", values, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306Between(String value1, String value2) {
            addCriterion("amt_rate_30_6 between", value1, value2, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andAmtRate306NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_6 not between", value1, value2, "amtRate306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306IsNull() {
            addCriterion("profit_amt_cum_30_6 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306IsNotNull() {
            addCriterion("profit_amt_cum_30_6 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306EqualTo(String value) {
            addCriterion("profit_amt_cum_30_6 =", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_6 <>", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_6 >", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_6 >=", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306LessThan(String value) {
            addCriterion("profit_amt_cum_30_6 <", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_6 <=", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306Like(String value) {
            addCriterion("profit_amt_cum_30_6 like", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306NotLike(String value) {
            addCriterion("profit_amt_cum_30_6 not like", value, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306In(List<String> values) {
            addCriterion("profit_amt_cum_30_6 in", values, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_6 not in", values, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_6 between", value1, value2, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum306NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_6 not between", value1, value2, "profitAmtCum306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306IsNull() {
            addCriterion("profit_rate_30_6 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate306IsNotNull() {
            addCriterion("profit_rate_30_6 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate306EqualTo(String value) {
            addCriterion("profit_rate_30_6 =", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306NotEqualTo(String value) {
            addCriterion("profit_rate_30_6 <>", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306GreaterThan(String value) {
            addCriterion("profit_rate_30_6 >", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_6 >=", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306LessThan(String value) {
            addCriterion("profit_rate_30_6 <", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_6 <=", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306Like(String value) {
            addCriterion("profit_rate_30_6 like", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306NotLike(String value) {
            addCriterion("profit_rate_30_6 not like", value, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306In(List<String> values) {
            addCriterion("profit_rate_30_6 in", values, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306NotIn(List<String> values) {
            addCriterion("profit_rate_30_6 not in", values, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306Between(String value1, String value2) {
            addCriterion("profit_rate_30_6 between", value1, value2, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andProfitRate306NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_6 not between", value1, value2, "profitRate306");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6IsNull() {
            addCriterion("amt_inv_new_6 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6IsNotNull() {
            addCriterion("amt_inv_new_6 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6EqualTo(String value) {
            addCriterion("amt_inv_new_6 =", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6NotEqualTo(String value) {
            addCriterion("amt_inv_new_6 <>", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6GreaterThan(String value) {
            addCriterion("amt_inv_new_6 >", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_6 >=", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6LessThan(String value) {
            addCriterion("amt_inv_new_6 <", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_6 <=", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6Like(String value) {
            addCriterion("amt_inv_new_6 like", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6NotLike(String value) {
            addCriterion("amt_inv_new_6 not like", value, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6In(List<String> values) {
            addCriterion("amt_inv_new_6 in", values, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6NotIn(List<String> values) {
            addCriterion("amt_inv_new_6 not in", values, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6Between(String value1, String value2) {
            addCriterion("amt_inv_new_6 between", value1, value2, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew6NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_6 not between", value1, value2, "amtInvNew6");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7IsNull() {
            addCriterion("sku_cnt_total_7 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7IsNotNull() {
            addCriterion("sku_cnt_total_7 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7EqualTo(String value) {
            addCriterion("sku_cnt_total_7 =", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7NotEqualTo(String value) {
            addCriterion("sku_cnt_total_7 <>", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7GreaterThan(String value) {
            addCriterion("sku_cnt_total_7 >", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_7 >=", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7LessThan(String value) {
            addCriterion("sku_cnt_total_7 <", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_7 <=", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7Like(String value) {
            addCriterion("sku_cnt_total_7 like", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7NotLike(String value) {
            addCriterion("sku_cnt_total_7 not like", value, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7In(List<String> values) {
            addCriterion("sku_cnt_total_7 in", values, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7NotIn(List<String> values) {
            addCriterion("sku_cnt_total_7 not in", values, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7Between(String value1, String value2) {
            addCriterion("sku_cnt_total_7 between", value1, value2, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal7NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_7 not between", value1, value2, "skuCntTotal7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7IsNull() {
            addCriterion("sku_cnt_7 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7IsNotNull() {
            addCriterion("sku_cnt_7 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7EqualTo(String value) {
            addCriterion("sku_cnt_7 =", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7NotEqualTo(String value) {
            addCriterion("sku_cnt_7 <>", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7GreaterThan(String value) {
            addCriterion("sku_cnt_7 >", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_7 >=", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7LessThan(String value) {
            addCriterion("sku_cnt_7 <", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_7 <=", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7Like(String value) {
            addCriterion("sku_cnt_7 like", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7NotLike(String value) {
            addCriterion("sku_cnt_7 not like", value, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7In(List<String> values) {
            addCriterion("sku_cnt_7 in", values, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7NotIn(List<String> values) {
            addCriterion("sku_cnt_7 not in", values, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7Between(String value1, String value2) {
            addCriterion("sku_cnt_7 between", value1, value2, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCnt7NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_7 not between", value1, value2, "skuCnt7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7IsNull() {
            addCriterion("sku_cnt_new_7 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7IsNotNull() {
            addCriterion("sku_cnt_new_7 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7EqualTo(String value) {
            addCriterion("sku_cnt_new_7 =", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7NotEqualTo(String value) {
            addCriterion("sku_cnt_new_7 <>", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7GreaterThan(String value) {
            addCriterion("sku_cnt_new_7 >", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_7 >=", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7LessThan(String value) {
            addCriterion("sku_cnt_new_7 <", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_7 <=", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7Like(String value) {
            addCriterion("sku_cnt_new_7 like", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7NotLike(String value) {
            addCriterion("sku_cnt_new_7 not like", value, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7In(List<String> values) {
            addCriterion("sku_cnt_new_7 in", values, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7NotIn(List<String> values) {
            addCriterion("sku_cnt_new_7 not in", values, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7Between(String value1, String value2) {
            addCriterion("sku_cnt_new_7 between", value1, value2, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew7NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_7 not between", value1, value2, "skuCntNew7");
            return (Criteria) this;
        }

        public Criteria andNumCum307IsNull() {
            addCriterion("num_cum_30_7 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum307IsNotNull() {
            addCriterion("num_cum_30_7 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum307EqualTo(String value) {
            addCriterion("num_cum_30_7 =", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307NotEqualTo(String value) {
            addCriterion("num_cum_30_7 <>", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307GreaterThan(String value) {
            addCriterion("num_cum_30_7 >", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_7 >=", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307LessThan(String value) {
            addCriterion("num_cum_30_7 <", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_7 <=", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307Like(String value) {
            addCriterion("num_cum_30_7 like", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307NotLike(String value) {
            addCriterion("num_cum_30_7 not like", value, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307In(List<String> values) {
            addCriterion("num_cum_30_7 in", values, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307NotIn(List<String> values) {
            addCriterion("num_cum_30_7 not in", values, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307Between(String value1, String value2) {
            addCriterion("num_cum_30_7 between", value1, value2, "numCum307");
            return (Criteria) this;
        }

        public Criteria andNumCum307NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_7 not between", value1, value2, "numCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307IsNull() {
            addCriterion("amt_cum_30_7 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum307IsNotNull() {
            addCriterion("amt_cum_30_7 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum307EqualTo(String value) {
            addCriterion("amt_cum_30_7 =", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307NotEqualTo(String value) {
            addCriterion("amt_cum_30_7 <>", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307GreaterThan(String value) {
            addCriterion("amt_cum_30_7 >", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_7 >=", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307LessThan(String value) {
            addCriterion("amt_cum_30_7 <", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_7 <=", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307Like(String value) {
            addCriterion("amt_cum_30_7 like", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307NotLike(String value) {
            addCriterion("amt_cum_30_7 not like", value, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307In(List<String> values) {
            addCriterion("amt_cum_30_7 in", values, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307NotIn(List<String> values) {
            addCriterion("amt_cum_30_7 not in", values, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307Between(String value1, String value2) {
            addCriterion("amt_cum_30_7 between", value1, value2, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtCum307NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_7 not between", value1, value2, "amtCum307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307IsNull() {
            addCriterion("amt_rate_30_7 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate307IsNotNull() {
            addCriterion("amt_rate_30_7 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate307EqualTo(String value) {
            addCriterion("amt_rate_30_7 =", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307NotEqualTo(String value) {
            addCriterion("amt_rate_30_7 <>", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307GreaterThan(String value) {
            addCriterion("amt_rate_30_7 >", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_7 >=", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307LessThan(String value) {
            addCriterion("amt_rate_30_7 <", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_7 <=", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307Like(String value) {
            addCriterion("amt_rate_30_7 like", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307NotLike(String value) {
            addCriterion("amt_rate_30_7 not like", value, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307In(List<String> values) {
            addCriterion("amt_rate_30_7 in", values, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307NotIn(List<String> values) {
            addCriterion("amt_rate_30_7 not in", values, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307Between(String value1, String value2) {
            addCriterion("amt_rate_30_7 between", value1, value2, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andAmtRate307NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_7 not between", value1, value2, "amtRate307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307IsNull() {
            addCriterion("profit_amt_cum_30_7 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307IsNotNull() {
            addCriterion("profit_amt_cum_30_7 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307EqualTo(String value) {
            addCriterion("profit_amt_cum_30_7 =", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_7 <>", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_7 >", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_7 >=", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307LessThan(String value) {
            addCriterion("profit_amt_cum_30_7 <", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_7 <=", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307Like(String value) {
            addCriterion("profit_amt_cum_30_7 like", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307NotLike(String value) {
            addCriterion("profit_amt_cum_30_7 not like", value, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307In(List<String> values) {
            addCriterion("profit_amt_cum_30_7 in", values, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_7 not in", values, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_7 between", value1, value2, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum307NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_7 not between", value1, value2, "profitAmtCum307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307IsNull() {
            addCriterion("profit_rate_30_7 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate307IsNotNull() {
            addCriterion("profit_rate_30_7 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate307EqualTo(String value) {
            addCriterion("profit_rate_30_7 =", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307NotEqualTo(String value) {
            addCriterion("profit_rate_30_7 <>", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307GreaterThan(String value) {
            addCriterion("profit_rate_30_7 >", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_7 >=", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307LessThan(String value) {
            addCriterion("profit_rate_30_7 <", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_7 <=", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307Like(String value) {
            addCriterion("profit_rate_30_7 like", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307NotLike(String value) {
            addCriterion("profit_rate_30_7 not like", value, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307In(List<String> values) {
            addCriterion("profit_rate_30_7 in", values, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307NotIn(List<String> values) {
            addCriterion("profit_rate_30_7 not in", values, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307Between(String value1, String value2) {
            addCriterion("profit_rate_30_7 between", value1, value2, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andProfitRate307NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_7 not between", value1, value2, "profitRate307");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7IsNull() {
            addCriterion("amt_inv_new_7 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7IsNotNull() {
            addCriterion("amt_inv_new_7 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7EqualTo(String value) {
            addCriterion("amt_inv_new_7 =", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7NotEqualTo(String value) {
            addCriterion("amt_inv_new_7 <>", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7GreaterThan(String value) {
            addCriterion("amt_inv_new_7 >", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_7 >=", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7LessThan(String value) {
            addCriterion("amt_inv_new_7 <", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_7 <=", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7Like(String value) {
            addCriterion("amt_inv_new_7 like", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7NotLike(String value) {
            addCriterion("amt_inv_new_7 not like", value, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7In(List<String> values) {
            addCriterion("amt_inv_new_7 in", values, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7NotIn(List<String> values) {
            addCriterion("amt_inv_new_7 not in", values, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7Between(String value1, String value2) {
            addCriterion("amt_inv_new_7 between", value1, value2, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew7NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_7 not between", value1, value2, "amtInvNew7");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8IsNull() {
            addCriterion("sku_cnt_total_8 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8IsNotNull() {
            addCriterion("sku_cnt_total_8 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8EqualTo(String value) {
            addCriterion("sku_cnt_total_8 =", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8NotEqualTo(String value) {
            addCriterion("sku_cnt_total_8 <>", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8GreaterThan(String value) {
            addCriterion("sku_cnt_total_8 >", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_8 >=", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8LessThan(String value) {
            addCriterion("sku_cnt_total_8 <", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_8 <=", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8Like(String value) {
            addCriterion("sku_cnt_total_8 like", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8NotLike(String value) {
            addCriterion("sku_cnt_total_8 not like", value, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8In(List<String> values) {
            addCriterion("sku_cnt_total_8 in", values, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8NotIn(List<String> values) {
            addCriterion("sku_cnt_total_8 not in", values, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8Between(String value1, String value2) {
            addCriterion("sku_cnt_total_8 between", value1, value2, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal8NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_8 not between", value1, value2, "skuCntTotal8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8IsNull() {
            addCriterion("sku_cnt_8 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8IsNotNull() {
            addCriterion("sku_cnt_8 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8EqualTo(String value) {
            addCriterion("sku_cnt_8 =", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8NotEqualTo(String value) {
            addCriterion("sku_cnt_8 <>", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8GreaterThan(String value) {
            addCriterion("sku_cnt_8 >", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_8 >=", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8LessThan(String value) {
            addCriterion("sku_cnt_8 <", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_8 <=", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8Like(String value) {
            addCriterion("sku_cnt_8 like", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8NotLike(String value) {
            addCriterion("sku_cnt_8 not like", value, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8In(List<String> values) {
            addCriterion("sku_cnt_8 in", values, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8NotIn(List<String> values) {
            addCriterion("sku_cnt_8 not in", values, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8Between(String value1, String value2) {
            addCriterion("sku_cnt_8 between", value1, value2, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCnt8NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_8 not between", value1, value2, "skuCnt8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8IsNull() {
            addCriterion("sku_cnt_new_8 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8IsNotNull() {
            addCriterion("sku_cnt_new_8 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8EqualTo(String value) {
            addCriterion("sku_cnt_new_8 =", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8NotEqualTo(String value) {
            addCriterion("sku_cnt_new_8 <>", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8GreaterThan(String value) {
            addCriterion("sku_cnt_new_8 >", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_8 >=", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8LessThan(String value) {
            addCriterion("sku_cnt_new_8 <", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_8 <=", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8Like(String value) {
            addCriterion("sku_cnt_new_8 like", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8NotLike(String value) {
            addCriterion("sku_cnt_new_8 not like", value, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8In(List<String> values) {
            addCriterion("sku_cnt_new_8 in", values, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8NotIn(List<String> values) {
            addCriterion("sku_cnt_new_8 not in", values, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8Between(String value1, String value2) {
            addCriterion("sku_cnt_new_8 between", value1, value2, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew8NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_8 not between", value1, value2, "skuCntNew8");
            return (Criteria) this;
        }

        public Criteria andNumCum308IsNull() {
            addCriterion("num_cum_30_8 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum308IsNotNull() {
            addCriterion("num_cum_30_8 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum308EqualTo(String value) {
            addCriterion("num_cum_30_8 =", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308NotEqualTo(String value) {
            addCriterion("num_cum_30_8 <>", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308GreaterThan(String value) {
            addCriterion("num_cum_30_8 >", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_8 >=", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308LessThan(String value) {
            addCriterion("num_cum_30_8 <", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_8 <=", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308Like(String value) {
            addCriterion("num_cum_30_8 like", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308NotLike(String value) {
            addCriterion("num_cum_30_8 not like", value, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308In(List<String> values) {
            addCriterion("num_cum_30_8 in", values, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308NotIn(List<String> values) {
            addCriterion("num_cum_30_8 not in", values, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308Between(String value1, String value2) {
            addCriterion("num_cum_30_8 between", value1, value2, "numCum308");
            return (Criteria) this;
        }

        public Criteria andNumCum308NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_8 not between", value1, value2, "numCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308IsNull() {
            addCriterion("amt_cum_30_8 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum308IsNotNull() {
            addCriterion("amt_cum_30_8 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum308EqualTo(String value) {
            addCriterion("amt_cum_30_8 =", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308NotEqualTo(String value) {
            addCriterion("amt_cum_30_8 <>", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308GreaterThan(String value) {
            addCriterion("amt_cum_30_8 >", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_8 >=", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308LessThan(String value) {
            addCriterion("amt_cum_30_8 <", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_8 <=", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308Like(String value) {
            addCriterion("amt_cum_30_8 like", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308NotLike(String value) {
            addCriterion("amt_cum_30_8 not like", value, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308In(List<String> values) {
            addCriterion("amt_cum_30_8 in", values, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308NotIn(List<String> values) {
            addCriterion("amt_cum_30_8 not in", values, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308Between(String value1, String value2) {
            addCriterion("amt_cum_30_8 between", value1, value2, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtCum308NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_8 not between", value1, value2, "amtCum308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308IsNull() {
            addCriterion("amt_rate_30_8 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate308IsNotNull() {
            addCriterion("amt_rate_30_8 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate308EqualTo(String value) {
            addCriterion("amt_rate_30_8 =", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308NotEqualTo(String value) {
            addCriterion("amt_rate_30_8 <>", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308GreaterThan(String value) {
            addCriterion("amt_rate_30_8 >", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_8 >=", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308LessThan(String value) {
            addCriterion("amt_rate_30_8 <", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_8 <=", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308Like(String value) {
            addCriterion("amt_rate_30_8 like", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308NotLike(String value) {
            addCriterion("amt_rate_30_8 not like", value, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308In(List<String> values) {
            addCriterion("amt_rate_30_8 in", values, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308NotIn(List<String> values) {
            addCriterion("amt_rate_30_8 not in", values, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308Between(String value1, String value2) {
            addCriterion("amt_rate_30_8 between", value1, value2, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andAmtRate308NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_8 not between", value1, value2, "amtRate308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308IsNull() {
            addCriterion("profit_amt_cum_30_8 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308IsNotNull() {
            addCriterion("profit_amt_cum_30_8 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308EqualTo(String value) {
            addCriterion("profit_amt_cum_30_8 =", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_8 <>", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_8 >", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_8 >=", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308LessThan(String value) {
            addCriterion("profit_amt_cum_30_8 <", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_8 <=", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308Like(String value) {
            addCriterion("profit_amt_cum_30_8 like", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308NotLike(String value) {
            addCriterion("profit_amt_cum_30_8 not like", value, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308In(List<String> values) {
            addCriterion("profit_amt_cum_30_8 in", values, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_8 not in", values, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_8 between", value1, value2, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum308NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_8 not between", value1, value2, "profitAmtCum308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308IsNull() {
            addCriterion("profit_rate_30_8 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate308IsNotNull() {
            addCriterion("profit_rate_30_8 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate308EqualTo(String value) {
            addCriterion("profit_rate_30_8 =", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308NotEqualTo(String value) {
            addCriterion("profit_rate_30_8 <>", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308GreaterThan(String value) {
            addCriterion("profit_rate_30_8 >", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_8 >=", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308LessThan(String value) {
            addCriterion("profit_rate_30_8 <", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_8 <=", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308Like(String value) {
            addCriterion("profit_rate_30_8 like", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308NotLike(String value) {
            addCriterion("profit_rate_30_8 not like", value, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308In(List<String> values) {
            addCriterion("profit_rate_30_8 in", values, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308NotIn(List<String> values) {
            addCriterion("profit_rate_30_8 not in", values, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308Between(String value1, String value2) {
            addCriterion("profit_rate_30_8 between", value1, value2, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andProfitRate308NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_8 not between", value1, value2, "profitRate308");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8IsNull() {
            addCriterion("amt_inv_new_8 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8IsNotNull() {
            addCriterion("amt_inv_new_8 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8EqualTo(String value) {
            addCriterion("amt_inv_new_8 =", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8NotEqualTo(String value) {
            addCriterion("amt_inv_new_8 <>", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8GreaterThan(String value) {
            addCriterion("amt_inv_new_8 >", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_8 >=", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8LessThan(String value) {
            addCriterion("amt_inv_new_8 <", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_8 <=", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8Like(String value) {
            addCriterion("amt_inv_new_8 like", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8NotLike(String value) {
            addCriterion("amt_inv_new_8 not like", value, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8In(List<String> values) {
            addCriterion("amt_inv_new_8 in", values, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8NotIn(List<String> values) {
            addCriterion("amt_inv_new_8 not in", values, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8Between(String value1, String value2) {
            addCriterion("amt_inv_new_8 between", value1, value2, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew8NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_8 not between", value1, value2, "amtInvNew8");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9IsNull() {
            addCriterion("sku_cnt_total_9 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9IsNotNull() {
            addCriterion("sku_cnt_total_9 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9EqualTo(String value) {
            addCriterion("sku_cnt_total_9 =", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9NotEqualTo(String value) {
            addCriterion("sku_cnt_total_9 <>", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9GreaterThan(String value) {
            addCriterion("sku_cnt_total_9 >", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_9 >=", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9LessThan(String value) {
            addCriterion("sku_cnt_total_9 <", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_total_9 <=", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9Like(String value) {
            addCriterion("sku_cnt_total_9 like", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9NotLike(String value) {
            addCriterion("sku_cnt_total_9 not like", value, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9In(List<String> values) {
            addCriterion("sku_cnt_total_9 in", values, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9NotIn(List<String> values) {
            addCriterion("sku_cnt_total_9 not in", values, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9Between(String value1, String value2) {
            addCriterion("sku_cnt_total_9 between", value1, value2, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCntTotal9NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_total_9 not between", value1, value2, "skuCntTotal9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9IsNull() {
            addCriterion("sku_cnt_9 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9IsNotNull() {
            addCriterion("sku_cnt_9 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9EqualTo(String value) {
            addCriterion("sku_cnt_9 =", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9NotEqualTo(String value) {
            addCriterion("sku_cnt_9 <>", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9GreaterThan(String value) {
            addCriterion("sku_cnt_9 >", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_9 >=", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9LessThan(String value) {
            addCriterion("sku_cnt_9 <", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_9 <=", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9Like(String value) {
            addCriterion("sku_cnt_9 like", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9NotLike(String value) {
            addCriterion("sku_cnt_9 not like", value, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9In(List<String> values) {
            addCriterion("sku_cnt_9 in", values, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9NotIn(List<String> values) {
            addCriterion("sku_cnt_9 not in", values, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9Between(String value1, String value2) {
            addCriterion("sku_cnt_9 between", value1, value2, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCnt9NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_9 not between", value1, value2, "skuCnt9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9IsNull() {
            addCriterion("sku_cnt_new_9 is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9IsNotNull() {
            addCriterion("sku_cnt_new_9 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9EqualTo(String value) {
            addCriterion("sku_cnt_new_9 =", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9NotEqualTo(String value) {
            addCriterion("sku_cnt_new_9 <>", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9GreaterThan(String value) {
            addCriterion("sku_cnt_new_9 >", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9GreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_9 >=", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9LessThan(String value) {
            addCriterion("sku_cnt_new_9 <", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9LessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_new_9 <=", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9Like(String value) {
            addCriterion("sku_cnt_new_9 like", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9NotLike(String value) {
            addCriterion("sku_cnt_new_9 not like", value, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9In(List<String> values) {
            addCriterion("sku_cnt_new_9 in", values, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9NotIn(List<String> values) {
            addCriterion("sku_cnt_new_9 not in", values, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9Between(String value1, String value2) {
            addCriterion("sku_cnt_new_9 between", value1, value2, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andSkuCntNew9NotBetween(String value1, String value2) {
            addCriterion("sku_cnt_new_9 not between", value1, value2, "skuCntNew9");
            return (Criteria) this;
        }

        public Criteria andNumCum309IsNull() {
            addCriterion("num_cum_30_9 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum309IsNotNull() {
            addCriterion("num_cum_30_9 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum309EqualTo(String value) {
            addCriterion("num_cum_30_9 =", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309NotEqualTo(String value) {
            addCriterion("num_cum_30_9 <>", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309GreaterThan(String value) {
            addCriterion("num_cum_30_9 >", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_30_9 >=", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309LessThan(String value) {
            addCriterion("num_cum_30_9 <", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309LessThanOrEqualTo(String value) {
            addCriterion("num_cum_30_9 <=", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309Like(String value) {
            addCriterion("num_cum_30_9 like", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309NotLike(String value) {
            addCriterion("num_cum_30_9 not like", value, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309In(List<String> values) {
            addCriterion("num_cum_30_9 in", values, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309NotIn(List<String> values) {
            addCriterion("num_cum_30_9 not in", values, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309Between(String value1, String value2) {
            addCriterion("num_cum_30_9 between", value1, value2, "numCum309");
            return (Criteria) this;
        }

        public Criteria andNumCum309NotBetween(String value1, String value2) {
            addCriterion("num_cum_30_9 not between", value1, value2, "numCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309IsNull() {
            addCriterion("amt_cum_30_9 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum309IsNotNull() {
            addCriterion("amt_cum_30_9 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum309EqualTo(String value) {
            addCriterion("amt_cum_30_9 =", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309NotEqualTo(String value) {
            addCriterion("amt_cum_30_9 <>", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309GreaterThan(String value) {
            addCriterion("amt_cum_30_9 >", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_9 >=", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309LessThan(String value) {
            addCriterion("amt_cum_30_9 <", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_9 <=", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309Like(String value) {
            addCriterion("amt_cum_30_9 like", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309NotLike(String value) {
            addCriterion("amt_cum_30_9 not like", value, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309In(List<String> values) {
            addCriterion("amt_cum_30_9 in", values, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309NotIn(List<String> values) {
            addCriterion("amt_cum_30_9 not in", values, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309Between(String value1, String value2) {
            addCriterion("amt_cum_30_9 between", value1, value2, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtCum309NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_9 not between", value1, value2, "amtCum309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309IsNull() {
            addCriterion("amt_rate_30_9 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRate309IsNotNull() {
            addCriterion("amt_rate_30_9 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRate309EqualTo(String value) {
            addCriterion("amt_rate_30_9 =", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309NotEqualTo(String value) {
            addCriterion("amt_rate_30_9 <>", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309GreaterThan(String value) {
            addCriterion("amt_rate_30_9 >", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_9 >=", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309LessThan(String value) {
            addCriterion("amt_rate_30_9 <", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_30_9 <=", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309Like(String value) {
            addCriterion("amt_rate_30_9 like", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309NotLike(String value) {
            addCriterion("amt_rate_30_9 not like", value, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309In(List<String> values) {
            addCriterion("amt_rate_30_9 in", values, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309NotIn(List<String> values) {
            addCriterion("amt_rate_30_9 not in", values, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309Between(String value1, String value2) {
            addCriterion("amt_rate_30_9 between", value1, value2, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andAmtRate309NotBetween(String value1, String value2) {
            addCriterion("amt_rate_30_9 not between", value1, value2, "amtRate309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309IsNull() {
            addCriterion("profit_amt_cum_30_9 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309IsNotNull() {
            addCriterion("profit_amt_cum_30_9 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309EqualTo(String value) {
            addCriterion("profit_amt_cum_30_9 =", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309NotEqualTo(String value) {
            addCriterion("profit_amt_cum_30_9 <>", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309GreaterThan(String value) {
            addCriterion("profit_amt_cum_30_9 >", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_9 >=", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309LessThan(String value) {
            addCriterion("profit_amt_cum_30_9 <", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_30_9 <=", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309Like(String value) {
            addCriterion("profit_amt_cum_30_9 like", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309NotLike(String value) {
            addCriterion("profit_amt_cum_30_9 not like", value, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309In(List<String> values) {
            addCriterion("profit_amt_cum_30_9 in", values, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309NotIn(List<String> values) {
            addCriterion("profit_amt_cum_30_9 not in", values, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309Between(String value1, String value2) {
            addCriterion("profit_amt_cum_30_9 between", value1, value2, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum309NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_30_9 not between", value1, value2, "profitAmtCum309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309IsNull() {
            addCriterion("profit_rate_30_9 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate309IsNotNull() {
            addCriterion("profit_rate_30_9 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate309EqualTo(String value) {
            addCriterion("profit_rate_30_9 =", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309NotEqualTo(String value) {
            addCriterion("profit_rate_30_9 <>", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309GreaterThan(String value) {
            addCriterion("profit_rate_30_9 >", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_9 >=", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309LessThan(String value) {
            addCriterion("profit_rate_30_9 <", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_30_9 <=", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309Like(String value) {
            addCriterion("profit_rate_30_9 like", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309NotLike(String value) {
            addCriterion("profit_rate_30_9 not like", value, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309In(List<String> values) {
            addCriterion("profit_rate_30_9 in", values, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309NotIn(List<String> values) {
            addCriterion("profit_rate_30_9 not in", values, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309Between(String value1, String value2) {
            addCriterion("profit_rate_30_9 between", value1, value2, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andProfitRate309NotBetween(String value1, String value2) {
            addCriterion("profit_rate_30_9 not between", value1, value2, "profitRate309");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9IsNull() {
            addCriterion("amt_inv_new_9 is null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9IsNotNull() {
            addCriterion("amt_inv_new_9 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9EqualTo(String value) {
            addCriterion("amt_inv_new_9 =", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9NotEqualTo(String value) {
            addCriterion("amt_inv_new_9 <>", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9GreaterThan(String value) {
            addCriterion("amt_inv_new_9 >", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9GreaterThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_9 >=", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9LessThan(String value) {
            addCriterion("amt_inv_new_9 <", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9LessThanOrEqualTo(String value) {
            addCriterion("amt_inv_new_9 <=", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9Like(String value) {
            addCriterion("amt_inv_new_9 like", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9NotLike(String value) {
            addCriterion("amt_inv_new_9 not like", value, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9In(List<String> values) {
            addCriterion("amt_inv_new_9 in", values, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9NotIn(List<String> values) {
            addCriterion("amt_inv_new_9 not in", values, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9Between(String value1, String value2) {
            addCriterion("amt_inv_new_9 between", value1, value2, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andAmtInvNew9NotBetween(String value1, String value2) {
            addCriterion("amt_inv_new_9 not between", value1, value2, "amtInvNew9");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}