package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrackResultLevelNecessaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TrackResultLevelNecessaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("`level` is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("`level` is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(Integer value) {
            addCriterion("`level` =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(Integer value) {
            addCriterion("`level` <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(Integer value) {
            addCriterion("`level` >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("`level` >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(Integer value) {
            addCriterion("`level` <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(Integer value) {
            addCriterion("`level` <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<Integer> values) {
            addCriterion("`level` in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<Integer> values) {
            addCriterion("`level` not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(Integer value1, Integer value2) {
            addCriterion("`level` between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("`level` not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidIsNull() {
            addCriterion("plat_orgid is null");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidIsNotNull() {
            addCriterion("plat_orgid is not null");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidEqualTo(String value) {
            addCriterion("plat_orgid =", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotEqualTo(String value) {
            addCriterion("plat_orgid <>", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidGreaterThan(String value) {
            addCriterion("plat_orgid >", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidGreaterThanOrEqualTo(String value) {
            addCriterion("plat_orgid >=", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidLessThan(String value) {
            addCriterion("plat_orgid <", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidLessThanOrEqualTo(String value) {
            addCriterion("plat_orgid <=", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidLike(String value) {
            addCriterion("plat_orgid like", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotLike(String value) {
            addCriterion("plat_orgid not like", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidIn(List<String> values) {
            addCriterion("plat_orgid in", values, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotIn(List<String> values) {
            addCriterion("plat_orgid not in", values, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidBetween(String value1, String value2) {
            addCriterion("plat_orgid between", value1, value2, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotBetween(String value1, String value2) {
            addCriterion("plat_orgid not between", value1, value2, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andCompidIsNull() {
            addCriterion("compid is null");
            return (Criteria) this;
        }

        public Criteria andCompidIsNotNull() {
            addCriterion("compid is not null");
            return (Criteria) this;
        }

        public Criteria andCompidEqualTo(String value) {
            addCriterion("compid =", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotEqualTo(String value) {
            addCriterion("compid <>", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidGreaterThan(String value) {
            addCriterion("compid >", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidGreaterThanOrEqualTo(String value) {
            addCriterion("compid >=", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidLessThan(String value) {
            addCriterion("compid <", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidLessThanOrEqualTo(String value) {
            addCriterion("compid <=", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidLike(String value) {
            addCriterion("compid like", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotLike(String value) {
            addCriterion("compid not like", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidIn(List<String> values) {
            addCriterion("compid in", values, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotIn(List<String> values) {
            addCriterion("compid not in", values, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidBetween(String value1, String value2) {
            addCriterion("compid between", value1, value2, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotBetween(String value1, String value2) {
            addCriterion("compid not between", value1, value2, "compid");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andStoreGroupIsNull() {
            addCriterion("store_group is null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupIsNotNull() {
            addCriterion("store_group is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupEqualTo(String value) {
            addCriterion("store_group =", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotEqualTo(String value) {
            addCriterion("store_group <>", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupGreaterThan(String value) {
            addCriterion("store_group >", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupGreaterThanOrEqualTo(String value) {
            addCriterion("store_group >=", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupLessThan(String value) {
            addCriterion("store_group <", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupLessThanOrEqualTo(String value) {
            addCriterion("store_group <=", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupLike(String value) {
            addCriterion("store_group like", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotLike(String value) {
            addCriterion("store_group not like", value, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupIn(List<String> values) {
            addCriterion("store_group in", values, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotIn(List<String> values) {
            addCriterion("store_group not in", values, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupBetween(String value1, String value2) {
            addCriterion("store_group between", value1, value2, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreGroupNotBetween(String value1, String value2) {
            addCriterion("store_group not between", value1, value2, "storeGroup");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("goods_id is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("goods_id is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(String value) {
            addCriterion("goods_id =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(String value) {
            addCriterion("goods_id <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(String value) {
            addCriterion("goods_id >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(String value) {
            addCriterion("goods_id >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(String value) {
            addCriterion("goods_id <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(String value) {
            addCriterion("goods_id <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLike(String value) {
            addCriterion("goods_id like", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotLike(String value) {
            addCriterion("goods_id not like", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<String> values) {
            addCriterion("goods_id in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<String> values) {
            addCriterion("goods_id not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(String value1, String value2) {
            addCriterion("goods_id between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(String value1, String value2) {
            addCriterion("goods_id not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andBakIsNull() {
            addCriterion("bak is null");
            return (Criteria) this;
        }

        public Criteria andBakIsNotNull() {
            addCriterion("bak is not null");
            return (Criteria) this;
        }

        public Criteria andBakEqualTo(String value) {
            addCriterion("bak =", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotEqualTo(String value) {
            addCriterion("bak <>", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakGreaterThan(String value) {
            addCriterion("bak >", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakGreaterThanOrEqualTo(String value) {
            addCriterion("bak >=", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakLessThan(String value) {
            addCriterion("bak <", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakLessThanOrEqualTo(String value) {
            addCriterion("bak <=", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakLike(String value) {
            addCriterion("bak like", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotLike(String value) {
            addCriterion("bak not like", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakIn(List<String> values) {
            addCriterion("bak in", values, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotIn(List<String> values) {
            addCriterion("bak not in", values, "bak");
            return (Criteria) this;
        }

        public Criteria andBakBetween(String value1, String value2) {
            addCriterion("bak between", value1, value2, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotBetween(String value1, String value2) {
            addCriterion("bak not between", value1, value2, "bak");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationIsNull() {
            addCriterion("store_concentration is null");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationIsNotNull() {
            addCriterion("store_concentration is not null");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationEqualTo(String value) {
            addCriterion("store_concentration =", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationNotEqualTo(String value) {
            addCriterion("store_concentration <>", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationGreaterThan(String value) {
            addCriterion("store_concentration >", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationGreaterThanOrEqualTo(String value) {
            addCriterion("store_concentration >=", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationLessThan(String value) {
            addCriterion("store_concentration <", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationLessThanOrEqualTo(String value) {
            addCriterion("store_concentration <=", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationLike(String value) {
            addCriterion("store_concentration like", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationNotLike(String value) {
            addCriterion("store_concentration not like", value, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationIn(List<String> values) {
            addCriterion("store_concentration in", values, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationNotIn(List<String> values) {
            addCriterion("store_concentration not in", values, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationBetween(String value1, String value2) {
            addCriterion("store_concentration between", value1, value2, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreConcentrationNotBetween(String value1, String value2) {
            addCriterion("store_concentration not between", value1, value2, "storeConcentration");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateIsNull() {
            addCriterion("store_sale_rate is null");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateIsNotNull() {
            addCriterion("store_sale_rate is not null");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateEqualTo(String value) {
            addCriterion("store_sale_rate =", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateNotEqualTo(String value) {
            addCriterion("store_sale_rate <>", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateGreaterThan(String value) {
            addCriterion("store_sale_rate >", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateGreaterThanOrEqualTo(String value) {
            addCriterion("store_sale_rate >=", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateLessThan(String value) {
            addCriterion("store_sale_rate <", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateLessThanOrEqualTo(String value) {
            addCriterion("store_sale_rate <=", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateLike(String value) {
            addCriterion("store_sale_rate like", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateNotLike(String value) {
            addCriterion("store_sale_rate not like", value, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateIn(List<String> values) {
            addCriterion("store_sale_rate in", values, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateNotIn(List<String> values) {
            addCriterion("store_sale_rate not in", values, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateBetween(String value1, String value2) {
            addCriterion("store_sale_rate between", value1, value2, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andStoreSaleRateNotBetween(String value1, String value2) {
            addCriterion("store_sale_rate not between", value1, value2, "storeSaleRate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}