package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-sku数配置上线管理
 */
@Data
public class JymlSkuMaxLimitConfigure implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 区域平台id
     */
    private Long platformOrgId;

    /**
     * 区域平台
     */
    private String platformName;

    /**
     * 项目公司id
     */
    private Long businessOrgId;

    /**
     * 项目公司
     */
    private String businessName;

    /**
     * 城市
     */
    private String city;

    /**
     * 店型编码
     */
    private String storeType;

    /**
     * 组货店型
     */
    private String storeTypeName;

    /**
     * 商品大类id
     */
    private String category;

    /**
     * 商品大类
     */
    private String categoryName;

    /**
     * 商品中类id
     */
    private String middleCategory;

    /**
     * 商品中类
     */
    private String middleCategoryName;

    /**
     * 商品小类id
     */
    private String smallCategory;

    /**
     * 商品小类
     */
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    private String subCategory;

    /**
     * 商品子类
     */
    private String subCategoryName;

    /**
     * SKU配置数上限
     */
    private Integer skuMaxLimit;

    /**
     * SKU配置数上限建议
     */
    private Integer skuSuggestedLimit;

    /**
     * SKU配置数下限
     */
    private Integer skuLowerLimit;

    /**
     * SKU配置数下限建议
     */
    private Integer skuSuggestedLowerLimit;

    /**
     * 涉及子类/成分个数
     */
    private Integer ingredientCount;

    /**
     * 生效状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtUpdate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本
     */
    private Long version;

    /**
     * 环境变量
     */
    private String env;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 商品大类是rx/otc
     */
    private String rxOtc;

    private static final long serialVersionUID = 1L;
}