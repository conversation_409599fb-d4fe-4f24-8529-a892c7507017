package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlStoreSkuConfirmDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlStoreSkuConfirmDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNull() {
            addCriterion("business_org_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNotNull() {
            addCriterion("business_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdEqualTo(Long value) {
            addCriterion("business_org_id =", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotEqualTo(Long value) {
            addCriterion("business_org_id <>", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThan(Long value) {
            addCriterion("business_org_id >", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_org_id >=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThan(Long value) {
            addCriterion("business_org_id <", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("business_org_id <=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIn(List<Long> values) {
            addCriterion("business_org_id in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotIn(List<Long> values) {
            addCriterion("business_org_id not in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdBetween(Long value1, Long value2) {
            addCriterion("business_org_id between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("business_org_id not between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdIsNull() {
            addCriterion("process_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdIsNotNull() {
            addCriterion("process_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdEqualTo(Long value) {
            addCriterion("process_detail_id =", value, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdNotEqualTo(Long value) {
            addCriterion("process_detail_id <>", value, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdGreaterThan(Long value) {
            addCriterion("process_detail_id >", value, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_detail_id >=", value, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdLessThan(Long value) {
            addCriterion("process_detail_id <", value, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("process_detail_id <=", value, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdIn(List<Long> values) {
            addCriterion("process_detail_id in", values, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdNotIn(List<Long> values) {
            addCriterion("process_detail_id not in", values, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdBetween(Long value1, Long value2) {
            addCriterion("process_detail_id between", value1, value2, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andProcessDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("process_detail_id not between", value1, value2, "processDetailId");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNull() {
            addCriterion("middle_category is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNotNull() {
            addCriterion("middle_category is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryEqualTo(String value) {
            addCriterion("middle_category =", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotEqualTo(String value) {
            addCriterion("middle_category <>", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThan(String value) {
            addCriterion("middle_category >", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category >=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThan(String value) {
            addCriterion("middle_category <", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThanOrEqualTo(String value) {
            addCriterion("middle_category <=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLike(String value) {
            addCriterion("middle_category like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotLike(String value) {
            addCriterion("middle_category not like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIn(List<String> values) {
            addCriterion("middle_category in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotIn(List<String> values) {
            addCriterion("middle_category not in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryBetween(String value1, String value2) {
            addCriterion("middle_category between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotBetween(String value1, String value2) {
            addCriterion("middle_category not between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNull() {
            addCriterion("small_category is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNotNull() {
            addCriterion("small_category is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryEqualTo(String value) {
            addCriterion("small_category =", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotEqualTo(String value) {
            addCriterion("small_category <>", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThan(String value) {
            addCriterion("small_category >", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("small_category >=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThan(String value) {
            addCriterion("small_category <", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThanOrEqualTo(String value) {
            addCriterion("small_category <=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLike(String value) {
            addCriterion("small_category like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotLike(String value) {
            addCriterion("small_category not like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIn(List<String> values) {
            addCriterion("small_category in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotIn(List<String> values) {
            addCriterion("small_category not in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryBetween(String value1, String value2) {
            addCriterion("small_category between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotBetween(String value1, String value2) {
            addCriterion("small_category not between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNull() {
            addCriterion("sub_category is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNotNull() {
            addCriterion("sub_category is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryEqualTo(String value) {
            addCriterion("sub_category =", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotEqualTo(String value) {
            addCriterion("sub_category <>", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThan(String value) {
            addCriterion("sub_category >", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category >=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThan(String value) {
            addCriterion("sub_category <", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThanOrEqualTo(String value) {
            addCriterion("sub_category <=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLike(String value) {
            addCriterion("sub_category like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotLike(String value) {
            addCriterion("sub_category not like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIn(List<String> values) {
            addCriterion("sub_category in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotIn(List<String> values) {
            addCriterion("sub_category not in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryBetween(String value1, String value2) {
            addCriterion("sub_category between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotBetween(String value1, String value2) {
            addCriterion("sub_category not between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andCategoryPathIsNull() {
            addCriterion("category_path is null");
            return (Criteria) this;
        }

        public Criteria andCategoryPathIsNotNull() {
            addCriterion("category_path is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryPathEqualTo(String value) {
            addCriterion("category_path =", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNotEqualTo(String value) {
            addCriterion("category_path <>", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathGreaterThan(String value) {
            addCriterion("category_path >", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathGreaterThanOrEqualTo(String value) {
            addCriterion("category_path >=", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathLessThan(String value) {
            addCriterion("category_path <", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathLessThanOrEqualTo(String value) {
            addCriterion("category_path <=", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathLike(String value) {
            addCriterion("category_path like", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNotLike(String value) {
            addCriterion("category_path not like", value, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathIn(List<String> values) {
            addCriterion("category_path in", values, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNotIn(List<String> values) {
            addCriterion("category_path not in", values, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathBetween(String value1, String value2) {
            addCriterion("category_path between", value1, value2, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNotBetween(String value1, String value2) {
            addCriterion("category_path not between", value1, value2, "categoryPath");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestIsNull() {
            addCriterion("system_suggest is null");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestIsNotNull() {
            addCriterion("system_suggest is not null");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestEqualTo(Integer value) {
            addCriterion("system_suggest =", value, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestNotEqualTo(Integer value) {
            addCriterion("system_suggest <>", value, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestGreaterThan(Integer value) {
            addCriterion("system_suggest >", value, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_suggest >=", value, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestLessThan(Integer value) {
            addCriterion("system_suggest <", value, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestLessThanOrEqualTo(Integer value) {
            addCriterion("system_suggest <=", value, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestIn(List<Integer> values) {
            addCriterion("system_suggest in", values, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestNotIn(List<Integer> values) {
            addCriterion("system_suggest not in", values, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestBetween(Integer value1, Integer value2) {
            addCriterion("system_suggest between", value1, value2, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andSystemSuggestNotBetween(Integer value1, Integer value2) {
            addCriterion("system_suggest not between", value1, value2, "systemSuggest");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultIsNull() {
            addCriterion("previous_review_result is null");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultIsNotNull() {
            addCriterion("previous_review_result is not null");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultEqualTo(Integer value) {
            addCriterion("previous_review_result =", value, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultNotEqualTo(Integer value) {
            addCriterion("previous_review_result <>", value, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultGreaterThan(Integer value) {
            addCriterion("previous_review_result >", value, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("previous_review_result >=", value, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultLessThan(Integer value) {
            addCriterion("previous_review_result <", value, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultLessThanOrEqualTo(Integer value) {
            addCriterion("previous_review_result <=", value, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultIn(List<Integer> values) {
            addCriterion("previous_review_result in", values, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultNotIn(List<Integer> values) {
            addCriterion("previous_review_result not in", values, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultBetween(Integer value1, Integer value2) {
            addCriterion("previous_review_result between", value1, value2, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andPreviousReviewResultNotBetween(Integer value1, Integer value2) {
            addCriterion("previous_review_result not between", value1, value2, "previousReviewResult");
            return (Criteria) this;
        }

        public Criteria andMyConfirmIsNull() {
            addCriterion("my_confirm is null");
            return (Criteria) this;
        }

        public Criteria andMyConfirmIsNotNull() {
            addCriterion("my_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andMyConfirmEqualTo(Integer value) {
            addCriterion("my_confirm =", value, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmNotEqualTo(Integer value) {
            addCriterion("my_confirm <>", value, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmGreaterThan(Integer value) {
            addCriterion("my_confirm >", value, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmGreaterThanOrEqualTo(Integer value) {
            addCriterion("my_confirm >=", value, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmLessThan(Integer value) {
            addCriterion("my_confirm <", value, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmLessThanOrEqualTo(Integer value) {
            addCriterion("my_confirm <=", value, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmIn(List<Integer> values) {
            addCriterion("my_confirm in", values, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmNotIn(List<Integer> values) {
            addCriterion("my_confirm not in", values, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmBetween(Integer value1, Integer value2) {
            addCriterion("my_confirm between", value1, value2, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andMyConfirmNotBetween(Integer value1, Integer value2) {
            addCriterion("my_confirm not between", value1, value2, "myConfirm");
            return (Criteria) this;
        }

        public Criteria andReviewResultIsNull() {
            addCriterion("review_result is null");
            return (Criteria) this;
        }

        public Criteria andReviewResultIsNotNull() {
            addCriterion("review_result is not null");
            return (Criteria) this;
        }

        public Criteria andReviewResultEqualTo(Integer value) {
            addCriterion("review_result =", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotEqualTo(Integer value) {
            addCriterion("review_result <>", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultGreaterThan(Integer value) {
            addCriterion("review_result >", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("review_result >=", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultLessThan(Integer value) {
            addCriterion("review_result <", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultLessThanOrEqualTo(Integer value) {
            addCriterion("review_result <=", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultIn(List<Integer> values) {
            addCriterion("review_result in", values, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotIn(List<Integer> values) {
            addCriterion("review_result not in", values, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultBetween(Integer value1, Integer value2) {
            addCriterion("review_result between", value1, value2, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotBetween(Integer value1, Integer value2) {
            addCriterion("review_result not between", value1, value2, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andSubmitByIsNull() {
            addCriterion("submit_by is null");
            return (Criteria) this;
        }

        public Criteria andSubmitByIsNotNull() {
            addCriterion("submit_by is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitByEqualTo(Long value) {
            addCriterion("submit_by =", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotEqualTo(Long value) {
            addCriterion("submit_by <>", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByGreaterThan(Long value) {
            addCriterion("submit_by >", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByGreaterThanOrEqualTo(Long value) {
            addCriterion("submit_by >=", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByLessThan(Long value) {
            addCriterion("submit_by <", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByLessThanOrEqualTo(Long value) {
            addCriterion("submit_by <=", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByIn(List<Long> values) {
            addCriterion("submit_by in", values, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotIn(List<Long> values) {
            addCriterion("submit_by not in", values, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByBetween(Long value1, Long value2) {
            addCriterion("submit_by between", value1, value2, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotBetween(Long value1, Long value2) {
            addCriterion("submit_by not between", value1, value2, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitNameIsNull() {
            addCriterion("submit_name is null");
            return (Criteria) this;
        }

        public Criteria andSubmitNameIsNotNull() {
            addCriterion("submit_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitNameEqualTo(String value) {
            addCriterion("submit_name =", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameNotEqualTo(String value) {
            addCriterion("submit_name <>", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameGreaterThan(String value) {
            addCriterion("submit_name >", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameGreaterThanOrEqualTo(String value) {
            addCriterion("submit_name >=", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameLessThan(String value) {
            addCriterion("submit_name <", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameLessThanOrEqualTo(String value) {
            addCriterion("submit_name <=", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameLike(String value) {
            addCriterion("submit_name like", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameNotLike(String value) {
            addCriterion("submit_name not like", value, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameIn(List<String> values) {
            addCriterion("submit_name in", values, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameNotIn(List<String> values) {
            addCriterion("submit_name not in", values, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameBetween(String value1, String value2) {
            addCriterion("submit_name between", value1, value2, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitNameNotBetween(String value1, String value2) {
            addCriterion("submit_name not between", value1, value2, "submitName");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNull() {
            addCriterion("submit_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNotNull() {
            addCriterion("submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeEqualTo(Date value) {
            addCriterion("submit_time =", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotEqualTo(Date value) {
            addCriterion("submit_time <>", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThan(Date value) {
            addCriterion("submit_time >", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("submit_time >=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThan(Date value) {
            addCriterion("submit_time <", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThanOrEqualTo(Date value) {
            addCriterion("submit_time <=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIn(List<Date> values) {
            addCriterion("submit_time in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotIn(List<Date> values) {
            addCriterion("submit_time not in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeBetween(Date value1, Date value2) {
            addCriterion("submit_time between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotBetween(Date value1, Date value2) {
            addCriterion("submit_time not between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andReviewByIsNull() {
            addCriterion("review_by is null");
            return (Criteria) this;
        }

        public Criteria andReviewByIsNotNull() {
            addCriterion("review_by is not null");
            return (Criteria) this;
        }

        public Criteria andReviewByEqualTo(Long value) {
            addCriterion("review_by =", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotEqualTo(Long value) {
            addCriterion("review_by <>", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByGreaterThan(Long value) {
            addCriterion("review_by >", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByGreaterThanOrEqualTo(Long value) {
            addCriterion("review_by >=", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByLessThan(Long value) {
            addCriterion("review_by <", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByLessThanOrEqualTo(Long value) {
            addCriterion("review_by <=", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByIn(List<Long> values) {
            addCriterion("review_by in", values, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotIn(List<Long> values) {
            addCriterion("review_by not in", values, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByBetween(Long value1, Long value2) {
            addCriterion("review_by between", value1, value2, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotBetween(Long value1, Long value2) {
            addCriterion("review_by not between", value1, value2, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewNameIsNull() {
            addCriterion("review_name is null");
            return (Criteria) this;
        }

        public Criteria andReviewNameIsNotNull() {
            addCriterion("review_name is not null");
            return (Criteria) this;
        }

        public Criteria andReviewNameEqualTo(String value) {
            addCriterion("review_name =", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameNotEqualTo(String value) {
            addCriterion("review_name <>", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameGreaterThan(String value) {
            addCriterion("review_name >", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameGreaterThanOrEqualTo(String value) {
            addCriterion("review_name >=", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameLessThan(String value) {
            addCriterion("review_name <", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameLessThanOrEqualTo(String value) {
            addCriterion("review_name <=", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameLike(String value) {
            addCriterion("review_name like", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameNotLike(String value) {
            addCriterion("review_name not like", value, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameIn(List<String> values) {
            addCriterion("review_name in", values, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameNotIn(List<String> values) {
            addCriterion("review_name not in", values, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameBetween(String value1, String value2) {
            addCriterion("review_name between", value1, value2, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewNameNotBetween(String value1, String value2) {
            addCriterion("review_name not between", value1, value2, "reviewName");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNull() {
            addCriterion("review_time is null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNotNull() {
            addCriterion("review_time is not null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeEqualTo(Date value) {
            addCriterion("review_time =", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotEqualTo(Date value) {
            addCriterion("review_time <>", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThan(Date value) {
            addCriterion("review_time >", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("review_time >=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThan(Date value) {
            addCriterion("review_time <", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThanOrEqualTo(Date value) {
            addCriterion("review_time <=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIn(List<Date> values) {
            addCriterion("review_time in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotIn(List<Date> values) {
            addCriterion("review_time not in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeBetween(Date value1, Date value2) {
            addCriterion("review_time between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotBetween(Date value1, Date value2) {
            addCriterion("review_time not between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}