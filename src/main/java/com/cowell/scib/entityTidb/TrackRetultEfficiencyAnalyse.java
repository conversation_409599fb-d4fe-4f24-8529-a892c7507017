package com.cowell.scib.entityTidb;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.converters.string.StringNumberConverter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import java.io.Serializable;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TrackRetultEfficiencyAnalyse implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 任务ID
     */
    @ExcelIgnore
    private Long taskId;

    /**
     * 平台
     */
    @ExcelProperty({"平台", "平台"})
    private String zoneNew;

    /**
     * 企业
     */
    @ExcelProperty({"企业", "企业"})
    private String chainName;

    /**
     * 必备类型
     */
    @ExcelProperty({"必备类型", "必备类型"})
    private String reviseTypes;

    /**
     * 门店数现状
     */
    @ExcelProperty(value = {"参与门店数", "门店数现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String orgCntV2;

    /**
     * 门店数3.0
     */
    @ExcelProperty(value = {"参与门店数", "门店数3.0"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String orgCntV3;

    /**
     * SKU个数（一店一目级）SKU数
     */
    @ExcelProperty(value = {"SKU个数（一店一目级）", "SKU数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String cntV3;

    /**
     * SKU个数（一店一目级）SKU - 现状
     */
    @ExcelProperty(value = {"SKU个数（一店一目级）", "SKU - 现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String cntV3V2;

    /**
     * SKU个数（一店一目级）VS现状新增
     */
    @ExcelProperty(value = {"SKU个数（一店一目级）", "VS现状新增"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String cntXz;

    /**
     * SKU个数（一店一目级）VS现状剔除
     */
    @ExcelProperty(value = {"SKU个数（一店一目级）", "VS现状剔除"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String cntTc;

    /**
     * V3必备目录SKU个数 店均SKU个数
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "店均SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCnt;

    /**
     * V3必备目录SKU个数 VS现状店均SKU
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "VS现状店均SKU"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntV3V2;

    /**
     * V3必备目录SKU个数 统采（含贴牌、专销）店均SKU占比
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "统采（含贴牌、专销）店均SKU占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntTc;

    /**
     * V3必备目录SKU个数 集采店均SKU占比
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "集采店均SKU占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntJc;

    /**
     * V3必备目录SKU个数 地采店均SKU占比
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "地采店均SKU占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntDc;

    /**
     * V3必备目录SKU个数 AA店均SKU占比
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "AA店均SKU占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntAa;

    /**
     * V3必备目录SKU个数 A1店均SKU占比
     */
    @ExcelProperty(value = {"V3必备目录SKU个数（店均值）", "A1店均SKU占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String avgSkuCntA1;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 销售额
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Total;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3Total;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 销售占比VS现状
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "销售占比VS现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3V2Total;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 销售额增量
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "销售额增量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum30V3V2Total;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 销售增长VS现状（%）
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "销售增长VS现状（%）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30V3V21Total;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） AA销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "AA销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalAa;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） A1销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "A1销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalA1;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 统采（含贴牌、专销）销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "统采（含贴牌、专销）销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalTc;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 集采销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "集采销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalJc;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 地采销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "地采销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalDc;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 线上销售（O2O)占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "线上销售（O2O)占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalOn;

    /**
     * V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125） 线下销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售（含新增预估，新成分0.25+旧成分*0.125）（千元）", "线下销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3TotalOff;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 毛利额
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitCum30V3Total;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 毛利额VS现状
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "毛利额VS现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitCum30V3V2Total;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 毛利额增长VS现状（%）
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "毛利额增长VS现状（%）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30V3V21Total;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3Total;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 毛利贡献率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "毛利贡献率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateGxV3Total;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 毛利率VS现状
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "毛利率VS现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3V2Total;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） AA毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "AA毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalAa;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） A1毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "A1毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalA1;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 统采（含贴牌、专销）毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "统采（含贴牌、专销）毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalTc;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 集采毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "集采毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalJc;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 地采毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "地采毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalDc;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 线上（O2O)毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "线上（O2O)毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalOn;

    /**
     * V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125） 线下毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利（含新增预估，新成分0.25+旧成分*0.125）", "线下毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3TotalOff;

    /**
     * 纯新增品未来30天预估销售 店均净增SKU
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "店均净增SKU"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String avgCnt;

    /**
     * 纯新增品未来30天预估销售 无法预估销售的门店*SKU占比
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "无法预估销售的门店*SKU占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30Null;

    /**
     * 纯新增品未来30天预估销售 无法预估销售的店均SKU
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "无法预估销售的店均SKU"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String avgAmtCum30Null;

    /**
     * 纯新增品未来30天预估销售 新增品预估销售额
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "新增品预估销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum30New;

    /**
     * 纯新增品未来30天预估销售 新增品店均销售额
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "新增品店均销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String avgAmtCum30New;

    /**
     * 纯新增品未来30天预估销售 新增品销售占比
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "新增品销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateNew;

    /**
     * 纯新增品未来30天预估销售 新增品毛利额
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "新增品毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitCum30New;

    /**
     * 纯新增品未来30天预估销售 毛利率
     */
    @ExcelProperty(value = {"纯新增品未来30天预估销售（新成分0.25+旧成分*0.125）", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateNew;

    /**
     * V3必备目录销售 销售额
     */
    @ExcelProperty(value = {"V3必备目录销售", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum30V3;

    /**
     * V3必备目录销售 销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3;

    /**
     * V3必备目录销售 销售占比VS现状
     */
    @ExcelProperty(value = {"V3必备目录销售", "销售占比VS现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3V2;

    /**
     * V3必备目录销售 销售额增量
     */
    @ExcelProperty(value = {"V3必备目录销售", "销售额增量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum30V3V2;

    /**
     * V3必备目录销售 销售增长VS现状(%)
     */
    @ExcelProperty(value = {"V3必备目录销售", "销售增长VS现状(%)"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30V3V21;

    /**
     * V3必备目录销售 AA销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "AA销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3Aa;

    /**
     * V3必备目录销售 A1销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "A1销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3A1;

    /**
     * V3必备目录销售 统采（含贴牌、专销）销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "统采（含贴牌、专销）销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3Tc;

    /**
     * V3必备目录销售 集采销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "集采销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3Jc;

    /**
     * V3必备目录销售 地采销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "地采销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3Dc;

    /**
     * V3必备目录销售 线上销售（O2O)占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "线上销售（O2O)占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3On;

    /**
     * V3必备目录销售 线下销售占比
     */
    @ExcelProperty(value = {"V3必备目录销售", "线下销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateV3Off;

    /**
     * V3必备目录毛利 毛利额
     */
    @ExcelProperty(value = {"V3必备目录毛利", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitCum30V3;

    /**
     * V3必备目录毛利 毛利额VS现状
     */
    @ExcelProperty(value = {"V3必备目录毛利", "毛利额VS现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitCum30V3V2;

    /**
     * V3必备目录毛利 毛利额增长VS现状（%）
     */
    @ExcelProperty(value = {"V3必备目录毛利", "毛利额增长VS现状（%）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30V3V21;

    /**
     * V3必备目录毛利 毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3;

    /**
     * V3必备目录毛利 毛利贡献率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "毛利贡献率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String amtCum30RateGxV3;

    /**
     * V3必备目录毛利 毛利率VS 现状
     */
    @ExcelProperty(value = {"V3必备目录毛利", "毛利率VS 现状"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3V2;

    /**
     * V3必备目录毛利 AA毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "AA毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3Aa;

    /**
     * V3必备目录毛利 A1毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "A1毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3A1;

    /**
     * V3必备目录毛利 统采（含贴牌、专销）毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "统采（含贴牌、专销）毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3Tc;

    /**
     * V3必备目录毛利 集采毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "集采毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3Jc;

    /**
     * V3必备目录毛利 地采毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "地采毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3Dc;

    /**
     * V3必备目录毛利 线上（O2O)毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "线上（O2O)毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3On;

    /**
     * V3必备目录毛利 线下毛利率
     */
    @ExcelProperty(value = {"V3必备目录毛利", "线下毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%",roundingMode = RoundingMode.HALF_UP)
    private String profitCum30RateV3Off;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date gmtCreate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getZoneNew() {
        return zoneNew;
    }

    public void setZoneNew(String zoneNew) {
        this.zoneNew = zoneNew;
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public String getReviseTypes() {
        return reviseTypes;
    }

    public void setReviseTypes(String reviseTypes) {
        this.reviseTypes = reviseTypes;
    }

    public String getOrgCntV2() {
        return orgCntV2;
    }

    public void setOrgCntV2(String orgCntV2) {
        this.orgCntV2 = orgCntV2;
    }

    public String getOrgCntV3() {
        return orgCntV3;
    }

    public void setOrgCntV3(String orgCntV3) {
        this.orgCntV3 = orgCntV3;
    }

    public String getCntV3() {
        return cntV3;
    }

    public void setCntV3(String cntV3) {
        this.cntV3 = cntV3;
    }

    public String getCntV3V2() {
        return cntV3V2;
    }

    public void setCntV3V2(String cntV3V2) {
        this.cntV3V2 = cntV3V2;
    }

    public String getCntXz() {
        return cntXz;
    }

    public void setCntXz(String cntXz) {
        this.cntXz = cntXz;
    }

    public String getCntTc() {
        return cntTc;
    }

    public void setCntTc(String cntTc) {
        this.cntTc = cntTc;
    }

    public String getAvgSkuCnt() {
        return avgSkuCnt;
    }

    public void setAvgSkuCnt(String avgSkuCnt) {
        this.avgSkuCnt = avgSkuCnt;
    }

    public String getAvgSkuCntV3V2() {
        return avgSkuCntV3V2;
    }

    public void setAvgSkuCntV3V2(String avgSkuCntV3V2) {
        this.avgSkuCntV3V2 = avgSkuCntV3V2;
    }

    public String getAvgSkuCntTc() {
        return avgSkuCntTc;
    }

    public void setAvgSkuCntTc(String avgSkuCntTc) {
        this.avgSkuCntTc = avgSkuCntTc;
    }

    public String getAvgSkuCntJc() {
        return avgSkuCntJc;
    }

    public void setAvgSkuCntJc(String avgSkuCntJc) {
        this.avgSkuCntJc = avgSkuCntJc;
    }

    public String getAvgSkuCntDc() {
        return avgSkuCntDc;
    }

    public void setAvgSkuCntDc(String avgSkuCntDc) {
        this.avgSkuCntDc = avgSkuCntDc;
    }

    public String getAvgSkuCntAa() {
        return avgSkuCntAa;
    }

    public void setAvgSkuCntAa(String avgSkuCntAa) {
        this.avgSkuCntAa = avgSkuCntAa;
    }

    public String getAvgSkuCntA1() {
        return avgSkuCntA1;
    }

    public void setAvgSkuCntA1(String avgSkuCntA1) {
        this.avgSkuCntA1 = avgSkuCntA1;
    }

    public String getAmtCum30Total() {
        return amtCum30Total;
    }

    public void setAmtCum30Total(String amtCum30Total) {
        this.amtCum30Total = amtCum30Total;
    }

    public String getAmtCum30RateV3Total() {
        return amtCum30RateV3Total;
    }

    public void setAmtCum30RateV3Total(String amtCum30RateV3Total) {
        this.amtCum30RateV3Total = amtCum30RateV3Total;
    }

    public String getAmtCum30RateV3V2Total() {
        return amtCum30RateV3V2Total;
    }

    public void setAmtCum30RateV3V2Total(String amtCum30RateV3V2Total) {
        this.amtCum30RateV3V2Total = amtCum30RateV3V2Total;
    }

    public String getAmtCum30V3V2Total() {
        return amtCum30V3V2Total;
    }

    public void setAmtCum30V3V2Total(String amtCum30V3V2Total) {
        this.amtCum30V3V2Total = amtCum30V3V2Total;
    }

    public String getAmtCum30V3V21Total() {
        return amtCum30V3V21Total;
    }

    public void setAmtCum30V3V21Total(String amtCum30V3V21Total) {
        this.amtCum30V3V21Total = amtCum30V3V21Total;
    }

    public String getAmtCum30RateV3TotalAa() {
        return amtCum30RateV3TotalAa;
    }

    public void setAmtCum30RateV3TotalAa(String amtCum30RateV3TotalAa) {
        this.amtCum30RateV3TotalAa = amtCum30RateV3TotalAa;
    }

    public String getAmtCum30RateV3TotalA1() {
        return amtCum30RateV3TotalA1;
    }

    public void setAmtCum30RateV3TotalA1(String amtCum30RateV3TotalA1) {
        this.amtCum30RateV3TotalA1 = amtCum30RateV3TotalA1;
    }

    public String getAmtCum30RateV3TotalTc() {
        return amtCum30RateV3TotalTc;
    }

    public void setAmtCum30RateV3TotalTc(String amtCum30RateV3TotalTc) {
        this.amtCum30RateV3TotalTc = amtCum30RateV3TotalTc;
    }

    public String getAmtCum30RateV3TotalJc() {
        return amtCum30RateV3TotalJc;
    }

    public void setAmtCum30RateV3TotalJc(String amtCum30RateV3TotalJc) {
        this.amtCum30RateV3TotalJc = amtCum30RateV3TotalJc;
    }

    public String getAmtCum30RateV3TotalDc() {
        return amtCum30RateV3TotalDc;
    }

    public void setAmtCum30RateV3TotalDc(String amtCum30RateV3TotalDc) {
        this.amtCum30RateV3TotalDc = amtCum30RateV3TotalDc;
    }

    public String getAmtCum30RateV3TotalOn() {
        return amtCum30RateV3TotalOn;
    }

    public void setAmtCum30RateV3TotalOn(String amtCum30RateV3TotalOn) {
        this.amtCum30RateV3TotalOn = amtCum30RateV3TotalOn;
    }

    public String getAmtCum30RateV3TotalOff() {
        return amtCum30RateV3TotalOff;
    }

    public void setAmtCum30RateV3TotalOff(String amtCum30RateV3TotalOff) {
        this.amtCum30RateV3TotalOff = amtCum30RateV3TotalOff;
    }

    public String getProfitCum30V3Total() {
        return profitCum30V3Total;
    }

    public void setProfitCum30V3Total(String profitCum30V3Total) {
        this.profitCum30V3Total = profitCum30V3Total;
    }

    public String getProfitCum30V3V2Total() {
        return profitCum30V3V2Total;
    }

    public void setProfitCum30V3V2Total(String profitCum30V3V2Total) {
        this.profitCum30V3V2Total = profitCum30V3V2Total;
    }

    public String getProfitCum30V3V21Total() {
        return profitCum30V3V21Total;
    }

    public void setProfitCum30V3V21Total(String profitCum30V3V21Total) {
        this.profitCum30V3V21Total = profitCum30V3V21Total;
    }

    public String getProfitCum30RateV3Total() {
        return profitCum30RateV3Total;
    }

    public void setProfitCum30RateV3Total(String profitCum30RateV3Total) {
        this.profitCum30RateV3Total = profitCum30RateV3Total;
    }

    public String getAmtCum30RateGxV3Total() {
        return amtCum30RateGxV3Total;
    }

    public void setAmtCum30RateGxV3Total(String amtCum30RateGxV3Total) {
        this.amtCum30RateGxV3Total = amtCum30RateGxV3Total;
    }

    public String getProfitCum30RateV3V2Total() {
        return profitCum30RateV3V2Total;
    }

    public void setProfitCum30RateV3V2Total(String profitCum30RateV3V2Total) {
        this.profitCum30RateV3V2Total = profitCum30RateV3V2Total;
    }

    public String getProfitCum30RateV3TotalAa() {
        return profitCum30RateV3TotalAa;
    }

    public void setProfitCum30RateV3TotalAa(String profitCum30RateV3TotalAa) {
        this.profitCum30RateV3TotalAa = profitCum30RateV3TotalAa;
    }

    public String getProfitCum30RateV3TotalA1() {
        return profitCum30RateV3TotalA1;
    }

    public void setProfitCum30RateV3TotalA1(String profitCum30RateV3TotalA1) {
        this.profitCum30RateV3TotalA1 = profitCum30RateV3TotalA1;
    }

    public String getProfitCum30RateV3TotalTc() {
        return profitCum30RateV3TotalTc;
    }

    public void setProfitCum30RateV3TotalTc(String profitCum30RateV3TotalTc) {
        this.profitCum30RateV3TotalTc = profitCum30RateV3TotalTc;
    }

    public String getProfitCum30RateV3TotalJc() {
        return profitCum30RateV3TotalJc;
    }

    public void setProfitCum30RateV3TotalJc(String profitCum30RateV3TotalJc) {
        this.profitCum30RateV3TotalJc = profitCum30RateV3TotalJc;
    }

    public String getProfitCum30RateV3TotalDc() {
        return profitCum30RateV3TotalDc;
    }

    public void setProfitCum30RateV3TotalDc(String profitCum30RateV3TotalDc) {
        this.profitCum30RateV3TotalDc = profitCum30RateV3TotalDc;
    }

    public String getProfitCum30RateV3TotalOn() {
        return profitCum30RateV3TotalOn;
    }

    public void setProfitCum30RateV3TotalOn(String profitCum30RateV3TotalOn) {
        this.profitCum30RateV3TotalOn = profitCum30RateV3TotalOn;
    }

    public String getProfitCum30RateV3TotalOff() {
        return profitCum30RateV3TotalOff;
    }

    public void setProfitCum30RateV3TotalOff(String profitCum30RateV3TotalOff) {
        this.profitCum30RateV3TotalOff = profitCum30RateV3TotalOff;
    }

    public String getAvgCnt() {
        return avgCnt;
    }

    public void setAvgCnt(String avgCnt) {
        this.avgCnt = avgCnt;
    }

    public String getAmtCum30Null() {
        return amtCum30Null;
    }

    public void setAmtCum30Null(String amtCum30Null) {
        this.amtCum30Null = amtCum30Null;
    }

    public String getAvgAmtCum30Null() {
        return avgAmtCum30Null;
    }

    public void setAvgAmtCum30Null(String avgAmtCum30Null) {
        this.avgAmtCum30Null = avgAmtCum30Null;
    }

    public String getAmtCum30New() {
        return amtCum30New;
    }

    public void setAmtCum30New(String amtCum30New) {
        this.amtCum30New = amtCum30New;
    }

    public String getAvgAmtCum30New() {
        return avgAmtCum30New;
    }

    public void setAvgAmtCum30New(String avgAmtCum30New) {
        this.avgAmtCum30New = avgAmtCum30New;
    }

    public String getAmtCum30RateNew() {
        return amtCum30RateNew;
    }

    public void setAmtCum30RateNew(String amtCum30RateNew) {
        this.amtCum30RateNew = amtCum30RateNew;
    }

    public String getProfitCum30New() {
        return profitCum30New;
    }

    public void setProfitCum30New(String profitCum30New) {
        this.profitCum30New = profitCum30New;
    }

    public String getProfitCum30RateNew() {
        return profitCum30RateNew;
    }

    public void setProfitCum30RateNew(String profitCum30RateNew) {
        this.profitCum30RateNew = profitCum30RateNew;
    }

    public String getAmtCum30V3() {
        return amtCum30V3;
    }

    public void setAmtCum30V3(String amtCum30V3) {
        this.amtCum30V3 = amtCum30V3;
    }

    public String getAmtCum30RateV3() {
        return amtCum30RateV3;
    }

    public void setAmtCum30RateV3(String amtCum30RateV3) {
        this.amtCum30RateV3 = amtCum30RateV3;
    }

    public String getAmtCum30RateV3V2() {
        return amtCum30RateV3V2;
    }

    public void setAmtCum30RateV3V2(String amtCum30RateV3V2) {
        this.amtCum30RateV3V2 = amtCum30RateV3V2;
    }

    public String getAmtCum30V3V2() {
        return amtCum30V3V2;
    }

    public void setAmtCum30V3V2(String amtCum30V3V2) {
        this.amtCum30V3V2 = amtCum30V3V2;
    }

    public String getAmtCum30V3V21() {
        return amtCum30V3V21;
    }

    public void setAmtCum30V3V21(String amtCum30V3V21) {
        this.amtCum30V3V21 = amtCum30V3V21;
    }

    public String getAmtCum30RateV3Aa() {
        return amtCum30RateV3Aa;
    }

    public void setAmtCum30RateV3Aa(String amtCum30RateV3Aa) {
        this.amtCum30RateV3Aa = amtCum30RateV3Aa;
    }

    public String getAmtCum30RateV3A1() {
        return amtCum30RateV3A1;
    }

    public void setAmtCum30RateV3A1(String amtCum30RateV3A1) {
        this.amtCum30RateV3A1 = amtCum30RateV3A1;
    }

    public String getAmtCum30RateV3Tc() {
        return amtCum30RateV3Tc;
    }

    public void setAmtCum30RateV3Tc(String amtCum30RateV3Tc) {
        this.amtCum30RateV3Tc = amtCum30RateV3Tc;
    }

    public String getAmtCum30RateV3Jc() {
        return amtCum30RateV3Jc;
    }

    public void setAmtCum30RateV3Jc(String amtCum30RateV3Jc) {
        this.amtCum30RateV3Jc = amtCum30RateV3Jc;
    }

    public String getAmtCum30RateV3Dc() {
        return amtCum30RateV3Dc;
    }

    public void setAmtCum30RateV3Dc(String amtCum30RateV3Dc) {
        this.amtCum30RateV3Dc = amtCum30RateV3Dc;
    }

    public String getAmtCum30RateV3On() {
        return amtCum30RateV3On;
    }

    public void setAmtCum30RateV3On(String amtCum30RateV3On) {
        this.amtCum30RateV3On = amtCum30RateV3On;
    }

    public String getAmtCum30RateV3Off() {
        return amtCum30RateV3Off;
    }

    public void setAmtCum30RateV3Off(String amtCum30RateV3Off) {
        this.amtCum30RateV3Off = amtCum30RateV3Off;
    }

    public String getProfitCum30V3() {
        return profitCum30V3;
    }

    public void setProfitCum30V3(String profitCum30V3) {
        this.profitCum30V3 = profitCum30V3;
    }

    public String getProfitCum30V3V2() {
        return profitCum30V3V2;
    }

    public void setProfitCum30V3V2(String profitCum30V3V2) {
        this.profitCum30V3V2 = profitCum30V3V2;
    }

    public String getProfitCum30V3V21() {
        return profitCum30V3V21;
    }

    public void setProfitCum30V3V21(String profitCum30V3V21) {
        this.profitCum30V3V21 = profitCum30V3V21;
    }

    public String getProfitCum30RateV3() {
        return profitCum30RateV3;
    }

    public void setProfitCum30RateV3(String profitCum30RateV3) {
        this.profitCum30RateV3 = profitCum30RateV3;
    }

    public String getAmtCum30RateGxV3() {
        return amtCum30RateGxV3;
    }

    public void setAmtCum30RateGxV3(String amtCum30RateGxV3) {
        this.amtCum30RateGxV3 = amtCum30RateGxV3;
    }

    public String getProfitCum30RateV3V2() {
        return profitCum30RateV3V2;
    }

    public void setProfitCum30RateV3V2(String profitCum30RateV3V2) {
        this.profitCum30RateV3V2 = profitCum30RateV3V2;
    }

    public String getProfitCum30RateV3Aa() {
        return profitCum30RateV3Aa;
    }

    public void setProfitCum30RateV3Aa(String profitCum30RateV3Aa) {
        this.profitCum30RateV3Aa = profitCum30RateV3Aa;
    }

    public String getProfitCum30RateV3A1() {
        return profitCum30RateV3A1;
    }

    public void setProfitCum30RateV3A1(String profitCum30RateV3A1) {
        this.profitCum30RateV3A1 = profitCum30RateV3A1;
    }

    public String getProfitCum30RateV3Tc() {
        return profitCum30RateV3Tc;
    }

    public void setProfitCum30RateV3Tc(String profitCum30RateV3Tc) {
        this.profitCum30RateV3Tc = profitCum30RateV3Tc;
    }

    public String getProfitCum30RateV3Jc() {
        return profitCum30RateV3Jc;
    }

    public void setProfitCum30RateV3Jc(String profitCum30RateV3Jc) {
        this.profitCum30RateV3Jc = profitCum30RateV3Jc;
    }

    public String getProfitCum30RateV3Dc() {
        return profitCum30RateV3Dc;
    }

    public void setProfitCum30RateV3Dc(String profitCum30RateV3Dc) {
        this.profitCum30RateV3Dc = profitCum30RateV3Dc;
    }

    public String getProfitCum30RateV3On() {
        return profitCum30RateV3On;
    }

    public void setProfitCum30RateV3On(String profitCum30RateV3On) {
        this.profitCum30RateV3On = profitCum30RateV3On;
    }

    public String getProfitCum30RateV3Off() {
        return profitCum30RateV3Off;
    }

    public void setProfitCum30RateV3Off(String profitCum30RateV3Off) {
        this.profitCum30RateV3Off = profitCum30RateV3Off;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TrackRetultEfficiencyAnalyse other = (TrackRetultEfficiencyAnalyse) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
                && (this.getZoneNew() == null ? other.getZoneNew() == null : this.getZoneNew().equals(other.getZoneNew()))
                && (this.getChainName() == null ? other.getChainName() == null : this.getChainName().equals(other.getChainName()))
                && (this.getReviseTypes() == null ? other.getReviseTypes() == null : this.getReviseTypes().equals(other.getReviseTypes()))
                && (this.getOrgCntV2() == null ? other.getOrgCntV2() == null : this.getOrgCntV2().equals(other.getOrgCntV2()))
                && (this.getOrgCntV3() == null ? other.getOrgCntV3() == null : this.getOrgCntV3().equals(other.getOrgCntV3()))
                && (this.getCntV3() == null ? other.getCntV3() == null : this.getCntV3().equals(other.getCntV3()))
                && (this.getCntV3V2() == null ? other.getCntV3V2() == null : this.getCntV3V2().equals(other.getCntV3V2()))
                && (this.getCntXz() == null ? other.getCntXz() == null : this.getCntXz().equals(other.getCntXz()))
                && (this.getCntTc() == null ? other.getCntTc() == null : this.getCntTc().equals(other.getCntTc()))
                && (this.getAvgSkuCnt() == null ? other.getAvgSkuCnt() == null : this.getAvgSkuCnt().equals(other.getAvgSkuCnt()))
                && (this.getAvgSkuCntV3V2() == null ? other.getAvgSkuCntV3V2() == null : this.getAvgSkuCntV3V2().equals(other.getAvgSkuCntV3V2()))
                && (this.getAvgSkuCntTc() == null ? other.getAvgSkuCntTc() == null : this.getAvgSkuCntTc().equals(other.getAvgSkuCntTc()))
                && (this.getAvgSkuCntJc() == null ? other.getAvgSkuCntJc() == null : this.getAvgSkuCntJc().equals(other.getAvgSkuCntJc()))
                && (this.getAvgSkuCntDc() == null ? other.getAvgSkuCntDc() == null : this.getAvgSkuCntDc().equals(other.getAvgSkuCntDc()))
                && (this.getAvgSkuCntAa() == null ? other.getAvgSkuCntAa() == null : this.getAvgSkuCntAa().equals(other.getAvgSkuCntAa()))
                && (this.getAvgSkuCntA1() == null ? other.getAvgSkuCntA1() == null : this.getAvgSkuCntA1().equals(other.getAvgSkuCntA1()))
                && (this.getAmtCum30Total() == null ? other.getAmtCum30Total() == null : this.getAmtCum30Total().equals(other.getAmtCum30Total()))
                && (this.getAmtCum30RateV3Total() == null ? other.getAmtCum30RateV3Total() == null : this.getAmtCum30RateV3Total().equals(other.getAmtCum30RateV3Total()))
                && (this.getAmtCum30RateV3V2Total() == null ? other.getAmtCum30RateV3V2Total() == null : this.getAmtCum30RateV3V2Total().equals(other.getAmtCum30RateV3V2Total()))
                && (this.getAmtCum30V3V2Total() == null ? other.getAmtCum30V3V2Total() == null : this.getAmtCum30V3V2Total().equals(other.getAmtCum30V3V2Total()))
                && (this.getAmtCum30V3V21Total() == null ? other.getAmtCum30V3V21Total() == null : this.getAmtCum30V3V21Total().equals(other.getAmtCum30V3V21Total()))
                && (this.getAmtCum30RateV3TotalAa() == null ? other.getAmtCum30RateV3TotalAa() == null : this.getAmtCum30RateV3TotalAa().equals(other.getAmtCum30RateV3TotalAa()))
                && (this.getAmtCum30RateV3TotalA1() == null ? other.getAmtCum30RateV3TotalA1() == null : this.getAmtCum30RateV3TotalA1().equals(other.getAmtCum30RateV3TotalA1()))
                && (this.getAmtCum30RateV3TotalTc() == null ? other.getAmtCum30RateV3TotalTc() == null : this.getAmtCum30RateV3TotalTc().equals(other.getAmtCum30RateV3TotalTc()))
                && (this.getAmtCum30RateV3TotalJc() == null ? other.getAmtCum30RateV3TotalJc() == null : this.getAmtCum30RateV3TotalJc().equals(other.getAmtCum30RateV3TotalJc()))
                && (this.getAmtCum30RateV3TotalDc() == null ? other.getAmtCum30RateV3TotalDc() == null : this.getAmtCum30RateV3TotalDc().equals(other.getAmtCum30RateV3TotalDc()))
                && (this.getAmtCum30RateV3TotalOn() == null ? other.getAmtCum30RateV3TotalOn() == null : this.getAmtCum30RateV3TotalOn().equals(other.getAmtCum30RateV3TotalOn()))
                && (this.getAmtCum30RateV3TotalOff() == null ? other.getAmtCum30RateV3TotalOff() == null : this.getAmtCum30RateV3TotalOff().equals(other.getAmtCum30RateV3TotalOff()))
                && (this.getProfitCum30V3Total() == null ? other.getProfitCum30V3Total() == null : this.getProfitCum30V3Total().equals(other.getProfitCum30V3Total()))
                && (this.getProfitCum30V3V2Total() == null ? other.getProfitCum30V3V2Total() == null : this.getProfitCum30V3V2Total().equals(other.getProfitCum30V3V2Total()))
                && (this.getProfitCum30V3V21Total() == null ? other.getProfitCum30V3V21Total() == null : this.getProfitCum30V3V21Total().equals(other.getProfitCum30V3V21Total()))
                && (this.getProfitCum30RateV3Total() == null ? other.getProfitCum30RateV3Total() == null : this.getProfitCum30RateV3Total().equals(other.getProfitCum30RateV3Total()))
                && (this.getAmtCum30RateGxV3Total() == null ? other.getAmtCum30RateGxV3Total() == null : this.getAmtCum30RateGxV3Total().equals(other.getAmtCum30RateGxV3Total()))
                && (this.getProfitCum30RateV3V2Total() == null ? other.getProfitCum30RateV3V2Total() == null : this.getProfitCum30RateV3V2Total().equals(other.getProfitCum30RateV3V2Total()))
                && (this.getProfitCum30RateV3TotalAa() == null ? other.getProfitCum30RateV3TotalAa() == null : this.getProfitCum30RateV3TotalAa().equals(other.getProfitCum30RateV3TotalAa()))
                && (this.getProfitCum30RateV3TotalA1() == null ? other.getProfitCum30RateV3TotalA1() == null : this.getProfitCum30RateV3TotalA1().equals(other.getProfitCum30RateV3TotalA1()))
                && (this.getProfitCum30RateV3TotalTc() == null ? other.getProfitCum30RateV3TotalTc() == null : this.getProfitCum30RateV3TotalTc().equals(other.getProfitCum30RateV3TotalTc()))
                && (this.getProfitCum30RateV3TotalJc() == null ? other.getProfitCum30RateV3TotalJc() == null : this.getProfitCum30RateV3TotalJc().equals(other.getProfitCum30RateV3TotalJc()))
                && (this.getProfitCum30RateV3TotalDc() == null ? other.getProfitCum30RateV3TotalDc() == null : this.getProfitCum30RateV3TotalDc().equals(other.getProfitCum30RateV3TotalDc()))
                && (this.getProfitCum30RateV3TotalOn() == null ? other.getProfitCum30RateV3TotalOn() == null : this.getProfitCum30RateV3TotalOn().equals(other.getProfitCum30RateV3TotalOn()))
                && (this.getProfitCum30RateV3TotalOff() == null ? other.getProfitCum30RateV3TotalOff() == null : this.getProfitCum30RateV3TotalOff().equals(other.getProfitCum30RateV3TotalOff()))
                && (this.getAvgCnt() == null ? other.getAvgCnt() == null : this.getAvgCnt().equals(other.getAvgCnt()))
                && (this.getAmtCum30Null() == null ? other.getAmtCum30Null() == null : this.getAmtCum30Null().equals(other.getAmtCum30Null()))
                && (this.getAvgAmtCum30Null() == null ? other.getAvgAmtCum30Null() == null : this.getAvgAmtCum30Null().equals(other.getAvgAmtCum30Null()))
                && (this.getAmtCum30New() == null ? other.getAmtCum30New() == null : this.getAmtCum30New().equals(other.getAmtCum30New()))
                && (this.getAvgAmtCum30New() == null ? other.getAvgAmtCum30New() == null : this.getAvgAmtCum30New().equals(other.getAvgAmtCum30New()))
                && (this.getAmtCum30RateNew() == null ? other.getAmtCum30RateNew() == null : this.getAmtCum30RateNew().equals(other.getAmtCum30RateNew()))
                && (this.getProfitCum30New() == null ? other.getProfitCum30New() == null : this.getProfitCum30New().equals(other.getProfitCum30New()))
                && (this.getProfitCum30RateNew() == null ? other.getProfitCum30RateNew() == null : this.getProfitCum30RateNew().equals(other.getProfitCum30RateNew()))
                && (this.getAmtCum30V3() == null ? other.getAmtCum30V3() == null : this.getAmtCum30V3().equals(other.getAmtCum30V3()))
                && (this.getAmtCum30RateV3() == null ? other.getAmtCum30RateV3() == null : this.getAmtCum30RateV3().equals(other.getAmtCum30RateV3()))
                && (this.getAmtCum30RateV3V2() == null ? other.getAmtCum30RateV3V2() == null : this.getAmtCum30RateV3V2().equals(other.getAmtCum30RateV3V2()))
                && (this.getAmtCum30V3V2() == null ? other.getAmtCum30V3V2() == null : this.getAmtCum30V3V2().equals(other.getAmtCum30V3V2()))
                && (this.getAmtCum30V3V21() == null ? other.getAmtCum30V3V21() == null : this.getAmtCum30V3V21().equals(other.getAmtCum30V3V21()))
                && (this.getAmtCum30RateV3Aa() == null ? other.getAmtCum30RateV3Aa() == null : this.getAmtCum30RateV3Aa().equals(other.getAmtCum30RateV3Aa()))
                && (this.getAmtCum30RateV3A1() == null ? other.getAmtCum30RateV3A1() == null : this.getAmtCum30RateV3A1().equals(other.getAmtCum30RateV3A1()))
                && (this.getAmtCum30RateV3Tc() == null ? other.getAmtCum30RateV3Tc() == null : this.getAmtCum30RateV3Tc().equals(other.getAmtCum30RateV3Tc()))
                && (this.getAmtCum30RateV3Jc() == null ? other.getAmtCum30RateV3Jc() == null : this.getAmtCum30RateV3Jc().equals(other.getAmtCum30RateV3Jc()))
                && (this.getAmtCum30RateV3Dc() == null ? other.getAmtCum30RateV3Dc() == null : this.getAmtCum30RateV3Dc().equals(other.getAmtCum30RateV3Dc()))
                && (this.getAmtCum30RateV3On() == null ? other.getAmtCum30RateV3On() == null : this.getAmtCum30RateV3On().equals(other.getAmtCum30RateV3On()))
                && (this.getAmtCum30RateV3Off() == null ? other.getAmtCum30RateV3Off() == null : this.getAmtCum30RateV3Off().equals(other.getAmtCum30RateV3Off()))
                && (this.getProfitCum30V3() == null ? other.getProfitCum30V3() == null : this.getProfitCum30V3().equals(other.getProfitCum30V3()))
                && (this.getProfitCum30V3V2() == null ? other.getProfitCum30V3V2() == null : this.getProfitCum30V3V2().equals(other.getProfitCum30V3V2()))
                && (this.getProfitCum30V3V21() == null ? other.getProfitCum30V3V21() == null : this.getProfitCum30V3V21().equals(other.getProfitCum30V3V21()))
                && (this.getProfitCum30RateV3() == null ? other.getProfitCum30RateV3() == null : this.getProfitCum30RateV3().equals(other.getProfitCum30RateV3()))
                && (this.getAmtCum30RateGxV3() == null ? other.getAmtCum30RateGxV3() == null : this.getAmtCum30RateGxV3().equals(other.getAmtCum30RateGxV3()))
                && (this.getProfitCum30RateV3V2() == null ? other.getProfitCum30RateV3V2() == null : this.getProfitCum30RateV3V2().equals(other.getProfitCum30RateV3V2()))
                && (this.getProfitCum30RateV3Aa() == null ? other.getProfitCum30RateV3Aa() == null : this.getProfitCum30RateV3Aa().equals(other.getProfitCum30RateV3Aa()))
                && (this.getProfitCum30RateV3A1() == null ? other.getProfitCum30RateV3A1() == null : this.getProfitCum30RateV3A1().equals(other.getProfitCum30RateV3A1()))
                && (this.getProfitCum30RateV3Tc() == null ? other.getProfitCum30RateV3Tc() == null : this.getProfitCum30RateV3Tc().equals(other.getProfitCum30RateV3Tc()))
                && (this.getProfitCum30RateV3Jc() == null ? other.getProfitCum30RateV3Jc() == null : this.getProfitCum30RateV3Jc().equals(other.getProfitCum30RateV3Jc()))
                && (this.getProfitCum30RateV3Dc() == null ? other.getProfitCum30RateV3Dc() == null : this.getProfitCum30RateV3Dc().equals(other.getProfitCum30RateV3Dc()))
                && (this.getProfitCum30RateV3On() == null ? other.getProfitCum30RateV3On() == null : this.getProfitCum30RateV3On().equals(other.getProfitCum30RateV3On()))
                && (this.getProfitCum30RateV3Off() == null ? other.getProfitCum30RateV3Off() == null : this.getProfitCum30RateV3Off().equals(other.getProfitCum30RateV3Off()))
                && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getZoneNew() == null) ? 0 : getZoneNew().hashCode());
        result = prime * result + ((getChainName() == null) ? 0 : getChainName().hashCode());
        result = prime * result + ((getReviseTypes() == null) ? 0 : getReviseTypes().hashCode());
        result = prime * result + ((getOrgCntV2() == null) ? 0 : getOrgCntV2().hashCode());
        result = prime * result + ((getOrgCntV3() == null) ? 0 : getOrgCntV3().hashCode());
        result = prime * result + ((getCntV3() == null) ? 0 : getCntV3().hashCode());
        result = prime * result + ((getCntV3V2() == null) ? 0 : getCntV3V2().hashCode());
        result = prime * result + ((getCntXz() == null) ? 0 : getCntXz().hashCode());
        result = prime * result + ((getCntTc() == null) ? 0 : getCntTc().hashCode());
        result = prime * result + ((getAvgSkuCnt() == null) ? 0 : getAvgSkuCnt().hashCode());
        result = prime * result + ((getAvgSkuCntV3V2() == null) ? 0 : getAvgSkuCntV3V2().hashCode());
        result = prime * result + ((getAvgSkuCntTc() == null) ? 0 : getAvgSkuCntTc().hashCode());
        result = prime * result + ((getAvgSkuCntJc() == null) ? 0 : getAvgSkuCntJc().hashCode());
        result = prime * result + ((getAvgSkuCntDc() == null) ? 0 : getAvgSkuCntDc().hashCode());
        result = prime * result + ((getAvgSkuCntAa() == null) ? 0 : getAvgSkuCntAa().hashCode());
        result = prime * result + ((getAvgSkuCntA1() == null) ? 0 : getAvgSkuCntA1().hashCode());
        result = prime * result + ((getAmtCum30Total() == null) ? 0 : getAmtCum30Total().hashCode());
        result = prime * result + ((getAmtCum30RateV3Total() == null) ? 0 : getAmtCum30RateV3Total().hashCode());
        result = prime * result + ((getAmtCum30RateV3V2Total() == null) ? 0 : getAmtCum30RateV3V2Total().hashCode());
        result = prime * result + ((getAmtCum30V3V2Total() == null) ? 0 : getAmtCum30V3V2Total().hashCode());
        result = prime * result + ((getAmtCum30V3V21Total() == null) ? 0 : getAmtCum30V3V21Total().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalAa() == null) ? 0 : getAmtCum30RateV3TotalAa().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalA1() == null) ? 0 : getAmtCum30RateV3TotalA1().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalTc() == null) ? 0 : getAmtCum30RateV3TotalTc().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalJc() == null) ? 0 : getAmtCum30RateV3TotalJc().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalDc() == null) ? 0 : getAmtCum30RateV3TotalDc().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalOn() == null) ? 0 : getAmtCum30RateV3TotalOn().hashCode());
        result = prime * result + ((getAmtCum30RateV3TotalOff() == null) ? 0 : getAmtCum30RateV3TotalOff().hashCode());
        result = prime * result + ((getProfitCum30V3Total() == null) ? 0 : getProfitCum30V3Total().hashCode());
        result = prime * result + ((getProfitCum30V3V2Total() == null) ? 0 : getProfitCum30V3V2Total().hashCode());
        result = prime * result + ((getProfitCum30V3V21Total() == null) ? 0 : getProfitCum30V3V21Total().hashCode());
        result = prime * result + ((getProfitCum30RateV3Total() == null) ? 0 : getProfitCum30RateV3Total().hashCode());
        result = prime * result + ((getAmtCum30RateGxV3Total() == null) ? 0 : getAmtCum30RateGxV3Total().hashCode());
        result = prime * result + ((getProfitCum30RateV3V2Total() == null) ? 0 : getProfitCum30RateV3V2Total().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalAa() == null) ? 0 : getProfitCum30RateV3TotalAa().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalA1() == null) ? 0 : getProfitCum30RateV3TotalA1().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalTc() == null) ? 0 : getProfitCum30RateV3TotalTc().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalJc() == null) ? 0 : getProfitCum30RateV3TotalJc().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalDc() == null) ? 0 : getProfitCum30RateV3TotalDc().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalOn() == null) ? 0 : getProfitCum30RateV3TotalOn().hashCode());
        result = prime * result + ((getProfitCum30RateV3TotalOff() == null) ? 0 : getProfitCum30RateV3TotalOff().hashCode());
        result = prime * result + ((getAvgCnt() == null) ? 0 : getAvgCnt().hashCode());
        result = prime * result + ((getAmtCum30Null() == null) ? 0 : getAmtCum30Null().hashCode());
        result = prime * result + ((getAvgAmtCum30Null() == null) ? 0 : getAvgAmtCum30Null().hashCode());
        result = prime * result + ((getAmtCum30New() == null) ? 0 : getAmtCum30New().hashCode());
        result = prime * result + ((getAvgAmtCum30New() == null) ? 0 : getAvgAmtCum30New().hashCode());
        result = prime * result + ((getAmtCum30RateNew() == null) ? 0 : getAmtCum30RateNew().hashCode());
        result = prime * result + ((getProfitCum30New() == null) ? 0 : getProfitCum30New().hashCode());
        result = prime * result + ((getProfitCum30RateNew() == null) ? 0 : getProfitCum30RateNew().hashCode());
        result = prime * result + ((getAmtCum30V3() == null) ? 0 : getAmtCum30V3().hashCode());
        result = prime * result + ((getAmtCum30RateV3() == null) ? 0 : getAmtCum30RateV3().hashCode());
        result = prime * result + ((getAmtCum30RateV3V2() == null) ? 0 : getAmtCum30RateV3V2().hashCode());
        result = prime * result + ((getAmtCum30V3V2() == null) ? 0 : getAmtCum30V3V2().hashCode());
        result = prime * result + ((getAmtCum30V3V21() == null) ? 0 : getAmtCum30V3V21().hashCode());
        result = prime * result + ((getAmtCum30RateV3Aa() == null) ? 0 : getAmtCum30RateV3Aa().hashCode());
        result = prime * result + ((getAmtCum30RateV3A1() == null) ? 0 : getAmtCum30RateV3A1().hashCode());
        result = prime * result + ((getAmtCum30RateV3Tc() == null) ? 0 : getAmtCum30RateV3Tc().hashCode());
        result = prime * result + ((getAmtCum30RateV3Jc() == null) ? 0 : getAmtCum30RateV3Jc().hashCode());
        result = prime * result + ((getAmtCum30RateV3Dc() == null) ? 0 : getAmtCum30RateV3Dc().hashCode());
        result = prime * result + ((getAmtCum30RateV3On() == null) ? 0 : getAmtCum30RateV3On().hashCode());
        result = prime * result + ((getAmtCum30RateV3Off() == null) ? 0 : getAmtCum30RateV3Off().hashCode());
        result = prime * result + ((getProfitCum30V3() == null) ? 0 : getProfitCum30V3().hashCode());
        result = prime * result + ((getProfitCum30V3V2() == null) ? 0 : getProfitCum30V3V2().hashCode());
        result = prime * result + ((getProfitCum30V3V21() == null) ? 0 : getProfitCum30V3V21().hashCode());
        result = prime * result + ((getProfitCum30RateV3() == null) ? 0 : getProfitCum30RateV3().hashCode());
        result = prime * result + ((getAmtCum30RateGxV3() == null) ? 0 : getAmtCum30RateGxV3().hashCode());
        result = prime * result + ((getProfitCum30RateV3V2() == null) ? 0 : getProfitCum30RateV3V2().hashCode());
        result = prime * result + ((getProfitCum30RateV3Aa() == null) ? 0 : getProfitCum30RateV3Aa().hashCode());
        result = prime * result + ((getProfitCum30RateV3A1() == null) ? 0 : getProfitCum30RateV3A1().hashCode());
        result = prime * result + ((getProfitCum30RateV3Tc() == null) ? 0 : getProfitCum30RateV3Tc().hashCode());
        result = prime * result + ((getProfitCum30RateV3Jc() == null) ? 0 : getProfitCum30RateV3Jc().hashCode());
        result = prime * result + ((getProfitCum30RateV3Dc() == null) ? 0 : getProfitCum30RateV3Dc().hashCode());
        result = prime * result + ((getProfitCum30RateV3On() == null) ? 0 : getProfitCum30RateV3On().hashCode());
        result = prime * result + ((getProfitCum30RateV3Off() == null) ? 0 : getProfitCum30RateV3Off().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", zoneNew=").append(zoneNew);
        sb.append(", chainName=").append(chainName);
        sb.append(", reviseTypes=").append(reviseTypes);
        sb.append(", orgCntV2=").append(orgCntV2);
        sb.append(", orgCntV3=").append(orgCntV3);
        sb.append(", cntV3=").append(cntV3);
        sb.append(", cntV3V2=").append(cntV3V2);
        sb.append(", cntXz=").append(cntXz);
        sb.append(", cntTc=").append(cntTc);
        sb.append(", avgSkuCnt=").append(avgSkuCnt);
        sb.append(", avgSkuCntV3V2=").append(avgSkuCntV3V2);
        sb.append(", avgSkuCntTc=").append(avgSkuCntTc);
        sb.append(", avgSkuCntJc=").append(avgSkuCntJc);
        sb.append(", avgSkuCntDc=").append(avgSkuCntDc);
        sb.append(", avgSkuCntAa=").append(avgSkuCntAa);
        sb.append(", avgSkuCntA1=").append(avgSkuCntA1);
        sb.append(", amtCum30Total=").append(amtCum30Total);
        sb.append(", amtCum30RateV3Total=").append(amtCum30RateV3Total);
        sb.append(", amtCum30RateV3V2Total=").append(amtCum30RateV3V2Total);
        sb.append(", amtCum30V3V2Total=").append(amtCum30V3V2Total);
        sb.append(", amtCum30V3V21Total=").append(amtCum30V3V21Total);
        sb.append(", amtCum30RateV3TotalAa=").append(amtCum30RateV3TotalAa);
        sb.append(", amtCum30RateV3TotalA1=").append(amtCum30RateV3TotalA1);
        sb.append(", amtCum30RateV3TotalTc=").append(amtCum30RateV3TotalTc);
        sb.append(", amtCum30RateV3TotalJc=").append(amtCum30RateV3TotalJc);
        sb.append(", amtCum30RateV3TotalDc=").append(amtCum30RateV3TotalDc);
        sb.append(", amtCum30RateV3TotalOn=").append(amtCum30RateV3TotalOn);
        sb.append(", amtCum30RateV3TotalOff=").append(amtCum30RateV3TotalOff);
        sb.append(", profitCum30V3Total=").append(profitCum30V3Total);
        sb.append(", profitCum30V3V2Total=").append(profitCum30V3V2Total);
        sb.append(", profitCum30V3V21Total=").append(profitCum30V3V21Total);
        sb.append(", profitCum30RateV3Total=").append(profitCum30RateV3Total);
        sb.append(", amtCum30RateGxV3Total=").append(amtCum30RateGxV3Total);
        sb.append(", profitCum30RateV3V2Total=").append(profitCum30RateV3V2Total);
        sb.append(", profitCum30RateV3TotalAa=").append(profitCum30RateV3TotalAa);
        sb.append(", profitCum30RateV3TotalA1=").append(profitCum30RateV3TotalA1);
        sb.append(", profitCum30RateV3TotalTc=").append(profitCum30RateV3TotalTc);
        sb.append(", profitCum30RateV3TotalJc=").append(profitCum30RateV3TotalJc);
        sb.append(", profitCum30RateV3TotalDc=").append(profitCum30RateV3TotalDc);
        sb.append(", profitCum30RateV3TotalOn=").append(profitCum30RateV3TotalOn);
        sb.append(", profitCum30RateV3TotalOff=").append(profitCum30RateV3TotalOff);
        sb.append(", avgCnt=").append(avgCnt);
        sb.append(", amtCum30Null=").append(amtCum30Null);
        sb.append(", avgAmtCum30Null=").append(avgAmtCum30Null);
        sb.append(", amtCum30New=").append(amtCum30New);
        sb.append(", avgAmtCum30New=").append(avgAmtCum30New);
        sb.append(", amtCum30RateNew=").append(amtCum30RateNew);
        sb.append(", profitCum30New=").append(profitCum30New);
        sb.append(", profitCum30RateNew=").append(profitCum30RateNew);
        sb.append(", amtCum30V3=").append(amtCum30V3);
        sb.append(", amtCum30RateV3=").append(amtCum30RateV3);
        sb.append(", amtCum30RateV3V2=").append(amtCum30RateV3V2);
        sb.append(", amtCum30V3V2=").append(amtCum30V3V2);
        sb.append(", amtCum30V3V21=").append(amtCum30V3V21);
        sb.append(", amtCum30RateV3Aa=").append(amtCum30RateV3Aa);
        sb.append(", amtCum30RateV3A1=").append(amtCum30RateV3A1);
        sb.append(", amtCum30RateV3Tc=").append(amtCum30RateV3Tc);
        sb.append(", amtCum30RateV3Jc=").append(amtCum30RateV3Jc);
        sb.append(", amtCum30RateV3Dc=").append(amtCum30RateV3Dc);
        sb.append(", amtCum30RateV3On=").append(amtCum30RateV3On);
        sb.append(", amtCum30RateV3Off=").append(amtCum30RateV3Off);
        sb.append(", profitCum30V3=").append(profitCum30V3);
        sb.append(", profitCum30V3V2=").append(profitCum30V3V2);
        sb.append(", profitCum30V3V21=").append(profitCum30V3V21);
        sb.append(", profitCum30RateV3=").append(profitCum30RateV3);
        sb.append(", amtCum30RateGxV3=").append(amtCum30RateGxV3);
        sb.append(", profitCum30RateV3V2=").append(profitCum30RateV3V2);
        sb.append(", profitCum30RateV3Aa=").append(profitCum30RateV3Aa);
        sb.append(", profitCum30RateV3A1=").append(profitCum30RateV3A1);
        sb.append(", profitCum30RateV3Tc=").append(profitCum30RateV3Tc);
        sb.append(", profitCum30RateV3Jc=").append(profitCum30RateV3Jc);
        sb.append(", profitCum30RateV3Dc=").append(profitCum30RateV3Dc);
        sb.append(", profitCum30RateV3On=").append(profitCum30RateV3On);
        sb.append(", profitCum30RateV3Off=").append(profitCum30RateV3Off);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}