package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlSkuMaxLimitConfigureExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlSkuMaxLimitConfigureExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNull() {
            addCriterion("platform_org_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNotNull() {
            addCriterion("platform_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdEqualTo(Long value) {
            addCriterion("platform_org_id =", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotEqualTo(Long value) {
            addCriterion("platform_org_id <>", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThan(Long value) {
            addCriterion("platform_org_id >", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("platform_org_id >=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThan(Long value) {
            addCriterion("platform_org_id <", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("platform_org_id <=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIn(List<Long> values) {
            addCriterion("platform_org_id in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotIn(List<Long> values) {
            addCriterion("platform_org_id not in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdBetween(Long value1, Long value2) {
            addCriterion("platform_org_id between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("platform_org_id not between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNull() {
            addCriterion("business_org_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNotNull() {
            addCriterion("business_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdEqualTo(Long value) {
            addCriterion("business_org_id =", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotEqualTo(Long value) {
            addCriterion("business_org_id <>", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThan(Long value) {
            addCriterion("business_org_id >", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_org_id >=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThan(Long value) {
            addCriterion("business_org_id <", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("business_org_id <=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIn(List<Long> values) {
            addCriterion("business_org_id in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotIn(List<Long> values) {
            addCriterion("business_org_id not in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdBetween(Long value1, Long value2) {
            addCriterion("business_org_id between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("business_org_id not between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIsNull() {
            addCriterion("business_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIsNotNull() {
            addCriterion("business_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNameEqualTo(String value) {
            addCriterion("business_name =", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotEqualTo(String value) {
            addCriterion("business_name <>", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameGreaterThan(String value) {
            addCriterion("business_name >", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_name >=", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLessThan(String value) {
            addCriterion("business_name <", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLessThanOrEqualTo(String value) {
            addCriterion("business_name <=", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLike(String value) {
            addCriterion("business_name like", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotLike(String value) {
            addCriterion("business_name not like", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIn(List<String> values) {
            addCriterion("business_name in", values, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotIn(List<String> values) {
            addCriterion("business_name not in", values, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameBetween(String value1, String value2) {
            addCriterion("business_name between", value1, value2, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotBetween(String value1, String value2) {
            addCriterion("business_name not between", value1, value2, "businessName");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNull() {
            addCriterion("store_type is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNotNull() {
            addCriterion("store_type is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeEqualTo(String value) {
            addCriterion("store_type =", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotEqualTo(String value) {
            addCriterion("store_type <>", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThan(String value) {
            addCriterion("store_type >", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThanOrEqualTo(String value) {
            addCriterion("store_type >=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThan(String value) {
            addCriterion("store_type <", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThanOrEqualTo(String value) {
            addCriterion("store_type <=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLike(String value) {
            addCriterion("store_type like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotLike(String value) {
            addCriterion("store_type not like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIn(List<String> values) {
            addCriterion("store_type in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotIn(List<String> values) {
            addCriterion("store_type not in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeBetween(String value1, String value2) {
            addCriterion("store_type between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotBetween(String value1, String value2) {
            addCriterion("store_type not between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameIsNull() {
            addCriterion("store_type_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameIsNotNull() {
            addCriterion("store_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameEqualTo(String value) {
            addCriterion("store_type_name =", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotEqualTo(String value) {
            addCriterion("store_type_name <>", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameGreaterThan(String value) {
            addCriterion("store_type_name >", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_type_name >=", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameLessThan(String value) {
            addCriterion("store_type_name <", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameLessThanOrEqualTo(String value) {
            addCriterion("store_type_name <=", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameLike(String value) {
            addCriterion("store_type_name like", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotLike(String value) {
            addCriterion("store_type_name not like", value, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameIn(List<String> values) {
            addCriterion("store_type_name in", values, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotIn(List<String> values) {
            addCriterion("store_type_name not in", values, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameBetween(String value1, String value2) {
            addCriterion("store_type_name between", value1, value2, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNameNotBetween(String value1, String value2) {
            addCriterion("store_type_name not between", value1, value2, "storeTypeName");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNull() {
            addCriterion("middle_category is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNotNull() {
            addCriterion("middle_category is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryEqualTo(String value) {
            addCriterion("middle_category =", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotEqualTo(String value) {
            addCriterion("middle_category <>", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThan(String value) {
            addCriterion("middle_category >", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category >=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThan(String value) {
            addCriterion("middle_category <", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThanOrEqualTo(String value) {
            addCriterion("middle_category <=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLike(String value) {
            addCriterion("middle_category like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotLike(String value) {
            addCriterion("middle_category not like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIn(List<String> values) {
            addCriterion("middle_category in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotIn(List<String> values) {
            addCriterion("middle_category not in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryBetween(String value1, String value2) {
            addCriterion("middle_category between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotBetween(String value1, String value2) {
            addCriterion("middle_category not between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNull() {
            addCriterion("middle_category_name is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNotNull() {
            addCriterion("middle_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameEqualTo(String value) {
            addCriterion("middle_category_name =", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotEqualTo(String value) {
            addCriterion("middle_category_name <>", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThan(String value) {
            addCriterion("middle_category_name >", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category_name >=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThan(String value) {
            addCriterion("middle_category_name <", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("middle_category_name <=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLike(String value) {
            addCriterion("middle_category_name like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotLike(String value) {
            addCriterion("middle_category_name not like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIn(List<String> values) {
            addCriterion("middle_category_name in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotIn(List<String> values) {
            addCriterion("middle_category_name not in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameBetween(String value1, String value2) {
            addCriterion("middle_category_name between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotBetween(String value1, String value2) {
            addCriterion("middle_category_name not between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNull() {
            addCriterion("small_category is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNotNull() {
            addCriterion("small_category is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryEqualTo(String value) {
            addCriterion("small_category =", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotEqualTo(String value) {
            addCriterion("small_category <>", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThan(String value) {
            addCriterion("small_category >", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("small_category >=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThan(String value) {
            addCriterion("small_category <", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThanOrEqualTo(String value) {
            addCriterion("small_category <=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLike(String value) {
            addCriterion("small_category like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotLike(String value) {
            addCriterion("small_category not like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIn(List<String> values) {
            addCriterion("small_category in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotIn(List<String> values) {
            addCriterion("small_category not in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryBetween(String value1, String value2) {
            addCriterion("small_category between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotBetween(String value1, String value2) {
            addCriterion("small_category not between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNull() {
            addCriterion("small_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNotNull() {
            addCriterion("small_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameEqualTo(String value) {
            addCriterion("small_category_name =", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotEqualTo(String value) {
            addCriterion("small_category_name <>", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThan(String value) {
            addCriterion("small_category_name >", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("small_category_name >=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThan(String value) {
            addCriterion("small_category_name <", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("small_category_name <=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLike(String value) {
            addCriterion("small_category_name like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotLike(String value) {
            addCriterion("small_category_name not like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIn(List<String> values) {
            addCriterion("small_category_name in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotIn(List<String> values) {
            addCriterion("small_category_name not in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameBetween(String value1, String value2) {
            addCriterion("small_category_name between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotBetween(String value1, String value2) {
            addCriterion("small_category_name not between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNull() {
            addCriterion("sub_category is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNotNull() {
            addCriterion("sub_category is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryEqualTo(String value) {
            addCriterion("sub_category =", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotEqualTo(String value) {
            addCriterion("sub_category <>", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThan(String value) {
            addCriterion("sub_category >", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category >=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThan(String value) {
            addCriterion("sub_category <", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThanOrEqualTo(String value) {
            addCriterion("sub_category <=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLike(String value) {
            addCriterion("sub_category like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotLike(String value) {
            addCriterion("sub_category not like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIn(List<String> values) {
            addCriterion("sub_category in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotIn(List<String> values) {
            addCriterion("sub_category not in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryBetween(String value1, String value2) {
            addCriterion("sub_category between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotBetween(String value1, String value2) {
            addCriterion("sub_category not between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNull() {
            addCriterion("sub_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNotNull() {
            addCriterion("sub_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameEqualTo(String value) {
            addCriterion("sub_category_name =", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotEqualTo(String value) {
            addCriterion("sub_category_name <>", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThan(String value) {
            addCriterion("sub_category_name >", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_name >=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThan(String value) {
            addCriterion("sub_category_name <", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("sub_category_name <=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLike(String value) {
            addCriterion("sub_category_name like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotLike(String value) {
            addCriterion("sub_category_name not like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIn(List<String> values) {
            addCriterion("sub_category_name in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotIn(List<String> values) {
            addCriterion("sub_category_name not in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameBetween(String value1, String value2) {
            addCriterion("sub_category_name between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotBetween(String value1, String value2) {
            addCriterion("sub_category_name not between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitIsNull() {
            addCriterion("sku_max_limit is null");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitIsNotNull() {
            addCriterion("sku_max_limit is not null");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitEqualTo(Integer value) {
            addCriterion("sku_max_limit =", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitNotEqualTo(Integer value) {
            addCriterion("sku_max_limit <>", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitGreaterThan(Integer value) {
            addCriterion("sku_max_limit >", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_max_limit >=", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitLessThan(Integer value) {
            addCriterion("sku_max_limit <", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitLessThanOrEqualTo(Integer value) {
            addCriterion("sku_max_limit <=", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitIn(List<Integer> values) {
            addCriterion("sku_max_limit in", values, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitNotIn(List<Integer> values) {
            addCriterion("sku_max_limit not in", values, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitBetween(Integer value1, Integer value2) {
            addCriterion("sku_max_limit between", value1, value2, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_max_limit not between", value1, value2, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitIsNull() {
            addCriterion("sku_suggested_limit is null");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitIsNotNull() {
            addCriterion("sku_suggested_limit is not null");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitEqualTo(Integer value) {
            addCriterion("sku_suggested_limit =", value, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitNotEqualTo(Integer value) {
            addCriterion("sku_suggested_limit <>", value, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitGreaterThan(Integer value) {
            addCriterion("sku_suggested_limit >", value, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_suggested_limit >=", value, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitLessThan(Integer value) {
            addCriterion("sku_suggested_limit <", value, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitLessThanOrEqualTo(Integer value) {
            addCriterion("sku_suggested_limit <=", value, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitIn(List<Integer> values) {
            addCriterion("sku_suggested_limit in", values, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitNotIn(List<Integer> values) {
            addCriterion("sku_suggested_limit not in", values, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitBetween(Integer value1, Integer value2) {
            addCriterion("sku_suggested_limit between", value1, value2, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_suggested_limit not between", value1, value2, "skuSuggestedLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitIsNull() {
            addCriterion("sku_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitIsNotNull() {
            addCriterion("sku_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitEqualTo(Integer value) {
            addCriterion("sku_lower_limit =", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitNotEqualTo(Integer value) {
            addCriterion("sku_lower_limit <>", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitGreaterThan(Integer value) {
            addCriterion("sku_lower_limit >", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_lower_limit >=", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitLessThan(Integer value) {
            addCriterion("sku_lower_limit <", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitLessThanOrEqualTo(Integer value) {
            addCriterion("sku_lower_limit <=", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitIn(List<Integer> values) {
            addCriterion("sku_lower_limit in", values, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitNotIn(List<Integer> values) {
            addCriterion("sku_lower_limit not in", values, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitBetween(Integer value1, Integer value2) {
            addCriterion("sku_lower_limit between", value1, value2, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_lower_limit not between", value1, value2, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitIsNull() {
            addCriterion("sku_suggested_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitIsNotNull() {
            addCriterion("sku_suggested_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitEqualTo(Integer value) {
            addCriterion("sku_suggested_lower_limit =", value, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitNotEqualTo(Integer value) {
            addCriterion("sku_suggested_lower_limit <>", value, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitGreaterThan(Integer value) {
            addCriterion("sku_suggested_lower_limit >", value, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_suggested_lower_limit >=", value, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitLessThan(Integer value) {
            addCriterion("sku_suggested_lower_limit <", value, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitLessThanOrEqualTo(Integer value) {
            addCriterion("sku_suggested_lower_limit <=", value, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitIn(List<Integer> values) {
            addCriterion("sku_suggested_lower_limit in", values, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitNotIn(List<Integer> values) {
            addCriterion("sku_suggested_lower_limit not in", values, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitBetween(Integer value1, Integer value2) {
            addCriterion("sku_suggested_lower_limit between", value1, value2, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuSuggestedLowerLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_suggested_lower_limit not between", value1, value2, "skuSuggestedLowerLimit");
            return (Criteria) this;
        }

        public Criteria andIngredientCountIsNull() {
            addCriterion("ingredient_count is null");
            return (Criteria) this;
        }

        public Criteria andIngredientCountIsNotNull() {
            addCriterion("ingredient_count is not null");
            return (Criteria) this;
        }

        public Criteria andIngredientCountEqualTo(Integer value) {
            addCriterion("ingredient_count =", value, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountNotEqualTo(Integer value) {
            addCriterion("ingredient_count <>", value, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountGreaterThan(Integer value) {
            addCriterion("ingredient_count >", value, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ingredient_count >=", value, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountLessThan(Integer value) {
            addCriterion("ingredient_count <", value, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountLessThanOrEqualTo(Integer value) {
            addCriterion("ingredient_count <=", value, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountIn(List<Integer> values) {
            addCriterion("ingredient_count in", values, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountNotIn(List<Integer> values) {
            addCriterion("ingredient_count not in", values, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountBetween(Integer value1, Integer value2) {
            addCriterion("ingredient_count between", value1, value2, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andIngredientCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ingredient_count not between", value1, value2, "ingredientCount");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andEnvIsNull() {
            addCriterion("env is null");
            return (Criteria) this;
        }

        public Criteria andEnvIsNotNull() {
            addCriterion("env is not null");
            return (Criteria) this;
        }

        public Criteria andEnvEqualTo(String value) {
            addCriterion("env =", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotEqualTo(String value) {
            addCriterion("env <>", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvGreaterThan(String value) {
            addCriterion("env >", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvGreaterThanOrEqualTo(String value) {
            addCriterion("env >=", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvLessThan(String value) {
            addCriterion("env <", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvLessThanOrEqualTo(String value) {
            addCriterion("env <=", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvLike(String value) {
            addCriterion("env like", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotLike(String value) {
            addCriterion("env not like", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvIn(List<String> values) {
            addCriterion("env in", values, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotIn(List<String> values) {
            addCriterion("env not in", values, "env");
            return (Criteria) this;
        }

        public Criteria andEnvBetween(String value1, String value2) {
            addCriterion("env between", value1, value2, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotBetween(String value1, String value2) {
            addCriterion("env not between", value1, value2, "env");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNull() {
            addCriterion("create_by_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNotNull() {
            addCriterion("create_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdEqualTo(Long value) {
            addCriterion("create_by_id =", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotEqualTo(Long value) {
            addCriterion("create_by_id <>", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThan(Long value) {
            addCriterion("create_by_id >", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by_id >=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThan(Long value) {
            addCriterion("create_by_id <", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThanOrEqualTo(Long value) {
            addCriterion("create_by_id <=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIn(List<Long> values) {
            addCriterion("create_by_id in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotIn(List<Long> values) {
            addCriterion("create_by_id not in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdBetween(Long value1, Long value2) {
            addCriterion("create_by_id between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotBetween(Long value1, Long value2) {
            addCriterion("create_by_id not between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNull() {
            addCriterion("update_by_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIsNotNull() {
            addCriterion("update_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdEqualTo(Long value) {
            addCriterion("update_by_id =", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotEqualTo(Long value) {
            addCriterion("update_by_id <>", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThan(Long value) {
            addCriterion("update_by_id >", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by_id >=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThan(Long value) {
            addCriterion("update_by_id <", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdLessThanOrEqualTo(Long value) {
            addCriterion("update_by_id <=", value, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdIn(List<Long> values) {
            addCriterion("update_by_id in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotIn(List<Long> values) {
            addCriterion("update_by_id not in", values, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdBetween(Long value1, Long value2) {
            addCriterion("update_by_id between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andUpdateByIdNotBetween(Long value1, Long value2) {
            addCriterion("update_by_id not between", value1, value2, "updateById");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNull() {
            addCriterion("rx_otc is null");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNotNull() {
            addCriterion("rx_otc is not null");
            return (Criteria) this;
        }

        public Criteria andRxOtcEqualTo(String value) {
            addCriterion("rx_otc =", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotEqualTo(String value) {
            addCriterion("rx_otc <>", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThan(String value) {
            addCriterion("rx_otc >", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThanOrEqualTo(String value) {
            addCriterion("rx_otc >=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThan(String value) {
            addCriterion("rx_otc <", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThanOrEqualTo(String value) {
            addCriterion("rx_otc <=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLike(String value) {
            addCriterion("rx_otc like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotLike(String value) {
            addCriterion("rx_otc not like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcIn(List<String> values) {
            addCriterion("rx_otc in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotIn(List<String> values) {
            addCriterion("rx_otc not in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcBetween(String value1, String value2) {
            addCriterion("rx_otc between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotBetween(String value1, String value2) {
            addCriterion("rx_otc not between", value1, value2, "rxOtc");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}