package com.cowell.scib.entityTidb;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.converters.string.StringNumberConverter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import java.io.Serializable;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TrackRetultCompositionReview implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 任务ID
     */
    @ExcelIgnore
    private Long taskId;

    /**
     * 平台
     */
    @ExcelProperty({"平台", "平台"})
    private String zoneNew;

    /**
     * 企业
     */
    @ExcelProperty({"企业", "企业"})
    private String chainName;

    /**
     * 城市
     */
    @ExcelProperty({"城市", "城市"})
    private String city;

    /**
     * 店型
     */
    @ExcelProperty({"店型", "店型"})
    private String storeGroup;

    /**
     * 大类
     */
    @ExcelProperty({"大类", "大类"})
    private String classoneName;

    /**
     * 中类
     */
    @ExcelProperty({"中类", "中类"})
    private String classtwoName;

    /**
     * 小类
     */
    @ExcelProperty({"小类", "小类"})
    private String classthreeName;

    /**
     * 子类
     */
    @ExcelProperty({"子类", "子类"})
    private String classfourName;

    /**
     * 成分/子类名称
     */
    @ExcelProperty({"成分/子类名称", "成分/子类名称"})
    private String component;

    /**
     * 企业成分/子类正常在营SKU（包含必备+选配）
     */
    @ExcelProperty(value = {"业成分/子类正常在营SKU（包含必备+选配）", "业成分/子类正常在营SKU（包含必备+选配）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt;

    /**
     * 6项必备 SKU个数（本次6级必备的SKU个数）
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "SKU个数（本次6级必备的SKU个数）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal2;

    /**
     * 6项必备 SKU在营的必备个数（当前一店一目中，必备的SKU个数）
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "SKU在营的必备个数（当前一店一目中，必备的SKU个数）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt2;

    /**
     * 6项必备 V3新增SKU个数（K-L列）
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "V3新增SKU个数（K-L列）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew2;

    /**
     * 6项必备 销售数量
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum302;

    /**
     * 6项必备 销售额
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum302;

    /**
     * 6项必备 成分/子类销售占比（全量，含淘汰等）
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "成分/子类销售占比（全量，含淘汰等）"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate302;

    /**
     * 6项必备 毛利额
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum302;

    /**
     * 6项必备 毛利率
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate302;

    /**
     * 6项必备 新增库存金额
     */
    @ExcelProperty(value = {"合计（后面6项必备）", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew2;

    /**
     * 集+平+企业必备合计 SKU个数
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal3;

    /**
     * 集+平+企业必备合计 SKU在营的必备个数
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt3;

    /**
     * 集+平+企业必备合计 V3新增SKU个数
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew3;

    /**
     * 集+平+企业必备合计 销售数量
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum303;

    /**
     * 集+平+企业必备合计 销售额
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum303;

    /**
     * 集+平+企业必备合计 成分/子类销售占比
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate303;

    /**
     * 集+平+企业必备合计 毛利额
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum303;

    /**
     * 集+平+企业必备合计 毛利率
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate303;

    /**
     * 集+平+企业必备合计 新增库存金额
     */
    @ExcelProperty(value = {"集+平+企业必备合计", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew3;

    /**
     * 集团必备 SKU个数
     */
    @ExcelProperty(value = {"集团必备", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal4;

    /**
     * 集团必备 SKU在营的必备个数
     */
    @ExcelProperty(value = {"集团必备", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt4;

    /**
     * 集团必备 V3新增SKU个数
     */
    @ExcelProperty(value = {"集团必备", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew4;

    /**
     * 集团必备 销售数量
     */
    @ExcelProperty(value = {"集团必备", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum304;

    /**
     * 集团必备 销售额
     */
    @ExcelProperty(value = {"集团必备", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum304;

    /**
     * 集团必备 成分/子类销售占比
     */
    @ExcelProperty(value = {"集团必备", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate304;

    /**
     * 集团必备 毛利额
     */
    @ExcelProperty(value = {"集团必备", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum304;

    /**
     * 集团必备 毛利率
     */
    @ExcelProperty(value = {"集团必备", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate304;

    /**
     * 集团必备 新增库存金额
     */
    @ExcelProperty(value = {"集团必备", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew4;

    /**
     * 平台必备 SKU个数
     */
    @ExcelProperty(value = {"平台必备", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal5;

    /**
     * 平台必备 SKU在营的必备个数
     */
    @ExcelProperty(value = {"平台必备", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt5;

    /**
     * 平台必备 V3新增SKU个数
     */
    @ExcelProperty(value = {"平台必备", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew5;

    /**
     * 平台必备 销售数量
     */
    @ExcelProperty(value = {"平台必备", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum305;

    /**
     * 平台必备 销售额
     */
    @ExcelProperty(value = {"平台必备", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum305;

    /**
     * 平台必备 成分/子类销售占比
     */
    @ExcelProperty(value = {"平台必备", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate305;

    /**
     * 平台必备 毛利额
     */
    @ExcelProperty(value = {"平台必备", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum305;

    /**
     * 平台必备 毛利率
     */
    @ExcelProperty(value = {"平台必备", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate305;

    /**
     * 平台必备 新增库存金额
     */
    @ExcelProperty(value = {"平台必备", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew5;

    /**
     * 企业必备 SKU个数
     */
    @ExcelProperty(value = {"企业必备", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal6;

    /**
     * 企业必备 SKU在营的必备个数
     */
    @ExcelProperty(value = {"企业必备", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt6;

    /**
     * 企业必备 V3新增SKU个数
     */
    @ExcelProperty(value = {"企业必备", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew6;

    /**
     * 企业必备 销售数量
     */
    @ExcelProperty(value = {"企业必备", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum306;

    /**
     * 企业必备 销售额
     */
    @ExcelProperty(value = {"企业必备", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum306;

    /**
     * 企业必备 成分/子类销售占比
     */
    @ExcelProperty(value = {"企业必备", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate306;

    /**
     * 企业必备 毛利额
     */
    @ExcelProperty(value = {"企业必备", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum306;

    /**
     * 企业必备 毛利率
     */
    @ExcelProperty(value = {"企业必备", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate306;

    /**
     * 企业必备 新增库存金额
     */
    @ExcelProperty(value = {"企业必备", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew6;

    /**
     * 店型必备 SKU个数
     */
    @ExcelProperty(value = {"店型必备", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal7;

    /**
     * 店型必备 SKU在营的必备个数
     */
    @ExcelProperty(value = {"店型必备", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt7;

    /**
     * 店型必备 V3新增SKU个数
     */
    @ExcelProperty(value = {"店型必备", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew7;

    /**
     * 店型必备 销售数量
     */
    @ExcelProperty(value = {"店型必备", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum307;

    /**
     * 店型必备 销售额
     */
    @ExcelProperty(value = {"店型必备", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum307;

    /**
     * 店型必备 成分/子类销售占比
     */
    @ExcelProperty(value = {"店型必备", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate307;

    /**
     * 店型必备 毛利额
     */
    @ExcelProperty(value = {"店型必备", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum307;

    /**
     * 店型必备 毛利率
     */
    @ExcelProperty(value = {"店型必备", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate307;

    /**
     * 店型必备 新增库存金额
     */
    @ExcelProperty(value = {"店型必备", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew7;

    /**
     * 店型选配 SKU个数
     */
    @ExcelProperty(value = {"店型选配", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal8;

    /**
     * 店型选配 SKU在营的必备个数
     */
    @ExcelProperty(value = {"店型选配", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt8;

    /**
     * 店型选配 V3新增SKU个数
     */
    @ExcelProperty(value = {"店型选配", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew8;

    /**
     * 店型选配 销售数量
     */
    @ExcelProperty(value = {"店型选配", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum308;

    /**
     * 店型选配 销售额
     */
    @ExcelProperty(value = {"店型选配", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum308;

    /**
     * 店型选配 成分/子类销售占比
     */
    @ExcelProperty(value = {"店型选配", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate308;

    /**
     * 店型选配 毛利额
     */
    @ExcelProperty(value = {"店型选配", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum308;

    /**
     * 店型选配 毛利率
     */
    @ExcelProperty(value = {"店型选配", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate308;

    /**
     * 店型选配 新增库存金额
     */
    @ExcelProperty(value = {"店型选配", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew8;

    /**
     * 单店必备 SKU个数
     */
    @ExcelProperty(value = {"单店必备", "SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntTotal9;

    /**
     * 单店必备 SKU在营的必备个数
     */
    @ExcelProperty(value = {"单店必备", "SKU在营的必备个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCnt9;

    /**
     * 单店必备 V3新增SKU个数
     */
    @ExcelProperty(value = {"单店必备", "V3新增SKU个数"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String skuCntNew9;

    /**
     * 单店必备 销售数量
     */
    @ExcelProperty(value = {"单店必备", "销售数量"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String numCum309;

    /**
     * 单店必备 销售额
     */
    @ExcelProperty(value = {"单店必备", "销售额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtCum309;

    /**
     * 单店必备 成分/子类销售占比
     */
    @ExcelProperty(value = {"单店必备", "成分/子类销售占比"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String amtRate309;

    /**
     * 单店必备 毛利额
     */
    @ExcelProperty(value = {"单店必备", "毛利额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String profitAmtCum309;

    /**
     * 单店必备 毛利率
     */
    @ExcelProperty(value = {"单店必备", "毛利率"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat("#%")
    private String profitRate309;

    /**
     * 单店必备 新增库存金额
     */
    @ExcelProperty(value = {"单店必备", "新增库存金额"}, converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat("0")
    private String amtInvNew9;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date gmtCreate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getZoneNew() {
        return zoneNew;
    }

    public void setZoneNew(String zoneNew) {
        this.zoneNew = zoneNew;
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStoreGroup() {
        return storeGroup;
    }

    public void setStoreGroup(String storeGroup) {
        this.storeGroup = storeGroup;
    }

    public String getClassoneName() {
        return classoneName;
    }

    public void setClassoneName(String classoneName) {
        this.classoneName = classoneName;
    }

    public String getClasstwoName() {
        return classtwoName;
    }

    public void setClasstwoName(String classtwoName) {
        this.classtwoName = classtwoName;
    }

    public String getClassthreeName() {
        return classthreeName;
    }

    public void setClassthreeName(String classthreeName) {
        this.classthreeName = classthreeName;
    }

    public String getClassfourName() {
        return classfourName;
    }

    public void setClassfourName(String classfourName) {
        this.classfourName = classfourName;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getSkuCnt() {
        return skuCnt;
    }

    public void setSkuCnt(String skuCnt) {
        this.skuCnt = skuCnt;
    }

    public String getSkuCntTotal2() {
        return skuCntTotal2;
    }

    public void setSkuCntTotal2(String skuCntTotal2) {
        this.skuCntTotal2 = skuCntTotal2;
    }

    public String getSkuCnt2() {
        return skuCnt2;
    }

    public void setSkuCnt2(String skuCnt2) {
        this.skuCnt2 = skuCnt2;
    }

    public String getSkuCntNew2() {
        return skuCntNew2;
    }

    public void setSkuCntNew2(String skuCntNew2) {
        this.skuCntNew2 = skuCntNew2;
    }

    public String getNumCum302() {
        return numCum302;
    }

    public void setNumCum302(String numCum302) {
        this.numCum302 = numCum302;
    }

    public String getAmtCum302() {
        return amtCum302;
    }

    public void setAmtCum302(String amtCum302) {
        this.amtCum302 = amtCum302;
    }

    public String getAmtRate302() {
        return amtRate302;
    }

    public void setAmtRate302(String amtRate302) {
        this.amtRate302 = amtRate302;
    }

    public String getProfitAmtCum302() {
        return profitAmtCum302;
    }

    public void setProfitAmtCum302(String profitAmtCum302) {
        this.profitAmtCum302 = profitAmtCum302;
    }

    public String getProfitRate302() {
        return profitRate302;
    }

    public void setProfitRate302(String profitRate302) {
        this.profitRate302 = profitRate302;
    }

    public String getAmtInvNew2() {
        return amtInvNew2;
    }

    public void setAmtInvNew2(String amtInvNew2) {
        this.amtInvNew2 = amtInvNew2;
    }

    public String getSkuCntTotal3() {
        return skuCntTotal3;
    }

    public void setSkuCntTotal3(String skuCntTotal3) {
        this.skuCntTotal3 = skuCntTotal3;
    }

    public String getSkuCnt3() {
        return skuCnt3;
    }

    public void setSkuCnt3(String skuCnt3) {
        this.skuCnt3 = skuCnt3;
    }

    public String getSkuCntNew3() {
        return skuCntNew3;
    }

    public void setSkuCntNew3(String skuCntNew3) {
        this.skuCntNew3 = skuCntNew3;
    }

    public String getNumCum303() {
        return numCum303;
    }

    public void setNumCum303(String numCum303) {
        this.numCum303 = numCum303;
    }

    public String getAmtCum303() {
        return amtCum303;
    }

    public void setAmtCum303(String amtCum303) {
        this.amtCum303 = amtCum303;
    }

    public String getAmtRate303() {
        return amtRate303;
    }

    public void setAmtRate303(String amtRate303) {
        this.amtRate303 = amtRate303;
    }

    public String getProfitAmtCum303() {
        return profitAmtCum303;
    }

    public void setProfitAmtCum303(String profitAmtCum303) {
        this.profitAmtCum303 = profitAmtCum303;
    }

    public String getProfitRate303() {
        return profitRate303;
    }

    public void setProfitRate303(String profitRate303) {
        this.profitRate303 = profitRate303;
    }

    public String getAmtInvNew3() {
        return amtInvNew3;
    }

    public void setAmtInvNew3(String amtInvNew3) {
        this.amtInvNew3 = amtInvNew3;
    }

    public String getSkuCntTotal4() {
        return skuCntTotal4;
    }

    public void setSkuCntTotal4(String skuCntTotal4) {
        this.skuCntTotal4 = skuCntTotal4;
    }

    public String getSkuCnt4() {
        return skuCnt4;
    }

    public void setSkuCnt4(String skuCnt4) {
        this.skuCnt4 = skuCnt4;
    }

    public String getSkuCntNew4() {
        return skuCntNew4;
    }

    public void setSkuCntNew4(String skuCntNew4) {
        this.skuCntNew4 = skuCntNew4;
    }

    public String getNumCum304() {
        return numCum304;
    }

    public void setNumCum304(String numCum304) {
        this.numCum304 = numCum304;
    }

    public String getAmtCum304() {
        return amtCum304;
    }

    public void setAmtCum304(String amtCum304) {
        this.amtCum304 = amtCum304;
    }

    public String getAmtRate304() {
        return amtRate304;
    }

    public void setAmtRate304(String amtRate304) {
        this.amtRate304 = amtRate304;
    }

    public String getProfitAmtCum304() {
        return profitAmtCum304;
    }

    public void setProfitAmtCum304(String profitAmtCum304) {
        this.profitAmtCum304 = profitAmtCum304;
    }

    public String getProfitRate304() {
        return profitRate304;
    }

    public void setProfitRate304(String profitRate304) {
        this.profitRate304 = profitRate304;
    }

    public String getAmtInvNew4() {
        return amtInvNew4;
    }

    public void setAmtInvNew4(String amtInvNew4) {
        this.amtInvNew4 = amtInvNew4;
    }

    public String getSkuCntTotal5() {
        return skuCntTotal5;
    }

    public void setSkuCntTotal5(String skuCntTotal5) {
        this.skuCntTotal5 = skuCntTotal5;
    }

    public String getSkuCnt5() {
        return skuCnt5;
    }

    public void setSkuCnt5(String skuCnt5) {
        this.skuCnt5 = skuCnt5;
    }

    public String getSkuCntNew5() {
        return skuCntNew5;
    }

    public void setSkuCntNew5(String skuCntNew5) {
        this.skuCntNew5 = skuCntNew5;
    }

    public String getNumCum305() {
        return numCum305;
    }

    public void setNumCum305(String numCum305) {
        this.numCum305 = numCum305;
    }

    public String getAmtCum305() {
        return amtCum305;
    }

    public void setAmtCum305(String amtCum305) {
        this.amtCum305 = amtCum305;
    }

    public String getAmtRate305() {
        return amtRate305;
    }

    public void setAmtRate305(String amtRate305) {
        this.amtRate305 = amtRate305;
    }

    public String getProfitAmtCum305() {
        return profitAmtCum305;
    }

    public void setProfitAmtCum305(String profitAmtCum305) {
        this.profitAmtCum305 = profitAmtCum305;
    }

    public String getProfitRate305() {
        return profitRate305;
    }

    public void setProfitRate305(String profitRate305) {
        this.profitRate305 = profitRate305;
    }

    public String getAmtInvNew5() {
        return amtInvNew5;
    }

    public void setAmtInvNew5(String amtInvNew5) {
        this.amtInvNew5 = amtInvNew5;
    }

    public String getSkuCntTotal6() {
        return skuCntTotal6;
    }

    public void setSkuCntTotal6(String skuCntTotal6) {
        this.skuCntTotal6 = skuCntTotal6;
    }

    public String getSkuCnt6() {
        return skuCnt6;
    }

    public void setSkuCnt6(String skuCnt6) {
        this.skuCnt6 = skuCnt6;
    }

    public String getSkuCntNew6() {
        return skuCntNew6;
    }

    public void setSkuCntNew6(String skuCntNew6) {
        this.skuCntNew6 = skuCntNew6;
    }

    public String getNumCum306() {
        return numCum306;
    }

    public void setNumCum306(String numCum306) {
        this.numCum306 = numCum306;
    }

    public String getAmtCum306() {
        return amtCum306;
    }

    public void setAmtCum306(String amtCum306) {
        this.amtCum306 = amtCum306;
    }

    public String getAmtRate306() {
        return amtRate306;
    }

    public void setAmtRate306(String amtRate306) {
        this.amtRate306 = amtRate306;
    }

    public String getProfitAmtCum306() {
        return profitAmtCum306;
    }

    public void setProfitAmtCum306(String profitAmtCum306) {
        this.profitAmtCum306 = profitAmtCum306;
    }

    public String getProfitRate306() {
        return profitRate306;
    }

    public void setProfitRate306(String profitRate306) {
        this.profitRate306 = profitRate306;
    }

    public String getAmtInvNew6() {
        return amtInvNew6;
    }

    public void setAmtInvNew6(String amtInvNew6) {
        this.amtInvNew6 = amtInvNew6;
    }

    public String getSkuCntTotal7() {
        return skuCntTotal7;
    }

    public void setSkuCntTotal7(String skuCntTotal7) {
        this.skuCntTotal7 = skuCntTotal7;
    }

    public String getSkuCnt7() {
        return skuCnt7;
    }

    public void setSkuCnt7(String skuCnt7) {
        this.skuCnt7 = skuCnt7;
    }

    public String getSkuCntNew7() {
        return skuCntNew7;
    }

    public void setSkuCntNew7(String skuCntNew7) {
        this.skuCntNew7 = skuCntNew7;
    }

    public String getNumCum307() {
        return numCum307;
    }

    public void setNumCum307(String numCum307) {
        this.numCum307 = numCum307;
    }

    public String getAmtCum307() {
        return amtCum307;
    }

    public void setAmtCum307(String amtCum307) {
        this.amtCum307 = amtCum307;
    }

    public String getAmtRate307() {
        return amtRate307;
    }

    public void setAmtRate307(String amtRate307) {
        this.amtRate307 = amtRate307;
    }

    public String getProfitAmtCum307() {
        return profitAmtCum307;
    }

    public void setProfitAmtCum307(String profitAmtCum307) {
        this.profitAmtCum307 = profitAmtCum307;
    }

    public String getProfitRate307() {
        return profitRate307;
    }

    public void setProfitRate307(String profitRate307) {
        this.profitRate307 = profitRate307;
    }

    public String getAmtInvNew7() {
        return amtInvNew7;
    }

    public void setAmtInvNew7(String amtInvNew7) {
        this.amtInvNew7 = amtInvNew7;
    }

    public String getSkuCntTotal8() {
        return skuCntTotal8;
    }

    public void setSkuCntTotal8(String skuCntTotal8) {
        this.skuCntTotal8 = skuCntTotal8;
    }

    public String getSkuCnt8() {
        return skuCnt8;
    }

    public void setSkuCnt8(String skuCnt8) {
        this.skuCnt8 = skuCnt8;
    }

    public String getSkuCntNew8() {
        return skuCntNew8;
    }

    public void setSkuCntNew8(String skuCntNew8) {
        this.skuCntNew8 = skuCntNew8;
    }

    public String getNumCum308() {
        return numCum308;
    }

    public void setNumCum308(String numCum308) {
        this.numCum308 = numCum308;
    }

    public String getAmtCum308() {
        return amtCum308;
    }

    public void setAmtCum308(String amtCum308) {
        this.amtCum308 = amtCum308;
    }

    public String getAmtRate308() {
        return amtRate308;
    }

    public void setAmtRate308(String amtRate308) {
        this.amtRate308 = amtRate308;
    }

    public String getProfitAmtCum308() {
        return profitAmtCum308;
    }

    public void setProfitAmtCum308(String profitAmtCum308) {
        this.profitAmtCum308 = profitAmtCum308;
    }

    public String getProfitRate308() {
        return profitRate308;
    }

    public void setProfitRate308(String profitRate308) {
        this.profitRate308 = profitRate308;
    }

    public String getAmtInvNew8() {
        return amtInvNew8;
    }

    public void setAmtInvNew8(String amtInvNew8) {
        this.amtInvNew8 = amtInvNew8;
    }

    public String getSkuCntTotal9() {
        return skuCntTotal9;
    }

    public void setSkuCntTotal9(String skuCntTotal9) {
        this.skuCntTotal9 = skuCntTotal9;
    }

    public String getSkuCnt9() {
        return skuCnt9;
    }

    public void setSkuCnt9(String skuCnt9) {
        this.skuCnt9 = skuCnt9;
    }

    public String getSkuCntNew9() {
        return skuCntNew9;
    }

    public void setSkuCntNew9(String skuCntNew9) {
        this.skuCntNew9 = skuCntNew9;
    }

    public String getNumCum309() {
        return numCum309;
    }

    public void setNumCum309(String numCum309) {
        this.numCum309 = numCum309;
    }

    public String getAmtCum309() {
        return amtCum309;
    }

    public void setAmtCum309(String amtCum309) {
        this.amtCum309 = amtCum309;
    }

    public String getAmtRate309() {
        return amtRate309;
    }

    public void setAmtRate309(String amtRate309) {
        this.amtRate309 = amtRate309;
    }

    public String getProfitAmtCum309() {
        return profitAmtCum309;
    }

    public void setProfitAmtCum309(String profitAmtCum309) {
        this.profitAmtCum309 = profitAmtCum309;
    }

    public String getProfitRate309() {
        return profitRate309;
    }

    public void setProfitRate309(String profitRate309) {
        this.profitRate309 = profitRate309;
    }

    public String getAmtInvNew9() {
        return amtInvNew9;
    }

    public void setAmtInvNew9(String amtInvNew9) {
        this.amtInvNew9 = amtInvNew9;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TrackRetultCompositionReview other = (TrackRetultCompositionReview) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
                && (this.getZoneNew() == null ? other.getZoneNew() == null : this.getZoneNew().equals(other.getZoneNew()))
                && (this.getChainName() == null ? other.getChainName() == null : this.getChainName().equals(other.getChainName()))
                && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
                && (this.getStoreGroup() == null ? other.getStoreGroup() == null : this.getStoreGroup().equals(other.getStoreGroup()))
                && (this.getClassoneName() == null ? other.getClassoneName() == null : this.getClassoneName().equals(other.getClassoneName()))
                && (this.getClasstwoName() == null ? other.getClasstwoName() == null : this.getClasstwoName().equals(other.getClasstwoName()))
                && (this.getClassthreeName() == null ? other.getClassthreeName() == null : this.getClassthreeName().equals(other.getClassthreeName()))
                && (this.getClassfourName() == null ? other.getClassfourName() == null : this.getClassfourName().equals(other.getClassfourName()))
                && (this.getComponent() == null ? other.getComponent() == null : this.getComponent().equals(other.getComponent()))
                && (this.getSkuCnt() == null ? other.getSkuCnt() == null : this.getSkuCnt().equals(other.getSkuCnt()))
                && (this.getSkuCntTotal2() == null ? other.getSkuCntTotal2() == null : this.getSkuCntTotal2().equals(other.getSkuCntTotal2()))
                && (this.getSkuCnt2() == null ? other.getSkuCnt2() == null : this.getSkuCnt2().equals(other.getSkuCnt2()))
                && (this.getSkuCntNew2() == null ? other.getSkuCntNew2() == null : this.getSkuCntNew2().equals(other.getSkuCntNew2()))
                && (this.getNumCum302() == null ? other.getNumCum302() == null : this.getNumCum302().equals(other.getNumCum302()))
                && (this.getAmtCum302() == null ? other.getAmtCum302() == null : this.getAmtCum302().equals(other.getAmtCum302()))
                && (this.getAmtRate302() == null ? other.getAmtRate302() == null : this.getAmtRate302().equals(other.getAmtRate302()))
                && (this.getProfitAmtCum302() == null ? other.getProfitAmtCum302() == null : this.getProfitAmtCum302().equals(other.getProfitAmtCum302()))
                && (this.getProfitRate302() == null ? other.getProfitRate302() == null : this.getProfitRate302().equals(other.getProfitRate302()))
                && (this.getAmtInvNew2() == null ? other.getAmtInvNew2() == null : this.getAmtInvNew2().equals(other.getAmtInvNew2()))
                && (this.getSkuCntTotal3() == null ? other.getSkuCntTotal3() == null : this.getSkuCntTotal3().equals(other.getSkuCntTotal3()))
                && (this.getSkuCnt3() == null ? other.getSkuCnt3() == null : this.getSkuCnt3().equals(other.getSkuCnt3()))
                && (this.getSkuCntNew3() == null ? other.getSkuCntNew3() == null : this.getSkuCntNew3().equals(other.getSkuCntNew3()))
                && (this.getNumCum303() == null ? other.getNumCum303() == null : this.getNumCum303().equals(other.getNumCum303()))
                && (this.getAmtCum303() == null ? other.getAmtCum303() == null : this.getAmtCum303().equals(other.getAmtCum303()))
                && (this.getAmtRate303() == null ? other.getAmtRate303() == null : this.getAmtRate303().equals(other.getAmtRate303()))
                && (this.getProfitAmtCum303() == null ? other.getProfitAmtCum303() == null : this.getProfitAmtCum303().equals(other.getProfitAmtCum303()))
                && (this.getProfitRate303() == null ? other.getProfitRate303() == null : this.getProfitRate303().equals(other.getProfitRate303()))
                && (this.getAmtInvNew3() == null ? other.getAmtInvNew3() == null : this.getAmtInvNew3().equals(other.getAmtInvNew3()))
                && (this.getSkuCntTotal4() == null ? other.getSkuCntTotal4() == null : this.getSkuCntTotal4().equals(other.getSkuCntTotal4()))
                && (this.getSkuCnt4() == null ? other.getSkuCnt4() == null : this.getSkuCnt4().equals(other.getSkuCnt4()))
                && (this.getSkuCntNew4() == null ? other.getSkuCntNew4() == null : this.getSkuCntNew4().equals(other.getSkuCntNew4()))
                && (this.getNumCum304() == null ? other.getNumCum304() == null : this.getNumCum304().equals(other.getNumCum304()))
                && (this.getAmtCum304() == null ? other.getAmtCum304() == null : this.getAmtCum304().equals(other.getAmtCum304()))
                && (this.getAmtRate304() == null ? other.getAmtRate304() == null : this.getAmtRate304().equals(other.getAmtRate304()))
                && (this.getProfitAmtCum304() == null ? other.getProfitAmtCum304() == null : this.getProfitAmtCum304().equals(other.getProfitAmtCum304()))
                && (this.getProfitRate304() == null ? other.getProfitRate304() == null : this.getProfitRate304().equals(other.getProfitRate304()))
                && (this.getAmtInvNew4() == null ? other.getAmtInvNew4() == null : this.getAmtInvNew4().equals(other.getAmtInvNew4()))
                && (this.getSkuCntTotal5() == null ? other.getSkuCntTotal5() == null : this.getSkuCntTotal5().equals(other.getSkuCntTotal5()))
                && (this.getSkuCnt5() == null ? other.getSkuCnt5() == null : this.getSkuCnt5().equals(other.getSkuCnt5()))
                && (this.getSkuCntNew5() == null ? other.getSkuCntNew5() == null : this.getSkuCntNew5().equals(other.getSkuCntNew5()))
                && (this.getNumCum305() == null ? other.getNumCum305() == null : this.getNumCum305().equals(other.getNumCum305()))
                && (this.getAmtCum305() == null ? other.getAmtCum305() == null : this.getAmtCum305().equals(other.getAmtCum305()))
                && (this.getAmtRate305() == null ? other.getAmtRate305() == null : this.getAmtRate305().equals(other.getAmtRate305()))
                && (this.getProfitAmtCum305() == null ? other.getProfitAmtCum305() == null : this.getProfitAmtCum305().equals(other.getProfitAmtCum305()))
                && (this.getProfitRate305() == null ? other.getProfitRate305() == null : this.getProfitRate305().equals(other.getProfitRate305()))
                && (this.getAmtInvNew5() == null ? other.getAmtInvNew5() == null : this.getAmtInvNew5().equals(other.getAmtInvNew5()))
                && (this.getSkuCntTotal6() == null ? other.getSkuCntTotal6() == null : this.getSkuCntTotal6().equals(other.getSkuCntTotal6()))
                && (this.getSkuCnt6() == null ? other.getSkuCnt6() == null : this.getSkuCnt6().equals(other.getSkuCnt6()))
                && (this.getSkuCntNew6() == null ? other.getSkuCntNew6() == null : this.getSkuCntNew6().equals(other.getSkuCntNew6()))
                && (this.getNumCum306() == null ? other.getNumCum306() == null : this.getNumCum306().equals(other.getNumCum306()))
                && (this.getAmtCum306() == null ? other.getAmtCum306() == null : this.getAmtCum306().equals(other.getAmtCum306()))
                && (this.getAmtRate306() == null ? other.getAmtRate306() == null : this.getAmtRate306().equals(other.getAmtRate306()))
                && (this.getProfitAmtCum306() == null ? other.getProfitAmtCum306() == null : this.getProfitAmtCum306().equals(other.getProfitAmtCum306()))
                && (this.getProfitRate306() == null ? other.getProfitRate306() == null : this.getProfitRate306().equals(other.getProfitRate306()))
                && (this.getAmtInvNew6() == null ? other.getAmtInvNew6() == null : this.getAmtInvNew6().equals(other.getAmtInvNew6()))
                && (this.getSkuCntTotal7() == null ? other.getSkuCntTotal7() == null : this.getSkuCntTotal7().equals(other.getSkuCntTotal7()))
                && (this.getSkuCnt7() == null ? other.getSkuCnt7() == null : this.getSkuCnt7().equals(other.getSkuCnt7()))
                && (this.getSkuCntNew7() == null ? other.getSkuCntNew7() == null : this.getSkuCntNew7().equals(other.getSkuCntNew7()))
                && (this.getNumCum307() == null ? other.getNumCum307() == null : this.getNumCum307().equals(other.getNumCum307()))
                && (this.getAmtCum307() == null ? other.getAmtCum307() == null : this.getAmtCum307().equals(other.getAmtCum307()))
                && (this.getAmtRate307() == null ? other.getAmtRate307() == null : this.getAmtRate307().equals(other.getAmtRate307()))
                && (this.getProfitAmtCum307() == null ? other.getProfitAmtCum307() == null : this.getProfitAmtCum307().equals(other.getProfitAmtCum307()))
                && (this.getProfitRate307() == null ? other.getProfitRate307() == null : this.getProfitRate307().equals(other.getProfitRate307()))
                && (this.getAmtInvNew7() == null ? other.getAmtInvNew7() == null : this.getAmtInvNew7().equals(other.getAmtInvNew7()))
                && (this.getSkuCntTotal8() == null ? other.getSkuCntTotal8() == null : this.getSkuCntTotal8().equals(other.getSkuCntTotal8()))
                && (this.getSkuCnt8() == null ? other.getSkuCnt8() == null : this.getSkuCnt8().equals(other.getSkuCnt8()))
                && (this.getSkuCntNew8() == null ? other.getSkuCntNew8() == null : this.getSkuCntNew8().equals(other.getSkuCntNew8()))
                && (this.getNumCum308() == null ? other.getNumCum308() == null : this.getNumCum308().equals(other.getNumCum308()))
                && (this.getAmtCum308() == null ? other.getAmtCum308() == null : this.getAmtCum308().equals(other.getAmtCum308()))
                && (this.getAmtRate308() == null ? other.getAmtRate308() == null : this.getAmtRate308().equals(other.getAmtRate308()))
                && (this.getProfitAmtCum308() == null ? other.getProfitAmtCum308() == null : this.getProfitAmtCum308().equals(other.getProfitAmtCum308()))
                && (this.getProfitRate308() == null ? other.getProfitRate308() == null : this.getProfitRate308().equals(other.getProfitRate308()))
                && (this.getAmtInvNew8() == null ? other.getAmtInvNew8() == null : this.getAmtInvNew8().equals(other.getAmtInvNew8()))
                && (this.getSkuCntTotal9() == null ? other.getSkuCntTotal9() == null : this.getSkuCntTotal9().equals(other.getSkuCntTotal9()))
                && (this.getSkuCnt9() == null ? other.getSkuCnt9() == null : this.getSkuCnt9().equals(other.getSkuCnt9()))
                && (this.getSkuCntNew9() == null ? other.getSkuCntNew9() == null : this.getSkuCntNew9().equals(other.getSkuCntNew9()))
                && (this.getNumCum309() == null ? other.getNumCum309() == null : this.getNumCum309().equals(other.getNumCum309()))
                && (this.getAmtCum309() == null ? other.getAmtCum309() == null : this.getAmtCum309().equals(other.getAmtCum309()))
                && (this.getAmtRate309() == null ? other.getAmtRate309() == null : this.getAmtRate309().equals(other.getAmtRate309()))
                && (this.getProfitAmtCum309() == null ? other.getProfitAmtCum309() == null : this.getProfitAmtCum309().equals(other.getProfitAmtCum309()))
                && (this.getProfitRate309() == null ? other.getProfitRate309() == null : this.getProfitRate309().equals(other.getProfitRate309()))
                && (this.getAmtInvNew9() == null ? other.getAmtInvNew9() == null : this.getAmtInvNew9().equals(other.getAmtInvNew9()))
                && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getZoneNew() == null) ? 0 : getZoneNew().hashCode());
        result = prime * result + ((getChainName() == null) ? 0 : getChainName().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getStoreGroup() == null) ? 0 : getStoreGroup().hashCode());
        result = prime * result + ((getClassoneName() == null) ? 0 : getClassoneName().hashCode());
        result = prime * result + ((getClasstwoName() == null) ? 0 : getClasstwoName().hashCode());
        result = prime * result + ((getClassthreeName() == null) ? 0 : getClassthreeName().hashCode());
        result = prime * result + ((getClassfourName() == null) ? 0 : getClassfourName().hashCode());
        result = prime * result + ((getComponent() == null) ? 0 : getComponent().hashCode());
        result = prime * result + ((getSkuCnt() == null) ? 0 : getSkuCnt().hashCode());
        result = prime * result + ((getSkuCntTotal2() == null) ? 0 : getSkuCntTotal2().hashCode());
        result = prime * result + ((getSkuCnt2() == null) ? 0 : getSkuCnt2().hashCode());
        result = prime * result + ((getSkuCntNew2() == null) ? 0 : getSkuCntNew2().hashCode());
        result = prime * result + ((getNumCum302() == null) ? 0 : getNumCum302().hashCode());
        result = prime * result + ((getAmtCum302() == null) ? 0 : getAmtCum302().hashCode());
        result = prime * result + ((getAmtRate302() == null) ? 0 : getAmtRate302().hashCode());
        result = prime * result + ((getProfitAmtCum302() == null) ? 0 : getProfitAmtCum302().hashCode());
        result = prime * result + ((getProfitRate302() == null) ? 0 : getProfitRate302().hashCode());
        result = prime * result + ((getAmtInvNew2() == null) ? 0 : getAmtInvNew2().hashCode());
        result = prime * result + ((getSkuCntTotal3() == null) ? 0 : getSkuCntTotal3().hashCode());
        result = prime * result + ((getSkuCnt3() == null) ? 0 : getSkuCnt3().hashCode());
        result = prime * result + ((getSkuCntNew3() == null) ? 0 : getSkuCntNew3().hashCode());
        result = prime * result + ((getNumCum303() == null) ? 0 : getNumCum303().hashCode());
        result = prime * result + ((getAmtCum303() == null) ? 0 : getAmtCum303().hashCode());
        result = prime * result + ((getAmtRate303() == null) ? 0 : getAmtRate303().hashCode());
        result = prime * result + ((getProfitAmtCum303() == null) ? 0 : getProfitAmtCum303().hashCode());
        result = prime * result + ((getProfitRate303() == null) ? 0 : getProfitRate303().hashCode());
        result = prime * result + ((getAmtInvNew3() == null) ? 0 : getAmtInvNew3().hashCode());
        result = prime * result + ((getSkuCntTotal4() == null) ? 0 : getSkuCntTotal4().hashCode());
        result = prime * result + ((getSkuCnt4() == null) ? 0 : getSkuCnt4().hashCode());
        result = prime * result + ((getSkuCntNew4() == null) ? 0 : getSkuCntNew4().hashCode());
        result = prime * result + ((getNumCum304() == null) ? 0 : getNumCum304().hashCode());
        result = prime * result + ((getAmtCum304() == null) ? 0 : getAmtCum304().hashCode());
        result = prime * result + ((getAmtRate304() == null) ? 0 : getAmtRate304().hashCode());
        result = prime * result + ((getProfitAmtCum304() == null) ? 0 : getProfitAmtCum304().hashCode());
        result = prime * result + ((getProfitRate304() == null) ? 0 : getProfitRate304().hashCode());
        result = prime * result + ((getAmtInvNew4() == null) ? 0 : getAmtInvNew4().hashCode());
        result = prime * result + ((getSkuCntTotal5() == null) ? 0 : getSkuCntTotal5().hashCode());
        result = prime * result + ((getSkuCnt5() == null) ? 0 : getSkuCnt5().hashCode());
        result = prime * result + ((getSkuCntNew5() == null) ? 0 : getSkuCntNew5().hashCode());
        result = prime * result + ((getNumCum305() == null) ? 0 : getNumCum305().hashCode());
        result = prime * result + ((getAmtCum305() == null) ? 0 : getAmtCum305().hashCode());
        result = prime * result + ((getAmtRate305() == null) ? 0 : getAmtRate305().hashCode());
        result = prime * result + ((getProfitAmtCum305() == null) ? 0 : getProfitAmtCum305().hashCode());
        result = prime * result + ((getProfitRate305() == null) ? 0 : getProfitRate305().hashCode());
        result = prime * result + ((getAmtInvNew5() == null) ? 0 : getAmtInvNew5().hashCode());
        result = prime * result + ((getSkuCntTotal6() == null) ? 0 : getSkuCntTotal6().hashCode());
        result = prime * result + ((getSkuCnt6() == null) ? 0 : getSkuCnt6().hashCode());
        result = prime * result + ((getSkuCntNew6() == null) ? 0 : getSkuCntNew6().hashCode());
        result = prime * result + ((getNumCum306() == null) ? 0 : getNumCum306().hashCode());
        result = prime * result + ((getAmtCum306() == null) ? 0 : getAmtCum306().hashCode());
        result = prime * result + ((getAmtRate306() == null) ? 0 : getAmtRate306().hashCode());
        result = prime * result + ((getProfitAmtCum306() == null) ? 0 : getProfitAmtCum306().hashCode());
        result = prime * result + ((getProfitRate306() == null) ? 0 : getProfitRate306().hashCode());
        result = prime * result + ((getAmtInvNew6() == null) ? 0 : getAmtInvNew6().hashCode());
        result = prime * result + ((getSkuCntTotal7() == null) ? 0 : getSkuCntTotal7().hashCode());
        result = prime * result + ((getSkuCnt7() == null) ? 0 : getSkuCnt7().hashCode());
        result = prime * result + ((getSkuCntNew7() == null) ? 0 : getSkuCntNew7().hashCode());
        result = prime * result + ((getNumCum307() == null) ? 0 : getNumCum307().hashCode());
        result = prime * result + ((getAmtCum307() == null) ? 0 : getAmtCum307().hashCode());
        result = prime * result + ((getAmtRate307() == null) ? 0 : getAmtRate307().hashCode());
        result = prime * result + ((getProfitAmtCum307() == null) ? 0 : getProfitAmtCum307().hashCode());
        result = prime * result + ((getProfitRate307() == null) ? 0 : getProfitRate307().hashCode());
        result = prime * result + ((getAmtInvNew7() == null) ? 0 : getAmtInvNew7().hashCode());
        result = prime * result + ((getSkuCntTotal8() == null) ? 0 : getSkuCntTotal8().hashCode());
        result = prime * result + ((getSkuCnt8() == null) ? 0 : getSkuCnt8().hashCode());
        result = prime * result + ((getSkuCntNew8() == null) ? 0 : getSkuCntNew8().hashCode());
        result = prime * result + ((getNumCum308() == null) ? 0 : getNumCum308().hashCode());
        result = prime * result + ((getAmtCum308() == null) ? 0 : getAmtCum308().hashCode());
        result = prime * result + ((getAmtRate308() == null) ? 0 : getAmtRate308().hashCode());
        result = prime * result + ((getProfitAmtCum308() == null) ? 0 : getProfitAmtCum308().hashCode());
        result = prime * result + ((getProfitRate308() == null) ? 0 : getProfitRate308().hashCode());
        result = prime * result + ((getAmtInvNew8() == null) ? 0 : getAmtInvNew8().hashCode());
        result = prime * result + ((getSkuCntTotal9() == null) ? 0 : getSkuCntTotal9().hashCode());
        result = prime * result + ((getSkuCnt9() == null) ? 0 : getSkuCnt9().hashCode());
        result = prime * result + ((getSkuCntNew9() == null) ? 0 : getSkuCntNew9().hashCode());
        result = prime * result + ((getNumCum309() == null) ? 0 : getNumCum309().hashCode());
        result = prime * result + ((getAmtCum309() == null) ? 0 : getAmtCum309().hashCode());
        result = prime * result + ((getAmtRate309() == null) ? 0 : getAmtRate309().hashCode());
        result = prime * result + ((getProfitAmtCum309() == null) ? 0 : getProfitAmtCum309().hashCode());
        result = prime * result + ((getProfitRate309() == null) ? 0 : getProfitRate309().hashCode());
        result = prime * result + ((getAmtInvNew9() == null) ? 0 : getAmtInvNew9().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", zoneNew=").append(zoneNew);
        sb.append(", chainName=").append(chainName);
        sb.append(", city=").append(city);
        sb.append(", storeGroup=").append(storeGroup);
        sb.append(", classoneName=").append(classoneName);
        sb.append(", classtwoName=").append(classtwoName);
        sb.append(", classthreeName=").append(classthreeName);
        sb.append(", classfourName=").append(classfourName);
        sb.append(", component=").append(component);
        sb.append(", skuCnt=").append(skuCnt);
        sb.append(", skuCntTotal2=").append(skuCntTotal2);
        sb.append(", skuCnt2=").append(skuCnt2);
        sb.append(", skuCntNew2=").append(skuCntNew2);
        sb.append(", numCum302=").append(numCum302);
        sb.append(", amtCum302=").append(amtCum302);
        sb.append(", amtRate302=").append(amtRate302);
        sb.append(", profitAmtCum302=").append(profitAmtCum302);
        sb.append(", profitRate302=").append(profitRate302);
        sb.append(", amtInvNew2=").append(amtInvNew2);
        sb.append(", skuCntTotal3=").append(skuCntTotal3);
        sb.append(", skuCnt3=").append(skuCnt3);
        sb.append(", skuCntNew3=").append(skuCntNew3);
        sb.append(", numCum303=").append(numCum303);
        sb.append(", amtCum303=").append(amtCum303);
        sb.append(", amtRate303=").append(amtRate303);
        sb.append(", profitAmtCum303=").append(profitAmtCum303);
        sb.append(", profitRate303=").append(profitRate303);
        sb.append(", amtInvNew3=").append(amtInvNew3);
        sb.append(", skuCntTotal4=").append(skuCntTotal4);
        sb.append(", skuCnt4=").append(skuCnt4);
        sb.append(", skuCntNew4=").append(skuCntNew4);
        sb.append(", numCum304=").append(numCum304);
        sb.append(", amtCum304=").append(amtCum304);
        sb.append(", amtRate304=").append(amtRate304);
        sb.append(", profitAmtCum304=").append(profitAmtCum304);
        sb.append(", profitRate304=").append(profitRate304);
        sb.append(", amtInvNew4=").append(amtInvNew4);
        sb.append(", skuCntTotal5=").append(skuCntTotal5);
        sb.append(", skuCnt5=").append(skuCnt5);
        sb.append(", skuCntNew5=").append(skuCntNew5);
        sb.append(", numCum305=").append(numCum305);
        sb.append(", amtCum305=").append(amtCum305);
        sb.append(", amtRate305=").append(amtRate305);
        sb.append(", profitAmtCum305=").append(profitAmtCum305);
        sb.append(", profitRate305=").append(profitRate305);
        sb.append(", amtInvNew5=").append(amtInvNew5);
        sb.append(", skuCntTotal6=").append(skuCntTotal6);
        sb.append(", skuCnt6=").append(skuCnt6);
        sb.append(", skuCntNew6=").append(skuCntNew6);
        sb.append(", numCum306=").append(numCum306);
        sb.append(", amtCum306=").append(amtCum306);
        sb.append(", amtRate306=").append(amtRate306);
        sb.append(", profitAmtCum306=").append(profitAmtCum306);
        sb.append(", profitRate306=").append(profitRate306);
        sb.append(", amtInvNew6=").append(amtInvNew6);
        sb.append(", skuCntTotal7=").append(skuCntTotal7);
        sb.append(", skuCnt7=").append(skuCnt7);
        sb.append(", skuCntNew7=").append(skuCntNew7);
        sb.append(", numCum307=").append(numCum307);
        sb.append(", amtCum307=").append(amtCum307);
        sb.append(", amtRate307=").append(amtRate307);
        sb.append(", profitAmtCum307=").append(profitAmtCum307);
        sb.append(", profitRate307=").append(profitRate307);
        sb.append(", amtInvNew7=").append(amtInvNew7);
        sb.append(", skuCntTotal8=").append(skuCntTotal8);
        sb.append(", skuCnt8=").append(skuCnt8);
        sb.append(", skuCntNew8=").append(skuCntNew8);
        sb.append(", numCum308=").append(numCum308);
        sb.append(", amtCum308=").append(amtCum308);
        sb.append(", amtRate308=").append(amtRate308);
        sb.append(", profitAmtCum308=").append(profitAmtCum308);
        sb.append(", profitRate308=").append(profitRate308);
        sb.append(", amtInvNew8=").append(amtInvNew8);
        sb.append(", skuCntTotal9=").append(skuCntTotal9);
        sb.append(", skuCnt9=").append(skuCnt9);
        sb.append(", skuCntNew9=").append(skuCntNew9);
        sb.append(", numCum309=").append(numCum309);
        sb.append(", amtCum309=").append(amtCum309);
        sb.append(", amtRate309=").append(amtRate309);
        sb.append(", profitAmtCum309=").append(profitAmtCum309);
        sb.append(", profitRate309=").append(profitRate309);
        sb.append(", amtInvNew9=").append(amtInvNew9);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}