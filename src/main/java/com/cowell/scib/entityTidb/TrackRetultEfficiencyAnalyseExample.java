package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrackRetultEfficiencyAnalyseExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TrackRetultEfficiencyAnalyseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNull() {
            addCriterion("zone_new is null");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNotNull() {
            addCriterion("zone_new is not null");
            return (Criteria) this;
        }

        public Criteria andZoneNewEqualTo(String value) {
            addCriterion("zone_new =", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotEqualTo(String value) {
            addCriterion("zone_new <>", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThan(String value) {
            addCriterion("zone_new >", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThanOrEqualTo(String value) {
            addCriterion("zone_new >=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThan(String value) {
            addCriterion("zone_new <", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThanOrEqualTo(String value) {
            addCriterion("zone_new <=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLike(String value) {
            addCriterion("zone_new like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotLike(String value) {
            addCriterion("zone_new not like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewIn(List<String> values) {
            addCriterion("zone_new in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotIn(List<String> values) {
            addCriterion("zone_new not in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewBetween(String value1, String value2) {
            addCriterion("zone_new between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotBetween(String value1, String value2) {
            addCriterion("zone_new not between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andChainNameIsNull() {
            addCriterion("chain_name is null");
            return (Criteria) this;
        }

        public Criteria andChainNameIsNotNull() {
            addCriterion("chain_name is not null");
            return (Criteria) this;
        }

        public Criteria andChainNameEqualTo(String value) {
            addCriterion("chain_name =", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotEqualTo(String value) {
            addCriterion("chain_name <>", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameGreaterThan(String value) {
            addCriterion("chain_name >", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameGreaterThanOrEqualTo(String value) {
            addCriterion("chain_name >=", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLessThan(String value) {
            addCriterion("chain_name <", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLessThanOrEqualTo(String value) {
            addCriterion("chain_name <=", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLike(String value) {
            addCriterion("chain_name like", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotLike(String value) {
            addCriterion("chain_name not like", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameIn(List<String> values) {
            addCriterion("chain_name in", values, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotIn(List<String> values) {
            addCriterion("chain_name not in", values, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameBetween(String value1, String value2) {
            addCriterion("chain_name between", value1, value2, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotBetween(String value1, String value2) {
            addCriterion("chain_name not between", value1, value2, "chainName");
            return (Criteria) this;
        }

        public Criteria andReviseTypesIsNull() {
            addCriterion("revise_types is null");
            return (Criteria) this;
        }

        public Criteria andReviseTypesIsNotNull() {
            addCriterion("revise_types is not null");
            return (Criteria) this;
        }

        public Criteria andReviseTypesEqualTo(String value) {
            addCriterion("revise_types =", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesNotEqualTo(String value) {
            addCriterion("revise_types <>", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesGreaterThan(String value) {
            addCriterion("revise_types >", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesGreaterThanOrEqualTo(String value) {
            addCriterion("revise_types >=", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesLessThan(String value) {
            addCriterion("revise_types <", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesLessThanOrEqualTo(String value) {
            addCriterion("revise_types <=", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesLike(String value) {
            addCriterion("revise_types like", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesNotLike(String value) {
            addCriterion("revise_types not like", value, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesIn(List<String> values) {
            addCriterion("revise_types in", values, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesNotIn(List<String> values) {
            addCriterion("revise_types not in", values, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesBetween(String value1, String value2) {
            addCriterion("revise_types between", value1, value2, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andReviseTypesNotBetween(String value1, String value2) {
            addCriterion("revise_types not between", value1, value2, "reviseTypes");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2IsNull() {
            addCriterion("org_cnt_v2 is null");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2IsNotNull() {
            addCriterion("org_cnt_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2EqualTo(String value) {
            addCriterion("org_cnt_v2 =", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2NotEqualTo(String value) {
            addCriterion("org_cnt_v2 <>", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2GreaterThan(String value) {
            addCriterion("org_cnt_v2 >", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2GreaterThanOrEqualTo(String value) {
            addCriterion("org_cnt_v2 >=", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2LessThan(String value) {
            addCriterion("org_cnt_v2 <", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2LessThanOrEqualTo(String value) {
            addCriterion("org_cnt_v2 <=", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2Like(String value) {
            addCriterion("org_cnt_v2 like", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2NotLike(String value) {
            addCriterion("org_cnt_v2 not like", value, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2In(List<String> values) {
            addCriterion("org_cnt_v2 in", values, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2NotIn(List<String> values) {
            addCriterion("org_cnt_v2 not in", values, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2Between(String value1, String value2) {
            addCriterion("org_cnt_v2 between", value1, value2, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV2NotBetween(String value1, String value2) {
            addCriterion("org_cnt_v2 not between", value1, value2, "orgCntV2");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3IsNull() {
            addCriterion("org_cnt_v3 is null");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3IsNotNull() {
            addCriterion("org_cnt_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3EqualTo(String value) {
            addCriterion("org_cnt_v3 =", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotEqualTo(String value) {
            addCriterion("org_cnt_v3 <>", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3GreaterThan(String value) {
            addCriterion("org_cnt_v3 >", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3GreaterThanOrEqualTo(String value) {
            addCriterion("org_cnt_v3 >=", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3LessThan(String value) {
            addCriterion("org_cnt_v3 <", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3LessThanOrEqualTo(String value) {
            addCriterion("org_cnt_v3 <=", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3Like(String value) {
            addCriterion("org_cnt_v3 like", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotLike(String value) {
            addCriterion("org_cnt_v3 not like", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3In(List<String> values) {
            addCriterion("org_cnt_v3 in", values, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotIn(List<String> values) {
            addCriterion("org_cnt_v3 not in", values, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3Between(String value1, String value2) {
            addCriterion("org_cnt_v3 between", value1, value2, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotBetween(String value1, String value2) {
            addCriterion("org_cnt_v3 not between", value1, value2, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3IsNull() {
            addCriterion("cnt_v3 is null");
            return (Criteria) this;
        }

        public Criteria andCntV3IsNotNull() {
            addCriterion("cnt_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andCntV3EqualTo(String value) {
            addCriterion("cnt_v3 =", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3NotEqualTo(String value) {
            addCriterion("cnt_v3 <>", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3GreaterThan(String value) {
            addCriterion("cnt_v3 >", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3GreaterThanOrEqualTo(String value) {
            addCriterion("cnt_v3 >=", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3LessThan(String value) {
            addCriterion("cnt_v3 <", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3LessThanOrEqualTo(String value) {
            addCriterion("cnt_v3 <=", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3Like(String value) {
            addCriterion("cnt_v3 like", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3NotLike(String value) {
            addCriterion("cnt_v3 not like", value, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3In(List<String> values) {
            addCriterion("cnt_v3 in", values, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3NotIn(List<String> values) {
            addCriterion("cnt_v3 not in", values, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3Between(String value1, String value2) {
            addCriterion("cnt_v3 between", value1, value2, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3NotBetween(String value1, String value2) {
            addCriterion("cnt_v3 not between", value1, value2, "cntV3");
            return (Criteria) this;
        }

        public Criteria andCntV3V2IsNull() {
            addCriterion("cnt_v3_v2 is null");
            return (Criteria) this;
        }

        public Criteria andCntV3V2IsNotNull() {
            addCriterion("cnt_v3_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andCntV3V2EqualTo(String value) {
            addCriterion("cnt_v3_v2 =", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2NotEqualTo(String value) {
            addCriterion("cnt_v3_v2 <>", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2GreaterThan(String value) {
            addCriterion("cnt_v3_v2 >", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2GreaterThanOrEqualTo(String value) {
            addCriterion("cnt_v3_v2 >=", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2LessThan(String value) {
            addCriterion("cnt_v3_v2 <", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2LessThanOrEqualTo(String value) {
            addCriterion("cnt_v3_v2 <=", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2Like(String value) {
            addCriterion("cnt_v3_v2 like", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2NotLike(String value) {
            addCriterion("cnt_v3_v2 not like", value, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2In(List<String> values) {
            addCriterion("cnt_v3_v2 in", values, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2NotIn(List<String> values) {
            addCriterion("cnt_v3_v2 not in", values, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2Between(String value1, String value2) {
            addCriterion("cnt_v3_v2 between", value1, value2, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntV3V2NotBetween(String value1, String value2) {
            addCriterion("cnt_v3_v2 not between", value1, value2, "cntV3V2");
            return (Criteria) this;
        }

        public Criteria andCntXzIsNull() {
            addCriterion("cnt_xz is null");
            return (Criteria) this;
        }

        public Criteria andCntXzIsNotNull() {
            addCriterion("cnt_xz is not null");
            return (Criteria) this;
        }

        public Criteria andCntXzEqualTo(String value) {
            addCriterion("cnt_xz =", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzNotEqualTo(String value) {
            addCriterion("cnt_xz <>", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzGreaterThan(String value) {
            addCriterion("cnt_xz >", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzGreaterThanOrEqualTo(String value) {
            addCriterion("cnt_xz >=", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzLessThan(String value) {
            addCriterion("cnt_xz <", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzLessThanOrEqualTo(String value) {
            addCriterion("cnt_xz <=", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzLike(String value) {
            addCriterion("cnt_xz like", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzNotLike(String value) {
            addCriterion("cnt_xz not like", value, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzIn(List<String> values) {
            addCriterion("cnt_xz in", values, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzNotIn(List<String> values) {
            addCriterion("cnt_xz not in", values, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzBetween(String value1, String value2) {
            addCriterion("cnt_xz between", value1, value2, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntXzNotBetween(String value1, String value2) {
            addCriterion("cnt_xz not between", value1, value2, "cntXz");
            return (Criteria) this;
        }

        public Criteria andCntTcIsNull() {
            addCriterion("cnt_tc is null");
            return (Criteria) this;
        }

        public Criteria andCntTcIsNotNull() {
            addCriterion("cnt_tc is not null");
            return (Criteria) this;
        }

        public Criteria andCntTcEqualTo(String value) {
            addCriterion("cnt_tc =", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcNotEqualTo(String value) {
            addCriterion("cnt_tc <>", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcGreaterThan(String value) {
            addCriterion("cnt_tc >", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcGreaterThanOrEqualTo(String value) {
            addCriterion("cnt_tc >=", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcLessThan(String value) {
            addCriterion("cnt_tc <", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcLessThanOrEqualTo(String value) {
            addCriterion("cnt_tc <=", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcLike(String value) {
            addCriterion("cnt_tc like", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcNotLike(String value) {
            addCriterion("cnt_tc not like", value, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcIn(List<String> values) {
            addCriterion("cnt_tc in", values, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcNotIn(List<String> values) {
            addCriterion("cnt_tc not in", values, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcBetween(String value1, String value2) {
            addCriterion("cnt_tc between", value1, value2, "cntTc");
            return (Criteria) this;
        }

        public Criteria andCntTcNotBetween(String value1, String value2) {
            addCriterion("cnt_tc not between", value1, value2, "cntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntIsNull() {
            addCriterion("avg_sku_cnt is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntIsNotNull() {
            addCriterion("avg_sku_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntEqualTo(String value) {
            addCriterion("avg_sku_cnt =", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntNotEqualTo(String value) {
            addCriterion("avg_sku_cnt <>", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntGreaterThan(String value) {
            addCriterion("avg_sku_cnt >", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt >=", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntLessThan(String value) {
            addCriterion("avg_sku_cnt <", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt <=", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntLike(String value) {
            addCriterion("avg_sku_cnt like", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntNotLike(String value) {
            addCriterion("avg_sku_cnt not like", value, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntIn(List<String> values) {
            addCriterion("avg_sku_cnt in", values, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntNotIn(List<String> values) {
            addCriterion("avg_sku_cnt not in", values, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt between", value1, value2, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt not between", value1, value2, "avgSkuCnt");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2IsNull() {
            addCriterion("avg_sku_cnt_v3_v2 is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2IsNotNull() {
            addCriterion("avg_sku_cnt_v3_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2EqualTo(String value) {
            addCriterion("avg_sku_cnt_v3_v2 =", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2NotEqualTo(String value) {
            addCriterion("avg_sku_cnt_v3_v2 <>", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2GreaterThan(String value) {
            addCriterion("avg_sku_cnt_v3_v2 >", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2GreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_v3_v2 >=", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2LessThan(String value) {
            addCriterion("avg_sku_cnt_v3_v2 <", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2LessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_v3_v2 <=", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2Like(String value) {
            addCriterion("avg_sku_cnt_v3_v2 like", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2NotLike(String value) {
            addCriterion("avg_sku_cnt_v3_v2 not like", value, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2In(List<String> values) {
            addCriterion("avg_sku_cnt_v3_v2 in", values, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2NotIn(List<String> values) {
            addCriterion("avg_sku_cnt_v3_v2 not in", values, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2Between(String value1, String value2) {
            addCriterion("avg_sku_cnt_v3_v2 between", value1, value2, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntV3V2NotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_v3_v2 not between", value1, value2, "avgSkuCntV3V2");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcIsNull() {
            addCriterion("avg_sku_cnt_tc is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcIsNotNull() {
            addCriterion("avg_sku_cnt_tc is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcEqualTo(String value) {
            addCriterion("avg_sku_cnt_tc =", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcNotEqualTo(String value) {
            addCriterion("avg_sku_cnt_tc <>", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcGreaterThan(String value) {
            addCriterion("avg_sku_cnt_tc >", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_tc >=", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcLessThan(String value) {
            addCriterion("avg_sku_cnt_tc <", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_tc <=", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcLike(String value) {
            addCriterion("avg_sku_cnt_tc like", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcNotLike(String value) {
            addCriterion("avg_sku_cnt_tc not like", value, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcIn(List<String> values) {
            addCriterion("avg_sku_cnt_tc in", values, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcNotIn(List<String> values) {
            addCriterion("avg_sku_cnt_tc not in", values, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_tc between", value1, value2, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntTcNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_tc not between", value1, value2, "avgSkuCntTc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcIsNull() {
            addCriterion("avg_sku_cnt_jc is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcIsNotNull() {
            addCriterion("avg_sku_cnt_jc is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcEqualTo(String value) {
            addCriterion("avg_sku_cnt_jc =", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcNotEqualTo(String value) {
            addCriterion("avg_sku_cnt_jc <>", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcGreaterThan(String value) {
            addCriterion("avg_sku_cnt_jc >", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_jc >=", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcLessThan(String value) {
            addCriterion("avg_sku_cnt_jc <", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_jc <=", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcLike(String value) {
            addCriterion("avg_sku_cnt_jc like", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcNotLike(String value) {
            addCriterion("avg_sku_cnt_jc not like", value, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcIn(List<String> values) {
            addCriterion("avg_sku_cnt_jc in", values, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcNotIn(List<String> values) {
            addCriterion("avg_sku_cnt_jc not in", values, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_jc between", value1, value2, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJcNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_jc not between", value1, value2, "avgSkuCntJc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcIsNull() {
            addCriterion("avg_sku_cnt_dc is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcIsNotNull() {
            addCriterion("avg_sku_cnt_dc is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcEqualTo(String value) {
            addCriterion("avg_sku_cnt_dc =", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcNotEqualTo(String value) {
            addCriterion("avg_sku_cnt_dc <>", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcGreaterThan(String value) {
            addCriterion("avg_sku_cnt_dc >", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_dc >=", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcLessThan(String value) {
            addCriterion("avg_sku_cnt_dc <", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_dc <=", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcLike(String value) {
            addCriterion("avg_sku_cnt_dc like", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcNotLike(String value) {
            addCriterion("avg_sku_cnt_dc not like", value, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcIn(List<String> values) {
            addCriterion("avg_sku_cnt_dc in", values, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcNotIn(List<String> values) {
            addCriterion("avg_sku_cnt_dc not in", values, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_dc between", value1, value2, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntDcNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_dc not between", value1, value2, "avgSkuCntDc");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaIsNull() {
            addCriterion("avg_sku_cnt_aa is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaIsNotNull() {
            addCriterion("avg_sku_cnt_aa is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaEqualTo(String value) {
            addCriterion("avg_sku_cnt_aa =", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaNotEqualTo(String value) {
            addCriterion("avg_sku_cnt_aa <>", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaGreaterThan(String value) {
            addCriterion("avg_sku_cnt_aa >", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_aa >=", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaLessThan(String value) {
            addCriterion("avg_sku_cnt_aa <", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_aa <=", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaLike(String value) {
            addCriterion("avg_sku_cnt_aa like", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaNotLike(String value) {
            addCriterion("avg_sku_cnt_aa not like", value, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaIn(List<String> values) {
            addCriterion("avg_sku_cnt_aa in", values, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaNotIn(List<String> values) {
            addCriterion("avg_sku_cnt_aa not in", values, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_aa between", value1, value2, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntAaNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_aa not between", value1, value2, "avgSkuCntAa");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1IsNull() {
            addCriterion("avg_sku_cnt_a1 is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1IsNotNull() {
            addCriterion("avg_sku_cnt_a1 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1EqualTo(String value) {
            addCriterion("avg_sku_cnt_a1 =", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1NotEqualTo(String value) {
            addCriterion("avg_sku_cnt_a1 <>", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1GreaterThan(String value) {
            addCriterion("avg_sku_cnt_a1 >", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1GreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_a1 >=", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1LessThan(String value) {
            addCriterion("avg_sku_cnt_a1 <", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1LessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_a1 <=", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1Like(String value) {
            addCriterion("avg_sku_cnt_a1 like", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1NotLike(String value) {
            addCriterion("avg_sku_cnt_a1 not like", value, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1In(List<String> values) {
            addCriterion("avg_sku_cnt_a1 in", values, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1NotIn(List<String> values) {
            addCriterion("avg_sku_cnt_a1 not in", values, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1Between(String value1, String value2) {
            addCriterion("avg_sku_cnt_a1 between", value1, value2, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntA1NotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_a1 not between", value1, value2, "avgSkuCntA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalIsNull() {
            addCriterion("amt_cum_30_total is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalIsNotNull() {
            addCriterion("amt_cum_30_total is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalEqualTo(String value) {
            addCriterion("amt_cum_30_total =", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalNotEqualTo(String value) {
            addCriterion("amt_cum_30_total <>", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalGreaterThan(String value) {
            addCriterion("amt_cum_30_total >", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_total >=", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalLessThan(String value) {
            addCriterion("amt_cum_30_total <", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_total <=", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalLike(String value) {
            addCriterion("amt_cum_30_total like", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalNotLike(String value) {
            addCriterion("amt_cum_30_total not like", value, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalIn(List<String> values) {
            addCriterion("amt_cum_30_total in", values, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalNotIn(List<String> values) {
            addCriterion("amt_cum_30_total not in", values, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalBetween(String value1, String value2) {
            addCriterion("amt_cum_30_total between", value1, value2, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30TotalNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_total not between", value1, value2, "amtCum30Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalIsNull() {
            addCriterion("amt_cum_30_rate_v3_total is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total =", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total <>", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total >", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total >=", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total <", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total <=", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total like", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total not like", value, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total in", values, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total not in", values, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total between", value1, value2, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total not between", value1, value2, "amtCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalIsNull() {
            addCriterion("amt_cum_30_rate_v3_v2_total is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_v2_total is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total =", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total <>", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total >", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total >=", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total <", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total <=", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalLike(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total like", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_v2_total not like", value, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_v2_total in", values, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_v2_total not in", values, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_v2_total between", value1, value2, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2TotalNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_v2_total not between", value1, value2, "amtCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalIsNull() {
            addCriterion("amt_cum_30_v3_v2_total is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalIsNotNull() {
            addCriterion("amt_cum_30_v3_v2_total is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_total =", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalNotEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_total <>", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalGreaterThan(String value) {
            addCriterion("amt_cum_30_v3_v2_total >", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_total >=", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalLessThan(String value) {
            addCriterion("amt_cum_30_v3_v2_total <", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_total <=", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalLike(String value) {
            addCriterion("amt_cum_30_v3_v2_total like", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalNotLike(String value) {
            addCriterion("amt_cum_30_v3_v2_total not like", value, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalIn(List<String> values) {
            addCriterion("amt_cum_30_v3_v2_total in", values, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalNotIn(List<String> values) {
            addCriterion("amt_cum_30_v3_v2_total not in", values, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2_total between", value1, value2, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2TotalNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2_total not between", value1, value2, "amtCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalIsNull() {
            addCriterion("amt_cum_30_v3_v2_1_total is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalIsNotNull() {
            addCriterion("amt_cum_30_v3_v2_1_total is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total =", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalNotEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total <>", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalGreaterThan(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total >", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total >=", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalLessThan(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total <", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total <=", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalLike(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total like", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalNotLike(String value) {
            addCriterion("amt_cum_30_v3_v2_1_total not like", value, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalIn(List<String> values) {
            addCriterion("amt_cum_30_v3_v2_1_total in", values, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalNotIn(List<String> values) {
            addCriterion("amt_cum_30_v3_v2_1_total not in", values, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2_1_total between", value1, value2, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21TotalNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2_1_total not between", value1, value2, "amtCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaIsNull() {
            addCriterion("amt_cum_30_rate_v3_total_aa is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_aa is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa =", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa <>", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa >", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa >=", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa <", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa <=", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa like", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_aa not like", value, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_aa in", values, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_aa not in", values, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_aa between", value1, value2, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalAaNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_aa not between", value1, value2, "amtCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1IsNull() {
            addCriterion("amt_cum_30_rate_v3_total_a1 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1IsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_a1 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1EqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 =", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1NotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 <>", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1GreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 >", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 >=", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1LessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 <", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 <=", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1Like(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 like", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1NotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_a1 not like", value, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1In(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_a1 in", values, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1NotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_a1 not in", values, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1Between(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_a1 between", value1, value2, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalA1NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_a1 not between", value1, value2, "amtCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcIsNull() {
            addCriterion("amt_cum_30_rate_v3_total_tc is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_tc is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc =", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc <>", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc >", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc >=", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc <", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc <=", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc like", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_tc not like", value, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_tc in", values, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_tc not in", values, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_tc between", value1, value2, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalTcNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_tc not between", value1, value2, "amtCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcIsNull() {
            addCriterion("amt_cum_30_rate_v3_total_jc is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_jc is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc =", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc <>", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc >", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc >=", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc <", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc <=", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc like", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_jc not like", value, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_jc in", values, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_jc not in", values, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_jc between", value1, value2, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalJcNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_jc not between", value1, value2, "amtCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcIsNull() {
            addCriterion("amt_cum_30_rate_v3_total_dc is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_dc is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc =", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc <>", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc >", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc >=", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc <", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc <=", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc like", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_dc not like", value, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_dc in", values, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_dc not in", values, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_dc between", value1, value2, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalDcNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_dc not between", value1, value2, "amtCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnIsNull() {
            addCriterion("amt_cum_30_rate_v3_total_on is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_on is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on =", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on <>", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on >", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on >=", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on <", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on <=", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on like", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_on not like", value, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_on in", values, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_on not in", values, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_on between", value1, value2, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOnNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_on not between", value1, value2, "amtCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffIsNull() {
            addCriterion("amt_cum_30_rate_v3_total_off is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_total_off is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off =", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off <>", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off >", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off >=", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off <", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off <=", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off like", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_total_off not like", value, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_off in", values, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_total_off not in", values, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_off between", value1, value2, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TotalOffNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_total_off not between", value1, value2, "amtCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalIsNull() {
            addCriterion("profit_cum_30_v3_total is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalIsNotNull() {
            addCriterion("profit_cum_30_v3_total is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalEqualTo(String value) {
            addCriterion("profit_cum_30_v3_total =", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalNotEqualTo(String value) {
            addCriterion("profit_cum_30_v3_total <>", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalGreaterThan(String value) {
            addCriterion("profit_cum_30_v3_total >", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_total >=", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalLessThan(String value) {
            addCriterion("profit_cum_30_v3_total <", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_total <=", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalLike(String value) {
            addCriterion("profit_cum_30_v3_total like", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalNotLike(String value) {
            addCriterion("profit_cum_30_v3_total not like", value, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalIn(List<String> values) {
            addCriterion("profit_cum_30_v3_total in", values, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalNotIn(List<String> values) {
            addCriterion("profit_cum_30_v3_total not in", values, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_total between", value1, value2, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3TotalNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_total not between", value1, value2, "profitCum30V3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalIsNull() {
            addCriterion("profit_cum_30_v3_v2_total is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalIsNotNull() {
            addCriterion("profit_cum_30_v3_v2_total is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_total =", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalNotEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_total <>", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalGreaterThan(String value) {
            addCriterion("profit_cum_30_v3_v2_total >", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_total >=", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalLessThan(String value) {
            addCriterion("profit_cum_30_v3_v2_total <", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_total <=", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalLike(String value) {
            addCriterion("profit_cum_30_v3_v2_total like", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalNotLike(String value) {
            addCriterion("profit_cum_30_v3_v2_total not like", value, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalIn(List<String> values) {
            addCriterion("profit_cum_30_v3_v2_total in", values, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalNotIn(List<String> values) {
            addCriterion("profit_cum_30_v3_v2_total not in", values, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2_total between", value1, value2, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2TotalNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2_total not between", value1, value2, "profitCum30V3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalIsNull() {
            addCriterion("profit_cum_30_v3_v2_1_total is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalIsNotNull() {
            addCriterion("profit_cum_30_v3_v2_1_total is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total =", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalNotEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total <>", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalGreaterThan(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total >", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total >=", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalLessThan(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total <", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total <=", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalLike(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total like", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalNotLike(String value) {
            addCriterion("profit_cum_30_v3_v2_1_total not like", value, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalIn(List<String> values) {
            addCriterion("profit_cum_30_v3_v2_1_total in", values, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalNotIn(List<String> values) {
            addCriterion("profit_cum_30_v3_v2_1_total not in", values, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2_1_total between", value1, value2, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21TotalNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2_1_total not between", value1, value2, "profitCum30V3V21Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalIsNull() {
            addCriterion("profit_cum_30_rate_v3_total is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total =", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total <>", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total >", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total >=", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total <", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total <=", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total like", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total not like", value, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total in", values, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total not in", values, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total between", value1, value2, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total not between", value1, value2, "profitCum30RateV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalIsNull() {
            addCriterion("amt_cum_30_rate_gx_v3_total is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalIsNotNull() {
            addCriterion("amt_cum_30_rate_gx_v3_total is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total =", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total <>", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total >", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total >=", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalLessThan(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total <", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total <=", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalLike(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total like", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalNotLike(String value) {
            addCriterion("amt_cum_30_rate_gx_v3_total not like", value, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalIn(List<String> values) {
            addCriterion("amt_cum_30_rate_gx_v3_total in", values, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_gx_v3_total not in", values, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_gx_v3_total between", value1, value2, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3TotalNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_gx_v3_total not between", value1, value2, "amtCum30RateGxV3Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalIsNull() {
            addCriterion("profit_cum_30_rate_v3_v2_total is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_v2_total is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total =", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total <>", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total >", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total >=", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total <", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total <=", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalLike(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total like", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_v2_total not like", value, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_v2_total in", values, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_v2_total not in", values, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_v2_total between", value1, value2, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2TotalNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_v2_total not between", value1, value2, "profitCum30RateV3V2Total");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaIsNull() {
            addCriterion("profit_cum_30_rate_v3_total_aa is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_aa is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa =", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa <>", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa >", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa >=", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa <", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa <=", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa like", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_aa not like", value, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_aa in", values, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_aa not in", values, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_aa between", value1, value2, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalAaNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_aa not between", value1, value2, "profitCum30RateV3TotalAa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1IsNull() {
            addCriterion("profit_cum_30_rate_v3_total_a1 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1IsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_a1 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1EqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 =", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1NotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 <>", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1GreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 >", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 >=", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1LessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 <", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 <=", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1Like(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 like", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1NotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_a1 not like", value, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1In(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_a1 in", values, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1NotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_a1 not in", values, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1Between(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_a1 between", value1, value2, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalA1NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_a1 not between", value1, value2, "profitCum30RateV3TotalA1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcIsNull() {
            addCriterion("profit_cum_30_rate_v3_total_tc is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_tc is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc =", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc <>", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc >", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc >=", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc <", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc <=", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc like", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_tc not like", value, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_tc in", values, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_tc not in", values, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_tc between", value1, value2, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalTcNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_tc not between", value1, value2, "profitCum30RateV3TotalTc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcIsNull() {
            addCriterion("profit_cum_30_rate_v3_total_jc is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_jc is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc =", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc <>", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc >", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc >=", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc <", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc <=", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc like", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_jc not like", value, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_jc in", values, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_jc not in", values, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_jc between", value1, value2, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalJcNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_jc not between", value1, value2, "profitCum30RateV3TotalJc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcIsNull() {
            addCriterion("profit_cum_30_rate_v3_total_dc is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_dc is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc =", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc <>", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc >", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc >=", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc <", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc <=", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc like", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_dc not like", value, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_dc in", values, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_dc not in", values, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_dc between", value1, value2, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalDcNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_dc not between", value1, value2, "profitCum30RateV3TotalDc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnIsNull() {
            addCriterion("profit_cum_30_rate_v3_total_on is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_on is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on =", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on <>", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on >", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on >=", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on <", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on <=", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on like", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_on not like", value, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_on in", values, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_on not in", values, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_on between", value1, value2, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOnNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_on not between", value1, value2, "profitCum30RateV3TotalOn");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffIsNull() {
            addCriterion("profit_cum_30_rate_v3_total_off is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_total_off is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off =", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off <>", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off >", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off >=", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off <", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off <=", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off like", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_total_off not like", value, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_off in", values, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_total_off not in", values, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_off between", value1, value2, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TotalOffNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_total_off not between", value1, value2, "profitCum30RateV3TotalOff");
            return (Criteria) this;
        }

        public Criteria andAvgCntIsNull() {
            addCriterion("avg_cnt is null");
            return (Criteria) this;
        }

        public Criteria andAvgCntIsNotNull() {
            addCriterion("avg_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andAvgCntEqualTo(String value) {
            addCriterion("avg_cnt =", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotEqualTo(String value) {
            addCriterion("avg_cnt <>", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntGreaterThan(String value) {
            addCriterion("avg_cnt >", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntGreaterThanOrEqualTo(String value) {
            addCriterion("avg_cnt >=", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntLessThan(String value) {
            addCriterion("avg_cnt <", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntLessThanOrEqualTo(String value) {
            addCriterion("avg_cnt <=", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntLike(String value) {
            addCriterion("avg_cnt like", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotLike(String value) {
            addCriterion("avg_cnt not like", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntIn(List<String> values) {
            addCriterion("avg_cnt in", values, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotIn(List<String> values) {
            addCriterion("avg_cnt not in", values, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntBetween(String value1, String value2) {
            addCriterion("avg_cnt between", value1, value2, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotBetween(String value1, String value2) {
            addCriterion("avg_cnt not between", value1, value2, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullIsNull() {
            addCriterion("amt_cum_30_null is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullIsNotNull() {
            addCriterion("amt_cum_30_null is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullEqualTo(String value) {
            addCriterion("amt_cum_30_null =", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotEqualTo(String value) {
            addCriterion("amt_cum_30_null <>", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullGreaterThan(String value) {
            addCriterion("amt_cum_30_null >", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_null >=", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullLessThan(String value) {
            addCriterion("amt_cum_30_null <", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_null <=", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullLike(String value) {
            addCriterion("amt_cum_30_null like", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotLike(String value) {
            addCriterion("amt_cum_30_null not like", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullIn(List<String> values) {
            addCriterion("amt_cum_30_null in", values, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotIn(List<String> values) {
            addCriterion("amt_cum_30_null not in", values, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullBetween(String value1, String value2) {
            addCriterion("amt_cum_30_null between", value1, value2, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_null not between", value1, value2, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullIsNull() {
            addCriterion("avg_amt_cum_30_null is null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullIsNotNull() {
            addCriterion("avg_amt_cum_30_null is not null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null =", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null <>", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullGreaterThan(String value) {
            addCriterion("avg_amt_cum_30_null >", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullGreaterThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null >=", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullLessThan(String value) {
            addCriterion("avg_amt_cum_30_null <", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullLessThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null <=", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullLike(String value) {
            addCriterion("avg_amt_cum_30_null like", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotLike(String value) {
            addCriterion("avg_amt_cum_30_null not like", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullIn(List<String> values) {
            addCriterion("avg_amt_cum_30_null in", values, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotIn(List<String> values) {
            addCriterion("avg_amt_cum_30_null not in", values, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_null between", value1, value2, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_null not between", value1, value2, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewIsNull() {
            addCriterion("amt_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewIsNotNull() {
            addCriterion("amt_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewEqualTo(String value) {
            addCriterion("amt_cum_30_new =", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotEqualTo(String value) {
            addCriterion("amt_cum_30_new <>", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewGreaterThan(String value) {
            addCriterion("amt_cum_30_new >", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_new >=", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewLessThan(String value) {
            addCriterion("amt_cum_30_new <", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_new <=", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewLike(String value) {
            addCriterion("amt_cum_30_new like", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotLike(String value) {
            addCriterion("amt_cum_30_new not like", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewIn(List<String> values) {
            addCriterion("amt_cum_30_new in", values, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotIn(List<String> values) {
            addCriterion("amt_cum_30_new not in", values, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewBetween(String value1, String value2) {
            addCriterion("amt_cum_30_new between", value1, value2, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_new not between", value1, value2, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewIsNull() {
            addCriterion("avg_amt_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewIsNotNull() {
            addCriterion("avg_amt_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new =", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new <>", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewGreaterThan(String value) {
            addCriterion("avg_amt_cum_30_new >", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new >=", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewLessThan(String value) {
            addCriterion("avg_amt_cum_30_new <", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewLessThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new <=", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewLike(String value) {
            addCriterion("avg_amt_cum_30_new like", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotLike(String value) {
            addCriterion("avg_amt_cum_30_new not like", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewIn(List<String> values) {
            addCriterion("avg_amt_cum_30_new in", values, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotIn(List<String> values) {
            addCriterion("avg_amt_cum_30_new not in", values, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_new between", value1, value2, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_new not between", value1, value2, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewIsNull() {
            addCriterion("amt_cum_30_rate_new is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewIsNotNull() {
            addCriterion("amt_cum_30_rate_new is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new =", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new <>", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_new >", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new >=", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewLessThan(String value) {
            addCriterion("amt_cum_30_rate_new <", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new <=", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewLike(String value) {
            addCriterion("amt_cum_30_rate_new like", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotLike(String value) {
            addCriterion("amt_cum_30_rate_new not like", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new in", values, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new not in", values, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new between", value1, value2, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new not between", value1, value2, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewIsNull() {
            addCriterion("profit_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewIsNotNull() {
            addCriterion("profit_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewEqualTo(String value) {
            addCriterion("profit_cum_30_new =", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotEqualTo(String value) {
            addCriterion("profit_cum_30_new <>", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewGreaterThan(String value) {
            addCriterion("profit_cum_30_new >", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_new >=", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewLessThan(String value) {
            addCriterion("profit_cum_30_new <", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_new <=", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewLike(String value) {
            addCriterion("profit_cum_30_new like", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotLike(String value) {
            addCriterion("profit_cum_30_new not like", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewIn(List<String> values) {
            addCriterion("profit_cum_30_new in", values, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotIn(List<String> values) {
            addCriterion("profit_cum_30_new not in", values, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewBetween(String value1, String value2) {
            addCriterion("profit_cum_30_new between", value1, value2, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_new not between", value1, value2, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewIsNull() {
            addCriterion("profit_cum_30_rate_new is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewIsNotNull() {
            addCriterion("profit_cum_30_rate_new is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new =", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new <>", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_new >", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new >=", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewLessThan(String value) {
            addCriterion("profit_cum_30_rate_new <", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new <=", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewLike(String value) {
            addCriterion("profit_cum_30_rate_new like", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotLike(String value) {
            addCriterion("profit_cum_30_rate_new not like", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewIn(List<String> values) {
            addCriterion("profit_cum_30_rate_new in", values, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_new not in", values, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_new between", value1, value2, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_new not between", value1, value2, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3IsNull() {
            addCriterion("amt_cum_30_v3 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3IsNotNull() {
            addCriterion("amt_cum_30_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3EqualTo(String value) {
            addCriterion("amt_cum_30_v3 =", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3NotEqualTo(String value) {
            addCriterion("amt_cum_30_v3 <>", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3GreaterThan(String value) {
            addCriterion("amt_cum_30_v3 >", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3 >=", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3LessThan(String value) {
            addCriterion("amt_cum_30_v3 <", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3 <=", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3Like(String value) {
            addCriterion("amt_cum_30_v3 like", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3NotLike(String value) {
            addCriterion("amt_cum_30_v3 not like", value, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3In(List<String> values) {
            addCriterion("amt_cum_30_v3 in", values, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3NotIn(List<String> values) {
            addCriterion("amt_cum_30_v3 not in", values, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3Between(String value1, String value2) {
            addCriterion("amt_cum_30_v3 between", value1, value2, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3 not between", value1, value2, "amtCum30V3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3IsNull() {
            addCriterion("amt_cum_30_rate_v3 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3IsNotNull() {
            addCriterion("amt_cum_30_rate_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3EqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3 =", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3NotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3 <>", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3GreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3 >", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3 >=", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3LessThan(String value) {
            addCriterion("amt_cum_30_rate_v3 <", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3 <=", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3Like(String value) {
            addCriterion("amt_cum_30_rate_v3 like", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3NotLike(String value) {
            addCriterion("amt_cum_30_rate_v3 not like", value, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3In(List<String> values) {
            addCriterion("amt_cum_30_rate_v3 in", values, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3NotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3 not in", values, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3Between(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3 between", value1, value2, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3 not between", value1, value2, "amtCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2IsNull() {
            addCriterion("amt_cum_30_rate_v3_v2 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2IsNotNull() {
            addCriterion("amt_cum_30_rate_v3_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2EqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 =", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2NotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 <>", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2GreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 >", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 >=", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2LessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 <", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 <=", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2Like(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 like", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2NotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_v2 not like", value, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2In(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_v2 in", values, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2NotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_v2 not in", values, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2Between(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_v2 between", value1, value2, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3V2NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_v2 not between", value1, value2, "amtCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2IsNull() {
            addCriterion("amt_cum_30_v3_v2 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2IsNotNull() {
            addCriterion("amt_cum_30_v3_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2EqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2 =", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2NotEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2 <>", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2GreaterThan(String value) {
            addCriterion("amt_cum_30_v3_v2 >", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2 >=", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2LessThan(String value) {
            addCriterion("amt_cum_30_v3_v2 <", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2 <=", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2Like(String value) {
            addCriterion("amt_cum_30_v3_v2 like", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2NotLike(String value) {
            addCriterion("amt_cum_30_v3_v2 not like", value, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2In(List<String> values) {
            addCriterion("amt_cum_30_v3_v2 in", values, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2NotIn(List<String> values) {
            addCriterion("amt_cum_30_v3_v2 not in", values, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2Between(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2 between", value1, value2, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V2NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2 not between", value1, value2, "amtCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21IsNull() {
            addCriterion("amt_cum_30_v3_v2_1 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21IsNotNull() {
            addCriterion("amt_cum_30_v3_v2_1 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21EqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1 =", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21NotEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1 <>", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21GreaterThan(String value) {
            addCriterion("amt_cum_30_v3_v2_1 >", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1 >=", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21LessThan(String value) {
            addCriterion("amt_cum_30_v3_v2_1 <", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_v3_v2_1 <=", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21Like(String value) {
            addCriterion("amt_cum_30_v3_v2_1 like", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21NotLike(String value) {
            addCriterion("amt_cum_30_v3_v2_1 not like", value, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21In(List<String> values) {
            addCriterion("amt_cum_30_v3_v2_1 in", values, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21NotIn(List<String> values) {
            addCriterion("amt_cum_30_v3_v2_1 not in", values, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21Between(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2_1 between", value1, value2, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30V3V21NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_v3_v2_1 not between", value1, value2, "amtCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaIsNull() {
            addCriterion("amt_cum_30_rate_v3_aa is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_aa is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_aa =", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_aa <>", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_aa >", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_aa >=", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_aa <", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_aa <=", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaLike(String value) {
            addCriterion("amt_cum_30_rate_v3_aa like", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_aa not like", value, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_aa in", values, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_aa not in", values, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_aa between", value1, value2, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3AaNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_aa not between", value1, value2, "amtCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1IsNull() {
            addCriterion("amt_cum_30_rate_v3_a1 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1IsNotNull() {
            addCriterion("amt_cum_30_rate_v3_a1 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1EqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 =", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1NotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 <>", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1GreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 >", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 >=", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1LessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 <", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 <=", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1Like(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 like", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1NotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_a1 not like", value, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1In(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_a1 in", values, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1NotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_a1 not in", values, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1Between(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_a1 between", value1, value2, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3A1NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_a1 not between", value1, value2, "amtCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcIsNull() {
            addCriterion("amt_cum_30_rate_v3_tc is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_tc is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_tc =", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_tc <>", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_tc >", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_tc >=", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_tc <", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_tc <=", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcLike(String value) {
            addCriterion("amt_cum_30_rate_v3_tc like", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_tc not like", value, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_tc in", values, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_tc not in", values, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_tc between", value1, value2, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3TcNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_tc not between", value1, value2, "amtCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcIsNull() {
            addCriterion("amt_cum_30_rate_v3_jc is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_jc is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_jc =", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_jc <>", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_jc >", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_jc >=", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_jc <", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_jc <=", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcLike(String value) {
            addCriterion("amt_cum_30_rate_v3_jc like", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_jc not like", value, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_jc in", values, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_jc not in", values, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_jc between", value1, value2, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3JcNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_jc not between", value1, value2, "amtCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcIsNull() {
            addCriterion("amt_cum_30_rate_v3_dc is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_dc is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_dc =", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_dc <>", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_dc >", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_dc >=", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_dc <", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_dc <=", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcLike(String value) {
            addCriterion("amt_cum_30_rate_v3_dc like", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_dc not like", value, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_dc in", values, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_dc not in", values, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_dc between", value1, value2, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3DcNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_dc not between", value1, value2, "amtCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnIsNull() {
            addCriterion("amt_cum_30_rate_v3_on is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_on is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_on =", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_on <>", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_on >", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_on >=", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_on <", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_on <=", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnLike(String value) {
            addCriterion("amt_cum_30_rate_v3_on like", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_on not like", value, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_on in", values, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_on not in", values, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_on between", value1, value2, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OnNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_on not between", value1, value2, "amtCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffIsNull() {
            addCriterion("amt_cum_30_rate_v3_off is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffIsNotNull() {
            addCriterion("amt_cum_30_rate_v3_off is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_off =", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_off <>", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_v3_off >", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_off >=", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffLessThan(String value) {
            addCriterion("amt_cum_30_rate_v3_off <", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_v3_off <=", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffLike(String value) {
            addCriterion("amt_cum_30_rate_v3_off like", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffNotLike(String value) {
            addCriterion("amt_cum_30_rate_v3_off not like", value, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_off in", values, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_v3_off not in", values, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_off between", value1, value2, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateV3OffNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_v3_off not between", value1, value2, "amtCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3IsNull() {
            addCriterion("profit_cum_30_v3 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3IsNotNull() {
            addCriterion("profit_cum_30_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3EqualTo(String value) {
            addCriterion("profit_cum_30_v3 =", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3NotEqualTo(String value) {
            addCriterion("profit_cum_30_v3 <>", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3GreaterThan(String value) {
            addCriterion("profit_cum_30_v3 >", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3 >=", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3LessThan(String value) {
            addCriterion("profit_cum_30_v3 <", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3 <=", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3Like(String value) {
            addCriterion("profit_cum_30_v3 like", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3NotLike(String value) {
            addCriterion("profit_cum_30_v3 not like", value, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3In(List<String> values) {
            addCriterion("profit_cum_30_v3 in", values, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3NotIn(List<String> values) {
            addCriterion("profit_cum_30_v3 not in", values, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3Between(String value1, String value2) {
            addCriterion("profit_cum_30_v3 between", value1, value2, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3 not between", value1, value2, "profitCum30V3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2IsNull() {
            addCriterion("profit_cum_30_v3_v2 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2IsNotNull() {
            addCriterion("profit_cum_30_v3_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2EqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2 =", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2NotEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2 <>", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2GreaterThan(String value) {
            addCriterion("profit_cum_30_v3_v2 >", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2 >=", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2LessThan(String value) {
            addCriterion("profit_cum_30_v3_v2 <", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2 <=", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2Like(String value) {
            addCriterion("profit_cum_30_v3_v2 like", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2NotLike(String value) {
            addCriterion("profit_cum_30_v3_v2 not like", value, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2In(List<String> values) {
            addCriterion("profit_cum_30_v3_v2 in", values, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2NotIn(List<String> values) {
            addCriterion("profit_cum_30_v3_v2 not in", values, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2Between(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2 between", value1, value2, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V2NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2 not between", value1, value2, "profitCum30V3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21IsNull() {
            addCriterion("profit_cum_30_v3_v2_1 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21IsNotNull() {
            addCriterion("profit_cum_30_v3_v2_1 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21EqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1 =", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21NotEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1 <>", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21GreaterThan(String value) {
            addCriterion("profit_cum_30_v3_v2_1 >", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1 >=", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21LessThan(String value) {
            addCriterion("profit_cum_30_v3_v2_1 <", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_v3_v2_1 <=", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21Like(String value) {
            addCriterion("profit_cum_30_v3_v2_1 like", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21NotLike(String value) {
            addCriterion("profit_cum_30_v3_v2_1 not like", value, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21In(List<String> values) {
            addCriterion("profit_cum_30_v3_v2_1 in", values, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21NotIn(List<String> values) {
            addCriterion("profit_cum_30_v3_v2_1 not in", values, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21Between(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2_1 between", value1, value2, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30V3V21NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_v3_v2_1 not between", value1, value2, "profitCum30V3V21");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3IsNull() {
            addCriterion("profit_cum_30_rate_v3 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3IsNotNull() {
            addCriterion("profit_cum_30_rate_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3EqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3 =", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3NotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3 <>", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3GreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3 >", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3 >=", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3LessThan(String value) {
            addCriterion("profit_cum_30_rate_v3 <", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3 <=", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3Like(String value) {
            addCriterion("profit_cum_30_rate_v3 like", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3NotLike(String value) {
            addCriterion("profit_cum_30_rate_v3 not like", value, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3In(List<String> values) {
            addCriterion("profit_cum_30_rate_v3 in", values, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3NotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3 not in", values, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3Between(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3 between", value1, value2, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3 not between", value1, value2, "profitCum30RateV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3IsNull() {
            addCriterion("amt_cum_30_rate_gx_v3 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3IsNotNull() {
            addCriterion("amt_cum_30_rate_gx_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3EqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 =", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3NotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 <>", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3GreaterThan(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 >", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 >=", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3LessThan(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 <", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 <=", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3Like(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 like", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3NotLike(String value) {
            addCriterion("amt_cum_30_rate_gx_v3 not like", value, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3In(List<String> values) {
            addCriterion("amt_cum_30_rate_gx_v3 in", values, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3NotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_gx_v3 not in", values, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3Between(String value1, String value2) {
            addCriterion("amt_cum_30_rate_gx_v3 between", value1, value2, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGxV3NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_gx_v3 not between", value1, value2, "amtCum30RateGxV3");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2IsNull() {
            addCriterion("profit_cum_30_rate_v3_v2 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2IsNotNull() {
            addCriterion("profit_cum_30_rate_v3_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2EqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 =", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2NotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 <>", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2GreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 >", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 >=", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2LessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 <", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 <=", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2Like(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 like", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2NotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_v2 not like", value, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2In(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_v2 in", values, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2NotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_v2 not in", values, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2Between(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_v2 between", value1, value2, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3V2NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_v2 not between", value1, value2, "profitCum30RateV3V2");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaIsNull() {
            addCriterion("profit_cum_30_rate_v3_aa is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_aa is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_aa =", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_aa <>", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_aa >", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_aa >=", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_aa <", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_aa <=", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaLike(String value) {
            addCriterion("profit_cum_30_rate_v3_aa like", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_aa not like", value, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_aa in", values, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_aa not in", values, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_aa between", value1, value2, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3AaNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_aa not between", value1, value2, "profitCum30RateV3Aa");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1IsNull() {
            addCriterion("profit_cum_30_rate_v3_a1 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1IsNotNull() {
            addCriterion("profit_cum_30_rate_v3_a1 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1EqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 =", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1NotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 <>", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1GreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 >", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 >=", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1LessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 <", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 <=", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1Like(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 like", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1NotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_a1 not like", value, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1In(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_a1 in", values, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1NotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_a1 not in", values, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1Between(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_a1 between", value1, value2, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3A1NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_a1 not between", value1, value2, "profitCum30RateV3A1");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcIsNull() {
            addCriterion("profit_cum_30_rate_v3_tc is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_tc is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_tc =", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_tc <>", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_tc >", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_tc >=", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_tc <", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_tc <=", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcLike(String value) {
            addCriterion("profit_cum_30_rate_v3_tc like", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_tc not like", value, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_tc in", values, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_tc not in", values, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_tc between", value1, value2, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3TcNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_tc not between", value1, value2, "profitCum30RateV3Tc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcIsNull() {
            addCriterion("profit_cum_30_rate_v3_jc is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_jc is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_jc =", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_jc <>", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_jc >", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_jc >=", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_jc <", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_jc <=", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcLike(String value) {
            addCriterion("profit_cum_30_rate_v3_jc like", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_jc not like", value, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_jc in", values, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_jc not in", values, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_jc between", value1, value2, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3JcNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_jc not between", value1, value2, "profitCum30RateV3Jc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcIsNull() {
            addCriterion("profit_cum_30_rate_v3_dc is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_dc is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_dc =", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_dc <>", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_dc >", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_dc >=", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_dc <", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_dc <=", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcLike(String value) {
            addCriterion("profit_cum_30_rate_v3_dc like", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_dc not like", value, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_dc in", values, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_dc not in", values, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_dc between", value1, value2, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3DcNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_dc not between", value1, value2, "profitCum30RateV3Dc");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnIsNull() {
            addCriterion("profit_cum_30_rate_v3_on is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_on is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_on =", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_on <>", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_on >", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_on >=", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_on <", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_on <=", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnLike(String value) {
            addCriterion("profit_cum_30_rate_v3_on like", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_on not like", value, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_on in", values, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_on not in", values, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_on between", value1, value2, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OnNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_on not between", value1, value2, "profitCum30RateV3On");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffIsNull() {
            addCriterion("profit_cum_30_rate_v3_off is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffIsNotNull() {
            addCriterion("profit_cum_30_rate_v3_off is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_off =", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_off <>", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_v3_off >", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_off >=", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffLessThan(String value) {
            addCriterion("profit_cum_30_rate_v3_off <", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_v3_off <=", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffLike(String value) {
            addCriterion("profit_cum_30_rate_v3_off like", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffNotLike(String value) {
            addCriterion("profit_cum_30_rate_v3_off not like", value, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_off in", values, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_v3_off not in", values, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_off between", value1, value2, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateV3OffNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_v3_off not between", value1, value2, "profitCum30RateV3Off");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}