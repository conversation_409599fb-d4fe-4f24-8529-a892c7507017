package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 新店推荐目录排名
 */
public class TrackRetultNewStoreRankDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 门店MDM编码
     */
    private String orgNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品编码
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsname;

    /**
     * 规格
     */
    private String goodsspec;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 剂型
     */
    private String jxCate1Name;

    /**
     * 单位
     */
    private String goodsunit;

    /**
     * 推荐来源
     */
    private String level;

    /**
     * 建议首次备货数量
     */
    private BigDecimal suggestPhQty;

    /**
     * 备货库存成本金额
     */
    private BigDecimal phCost;

    /**
     * 经营属性
     */
    private String taotaiType;

    /**
     * 销售属性
     */
    private String stjb;

    /**
     * 采购属性
     */
    private String grossprofit;

    /**
     * 子类id
     */
    private String subCategoryId;

    /**
     * 大类
     */
    private String classoneName;

    /**
     * 中类
     */
    private String classtwoName;

    /**
     * 小类
     */
    private String classthreeName;

    /**
     * 子类
     */
    private String classfourName;

    /**
     * 成分
     */
    private String component;

    /**
     * DTP商品(D)
     */
    private String dtpgood;

    /**
     * 疾病种
     */
    private String flagDisease;

    /**
     * 归属部门
     */
    private String department;

    /**
     * 禁止配送
     */
    private String distribind;

    /**
     * 是否贵重
     */
    private String precious;

    /**
     * 参考零售价
     */
    private String refretailprice;

    /**
     * 集中度-本企业本城市本店型
     */
    private String inStockRateDx;

    /**
     * 动销率-本企业本城市本店型
     */
    private String inSalesRateDx;

    /**
     * 近90天销售数量-本企业本城市本店型
     */
    private String numCum90Dx;

    /**
     * 近90天客流量-本企业本城市本店型
     */
    private String billCntsCum90Dx;

    /**
     * 近90天销售金额-本企业本城市本店型
     */
    private String amtCum90Dx;

    /**
     * 毛利率-本企业本城市本店型
     */
    private String profitRate90Dx;

    /**
     * 集中度-本企业本城市
     */
    private String inStockRateCity;

    /**
     * 动销率-本企业本城市
     */
    private String inSalesRateCity;

    /**
     * 近90天销售数量-本企业本城市
     */
    private String numCum90City;

    /**
     * 近90天客流量-本企业本城市
     */
    private String billCntsCum90City;

    /**
     * 近90天销售金额-本企业本城市
     */
    private String amtCum90City;

    /**
     * 毛利率-本企业本城市
     */
    private String profitRate90City;

    /**
     * 集中度-本企业
     */
    private String inStockRateQy;

    /**
     * 动销率-本企业
     */
    private String inSalesRateQy;

    /**
     * 近90天销售数量-本企业
     */
    private String numCum90Qy;

    /**
     * 近90天客流量-本企业
     */
    private String billCntsCum90Qy;

    /**
     * 近90天销售金额-本企业
     */
    private String amtCum90Qy;

    /**
     * 毛利率-本企业
     */
    private String profitRate90Qy;

    /**
     * 集中度-相似门店
     */
    private String inStockRateMd;

    /**
     * 动销率-相似门店
     */
    private String inSalesRateMd;

    /**
     * 近90天销售数量-相似门店
     */
    private String numCum90Md;

    /**
     * 近90天客流量-相似门店
     */
    private String billCntsCum90Md;

    /**
     * 近90天销售金额-相似门店
     */
    private String amtCum90Md;

    /**
     * 毛利率-相似门店
     */
    private String profitRate90Md;

    /**
     * 是否经营 0 否 1是
     */
    private Byte jyAble;

    /**
     * rx/otc
     */
    private String rxOtc;

    private String bak1;

    private String bak2;

    private String bak3;

    private String bak4;

    private String bak5;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getJxCate1Name() {
        return jxCate1Name;
    }

    public void setJxCate1Name(String jxCate1Name) {
        this.jxCate1Name = jxCate1Name;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public BigDecimal getSuggestPhQty() {
        return suggestPhQty;
    }

    public void setSuggestPhQty(BigDecimal suggestPhQty) {
        this.suggestPhQty = suggestPhQty;
    }

    public BigDecimal getPhCost() {
        return phCost;
    }

    public void setPhCost(BigDecimal phCost) {
        this.phCost = phCost;
    }

    public String getTaotaiType() {
        return taotaiType;
    }

    public void setTaotaiType(String taotaiType) {
        this.taotaiType = taotaiType;
    }

    public String getStjb() {
        return stjb;
    }

    public void setStjb(String stjb) {
        this.stjb = stjb;
    }

    public String getGrossprofit() {
        return grossprofit;
    }

    public void setGrossprofit(String grossprofit) {
        this.grossprofit = grossprofit;
    }

    public String getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(String subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getClassoneName() {
        return classoneName;
    }

    public void setClassoneName(String classoneName) {
        this.classoneName = classoneName;
    }

    public String getClasstwoName() {
        return classtwoName;
    }

    public void setClasstwoName(String classtwoName) {
        this.classtwoName = classtwoName;
    }

    public String getClassthreeName() {
        return classthreeName;
    }

    public void setClassthreeName(String classthreeName) {
        this.classthreeName = classthreeName;
    }

    public String getClassfourName() {
        return classfourName;
    }

    public void setClassfourName(String classfourName) {
        this.classfourName = classfourName;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getDtpgood() {
        return dtpgood;
    }

    public void setDtpgood(String dtpgood) {
        this.dtpgood = dtpgood;
    }

    public String getFlagDisease() {
        return flagDisease;
    }

    public void setFlagDisease(String flagDisease) {
        this.flagDisease = flagDisease;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDistribind() {
        return distribind;
    }

    public void setDistribind(String distribind) {
        this.distribind = distribind;
    }

    public String getPrecious() {
        return precious;
    }

    public void setPrecious(String precious) {
        this.precious = precious;
    }

    public String getRefretailprice() {
        return refretailprice;
    }

    public void setRefretailprice(String refretailprice) {
        this.refretailprice = refretailprice;
    }

    public String getInStockRateDx() {
        return inStockRateDx;
    }

    public void setInStockRateDx(String inStockRateDx) {
        this.inStockRateDx = inStockRateDx;
    }

    public String getInSalesRateDx() {
        return inSalesRateDx;
    }

    public void setInSalesRateDx(String inSalesRateDx) {
        this.inSalesRateDx = inSalesRateDx;
    }

    public String getNumCum90Dx() {
        return numCum90Dx;
    }

    public void setNumCum90Dx(String numCum90Dx) {
        this.numCum90Dx = numCum90Dx;
    }

    public String getBillCntsCum90Dx() {
        return billCntsCum90Dx;
    }

    public void setBillCntsCum90Dx(String billCntsCum90Dx) {
        this.billCntsCum90Dx = billCntsCum90Dx;
    }

    public String getAmtCum90Dx() {
        return amtCum90Dx;
    }

    public void setAmtCum90Dx(String amtCum90Dx) {
        this.amtCum90Dx = amtCum90Dx;
    }

    public String getProfitRate90Dx() {
        return profitRate90Dx;
    }

    public void setProfitRate90Dx(String profitRate90Dx) {
        this.profitRate90Dx = profitRate90Dx;
    }

    public String getInStockRateCity() {
        return inStockRateCity;
    }

    public void setInStockRateCity(String inStockRateCity) {
        this.inStockRateCity = inStockRateCity;
    }

    public String getInSalesRateCity() {
        return inSalesRateCity;
    }

    public void setInSalesRateCity(String inSalesRateCity) {
        this.inSalesRateCity = inSalesRateCity;
    }

    public String getNumCum90City() {
        return numCum90City;
    }

    public void setNumCum90City(String numCum90City) {
        this.numCum90City = numCum90City;
    }

    public String getBillCntsCum90City() {
        return billCntsCum90City;
    }

    public void setBillCntsCum90City(String billCntsCum90City) {
        this.billCntsCum90City = billCntsCum90City;
    }

    public String getAmtCum90City() {
        return amtCum90City;
    }

    public void setAmtCum90City(String amtCum90City) {
        this.amtCum90City = amtCum90City;
    }

    public String getProfitRate90City() {
        return profitRate90City;
    }

    public void setProfitRate90City(String profitRate90City) {
        this.profitRate90City = profitRate90City;
    }

    public String getInStockRateQy() {
        return inStockRateQy;
    }

    public void setInStockRateQy(String inStockRateQy) {
        this.inStockRateQy = inStockRateQy;
    }

    public String getInSalesRateQy() {
        return inSalesRateQy;
    }

    public void setInSalesRateQy(String inSalesRateQy) {
        this.inSalesRateQy = inSalesRateQy;
    }

    public String getNumCum90Qy() {
        return numCum90Qy;
    }

    public void setNumCum90Qy(String numCum90Qy) {
        this.numCum90Qy = numCum90Qy;
    }

    public String getBillCntsCum90Qy() {
        return billCntsCum90Qy;
    }

    public void setBillCntsCum90Qy(String billCntsCum90Qy) {
        this.billCntsCum90Qy = billCntsCum90Qy;
    }

    public String getAmtCum90Qy() {
        return amtCum90Qy;
    }

    public void setAmtCum90Qy(String amtCum90Qy) {
        this.amtCum90Qy = amtCum90Qy;
    }

    public String getProfitRate90Qy() {
        return profitRate90Qy;
    }

    public void setProfitRate90Qy(String profitRate90Qy) {
        this.profitRate90Qy = profitRate90Qy;
    }

    public String getInStockRateMd() {
        return inStockRateMd;
    }

    public void setInStockRateMd(String inStockRateMd) {
        this.inStockRateMd = inStockRateMd;
    }

    public String getInSalesRateMd() {
        return inSalesRateMd;
    }

    public void setInSalesRateMd(String inSalesRateMd) {
        this.inSalesRateMd = inSalesRateMd;
    }

    public String getNumCum90Md() {
        return numCum90Md;
    }

    public void setNumCum90Md(String numCum90Md) {
        this.numCum90Md = numCum90Md;
    }

    public String getBillCntsCum90Md() {
        return billCntsCum90Md;
    }

    public void setBillCntsCum90Md(String billCntsCum90Md) {
        this.billCntsCum90Md = billCntsCum90Md;
    }

    public String getAmtCum90Md() {
        return amtCum90Md;
    }

    public void setAmtCum90Md(String amtCum90Md) {
        this.amtCum90Md = amtCum90Md;
    }

    public String getProfitRate90Md() {
        return profitRate90Md;
    }

    public void setProfitRate90Md(String profitRate90Md) {
        this.profitRate90Md = profitRate90Md;
    }

    public Byte getJyAble() {
        return jyAble;
    }

    public void setJyAble(Byte jyAble) {
        this.jyAble = jyAble;
    }

    public String getRxOtc() {
        return rxOtc;
    }

    public void setRxOtc(String rxOtc) {
        this.rxOtc = rxOtc;
    }

    public String getBak1() {
        return bak1;
    }

    public void setBak1(String bak1) {
        this.bak1 = bak1;
    }

    public String getBak2() {
        return bak2;
    }

    public void setBak2(String bak2) {
        this.bak2 = bak2;
    }

    public String getBak3() {
        return bak3;
    }

    public void setBak3(String bak3) {
        this.bak3 = bak3;
    }

    public String getBak4() {
        return bak4;
    }

    public void setBak4(String bak4) {
        this.bak4 = bak4;
    }

    public String getBak5() {
        return bak5;
    }

    public void setBak5(String bak5) {
        this.bak5 = bak5;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TrackRetultNewStoreRankDetail other = (TrackRetultNewStoreRankDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getRank() == null ? other.getRank() == null : this.getRank().equals(other.getRank()))
            && (this.getOrgNo() == null ? other.getOrgNo() == null : this.getOrgNo().equals(other.getOrgNo()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getGoodsId() == null ? other.getGoodsId() == null : this.getGoodsId().equals(other.getGoodsId()))
            && (this.getGoodsname() == null ? other.getGoodsname() == null : this.getGoodsname().equals(other.getGoodsname()))
            && (this.getGoodsspec() == null ? other.getGoodsspec() == null : this.getGoodsspec().equals(other.getGoodsspec()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getJxCate1Name() == null ? other.getJxCate1Name() == null : this.getJxCate1Name().equals(other.getJxCate1Name()))
            && (this.getGoodsunit() == null ? other.getGoodsunit() == null : this.getGoodsunit().equals(other.getGoodsunit()))
            && (this.getLevel() == null ? other.getLevel() == null : this.getLevel().equals(other.getLevel()))
            && (this.getSuggestPhQty() == null ? other.getSuggestPhQty() == null : this.getSuggestPhQty().equals(other.getSuggestPhQty()))
            && (this.getPhCost() == null ? other.getPhCost() == null : this.getPhCost().equals(other.getPhCost()))
            && (this.getTaotaiType() == null ? other.getTaotaiType() == null : this.getTaotaiType().equals(other.getTaotaiType()))
            && (this.getStjb() == null ? other.getStjb() == null : this.getStjb().equals(other.getStjb()))
            && (this.getGrossprofit() == null ? other.getGrossprofit() == null : this.getGrossprofit().equals(other.getGrossprofit()))
            && (this.getSubCategoryId() == null ? other.getSubCategoryId() == null : this.getSubCategoryId().equals(other.getSubCategoryId()))
            && (this.getClassoneName() == null ? other.getClassoneName() == null : this.getClassoneName().equals(other.getClassoneName()))
            && (this.getClasstwoName() == null ? other.getClasstwoName() == null : this.getClasstwoName().equals(other.getClasstwoName()))
            && (this.getClassthreeName() == null ? other.getClassthreeName() == null : this.getClassthreeName().equals(other.getClassthreeName()))
            && (this.getClassfourName() == null ? other.getClassfourName() == null : this.getClassfourName().equals(other.getClassfourName()))
            && (this.getComponent() == null ? other.getComponent() == null : this.getComponent().equals(other.getComponent()))
            && (this.getDtpgood() == null ? other.getDtpgood() == null : this.getDtpgood().equals(other.getDtpgood()))
            && (this.getFlagDisease() == null ? other.getFlagDisease() == null : this.getFlagDisease().equals(other.getFlagDisease()))
            && (this.getDepartment() == null ? other.getDepartment() == null : this.getDepartment().equals(other.getDepartment()))
            && (this.getDistribind() == null ? other.getDistribind() == null : this.getDistribind().equals(other.getDistribind()))
            && (this.getPrecious() == null ? other.getPrecious() == null : this.getPrecious().equals(other.getPrecious()))
            && (this.getRefretailprice() == null ? other.getRefretailprice() == null : this.getRefretailprice().equals(other.getRefretailprice()))
            && (this.getInStockRateDx() == null ? other.getInStockRateDx() == null : this.getInStockRateDx().equals(other.getInStockRateDx()))
            && (this.getInSalesRateDx() == null ? other.getInSalesRateDx() == null : this.getInSalesRateDx().equals(other.getInSalesRateDx()))
            && (this.getNumCum90Dx() == null ? other.getNumCum90Dx() == null : this.getNumCum90Dx().equals(other.getNumCum90Dx()))
            && (this.getBillCntsCum90Dx() == null ? other.getBillCntsCum90Dx() == null : this.getBillCntsCum90Dx().equals(other.getBillCntsCum90Dx()))
            && (this.getAmtCum90Dx() == null ? other.getAmtCum90Dx() == null : this.getAmtCum90Dx().equals(other.getAmtCum90Dx()))
            && (this.getProfitRate90Dx() == null ? other.getProfitRate90Dx() == null : this.getProfitRate90Dx().equals(other.getProfitRate90Dx()))
            && (this.getInStockRateCity() == null ? other.getInStockRateCity() == null : this.getInStockRateCity().equals(other.getInStockRateCity()))
            && (this.getInSalesRateCity() == null ? other.getInSalesRateCity() == null : this.getInSalesRateCity().equals(other.getInSalesRateCity()))
            && (this.getNumCum90City() == null ? other.getNumCum90City() == null : this.getNumCum90City().equals(other.getNumCum90City()))
            && (this.getBillCntsCum90City() == null ? other.getBillCntsCum90City() == null : this.getBillCntsCum90City().equals(other.getBillCntsCum90City()))
            && (this.getAmtCum90City() == null ? other.getAmtCum90City() == null : this.getAmtCum90City().equals(other.getAmtCum90City()))
            && (this.getProfitRate90City() == null ? other.getProfitRate90City() == null : this.getProfitRate90City().equals(other.getProfitRate90City()))
            && (this.getInStockRateQy() == null ? other.getInStockRateQy() == null : this.getInStockRateQy().equals(other.getInStockRateQy()))
            && (this.getInSalesRateQy() == null ? other.getInSalesRateQy() == null : this.getInSalesRateQy().equals(other.getInSalesRateQy()))
            && (this.getNumCum90Qy() == null ? other.getNumCum90Qy() == null : this.getNumCum90Qy().equals(other.getNumCum90Qy()))
            && (this.getBillCntsCum90Qy() == null ? other.getBillCntsCum90Qy() == null : this.getBillCntsCum90Qy().equals(other.getBillCntsCum90Qy()))
            && (this.getAmtCum90Qy() == null ? other.getAmtCum90Qy() == null : this.getAmtCum90Qy().equals(other.getAmtCum90Qy()))
            && (this.getProfitRate90Qy() == null ? other.getProfitRate90Qy() == null : this.getProfitRate90Qy().equals(other.getProfitRate90Qy()))
            && (this.getInStockRateMd() == null ? other.getInStockRateMd() == null : this.getInStockRateMd().equals(other.getInStockRateMd()))
            && (this.getInSalesRateMd() == null ? other.getInSalesRateMd() == null : this.getInSalesRateMd().equals(other.getInSalesRateMd()))
            && (this.getNumCum90Md() == null ? other.getNumCum90Md() == null : this.getNumCum90Md().equals(other.getNumCum90Md()))
            && (this.getBillCntsCum90Md() == null ? other.getBillCntsCum90Md() == null : this.getBillCntsCum90Md().equals(other.getBillCntsCum90Md()))
            && (this.getAmtCum90Md() == null ? other.getAmtCum90Md() == null : this.getAmtCum90Md().equals(other.getAmtCum90Md()))
            && (this.getProfitRate90Md() == null ? other.getProfitRate90Md() == null : this.getProfitRate90Md().equals(other.getProfitRate90Md()))
            && (this.getJyAble() == null ? other.getJyAble() == null : this.getJyAble().equals(other.getJyAble()))
            && (this.getRxOtc() == null ? other.getRxOtc() == null : this.getRxOtc().equals(other.getRxOtc()))
            && (this.getBak1() == null ? other.getBak1() == null : this.getBak1().equals(other.getBak1()))
            && (this.getBak2() == null ? other.getBak2() == null : this.getBak2().equals(other.getBak2()))
            && (this.getBak3() == null ? other.getBak3() == null : this.getBak3().equals(other.getBak3()))
            && (this.getBak4() == null ? other.getBak4() == null : this.getBak4().equals(other.getBak4()))
            && (this.getBak5() == null ? other.getBak5() == null : this.getBak5().equals(other.getBak5()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getRank() == null) ? 0 : getRank().hashCode());
        result = prime * result + ((getOrgNo() == null) ? 0 : getOrgNo().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getGoodsId() == null) ? 0 : getGoodsId().hashCode());
        result = prime * result + ((getGoodsname() == null) ? 0 : getGoodsname().hashCode());
        result = prime * result + ((getGoodsspec() == null) ? 0 : getGoodsspec().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getJxCate1Name() == null) ? 0 : getJxCate1Name().hashCode());
        result = prime * result + ((getGoodsunit() == null) ? 0 : getGoodsunit().hashCode());
        result = prime * result + ((getLevel() == null) ? 0 : getLevel().hashCode());
        result = prime * result + ((getSuggestPhQty() == null) ? 0 : getSuggestPhQty().hashCode());
        result = prime * result + ((getPhCost() == null) ? 0 : getPhCost().hashCode());
        result = prime * result + ((getTaotaiType() == null) ? 0 : getTaotaiType().hashCode());
        result = prime * result + ((getStjb() == null) ? 0 : getStjb().hashCode());
        result = prime * result + ((getGrossprofit() == null) ? 0 : getGrossprofit().hashCode());
        result = prime * result + ((getSubCategoryId() == null) ? 0 : getSubCategoryId().hashCode());
        result = prime * result + ((getClassoneName() == null) ? 0 : getClassoneName().hashCode());
        result = prime * result + ((getClasstwoName() == null) ? 0 : getClasstwoName().hashCode());
        result = prime * result + ((getClassthreeName() == null) ? 0 : getClassthreeName().hashCode());
        result = prime * result + ((getClassfourName() == null) ? 0 : getClassfourName().hashCode());
        result = prime * result + ((getComponent() == null) ? 0 : getComponent().hashCode());
        result = prime * result + ((getDtpgood() == null) ? 0 : getDtpgood().hashCode());
        result = prime * result + ((getFlagDisease() == null) ? 0 : getFlagDisease().hashCode());
        result = prime * result + ((getDepartment() == null) ? 0 : getDepartment().hashCode());
        result = prime * result + ((getDistribind() == null) ? 0 : getDistribind().hashCode());
        result = prime * result + ((getPrecious() == null) ? 0 : getPrecious().hashCode());
        result = prime * result + ((getRefretailprice() == null) ? 0 : getRefretailprice().hashCode());
        result = prime * result + ((getInStockRateDx() == null) ? 0 : getInStockRateDx().hashCode());
        result = prime * result + ((getInSalesRateDx() == null) ? 0 : getInSalesRateDx().hashCode());
        result = prime * result + ((getNumCum90Dx() == null) ? 0 : getNumCum90Dx().hashCode());
        result = prime * result + ((getBillCntsCum90Dx() == null) ? 0 : getBillCntsCum90Dx().hashCode());
        result = prime * result + ((getAmtCum90Dx() == null) ? 0 : getAmtCum90Dx().hashCode());
        result = prime * result + ((getProfitRate90Dx() == null) ? 0 : getProfitRate90Dx().hashCode());
        result = prime * result + ((getInStockRateCity() == null) ? 0 : getInStockRateCity().hashCode());
        result = prime * result + ((getInSalesRateCity() == null) ? 0 : getInSalesRateCity().hashCode());
        result = prime * result + ((getNumCum90City() == null) ? 0 : getNumCum90City().hashCode());
        result = prime * result + ((getBillCntsCum90City() == null) ? 0 : getBillCntsCum90City().hashCode());
        result = prime * result + ((getAmtCum90City() == null) ? 0 : getAmtCum90City().hashCode());
        result = prime * result + ((getProfitRate90City() == null) ? 0 : getProfitRate90City().hashCode());
        result = prime * result + ((getInStockRateQy() == null) ? 0 : getInStockRateQy().hashCode());
        result = prime * result + ((getInSalesRateQy() == null) ? 0 : getInSalesRateQy().hashCode());
        result = prime * result + ((getNumCum90Qy() == null) ? 0 : getNumCum90Qy().hashCode());
        result = prime * result + ((getBillCntsCum90Qy() == null) ? 0 : getBillCntsCum90Qy().hashCode());
        result = prime * result + ((getAmtCum90Qy() == null) ? 0 : getAmtCum90Qy().hashCode());
        result = prime * result + ((getProfitRate90Qy() == null) ? 0 : getProfitRate90Qy().hashCode());
        result = prime * result + ((getInStockRateMd() == null) ? 0 : getInStockRateMd().hashCode());
        result = prime * result + ((getInSalesRateMd() == null) ? 0 : getInSalesRateMd().hashCode());
        result = prime * result + ((getNumCum90Md() == null) ? 0 : getNumCum90Md().hashCode());
        result = prime * result + ((getBillCntsCum90Md() == null) ? 0 : getBillCntsCum90Md().hashCode());
        result = prime * result + ((getAmtCum90Md() == null) ? 0 : getAmtCum90Md().hashCode());
        result = prime * result + ((getProfitRate90Md() == null) ? 0 : getProfitRate90Md().hashCode());
        result = prime * result + ((getJyAble() == null) ? 0 : getJyAble().hashCode());
        result = prime * result + ((getRxOtc() == null) ? 0 : getRxOtc().hashCode());
        result = prime * result + ((getBak1() == null) ? 0 : getBak1().hashCode());
        result = prime * result + ((getBak2() == null) ? 0 : getBak2().hashCode());
        result = prime * result + ((getBak3() == null) ? 0 : getBak3().hashCode());
        result = prime * result + ((getBak4() == null) ? 0 : getBak4().hashCode());
        result = prime * result + ((getBak5() == null) ? 0 : getBak5().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", rank=").append(rank);
        sb.append(", orgNo=").append(orgNo);
        sb.append(", storeName=").append(storeName);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsname=").append(goodsname);
        sb.append(", goodsspec=").append(goodsspec);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", jxCate1Name=").append(jxCate1Name);
        sb.append(", goodsunit=").append(goodsunit);
        sb.append(", level=").append(level);
        sb.append(", suggestPhQty=").append(suggestPhQty);
        sb.append(", phCost=").append(phCost);
        sb.append(", taotaiType=").append(taotaiType);
        sb.append(", stjb=").append(stjb);
        sb.append(", grossprofit=").append(grossprofit);
        sb.append(", subCategoryId=").append(subCategoryId);
        sb.append(", classoneName=").append(classoneName);
        sb.append(", classtwoName=").append(classtwoName);
        sb.append(", classthreeName=").append(classthreeName);
        sb.append(", classfourName=").append(classfourName);
        sb.append(", component=").append(component);
        sb.append(", dtpgood=").append(dtpgood);
        sb.append(", flagDisease=").append(flagDisease);
        sb.append(", department=").append(department);
        sb.append(", distribind=").append(distribind);
        sb.append(", precious=").append(precious);
        sb.append(", refretailprice=").append(refretailprice);
        sb.append(", inStockRateDx=").append(inStockRateDx);
        sb.append(", inSalesRateDx=").append(inSalesRateDx);
        sb.append(", numCum90Dx=").append(numCum90Dx);
        sb.append(", billCntsCum90Dx=").append(billCntsCum90Dx);
        sb.append(", amtCum90Dx=").append(amtCum90Dx);
        sb.append(", profitRate90Dx=").append(profitRate90Dx);
        sb.append(", inStockRateCity=").append(inStockRateCity);
        sb.append(", inSalesRateCity=").append(inSalesRateCity);
        sb.append(", numCum90City=").append(numCum90City);
        sb.append(", billCntsCum90City=").append(billCntsCum90City);
        sb.append(", amtCum90City=").append(amtCum90City);
        sb.append(", profitRate90City=").append(profitRate90City);
        sb.append(", inStockRateQy=").append(inStockRateQy);
        sb.append(", inSalesRateQy=").append(inSalesRateQy);
        sb.append(", numCum90Qy=").append(numCum90Qy);
        sb.append(", billCntsCum90Qy=").append(billCntsCum90Qy);
        sb.append(", amtCum90Qy=").append(amtCum90Qy);
        sb.append(", profitRate90Qy=").append(profitRate90Qy);
        sb.append(", inStockRateMd=").append(inStockRateMd);
        sb.append(", inSalesRateMd=").append(inSalesRateMd);
        sb.append(", numCum90Md=").append(numCum90Md);
        sb.append(", billCntsCum90Md=").append(billCntsCum90Md);
        sb.append(", amtCum90Md=").append(amtCum90Md);
        sb.append(", profitRate90Md=").append(profitRate90Md);
        sb.append(", jyAble=").append(jyAble);
        sb.append(", rxOtc=").append(rxOtc);
        sb.append(", bak1=").append(bak1);
        sb.append(", bak2=").append(bak2);
        sb.append(", bak3=").append(bak3);
        sb.append(", bak4=").append(bak4);
        sb.append(", bak5=").append(bak5);
        sb.append(", extend=").append(extend);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", status=").append(status);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}