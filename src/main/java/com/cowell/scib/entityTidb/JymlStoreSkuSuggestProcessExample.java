package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlStoreSkuSuggestProcessExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlStoreSkuSuggestProcessExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNull() {
            addCriterion("business_org_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNotNull() {
            addCriterion("business_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdEqualTo(Long value) {
            addCriterion("business_org_id =", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotEqualTo(Long value) {
            addCriterion("business_org_id <>", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThan(Long value) {
            addCriterion("business_org_id >", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_org_id >=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThan(Long value) {
            addCriterion("business_org_id <", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("business_org_id <=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIn(List<Long> values) {
            addCriterion("business_org_id in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotIn(List<Long> values) {
            addCriterion("business_org_id not in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdBetween(Long value1, Long value2) {
            addCriterion("business_org_id between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("business_org_id not between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeIsNull() {
            addCriterion("zdt_start_notice is null");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeIsNotNull() {
            addCriterion("zdt_start_notice is not null");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeEqualTo(String value) {
            addCriterion("zdt_start_notice =", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeNotEqualTo(String value) {
            addCriterion("zdt_start_notice <>", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeGreaterThan(String value) {
            addCriterion("zdt_start_notice >", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeGreaterThanOrEqualTo(String value) {
            addCriterion("zdt_start_notice >=", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeLessThan(String value) {
            addCriterion("zdt_start_notice <", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeLessThanOrEqualTo(String value) {
            addCriterion("zdt_start_notice <=", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeLike(String value) {
            addCriterion("zdt_start_notice like", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeNotLike(String value) {
            addCriterion("zdt_start_notice not like", value, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeIn(List<String> values) {
            addCriterion("zdt_start_notice in", values, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeNotIn(List<String> values) {
            addCriterion("zdt_start_notice not in", values, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeBetween(String value1, String value2) {
            addCriterion("zdt_start_notice between", value1, value2, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeNotBetween(String value1, String value2) {
            addCriterion("zdt_start_notice not between", value1, value2, "zdtStartNotice");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgIsNull() {
            addCriterion("zdt_start_notice_falg is null");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgIsNotNull() {
            addCriterion("zdt_start_notice_falg is not null");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgEqualTo(Integer value) {
            addCriterion("zdt_start_notice_falg =", value, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgNotEqualTo(Integer value) {
            addCriterion("zdt_start_notice_falg <>", value, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgGreaterThan(Integer value) {
            addCriterion("zdt_start_notice_falg >", value, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgGreaterThanOrEqualTo(Integer value) {
            addCriterion("zdt_start_notice_falg >=", value, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgLessThan(Integer value) {
            addCriterion("zdt_start_notice_falg <", value, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgLessThanOrEqualTo(Integer value) {
            addCriterion("zdt_start_notice_falg <=", value, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgIn(List<Integer> values) {
            addCriterion("zdt_start_notice_falg in", values, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgNotIn(List<Integer> values) {
            addCriterion("zdt_start_notice_falg not in", values, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgBetween(Integer value1, Integer value2) {
            addCriterion("zdt_start_notice_falg between", value1, value2, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtStartNoticeFalgNotBetween(Integer value1, Integer value2) {
            addCriterion("zdt_start_notice_falg not between", value1, value2, "zdtStartNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeIsNull() {
            addCriterion("begin_process_time is null");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeIsNotNull() {
            addCriterion("begin_process_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeEqualTo(Date value) {
            addCriterion("begin_process_time =", value, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeNotEqualTo(Date value) {
            addCriterion("begin_process_time <>", value, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeGreaterThan(Date value) {
            addCriterion("begin_process_time >", value, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("begin_process_time >=", value, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeLessThan(Date value) {
            addCriterion("begin_process_time <", value, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("begin_process_time <=", value, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeIn(List<Date> values) {
            addCriterion("begin_process_time in", values, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeNotIn(List<Date> values) {
            addCriterion("begin_process_time not in", values, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeBetween(Date value1, Date value2) {
            addCriterion("begin_process_time between", value1, value2, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andBeginProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("begin_process_time not between", value1, value2, "beginProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeIsNull() {
            addCriterion("scheduled_process_time is null");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeIsNotNull() {
            addCriterion("scheduled_process_time is not null");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeEqualTo(Date value) {
            addCriterion("scheduled_process_time =", value, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeNotEqualTo(Date value) {
            addCriterion("scheduled_process_time <>", value, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeGreaterThan(Date value) {
            addCriterion("scheduled_process_time >", value, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("scheduled_process_time >=", value, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeLessThan(Date value) {
            addCriterion("scheduled_process_time <", value, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("scheduled_process_time <=", value, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeIn(List<Date> values) {
            addCriterion("scheduled_process_time in", values, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeNotIn(List<Date> values) {
            addCriterion("scheduled_process_time not in", values, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeBetween(Date value1, Date value2) {
            addCriterion("scheduled_process_time between", value1, value2, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andScheduledProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("scheduled_process_time not between", value1, value2, "scheduledProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeIsNull() {
            addCriterion("end_process_time is null");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeIsNotNull() {
            addCriterion("end_process_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeEqualTo(Date value) {
            addCriterion("end_process_time =", value, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeNotEqualTo(Date value) {
            addCriterion("end_process_time <>", value, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeGreaterThan(Date value) {
            addCriterion("end_process_time >", value, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_process_time >=", value, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeLessThan(Date value) {
            addCriterion("end_process_time <", value, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_process_time <=", value, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeIn(List<Date> values) {
            addCriterion("end_process_time in", values, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeNotIn(List<Date> values) {
            addCriterion("end_process_time not in", values, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeBetween(Date value1, Date value2) {
            addCriterion("end_process_time between", value1, value2, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andEndProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_process_time not between", value1, value2, "endProcessTime");
            return (Criteria) this;
        }

        public Criteria andConfirmedIsNull() {
            addCriterion("confirmed is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIsNotNull() {
            addCriterion("confirmed is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedEqualTo(Integer value) {
            addCriterion("confirmed =", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedNotEqualTo(Integer value) {
            addCriterion("confirmed <>", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedGreaterThan(Integer value) {
            addCriterion("confirmed >", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedGreaterThanOrEqualTo(Integer value) {
            addCriterion("confirmed >=", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedLessThan(Integer value) {
            addCriterion("confirmed <", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedLessThanOrEqualTo(Integer value) {
            addCriterion("confirmed <=", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedIn(List<Integer> values) {
            addCriterion("confirmed in", values, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedNotIn(List<Integer> values) {
            addCriterion("confirmed not in", values, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedBetween(Integer value1, Integer value2) {
            addCriterion("confirmed between", value1, value2, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedNotBetween(Integer value1, Integer value2) {
            addCriterion("confirmed not between", value1, value2, "confirmed");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeIsNull() {
            addCriterion("zdt_end_notice is null");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeIsNotNull() {
            addCriterion("zdt_end_notice is not null");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeEqualTo(String value) {
            addCriterion("zdt_end_notice =", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeNotEqualTo(String value) {
            addCriterion("zdt_end_notice <>", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeGreaterThan(String value) {
            addCriterion("zdt_end_notice >", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeGreaterThanOrEqualTo(String value) {
            addCriterion("zdt_end_notice >=", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeLessThan(String value) {
            addCriterion("zdt_end_notice <", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeLessThanOrEqualTo(String value) {
            addCriterion("zdt_end_notice <=", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeLike(String value) {
            addCriterion("zdt_end_notice like", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeNotLike(String value) {
            addCriterion("zdt_end_notice not like", value, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeIn(List<String> values) {
            addCriterion("zdt_end_notice in", values, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeNotIn(List<String> values) {
            addCriterion("zdt_end_notice not in", values, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeBetween(String value1, String value2) {
            addCriterion("zdt_end_notice between", value1, value2, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeNotBetween(String value1, String value2) {
            addCriterion("zdt_end_notice not between", value1, value2, "zdtEndNotice");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgIsNull() {
            addCriterion("zdt_end_notice_falg is null");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgIsNotNull() {
            addCriterion("zdt_end_notice_falg is not null");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgEqualTo(Integer value) {
            addCriterion("zdt_end_notice_falg =", value, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgNotEqualTo(Integer value) {
            addCriterion("zdt_end_notice_falg <>", value, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgGreaterThan(Integer value) {
            addCriterion("zdt_end_notice_falg >", value, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgGreaterThanOrEqualTo(Integer value) {
            addCriterion("zdt_end_notice_falg >=", value, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgLessThan(Integer value) {
            addCriterion("zdt_end_notice_falg <", value, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgLessThanOrEqualTo(Integer value) {
            addCriterion("zdt_end_notice_falg <=", value, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgIn(List<Integer> values) {
            addCriterion("zdt_end_notice_falg in", values, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgNotIn(List<Integer> values) {
            addCriterion("zdt_end_notice_falg not in", values, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgBetween(Integer value1, Integer value2) {
            addCriterion("zdt_end_notice_falg between", value1, value2, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andZdtEndNoticeFalgNotBetween(Integer value1, Integer value2) {
            addCriterion("zdt_end_notice_falg not between", value1, value2, "zdtEndNoticeFalg");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeIsNull() {
            addCriterion("next_begin_process_time is null");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeIsNotNull() {
            addCriterion("next_begin_process_time is not null");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeEqualTo(Date value) {
            addCriterion("next_begin_process_time =", value, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeNotEqualTo(Date value) {
            addCriterion("next_begin_process_time <>", value, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeGreaterThan(Date value) {
            addCriterion("next_begin_process_time >", value, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("next_begin_process_time >=", value, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeLessThan(Date value) {
            addCriterion("next_begin_process_time <", value, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("next_begin_process_time <=", value, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeIn(List<Date> values) {
            addCriterion("next_begin_process_time in", values, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeNotIn(List<Date> values) {
            addCriterion("next_begin_process_time not in", values, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeBetween(Date value1, Date value2) {
            addCriterion("next_begin_process_time between", value1, value2, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextBeginProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("next_begin_process_time not between", value1, value2, "nextBeginProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeIsNull() {
            addCriterion("next_end_process_time is null");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeIsNotNull() {
            addCriterion("next_end_process_time is not null");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeEqualTo(Date value) {
            addCriterion("next_end_process_time =", value, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeNotEqualTo(Date value) {
            addCriterion("next_end_process_time <>", value, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeGreaterThan(Date value) {
            addCriterion("next_end_process_time >", value, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("next_end_process_time >=", value, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeLessThan(Date value) {
            addCriterion("next_end_process_time <", value, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("next_end_process_time <=", value, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeIn(List<Date> values) {
            addCriterion("next_end_process_time in", values, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeNotIn(List<Date> values) {
            addCriterion("next_end_process_time not in", values, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeBetween(Date value1, Date value2) {
            addCriterion("next_end_process_time between", value1, value2, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andNextEndProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("next_end_process_time not between", value1, value2, "nextEndProcessTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}