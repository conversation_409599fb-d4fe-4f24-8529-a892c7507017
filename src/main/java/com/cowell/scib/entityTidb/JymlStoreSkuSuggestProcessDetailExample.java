package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlStoreSkuSuggestProcessDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlStoreSkuSuggestProcessDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNull() {
            addCriterion("business_org_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNotNull() {
            addCriterion("business_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdEqualTo(Long value) {
            addCriterion("business_org_id =", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotEqualTo(Long value) {
            addCriterion("business_org_id <>", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThan(Long value) {
            addCriterion("business_org_id >", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_org_id >=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThan(Long value) {
            addCriterion("business_org_id <", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("business_org_id <=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIn(List<Long> values) {
            addCriterion("business_org_id in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotIn(List<Long> values) {
            addCriterion("business_org_id not in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdBetween(Long value1, Long value2) {
            addCriterion("business_org_id between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("business_org_id not between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNull() {
            addCriterion("rx_otc is null");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNotNull() {
            addCriterion("rx_otc is not null");
            return (Criteria) this;
        }

        public Criteria andRxOtcEqualTo(String value) {
            addCriterion("rx_otc =", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotEqualTo(String value) {
            addCriterion("rx_otc <>", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThan(String value) {
            addCriterion("rx_otc >", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThanOrEqualTo(String value) {
            addCriterion("rx_otc >=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThan(String value) {
            addCriterion("rx_otc <", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThanOrEqualTo(String value) {
            addCriterion("rx_otc <=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLike(String value) {
            addCriterion("rx_otc like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotLike(String value) {
            addCriterion("rx_otc not like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcIn(List<String> values) {
            addCriterion("rx_otc in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotIn(List<String> values) {
            addCriterion("rx_otc not in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcBetween(String value1, String value2) {
            addCriterion("rx_otc between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotBetween(String value1, String value2) {
            addCriterion("rx_otc not between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNull() {
            addCriterion("middle_category is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNotNull() {
            addCriterion("middle_category is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryEqualTo(String value) {
            addCriterion("middle_category =", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotEqualTo(String value) {
            addCriterion("middle_category <>", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThan(String value) {
            addCriterion("middle_category >", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category >=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThan(String value) {
            addCriterion("middle_category <", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThanOrEqualTo(String value) {
            addCriterion("middle_category <=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLike(String value) {
            addCriterion("middle_category like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotLike(String value) {
            addCriterion("middle_category not like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIn(List<String> values) {
            addCriterion("middle_category in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotIn(List<String> values) {
            addCriterion("middle_category not in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryBetween(String value1, String value2) {
            addCriterion("middle_category between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotBetween(String value1, String value2) {
            addCriterion("middle_category not between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNull() {
            addCriterion("middle_category_name is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNotNull() {
            addCriterion("middle_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameEqualTo(String value) {
            addCriterion("middle_category_name =", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotEqualTo(String value) {
            addCriterion("middle_category_name <>", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThan(String value) {
            addCriterion("middle_category_name >", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category_name >=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThan(String value) {
            addCriterion("middle_category_name <", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("middle_category_name <=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLike(String value) {
            addCriterion("middle_category_name like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotLike(String value) {
            addCriterion("middle_category_name not like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIn(List<String> values) {
            addCriterion("middle_category_name in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotIn(List<String> values) {
            addCriterion("middle_category_name not in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameBetween(String value1, String value2) {
            addCriterion("middle_category_name between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotBetween(String value1, String value2) {
            addCriterion("middle_category_name not between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNull() {
            addCriterion("small_category is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNotNull() {
            addCriterion("small_category is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryEqualTo(String value) {
            addCriterion("small_category =", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotEqualTo(String value) {
            addCriterion("small_category <>", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThan(String value) {
            addCriterion("small_category >", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("small_category >=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThan(String value) {
            addCriterion("small_category <", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThanOrEqualTo(String value) {
            addCriterion("small_category <=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLike(String value) {
            addCriterion("small_category like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotLike(String value) {
            addCriterion("small_category not like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIn(List<String> values) {
            addCriterion("small_category in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotIn(List<String> values) {
            addCriterion("small_category not in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryBetween(String value1, String value2) {
            addCriterion("small_category between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotBetween(String value1, String value2) {
            addCriterion("small_category not between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNull() {
            addCriterion("small_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNotNull() {
            addCriterion("small_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameEqualTo(String value) {
            addCriterion("small_category_name =", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotEqualTo(String value) {
            addCriterion("small_category_name <>", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThan(String value) {
            addCriterion("small_category_name >", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("small_category_name >=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThan(String value) {
            addCriterion("small_category_name <", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("small_category_name <=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLike(String value) {
            addCriterion("small_category_name like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotLike(String value) {
            addCriterion("small_category_name not like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIn(List<String> values) {
            addCriterion("small_category_name in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotIn(List<String> values) {
            addCriterion("small_category_name not in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameBetween(String value1, String value2) {
            addCriterion("small_category_name between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotBetween(String value1, String value2) {
            addCriterion("small_category_name not between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNull() {
            addCriterion("sub_category is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNotNull() {
            addCriterion("sub_category is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryEqualTo(String value) {
            addCriterion("sub_category =", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotEqualTo(String value) {
            addCriterion("sub_category <>", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThan(String value) {
            addCriterion("sub_category >", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category >=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThan(String value) {
            addCriterion("sub_category <", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThanOrEqualTo(String value) {
            addCriterion("sub_category <=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLike(String value) {
            addCriterion("sub_category like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotLike(String value) {
            addCriterion("sub_category not like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIn(List<String> values) {
            addCriterion("sub_category in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotIn(List<String> values) {
            addCriterion("sub_category not in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryBetween(String value1, String value2) {
            addCriterion("sub_category between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotBetween(String value1, String value2) {
            addCriterion("sub_category not between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNull() {
            addCriterion("sub_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNotNull() {
            addCriterion("sub_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameEqualTo(String value) {
            addCriterion("sub_category_name =", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotEqualTo(String value) {
            addCriterion("sub_category_name <>", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThan(String value) {
            addCriterion("sub_category_name >", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_name >=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThan(String value) {
            addCriterion("sub_category_name <", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("sub_category_name <=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLike(String value) {
            addCriterion("sub_category_name like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotLike(String value) {
            addCriterion("sub_category_name not like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIn(List<String> values) {
            addCriterion("sub_category_name in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotIn(List<String> values) {
            addCriterion("sub_category_name not in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameBetween(String value1, String value2) {
            addCriterion("sub_category_name between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotBetween(String value1, String value2) {
            addCriterion("sub_category_name not between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andUpperLimitIsNull() {
            addCriterion("upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andUpperLimitIsNotNull() {
            addCriterion("upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andUpperLimitEqualTo(Integer value) {
            addCriterion("upper_limit =", value, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitNotEqualTo(Integer value) {
            addCriterion("upper_limit <>", value, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitGreaterThan(Integer value) {
            addCriterion("upper_limit >", value, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("upper_limit >=", value, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitLessThan(Integer value) {
            addCriterion("upper_limit <", value, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitLessThanOrEqualTo(Integer value) {
            addCriterion("upper_limit <=", value, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitIn(List<Integer> values) {
            addCriterion("upper_limit in", values, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitNotIn(List<Integer> values) {
            addCriterion("upper_limit not in", values, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitBetween(Integer value1, Integer value2) {
            addCriterion("upper_limit between", value1, value2, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andUpperLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("upper_limit not between", value1, value2, "upperLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitIsNull() {
            addCriterion("sku_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitIsNotNull() {
            addCriterion("sku_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitEqualTo(Integer value) {
            addCriterion("sku_lower_limit =", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitNotEqualTo(Integer value) {
            addCriterion("sku_lower_limit <>", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitGreaterThan(Integer value) {
            addCriterion("sku_lower_limit >", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_lower_limit >=", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitLessThan(Integer value) {
            addCriterion("sku_lower_limit <", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitLessThanOrEqualTo(Integer value) {
            addCriterion("sku_lower_limit <=", value, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitIn(List<Integer> values) {
            addCriterion("sku_lower_limit in", values, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitNotIn(List<Integer> values) {
            addCriterion("sku_lower_limit not in", values, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitBetween(Integer value1, Integer value2) {
            addCriterion("sku_lower_limit between", value1, value2, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuLowerLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_lower_limit not between", value1, value2, "skuLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitIsNull() {
            addCriterion("sku_max_limit is null");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitIsNotNull() {
            addCriterion("sku_max_limit is not null");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitEqualTo(Integer value) {
            addCriterion("sku_max_limit =", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitNotEqualTo(Integer value) {
            addCriterion("sku_max_limit <>", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitGreaterThan(Integer value) {
            addCriterion("sku_max_limit >", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_max_limit >=", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitLessThan(Integer value) {
            addCriterion("sku_max_limit <", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitLessThanOrEqualTo(Integer value) {
            addCriterion("sku_max_limit <=", value, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitIn(List<Integer> values) {
            addCriterion("sku_max_limit in", values, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitNotIn(List<Integer> values) {
            addCriterion("sku_max_limit not in", values, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitBetween(Integer value1, Integer value2) {
            addCriterion("sku_max_limit between", value1, value2, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuMaxLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_max_limit not between", value1, value2, "skuMaxLimit");
            return (Criteria) this;
        }

        public Criteria andSkuCountIsNull() {
            addCriterion("sku_count is null");
            return (Criteria) this;
        }

        public Criteria andSkuCountIsNotNull() {
            addCriterion("sku_count is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCountEqualTo(Integer value) {
            addCriterion("sku_count =", value, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountNotEqualTo(Integer value) {
            addCriterion("sku_count <>", value, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountGreaterThan(Integer value) {
            addCriterion("sku_count >", value, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_count >=", value, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountLessThan(Integer value) {
            addCriterion("sku_count <", value, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountLessThanOrEqualTo(Integer value) {
            addCriterion("sku_count <=", value, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountIn(List<Integer> values) {
            addCriterion("sku_count in", values, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountNotIn(List<Integer> values) {
            addCriterion("sku_count not in", values, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountBetween(Integer value1, Integer value2) {
            addCriterion("sku_count between", value1, value2, "skuCount");
            return (Criteria) this;
        }

        public Criteria andSkuCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_count not between", value1, value2, "skuCount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIsNull() {
            addCriterion("confirmed is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIsNotNull() {
            addCriterion("confirmed is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedEqualTo(Integer value) {
            addCriterion("confirmed =", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedNotEqualTo(Integer value) {
            addCriterion("confirmed <>", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedGreaterThan(Integer value) {
            addCriterion("confirmed >", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedGreaterThanOrEqualTo(Integer value) {
            addCriterion("confirmed >=", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedLessThan(Integer value) {
            addCriterion("confirmed <", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedLessThanOrEqualTo(Integer value) {
            addCriterion("confirmed <=", value, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedIn(List<Integer> values) {
            addCriterion("confirmed in", values, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedNotIn(List<Integer> values) {
            addCriterion("confirmed not in", values, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedBetween(Integer value1, Integer value2) {
            addCriterion("confirmed between", value1, value2, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedNotBetween(Integer value1, Integer value2) {
            addCriterion("confirmed not between", value1, value2, "confirmed");
            return (Criteria) this;
        }

        public Criteria andConfirmedByIsNull() {
            addCriterion("confirmed_by is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedByIsNotNull() {
            addCriterion("confirmed_by is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedByEqualTo(Long value) {
            addCriterion("confirmed_by =", value, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByNotEqualTo(Long value) {
            addCriterion("confirmed_by <>", value, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByGreaterThan(Long value) {
            addCriterion("confirmed_by >", value, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByGreaterThanOrEqualTo(Long value) {
            addCriterion("confirmed_by >=", value, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByLessThan(Long value) {
            addCriterion("confirmed_by <", value, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByLessThanOrEqualTo(Long value) {
            addCriterion("confirmed_by <=", value, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByIn(List<Long> values) {
            addCriterion("confirmed_by in", values, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByNotIn(List<Long> values) {
            addCriterion("confirmed_by not in", values, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByBetween(Long value1, Long value2) {
            addCriterion("confirmed_by between", value1, value2, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedByNotBetween(Long value1, Long value2) {
            addCriterion("confirmed_by not between", value1, value2, "confirmedBy");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameIsNull() {
            addCriterion("confirmed_name is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameIsNotNull() {
            addCriterion("confirmed_name is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameEqualTo(String value) {
            addCriterion("confirmed_name =", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameNotEqualTo(String value) {
            addCriterion("confirmed_name <>", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameGreaterThan(String value) {
            addCriterion("confirmed_name >", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameGreaterThanOrEqualTo(String value) {
            addCriterion("confirmed_name >=", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameLessThan(String value) {
            addCriterion("confirmed_name <", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameLessThanOrEqualTo(String value) {
            addCriterion("confirmed_name <=", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameLike(String value) {
            addCriterion("confirmed_name like", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameNotLike(String value) {
            addCriterion("confirmed_name not like", value, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameIn(List<String> values) {
            addCriterion("confirmed_name in", values, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameNotIn(List<String> values) {
            addCriterion("confirmed_name not in", values, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameBetween(String value1, String value2) {
            addCriterion("confirmed_name between", value1, value2, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andConfirmedNameNotBetween(String value1, String value2) {
            addCriterion("confirmed_name not between", value1, value2, "confirmedName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}