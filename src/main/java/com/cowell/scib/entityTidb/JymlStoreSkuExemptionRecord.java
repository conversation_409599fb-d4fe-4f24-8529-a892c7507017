package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.cowell.scib.service.AmisDataInInterface;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-门店豁免商品记录
 */
@Data
public class JymlStoreSkuExemptionRecord implements AmisDataInInterface, Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 1：门店添加 2：规划导入 3：系统建议
     */
    private Byte source;

    /**
     * 连锁orgid
     */
    private Long businessOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 商品小类id
     */
    private String smallCategory;

    /**
     * 商品小类
     */
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    private String subCategory;

    /**
     * 商品子类
     */
    private String subCategoryName;

    /**
     * 规格
     */
    private String jhispecification;

    /**
     * 生产厂家
     */
    private String factoryid;

    /**
     * 单位
     */
    private String goodsunit;

    /**
     * 成分
     */
    private String component;

    /**
     * 商品综合贡献
     */
    private BigDecimal goodsContributeRate;

    /**
     * 近90天销售金额
     */
    private BigDecimal saleAmountQuarter;

    /**
     * 近180天销售金额
     */
    private BigDecimal saleAmountTowQuarter;

    /**
     * 近270天销售金额
     */
    private BigDecimal saleAmountThreeQuarter;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;
}
