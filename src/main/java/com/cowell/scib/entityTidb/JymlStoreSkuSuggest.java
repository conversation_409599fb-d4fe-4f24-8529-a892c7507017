package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 经营目录-一店一目经营建议表
 */
@Data
public class JymlStoreSkuSuggest implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 连锁orgid
     */
    private Long businessOrgId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 建议经营状态
     */
    private String suggestManageStatus;

    /**
     * 建议经营状态名称
     */
    private String suggestManageStatusName;

    /**
     * 商品大类id(11_rx/或11_otc/12)
     */
    private String category;

    /**
     * 商品大类
     */
    private String categoryName;

    /**
     * 商品大类是rx/otc
     */
    private String rxOtc;

    /**
     * 商品中类id
     */
    private String middleCategory;

    /**
     * 商品中类
     */
    private String middleCategoryName;

    /**
     * 商品小类id
     */
    private String smallCategory;

    /**
     * 商品小类
     */
    private String smallCategoryName;

    /**
     * 商品子类id
     */
    private String subCategory;

    /**
     * 商品子类
     */
    private String subCategoryName;

    /**
     * 成分
     */
    private String component;

    /**
     * 库存数量
     */
    private String stockQuantity;

    /**
     * 近30天销售数量
     */
    private String salesLast30d;

    /**
     * 近60天销售数量
     */
    private String salesLast60d;

    /**
     * 近90天销售数量
     */
    private String salesLast90d;

    /**
     * 近180天销售数量
     */
    private String salesLast180d;

    /**
     * 是否季节成分
     */
    private String seasonalComponent;

    /**
     * 去年同期后90天销售数量
     */
    private String salesLastYearSamePeriodLast90d;

    /**
     * 同城市近90天门店动销率
     */
    private String cityStoreSalesRate90d;

    /**
     * 同店型近90天门店动销率
     */
    private String storeTypeSalesRate90d;

    /**
     * 滞销天数
     */
    private String daysUnsold;

    /**
     * 城市内商品综合贡献排名
     */
    private String cityProductContributionRank;

    /**
     * 城市内同成分/子类综合贡献排名
     */
    private String citySubcategoryContributionRank;

    /**
     * 零售价
     */
    private String priceLsj;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;
}