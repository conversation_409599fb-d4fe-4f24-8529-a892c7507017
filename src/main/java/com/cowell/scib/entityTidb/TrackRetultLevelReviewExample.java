package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrackRetultLevelReviewExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TrackRetultLevelReviewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNull() {
            addCriterion("zone_new is null");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNotNull() {
            addCriterion("zone_new is not null");
            return (Criteria) this;
        }

        public Criteria andZoneNewEqualTo(String value) {
            addCriterion("zone_new =", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotEqualTo(String value) {
            addCriterion("zone_new <>", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThan(String value) {
            addCriterion("zone_new >", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThanOrEqualTo(String value) {
            addCriterion("zone_new >=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThan(String value) {
            addCriterion("zone_new <", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThanOrEqualTo(String value) {
            addCriterion("zone_new <=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLike(String value) {
            addCriterion("zone_new like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotLike(String value) {
            addCriterion("zone_new not like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewIn(List<String> values) {
            addCriterion("zone_new in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotIn(List<String> values) {
            addCriterion("zone_new not in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewBetween(String value1, String value2) {
            addCriterion("zone_new between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotBetween(String value1, String value2) {
            addCriterion("zone_new not between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andChainNameIsNull() {
            addCriterion("chain_name is null");
            return (Criteria) this;
        }

        public Criteria andChainNameIsNotNull() {
            addCriterion("chain_name is not null");
            return (Criteria) this;
        }

        public Criteria andChainNameEqualTo(String value) {
            addCriterion("chain_name =", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotEqualTo(String value) {
            addCriterion("chain_name <>", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameGreaterThan(String value) {
            addCriterion("chain_name >", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameGreaterThanOrEqualTo(String value) {
            addCriterion("chain_name >=", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLessThan(String value) {
            addCriterion("chain_name <", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLessThanOrEqualTo(String value) {
            addCriterion("chain_name <=", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameLike(String value) {
            addCriterion("chain_name like", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotLike(String value) {
            addCriterion("chain_name not like", value, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameIn(List<String> values) {
            addCriterion("chain_name in", values, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotIn(List<String> values) {
            addCriterion("chain_name not in", values, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameBetween(String value1, String value2) {
            addCriterion("chain_name between", value1, value2, "chainName");
            return (Criteria) this;
        }

        public Criteria andChainNameNotBetween(String value1, String value2) {
            addCriterion("chain_name not between", value1, value2, "chainName");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupIsNull() {
            addCriterion("revise_store_group is null");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupIsNotNull() {
            addCriterion("revise_store_group is not null");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupEqualTo(String value) {
            addCriterion("revise_store_group =", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotEqualTo(String value) {
            addCriterion("revise_store_group <>", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupGreaterThan(String value) {
            addCriterion("revise_store_group >", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupGreaterThanOrEqualTo(String value) {
            addCriterion("revise_store_group >=", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupLessThan(String value) {
            addCriterion("revise_store_group <", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupLessThanOrEqualTo(String value) {
            addCriterion("revise_store_group <=", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupLike(String value) {
            addCriterion("revise_store_group like", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotLike(String value) {
            addCriterion("revise_store_group not like", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupIn(List<String> values) {
            addCriterion("revise_store_group in", values, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotIn(List<String> values) {
            addCriterion("revise_store_group not in", values, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupBetween(String value1, String value2) {
            addCriterion("revise_store_group between", value1, value2, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotBetween(String value1, String value2) {
            addCriterion("revise_store_group not between", value1, value2, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNull() {
            addCriterion("classone_name is null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNotNull() {
            addCriterion("classone_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameEqualTo(String value) {
            addCriterion("classone_name =", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotEqualTo(String value) {
            addCriterion("classone_name <>", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThan(String value) {
            addCriterion("classone_name >", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("classone_name >=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThan(String value) {
            addCriterion("classone_name <", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThanOrEqualTo(String value) {
            addCriterion("classone_name <=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLike(String value) {
            addCriterion("classone_name like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotLike(String value) {
            addCriterion("classone_name not like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIn(List<String> values) {
            addCriterion("classone_name in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotIn(List<String> values) {
            addCriterion("classone_name not in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameBetween(String value1, String value2) {
            addCriterion("classone_name between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotBetween(String value1, String value2) {
            addCriterion("classone_name not between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3IsNull() {
            addCriterion("org_cnt_v3 is null");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3IsNotNull() {
            addCriterion("org_cnt_v3 is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3EqualTo(String value) {
            addCriterion("org_cnt_v3 =", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotEqualTo(String value) {
            addCriterion("org_cnt_v3 <>", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3GreaterThan(String value) {
            addCriterion("org_cnt_v3 >", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3GreaterThanOrEqualTo(String value) {
            addCriterion("org_cnt_v3 >=", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3LessThan(String value) {
            addCriterion("org_cnt_v3 <", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3LessThanOrEqualTo(String value) {
            addCriterion("org_cnt_v3 <=", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3Like(String value) {
            addCriterion("org_cnt_v3 like", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotLike(String value) {
            addCriterion("org_cnt_v3 not like", value, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3In(List<String> values) {
            addCriterion("org_cnt_v3 in", values, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotIn(List<String> values) {
            addCriterion("org_cnt_v3 not in", values, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3Between(String value1, String value2) {
            addCriterion("org_cnt_v3 between", value1, value2, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andOrgCntV3NotBetween(String value1, String value2) {
            addCriterion("org_cnt_v3 not between", value1, value2, "orgCntV3");
            return (Criteria) this;
        }

        public Criteria andSkuCntIsNull() {
            addCriterion("sku_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntIsNotNull() {
            addCriterion("sku_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntEqualTo(String value) {
            addCriterion("sku_cnt =", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotEqualTo(String value) {
            addCriterion("sku_cnt <>", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntGreaterThan(String value) {
            addCriterion("sku_cnt >", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt >=", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntLessThan(String value) {
            addCriterion("sku_cnt <", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt <=", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntLike(String value) {
            addCriterion("sku_cnt like", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotLike(String value) {
            addCriterion("sku_cnt not like", value, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntIn(List<String> values) {
            addCriterion("sku_cnt in", values, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotIn(List<String> values) {
            addCriterion("sku_cnt not in", values, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntBetween(String value1, String value2) {
            addCriterion("sku_cnt between", value1, value2, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntNotBetween(String value1, String value2) {
            addCriterion("sku_cnt not between", value1, value2, "skuCnt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtIsNull() {
            addCriterion("sku_cnt_jt is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtIsNotNull() {
            addCriterion("sku_cnt_jt is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtEqualTo(String value) {
            addCriterion("sku_cnt_jt =", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtNotEqualTo(String value) {
            addCriterion("sku_cnt_jt <>", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtGreaterThan(String value) {
            addCriterion("sku_cnt_jt >", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_jt >=", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtLessThan(String value) {
            addCriterion("sku_cnt_jt <", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_jt <=", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtLike(String value) {
            addCriterion("sku_cnt_jt like", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtNotLike(String value) {
            addCriterion("sku_cnt_jt not like", value, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtIn(List<String> values) {
            addCriterion("sku_cnt_jt in", values, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtNotIn(List<String> values) {
            addCriterion("sku_cnt_jt not in", values, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtBetween(String value1, String value2) {
            addCriterion("sku_cnt_jt between", value1, value2, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_jt not between", value1, value2, "skuCntJt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtIsNull() {
            addCriterion("sku_cnt_pt is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtIsNotNull() {
            addCriterion("sku_cnt_pt is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtEqualTo(String value) {
            addCriterion("sku_cnt_pt =", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtNotEqualTo(String value) {
            addCriterion("sku_cnt_pt <>", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtGreaterThan(String value) {
            addCriterion("sku_cnt_pt >", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_pt >=", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtLessThan(String value) {
            addCriterion("sku_cnt_pt <", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_pt <=", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtLike(String value) {
            addCriterion("sku_cnt_pt like", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtNotLike(String value) {
            addCriterion("sku_cnt_pt not like", value, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtIn(List<String> values) {
            addCriterion("sku_cnt_pt in", values, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtNotIn(List<String> values) {
            addCriterion("sku_cnt_pt not in", values, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtBetween(String value1, String value2) {
            addCriterion("sku_cnt_pt between", value1, value2, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntPtNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_pt not between", value1, value2, "skuCntPt");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyIsNull() {
            addCriterion("sku_cnt_qy is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyIsNotNull() {
            addCriterion("sku_cnt_qy is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyEqualTo(String value) {
            addCriterion("sku_cnt_qy =", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyNotEqualTo(String value) {
            addCriterion("sku_cnt_qy <>", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyGreaterThan(String value) {
            addCriterion("sku_cnt_qy >", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_qy >=", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyLessThan(String value) {
            addCriterion("sku_cnt_qy <", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_qy <=", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyLike(String value) {
            addCriterion("sku_cnt_qy like", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyNotLike(String value) {
            addCriterion("sku_cnt_qy not like", value, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyIn(List<String> values) {
            addCriterion("sku_cnt_qy in", values, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyNotIn(List<String> values) {
            addCriterion("sku_cnt_qy not in", values, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyBetween(String value1, String value2) {
            addCriterion("sku_cnt_qy between", value1, value2, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntQyNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_qy not between", value1, value2, "skuCntQy");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbIsNull() {
            addCriterion("sku_cnt_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbIsNotNull() {
            addCriterion("sku_cnt_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbEqualTo(String value) {
            addCriterion("sku_cnt_dxbb =", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbNotEqualTo(String value) {
            addCriterion("sku_cnt_dxbb <>", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbGreaterThan(String value) {
            addCriterion("sku_cnt_dxbb >", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_dxbb >=", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbLessThan(String value) {
            addCriterion("sku_cnt_dxbb <", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_dxbb <=", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbLike(String value) {
            addCriterion("sku_cnt_dxbb like", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbNotLike(String value) {
            addCriterion("sku_cnt_dxbb not like", value, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbIn(List<String> values) {
            addCriterion("sku_cnt_dxbb in", values, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbNotIn(List<String> values) {
            addCriterion("sku_cnt_dxbb not in", values, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbBetween(String value1, String value2) {
            addCriterion("sku_cnt_dxbb between", value1, value2, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxbbNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_dxbb not between", value1, value2, "skuCntDxbb");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpIsNull() {
            addCriterion("sku_cnt_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpIsNotNull() {
            addCriterion("sku_cnt_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpEqualTo(String value) {
            addCriterion("sku_cnt_dxxp =", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpNotEqualTo(String value) {
            addCriterion("sku_cnt_dxxp <>", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpGreaterThan(String value) {
            addCriterion("sku_cnt_dxxp >", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_dxxp >=", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpLessThan(String value) {
            addCriterion("sku_cnt_dxxp <", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_dxxp <=", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpLike(String value) {
            addCriterion("sku_cnt_dxxp like", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpNotLike(String value) {
            addCriterion("sku_cnt_dxxp not like", value, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpIn(List<String> values) {
            addCriterion("sku_cnt_dxxp in", values, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpNotIn(List<String> values) {
            addCriterion("sku_cnt_dxxp not in", values, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpBetween(String value1, String value2) {
            addCriterion("sku_cnt_dxxp between", value1, value2, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDxxpNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_dxxp not between", value1, value2, "skuCntDxxp");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdIsNull() {
            addCriterion("sku_cnt_dd is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdIsNotNull() {
            addCriterion("sku_cnt_dd is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdEqualTo(String value) {
            addCriterion("sku_cnt_dd =", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdNotEqualTo(String value) {
            addCriterion("sku_cnt_dd <>", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdGreaterThan(String value) {
            addCriterion("sku_cnt_dd >", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_dd >=", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdLessThan(String value) {
            addCriterion("sku_cnt_dd <", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_dd <=", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdLike(String value) {
            addCriterion("sku_cnt_dd like", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdNotLike(String value) {
            addCriterion("sku_cnt_dd not like", value, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdIn(List<String> values) {
            addCriterion("sku_cnt_dd in", values, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdNotIn(List<String> values) {
            addCriterion("sku_cnt_dd not in", values, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdBetween(String value1, String value2) {
            addCriterion("sku_cnt_dd between", value1, value2, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntDdNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_dd not between", value1, value2, "skuCntDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxIsNull() {
            addCriterion("sku_cnt_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxIsNotNull() {
            addCriterion("sku_cnt_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxEqualTo(String value) {
            addCriterion("sku_cnt_jt_qy_dx =", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxNotEqualTo(String value) {
            addCriterion("sku_cnt_jt_qy_dx <>", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxGreaterThan(String value) {
            addCriterion("sku_cnt_jt_qy_dx >", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_jt_qy_dx >=", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxLessThan(String value) {
            addCriterion("sku_cnt_jt_qy_dx <", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_jt_qy_dx <=", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxLike(String value) {
            addCriterion("sku_cnt_jt_qy_dx like", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxNotLike(String value) {
            addCriterion("sku_cnt_jt_qy_dx not like", value, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxIn(List<String> values) {
            addCriterion("sku_cnt_jt_qy_dx in", values, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxNotIn(List<String> values) {
            addCriterion("sku_cnt_jt_qy_dx not in", values, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxBetween(String value1, String value2) {
            addCriterion("sku_cnt_jt_qy_dx between", value1, value2, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntJtQyDxNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_jt_qy_dx not between", value1, value2, "skuCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdIsNull() {
            addCriterion("sku_cnt_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdIsNotNull() {
            addCriterion("sku_cnt_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdEqualTo(String value) {
            addCriterion("sku_cnt_no_dd =", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdNotEqualTo(String value) {
            addCriterion("sku_cnt_no_dd <>", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdGreaterThan(String value) {
            addCriterion("sku_cnt_no_dd >", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_no_dd >=", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdLessThan(String value) {
            addCriterion("sku_cnt_no_dd <", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_no_dd <=", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdLike(String value) {
            addCriterion("sku_cnt_no_dd like", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdNotLike(String value) {
            addCriterion("sku_cnt_no_dd not like", value, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdIn(List<String> values) {
            addCriterion("sku_cnt_no_dd in", values, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdNotIn(List<String> values) {
            addCriterion("sku_cnt_no_dd not in", values, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdBetween(String value1, String value2) {
            addCriterion("sku_cnt_no_dd between", value1, value2, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntNoDdNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_no_dd not between", value1, value2, "skuCntNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzIsNull() {
            addCriterion("sku_cnt_xz is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzIsNotNull() {
            addCriterion("sku_cnt_xz is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzEqualTo(String value) {
            addCriterion("sku_cnt_xz =", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzNotEqualTo(String value) {
            addCriterion("sku_cnt_xz <>", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzGreaterThan(String value) {
            addCriterion("sku_cnt_xz >", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_xz >=", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzLessThan(String value) {
            addCriterion("sku_cnt_xz <", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_xz <=", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzLike(String value) {
            addCriterion("sku_cnt_xz like", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzNotLike(String value) {
            addCriterion("sku_cnt_xz not like", value, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzIn(List<String> values) {
            addCriterion("sku_cnt_xz in", values, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzNotIn(List<String> values) {
            addCriterion("sku_cnt_xz not in", values, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzBetween(String value1, String value2) {
            addCriterion("sku_cnt_xz between", value1, value2, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntXzNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_xz not between", value1, value2, "skuCntXz");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsIsNull() {
            addCriterion("sku_cnt_js is null");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsIsNotNull() {
            addCriterion("sku_cnt_js is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsEqualTo(String value) {
            addCriterion("sku_cnt_js =", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsNotEqualTo(String value) {
            addCriterion("sku_cnt_js <>", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsGreaterThan(String value) {
            addCriterion("sku_cnt_js >", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsGreaterThanOrEqualTo(String value) {
            addCriterion("sku_cnt_js >=", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsLessThan(String value) {
            addCriterion("sku_cnt_js <", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsLessThanOrEqualTo(String value) {
            addCriterion("sku_cnt_js <=", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsLike(String value) {
            addCriterion("sku_cnt_js like", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsNotLike(String value) {
            addCriterion("sku_cnt_js not like", value, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsIn(List<String> values) {
            addCriterion("sku_cnt_js in", values, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsNotIn(List<String> values) {
            addCriterion("sku_cnt_js not in", values, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsBetween(String value1, String value2) {
            addCriterion("sku_cnt_js between", value1, value2, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuCntJsNotBetween(String value1, String value2) {
            addCriterion("sku_cnt_js not between", value1, value2, "skuCntJs");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4IsNull() {
            addCriterion("sku_rate_top4 is null");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4IsNotNull() {
            addCriterion("sku_rate_top4 is not null");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4EqualTo(String value) {
            addCriterion("sku_rate_top4 =", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4NotEqualTo(String value) {
            addCriterion("sku_rate_top4 <>", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4GreaterThan(String value) {
            addCriterion("sku_rate_top4 >", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4GreaterThanOrEqualTo(String value) {
            addCriterion("sku_rate_top4 >=", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4LessThan(String value) {
            addCriterion("sku_rate_top4 <", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4LessThanOrEqualTo(String value) {
            addCriterion("sku_rate_top4 <=", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4Like(String value) {
            addCriterion("sku_rate_top4 like", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4NotLike(String value) {
            addCriterion("sku_rate_top4 not like", value, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4In(List<String> values) {
            addCriterion("sku_rate_top4 in", values, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4NotIn(List<String> values) {
            addCriterion("sku_rate_top4 not in", values, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4Between(String value1, String value2) {
            addCriterion("sku_rate_top4 between", value1, value2, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateTop4NotBetween(String value1, String value2) {
            addCriterion("sku_rate_top4 not between", value1, value2, "skuRateTop4");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdIsNull() {
            addCriterion("sku_rate_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdIsNotNull() {
            addCriterion("sku_rate_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdEqualTo(String value) {
            addCriterion("sku_rate_no_dd =", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdNotEqualTo(String value) {
            addCriterion("sku_rate_no_dd <>", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdGreaterThan(String value) {
            addCriterion("sku_rate_no_dd >", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_rate_no_dd >=", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdLessThan(String value) {
            addCriterion("sku_rate_no_dd <", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdLessThanOrEqualTo(String value) {
            addCriterion("sku_rate_no_dd <=", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdLike(String value) {
            addCriterion("sku_rate_no_dd like", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdNotLike(String value) {
            addCriterion("sku_rate_no_dd not like", value, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdIn(List<String> values) {
            addCriterion("sku_rate_no_dd in", values, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdNotIn(List<String> values) {
            addCriterion("sku_rate_no_dd not in", values, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdBetween(String value1, String value2) {
            addCriterion("sku_rate_no_dd between", value1, value2, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andSkuRateNoDdNotBetween(String value1, String value2) {
            addCriterion("sku_rate_no_dd not between", value1, value2, "skuRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntIsNull() {
            addCriterion("comp_cnt is null");
            return (Criteria) this;
        }

        public Criteria andCompCntIsNotNull() {
            addCriterion("comp_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntEqualTo(String value) {
            addCriterion("comp_cnt =", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntNotEqualTo(String value) {
            addCriterion("comp_cnt <>", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntGreaterThan(String value) {
            addCriterion("comp_cnt >", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt >=", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntLessThan(String value) {
            addCriterion("comp_cnt <", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt <=", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntLike(String value) {
            addCriterion("comp_cnt like", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntNotLike(String value) {
            addCriterion("comp_cnt not like", value, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntIn(List<String> values) {
            addCriterion("comp_cnt in", values, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntNotIn(List<String> values) {
            addCriterion("comp_cnt not in", values, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntBetween(String value1, String value2) {
            addCriterion("comp_cnt between", value1, value2, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntNotBetween(String value1, String value2) {
            addCriterion("comp_cnt not between", value1, value2, "compCnt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtIsNull() {
            addCriterion("comp_cnt_jt is null");
            return (Criteria) this;
        }

        public Criteria andCompCntJtIsNotNull() {
            addCriterion("comp_cnt_jt is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntJtEqualTo(String value) {
            addCriterion("comp_cnt_jt =", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtNotEqualTo(String value) {
            addCriterion("comp_cnt_jt <>", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtGreaterThan(String value) {
            addCriterion("comp_cnt_jt >", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_jt >=", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtLessThan(String value) {
            addCriterion("comp_cnt_jt <", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_jt <=", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtLike(String value) {
            addCriterion("comp_cnt_jt like", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtNotLike(String value) {
            addCriterion("comp_cnt_jt not like", value, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtIn(List<String> values) {
            addCriterion("comp_cnt_jt in", values, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtNotIn(List<String> values) {
            addCriterion("comp_cnt_jt not in", values, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtBetween(String value1, String value2) {
            addCriterion("comp_cnt_jt between", value1, value2, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntJtNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_jt not between", value1, value2, "compCntJt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtIsNull() {
            addCriterion("comp_cnt_pt is null");
            return (Criteria) this;
        }

        public Criteria andCompCntPtIsNotNull() {
            addCriterion("comp_cnt_pt is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntPtEqualTo(String value) {
            addCriterion("comp_cnt_pt =", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtNotEqualTo(String value) {
            addCriterion("comp_cnt_pt <>", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtGreaterThan(String value) {
            addCriterion("comp_cnt_pt >", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_pt >=", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtLessThan(String value) {
            addCriterion("comp_cnt_pt <", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_pt <=", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtLike(String value) {
            addCriterion("comp_cnt_pt like", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtNotLike(String value) {
            addCriterion("comp_cnt_pt not like", value, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtIn(List<String> values) {
            addCriterion("comp_cnt_pt in", values, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtNotIn(List<String> values) {
            addCriterion("comp_cnt_pt not in", values, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtBetween(String value1, String value2) {
            addCriterion("comp_cnt_pt between", value1, value2, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntPtNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_pt not between", value1, value2, "compCntPt");
            return (Criteria) this;
        }

        public Criteria andCompCntQyIsNull() {
            addCriterion("comp_cnt_qy is null");
            return (Criteria) this;
        }

        public Criteria andCompCntQyIsNotNull() {
            addCriterion("comp_cnt_qy is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntQyEqualTo(String value) {
            addCriterion("comp_cnt_qy =", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyNotEqualTo(String value) {
            addCriterion("comp_cnt_qy <>", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyGreaterThan(String value) {
            addCriterion("comp_cnt_qy >", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_qy >=", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyLessThan(String value) {
            addCriterion("comp_cnt_qy <", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_qy <=", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyLike(String value) {
            addCriterion("comp_cnt_qy like", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyNotLike(String value) {
            addCriterion("comp_cnt_qy not like", value, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyIn(List<String> values) {
            addCriterion("comp_cnt_qy in", values, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyNotIn(List<String> values) {
            addCriterion("comp_cnt_qy not in", values, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyBetween(String value1, String value2) {
            addCriterion("comp_cnt_qy between", value1, value2, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntQyNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_qy not between", value1, value2, "compCntQy");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbIsNull() {
            addCriterion("comp_cnt_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbIsNotNull() {
            addCriterion("comp_cnt_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbEqualTo(String value) {
            addCriterion("comp_cnt_dxbb =", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbNotEqualTo(String value) {
            addCriterion("comp_cnt_dxbb <>", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbGreaterThan(String value) {
            addCriterion("comp_cnt_dxbb >", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_dxbb >=", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbLessThan(String value) {
            addCriterion("comp_cnt_dxbb <", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_dxbb <=", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbLike(String value) {
            addCriterion("comp_cnt_dxbb like", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbNotLike(String value) {
            addCriterion("comp_cnt_dxbb not like", value, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbIn(List<String> values) {
            addCriterion("comp_cnt_dxbb in", values, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbNotIn(List<String> values) {
            addCriterion("comp_cnt_dxbb not in", values, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbBetween(String value1, String value2) {
            addCriterion("comp_cnt_dxbb between", value1, value2, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxbbNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_dxbb not between", value1, value2, "compCntDxbb");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpIsNull() {
            addCriterion("comp_cnt_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpIsNotNull() {
            addCriterion("comp_cnt_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpEqualTo(String value) {
            addCriterion("comp_cnt_dxxp =", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpNotEqualTo(String value) {
            addCriterion("comp_cnt_dxxp <>", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpGreaterThan(String value) {
            addCriterion("comp_cnt_dxxp >", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_dxxp >=", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpLessThan(String value) {
            addCriterion("comp_cnt_dxxp <", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_dxxp <=", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpLike(String value) {
            addCriterion("comp_cnt_dxxp like", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpNotLike(String value) {
            addCriterion("comp_cnt_dxxp not like", value, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpIn(List<String> values) {
            addCriterion("comp_cnt_dxxp in", values, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpNotIn(List<String> values) {
            addCriterion("comp_cnt_dxxp not in", values, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpBetween(String value1, String value2) {
            addCriterion("comp_cnt_dxxp between", value1, value2, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDxxpNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_dxxp not between", value1, value2, "compCntDxxp");
            return (Criteria) this;
        }

        public Criteria andCompCntDdIsNull() {
            addCriterion("comp_cnt_dd is null");
            return (Criteria) this;
        }

        public Criteria andCompCntDdIsNotNull() {
            addCriterion("comp_cnt_dd is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntDdEqualTo(String value) {
            addCriterion("comp_cnt_dd =", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdNotEqualTo(String value) {
            addCriterion("comp_cnt_dd <>", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdGreaterThan(String value) {
            addCriterion("comp_cnt_dd >", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_dd >=", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdLessThan(String value) {
            addCriterion("comp_cnt_dd <", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_dd <=", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdLike(String value) {
            addCriterion("comp_cnt_dd like", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdNotLike(String value) {
            addCriterion("comp_cnt_dd not like", value, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdIn(List<String> values) {
            addCriterion("comp_cnt_dd in", values, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdNotIn(List<String> values) {
            addCriterion("comp_cnt_dd not in", values, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdBetween(String value1, String value2) {
            addCriterion("comp_cnt_dd between", value1, value2, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntDdNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_dd not between", value1, value2, "compCntDd");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxIsNull() {
            addCriterion("comp_cnt_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxIsNotNull() {
            addCriterion("comp_cnt_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxEqualTo(String value) {
            addCriterion("comp_cnt_jt_qy_dx =", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxNotEqualTo(String value) {
            addCriterion("comp_cnt_jt_qy_dx <>", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxGreaterThan(String value) {
            addCriterion("comp_cnt_jt_qy_dx >", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_jt_qy_dx >=", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxLessThan(String value) {
            addCriterion("comp_cnt_jt_qy_dx <", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_jt_qy_dx <=", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxLike(String value) {
            addCriterion("comp_cnt_jt_qy_dx like", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxNotLike(String value) {
            addCriterion("comp_cnt_jt_qy_dx not like", value, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxIn(List<String> values) {
            addCriterion("comp_cnt_jt_qy_dx in", values, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxNotIn(List<String> values) {
            addCriterion("comp_cnt_jt_qy_dx not in", values, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxBetween(String value1, String value2) {
            addCriterion("comp_cnt_jt_qy_dx between", value1, value2, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntJtQyDxNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_jt_qy_dx not between", value1, value2, "compCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdIsNull() {
            addCriterion("comp_cnt_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdIsNotNull() {
            addCriterion("comp_cnt_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdEqualTo(String value) {
            addCriterion("comp_cnt_no_dd =", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdNotEqualTo(String value) {
            addCriterion("comp_cnt_no_dd <>", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdGreaterThan(String value) {
            addCriterion("comp_cnt_no_dd >", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_no_dd >=", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdLessThan(String value) {
            addCriterion("comp_cnt_no_dd <", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_no_dd <=", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdLike(String value) {
            addCriterion("comp_cnt_no_dd like", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdNotLike(String value) {
            addCriterion("comp_cnt_no_dd not like", value, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdIn(List<String> values) {
            addCriterion("comp_cnt_no_dd in", values, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdNotIn(List<String> values) {
            addCriterion("comp_cnt_no_dd not in", values, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdBetween(String value1, String value2) {
            addCriterion("comp_cnt_no_dd between", value1, value2, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntNoDdNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_no_dd not between", value1, value2, "compCntNoDd");
            return (Criteria) this;
        }

        public Criteria andCompCntXzIsNull() {
            addCriterion("comp_cnt_xz is null");
            return (Criteria) this;
        }

        public Criteria andCompCntXzIsNotNull() {
            addCriterion("comp_cnt_xz is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntXzEqualTo(String value) {
            addCriterion("comp_cnt_xz =", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzNotEqualTo(String value) {
            addCriterion("comp_cnt_xz <>", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzGreaterThan(String value) {
            addCriterion("comp_cnt_xz >", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_xz >=", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzLessThan(String value) {
            addCriterion("comp_cnt_xz <", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_xz <=", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzLike(String value) {
            addCriterion("comp_cnt_xz like", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzNotLike(String value) {
            addCriterion("comp_cnt_xz not like", value, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzIn(List<String> values) {
            addCriterion("comp_cnt_xz in", values, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzNotIn(List<String> values) {
            addCriterion("comp_cnt_xz not in", values, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzBetween(String value1, String value2) {
            addCriterion("comp_cnt_xz between", value1, value2, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntXzNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_xz not between", value1, value2, "compCntXz");
            return (Criteria) this;
        }

        public Criteria andCompCntJsIsNull() {
            addCriterion("comp_cnt_js is null");
            return (Criteria) this;
        }

        public Criteria andCompCntJsIsNotNull() {
            addCriterion("comp_cnt_js is not null");
            return (Criteria) this;
        }

        public Criteria andCompCntJsEqualTo(String value) {
            addCriterion("comp_cnt_js =", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsNotEqualTo(String value) {
            addCriterion("comp_cnt_js <>", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsGreaterThan(String value) {
            addCriterion("comp_cnt_js >", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsGreaterThanOrEqualTo(String value) {
            addCriterion("comp_cnt_js >=", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsLessThan(String value) {
            addCriterion("comp_cnt_js <", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsLessThanOrEqualTo(String value) {
            addCriterion("comp_cnt_js <=", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsLike(String value) {
            addCriterion("comp_cnt_js like", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsNotLike(String value) {
            addCriterion("comp_cnt_js not like", value, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsIn(List<String> values) {
            addCriterion("comp_cnt_js in", values, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsNotIn(List<String> values) {
            addCriterion("comp_cnt_js not in", values, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsBetween(String value1, String value2) {
            addCriterion("comp_cnt_js between", value1, value2, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompCntJsNotBetween(String value1, String value2) {
            addCriterion("comp_cnt_js not between", value1, value2, "compCntJs");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4IsNull() {
            addCriterion("comp_rate_top4 is null");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4IsNotNull() {
            addCriterion("comp_rate_top4 is not null");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4EqualTo(String value) {
            addCriterion("comp_rate_top4 =", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4NotEqualTo(String value) {
            addCriterion("comp_rate_top4 <>", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4GreaterThan(String value) {
            addCriterion("comp_rate_top4 >", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4GreaterThanOrEqualTo(String value) {
            addCriterion("comp_rate_top4 >=", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4LessThan(String value) {
            addCriterion("comp_rate_top4 <", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4LessThanOrEqualTo(String value) {
            addCriterion("comp_rate_top4 <=", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4Like(String value) {
            addCriterion("comp_rate_top4 like", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4NotLike(String value) {
            addCriterion("comp_rate_top4 not like", value, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4In(List<String> values) {
            addCriterion("comp_rate_top4 in", values, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4NotIn(List<String> values) {
            addCriterion("comp_rate_top4 not in", values, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4Between(String value1, String value2) {
            addCriterion("comp_rate_top4 between", value1, value2, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateTop4NotBetween(String value1, String value2) {
            addCriterion("comp_rate_top4 not between", value1, value2, "compRateTop4");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdIsNull() {
            addCriterion("comp_rate_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdIsNotNull() {
            addCriterion("comp_rate_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdEqualTo(String value) {
            addCriterion("comp_rate_no_dd =", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdNotEqualTo(String value) {
            addCriterion("comp_rate_no_dd <>", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdGreaterThan(String value) {
            addCriterion("comp_rate_no_dd >", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("comp_rate_no_dd >=", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdLessThan(String value) {
            addCriterion("comp_rate_no_dd <", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdLessThanOrEqualTo(String value) {
            addCriterion("comp_rate_no_dd <=", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdLike(String value) {
            addCriterion("comp_rate_no_dd like", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdNotLike(String value) {
            addCriterion("comp_rate_no_dd not like", value, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdIn(List<String> values) {
            addCriterion("comp_rate_no_dd in", values, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdNotIn(List<String> values) {
            addCriterion("comp_rate_no_dd not in", values, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdBetween(String value1, String value2) {
            addCriterion("comp_rate_no_dd between", value1, value2, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andCompRateNoDdNotBetween(String value1, String value2) {
            addCriterion("comp_rate_no_dd not between", value1, value2, "compRateNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntIsNull() {
            addCriterion("class_cnt is null");
            return (Criteria) this;
        }

        public Criteria andClassCntIsNotNull() {
            addCriterion("class_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntEqualTo(String value) {
            addCriterion("class_cnt =", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntNotEqualTo(String value) {
            addCriterion("class_cnt <>", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntGreaterThan(String value) {
            addCriterion("class_cnt >", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt >=", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntLessThan(String value) {
            addCriterion("class_cnt <", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntLessThanOrEqualTo(String value) {
            addCriterion("class_cnt <=", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntLike(String value) {
            addCriterion("class_cnt like", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntNotLike(String value) {
            addCriterion("class_cnt not like", value, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntIn(List<String> values) {
            addCriterion("class_cnt in", values, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntNotIn(List<String> values) {
            addCriterion("class_cnt not in", values, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntBetween(String value1, String value2) {
            addCriterion("class_cnt between", value1, value2, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntNotBetween(String value1, String value2) {
            addCriterion("class_cnt not between", value1, value2, "classCnt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtIsNull() {
            addCriterion("class_cnt_jt is null");
            return (Criteria) this;
        }

        public Criteria andClassCntJtIsNotNull() {
            addCriterion("class_cnt_jt is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntJtEqualTo(String value) {
            addCriterion("class_cnt_jt =", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtNotEqualTo(String value) {
            addCriterion("class_cnt_jt <>", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtGreaterThan(String value) {
            addCriterion("class_cnt_jt >", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_jt >=", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtLessThan(String value) {
            addCriterion("class_cnt_jt <", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_jt <=", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtLike(String value) {
            addCriterion("class_cnt_jt like", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtNotLike(String value) {
            addCriterion("class_cnt_jt not like", value, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtIn(List<String> values) {
            addCriterion("class_cnt_jt in", values, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtNotIn(List<String> values) {
            addCriterion("class_cnt_jt not in", values, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtBetween(String value1, String value2) {
            addCriterion("class_cnt_jt between", value1, value2, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntJtNotBetween(String value1, String value2) {
            addCriterion("class_cnt_jt not between", value1, value2, "classCntJt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtIsNull() {
            addCriterion("class_cnt_pt is null");
            return (Criteria) this;
        }

        public Criteria andClassCntPtIsNotNull() {
            addCriterion("class_cnt_pt is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntPtEqualTo(String value) {
            addCriterion("class_cnt_pt =", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtNotEqualTo(String value) {
            addCriterion("class_cnt_pt <>", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtGreaterThan(String value) {
            addCriterion("class_cnt_pt >", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_pt >=", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtLessThan(String value) {
            addCriterion("class_cnt_pt <", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_pt <=", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtLike(String value) {
            addCriterion("class_cnt_pt like", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtNotLike(String value) {
            addCriterion("class_cnt_pt not like", value, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtIn(List<String> values) {
            addCriterion("class_cnt_pt in", values, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtNotIn(List<String> values) {
            addCriterion("class_cnt_pt not in", values, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtBetween(String value1, String value2) {
            addCriterion("class_cnt_pt between", value1, value2, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntPtNotBetween(String value1, String value2) {
            addCriterion("class_cnt_pt not between", value1, value2, "classCntPt");
            return (Criteria) this;
        }

        public Criteria andClassCntQyIsNull() {
            addCriterion("class_cnt_qy is null");
            return (Criteria) this;
        }

        public Criteria andClassCntQyIsNotNull() {
            addCriterion("class_cnt_qy is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntQyEqualTo(String value) {
            addCriterion("class_cnt_qy =", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyNotEqualTo(String value) {
            addCriterion("class_cnt_qy <>", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyGreaterThan(String value) {
            addCriterion("class_cnt_qy >", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_qy >=", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyLessThan(String value) {
            addCriterion("class_cnt_qy <", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_qy <=", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyLike(String value) {
            addCriterion("class_cnt_qy like", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyNotLike(String value) {
            addCriterion("class_cnt_qy not like", value, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyIn(List<String> values) {
            addCriterion("class_cnt_qy in", values, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyNotIn(List<String> values) {
            addCriterion("class_cnt_qy not in", values, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyBetween(String value1, String value2) {
            addCriterion("class_cnt_qy between", value1, value2, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntQyNotBetween(String value1, String value2) {
            addCriterion("class_cnt_qy not between", value1, value2, "classCntQy");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbIsNull() {
            addCriterion("class_cnt_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbIsNotNull() {
            addCriterion("class_cnt_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbEqualTo(String value) {
            addCriterion("class_cnt_dxbb =", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbNotEqualTo(String value) {
            addCriterion("class_cnt_dxbb <>", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbGreaterThan(String value) {
            addCriterion("class_cnt_dxbb >", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_dxbb >=", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbLessThan(String value) {
            addCriterion("class_cnt_dxbb <", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_dxbb <=", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbLike(String value) {
            addCriterion("class_cnt_dxbb like", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbNotLike(String value) {
            addCriterion("class_cnt_dxbb not like", value, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbIn(List<String> values) {
            addCriterion("class_cnt_dxbb in", values, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbNotIn(List<String> values) {
            addCriterion("class_cnt_dxbb not in", values, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbBetween(String value1, String value2) {
            addCriterion("class_cnt_dxbb between", value1, value2, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxbbNotBetween(String value1, String value2) {
            addCriterion("class_cnt_dxbb not between", value1, value2, "classCntDxbb");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpIsNull() {
            addCriterion("class_cnt_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpIsNotNull() {
            addCriterion("class_cnt_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpEqualTo(String value) {
            addCriterion("class_cnt_dxxp =", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpNotEqualTo(String value) {
            addCriterion("class_cnt_dxxp <>", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpGreaterThan(String value) {
            addCriterion("class_cnt_dxxp >", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_dxxp >=", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpLessThan(String value) {
            addCriterion("class_cnt_dxxp <", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_dxxp <=", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpLike(String value) {
            addCriterion("class_cnt_dxxp like", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpNotLike(String value) {
            addCriterion("class_cnt_dxxp not like", value, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpIn(List<String> values) {
            addCriterion("class_cnt_dxxp in", values, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpNotIn(List<String> values) {
            addCriterion("class_cnt_dxxp not in", values, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpBetween(String value1, String value2) {
            addCriterion("class_cnt_dxxp between", value1, value2, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDxxpNotBetween(String value1, String value2) {
            addCriterion("class_cnt_dxxp not between", value1, value2, "classCntDxxp");
            return (Criteria) this;
        }

        public Criteria andClassCntDdIsNull() {
            addCriterion("class_cnt_dd is null");
            return (Criteria) this;
        }

        public Criteria andClassCntDdIsNotNull() {
            addCriterion("class_cnt_dd is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntDdEqualTo(String value) {
            addCriterion("class_cnt_dd =", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdNotEqualTo(String value) {
            addCriterion("class_cnt_dd <>", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdGreaterThan(String value) {
            addCriterion("class_cnt_dd >", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_dd >=", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdLessThan(String value) {
            addCriterion("class_cnt_dd <", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_dd <=", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdLike(String value) {
            addCriterion("class_cnt_dd like", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdNotLike(String value) {
            addCriterion("class_cnt_dd not like", value, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdIn(List<String> values) {
            addCriterion("class_cnt_dd in", values, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdNotIn(List<String> values) {
            addCriterion("class_cnt_dd not in", values, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdBetween(String value1, String value2) {
            addCriterion("class_cnt_dd between", value1, value2, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntDdNotBetween(String value1, String value2) {
            addCriterion("class_cnt_dd not between", value1, value2, "classCntDd");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxIsNull() {
            addCriterion("class_cnt_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxIsNotNull() {
            addCriterion("class_cnt_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxEqualTo(String value) {
            addCriterion("class_cnt_jt_qy_dx =", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxNotEqualTo(String value) {
            addCriterion("class_cnt_jt_qy_dx <>", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxGreaterThan(String value) {
            addCriterion("class_cnt_jt_qy_dx >", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_jt_qy_dx >=", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxLessThan(String value) {
            addCriterion("class_cnt_jt_qy_dx <", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_jt_qy_dx <=", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxLike(String value) {
            addCriterion("class_cnt_jt_qy_dx like", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxNotLike(String value) {
            addCriterion("class_cnt_jt_qy_dx not like", value, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxIn(List<String> values) {
            addCriterion("class_cnt_jt_qy_dx in", values, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxNotIn(List<String> values) {
            addCriterion("class_cnt_jt_qy_dx not in", values, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxBetween(String value1, String value2) {
            addCriterion("class_cnt_jt_qy_dx between", value1, value2, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntJtQyDxNotBetween(String value1, String value2) {
            addCriterion("class_cnt_jt_qy_dx not between", value1, value2, "classCntJtQyDx");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdIsNull() {
            addCriterion("class_cnt_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdIsNotNull() {
            addCriterion("class_cnt_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdEqualTo(String value) {
            addCriterion("class_cnt_no_dd =", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdNotEqualTo(String value) {
            addCriterion("class_cnt_no_dd <>", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdGreaterThan(String value) {
            addCriterion("class_cnt_no_dd >", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_no_dd >=", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdLessThan(String value) {
            addCriterion("class_cnt_no_dd <", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_no_dd <=", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdLike(String value) {
            addCriterion("class_cnt_no_dd like", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdNotLike(String value) {
            addCriterion("class_cnt_no_dd not like", value, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdIn(List<String> values) {
            addCriterion("class_cnt_no_dd in", values, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdNotIn(List<String> values) {
            addCriterion("class_cnt_no_dd not in", values, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdBetween(String value1, String value2) {
            addCriterion("class_cnt_no_dd between", value1, value2, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntNoDdNotBetween(String value1, String value2) {
            addCriterion("class_cnt_no_dd not between", value1, value2, "classCntNoDd");
            return (Criteria) this;
        }

        public Criteria andClassCntXzIsNull() {
            addCriterion("class_cnt_xz is null");
            return (Criteria) this;
        }

        public Criteria andClassCntXzIsNotNull() {
            addCriterion("class_cnt_xz is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntXzEqualTo(String value) {
            addCriterion("class_cnt_xz =", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzNotEqualTo(String value) {
            addCriterion("class_cnt_xz <>", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzGreaterThan(String value) {
            addCriterion("class_cnt_xz >", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_xz >=", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzLessThan(String value) {
            addCriterion("class_cnt_xz <", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_xz <=", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzLike(String value) {
            addCriterion("class_cnt_xz like", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzNotLike(String value) {
            addCriterion("class_cnt_xz not like", value, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzIn(List<String> values) {
            addCriterion("class_cnt_xz in", values, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzNotIn(List<String> values) {
            addCriterion("class_cnt_xz not in", values, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzBetween(String value1, String value2) {
            addCriterion("class_cnt_xz between", value1, value2, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntXzNotBetween(String value1, String value2) {
            addCriterion("class_cnt_xz not between", value1, value2, "classCntXz");
            return (Criteria) this;
        }

        public Criteria andClassCntJsIsNull() {
            addCriterion("class_cnt_js is null");
            return (Criteria) this;
        }

        public Criteria andClassCntJsIsNotNull() {
            addCriterion("class_cnt_js is not null");
            return (Criteria) this;
        }

        public Criteria andClassCntJsEqualTo(String value) {
            addCriterion("class_cnt_js =", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsNotEqualTo(String value) {
            addCriterion("class_cnt_js <>", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsGreaterThan(String value) {
            addCriterion("class_cnt_js >", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsGreaterThanOrEqualTo(String value) {
            addCriterion("class_cnt_js >=", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsLessThan(String value) {
            addCriterion("class_cnt_js <", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsLessThanOrEqualTo(String value) {
            addCriterion("class_cnt_js <=", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsLike(String value) {
            addCriterion("class_cnt_js like", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsNotLike(String value) {
            addCriterion("class_cnt_js not like", value, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsIn(List<String> values) {
            addCriterion("class_cnt_js in", values, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsNotIn(List<String> values) {
            addCriterion("class_cnt_js not in", values, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsBetween(String value1, String value2) {
            addCriterion("class_cnt_js between", value1, value2, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andClassCntJsNotBetween(String value1, String value2) {
            addCriterion("class_cnt_js not between", value1, value2, "classCntJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30IsNull() {
            addCriterion("amt_cum_30 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30IsNotNull() {
            addCriterion("amt_cum_30 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30EqualTo(String value) {
            addCriterion("amt_cum_30 =", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NotEqualTo(String value) {
            addCriterion("amt_cum_30 <>", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30GreaterThan(String value) {
            addCriterion("amt_cum_30 >", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30 >=", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30LessThan(String value) {
            addCriterion("amt_cum_30 <", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30 <=", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30Like(String value) {
            addCriterion("amt_cum_30 like", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NotLike(String value) {
            addCriterion("amt_cum_30 not like", value, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30In(List<String> values) {
            addCriterion("amt_cum_30 in", values, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NotIn(List<String> values) {
            addCriterion("amt_cum_30 not in", values, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30Between(String value1, String value2) {
            addCriterion("amt_cum_30 between", value1, value2, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NotBetween(String value1, String value2) {
            addCriterion("amt_cum_30 not between", value1, value2, "amtCum30");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtIsNull() {
            addCriterion("amt_cum_30_jt is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtIsNotNull() {
            addCriterion("amt_cum_30_jt is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtEqualTo(String value) {
            addCriterion("amt_cum_30_jt =", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtNotEqualTo(String value) {
            addCriterion("amt_cum_30_jt <>", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtGreaterThan(String value) {
            addCriterion("amt_cum_30_jt >", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_jt >=", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtLessThan(String value) {
            addCriterion("amt_cum_30_jt <", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_jt <=", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtLike(String value) {
            addCriterion("amt_cum_30_jt like", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtNotLike(String value) {
            addCriterion("amt_cum_30_jt not like", value, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtIn(List<String> values) {
            addCriterion("amt_cum_30_jt in", values, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtNotIn(List<String> values) {
            addCriterion("amt_cum_30_jt not in", values, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtBetween(String value1, String value2) {
            addCriterion("amt_cum_30_jt between", value1, value2, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_jt not between", value1, value2, "amtCum30Jt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtIsNull() {
            addCriterion("amt_cum_30_pt is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtIsNotNull() {
            addCriterion("amt_cum_30_pt is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtEqualTo(String value) {
            addCriterion("amt_cum_30_pt =", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtNotEqualTo(String value) {
            addCriterion("amt_cum_30_pt <>", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtGreaterThan(String value) {
            addCriterion("amt_cum_30_pt >", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_pt >=", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtLessThan(String value) {
            addCriterion("amt_cum_30_pt <", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_pt <=", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtLike(String value) {
            addCriterion("amt_cum_30_pt like", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtNotLike(String value) {
            addCriterion("amt_cum_30_pt not like", value, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtIn(List<String> values) {
            addCriterion("amt_cum_30_pt in", values, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtNotIn(List<String> values) {
            addCriterion("amt_cum_30_pt not in", values, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtBetween(String value1, String value2) {
            addCriterion("amt_cum_30_pt between", value1, value2, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30PtNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_pt not between", value1, value2, "amtCum30Pt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyIsNull() {
            addCriterion("amt_cum_30_qy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyIsNotNull() {
            addCriterion("amt_cum_30_qy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyEqualTo(String value) {
            addCriterion("amt_cum_30_qy =", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyNotEqualTo(String value) {
            addCriterion("amt_cum_30_qy <>", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyGreaterThan(String value) {
            addCriterion("amt_cum_30_qy >", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_qy >=", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyLessThan(String value) {
            addCriterion("amt_cum_30_qy <", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_qy <=", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyLike(String value) {
            addCriterion("amt_cum_30_qy like", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyNotLike(String value) {
            addCriterion("amt_cum_30_qy not like", value, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyIn(List<String> values) {
            addCriterion("amt_cum_30_qy in", values, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyNotIn(List<String> values) {
            addCriterion("amt_cum_30_qy not in", values, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_qy between", value1, value2, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30QyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_qy not between", value1, value2, "amtCum30Qy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbIsNull() {
            addCriterion("amt_cum_30_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbIsNotNull() {
            addCriterion("amt_cum_30_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbEqualTo(String value) {
            addCriterion("amt_cum_30_dxbb =", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbNotEqualTo(String value) {
            addCriterion("amt_cum_30_dxbb <>", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbGreaterThan(String value) {
            addCriterion("amt_cum_30_dxbb >", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_dxbb >=", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbLessThan(String value) {
            addCriterion("amt_cum_30_dxbb <", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_dxbb <=", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbLike(String value) {
            addCriterion("amt_cum_30_dxbb like", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbNotLike(String value) {
            addCriterion("amt_cum_30_dxbb not like", value, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbIn(List<String> values) {
            addCriterion("amt_cum_30_dxbb in", values, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbNotIn(List<String> values) {
            addCriterion("amt_cum_30_dxbb not in", values, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbBetween(String value1, String value2) {
            addCriterion("amt_cum_30_dxbb between", value1, value2, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxbbNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_dxbb not between", value1, value2, "amtCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpIsNull() {
            addCriterion("amt_cum_30_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpIsNotNull() {
            addCriterion("amt_cum_30_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpEqualTo(String value) {
            addCriterion("amt_cum_30_dxxp =", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpNotEqualTo(String value) {
            addCriterion("amt_cum_30_dxxp <>", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpGreaterThan(String value) {
            addCriterion("amt_cum_30_dxxp >", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_dxxp >=", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpLessThan(String value) {
            addCriterion("amt_cum_30_dxxp <", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_dxxp <=", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpLike(String value) {
            addCriterion("amt_cum_30_dxxp like", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpNotLike(String value) {
            addCriterion("amt_cum_30_dxxp not like", value, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpIn(List<String> values) {
            addCriterion("amt_cum_30_dxxp in", values, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpNotIn(List<String> values) {
            addCriterion("amt_cum_30_dxxp not in", values, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_dxxp between", value1, value2, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DxxpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_dxxp not between", value1, value2, "amtCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdIsNull() {
            addCriterion("amt_cum_30_dd is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdIsNotNull() {
            addCriterion("amt_cum_30_dd is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdEqualTo(String value) {
            addCriterion("amt_cum_30_dd =", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdNotEqualTo(String value) {
            addCriterion("amt_cum_30_dd <>", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdGreaterThan(String value) {
            addCriterion("amt_cum_30_dd >", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_dd >=", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdLessThan(String value) {
            addCriterion("amt_cum_30_dd <", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_dd <=", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdLike(String value) {
            addCriterion("amt_cum_30_dd like", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdNotLike(String value) {
            addCriterion("amt_cum_30_dd not like", value, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdIn(List<String> values) {
            addCriterion("amt_cum_30_dd in", values, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdNotIn(List<String> values) {
            addCriterion("amt_cum_30_dd not in", values, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdBetween(String value1, String value2) {
            addCriterion("amt_cum_30_dd between", value1, value2, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30DdNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_dd not between", value1, value2, "amtCum30Dd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxIsNull() {
            addCriterion("amt_cum_30_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxIsNotNull() {
            addCriterion("amt_cum_30_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxEqualTo(String value) {
            addCriterion("amt_cum_30_jt_qy_dx =", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxNotEqualTo(String value) {
            addCriterion("amt_cum_30_jt_qy_dx <>", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxGreaterThan(String value) {
            addCriterion("amt_cum_30_jt_qy_dx >", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_jt_qy_dx >=", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxLessThan(String value) {
            addCriterion("amt_cum_30_jt_qy_dx <", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_jt_qy_dx <=", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxLike(String value) {
            addCriterion("amt_cum_30_jt_qy_dx like", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxNotLike(String value) {
            addCriterion("amt_cum_30_jt_qy_dx not like", value, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxIn(List<String> values) {
            addCriterion("amt_cum_30_jt_qy_dx in", values, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxNotIn(List<String> values) {
            addCriterion("amt_cum_30_jt_qy_dx not in", values, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxBetween(String value1, String value2) {
            addCriterion("amt_cum_30_jt_qy_dx between", value1, value2, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JtQyDxNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_jt_qy_dx not between", value1, value2, "amtCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdIsNull() {
            addCriterion("amt_cum_30_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdIsNotNull() {
            addCriterion("amt_cum_30_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdEqualTo(String value) {
            addCriterion("amt_cum_30_no_dd =", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdNotEqualTo(String value) {
            addCriterion("amt_cum_30_no_dd <>", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdGreaterThan(String value) {
            addCriterion("amt_cum_30_no_dd >", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_no_dd >=", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdLessThan(String value) {
            addCriterion("amt_cum_30_no_dd <", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_no_dd <=", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdLike(String value) {
            addCriterion("amt_cum_30_no_dd like", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdNotLike(String value) {
            addCriterion("amt_cum_30_no_dd not like", value, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdIn(List<String> values) {
            addCriterion("amt_cum_30_no_dd in", values, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdNotIn(List<String> values) {
            addCriterion("amt_cum_30_no_dd not in", values, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdBetween(String value1, String value2) {
            addCriterion("amt_cum_30_no_dd between", value1, value2, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NoDdNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_no_dd not between", value1, value2, "amtCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzIsNull() {
            addCriterion("amt_cum_30_xz is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzIsNotNull() {
            addCriterion("amt_cum_30_xz is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzEqualTo(String value) {
            addCriterion("amt_cum_30_xz =", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzNotEqualTo(String value) {
            addCriterion("amt_cum_30_xz <>", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzGreaterThan(String value) {
            addCriterion("amt_cum_30_xz >", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_xz >=", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzLessThan(String value) {
            addCriterion("amt_cum_30_xz <", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_xz <=", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzLike(String value) {
            addCriterion("amt_cum_30_xz like", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzNotLike(String value) {
            addCriterion("amt_cum_30_xz not like", value, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzIn(List<String> values) {
            addCriterion("amt_cum_30_xz in", values, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzNotIn(List<String> values) {
            addCriterion("amt_cum_30_xz not in", values, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzBetween(String value1, String value2) {
            addCriterion("amt_cum_30_xz between", value1, value2, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30XzNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_xz not between", value1, value2, "amtCum30Xz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsIsNull() {
            addCriterion("amt_cum_30_js is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsIsNotNull() {
            addCriterion("amt_cum_30_js is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsEqualTo(String value) {
            addCriterion("amt_cum_30_js =", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsNotEqualTo(String value) {
            addCriterion("amt_cum_30_js <>", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsGreaterThan(String value) {
            addCriterion("amt_cum_30_js >", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_js >=", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsLessThan(String value) {
            addCriterion("amt_cum_30_js <", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_js <=", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsLike(String value) {
            addCriterion("amt_cum_30_js like", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsNotLike(String value) {
            addCriterion("amt_cum_30_js not like", value, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsIn(List<String> values) {
            addCriterion("amt_cum_30_js in", values, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsNotIn(List<String> values) {
            addCriterion("amt_cum_30_js not in", values, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsBetween(String value1, String value2) {
            addCriterion("amt_cum_30_js between", value1, value2, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andAmtCum30JsNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_js not between", value1, value2, "amtCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30IsNull() {
            addCriterion("profit_cum_30 is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30IsNotNull() {
            addCriterion("profit_cum_30 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30EqualTo(String value) {
            addCriterion("profit_cum_30 =", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NotEqualTo(String value) {
            addCriterion("profit_cum_30 <>", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30GreaterThan(String value) {
            addCriterion("profit_cum_30 >", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30GreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30 >=", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30LessThan(String value) {
            addCriterion("profit_cum_30 <", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30LessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30 <=", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30Like(String value) {
            addCriterion("profit_cum_30 like", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NotLike(String value) {
            addCriterion("profit_cum_30 not like", value, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30In(List<String> values) {
            addCriterion("profit_cum_30 in", values, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NotIn(List<String> values) {
            addCriterion("profit_cum_30 not in", values, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30Between(String value1, String value2) {
            addCriterion("profit_cum_30 between", value1, value2, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NotBetween(String value1, String value2) {
            addCriterion("profit_cum_30 not between", value1, value2, "profitCum30");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtIsNull() {
            addCriterion("profit_cum_30_jt is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtIsNotNull() {
            addCriterion("profit_cum_30_jt is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtEqualTo(String value) {
            addCriterion("profit_cum_30_jt =", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtNotEqualTo(String value) {
            addCriterion("profit_cum_30_jt <>", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtGreaterThan(String value) {
            addCriterion("profit_cum_30_jt >", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_jt >=", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtLessThan(String value) {
            addCriterion("profit_cum_30_jt <", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_jt <=", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtLike(String value) {
            addCriterion("profit_cum_30_jt like", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtNotLike(String value) {
            addCriterion("profit_cum_30_jt not like", value, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtIn(List<String> values) {
            addCriterion("profit_cum_30_jt in", values, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtNotIn(List<String> values) {
            addCriterion("profit_cum_30_jt not in", values, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtBetween(String value1, String value2) {
            addCriterion("profit_cum_30_jt between", value1, value2, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_jt not between", value1, value2, "profitCum30Jt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtIsNull() {
            addCriterion("profit_cum_30_pt is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtIsNotNull() {
            addCriterion("profit_cum_30_pt is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtEqualTo(String value) {
            addCriterion("profit_cum_30_pt =", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtNotEqualTo(String value) {
            addCriterion("profit_cum_30_pt <>", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtGreaterThan(String value) {
            addCriterion("profit_cum_30_pt >", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_pt >=", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtLessThan(String value) {
            addCriterion("profit_cum_30_pt <", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_pt <=", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtLike(String value) {
            addCriterion("profit_cum_30_pt like", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtNotLike(String value) {
            addCriterion("profit_cum_30_pt not like", value, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtIn(List<String> values) {
            addCriterion("profit_cum_30_pt in", values, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtNotIn(List<String> values) {
            addCriterion("profit_cum_30_pt not in", values, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtBetween(String value1, String value2) {
            addCriterion("profit_cum_30_pt between", value1, value2, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30PtNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_pt not between", value1, value2, "profitCum30Pt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyIsNull() {
            addCriterion("profit_cum_30_qy is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyIsNotNull() {
            addCriterion("profit_cum_30_qy is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyEqualTo(String value) {
            addCriterion("profit_cum_30_qy =", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyNotEqualTo(String value) {
            addCriterion("profit_cum_30_qy <>", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyGreaterThan(String value) {
            addCriterion("profit_cum_30_qy >", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_qy >=", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyLessThan(String value) {
            addCriterion("profit_cum_30_qy <", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_qy <=", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyLike(String value) {
            addCriterion("profit_cum_30_qy like", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyNotLike(String value) {
            addCriterion("profit_cum_30_qy not like", value, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyIn(List<String> values) {
            addCriterion("profit_cum_30_qy in", values, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyNotIn(List<String> values) {
            addCriterion("profit_cum_30_qy not in", values, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyBetween(String value1, String value2) {
            addCriterion("profit_cum_30_qy between", value1, value2, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30QyNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_qy not between", value1, value2, "profitCum30Qy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbIsNull() {
            addCriterion("profit_cum_30_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbIsNotNull() {
            addCriterion("profit_cum_30_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbEqualTo(String value) {
            addCriterion("profit_cum_30_dxbb =", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbNotEqualTo(String value) {
            addCriterion("profit_cum_30_dxbb <>", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbGreaterThan(String value) {
            addCriterion("profit_cum_30_dxbb >", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_dxbb >=", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbLessThan(String value) {
            addCriterion("profit_cum_30_dxbb <", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_dxbb <=", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbLike(String value) {
            addCriterion("profit_cum_30_dxbb like", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbNotLike(String value) {
            addCriterion("profit_cum_30_dxbb not like", value, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbIn(List<String> values) {
            addCriterion("profit_cum_30_dxbb in", values, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbNotIn(List<String> values) {
            addCriterion("profit_cum_30_dxbb not in", values, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbBetween(String value1, String value2) {
            addCriterion("profit_cum_30_dxbb between", value1, value2, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxbbNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_dxbb not between", value1, value2, "profitCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpIsNull() {
            addCriterion("profit_cum_30_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpIsNotNull() {
            addCriterion("profit_cum_30_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpEqualTo(String value) {
            addCriterion("profit_cum_30_dxxp =", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpNotEqualTo(String value) {
            addCriterion("profit_cum_30_dxxp <>", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpGreaterThan(String value) {
            addCriterion("profit_cum_30_dxxp >", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_dxxp >=", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpLessThan(String value) {
            addCriterion("profit_cum_30_dxxp <", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_dxxp <=", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpLike(String value) {
            addCriterion("profit_cum_30_dxxp like", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpNotLike(String value) {
            addCriterion("profit_cum_30_dxxp not like", value, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpIn(List<String> values) {
            addCriterion("profit_cum_30_dxxp in", values, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpNotIn(List<String> values) {
            addCriterion("profit_cum_30_dxxp not in", values, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpBetween(String value1, String value2) {
            addCriterion("profit_cum_30_dxxp between", value1, value2, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DxxpNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_dxxp not between", value1, value2, "profitCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdIsNull() {
            addCriterion("profit_cum_30_dd is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdIsNotNull() {
            addCriterion("profit_cum_30_dd is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdEqualTo(String value) {
            addCriterion("profit_cum_30_dd =", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdNotEqualTo(String value) {
            addCriterion("profit_cum_30_dd <>", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdGreaterThan(String value) {
            addCriterion("profit_cum_30_dd >", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_dd >=", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdLessThan(String value) {
            addCriterion("profit_cum_30_dd <", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_dd <=", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdLike(String value) {
            addCriterion("profit_cum_30_dd like", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdNotLike(String value) {
            addCriterion("profit_cum_30_dd not like", value, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdIn(List<String> values) {
            addCriterion("profit_cum_30_dd in", values, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdNotIn(List<String> values) {
            addCriterion("profit_cum_30_dd not in", values, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdBetween(String value1, String value2) {
            addCriterion("profit_cum_30_dd between", value1, value2, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30DdNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_dd not between", value1, value2, "profitCum30Dd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxIsNull() {
            addCriterion("profit_cum_30_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxIsNotNull() {
            addCriterion("profit_cum_30_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxEqualTo(String value) {
            addCriterion("profit_cum_30_jt_qy_dx =", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxNotEqualTo(String value) {
            addCriterion("profit_cum_30_jt_qy_dx <>", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxGreaterThan(String value) {
            addCriterion("profit_cum_30_jt_qy_dx >", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_jt_qy_dx >=", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxLessThan(String value) {
            addCriterion("profit_cum_30_jt_qy_dx <", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_jt_qy_dx <=", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxLike(String value) {
            addCriterion("profit_cum_30_jt_qy_dx like", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxNotLike(String value) {
            addCriterion("profit_cum_30_jt_qy_dx not like", value, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxIn(List<String> values) {
            addCriterion("profit_cum_30_jt_qy_dx in", values, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxNotIn(List<String> values) {
            addCriterion("profit_cum_30_jt_qy_dx not in", values, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxBetween(String value1, String value2) {
            addCriterion("profit_cum_30_jt_qy_dx between", value1, value2, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JtQyDxNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_jt_qy_dx not between", value1, value2, "profitCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdIsNull() {
            addCriterion("profit_cum_30_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdIsNotNull() {
            addCriterion("profit_cum_30_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdEqualTo(String value) {
            addCriterion("profit_cum_30_no_dd =", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdNotEqualTo(String value) {
            addCriterion("profit_cum_30_no_dd <>", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdGreaterThan(String value) {
            addCriterion("profit_cum_30_no_dd >", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_no_dd >=", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdLessThan(String value) {
            addCriterion("profit_cum_30_no_dd <", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_no_dd <=", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdLike(String value) {
            addCriterion("profit_cum_30_no_dd like", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdNotLike(String value) {
            addCriterion("profit_cum_30_no_dd not like", value, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdIn(List<String> values) {
            addCriterion("profit_cum_30_no_dd in", values, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdNotIn(List<String> values) {
            addCriterion("profit_cum_30_no_dd not in", values, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdBetween(String value1, String value2) {
            addCriterion("profit_cum_30_no_dd between", value1, value2, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NoDdNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_no_dd not between", value1, value2, "profitCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzIsNull() {
            addCriterion("profit_cum_30_xz is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzIsNotNull() {
            addCriterion("profit_cum_30_xz is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzEqualTo(String value) {
            addCriterion("profit_cum_30_xz =", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzNotEqualTo(String value) {
            addCriterion("profit_cum_30_xz <>", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzGreaterThan(String value) {
            addCriterion("profit_cum_30_xz >", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_xz >=", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzLessThan(String value) {
            addCriterion("profit_cum_30_xz <", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_xz <=", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzLike(String value) {
            addCriterion("profit_cum_30_xz like", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzNotLike(String value) {
            addCriterion("profit_cum_30_xz not like", value, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzIn(List<String> values) {
            addCriterion("profit_cum_30_xz in", values, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzNotIn(List<String> values) {
            addCriterion("profit_cum_30_xz not in", values, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzBetween(String value1, String value2) {
            addCriterion("profit_cum_30_xz between", value1, value2, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30XzNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_xz not between", value1, value2, "profitCum30Xz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsIsNull() {
            addCriterion("profit_cum_30_js is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsIsNotNull() {
            addCriterion("profit_cum_30_js is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsEqualTo(String value) {
            addCriterion("profit_cum_30_js =", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsNotEqualTo(String value) {
            addCriterion("profit_cum_30_js <>", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsGreaterThan(String value) {
            addCriterion("profit_cum_30_js >", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_js >=", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsLessThan(String value) {
            addCriterion("profit_cum_30_js <", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_js <=", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsLike(String value) {
            addCriterion("profit_cum_30_js like", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsNotLike(String value) {
            addCriterion("profit_cum_30_js not like", value, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsIn(List<String> values) {
            addCriterion("profit_cum_30_js in", values, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsNotIn(List<String> values) {
            addCriterion("profit_cum_30_js not in", values, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsBetween(String value1, String value2) {
            addCriterion("profit_cum_30_js between", value1, value2, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30JsNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_js not between", value1, value2, "profitCum30Js");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateIsNull() {
            addCriterion("profit_cum_30_rate is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateIsNotNull() {
            addCriterion("profit_cum_30_rate is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateEqualTo(String value) {
            addCriterion("profit_cum_30_rate =", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate <>", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateGreaterThan(String value) {
            addCriterion("profit_cum_30_rate >", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate >=", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateLessThan(String value) {
            addCriterion("profit_cum_30_rate <", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate <=", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateLike(String value) {
            addCriterion("profit_cum_30_rate like", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNotLike(String value) {
            addCriterion("profit_cum_30_rate not like", value, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateIn(List<String> values) {
            addCriterion("profit_cum_30_rate in", values, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate not in", values, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate between", value1, value2, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate not between", value1, value2, "profitCum30Rate");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtIsNull() {
            addCriterion("profit_cum_30_rate_jt is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtIsNotNull() {
            addCriterion("profit_cum_30_rate_jt is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt =", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt <>", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_jt >", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt >=", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtLessThan(String value) {
            addCriterion("profit_cum_30_rate_jt <", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt <=", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtLike(String value) {
            addCriterion("profit_cum_30_rate_jt like", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtNotLike(String value) {
            addCriterion("profit_cum_30_rate_jt not like", value, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtIn(List<String> values) {
            addCriterion("profit_cum_30_rate_jt in", values, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_jt not in", values, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_jt between", value1, value2, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_jt not between", value1, value2, "profitCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtIsNull() {
            addCriterion("profit_cum_30_rate_pt is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtIsNotNull() {
            addCriterion("profit_cum_30_rate_pt is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtEqualTo(String value) {
            addCriterion("profit_cum_30_rate_pt =", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_pt <>", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_pt >", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_pt >=", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtLessThan(String value) {
            addCriterion("profit_cum_30_rate_pt <", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_pt <=", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtLike(String value) {
            addCriterion("profit_cum_30_rate_pt like", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtNotLike(String value) {
            addCriterion("profit_cum_30_rate_pt not like", value, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtIn(List<String> values) {
            addCriterion("profit_cum_30_rate_pt in", values, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_pt not in", values, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_pt between", value1, value2, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RatePtNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_pt not between", value1, value2, "profitCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyIsNull() {
            addCriterion("profit_cum_30_rate_qy is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyIsNotNull() {
            addCriterion("profit_cum_30_rate_qy is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyEqualTo(String value) {
            addCriterion("profit_cum_30_rate_qy =", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_qy <>", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_qy >", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_qy >=", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyLessThan(String value) {
            addCriterion("profit_cum_30_rate_qy <", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_qy <=", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyLike(String value) {
            addCriterion("profit_cum_30_rate_qy like", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyNotLike(String value) {
            addCriterion("profit_cum_30_rate_qy not like", value, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyIn(List<String> values) {
            addCriterion("profit_cum_30_rate_qy in", values, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_qy not in", values, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_qy between", value1, value2, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateQyNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_qy not between", value1, value2, "profitCum30RateQy");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbIsNull() {
            addCriterion("profit_cum_30_rate_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbIsNotNull() {
            addCriterion("profit_cum_30_rate_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxbb =", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxbb <>", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_dxbb >", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxbb >=", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbLessThan(String value) {
            addCriterion("profit_cum_30_rate_dxbb <", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxbb <=", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbLike(String value) {
            addCriterion("profit_cum_30_rate_dxbb like", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbNotLike(String value) {
            addCriterion("profit_cum_30_rate_dxbb not like", value, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbIn(List<String> values) {
            addCriterion("profit_cum_30_rate_dxbb in", values, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_dxbb not in", values, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_dxbb between", value1, value2, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxbbNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_dxbb not between", value1, value2, "profitCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpIsNull() {
            addCriterion("profit_cum_30_rate_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpIsNotNull() {
            addCriterion("profit_cum_30_rate_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxxp =", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxxp <>", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_dxxp >", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxxp >=", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpLessThan(String value) {
            addCriterion("profit_cum_30_rate_dxxp <", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dxxp <=", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpLike(String value) {
            addCriterion("profit_cum_30_rate_dxxp like", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpNotLike(String value) {
            addCriterion("profit_cum_30_rate_dxxp not like", value, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpIn(List<String> values) {
            addCriterion("profit_cum_30_rate_dxxp in", values, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_dxxp not in", values, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_dxxp between", value1, value2, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDxxpNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_dxxp not between", value1, value2, "profitCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdIsNull() {
            addCriterion("profit_cum_30_rate_dd is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdIsNotNull() {
            addCriterion("profit_cum_30_rate_dd is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dd =", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dd <>", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_dd >", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dd >=", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdLessThan(String value) {
            addCriterion("profit_cum_30_rate_dd <", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_dd <=", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdLike(String value) {
            addCriterion("profit_cum_30_rate_dd like", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdNotLike(String value) {
            addCriterion("profit_cum_30_rate_dd not like", value, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdIn(List<String> values) {
            addCriterion("profit_cum_30_rate_dd in", values, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_dd not in", values, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_dd between", value1, value2, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateDdNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_dd not between", value1, value2, "profitCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxIsNull() {
            addCriterion("profit_cum_30_rate_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxIsNotNull() {
            addCriterion("profit_cum_30_rate_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx =", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx <>", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx >", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx >=", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxLessThan(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx <", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx <=", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxLike(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx like", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxNotLike(String value) {
            addCriterion("profit_cum_30_rate_jt_qy_dx not like", value, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxIn(List<String> values) {
            addCriterion("profit_cum_30_rate_jt_qy_dx in", values, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_jt_qy_dx not in", values, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_jt_qy_dx between", value1, value2, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJtQyDxNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_jt_qy_dx not between", value1, value2, "profitCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdIsNull() {
            addCriterion("profit_cum_30_rate_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdIsNotNull() {
            addCriterion("profit_cum_30_rate_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdEqualTo(String value) {
            addCriterion("profit_cum_30_rate_no_dd =", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_no_dd <>", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_no_dd >", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_no_dd >=", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdLessThan(String value) {
            addCriterion("profit_cum_30_rate_no_dd <", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_no_dd <=", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdLike(String value) {
            addCriterion("profit_cum_30_rate_no_dd like", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdNotLike(String value) {
            addCriterion("profit_cum_30_rate_no_dd not like", value, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdIn(List<String> values) {
            addCriterion("profit_cum_30_rate_no_dd in", values, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_no_dd not in", values, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_no_dd between", value1, value2, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNoDdNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_no_dd not between", value1, value2, "profitCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzIsNull() {
            addCriterion("profit_cum_30_rate_xz is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzIsNotNull() {
            addCriterion("profit_cum_30_rate_xz is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzEqualTo(String value) {
            addCriterion("profit_cum_30_rate_xz =", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_xz <>", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_xz >", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_xz >=", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzLessThan(String value) {
            addCriterion("profit_cum_30_rate_xz <", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_xz <=", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzLike(String value) {
            addCriterion("profit_cum_30_rate_xz like", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzNotLike(String value) {
            addCriterion("profit_cum_30_rate_xz not like", value, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzIn(List<String> values) {
            addCriterion("profit_cum_30_rate_xz in", values, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_xz not in", values, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_xz between", value1, value2, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateXzNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_xz not between", value1, value2, "profitCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsIsNull() {
            addCriterion("profit_cum_30_rate_js is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsIsNotNull() {
            addCriterion("profit_cum_30_rate_js is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsEqualTo(String value) {
            addCriterion("profit_cum_30_rate_js =", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_js <>", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_js >", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_js >=", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsLessThan(String value) {
            addCriterion("profit_cum_30_rate_js <", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_js <=", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsLike(String value) {
            addCriterion("profit_cum_30_rate_js like", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsNotLike(String value) {
            addCriterion("profit_cum_30_rate_js not like", value, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsIn(List<String> values) {
            addCriterion("profit_cum_30_rate_js in", values, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_js not in", values, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_js between", value1, value2, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateJsNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_js not between", value1, value2, "profitCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthIsNull() {
            addCriterion("amt_cum_30_month is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthIsNotNull() {
            addCriterion("amt_cum_30_month is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthEqualTo(String value) {
            addCriterion("amt_cum_30_month =", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNotEqualTo(String value) {
            addCriterion("amt_cum_30_month <>", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthGreaterThan(String value) {
            addCriterion("amt_cum_30_month >", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_month >=", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthLessThan(String value) {
            addCriterion("amt_cum_30_month <", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_month <=", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthLike(String value) {
            addCriterion("amt_cum_30_month like", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNotLike(String value) {
            addCriterion("amt_cum_30_month not like", value, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthIn(List<String> values) {
            addCriterion("amt_cum_30_month in", values, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNotIn(List<String> values) {
            addCriterion("amt_cum_30_month not in", values, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthBetween(String value1, String value2) {
            addCriterion("amt_cum_30_month between", value1, value2, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_month not between", value1, value2, "amtCum30Month");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpIsNull() {
            addCriterion("amt_cum_30_month_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpIsNotNull() {
            addCriterion("amt_cum_30_month_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_month_nodtp =", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_month_nodtp <>", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_month_nodtp >", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_month_nodtp >=", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpLessThan(String value) {
            addCriterion("amt_cum_30_month_nodtp <", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_month_nodtp <=", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpLike(String value) {
            addCriterion("amt_cum_30_month_nodtp like", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpNotLike(String value) {
            addCriterion("amt_cum_30_month_nodtp not like", value, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_month_nodtp in", values, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_month_nodtp not in", values, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_month_nodtp between", value1, value2, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_month_nodtp not between", value1, value2, "amtCum30MonthNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyIsNull() {
            addCriterion("amt_cum_30_month_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyIsNotNull() {
            addCriterion("amt_cum_30_month_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyEqualTo(String value) {
            addCriterion("amt_cum_30_month_nozy =", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_month_nozy <>", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_month_nozy >", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_month_nozy >=", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyLessThan(String value) {
            addCriterion("amt_cum_30_month_nozy <", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_month_nozy <=", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyLike(String value) {
            addCriterion("amt_cum_30_month_nozy like", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyNotLike(String value) {
            addCriterion("amt_cum_30_month_nozy not like", value, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyIn(List<String> values) {
            addCriterion("amt_cum_30_month_nozy in", values, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_month_nozy not in", values, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_month_nozy between", value1, value2, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30MonthNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_month_nozy not between", value1, value2, "amtCum30MonthNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateIsNull() {
            addCriterion("amt_cum_30_rate is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateIsNotNull() {
            addCriterion("amt_cum_30_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateEqualTo(String value) {
            addCriterion("amt_cum_30_rate =", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate <>", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGreaterThan(String value) {
            addCriterion("amt_cum_30_rate >", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate >=", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateLessThan(String value) {
            addCriterion("amt_cum_30_rate <", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate <=", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateLike(String value) {
            addCriterion("amt_cum_30_rate like", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNotLike(String value) {
            addCriterion("amt_cum_30_rate not like", value, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateIn(List<String> values) {
            addCriterion("amt_cum_30_rate in", values, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate not in", values, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate between", value1, value2, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate not between", value1, value2, "amtCum30Rate");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtIsNull() {
            addCriterion("amt_cum_30_rate_jt is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtIsNotNull() {
            addCriterion("amt_cum_30_rate_jt is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt =", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt <>", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_jt >", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt >=", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtLessThan(String value) {
            addCriterion("amt_cum_30_rate_jt <", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt <=", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtLike(String value) {
            addCriterion("amt_cum_30_rate_jt like", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNotLike(String value) {
            addCriterion("amt_cum_30_rate_jt not like", value, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt in", values, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt not in", values, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt between", value1, value2, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt not between", value1, value2, "amtCum30RateJt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtIsNull() {
            addCriterion("amt_cum_30_rate_pt is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtIsNotNull() {
            addCriterion("amt_cum_30_rate_pt is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt =", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt <>", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_pt >", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt >=", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtLessThan(String value) {
            addCriterion("amt_cum_30_rate_pt <", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt <=", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtLike(String value) {
            addCriterion("amt_cum_30_rate_pt like", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNotLike(String value) {
            addCriterion("amt_cum_30_rate_pt not like", value, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtIn(List<String> values) {
            addCriterion("amt_cum_30_rate_pt in", values, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_pt not in", values, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_pt between", value1, value2, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_pt not between", value1, value2, "amtCum30RatePt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtIsNull() {
            addCriterion("amt_cum_30_rate_qt is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtIsNotNull() {
            addCriterion("amt_cum_30_rate_qt is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt =", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt <>", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_qt >", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt >=", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtLessThan(String value) {
            addCriterion("amt_cum_30_rate_qt <", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt <=", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtLike(String value) {
            addCriterion("amt_cum_30_rate_qt like", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNotLike(String value) {
            addCriterion("amt_cum_30_rate_qt not like", value, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtIn(List<String> values) {
            addCriterion("amt_cum_30_rate_qt in", values, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_qt not in", values, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_qt between", value1, value2, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_qt not between", value1, value2, "amtCum30RateQt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbIsNull() {
            addCriterion("amt_cum_30_rate_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbIsNotNull() {
            addCriterion("amt_cum_30_rate_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb =", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb <>", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dxbb >", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb >=", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbLessThan(String value) {
            addCriterion("amt_cum_30_rate_dxbb <", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb <=", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbLike(String value) {
            addCriterion("amt_cum_30_rate_dxbb like", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNotLike(String value) {
            addCriterion("amt_cum_30_rate_dxbb not like", value, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxbb in", values, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxbb not in", values, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxbb between", value1, value2, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxbb not between", value1, value2, "amtCum30RateDxbb");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpIsNull() {
            addCriterion("amt_cum_30_rate_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpIsNotNull() {
            addCriterion("amt_cum_30_rate_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp =", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp <>", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dxxp >", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp >=", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpLessThan(String value) {
            addCriterion("amt_cum_30_rate_dxxp <", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp <=", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpLike(String value) {
            addCriterion("amt_cum_30_rate_dxxp like", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNotLike(String value) {
            addCriterion("amt_cum_30_rate_dxxp not like", value, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxxp in", values, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxxp not in", values, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxxp between", value1, value2, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxxp not between", value1, value2, "amtCum30RateDxxp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdIsNull() {
            addCriterion("amt_cum_30_rate_dd is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdIsNotNull() {
            addCriterion("amt_cum_30_rate_dd is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd =", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd <>", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dd >", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd >=", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdLessThan(String value) {
            addCriterion("amt_cum_30_rate_dd <", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd <=", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdLike(String value) {
            addCriterion("amt_cum_30_rate_dd like", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNotLike(String value) {
            addCriterion("amt_cum_30_rate_dd not like", value, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dd in", values, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dd not in", values, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dd between", value1, value2, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dd not between", value1, value2, "amtCum30RateDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxIsNull() {
            addCriterion("amt_cum_30_rate_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxIsNotNull() {
            addCriterion("amt_cum_30_rate_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx =", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx <>", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx >", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx >=", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxLessThan(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx <", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx <=", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxLike(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx like", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNotLike(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx not like", value, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_qy_dx in", values, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_qy_dx not in", values, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_qy_dx between", value1, value2, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_qy_dx not between", value1, value2, "amtCum30RateJtQyDx");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdIsNull() {
            addCriterion("amt_cum_30_rate_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdIsNotNull() {
            addCriterion("amt_cum_30_rate_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd =", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd <>", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_no_dd >", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd >=", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdLessThan(String value) {
            addCriterion("amt_cum_30_rate_no_dd <", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd <=", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdLike(String value) {
            addCriterion("amt_cum_30_rate_no_dd like", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNotLike(String value) {
            addCriterion("amt_cum_30_rate_no_dd not like", value, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdIn(List<String> values) {
            addCriterion("amt_cum_30_rate_no_dd in", values, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_no_dd not in", values, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_no_dd between", value1, value2, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_no_dd not between", value1, value2, "amtCum30RateNoDd");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzIsNull() {
            addCriterion("amt_cum_30_rate_xz is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzIsNotNull() {
            addCriterion("amt_cum_30_rate_xz is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz =", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz <>", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_xz >", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz >=", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzLessThan(String value) {
            addCriterion("amt_cum_30_rate_xz <", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz <=", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzLike(String value) {
            addCriterion("amt_cum_30_rate_xz like", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNotLike(String value) {
            addCriterion("amt_cum_30_rate_xz not like", value, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzIn(List<String> values) {
            addCriterion("amt_cum_30_rate_xz in", values, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_xz not in", values, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_xz between", value1, value2, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_xz not between", value1, value2, "amtCum30RateXz");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsIsNull() {
            addCriterion("amt_cum_30_rate_js is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsIsNotNull() {
            addCriterion("amt_cum_30_rate_js is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js =", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js <>", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_js >", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js >=", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsLessThan(String value) {
            addCriterion("amt_cum_30_rate_js <", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js <=", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsLike(String value) {
            addCriterion("amt_cum_30_rate_js like", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNotLike(String value) {
            addCriterion("amt_cum_30_rate_js not like", value, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsIn(List<String> values) {
            addCriterion("amt_cum_30_rate_js in", values, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_js not in", values, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_js between", value1, value2, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_js not between", value1, value2, "amtCum30RateJs");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpIsNull() {
            addCriterion("amt_cum_30_rate_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nodtp =", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nodtp <>", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_nodtp >", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nodtp >=", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_nodtp <", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nodtp <=", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_nodtp like", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_nodtp not like", value, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_nodtp in", values, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_nodtp not in", values, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_nodtp between", value1, value2, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_nodtp not between", value1, value2, "amtCum30RateNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpIsNull() {
            addCriterion("amt_cum_30_rate_jt_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_jt_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp =", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp <>", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp >", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp >=", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp <", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp <=", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp like", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_jt_nodtp not like", value, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_nodtp in", values, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_nodtp not in", values, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_nodtp between", value1, value2, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_nodtp not between", value1, value2, "amtCum30RateJtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpIsNull() {
            addCriterion("amt_cum_30_rate_pt_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_pt_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp =", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp <>", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp >", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp >=", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp <", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp <=", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp like", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_pt_nodtp not like", value, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_pt_nodtp in", values, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_pt_nodtp not in", values, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_pt_nodtp between", value1, value2, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_pt_nodtp not between", value1, value2, "amtCum30RatePtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpIsNull() {
            addCriterion("amt_cum_30_rate_qt_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_qt_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp =", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp <>", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp >", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp >=", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp <", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp <=", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp like", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_qt_nodtp not like", value, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_qt_nodtp in", values, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_qt_nodtp not in", values, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_qt_nodtp between", value1, value2, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_qt_nodtp not between", value1, value2, "amtCum30RateQtNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpIsNull() {
            addCriterion("amt_cum_30_rate_dxbb_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_dxbb_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp =", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp <>", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp >", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp >=", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp <", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp <=", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp like", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp not like", value, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp in", values, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp not in", values, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp between", value1, value2, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxbb_nodtp not between", value1, value2, "amtCum30RateDxbbNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpIsNull() {
            addCriterion("amt_cum_30_rate_dxxp_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_dxxp_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp =", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp <>", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp >", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp >=", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp <", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp <=", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp like", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp not like", value, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp in", values, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp not in", values, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp between", value1, value2, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxxp_nodtp not between", value1, value2, "amtCum30RateDxxpNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpIsNull() {
            addCriterion("amt_cum_30_rate_dd_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_dd_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp =", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp <>", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp >", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp >=", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp <", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp <=", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp like", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_dd_nodtp not like", value, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dd_nodtp in", values, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dd_nodtp not in", values, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dd_nodtp between", value1, value2, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dd_nodtp not between", value1, value2, "amtCum30RateDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpIsNull() {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp =", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp <>", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp >", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp >=", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp <", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp <=", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp like", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp not like", value, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp in", values, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp not in", values, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp between", value1, value2, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nodtp not between", value1, value2, "amtCum30RateJtQyDxNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpIsNull() {
            addCriterion("amt_cum_30_rate_no_dd_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_no_dd_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp =", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp <>", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp >", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp >=", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp <", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp <=", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp like", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp not like", value, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp in", values, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp not in", values, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp between", value1, value2, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_no_dd_nodtp not between", value1, value2, "amtCum30RateNoDdNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpIsNull() {
            addCriterion("amt_cum_30_rate_xz_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_xz_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp =", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp <>", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp >", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp >=", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp <", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp <=", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp like", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_xz_nodtp not like", value, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_xz_nodtp in", values, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_xz_nodtp not in", values, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_xz_nodtp between", value1, value2, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_xz_nodtp not between", value1, value2, "amtCum30RateXzNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpIsNull() {
            addCriterion("amt_cum_30_rate_js_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_js_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp =", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp <>", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp >", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp >=", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp <", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp <=", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp like", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_js_nodtp not like", value, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_js_nodtp in", values, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_js_nodtp not in", values, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_js_nodtp between", value1, value2, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_js_nodtp not between", value1, value2, "amtCum30RateJsNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyIsNull() {
            addCriterion("amt_cum_30_rate_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nozy =", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nozy <>", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_nozy >", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nozy >=", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_nozy <", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_nozy <=", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyLike(String value) {
            addCriterion("amt_cum_30_rate_nozy like", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_nozy not like", value, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_nozy in", values, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_nozy not in", values, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_nozy between", value1, value2, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_nozy not between", value1, value2, "amtCum30RateNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyIsNull() {
            addCriterion("amt_cum_30_rate_jt_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_jt_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy =", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy <>", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy >", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy >=", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy <", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy <=", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyLike(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy like", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_jt_nozy not like", value, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_nozy in", values, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_nozy not in", values, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_nozy between", value1, value2, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_nozy not between", value1, value2, "amtCum30RateJtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyIsNull() {
            addCriterion("amt_cum_30_rate_pt_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_pt_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy =", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy <>", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy >", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy >=", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy <", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy <=", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyLike(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy like", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_pt_nozy not like", value, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_pt_nozy in", values, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_pt_nozy not in", values, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_pt_nozy between", value1, value2, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RatePtNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_pt_nozy not between", value1, value2, "amtCum30RatePtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyIsNull() {
            addCriterion("amt_cum_30_rate_qt_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_qt_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy =", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy <>", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy >", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy >=", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy <", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy <=", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyLike(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy like", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_qt_nozy not like", value, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_qt_nozy in", values, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_qt_nozy not in", values, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_qt_nozy between", value1, value2, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateQtNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_qt_nozy not between", value1, value2, "amtCum30RateQtNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyIsNull() {
            addCriterion("amt_cum_30_rate_dxbb_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_dxbb_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy =", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy <>", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy >", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy >=", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy <", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy <=", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyLike(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy like", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_dxbb_nozy not like", value, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxbb_nozy in", values, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxbb_nozy not in", values, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxbb_nozy between", value1, value2, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxbbNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxbb_nozy not between", value1, value2, "amtCum30RateDxbbNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyIsNull() {
            addCriterion("amt_cum_30_rate_dxxp_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_dxxp_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy =", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy <>", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy >", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy >=", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy <", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy <=", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyLike(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy like", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_dxxp_nozy not like", value, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxxp_nozy in", values, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dxxp_nozy not in", values, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxxp_nozy between", value1, value2, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDxxpNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dxxp_nozy not between", value1, value2, "amtCum30RateDxxpNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyIsNull() {
            addCriterion("amt_cum_30_rate_dd_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_dd_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy =", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy <>", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy >", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy >=", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy <", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy <=", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyLike(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy like", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_dd_nozy not like", value, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dd_nozy in", values, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_dd_nozy not in", values, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dd_nozy between", value1, value2, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateDdNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_dd_nozy not between", value1, value2, "amtCum30RateDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyIsNull() {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy =", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy <>", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy >", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy >=", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy <", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy <=", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyLike(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy like", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy not like", value, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy in", values, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy not in", values, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy between", value1, value2, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJtQyDxNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_jt_qy_dx_nozy not between", value1, value2, "amtCum30RateJtQyDxNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyIsNull() {
            addCriterion("amt_cum_30_rate_no_dd_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_no_dd_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy =", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy <>", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy >", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy >=", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy <", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy <=", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyLike(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy like", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_no_dd_nozy not like", value, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_no_dd_nozy in", values, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_no_dd_nozy not in", values, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_no_dd_nozy between", value1, value2, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNoDdNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_no_dd_nozy not between", value1, value2, "amtCum30RateNoDdNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyIsNull() {
            addCriterion("amt_cum_30_rate_xz_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_xz_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy =", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy <>", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy >", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy >=", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy <", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy <=", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyLike(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy like", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_xz_nozy not like", value, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_xz_nozy in", values, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_xz_nozy not in", values, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_xz_nozy between", value1, value2, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateXzNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_xz_nozy not between", value1, value2, "amtCum30RateXzNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyIsNull() {
            addCriterion("amt_cum_30_rate_js_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_js_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nozy =", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nozy <>", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_js_nozy >", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nozy >=", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_js_nozy <", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_js_nozy <=", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyLike(String value) {
            addCriterion("amt_cum_30_rate_js_nozy like", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_js_nozy not like", value, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_js_nozy in", values, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_js_nozy not in", values, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_js_nozy between", value1, value2, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateJsNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_js_nozy not between", value1, value2, "amtCum30RateJsNozy");
            return (Criteria) this;
        }

        public Criteria andStockCum30IsNull() {
            addCriterion("stock_cum_30 is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30IsNotNull() {
            addCriterion("stock_cum_30 is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30EqualTo(String value) {
            addCriterion("stock_cum_30 =", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30NotEqualTo(String value) {
            addCriterion("stock_cum_30 <>", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30GreaterThan(String value) {
            addCriterion("stock_cum_30 >", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30GreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30 >=", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30LessThan(String value) {
            addCriterion("stock_cum_30 <", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30LessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30 <=", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30Like(String value) {
            addCriterion("stock_cum_30 like", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30NotLike(String value) {
            addCriterion("stock_cum_30 not like", value, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30In(List<String> values) {
            addCriterion("stock_cum_30 in", values, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30NotIn(List<String> values) {
            addCriterion("stock_cum_30 not in", values, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30Between(String value1, String value2) {
            addCriterion("stock_cum_30 between", value1, value2, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30NotBetween(String value1, String value2) {
            addCriterion("stock_cum_30 not between", value1, value2, "stockCum30");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtIsNull() {
            addCriterion("stock_cum_30_jt is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtIsNotNull() {
            addCriterion("stock_cum_30_jt is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtEqualTo(String value) {
            addCriterion("stock_cum_30_jt =", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtNotEqualTo(String value) {
            addCriterion("stock_cum_30_jt <>", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtGreaterThan(String value) {
            addCriterion("stock_cum_30_jt >", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_jt >=", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtLessThan(String value) {
            addCriterion("stock_cum_30_jt <", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_jt <=", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtLike(String value) {
            addCriterion("stock_cum_30_jt like", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtNotLike(String value) {
            addCriterion("stock_cum_30_jt not like", value, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtIn(List<String> values) {
            addCriterion("stock_cum_30_jt in", values, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtNotIn(List<String> values) {
            addCriterion("stock_cum_30_jt not in", values, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtBetween(String value1, String value2) {
            addCriterion("stock_cum_30_jt between", value1, value2, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_jt not between", value1, value2, "stockCum30Jt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtIsNull() {
            addCriterion("stock_cum_30_pt is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtIsNotNull() {
            addCriterion("stock_cum_30_pt is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtEqualTo(String value) {
            addCriterion("stock_cum_30_pt =", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtNotEqualTo(String value) {
            addCriterion("stock_cum_30_pt <>", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtGreaterThan(String value) {
            addCriterion("stock_cum_30_pt >", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_pt >=", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtLessThan(String value) {
            addCriterion("stock_cum_30_pt <", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_pt <=", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtLike(String value) {
            addCriterion("stock_cum_30_pt like", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtNotLike(String value) {
            addCriterion("stock_cum_30_pt not like", value, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtIn(List<String> values) {
            addCriterion("stock_cum_30_pt in", values, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtNotIn(List<String> values) {
            addCriterion("stock_cum_30_pt not in", values, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtBetween(String value1, String value2) {
            addCriterion("stock_cum_30_pt between", value1, value2, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30PtNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_pt not between", value1, value2, "stockCum30Pt");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyIsNull() {
            addCriterion("stock_cum_30_qy is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyIsNotNull() {
            addCriterion("stock_cum_30_qy is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyEqualTo(String value) {
            addCriterion("stock_cum_30_qy =", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyNotEqualTo(String value) {
            addCriterion("stock_cum_30_qy <>", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyGreaterThan(String value) {
            addCriterion("stock_cum_30_qy >", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_qy >=", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyLessThan(String value) {
            addCriterion("stock_cum_30_qy <", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_qy <=", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyLike(String value) {
            addCriterion("stock_cum_30_qy like", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyNotLike(String value) {
            addCriterion("stock_cum_30_qy not like", value, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyIn(List<String> values) {
            addCriterion("stock_cum_30_qy in", values, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyNotIn(List<String> values) {
            addCriterion("stock_cum_30_qy not in", values, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyBetween(String value1, String value2) {
            addCriterion("stock_cum_30_qy between", value1, value2, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30QyNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_qy not between", value1, value2, "stockCum30Qy");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbIsNull() {
            addCriterion("stock_cum_30_dxbb is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbIsNotNull() {
            addCriterion("stock_cum_30_dxbb is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbEqualTo(String value) {
            addCriterion("stock_cum_30_dxbb =", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbNotEqualTo(String value) {
            addCriterion("stock_cum_30_dxbb <>", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbGreaterThan(String value) {
            addCriterion("stock_cum_30_dxbb >", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_dxbb >=", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbLessThan(String value) {
            addCriterion("stock_cum_30_dxbb <", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_dxbb <=", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbLike(String value) {
            addCriterion("stock_cum_30_dxbb like", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbNotLike(String value) {
            addCriterion("stock_cum_30_dxbb not like", value, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbIn(List<String> values) {
            addCriterion("stock_cum_30_dxbb in", values, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbNotIn(List<String> values) {
            addCriterion("stock_cum_30_dxbb not in", values, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbBetween(String value1, String value2) {
            addCriterion("stock_cum_30_dxbb between", value1, value2, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxbbNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_dxbb not between", value1, value2, "stockCum30Dxbb");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpIsNull() {
            addCriterion("stock_cum_30_dxxp is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpIsNotNull() {
            addCriterion("stock_cum_30_dxxp is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpEqualTo(String value) {
            addCriterion("stock_cum_30_dxxp =", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpNotEqualTo(String value) {
            addCriterion("stock_cum_30_dxxp <>", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpGreaterThan(String value) {
            addCriterion("stock_cum_30_dxxp >", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_dxxp >=", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpLessThan(String value) {
            addCriterion("stock_cum_30_dxxp <", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_dxxp <=", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpLike(String value) {
            addCriterion("stock_cum_30_dxxp like", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpNotLike(String value) {
            addCriterion("stock_cum_30_dxxp not like", value, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpIn(List<String> values) {
            addCriterion("stock_cum_30_dxxp in", values, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpNotIn(List<String> values) {
            addCriterion("stock_cum_30_dxxp not in", values, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpBetween(String value1, String value2) {
            addCriterion("stock_cum_30_dxxp between", value1, value2, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DxxpNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_dxxp not between", value1, value2, "stockCum30Dxxp");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdIsNull() {
            addCriterion("stock_cum_30_dd is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdIsNotNull() {
            addCriterion("stock_cum_30_dd is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdEqualTo(String value) {
            addCriterion("stock_cum_30_dd =", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdNotEqualTo(String value) {
            addCriterion("stock_cum_30_dd <>", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdGreaterThan(String value) {
            addCriterion("stock_cum_30_dd >", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_dd >=", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdLessThan(String value) {
            addCriterion("stock_cum_30_dd <", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_dd <=", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdLike(String value) {
            addCriterion("stock_cum_30_dd like", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdNotLike(String value) {
            addCriterion("stock_cum_30_dd not like", value, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdIn(List<String> values) {
            addCriterion("stock_cum_30_dd in", values, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdNotIn(List<String> values) {
            addCriterion("stock_cum_30_dd not in", values, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdBetween(String value1, String value2) {
            addCriterion("stock_cum_30_dd between", value1, value2, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30DdNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_dd not between", value1, value2, "stockCum30Dd");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxIsNull() {
            addCriterion("stock_cum_30_jt_qy_dx is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxIsNotNull() {
            addCriterion("stock_cum_30_jt_qy_dx is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxEqualTo(String value) {
            addCriterion("stock_cum_30_jt_qy_dx =", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxNotEqualTo(String value) {
            addCriterion("stock_cum_30_jt_qy_dx <>", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxGreaterThan(String value) {
            addCriterion("stock_cum_30_jt_qy_dx >", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_jt_qy_dx >=", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxLessThan(String value) {
            addCriterion("stock_cum_30_jt_qy_dx <", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_jt_qy_dx <=", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxLike(String value) {
            addCriterion("stock_cum_30_jt_qy_dx like", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxNotLike(String value) {
            addCriterion("stock_cum_30_jt_qy_dx not like", value, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxIn(List<String> values) {
            addCriterion("stock_cum_30_jt_qy_dx in", values, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxNotIn(List<String> values) {
            addCriterion("stock_cum_30_jt_qy_dx not in", values, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxBetween(String value1, String value2) {
            addCriterion("stock_cum_30_jt_qy_dx between", value1, value2, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30JtQyDxNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_jt_qy_dx not between", value1, value2, "stockCum30JtQyDx");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdIsNull() {
            addCriterion("stock_cum_30_no_dd is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdIsNotNull() {
            addCriterion("stock_cum_30_no_dd is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdEqualTo(String value) {
            addCriterion("stock_cum_30_no_dd =", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdNotEqualTo(String value) {
            addCriterion("stock_cum_30_no_dd <>", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdGreaterThan(String value) {
            addCriterion("stock_cum_30_no_dd >", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_no_dd >=", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdLessThan(String value) {
            addCriterion("stock_cum_30_no_dd <", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_no_dd <=", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdLike(String value) {
            addCriterion("stock_cum_30_no_dd like", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdNotLike(String value) {
            addCriterion("stock_cum_30_no_dd not like", value, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdIn(List<String> values) {
            addCriterion("stock_cum_30_no_dd in", values, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdNotIn(List<String> values) {
            addCriterion("stock_cum_30_no_dd not in", values, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdBetween(String value1, String value2) {
            addCriterion("stock_cum_30_no_dd between", value1, value2, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NoDdNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_no_dd not between", value1, value2, "stockCum30NoDd");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewIsNull() {
            addCriterion("stock_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewIsNotNull() {
            addCriterion("stock_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewEqualTo(String value) {
            addCriterion("stock_cum_30_new =", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewNotEqualTo(String value) {
            addCriterion("stock_cum_30_new <>", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewGreaterThan(String value) {
            addCriterion("stock_cum_30_new >", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_new >=", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewLessThan(String value) {
            addCriterion("stock_cum_30_new <", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_new <=", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewLike(String value) {
            addCriterion("stock_cum_30_new like", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewNotLike(String value) {
            addCriterion("stock_cum_30_new not like", value, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewIn(List<String> values) {
            addCriterion("stock_cum_30_new in", values, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewNotIn(List<String> values) {
            addCriterion("stock_cum_30_new not in", values, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewBetween(String value1, String value2) {
            addCriterion("stock_cum_30_new between", value1, value2, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30NewNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_new not between", value1, value2, "stockCum30New");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsIsNull() {
            addCriterion("stock_cum_30_js is null");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsIsNotNull() {
            addCriterion("stock_cum_30_js is not null");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsEqualTo(String value) {
            addCriterion("stock_cum_30_js =", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsNotEqualTo(String value) {
            addCriterion("stock_cum_30_js <>", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsGreaterThan(String value) {
            addCriterion("stock_cum_30_js >", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsGreaterThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_js >=", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsLessThan(String value) {
            addCriterion("stock_cum_30_js <", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsLessThanOrEqualTo(String value) {
            addCriterion("stock_cum_30_js <=", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsLike(String value) {
            addCriterion("stock_cum_30_js like", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsNotLike(String value) {
            addCriterion("stock_cum_30_js not like", value, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsIn(List<String> values) {
            addCriterion("stock_cum_30_js in", values, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsNotIn(List<String> values) {
            addCriterion("stock_cum_30_js not in", values, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsBetween(String value1, String value2) {
            addCriterion("stock_cum_30_js between", value1, value2, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andStockCum30JsNotBetween(String value1, String value2) {
            addCriterion("stock_cum_30_js not between", value1, value2, "stockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewIsNull() {
            addCriterion("avg_stock_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewIsNotNull() {
            addCriterion("avg_stock_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewEqualTo(String value) {
            addCriterion("avg_stock_cum_30_new =", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewNotEqualTo(String value) {
            addCriterion("avg_stock_cum_30_new <>", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewGreaterThan(String value) {
            addCriterion("avg_stock_cum_30_new >", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("avg_stock_cum_30_new >=", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewLessThan(String value) {
            addCriterion("avg_stock_cum_30_new <", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewLessThanOrEqualTo(String value) {
            addCriterion("avg_stock_cum_30_new <=", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewLike(String value) {
            addCriterion("avg_stock_cum_30_new like", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewNotLike(String value) {
            addCriterion("avg_stock_cum_30_new not like", value, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewIn(List<String> values) {
            addCriterion("avg_stock_cum_30_new in", values, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewNotIn(List<String> values) {
            addCriterion("avg_stock_cum_30_new not in", values, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewBetween(String value1, String value2) {
            addCriterion("avg_stock_cum_30_new between", value1, value2, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30NewNotBetween(String value1, String value2) {
            addCriterion("avg_stock_cum_30_new not between", value1, value2, "avgStockCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsIsNull() {
            addCriterion("avg_stock_cum_30_js is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsIsNotNull() {
            addCriterion("avg_stock_cum_30_js is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsEqualTo(String value) {
            addCriterion("avg_stock_cum_30_js =", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsNotEqualTo(String value) {
            addCriterion("avg_stock_cum_30_js <>", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsGreaterThan(String value) {
            addCriterion("avg_stock_cum_30_js >", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsGreaterThanOrEqualTo(String value) {
            addCriterion("avg_stock_cum_30_js >=", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsLessThan(String value) {
            addCriterion("avg_stock_cum_30_js <", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsLessThanOrEqualTo(String value) {
            addCriterion("avg_stock_cum_30_js <=", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsLike(String value) {
            addCriterion("avg_stock_cum_30_js like", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsNotLike(String value) {
            addCriterion("avg_stock_cum_30_js not like", value, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsIn(List<String> values) {
            addCriterion("avg_stock_cum_30_js in", values, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsNotIn(List<String> values) {
            addCriterion("avg_stock_cum_30_js not in", values, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsBetween(String value1, String value2) {
            addCriterion("avg_stock_cum_30_js between", value1, value2, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgStockCum30JsNotBetween(String value1, String value2) {
            addCriterion("avg_stock_cum_30_js not between", value1, value2, "avgStockCum30Js");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzIsNull() {
            addCriterion("avg_sku_cnt_xz is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzIsNotNull() {
            addCriterion("avg_sku_cnt_xz is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzEqualTo(String value) {
            addCriterion("avg_sku_cnt_xz =", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzNotEqualTo(String value) {
            addCriterion("avg_sku_cnt_xz <>", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzGreaterThan(String value) {
            addCriterion("avg_sku_cnt_xz >", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_xz >=", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzLessThan(String value) {
            addCriterion("avg_sku_cnt_xz <", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_xz <=", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzLike(String value) {
            addCriterion("avg_sku_cnt_xz like", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzNotLike(String value) {
            addCriterion("avg_sku_cnt_xz not like", value, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzIn(List<String> values) {
            addCriterion("avg_sku_cnt_xz in", values, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzNotIn(List<String> values) {
            addCriterion("avg_sku_cnt_xz not in", values, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_xz between", value1, value2, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntXzNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_xz not between", value1, value2, "avgSkuCntXz");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsIsNull() {
            addCriterion("avg_sku_cnt_js is null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsIsNotNull() {
            addCriterion("avg_sku_cnt_js is not null");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsEqualTo(String value) {
            addCriterion("avg_sku_cnt_js =", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsNotEqualTo(String value) {
            addCriterion("avg_sku_cnt_js <>", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsGreaterThan(String value) {
            addCriterion("avg_sku_cnt_js >", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsGreaterThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_js >=", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsLessThan(String value) {
            addCriterion("avg_sku_cnt_js <", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsLessThanOrEqualTo(String value) {
            addCriterion("avg_sku_cnt_js <=", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsLike(String value) {
            addCriterion("avg_sku_cnt_js like", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsNotLike(String value) {
            addCriterion("avg_sku_cnt_js not like", value, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsIn(List<String> values) {
            addCriterion("avg_sku_cnt_js in", values, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsNotIn(List<String> values) {
            addCriterion("avg_sku_cnt_js not in", values, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_js between", value1, value2, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgSkuCntJsNotBetween(String value1, String value2) {
            addCriterion("avg_sku_cnt_js not between", value1, value2, "avgSkuCntJs");
            return (Criteria) this;
        }

        public Criteria andAvgCntIsNull() {
            addCriterion("avg_cnt is null");
            return (Criteria) this;
        }

        public Criteria andAvgCntIsNotNull() {
            addCriterion("avg_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andAvgCntEqualTo(String value) {
            addCriterion("avg_cnt =", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotEqualTo(String value) {
            addCriterion("avg_cnt <>", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntGreaterThan(String value) {
            addCriterion("avg_cnt >", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntGreaterThanOrEqualTo(String value) {
            addCriterion("avg_cnt >=", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntLessThan(String value) {
            addCriterion("avg_cnt <", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntLessThanOrEqualTo(String value) {
            addCriterion("avg_cnt <=", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntLike(String value) {
            addCriterion("avg_cnt like", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotLike(String value) {
            addCriterion("avg_cnt not like", value, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntIn(List<String> values) {
            addCriterion("avg_cnt in", values, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotIn(List<String> values) {
            addCriterion("avg_cnt not in", values, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntBetween(String value1, String value2) {
            addCriterion("avg_cnt between", value1, value2, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAvgCntNotBetween(String value1, String value2) {
            addCriterion("avg_cnt not between", value1, value2, "avgCnt");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullIsNull() {
            addCriterion("amt_cum_30_null is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullIsNotNull() {
            addCriterion("amt_cum_30_null is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullEqualTo(String value) {
            addCriterion("amt_cum_30_null =", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotEqualTo(String value) {
            addCriterion("amt_cum_30_null <>", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullGreaterThan(String value) {
            addCriterion("amt_cum_30_null >", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_null >=", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullLessThan(String value) {
            addCriterion("amt_cum_30_null <", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_null <=", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullLike(String value) {
            addCriterion("amt_cum_30_null like", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotLike(String value) {
            addCriterion("amt_cum_30_null not like", value, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullIn(List<String> values) {
            addCriterion("amt_cum_30_null in", values, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotIn(List<String> values) {
            addCriterion("amt_cum_30_null not in", values, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullBetween(String value1, String value2) {
            addCriterion("amt_cum_30_null between", value1, value2, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NullNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_null not between", value1, value2, "amtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullIsNull() {
            addCriterion("avg_amt_cum_30_null is null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullIsNotNull() {
            addCriterion("avg_amt_cum_30_null is not null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null =", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null <>", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullGreaterThan(String value) {
            addCriterion("avg_amt_cum_30_null >", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullGreaterThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null >=", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullLessThan(String value) {
            addCriterion("avg_amt_cum_30_null <", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullLessThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_null <=", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullLike(String value) {
            addCriterion("avg_amt_cum_30_null like", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotLike(String value) {
            addCriterion("avg_amt_cum_30_null not like", value, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullIn(List<String> values) {
            addCriterion("avg_amt_cum_30_null in", values, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotIn(List<String> values) {
            addCriterion("avg_amt_cum_30_null not in", values, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_null between", value1, value2, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NullNotBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_null not between", value1, value2, "avgAmtCum30Null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewIsNull() {
            addCriterion("amt_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewIsNotNull() {
            addCriterion("amt_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewEqualTo(String value) {
            addCriterion("amt_cum_30_new =", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotEqualTo(String value) {
            addCriterion("amt_cum_30_new <>", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewGreaterThan(String value) {
            addCriterion("amt_cum_30_new >", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_new >=", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewLessThan(String value) {
            addCriterion("amt_cum_30_new <", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_new <=", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewLike(String value) {
            addCriterion("amt_cum_30_new like", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotLike(String value) {
            addCriterion("amt_cum_30_new not like", value, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewIn(List<String> values) {
            addCriterion("amt_cum_30_new in", values, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotIn(List<String> values) {
            addCriterion("amt_cum_30_new not in", values, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewBetween(String value1, String value2) {
            addCriterion("amt_cum_30_new between", value1, value2, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAmtCum30NewNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_new not between", value1, value2, "amtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewIsNull() {
            addCriterion("avg_amt_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewIsNotNull() {
            addCriterion("avg_amt_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new =", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new <>", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewGreaterThan(String value) {
            addCriterion("avg_amt_cum_30_new >", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new >=", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewLessThan(String value) {
            addCriterion("avg_amt_cum_30_new <", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewLessThanOrEqualTo(String value) {
            addCriterion("avg_amt_cum_30_new <=", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewLike(String value) {
            addCriterion("avg_amt_cum_30_new like", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotLike(String value) {
            addCriterion("avg_amt_cum_30_new not like", value, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewIn(List<String> values) {
            addCriterion("avg_amt_cum_30_new in", values, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotIn(List<String> values) {
            addCriterion("avg_amt_cum_30_new not in", values, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_new between", value1, value2, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andAvgAmtCum30NewNotBetween(String value1, String value2) {
            addCriterion("avg_amt_cum_30_new not between", value1, value2, "avgAmtCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewIsNull() {
            addCriterion("profit_cum_30_new is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewIsNotNull() {
            addCriterion("profit_cum_30_new is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewEqualTo(String value) {
            addCriterion("profit_cum_30_new =", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotEqualTo(String value) {
            addCriterion("profit_cum_30_new <>", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewGreaterThan(String value) {
            addCriterion("profit_cum_30_new >", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_new >=", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewLessThan(String value) {
            addCriterion("profit_cum_30_new <", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_new <=", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewLike(String value) {
            addCriterion("profit_cum_30_new like", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotLike(String value) {
            addCriterion("profit_cum_30_new not like", value, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewIn(List<String> values) {
            addCriterion("profit_cum_30_new in", values, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotIn(List<String> values) {
            addCriterion("profit_cum_30_new not in", values, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewBetween(String value1, String value2) {
            addCriterion("profit_cum_30_new between", value1, value2, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30NewNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_new not between", value1, value2, "profitCum30New");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewIsNull() {
            addCriterion("profit_cum_30_rate_new is null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewIsNotNull() {
            addCriterion("profit_cum_30_rate_new is not null");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new =", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new <>", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewGreaterThan(String value) {
            addCriterion("profit_cum_30_rate_new >", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewGreaterThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new >=", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewLessThan(String value) {
            addCriterion("profit_cum_30_rate_new <", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewLessThanOrEqualTo(String value) {
            addCriterion("profit_cum_30_rate_new <=", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewLike(String value) {
            addCriterion("profit_cum_30_rate_new like", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotLike(String value) {
            addCriterion("profit_cum_30_rate_new not like", value, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewIn(List<String> values) {
            addCriterion("profit_cum_30_rate_new in", values, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotIn(List<String> values) {
            addCriterion("profit_cum_30_rate_new not in", values, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_new between", value1, value2, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andProfitCum30RateNewNotBetween(String value1, String value2) {
            addCriterion("profit_cum_30_rate_new not between", value1, value2, "profitCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewIsNull() {
            addCriterion("amt_cum_30_rate_new is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewIsNotNull() {
            addCriterion("amt_cum_30_rate_new is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new =", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new <>", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_new >", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new >=", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewLessThan(String value) {
            addCriterion("amt_cum_30_rate_new <", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new <=", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewLike(String value) {
            addCriterion("amt_cum_30_rate_new like", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotLike(String value) {
            addCriterion("amt_cum_30_rate_new not like", value, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new in", values, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new not in", values, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new between", value1, value2, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new not between", value1, value2, "amtCum30RateNew");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpIsNull() {
            addCriterion("amt_cum_30_rate_new_nodtp is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpIsNotNull() {
            addCriterion("amt_cum_30_rate_new_nodtp is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp =", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp <>", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp >", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp >=", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpLessThan(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp <", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp <=", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpLike(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp like", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpNotLike(String value) {
            addCriterion("amt_cum_30_rate_new_nodtp not like", value, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new_nodtp in", values, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new_nodtp not in", values, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new_nodtp between", value1, value2, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNodtpNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new_nodtp not between", value1, value2, "amtCum30RateNewNodtp");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyIsNull() {
            addCriterion("amt_cum_30_rate_new_nozy is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyIsNotNull() {
            addCriterion("amt_cum_30_rate_new_nozy is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nozy =", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyNotEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nozy <>", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyGreaterThan(String value) {
            addCriterion("amt_cum_30_rate_new_nozy >", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyGreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nozy >=", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyLessThan(String value) {
            addCriterion("amt_cum_30_rate_new_nozy <", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyLessThanOrEqualTo(String value) {
            addCriterion("amt_cum_30_rate_new_nozy <=", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyLike(String value) {
            addCriterion("amt_cum_30_rate_new_nozy like", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyNotLike(String value) {
            addCriterion("amt_cum_30_rate_new_nozy not like", value, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new_nozy in", values, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyNotIn(List<String> values) {
            addCriterion("amt_cum_30_rate_new_nozy not in", values, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new_nozy between", value1, value2, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andAmtCum30RateNewNozyNotBetween(String value1, String value2) {
            addCriterion("amt_cum_30_rate_new_nozy not between", value1, value2, "amtCum30RateNewNozy");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}