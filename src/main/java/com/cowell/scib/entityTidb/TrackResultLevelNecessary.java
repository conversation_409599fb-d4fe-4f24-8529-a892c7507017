package com.cowell.scib.entityTidb;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 六级必备目录
 */
public class TrackResultLevelNecessary implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务Id
     */
    private Long taskId;

    /**
     * 必备层级 2.平台必备 3.企业必备 4.店型必备 5.店型选配 6.单店必备
     */
    private Integer level;

    /**
     * 平台ID
     */
    private String platOrgid;

    /**
     * 企业ID
     */
    private String compid;

    /**
     * 城市
     */
    private String city;

    /**
     * 店型
     */
    private String storeGroup;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 商品SKU
     */
    private String goodsId;

    /**
     * 入选原因
     */
    private String bak;

    /**
     * 门店集中度
     */
    private String storeConcentration;

    /**
     * 门店动销率
     */
    private String storeSaleRate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPlatOrgid() {
        return platOrgid;
    }

    public void setPlatOrgid(String platOrgid) {
        this.platOrgid = platOrgid;
    }

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStoreGroup() {
        return storeGroup;
    }

    public void setStoreGroup(String storeGroup) {
        this.storeGroup = storeGroup;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getBak() {
        return bak;
    }

    public void setBak(String bak) {
        this.bak = bak;
    }

    public String getStoreConcentration() {
        return storeConcentration;
    }

    public void setStoreConcentration(String storeConcentration) {
        this.storeConcentration = storeConcentration;
    }

    public String getStoreSaleRate() {
        return storeSaleRate;
    }

    public void setStoreSaleRate(String storeSaleRate) {
        this.storeSaleRate = storeSaleRate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TrackResultLevelNecessary other = (TrackResultLevelNecessary) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getLevel() == null ? other.getLevel() == null : this.getLevel().equals(other.getLevel()))
            && (this.getPlatOrgid() == null ? other.getPlatOrgid() == null : this.getPlatOrgid().equals(other.getPlatOrgid()))
            && (this.getCompid() == null ? other.getCompid() == null : this.getCompid().equals(other.getCompid()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getStoreGroup() == null ? other.getStoreGroup() == null : this.getStoreGroup().equals(other.getStoreGroup()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getGoodsId() == null ? other.getGoodsId() == null : this.getGoodsId().equals(other.getGoodsId()))
            && (this.getBak() == null ? other.getBak() == null : this.getBak().equals(other.getBak()))
            && (this.getStoreConcentration() == null ? other.getStoreConcentration() == null : this.getStoreConcentration().equals(other.getStoreConcentration()))
            && (this.getStoreSaleRate() == null ? other.getStoreSaleRate() == null : this.getStoreSaleRate().equals(other.getStoreSaleRate()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getLevel() == null) ? 0 : getLevel().hashCode());
        result = prime * result + ((getPlatOrgid() == null) ? 0 : getPlatOrgid().hashCode());
        result = prime * result + ((getCompid() == null) ? 0 : getCompid().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getStoreGroup() == null) ? 0 : getStoreGroup().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getGoodsId() == null) ? 0 : getGoodsId().hashCode());
        result = prime * result + ((getBak() == null) ? 0 : getBak().hashCode());
        result = prime * result + ((getStoreConcentration() == null) ? 0 : getStoreConcentration().hashCode());
        result = prime * result + ((getStoreSaleRate() == null) ? 0 : getStoreSaleRate().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", level=").append(level);
        sb.append(", platOrgid=").append(platOrgid);
        sb.append(", compid=").append(compid);
        sb.append(", city=").append(city);
        sb.append(", storeGroup=").append(storeGroup);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", bak=").append(bak);
        sb.append(", storeConcentration=").append(storeConcentration);
        sb.append(", storeSaleRate=").append(storeSaleRate);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}