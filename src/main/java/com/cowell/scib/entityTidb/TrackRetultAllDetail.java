package com.cowell.scib.entityTidb;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.converters.string.StringNumberConverter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import java.io.Serializable;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TrackRetultAllDetail implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 任务ID
     */
    @ExcelIgnore
    private Long taskId;

    /**
     * 平台
     */
    @ExcelProperty("平台")
    private String zoneNew;

    /**
     * 平台orgid
     */
    @ExcelProperty("平台orgid")
    private String platOrgid;

    /**
     * 企业名称
     */
    @ExcelProperty("企业名称")
    private String dataFrom;

    /**
     * 企业orgid
     */
    @ExcelProperty("企业orgid")
    private String compid;

    /**
     * 管理主体
     */
    @ExcelProperty("管理主体")
    private String dataFromV2;

    /**
     * 城市
     */
    @ExcelProperty("城市")
    private String city;

    /**
     * 门店MDM编码
     */
    @ExcelProperty("门店MDM编码")
    private String orgNo;

    /**
     * 门店名称
     */
    @ExcelProperty("门店名称")
    private String storeName;

    /**
     * 店型
     */
    @ExcelProperty("店型")
    private String reviseStoreGroup;

    /**
     * 门店月销售额等级
     */
    @ExcelProperty("门店月销售额等级")
    private String saleslevel;

    /**
     * 门店选址商圈店型
     */
    @ExcelProperty("门店选址商圈店型")
    private String tradingarea;

    /**
     * 门店中参店型
     */
    @ExcelProperty("门店中参店型")
    private String storeGroupZy;

    /**
     * SKU
     */
    @ExcelProperty("SKU")
    private String goodsId;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String goodsname;

    /**
     * 必备层级
     */
    @ExcelProperty("必备层级")
    private String level;

    /**
     * 引入原因
     */
    @ExcelProperty("引入原因")
    private String bak;

    /**
     * 子类id
     */
    @ExcelProperty("子类id")
    private String subCategoryId;

    /**
     * 该品种在该门店当前的经营状态
     */
    @ExcelProperty("该品种在该门店当前的经营状态")
    private String remark;

    /**
     * 新/旧成分新品
     */
    @ExcelProperty("新/旧成分新品")
    private String newIndBak;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String goodsunit;

    /**
     * 规格
     */
    @ExcelProperty("规格")
    private String goodsspec;

    /**
     * 剂型
     */
    @ExcelProperty("剂型")
    private String jxCate1Name;

    /**
     * 厂家名称
     */
    @ExcelProperty("厂家名称")
    private String manufacturer;

    /**
     * 大类
     */
    @ExcelProperty("大类")
    private String classoneName;

    /**
     * 中类
     */
    @ExcelProperty("中类")
    private String classtwoName;

    /**
     * 小类
     */
    @ExcelProperty("小类")
    private String classthreeName;

    /**
     * 子类
     */
    @ExcelProperty("子类")
    private String classfourName;

    /**
     * 成分
     */
    @ExcelProperty("成分")
    private String component;

    /**
     * 处方类别
     */
    @ExcelProperty("处方类别")
    private String isOtc;

    /**
     * 疾病种
     */
    @ExcelProperty("疾病种")
    private String flagDisease;

    /**
     * 采购属性
     */
    @ExcelProperty("采购属性")
    private String grossprofit;

    /**
     * 经营属性
     */
    @ExcelProperty("经营属性")
    private String taotaiType;

    /**
     * 销售属性
     */
    @ExcelProperty("销售属性")
    private String stjb;

    /**
     * 特殊销售属性
     */
    @ExcelProperty("特殊销售属性")
    private String specialattributesType;

    /**
     * 参考零售价
     */
    @ExcelProperty("参考零售价")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String retailPrice;


    /**
     * 店型在库率
     */
    @ExcelProperty("店型在库率")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String inStockRate;

    /**
     * 店型90天动销率
     */
    @ExcelProperty(value = "店型90天动销率", converter = StringNumberConverter.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String inSalesRate;

    /**
     * 成分必备SKU(平台+企业)
     */
    @ExcelProperty("成分必备SKU(平台+企业)")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String skuInt;

    /**
     * 库存增长额
     */
    @ExcelProperty("库存增长额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String costAmt;

    /**
     * 门店现有库存数
     */
    @ExcelProperty("门店现有库存数")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String stallNoNum;

    /**
     * 挂网是否考核（只看非中参的统采品）
     */
    @ExcelProperty("挂网是否考核（只看非中参的统采品）")
    private String phOrgBzFlag;

    /**
     * 门店类型
     */
    @ExcelProperty("门店类型")
    private String storeType;

    /**
     * 前90天销售数量
     */
    @ExcelProperty("原始_前90天销售数量")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String numCum90;

    /**
     * 前90天销售金额
     */
    @ExcelProperty("原始_前90天销售金额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum90;

    /**
     * 前90天毛利额
     */
    @ExcelProperty("原始_前90天毛利额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitAmtCum90;

    /**
     * 前90天毛利率
     */
    @ExcelProperty("原始_前90天毛利率")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String profitRate90;

    /**
     * 前90天销售占比（单品/门店总销售）
     */
    @ExcelProperty("前90天销售占比（单品/门店总销售）")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String amtRateOrg90;

    /**
     * 前90天成分销售占比（单品/门店成分销售）
     */
    @ExcelProperty("前90天成分销售占比（单品/门店成分销售）")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String amtRateComp90;

    /**
     * 前180天销售数量
     */
    @ExcelProperty("原始_前180天销售数量")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String numCum180;

    /**
     * 前180天销售金额
     */
    @ExcelProperty("原始_前180天销售金额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String amtCum180;

    /**
     * 前180天毛利额
     */
    @ExcelProperty("原始_前180天毛利额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 1)
    @NumberFormat(value = "0", roundingMode = RoundingMode.HALF_UP)
    private String profitAmtCum180;

    /**
     * 前180天毛利率
     */
    @ExcelProperty("原始_前180天毛利率")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String profitRate180;

    /**
     * 前180天销售占比（单品/门店总销售）
     */
    @ExcelProperty("前180天销售占比（单品/门店总销售）")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String amtRateOrg180;

    /**
     * 前180天成分销售占比（单品/门店成分销售）
     */
    @ExcelProperty("前180天成分销售占比（单品/门店成分销售）")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String amtRateComp180;

    /**
     * 模型使用_前90天销售数量
     */
    @ExcelProperty("模型使用_前90天销售数量")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseNumCum90;

    /**
     * 模型使用_前90天销售金额
     */
    @ExcelProperty("模型使用_前90天销售金额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseAmtCum90;

    /**
     * 模型使用_前90天毛利额
     */
    @ExcelProperty("模型使用_前90天毛利额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseProfitAmtCum90;

    /**
     * 模型使用_前90天毛利率
     */
    @ExcelProperty("模型使用_前90天毛利率")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseProfitRate90;

    /**
     * 模型使用_前180天销售数量
     */
    @ExcelProperty("模型使用_前180天销售数量")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseNumCum180;

    /**
     * 模型使用_前180天销售金额
     */
    @ExcelProperty("模型使用_前180天销售金额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseAmtCum180;

    /**
     * 模型使用_前180天毛利额
     */
    @ExcelProperty("模型使用_前180天毛利额")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseProfitAmtCum180;

    /**
     * 模型使用_前180天毛利率
     */
    @ExcelProperty("模型使用_前180天毛利率")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT, dataFormat = 9)
    @NumberFormat(value = "#%", roundingMode = RoundingMode.HALF_UP)
    private String reviseProfitRate180;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date gmtCreate;

    /**
     * 状态(-1删除，0正常)
     */
    @ExcelIgnore
    private Byte status;

    /**
     * 必备级别一店一目层级写6
     */
    @ExcelIgnore
    private String necessaryTag;


    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getZoneNew() {
        return zoneNew;
    }

    public void setZoneNew(String zoneNew) {
        this.zoneNew = zoneNew;
    }

    public String getPlatOrgid() {
        return platOrgid;
    }

    public void setPlatOrgid(String platOrgid) {
        this.platOrgid = platOrgid;
    }

    public String getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(String dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getDataFromV2() {
        return dataFromV2;
    }

    public void setDataFromV2(String dataFromV2) {
        this.dataFromV2 = dataFromV2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getReviseStoreGroup() {
        return reviseStoreGroup;
    }

    public void setReviseStoreGroup(String reviseStoreGroup) {
        this.reviseStoreGroup = reviseStoreGroup;
    }

    public String getSaleslevel() {
        return saleslevel;
    }

    public void setSaleslevel(String saleslevel) {
        this.saleslevel = saleslevel;
    }

    public String getTradingarea() {
        return tradingarea;
    }

    public void setTradingarea(String tradingarea) {
        this.tradingarea = tradingarea;
    }

    public String getStoreGroupZy() {
        return storeGroupZy;
    }

    public void setStoreGroupZy(String storeGroupZy) {
        this.storeGroupZy = storeGroupZy;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getBak() {
        return bak;
    }

    public void setBak(String bak) {
        this.bak = bak;
    }

    public String getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(String subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNewIndBak() {
        return newIndBak;
    }

    public void setNewIndBak(String newIndBak) {
        this.newIndBak = newIndBak;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getJxCate1Name() {
        return jxCate1Name;
    }

    public void setJxCate1Name(String jxCate1Name) {
        this.jxCate1Name = jxCate1Name;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getClassoneName() {
        return classoneName;
    }

    public void setClassoneName(String classoneName) {
        this.classoneName = classoneName;
    }

    public String getClasstwoName() {
        return classtwoName;
    }

    public void setClasstwoName(String classtwoName) {
        this.classtwoName = classtwoName;
    }

    public String getClassthreeName() {
        return classthreeName;
    }

    public void setClassthreeName(String classthreeName) {
        this.classthreeName = classthreeName;
    }

    public String getClassfourName() {
        return classfourName;
    }

    public void setClassfourName(String classfourName) {
        this.classfourName = classfourName;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getIsOtc() {
        return isOtc;
    }

    public void setIsOtc(String isOtc) {
        this.isOtc = isOtc;
    }

    public String getFlagDisease() {
        return flagDisease;
    }

    public void setFlagDisease(String flagDisease) {
        this.flagDisease = flagDisease;
    }

    public String getGrossprofit() {
        return grossprofit;
    }

    public void setGrossprofit(String grossprofit) {
        this.grossprofit = grossprofit;
    }

    public String getTaotaiType() {
        return taotaiType;
    }

    public void setTaotaiType(String taotaiType) {
        this.taotaiType = taotaiType;
    }

    public String getStjb() {
        return stjb;
    }

    public void setStjb(String stjb) {
        this.stjb = stjb;
    }

    public String getSpecialattributesType() {
        return specialattributesType;
    }

    public void setSpecialattributesType(String specialattributesType) {
        this.specialattributesType = specialattributesType;
    }

    public String getInStockRate() {
        return inStockRate;
    }

    public void setInStockRate(String inStockRate) {
        this.inStockRate = inStockRate;
    }

    public String getInSalesRate() {
        return inSalesRate;
    }

    public void setInSalesRate(String inSalesRate) {
        this.inSalesRate = inSalesRate;
    }

    public String getSkuInt() {
        return skuInt;
    }

    public void setSkuInt(String skuInt) {
        this.skuInt = skuInt;
    }

    public String getCostAmt() {
        return costAmt;
    }

    public void setCostAmt(String costAmt) {
        this.costAmt = costAmt;
    }

    public String getStallNoNum() {
        return stallNoNum;
    }

    public void setStallNoNum(String stallNoNum) {
        this.stallNoNum = stallNoNum;
    }

    public String getPhOrgBzFlag() {
        return phOrgBzFlag;
    }

    public void setPhOrgBzFlag(String phOrgBzFlag) {
        this.phOrgBzFlag = phOrgBzFlag;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getNumCum90() {
        return numCum90;
    }

    public void setNumCum90(String numCum90) {
        this.numCum90 = numCum90;
    }

    public String getAmtCum90() {
        return amtCum90;
    }

    public void setAmtCum90(String amtCum90) {
        this.amtCum90 = amtCum90;
    }

    public String getProfitAmtCum90() {
        return profitAmtCum90;
    }

    public void setProfitAmtCum90(String profitAmtCum90) {
        this.profitAmtCum90 = profitAmtCum90;
    }

    public String getProfitRate90() {
        return profitRate90;
    }

    public void setProfitRate90(String profitRate90) {
        this.profitRate90 = profitRate90;
    }

    public String getAmtRateOrg90() {
        return amtRateOrg90;
    }

    public void setAmtRateOrg90(String amtRateOrg90) {
        this.amtRateOrg90 = amtRateOrg90;
    }

    public String getAmtRateComp90() {
        return amtRateComp90;
    }

    public void setAmtRateComp90(String amtRateComp90) {
        this.amtRateComp90 = amtRateComp90;
    }

    public String getNumCum180() {
        return numCum180;
    }

    public void setNumCum180(String numCum180) {
        this.numCum180 = numCum180;
    }

    public String getAmtCum180() {
        return amtCum180;
    }

    public void setAmtCum180(String amtCum180) {
        this.amtCum180 = amtCum180;
    }

    public String getProfitAmtCum180() {
        return profitAmtCum180;
    }

    public void setProfitAmtCum180(String profitAmtCum180) {
        this.profitAmtCum180 = profitAmtCum180;
    }

    public String getProfitRate180() {
        return profitRate180;
    }

    public void setProfitRate180(String profitRate180) {
        this.profitRate180 = profitRate180;
    }

    public String getAmtRateOrg180() {
        return amtRateOrg180;
    }

    public void setAmtRateOrg180(String amtRateOrg180) {
        this.amtRateOrg180 = amtRateOrg180;
    }

    public String getAmtRateComp180() {
        return amtRateComp180;
    }

    public void setAmtRateComp180(String amtRateComp180) {
        this.amtRateComp180 = amtRateComp180;
    }

    public String getReviseNumCum90() {
        return reviseNumCum90;
    }

    public void setReviseNumCum90(String reviseNumCum90) {
        this.reviseNumCum90 = reviseNumCum90;
    }

    public String getReviseAmtCum90() {
        return reviseAmtCum90;
    }

    public void setReviseAmtCum90(String reviseAmtCum90) {
        this.reviseAmtCum90 = reviseAmtCum90;
    }

    public String getReviseProfitAmtCum90() {
        return reviseProfitAmtCum90;
    }

    public void setReviseProfitAmtCum90(String reviseProfitAmtCum90) {
        this.reviseProfitAmtCum90 = reviseProfitAmtCum90;
    }

    public String getReviseProfitRate90() {
        return reviseProfitRate90;
    }

    public void setReviseProfitRate90(String reviseProfitRate90) {
        this.reviseProfitRate90 = reviseProfitRate90;
    }

    public String getReviseNumCum180() {
        return reviseNumCum180;
    }

    public void setReviseNumCum180(String reviseNumCum180) {
        this.reviseNumCum180 = reviseNumCum180;
    }

    public String getReviseAmtCum180() {
        return reviseAmtCum180;
    }

    public void setReviseAmtCum180(String reviseAmtCum180) {
        this.reviseAmtCum180 = reviseAmtCum180;
    }

    public String getReviseProfitAmtCum180() {
        return reviseProfitAmtCum180;
    }

    public void setReviseProfitAmtCum180(String reviseProfitAmtCum180) {
        this.reviseProfitAmtCum180 = reviseProfitAmtCum180;
    }

    public String getReviseProfitRate180() {
        return reviseProfitRate180;
    }

    public void setReviseProfitRate180(String reviseProfitRate180) {
        this.reviseProfitRate180 = reviseProfitRate180;
    }

    public String getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(String retailPrice) {
        this.retailPrice = retailPrice;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getNecessaryTag() {
        return necessaryTag;
    }

    public void setNecessaryTag(String necessaryTag) {
        this.necessaryTag = necessaryTag;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TrackRetultAllDetail other = (TrackRetultAllDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getZoneNew() == null ? other.getZoneNew() == null : this.getZoneNew().equals(other.getZoneNew()))
            && (this.getPlatOrgid() == null ? other.getPlatOrgid() == null : this.getPlatOrgid().equals(other.getPlatOrgid()))
            && (this.getDataFrom() == null ? other.getDataFrom() == null : this.getDataFrom().equals(other.getDataFrom()))
            && (this.getCompid() == null ? other.getCompid() == null : this.getCompid().equals(other.getCompid()))
            && (this.getDataFromV2() == null ? other.getDataFromV2() == null : this.getDataFromV2().equals(other.getDataFromV2()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getOrgNo() == null ? other.getOrgNo() == null : this.getOrgNo().equals(other.getOrgNo()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getReviseStoreGroup() == null ? other.getReviseStoreGroup() == null : this.getReviseStoreGroup().equals(other.getReviseStoreGroup()))
            && (this.getSaleslevel() == null ? other.getSaleslevel() == null : this.getSaleslevel().equals(other.getSaleslevel()))
            && (this.getTradingarea() == null ? other.getTradingarea() == null : this.getTradingarea().equals(other.getTradingarea()))
            && (this.getStoreGroupZy() == null ? other.getStoreGroupZy() == null : this.getStoreGroupZy().equals(other.getStoreGroupZy()))
            && (this.getGoodsId() == null ? other.getGoodsId() == null : this.getGoodsId().equals(other.getGoodsId()))
            && (this.getGoodsname() == null ? other.getGoodsname() == null : this.getGoodsname().equals(other.getGoodsname()))
            && (this.getLevel() == null ? other.getLevel() == null : this.getLevel().equals(other.getLevel()))
            && (this.getBak() == null ? other.getBak() == null : this.getBak().equals(other.getBak()))
            && (this.getSubCategoryId() == null ? other.getSubCategoryId() == null : this.getSubCategoryId().equals(other.getSubCategoryId()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getNewIndBak() == null ? other.getNewIndBak() == null : this.getNewIndBak().equals(other.getNewIndBak()))
            && (this.getGoodsunit() == null ? other.getGoodsunit() == null : this.getGoodsunit().equals(other.getGoodsunit()))
            && (this.getGoodsspec() == null ? other.getGoodsspec() == null : this.getGoodsspec().equals(other.getGoodsspec()))
            && (this.getJxCate1Name() == null ? other.getJxCate1Name() == null : this.getJxCate1Name().equals(other.getJxCate1Name()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getClassoneName() == null ? other.getClassoneName() == null : this.getClassoneName().equals(other.getClassoneName()))
            && (this.getClasstwoName() == null ? other.getClasstwoName() == null : this.getClasstwoName().equals(other.getClasstwoName()))
            && (this.getClassthreeName() == null ? other.getClassthreeName() == null : this.getClassthreeName().equals(other.getClassthreeName()))
            && (this.getClassfourName() == null ? other.getClassfourName() == null : this.getClassfourName().equals(other.getClassfourName()))
            && (this.getComponent() == null ? other.getComponent() == null : this.getComponent().equals(other.getComponent()))
            && (this.getIsOtc() == null ? other.getIsOtc() == null : this.getIsOtc().equals(other.getIsOtc()))
            && (this.getFlagDisease() == null ? other.getFlagDisease() == null : this.getFlagDisease().equals(other.getFlagDisease()))
            && (this.getGrossprofit() == null ? other.getGrossprofit() == null : this.getGrossprofit().equals(other.getGrossprofit()))
            && (this.getTaotaiType() == null ? other.getTaotaiType() == null : this.getTaotaiType().equals(other.getTaotaiType()))
            && (this.getStjb() == null ? other.getStjb() == null : this.getStjb().equals(other.getStjb()))
            && (this.getSpecialattributesType() == null ? other.getSpecialattributesType() == null : this.getSpecialattributesType().equals(other.getSpecialattributesType()))
            && (this.getInStockRate() == null ? other.getInStockRate() == null : this.getInStockRate().equals(other.getInStockRate()))
            && (this.getInSalesRate() == null ? other.getInSalesRate() == null : this.getInSalesRate().equals(other.getInSalesRate()))
            && (this.getSkuInt() == null ? other.getSkuInt() == null : this.getSkuInt().equals(other.getSkuInt()))
            && (this.getCostAmt() == null ? other.getCostAmt() == null : this.getCostAmt().equals(other.getCostAmt()))
            && (this.getStallNoNum() == null ? other.getStallNoNum() == null : this.getStallNoNum().equals(other.getStallNoNum()))
            && (this.getPhOrgBzFlag() == null ? other.getPhOrgBzFlag() == null : this.getPhOrgBzFlag().equals(other.getPhOrgBzFlag()))
            && (this.getStoreType() == null ? other.getStoreType() == null : this.getStoreType().equals(other.getStoreType()))
            && (this.getNumCum90() == null ? other.getNumCum90() == null : this.getNumCum90().equals(other.getNumCum90()))
            && (this.getAmtCum90() == null ? other.getAmtCum90() == null : this.getAmtCum90().equals(other.getAmtCum90()))
            && (this.getProfitAmtCum90() == null ? other.getProfitAmtCum90() == null : this.getProfitAmtCum90().equals(other.getProfitAmtCum90()))
            && (this.getProfitRate90() == null ? other.getProfitRate90() == null : this.getProfitRate90().equals(other.getProfitRate90()))
            && (this.getAmtRateOrg90() == null ? other.getAmtRateOrg90() == null : this.getAmtRateOrg90().equals(other.getAmtRateOrg90()))
            && (this.getAmtRateComp90() == null ? other.getAmtRateComp90() == null : this.getAmtRateComp90().equals(other.getAmtRateComp90()))
            && (this.getNumCum180() == null ? other.getNumCum180() == null : this.getNumCum180().equals(other.getNumCum180()))
            && (this.getAmtCum180() == null ? other.getAmtCum180() == null : this.getAmtCum180().equals(other.getAmtCum180()))
            && (this.getProfitAmtCum180() == null ? other.getProfitAmtCum180() == null : this.getProfitAmtCum180().equals(other.getProfitAmtCum180()))
            && (this.getProfitRate180() == null ? other.getProfitRate180() == null : this.getProfitRate180().equals(other.getProfitRate180()))
            && (this.getAmtRateOrg180() == null ? other.getAmtRateOrg180() == null : this.getAmtRateOrg180().equals(other.getAmtRateOrg180()))
            && (this.getAmtRateComp180() == null ? other.getAmtRateComp180() == null : this.getAmtRateComp180().equals(other.getAmtRateComp180()))
            && (this.getReviseNumCum90() == null ? other.getReviseNumCum90() == null : this.getReviseNumCum90().equals(other.getReviseNumCum90()))
            && (this.getReviseAmtCum90() == null ? other.getReviseAmtCum90() == null : this.getReviseAmtCum90().equals(other.getReviseAmtCum90()))
            && (this.getReviseProfitAmtCum90() == null ? other.getReviseProfitAmtCum90() == null : this.getReviseProfitAmtCum90().equals(other.getReviseProfitAmtCum90()))
            && (this.getReviseProfitRate90() == null ? other.getReviseProfitRate90() == null : this.getReviseProfitRate90().equals(other.getReviseProfitRate90()))
            && (this.getReviseNumCum180() == null ? other.getReviseNumCum180() == null : this.getReviseNumCum180().equals(other.getReviseNumCum180()))
            && (this.getReviseAmtCum180() == null ? other.getReviseAmtCum180() == null : this.getReviseAmtCum180().equals(other.getReviseAmtCum180()))
            && (this.getReviseProfitAmtCum180() == null ? other.getReviseProfitAmtCum180() == null : this.getReviseProfitAmtCum180().equals(other.getReviseProfitAmtCum180()))
            && (this.getReviseProfitRate180() == null ? other.getReviseProfitRate180() == null : this.getReviseProfitRate180().equals(other.getReviseProfitRate180()))
            && (this.getRetailPrice() == null ? other.getRetailPrice() == null : this.getRetailPrice().equals(other.getRetailPrice()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getZoneNew() == null) ? 0 : getZoneNew().hashCode());
        result = prime * result + ((getPlatOrgid() == null) ? 0 : getPlatOrgid().hashCode());
        result = prime * result + ((getDataFrom() == null) ? 0 : getDataFrom().hashCode());
        result = prime * result + ((getCompid() == null) ? 0 : getCompid().hashCode());
        result = prime * result + ((getDataFromV2() == null) ? 0 : getDataFromV2().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getOrgNo() == null) ? 0 : getOrgNo().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getReviseStoreGroup() == null) ? 0 : getReviseStoreGroup().hashCode());
        result = prime * result + ((getSaleslevel() == null) ? 0 : getSaleslevel().hashCode());
        result = prime * result + ((getTradingarea() == null) ? 0 : getTradingarea().hashCode());
        result = prime * result + ((getStoreGroupZy() == null) ? 0 : getStoreGroupZy().hashCode());
        result = prime * result + ((getGoodsId() == null) ? 0 : getGoodsId().hashCode());
        result = prime * result + ((getGoodsname() == null) ? 0 : getGoodsname().hashCode());
        result = prime * result + ((getLevel() == null) ? 0 : getLevel().hashCode());
        result = prime * result + ((getBak() == null) ? 0 : getBak().hashCode());
        result = prime * result + ((getSubCategoryId() == null) ? 0 : getSubCategoryId().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getNewIndBak() == null) ? 0 : getNewIndBak().hashCode());
        result = prime * result + ((getGoodsunit() == null) ? 0 : getGoodsunit().hashCode());
        result = prime * result + ((getGoodsspec() == null) ? 0 : getGoodsspec().hashCode());
        result = prime * result + ((getJxCate1Name() == null) ? 0 : getJxCate1Name().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getClassoneName() == null) ? 0 : getClassoneName().hashCode());
        result = prime * result + ((getClasstwoName() == null) ? 0 : getClasstwoName().hashCode());
        result = prime * result + ((getClassthreeName() == null) ? 0 : getClassthreeName().hashCode());
        result = prime * result + ((getClassfourName() == null) ? 0 : getClassfourName().hashCode());
        result = prime * result + ((getComponent() == null) ? 0 : getComponent().hashCode());
        result = prime * result + ((getIsOtc() == null) ? 0 : getIsOtc().hashCode());
        result = prime * result + ((getFlagDisease() == null) ? 0 : getFlagDisease().hashCode());
        result = prime * result + ((getGrossprofit() == null) ? 0 : getGrossprofit().hashCode());
        result = prime * result + ((getTaotaiType() == null) ? 0 : getTaotaiType().hashCode());
        result = prime * result + ((getStjb() == null) ? 0 : getStjb().hashCode());
        result = prime * result + ((getSpecialattributesType() == null) ? 0 : getSpecialattributesType().hashCode());
        result = prime * result + ((getInStockRate() == null) ? 0 : getInStockRate().hashCode());
        result = prime * result + ((getInSalesRate() == null) ? 0 : getInSalesRate().hashCode());
        result = prime * result + ((getSkuInt() == null) ? 0 : getSkuInt().hashCode());
        result = prime * result + ((getCostAmt() == null) ? 0 : getCostAmt().hashCode());
        result = prime * result + ((getStallNoNum() == null) ? 0 : getStallNoNum().hashCode());
        result = prime * result + ((getPhOrgBzFlag() == null) ? 0 : getPhOrgBzFlag().hashCode());
        result = prime * result + ((getStoreType() == null) ? 0 : getStoreType().hashCode());
        result = prime * result + ((getNumCum90() == null) ? 0 : getNumCum90().hashCode());
        result = prime * result + ((getAmtCum90() == null) ? 0 : getAmtCum90().hashCode());
        result = prime * result + ((getProfitAmtCum90() == null) ? 0 : getProfitAmtCum90().hashCode());
        result = prime * result + ((getProfitRate90() == null) ? 0 : getProfitRate90().hashCode());
        result = prime * result + ((getAmtRateOrg90() == null) ? 0 : getAmtRateOrg90().hashCode());
        result = prime * result + ((getAmtRateComp90() == null) ? 0 : getAmtRateComp90().hashCode());
        result = prime * result + ((getNumCum180() == null) ? 0 : getNumCum180().hashCode());
        result = prime * result + ((getAmtCum180() == null) ? 0 : getAmtCum180().hashCode());
        result = prime * result + ((getProfitAmtCum180() == null) ? 0 : getProfitAmtCum180().hashCode());
        result = prime * result + ((getProfitRate180() == null) ? 0 : getProfitRate180().hashCode());
        result = prime * result + ((getAmtRateOrg180() == null) ? 0 : getAmtRateOrg180().hashCode());
        result = prime * result + ((getAmtRateComp180() == null) ? 0 : getAmtRateComp180().hashCode());
        result = prime * result + ((getReviseNumCum90() == null) ? 0 : getReviseNumCum90().hashCode());
        result = prime * result + ((getReviseAmtCum90() == null) ? 0 : getReviseAmtCum90().hashCode());
        result = prime * result + ((getReviseProfitAmtCum90() == null) ? 0 : getReviseProfitAmtCum90().hashCode());
        result = prime * result + ((getReviseProfitRate90() == null) ? 0 : getReviseProfitRate90().hashCode());
        result = prime * result + ((getReviseNumCum180() == null) ? 0 : getReviseNumCum180().hashCode());
        result = prime * result + ((getReviseAmtCum180() == null) ? 0 : getReviseAmtCum180().hashCode());
        result = prime * result + ((getReviseProfitAmtCum180() == null) ? 0 : getReviseProfitAmtCum180().hashCode());
        result = prime * result + ((getReviseProfitRate180() == null) ? 0 : getReviseProfitRate180().hashCode());
        result = prime * result + ((getRetailPrice() == null) ? 0 : getRetailPrice().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", zoneNew=").append(zoneNew);
        sb.append(", platOrgid=").append(platOrgid);
        sb.append(", dataFrom=").append(dataFrom);
        sb.append(", compid=").append(compid);
        sb.append(", dataFromV2=").append(dataFromV2);
        sb.append(", city=").append(city);
        sb.append(", orgNo=").append(orgNo);
        sb.append(", storeName=").append(storeName);
        sb.append(", reviseStoreGroup=").append(reviseStoreGroup);
        sb.append(", saleslevel=").append(saleslevel);
        sb.append(", tradingarea=").append(tradingarea);
        sb.append(", storeGroupZy=").append(storeGroupZy);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsname=").append(goodsname);
        sb.append(", level=").append(level);
        sb.append(", bak=").append(bak);
        sb.append(", subCategoryId=").append(subCategoryId);
        sb.append(", remark=").append(remark);
        sb.append(", newIndBak=").append(newIndBak);
        sb.append(", goodsunit=").append(goodsunit);
        sb.append(", goodsspec=").append(goodsspec);
        sb.append(", jxCate1Name=").append(jxCate1Name);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", classoneName=").append(classoneName);
        sb.append(", classtwoName=").append(classtwoName);
        sb.append(", classthreeName=").append(classthreeName);
        sb.append(", classfourName=").append(classfourName);
        sb.append(", component=").append(component);
        sb.append(", isOtc=").append(isOtc);
        sb.append(", flagDisease=").append(flagDisease);
        sb.append(", grossprofit=").append(grossprofit);
        sb.append(", taotaiType=").append(taotaiType);
        sb.append(", stjb=").append(stjb);
        sb.append(", specialattributesType=").append(specialattributesType);
        sb.append(", inStockRate=").append(inStockRate);
        sb.append(", inSalesRate=").append(inSalesRate);
        sb.append(", skuInt=").append(skuInt);
        sb.append(", costAmt=").append(costAmt);
        sb.append(", stallNoNum=").append(stallNoNum);
        sb.append(", phOrgBzFlag=").append(phOrgBzFlag);
        sb.append(", storeType=").append(storeType);
        sb.append(", numCum90=").append(numCum90);
        sb.append(", amtCum90=").append(amtCum90);
        sb.append(", profitAmtCum90=").append(profitAmtCum90);
        sb.append(", profitRate90=").append(profitRate90);
        sb.append(", amtRateOrg90=").append(amtRateOrg90);
        sb.append(", amtRateComp90=").append(amtRateComp90);
        sb.append(", numCum180=").append(numCum180);
        sb.append(", amtCum180=").append(amtCum180);
        sb.append(", profitAmtCum180=").append(profitAmtCum180);
        sb.append(", profitRate180=").append(profitRate180);
        sb.append(", amtRateOrg180=").append(amtRateOrg180);
        sb.append(", amtRateComp180=").append(amtRateComp180);
        sb.append(", reviseNumCum90=").append(reviseNumCum90);
        sb.append(", reviseAmtCum90=").append(reviseAmtCum90);
        sb.append(", reviseProfitAmtCum90=").append(reviseProfitAmtCum90);
        sb.append(", reviseProfitRate90=").append(reviseProfitRate90);
        sb.append(", reviseNumCum180=").append(reviseNumCum180);
        sb.append(", reviseAmtCum180=").append(reviseAmtCum180);
        sb.append(", reviseProfitAmtCum180=").append(reviseProfitAmtCum180);
        sb.append(", reviseProfitRate180=").append(reviseProfitRate180);
        sb.append(", retailPrice=").append(retailPrice);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", status=").append(status);
        sb.append(", necessaryTag=").append(necessaryTag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
