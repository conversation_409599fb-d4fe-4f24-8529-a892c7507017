package com.cowell.scib.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrackRetultAllDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TrackRetultAllDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNull() {
            addCriterion("zone_new is null");
            return (Criteria) this;
        }

        public Criteria andZoneNewIsNotNull() {
            addCriterion("zone_new is not null");
            return (Criteria) this;
        }

        public Criteria andZoneNewEqualTo(String value) {
            addCriterion("zone_new =", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotEqualTo(String value) {
            addCriterion("zone_new <>", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThan(String value) {
            addCriterion("zone_new >", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewGreaterThanOrEqualTo(String value) {
            addCriterion("zone_new >=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThan(String value) {
            addCriterion("zone_new <", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLessThanOrEqualTo(String value) {
            addCriterion("zone_new <=", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewLike(String value) {
            addCriterion("zone_new like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotLike(String value) {
            addCriterion("zone_new not like", value, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewIn(List<String> values) {
            addCriterion("zone_new in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotIn(List<String> values) {
            addCriterion("zone_new not in", values, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewBetween(String value1, String value2) {
            addCriterion("zone_new between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andZoneNewNotBetween(String value1, String value2) {
            addCriterion("zone_new not between", value1, value2, "zoneNew");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidIsNull() {
            addCriterion("plat_orgid is null");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidIsNotNull() {
            addCriterion("plat_orgid is not null");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidEqualTo(String value) {
            addCriterion("plat_orgid =", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotEqualTo(String value) {
            addCriterion("plat_orgid <>", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidGreaterThan(String value) {
            addCriterion("plat_orgid >", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidGreaterThanOrEqualTo(String value) {
            addCriterion("plat_orgid >=", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidLessThan(String value) {
            addCriterion("plat_orgid <", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidLessThanOrEqualTo(String value) {
            addCriterion("plat_orgid <=", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidLike(String value) {
            addCriterion("plat_orgid like", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotLike(String value) {
            addCriterion("plat_orgid not like", value, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidIn(List<String> values) {
            addCriterion("plat_orgid in", values, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotIn(List<String> values) {
            addCriterion("plat_orgid not in", values, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidBetween(String value1, String value2) {
            addCriterion("plat_orgid between", value1, value2, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andPlatOrgidNotBetween(String value1, String value2) {
            addCriterion("plat_orgid not between", value1, value2, "platOrgid");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNull() {
            addCriterion("data_from is null");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNotNull() {
            addCriterion("data_from is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromEqualTo(String value) {
            addCriterion("data_from =", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotEqualTo(String value) {
            addCriterion("data_from <>", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThan(String value) {
            addCriterion("data_from >", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThanOrEqualTo(String value) {
            addCriterion("data_from >=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThan(String value) {
            addCriterion("data_from <", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThanOrEqualTo(String value) {
            addCriterion("data_from <=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLike(String value) {
            addCriterion("data_from like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotLike(String value) {
            addCriterion("data_from not like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromIn(List<String> values) {
            addCriterion("data_from in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotIn(List<String> values) {
            addCriterion("data_from not in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromBetween(String value1, String value2) {
            addCriterion("data_from between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotBetween(String value1, String value2) {
            addCriterion("data_from not between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andCompidIsNull() {
            addCriterion("compid is null");
            return (Criteria) this;
        }

        public Criteria andCompidIsNotNull() {
            addCriterion("compid is not null");
            return (Criteria) this;
        }

        public Criteria andCompidEqualTo(String value) {
            addCriterion("compid =", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotEqualTo(String value) {
            addCriterion("compid <>", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidGreaterThan(String value) {
            addCriterion("compid >", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidGreaterThanOrEqualTo(String value) {
            addCriterion("compid >=", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidLessThan(String value) {
            addCriterion("compid <", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidLessThanOrEqualTo(String value) {
            addCriterion("compid <=", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidLike(String value) {
            addCriterion("compid like", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotLike(String value) {
            addCriterion("compid not like", value, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidIn(List<String> values) {
            addCriterion("compid in", values, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotIn(List<String> values) {
            addCriterion("compid not in", values, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidBetween(String value1, String value2) {
            addCriterion("compid between", value1, value2, "compid");
            return (Criteria) this;
        }

        public Criteria andCompidNotBetween(String value1, String value2) {
            addCriterion("compid not between", value1, value2, "compid");
            return (Criteria) this;
        }

        public Criteria andDataFromV2IsNull() {
            addCriterion("data_from_v2 is null");
            return (Criteria) this;
        }

        public Criteria andDataFromV2IsNotNull() {
            addCriterion("data_from_v2 is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromV2EqualTo(String value) {
            addCriterion("data_from_v2 =", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2NotEqualTo(String value) {
            addCriterion("data_from_v2 <>", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2GreaterThan(String value) {
            addCriterion("data_from_v2 >", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2GreaterThanOrEqualTo(String value) {
            addCriterion("data_from_v2 >=", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2LessThan(String value) {
            addCriterion("data_from_v2 <", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2LessThanOrEqualTo(String value) {
            addCriterion("data_from_v2 <=", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2Like(String value) {
            addCriterion("data_from_v2 like", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2NotLike(String value) {
            addCriterion("data_from_v2 not like", value, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2In(List<String> values) {
            addCriterion("data_from_v2 in", values, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2NotIn(List<String> values) {
            addCriterion("data_from_v2 not in", values, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2Between(String value1, String value2) {
            addCriterion("data_from_v2 between", value1, value2, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andDataFromV2NotBetween(String value1, String value2) {
            addCriterion("data_from_v2 not between", value1, value2, "dataFromV2");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andOrgNoIsNull() {
            addCriterion("org_no is null");
            return (Criteria) this;
        }

        public Criteria andOrgNoIsNotNull() {
            addCriterion("org_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNoEqualTo(String value) {
            addCriterion("org_no =", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotEqualTo(String value) {
            addCriterion("org_no <>", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoGreaterThan(String value) {
            addCriterion("org_no >", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoGreaterThanOrEqualTo(String value) {
            addCriterion("org_no >=", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLessThan(String value) {
            addCriterion("org_no <", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLessThanOrEqualTo(String value) {
            addCriterion("org_no <=", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLike(String value) {
            addCriterion("org_no like", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotLike(String value) {
            addCriterion("org_no not like", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoIn(List<String> values) {
            addCriterion("org_no in", values, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotIn(List<String> values) {
            addCriterion("org_no not in", values, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoBetween(String value1, String value2) {
            addCriterion("org_no between", value1, value2, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotBetween(String value1, String value2) {
            addCriterion("org_no not between", value1, value2, "orgNo");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupIsNull() {
            addCriterion("revise_store_group is null");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupIsNotNull() {
            addCriterion("revise_store_group is not null");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupEqualTo(String value) {
            addCriterion("revise_store_group =", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotEqualTo(String value) {
            addCriterion("revise_store_group <>", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupGreaterThan(String value) {
            addCriterion("revise_store_group >", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupGreaterThanOrEqualTo(String value) {
            addCriterion("revise_store_group >=", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupLessThan(String value) {
            addCriterion("revise_store_group <", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupLessThanOrEqualTo(String value) {
            addCriterion("revise_store_group <=", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupLike(String value) {
            addCriterion("revise_store_group like", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotLike(String value) {
            addCriterion("revise_store_group not like", value, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupIn(List<String> values) {
            addCriterion("revise_store_group in", values, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotIn(List<String> values) {
            addCriterion("revise_store_group not in", values, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupBetween(String value1, String value2) {
            addCriterion("revise_store_group between", value1, value2, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andReviseStoreGroupNotBetween(String value1, String value2) {
            addCriterion("revise_store_group not between", value1, value2, "reviseStoreGroup");
            return (Criteria) this;
        }

        public Criteria andSaleslevelIsNull() {
            addCriterion("saleslevel is null");
            return (Criteria) this;
        }

        public Criteria andSaleslevelIsNotNull() {
            addCriterion("saleslevel is not null");
            return (Criteria) this;
        }

        public Criteria andSaleslevelEqualTo(String value) {
            addCriterion("saleslevel =", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelNotEqualTo(String value) {
            addCriterion("saleslevel <>", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelGreaterThan(String value) {
            addCriterion("saleslevel >", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelGreaterThanOrEqualTo(String value) {
            addCriterion("saleslevel >=", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelLessThan(String value) {
            addCriterion("saleslevel <", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelLessThanOrEqualTo(String value) {
            addCriterion("saleslevel <=", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelLike(String value) {
            addCriterion("saleslevel like", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelNotLike(String value) {
            addCriterion("saleslevel not like", value, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelIn(List<String> values) {
            addCriterion("saleslevel in", values, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelNotIn(List<String> values) {
            addCriterion("saleslevel not in", values, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelBetween(String value1, String value2) {
            addCriterion("saleslevel between", value1, value2, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andSaleslevelNotBetween(String value1, String value2) {
            addCriterion("saleslevel not between", value1, value2, "saleslevel");
            return (Criteria) this;
        }

        public Criteria andTradingareaIsNull() {
            addCriterion("tradingarea is null");
            return (Criteria) this;
        }

        public Criteria andTradingareaIsNotNull() {
            addCriterion("tradingarea is not null");
            return (Criteria) this;
        }

        public Criteria andTradingareaEqualTo(String value) {
            addCriterion("tradingarea =", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaNotEqualTo(String value) {
            addCriterion("tradingarea <>", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaGreaterThan(String value) {
            addCriterion("tradingarea >", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaGreaterThanOrEqualTo(String value) {
            addCriterion("tradingarea >=", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaLessThan(String value) {
            addCriterion("tradingarea <", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaLessThanOrEqualTo(String value) {
            addCriterion("tradingarea <=", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaLike(String value) {
            addCriterion("tradingarea like", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaNotLike(String value) {
            addCriterion("tradingarea not like", value, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaIn(List<String> values) {
            addCriterion("tradingarea in", values, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaNotIn(List<String> values) {
            addCriterion("tradingarea not in", values, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaBetween(String value1, String value2) {
            addCriterion("tradingarea between", value1, value2, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andTradingareaNotBetween(String value1, String value2) {
            addCriterion("tradingarea not between", value1, value2, "tradingarea");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyIsNull() {
            addCriterion("store_group_zy is null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyIsNotNull() {
            addCriterion("store_group_zy is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyEqualTo(String value) {
            addCriterion("store_group_zy =", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyNotEqualTo(String value) {
            addCriterion("store_group_zy <>", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyGreaterThan(String value) {
            addCriterion("store_group_zy >", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyGreaterThanOrEqualTo(String value) {
            addCriterion("store_group_zy >=", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyLessThan(String value) {
            addCriterion("store_group_zy <", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyLessThanOrEqualTo(String value) {
            addCriterion("store_group_zy <=", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyLike(String value) {
            addCriterion("store_group_zy like", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyNotLike(String value) {
            addCriterion("store_group_zy not like", value, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyIn(List<String> values) {
            addCriterion("store_group_zy in", values, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyNotIn(List<String> values) {
            addCriterion("store_group_zy not in", values, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyBetween(String value1, String value2) {
            addCriterion("store_group_zy between", value1, value2, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andStoreGroupZyNotBetween(String value1, String value2) {
            addCriterion("store_group_zy not between", value1, value2, "storeGroupZy");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("goods_id is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("goods_id is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(String value) {
            addCriterion("goods_id =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(String value) {
            addCriterion("goods_id <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(String value) {
            addCriterion("goods_id >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(String value) {
            addCriterion("goods_id >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(String value) {
            addCriterion("goods_id <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(String value) {
            addCriterion("goods_id <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLike(String value) {
            addCriterion("goods_id like", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotLike(String value) {
            addCriterion("goods_id not like", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<String> values) {
            addCriterion("goods_id in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<String> values) {
            addCriterion("goods_id not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(String value1, String value2) {
            addCriterion("goods_id between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(String value1, String value2) {
            addCriterion("goods_id not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsnameIsNull() {
            addCriterion("goodsname is null");
            return (Criteria) this;
        }

        public Criteria andGoodsnameIsNotNull() {
            addCriterion("goodsname is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsnameEqualTo(String value) {
            addCriterion("goodsname =", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotEqualTo(String value) {
            addCriterion("goodsname <>", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameGreaterThan(String value) {
            addCriterion("goodsname >", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameGreaterThanOrEqualTo(String value) {
            addCriterion("goodsname >=", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameLessThan(String value) {
            addCriterion("goodsname <", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameLessThanOrEqualTo(String value) {
            addCriterion("goodsname <=", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameLike(String value) {
            addCriterion("goodsname like", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotLike(String value) {
            addCriterion("goodsname not like", value, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameIn(List<String> values) {
            addCriterion("goodsname in", values, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotIn(List<String> values) {
            addCriterion("goodsname not in", values, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameBetween(String value1, String value2) {
            addCriterion("goodsname between", value1, value2, "goodsname");
            return (Criteria) this;
        }

        public Criteria andGoodsnameNotBetween(String value1, String value2) {
            addCriterion("goodsname not between", value1, value2, "goodsname");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("`level` is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("`level` is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(String value) {
            addCriterion("`level` =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(String value) {
            addCriterion("`level` <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(String value) {
            addCriterion("`level` >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(String value) {
            addCriterion("`level` >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(String value) {
            addCriterion("`level` <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(String value) {
            addCriterion("`level` <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLike(String value) {
            addCriterion("`level` like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotLike(String value) {
            addCriterion("`level` not like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<String> values) {
            addCriterion("`level` in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<String> values) {
            addCriterion("`level` not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(String value1, String value2) {
            addCriterion("`level` between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(String value1, String value2) {
            addCriterion("`level` not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andBakIsNull() {
            addCriterion("bak is null");
            return (Criteria) this;
        }

        public Criteria andBakIsNotNull() {
            addCriterion("bak is not null");
            return (Criteria) this;
        }

        public Criteria andBakEqualTo(String value) {
            addCriterion("bak =", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotEqualTo(String value) {
            addCriterion("bak <>", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakGreaterThan(String value) {
            addCriterion("bak >", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakGreaterThanOrEqualTo(String value) {
            addCriterion("bak >=", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakLessThan(String value) {
            addCriterion("bak <", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakLessThanOrEqualTo(String value) {
            addCriterion("bak <=", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakLike(String value) {
            addCriterion("bak like", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotLike(String value) {
            addCriterion("bak not like", value, "bak");
            return (Criteria) this;
        }

        public Criteria andBakIn(List<String> values) {
            addCriterion("bak in", values, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotIn(List<String> values) {
            addCriterion("bak not in", values, "bak");
            return (Criteria) this;
        }

        public Criteria andBakBetween(String value1, String value2) {
            addCriterion("bak between", value1, value2, "bak");
            return (Criteria) this;
        }

        public Criteria andBakNotBetween(String value1, String value2) {
            addCriterion("bak not between", value1, value2, "bak");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNull() {
            addCriterion("sub_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNotNull() {
            addCriterion("sub_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdEqualTo(String value) {
            addCriterion("sub_category_id =", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotEqualTo(String value) {
            addCriterion("sub_category_id <>", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThan(String value) {
            addCriterion("sub_category_id >", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_id >=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThan(String value) {
            addCriterion("sub_category_id <", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("sub_category_id <=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLike(String value) {
            addCriterion("sub_category_id like", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotLike(String value) {
            addCriterion("sub_category_id not like", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIn(List<String> values) {
            addCriterion("sub_category_id in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotIn(List<String> values) {
            addCriterion("sub_category_id not in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdBetween(String value1, String value2) {
            addCriterion("sub_category_id between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotBetween(String value1, String value2) {
            addCriterion("sub_category_id not between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andNewIndBakIsNull() {
            addCriterion("new_ind_bak is null");
            return (Criteria) this;
        }

        public Criteria andNewIndBakIsNotNull() {
            addCriterion("new_ind_bak is not null");
            return (Criteria) this;
        }

        public Criteria andNewIndBakEqualTo(String value) {
            addCriterion("new_ind_bak =", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakNotEqualTo(String value) {
            addCriterion("new_ind_bak <>", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakGreaterThan(String value) {
            addCriterion("new_ind_bak >", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakGreaterThanOrEqualTo(String value) {
            addCriterion("new_ind_bak >=", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakLessThan(String value) {
            addCriterion("new_ind_bak <", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakLessThanOrEqualTo(String value) {
            addCriterion("new_ind_bak <=", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakLike(String value) {
            addCriterion("new_ind_bak like", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakNotLike(String value) {
            addCriterion("new_ind_bak not like", value, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakIn(List<String> values) {
            addCriterion("new_ind_bak in", values, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakNotIn(List<String> values) {
            addCriterion("new_ind_bak not in", values, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakBetween(String value1, String value2) {
            addCriterion("new_ind_bak between", value1, value2, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andNewIndBakNotBetween(String value1, String value2) {
            addCriterion("new_ind_bak not between", value1, value2, "newIndBak");
            return (Criteria) this;
        }

        public Criteria andGoodsunitIsNull() {
            addCriterion("goodsunit is null");
            return (Criteria) this;
        }

        public Criteria andGoodsunitIsNotNull() {
            addCriterion("goodsunit is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsunitEqualTo(String value) {
            addCriterion("goodsunit =", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotEqualTo(String value) {
            addCriterion("goodsunit <>", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitGreaterThan(String value) {
            addCriterion("goodsunit >", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitGreaterThanOrEqualTo(String value) {
            addCriterion("goodsunit >=", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitLessThan(String value) {
            addCriterion("goodsunit <", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitLessThanOrEqualTo(String value) {
            addCriterion("goodsunit <=", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitLike(String value) {
            addCriterion("goodsunit like", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotLike(String value) {
            addCriterion("goodsunit not like", value, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitIn(List<String> values) {
            addCriterion("goodsunit in", values, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotIn(List<String> values) {
            addCriterion("goodsunit not in", values, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitBetween(String value1, String value2) {
            addCriterion("goodsunit between", value1, value2, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsunitNotBetween(String value1, String value2) {
            addCriterion("goodsunit not between", value1, value2, "goodsunit");
            return (Criteria) this;
        }

        public Criteria andGoodsspecIsNull() {
            addCriterion("goodsspec is null");
            return (Criteria) this;
        }

        public Criteria andGoodsspecIsNotNull() {
            addCriterion("goodsspec is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsspecEqualTo(String value) {
            addCriterion("goodsspec =", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotEqualTo(String value) {
            addCriterion("goodsspec <>", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecGreaterThan(String value) {
            addCriterion("goodsspec >", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecGreaterThanOrEqualTo(String value) {
            addCriterion("goodsspec >=", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecLessThan(String value) {
            addCriterion("goodsspec <", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecLessThanOrEqualTo(String value) {
            addCriterion("goodsspec <=", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecLike(String value) {
            addCriterion("goodsspec like", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotLike(String value) {
            addCriterion("goodsspec not like", value, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecIn(List<String> values) {
            addCriterion("goodsspec in", values, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotIn(List<String> values) {
            addCriterion("goodsspec not in", values, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecBetween(String value1, String value2) {
            addCriterion("goodsspec between", value1, value2, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andGoodsspecNotBetween(String value1, String value2) {
            addCriterion("goodsspec not between", value1, value2, "goodsspec");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameIsNull() {
            addCriterion("jx_cate1_name is null");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameIsNotNull() {
            addCriterion("jx_cate1_name is not null");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameEqualTo(String value) {
            addCriterion("jx_cate1_name =", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotEqualTo(String value) {
            addCriterion("jx_cate1_name <>", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameGreaterThan(String value) {
            addCriterion("jx_cate1_name >", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameGreaterThanOrEqualTo(String value) {
            addCriterion("jx_cate1_name >=", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameLessThan(String value) {
            addCriterion("jx_cate1_name <", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameLessThanOrEqualTo(String value) {
            addCriterion("jx_cate1_name <=", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameLike(String value) {
            addCriterion("jx_cate1_name like", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotLike(String value) {
            addCriterion("jx_cate1_name not like", value, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameIn(List<String> values) {
            addCriterion("jx_cate1_name in", values, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotIn(List<String> values) {
            addCriterion("jx_cate1_name not in", values, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameBetween(String value1, String value2) {
            addCriterion("jx_cate1_name between", value1, value2, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andJxCate1NameNotBetween(String value1, String value2) {
            addCriterion("jx_cate1_name not between", value1, value2, "jxCate1Name");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNull() {
            addCriterion("classone_name is null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIsNotNull() {
            addCriterion("classone_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassoneNameEqualTo(String value) {
            addCriterion("classone_name =", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotEqualTo(String value) {
            addCriterion("classone_name <>", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThan(String value) {
            addCriterion("classone_name >", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("classone_name >=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThan(String value) {
            addCriterion("classone_name <", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLessThanOrEqualTo(String value) {
            addCriterion("classone_name <=", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameLike(String value) {
            addCriterion("classone_name like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotLike(String value) {
            addCriterion("classone_name not like", value, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameIn(List<String> values) {
            addCriterion("classone_name in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotIn(List<String> values) {
            addCriterion("classone_name not in", values, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameBetween(String value1, String value2) {
            addCriterion("classone_name between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClassoneNameNotBetween(String value1, String value2) {
            addCriterion("classone_name not between", value1, value2, "classoneName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIsNull() {
            addCriterion("classtwo_name is null");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIsNotNull() {
            addCriterion("classtwo_name is not null");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameEqualTo(String value) {
            addCriterion("classtwo_name =", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotEqualTo(String value) {
            addCriterion("classtwo_name <>", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameGreaterThan(String value) {
            addCriterion("classtwo_name >", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameGreaterThanOrEqualTo(String value) {
            addCriterion("classtwo_name >=", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLessThan(String value) {
            addCriterion("classtwo_name <", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLessThanOrEqualTo(String value) {
            addCriterion("classtwo_name <=", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameLike(String value) {
            addCriterion("classtwo_name like", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotLike(String value) {
            addCriterion("classtwo_name not like", value, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameIn(List<String> values) {
            addCriterion("classtwo_name in", values, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotIn(List<String> values) {
            addCriterion("classtwo_name not in", values, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameBetween(String value1, String value2) {
            addCriterion("classtwo_name between", value1, value2, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClasstwoNameNotBetween(String value1, String value2) {
            addCriterion("classtwo_name not between", value1, value2, "classtwoName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIsNull() {
            addCriterion("classthree_name is null");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIsNotNull() {
            addCriterion("classthree_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameEqualTo(String value) {
            addCriterion("classthree_name =", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotEqualTo(String value) {
            addCriterion("classthree_name <>", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameGreaterThan(String value) {
            addCriterion("classthree_name >", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameGreaterThanOrEqualTo(String value) {
            addCriterion("classthree_name >=", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLessThan(String value) {
            addCriterion("classthree_name <", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLessThanOrEqualTo(String value) {
            addCriterion("classthree_name <=", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameLike(String value) {
            addCriterion("classthree_name like", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotLike(String value) {
            addCriterion("classthree_name not like", value, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameIn(List<String> values) {
            addCriterion("classthree_name in", values, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotIn(List<String> values) {
            addCriterion("classthree_name not in", values, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameBetween(String value1, String value2) {
            addCriterion("classthree_name between", value1, value2, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassthreeNameNotBetween(String value1, String value2) {
            addCriterion("classthree_name not between", value1, value2, "classthreeName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIsNull() {
            addCriterion("classfour_name is null");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIsNotNull() {
            addCriterion("classfour_name is not null");
            return (Criteria) this;
        }

        public Criteria andClassfourNameEqualTo(String value) {
            addCriterion("classfour_name =", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotEqualTo(String value) {
            addCriterion("classfour_name <>", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameGreaterThan(String value) {
            addCriterion("classfour_name >", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameGreaterThanOrEqualTo(String value) {
            addCriterion("classfour_name >=", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLessThan(String value) {
            addCriterion("classfour_name <", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLessThanOrEqualTo(String value) {
            addCriterion("classfour_name <=", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameLike(String value) {
            addCriterion("classfour_name like", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotLike(String value) {
            addCriterion("classfour_name not like", value, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameIn(List<String> values) {
            addCriterion("classfour_name in", values, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotIn(List<String> values) {
            addCriterion("classfour_name not in", values, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameBetween(String value1, String value2) {
            addCriterion("classfour_name between", value1, value2, "classfourName");
            return (Criteria) this;
        }

        public Criteria andClassfourNameNotBetween(String value1, String value2) {
            addCriterion("classfour_name not between", value1, value2, "classfourName");
            return (Criteria) this;
        }

        public Criteria andComponentIsNull() {
            addCriterion("component is null");
            return (Criteria) this;
        }

        public Criteria andComponentIsNotNull() {
            addCriterion("component is not null");
            return (Criteria) this;
        }

        public Criteria andComponentEqualTo(String value) {
            addCriterion("component =", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotEqualTo(String value) {
            addCriterion("component <>", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThan(String value) {
            addCriterion("component >", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThanOrEqualTo(String value) {
            addCriterion("component >=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThan(String value) {
            addCriterion("component <", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThanOrEqualTo(String value) {
            addCriterion("component <=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLike(String value) {
            addCriterion("component like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotLike(String value) {
            addCriterion("component not like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentIn(List<String> values) {
            addCriterion("component in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotIn(List<String> values) {
            addCriterion("component not in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentBetween(String value1, String value2) {
            addCriterion("component between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotBetween(String value1, String value2) {
            addCriterion("component not between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andIsOtcIsNull() {
            addCriterion("is_otc is null");
            return (Criteria) this;
        }

        public Criteria andIsOtcIsNotNull() {
            addCriterion("is_otc is not null");
            return (Criteria) this;
        }

        public Criteria andIsOtcEqualTo(String value) {
            addCriterion("is_otc =", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcNotEqualTo(String value) {
            addCriterion("is_otc <>", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcGreaterThan(String value) {
            addCriterion("is_otc >", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcGreaterThanOrEqualTo(String value) {
            addCriterion("is_otc >=", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcLessThan(String value) {
            addCriterion("is_otc <", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcLessThanOrEqualTo(String value) {
            addCriterion("is_otc <=", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcLike(String value) {
            addCriterion("is_otc like", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcNotLike(String value) {
            addCriterion("is_otc not like", value, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcIn(List<String> values) {
            addCriterion("is_otc in", values, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcNotIn(List<String> values) {
            addCriterion("is_otc not in", values, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcBetween(String value1, String value2) {
            addCriterion("is_otc between", value1, value2, "isOtc");
            return (Criteria) this;
        }

        public Criteria andIsOtcNotBetween(String value1, String value2) {
            addCriterion("is_otc not between", value1, value2, "isOtc");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseIsNull() {
            addCriterion("flag_disease is null");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseIsNotNull() {
            addCriterion("flag_disease is not null");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseEqualTo(String value) {
            addCriterion("flag_disease =", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotEqualTo(String value) {
            addCriterion("flag_disease <>", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseGreaterThan(String value) {
            addCriterion("flag_disease >", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseGreaterThanOrEqualTo(String value) {
            addCriterion("flag_disease >=", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseLessThan(String value) {
            addCriterion("flag_disease <", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseLessThanOrEqualTo(String value) {
            addCriterion("flag_disease <=", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseLike(String value) {
            addCriterion("flag_disease like", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotLike(String value) {
            addCriterion("flag_disease not like", value, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseIn(List<String> values) {
            addCriterion("flag_disease in", values, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotIn(List<String> values) {
            addCriterion("flag_disease not in", values, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseBetween(String value1, String value2) {
            addCriterion("flag_disease between", value1, value2, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andFlagDiseaseNotBetween(String value1, String value2) {
            addCriterion("flag_disease not between", value1, value2, "flagDisease");
            return (Criteria) this;
        }

        public Criteria andGrossprofitIsNull() {
            addCriterion("grossprofit is null");
            return (Criteria) this;
        }

        public Criteria andGrossprofitIsNotNull() {
            addCriterion("grossprofit is not null");
            return (Criteria) this;
        }

        public Criteria andGrossprofitEqualTo(String value) {
            addCriterion("grossprofit =", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotEqualTo(String value) {
            addCriterion("grossprofit <>", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitGreaterThan(String value) {
            addCriterion("grossprofit >", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitGreaterThanOrEqualTo(String value) {
            addCriterion("grossprofit >=", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitLessThan(String value) {
            addCriterion("grossprofit <", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitLessThanOrEqualTo(String value) {
            addCriterion("grossprofit <=", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitLike(String value) {
            addCriterion("grossprofit like", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotLike(String value) {
            addCriterion("grossprofit not like", value, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitIn(List<String> values) {
            addCriterion("grossprofit in", values, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotIn(List<String> values) {
            addCriterion("grossprofit not in", values, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitBetween(String value1, String value2) {
            addCriterion("grossprofit between", value1, value2, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andGrossprofitNotBetween(String value1, String value2) {
            addCriterion("grossprofit not between", value1, value2, "grossprofit");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeIsNull() {
            addCriterion("taotai_type is null");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeIsNotNull() {
            addCriterion("taotai_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeEqualTo(String value) {
            addCriterion("taotai_type =", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotEqualTo(String value) {
            addCriterion("taotai_type <>", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeGreaterThan(String value) {
            addCriterion("taotai_type >", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeGreaterThanOrEqualTo(String value) {
            addCriterion("taotai_type >=", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeLessThan(String value) {
            addCriterion("taotai_type <", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeLessThanOrEqualTo(String value) {
            addCriterion("taotai_type <=", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeLike(String value) {
            addCriterion("taotai_type like", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotLike(String value) {
            addCriterion("taotai_type not like", value, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeIn(List<String> values) {
            addCriterion("taotai_type in", values, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotIn(List<String> values) {
            addCriterion("taotai_type not in", values, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeBetween(String value1, String value2) {
            addCriterion("taotai_type between", value1, value2, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andTaotaiTypeNotBetween(String value1, String value2) {
            addCriterion("taotai_type not between", value1, value2, "taotaiType");
            return (Criteria) this;
        }

        public Criteria andStjbIsNull() {
            addCriterion("stjb is null");
            return (Criteria) this;
        }

        public Criteria andStjbIsNotNull() {
            addCriterion("stjb is not null");
            return (Criteria) this;
        }

        public Criteria andStjbEqualTo(String value) {
            addCriterion("stjb =", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotEqualTo(String value) {
            addCriterion("stjb <>", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbGreaterThan(String value) {
            addCriterion("stjb >", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbGreaterThanOrEqualTo(String value) {
            addCriterion("stjb >=", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbLessThan(String value) {
            addCriterion("stjb <", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbLessThanOrEqualTo(String value) {
            addCriterion("stjb <=", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbLike(String value) {
            addCriterion("stjb like", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotLike(String value) {
            addCriterion("stjb not like", value, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbIn(List<String> values) {
            addCriterion("stjb in", values, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotIn(List<String> values) {
            addCriterion("stjb not in", values, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbBetween(String value1, String value2) {
            addCriterion("stjb between", value1, value2, "stjb");
            return (Criteria) this;
        }

        public Criteria andStjbNotBetween(String value1, String value2) {
            addCriterion("stjb not between", value1, value2, "stjb");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeIsNull() {
            addCriterion("specialattributes_type is null");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeIsNotNull() {
            addCriterion("specialattributes_type is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeEqualTo(String value) {
            addCriterion("specialattributes_type =", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeNotEqualTo(String value) {
            addCriterion("specialattributes_type <>", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeGreaterThan(String value) {
            addCriterion("specialattributes_type >", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeGreaterThanOrEqualTo(String value) {
            addCriterion("specialattributes_type >=", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeLessThan(String value) {
            addCriterion("specialattributes_type <", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeLessThanOrEqualTo(String value) {
            addCriterion("specialattributes_type <=", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeLike(String value) {
            addCriterion("specialattributes_type like", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeNotLike(String value) {
            addCriterion("specialattributes_type not like", value, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeIn(List<String> values) {
            addCriterion("specialattributes_type in", values, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeNotIn(List<String> values) {
            addCriterion("specialattributes_type not in", values, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeBetween(String value1, String value2) {
            addCriterion("specialattributes_type between", value1, value2, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesTypeNotBetween(String value1, String value2) {
            addCriterion("specialattributes_type not between", value1, value2, "specialattributesType");
            return (Criteria) this;
        }

        public Criteria andInStockRateIsNull() {
            addCriterion("in_stock_rate is null");
            return (Criteria) this;
        }

        public Criteria andInStockRateIsNotNull() {
            addCriterion("in_stock_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInStockRateEqualTo(String value) {
            addCriterion("in_stock_rate =", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateNotEqualTo(String value) {
            addCriterion("in_stock_rate <>", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateGreaterThan(String value) {
            addCriterion("in_stock_rate >", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateGreaterThanOrEqualTo(String value) {
            addCriterion("in_stock_rate >=", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateLessThan(String value) {
            addCriterion("in_stock_rate <", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateLessThanOrEqualTo(String value) {
            addCriterion("in_stock_rate <=", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateLike(String value) {
            addCriterion("in_stock_rate like", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateNotLike(String value) {
            addCriterion("in_stock_rate not like", value, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateIn(List<String> values) {
            addCriterion("in_stock_rate in", values, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateNotIn(List<String> values) {
            addCriterion("in_stock_rate not in", values, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateBetween(String value1, String value2) {
            addCriterion("in_stock_rate between", value1, value2, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInStockRateNotBetween(String value1, String value2) {
            addCriterion("in_stock_rate not between", value1, value2, "inStockRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateIsNull() {
            addCriterion("in_sales_rate is null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateIsNotNull() {
            addCriterion("in_sales_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInSalesRateEqualTo(String value) {
            addCriterion("in_sales_rate =", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateNotEqualTo(String value) {
            addCriterion("in_sales_rate <>", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateGreaterThan(String value) {
            addCriterion("in_sales_rate >", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateGreaterThanOrEqualTo(String value) {
            addCriterion("in_sales_rate >=", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateLessThan(String value) {
            addCriterion("in_sales_rate <", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateLessThanOrEqualTo(String value) {
            addCriterion("in_sales_rate <=", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateLike(String value) {
            addCriterion("in_sales_rate like", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateNotLike(String value) {
            addCriterion("in_sales_rate not like", value, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateIn(List<String> values) {
            addCriterion("in_sales_rate in", values, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateNotIn(List<String> values) {
            addCriterion("in_sales_rate not in", values, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateBetween(String value1, String value2) {
            addCriterion("in_sales_rate between", value1, value2, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andInSalesRateNotBetween(String value1, String value2) {
            addCriterion("in_sales_rate not between", value1, value2, "inSalesRate");
            return (Criteria) this;
        }

        public Criteria andSkuIntIsNull() {
            addCriterion("sku_int is null");
            return (Criteria) this;
        }

        public Criteria andSkuIntIsNotNull() {
            addCriterion("sku_int is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIntEqualTo(String value) {
            addCriterion("sku_int =", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntNotEqualTo(String value) {
            addCriterion("sku_int <>", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntGreaterThan(String value) {
            addCriterion("sku_int >", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntGreaterThanOrEqualTo(String value) {
            addCriterion("sku_int >=", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntLessThan(String value) {
            addCriterion("sku_int <", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntLessThanOrEqualTo(String value) {
            addCriterion("sku_int <=", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntLike(String value) {
            addCriterion("sku_int like", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntNotLike(String value) {
            addCriterion("sku_int not like", value, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntIn(List<String> values) {
            addCriterion("sku_int in", values, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntNotIn(List<String> values) {
            addCriterion("sku_int not in", values, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntBetween(String value1, String value2) {
            addCriterion("sku_int between", value1, value2, "skuInt");
            return (Criteria) this;
        }

        public Criteria andSkuIntNotBetween(String value1, String value2) {
            addCriterion("sku_int not between", value1, value2, "skuInt");
            return (Criteria) this;
        }

        public Criteria andCostAmtIsNull() {
            addCriterion("cost_amt is null");
            return (Criteria) this;
        }

        public Criteria andCostAmtIsNotNull() {
            addCriterion("cost_amt is not null");
            return (Criteria) this;
        }

        public Criteria andCostAmtEqualTo(String value) {
            addCriterion("cost_amt =", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtNotEqualTo(String value) {
            addCriterion("cost_amt <>", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtGreaterThan(String value) {
            addCriterion("cost_amt >", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtGreaterThanOrEqualTo(String value) {
            addCriterion("cost_amt >=", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtLessThan(String value) {
            addCriterion("cost_amt <", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtLessThanOrEqualTo(String value) {
            addCriterion("cost_amt <=", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtLike(String value) {
            addCriterion("cost_amt like", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtNotLike(String value) {
            addCriterion("cost_amt not like", value, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtIn(List<String> values) {
            addCriterion("cost_amt in", values, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtNotIn(List<String> values) {
            addCriterion("cost_amt not in", values, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtBetween(String value1, String value2) {
            addCriterion("cost_amt between", value1, value2, "costAmt");
            return (Criteria) this;
        }

        public Criteria andCostAmtNotBetween(String value1, String value2) {
            addCriterion("cost_amt not between", value1, value2, "costAmt");
            return (Criteria) this;
        }

        public Criteria andStallNoNumIsNull() {
            addCriterion("stall_no_num is null");
            return (Criteria) this;
        }

        public Criteria andStallNoNumIsNotNull() {
            addCriterion("stall_no_num is not null");
            return (Criteria) this;
        }

        public Criteria andStallNoNumEqualTo(String value) {
            addCriterion("stall_no_num =", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumNotEqualTo(String value) {
            addCriterion("stall_no_num <>", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumGreaterThan(String value) {
            addCriterion("stall_no_num >", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumGreaterThanOrEqualTo(String value) {
            addCriterion("stall_no_num >=", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumLessThan(String value) {
            addCriterion("stall_no_num <", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumLessThanOrEqualTo(String value) {
            addCriterion("stall_no_num <=", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumLike(String value) {
            addCriterion("stall_no_num like", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumNotLike(String value) {
            addCriterion("stall_no_num not like", value, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumIn(List<String> values) {
            addCriterion("stall_no_num in", values, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumNotIn(List<String> values) {
            addCriterion("stall_no_num not in", values, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumBetween(String value1, String value2) {
            addCriterion("stall_no_num between", value1, value2, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andStallNoNumNotBetween(String value1, String value2) {
            addCriterion("stall_no_num not between", value1, value2, "stallNoNum");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagIsNull() {
            addCriterion("ph_org_bz_flag is null");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagIsNotNull() {
            addCriterion("ph_org_bz_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagEqualTo(String value) {
            addCriterion("ph_org_bz_flag =", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagNotEqualTo(String value) {
            addCriterion("ph_org_bz_flag <>", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagGreaterThan(String value) {
            addCriterion("ph_org_bz_flag >", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagGreaterThanOrEqualTo(String value) {
            addCriterion("ph_org_bz_flag >=", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagLessThan(String value) {
            addCriterion("ph_org_bz_flag <", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagLessThanOrEqualTo(String value) {
            addCriterion("ph_org_bz_flag <=", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagLike(String value) {
            addCriterion("ph_org_bz_flag like", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagNotLike(String value) {
            addCriterion("ph_org_bz_flag not like", value, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagIn(List<String> values) {
            addCriterion("ph_org_bz_flag in", values, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagNotIn(List<String> values) {
            addCriterion("ph_org_bz_flag not in", values, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagBetween(String value1, String value2) {
            addCriterion("ph_org_bz_flag between", value1, value2, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andPhOrgBzFlagNotBetween(String value1, String value2) {
            addCriterion("ph_org_bz_flag not between", value1, value2, "phOrgBzFlag");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNull() {
            addCriterion("store_type is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIsNotNull() {
            addCriterion("store_type is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeEqualTo(String value) {
            addCriterion("store_type =", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotEqualTo(String value) {
            addCriterion("store_type <>", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThan(String value) {
            addCriterion("store_type >", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeGreaterThanOrEqualTo(String value) {
            addCriterion("store_type >=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThan(String value) {
            addCriterion("store_type <", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLessThanOrEqualTo(String value) {
            addCriterion("store_type <=", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeLike(String value) {
            addCriterion("store_type like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotLike(String value) {
            addCriterion("store_type not like", value, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeIn(List<String> values) {
            addCriterion("store_type in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotIn(List<String> values) {
            addCriterion("store_type not in", values, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeBetween(String value1, String value2) {
            addCriterion("store_type between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andStoreTypeNotBetween(String value1, String value2) {
            addCriterion("store_type not between", value1, value2, "storeType");
            return (Criteria) this;
        }

        public Criteria andNumCum90IsNull() {
            addCriterion("num_cum_90 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum90IsNotNull() {
            addCriterion("num_cum_90 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum90EqualTo(String value) {
            addCriterion("num_cum_90 =", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90NotEqualTo(String value) {
            addCriterion("num_cum_90 <>", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90GreaterThan(String value) {
            addCriterion("num_cum_90 >", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_90 >=", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90LessThan(String value) {
            addCriterion("num_cum_90 <", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90LessThanOrEqualTo(String value) {
            addCriterion("num_cum_90 <=", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90Like(String value) {
            addCriterion("num_cum_90 like", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90NotLike(String value) {
            addCriterion("num_cum_90 not like", value, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90In(List<String> values) {
            addCriterion("num_cum_90 in", values, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90NotIn(List<String> values) {
            addCriterion("num_cum_90 not in", values, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90Between(String value1, String value2) {
            addCriterion("num_cum_90 between", value1, value2, "numCum90");
            return (Criteria) this;
        }

        public Criteria andNumCum90NotBetween(String value1, String value2) {
            addCriterion("num_cum_90 not between", value1, value2, "numCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90IsNull() {
            addCriterion("amt_cum_90 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90IsNotNull() {
            addCriterion("amt_cum_90 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum90EqualTo(String value) {
            addCriterion("amt_cum_90 =", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90NotEqualTo(String value) {
            addCriterion("amt_cum_90 <>", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90GreaterThan(String value) {
            addCriterion("amt_cum_90 >", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_90 >=", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90LessThan(String value) {
            addCriterion("amt_cum_90 <", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_90 <=", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90Like(String value) {
            addCriterion("amt_cum_90 like", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90NotLike(String value) {
            addCriterion("amt_cum_90 not like", value, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90In(List<String> values) {
            addCriterion("amt_cum_90 in", values, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90NotIn(List<String> values) {
            addCriterion("amt_cum_90 not in", values, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90Between(String value1, String value2) {
            addCriterion("amt_cum_90 between", value1, value2, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andAmtCum90NotBetween(String value1, String value2) {
            addCriterion("amt_cum_90 not between", value1, value2, "amtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90IsNull() {
            addCriterion("profit_amt_cum_90 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90IsNotNull() {
            addCriterion("profit_amt_cum_90 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90EqualTo(String value) {
            addCriterion("profit_amt_cum_90 =", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90NotEqualTo(String value) {
            addCriterion("profit_amt_cum_90 <>", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90GreaterThan(String value) {
            addCriterion("profit_amt_cum_90 >", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_90 >=", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90LessThan(String value) {
            addCriterion("profit_amt_cum_90 <", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_90 <=", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90Like(String value) {
            addCriterion("profit_amt_cum_90 like", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90NotLike(String value) {
            addCriterion("profit_amt_cum_90 not like", value, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90In(List<String> values) {
            addCriterion("profit_amt_cum_90 in", values, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90NotIn(List<String> values) {
            addCriterion("profit_amt_cum_90 not in", values, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90Between(String value1, String value2) {
            addCriterion("profit_amt_cum_90 between", value1, value2, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum90NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_90 not between", value1, value2, "profitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90IsNull() {
            addCriterion("profit_rate_90 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90IsNotNull() {
            addCriterion("profit_rate_90 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate90EqualTo(String value) {
            addCriterion("profit_rate_90 =", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90NotEqualTo(String value) {
            addCriterion("profit_rate_90 <>", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90GreaterThan(String value) {
            addCriterion("profit_rate_90 >", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_90 >=", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90LessThan(String value) {
            addCriterion("profit_rate_90 <", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_90 <=", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90Like(String value) {
            addCriterion("profit_rate_90 like", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90NotLike(String value) {
            addCriterion("profit_rate_90 not like", value, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90In(List<String> values) {
            addCriterion("profit_rate_90 in", values, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90NotIn(List<String> values) {
            addCriterion("profit_rate_90 not in", values, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90Between(String value1, String value2) {
            addCriterion("profit_rate_90 between", value1, value2, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andProfitRate90NotBetween(String value1, String value2) {
            addCriterion("profit_rate_90 not between", value1, value2, "profitRate90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90IsNull() {
            addCriterion("amt_rate_org_90 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90IsNotNull() {
            addCriterion("amt_rate_org_90 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90EqualTo(String value) {
            addCriterion("amt_rate_org_90 =", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90NotEqualTo(String value) {
            addCriterion("amt_rate_org_90 <>", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90GreaterThan(String value) {
            addCriterion("amt_rate_org_90 >", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_org_90 >=", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90LessThan(String value) {
            addCriterion("amt_rate_org_90 <", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_org_90 <=", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90Like(String value) {
            addCriterion("amt_rate_org_90 like", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90NotLike(String value) {
            addCriterion("amt_rate_org_90 not like", value, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90In(List<String> values) {
            addCriterion("amt_rate_org_90 in", values, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90NotIn(List<String> values) {
            addCriterion("amt_rate_org_90 not in", values, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90Between(String value1, String value2) {
            addCriterion("amt_rate_org_90 between", value1, value2, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg90NotBetween(String value1, String value2) {
            addCriterion("amt_rate_org_90 not between", value1, value2, "amtRateOrg90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90IsNull() {
            addCriterion("amt_rate_comp_90 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90IsNotNull() {
            addCriterion("amt_rate_comp_90 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90EqualTo(String value) {
            addCriterion("amt_rate_comp_90 =", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90NotEqualTo(String value) {
            addCriterion("amt_rate_comp_90 <>", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90GreaterThan(String value) {
            addCriterion("amt_rate_comp_90 >", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_comp_90 >=", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90LessThan(String value) {
            addCriterion("amt_rate_comp_90 <", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_comp_90 <=", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90Like(String value) {
            addCriterion("amt_rate_comp_90 like", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90NotLike(String value) {
            addCriterion("amt_rate_comp_90 not like", value, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90In(List<String> values) {
            addCriterion("amt_rate_comp_90 in", values, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90NotIn(List<String> values) {
            addCriterion("amt_rate_comp_90 not in", values, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90Between(String value1, String value2) {
            addCriterion("amt_rate_comp_90 between", value1, value2, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp90NotBetween(String value1, String value2) {
            addCriterion("amt_rate_comp_90 not between", value1, value2, "amtRateComp90");
            return (Criteria) this;
        }

        public Criteria andNumCum180IsNull() {
            addCriterion("num_cum_180 is null");
            return (Criteria) this;
        }

        public Criteria andNumCum180IsNotNull() {
            addCriterion("num_cum_180 is not null");
            return (Criteria) this;
        }

        public Criteria andNumCum180EqualTo(String value) {
            addCriterion("num_cum_180 =", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180NotEqualTo(String value) {
            addCriterion("num_cum_180 <>", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180GreaterThan(String value) {
            addCriterion("num_cum_180 >", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180GreaterThanOrEqualTo(String value) {
            addCriterion("num_cum_180 >=", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180LessThan(String value) {
            addCriterion("num_cum_180 <", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180LessThanOrEqualTo(String value) {
            addCriterion("num_cum_180 <=", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180Like(String value) {
            addCriterion("num_cum_180 like", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180NotLike(String value) {
            addCriterion("num_cum_180 not like", value, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180In(List<String> values) {
            addCriterion("num_cum_180 in", values, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180NotIn(List<String> values) {
            addCriterion("num_cum_180 not in", values, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180Between(String value1, String value2) {
            addCriterion("num_cum_180 between", value1, value2, "numCum180");
            return (Criteria) this;
        }

        public Criteria andNumCum180NotBetween(String value1, String value2) {
            addCriterion("num_cum_180 not between", value1, value2, "numCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180IsNull() {
            addCriterion("amt_cum_180 is null");
            return (Criteria) this;
        }

        public Criteria andAmtCum180IsNotNull() {
            addCriterion("amt_cum_180 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtCum180EqualTo(String value) {
            addCriterion("amt_cum_180 =", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180NotEqualTo(String value) {
            addCriterion("amt_cum_180 <>", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180GreaterThan(String value) {
            addCriterion("amt_cum_180 >", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180GreaterThanOrEqualTo(String value) {
            addCriterion("amt_cum_180 >=", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180LessThan(String value) {
            addCriterion("amt_cum_180 <", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180LessThanOrEqualTo(String value) {
            addCriterion("amt_cum_180 <=", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180Like(String value) {
            addCriterion("amt_cum_180 like", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180NotLike(String value) {
            addCriterion("amt_cum_180 not like", value, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180In(List<String> values) {
            addCriterion("amt_cum_180 in", values, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180NotIn(List<String> values) {
            addCriterion("amt_cum_180 not in", values, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180Between(String value1, String value2) {
            addCriterion("amt_cum_180 between", value1, value2, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andAmtCum180NotBetween(String value1, String value2) {
            addCriterion("amt_cum_180 not between", value1, value2, "amtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180IsNull() {
            addCriterion("profit_amt_cum_180 is null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180IsNotNull() {
            addCriterion("profit_amt_cum_180 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180EqualTo(String value) {
            addCriterion("profit_amt_cum_180 =", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180NotEqualTo(String value) {
            addCriterion("profit_amt_cum_180 <>", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180GreaterThan(String value) {
            addCriterion("profit_amt_cum_180 >", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180GreaterThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_180 >=", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180LessThan(String value) {
            addCriterion("profit_amt_cum_180 <", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180LessThanOrEqualTo(String value) {
            addCriterion("profit_amt_cum_180 <=", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180Like(String value) {
            addCriterion("profit_amt_cum_180 like", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180NotLike(String value) {
            addCriterion("profit_amt_cum_180 not like", value, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180In(List<String> values) {
            addCriterion("profit_amt_cum_180 in", values, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180NotIn(List<String> values) {
            addCriterion("profit_amt_cum_180 not in", values, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180Between(String value1, String value2) {
            addCriterion("profit_amt_cum_180 between", value1, value2, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitAmtCum180NotBetween(String value1, String value2) {
            addCriterion("profit_amt_cum_180 not between", value1, value2, "profitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180IsNull() {
            addCriterion("profit_rate_180 is null");
            return (Criteria) this;
        }

        public Criteria andProfitRate180IsNotNull() {
            addCriterion("profit_rate_180 is not null");
            return (Criteria) this;
        }

        public Criteria andProfitRate180EqualTo(String value) {
            addCriterion("profit_rate_180 =", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180NotEqualTo(String value) {
            addCriterion("profit_rate_180 <>", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180GreaterThan(String value) {
            addCriterion("profit_rate_180 >", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180GreaterThanOrEqualTo(String value) {
            addCriterion("profit_rate_180 >=", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180LessThan(String value) {
            addCriterion("profit_rate_180 <", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180LessThanOrEqualTo(String value) {
            addCriterion("profit_rate_180 <=", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180Like(String value) {
            addCriterion("profit_rate_180 like", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180NotLike(String value) {
            addCriterion("profit_rate_180 not like", value, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180In(List<String> values) {
            addCriterion("profit_rate_180 in", values, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180NotIn(List<String> values) {
            addCriterion("profit_rate_180 not in", values, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180Between(String value1, String value2) {
            addCriterion("profit_rate_180 between", value1, value2, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andProfitRate180NotBetween(String value1, String value2) {
            addCriterion("profit_rate_180 not between", value1, value2, "profitRate180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180IsNull() {
            addCriterion("amt_rate_org_180 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180IsNotNull() {
            addCriterion("amt_rate_org_180 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180EqualTo(String value) {
            addCriterion("amt_rate_org_180 =", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180NotEqualTo(String value) {
            addCriterion("amt_rate_org_180 <>", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180GreaterThan(String value) {
            addCriterion("amt_rate_org_180 >", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_org_180 >=", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180LessThan(String value) {
            addCriterion("amt_rate_org_180 <", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_org_180 <=", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180Like(String value) {
            addCriterion("amt_rate_org_180 like", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180NotLike(String value) {
            addCriterion("amt_rate_org_180 not like", value, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180In(List<String> values) {
            addCriterion("amt_rate_org_180 in", values, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180NotIn(List<String> values) {
            addCriterion("amt_rate_org_180 not in", values, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180Between(String value1, String value2) {
            addCriterion("amt_rate_org_180 between", value1, value2, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateOrg180NotBetween(String value1, String value2) {
            addCriterion("amt_rate_org_180 not between", value1, value2, "amtRateOrg180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180IsNull() {
            addCriterion("amt_rate_comp_180 is null");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180IsNotNull() {
            addCriterion("amt_rate_comp_180 is not null");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180EqualTo(String value) {
            addCriterion("amt_rate_comp_180 =", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180NotEqualTo(String value) {
            addCriterion("amt_rate_comp_180 <>", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180GreaterThan(String value) {
            addCriterion("amt_rate_comp_180 >", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180GreaterThanOrEqualTo(String value) {
            addCriterion("amt_rate_comp_180 >=", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180LessThan(String value) {
            addCriterion("amt_rate_comp_180 <", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180LessThanOrEqualTo(String value) {
            addCriterion("amt_rate_comp_180 <=", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180Like(String value) {
            addCriterion("amt_rate_comp_180 like", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180NotLike(String value) {
            addCriterion("amt_rate_comp_180 not like", value, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180In(List<String> values) {
            addCriterion("amt_rate_comp_180 in", values, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180NotIn(List<String> values) {
            addCriterion("amt_rate_comp_180 not in", values, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180Between(String value1, String value2) {
            addCriterion("amt_rate_comp_180 between", value1, value2, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andAmtRateComp180NotBetween(String value1, String value2) {
            addCriterion("amt_rate_comp_180 not between", value1, value2, "amtRateComp180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90IsNull() {
            addCriterion("revise_num_cum_90 is null");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90IsNotNull() {
            addCriterion("revise_num_cum_90 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90EqualTo(String value) {
            addCriterion("revise_num_cum_90 =", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90NotEqualTo(String value) {
            addCriterion("revise_num_cum_90 <>", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90GreaterThan(String value) {
            addCriterion("revise_num_cum_90 >", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90GreaterThanOrEqualTo(String value) {
            addCriterion("revise_num_cum_90 >=", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90LessThan(String value) {
            addCriterion("revise_num_cum_90 <", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90LessThanOrEqualTo(String value) {
            addCriterion("revise_num_cum_90 <=", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90Like(String value) {
            addCriterion("revise_num_cum_90 like", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90NotLike(String value) {
            addCriterion("revise_num_cum_90 not like", value, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90In(List<String> values) {
            addCriterion("revise_num_cum_90 in", values, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90NotIn(List<String> values) {
            addCriterion("revise_num_cum_90 not in", values, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90Between(String value1, String value2) {
            addCriterion("revise_num_cum_90 between", value1, value2, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum90NotBetween(String value1, String value2) {
            addCriterion("revise_num_cum_90 not between", value1, value2, "reviseNumCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90IsNull() {
            addCriterion("revise_amt_cum_90 is null");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90IsNotNull() {
            addCriterion("revise_amt_cum_90 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90EqualTo(String value) {
            addCriterion("revise_amt_cum_90 =", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90NotEqualTo(String value) {
            addCriterion("revise_amt_cum_90 <>", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90GreaterThan(String value) {
            addCriterion("revise_amt_cum_90 >", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90GreaterThanOrEqualTo(String value) {
            addCriterion("revise_amt_cum_90 >=", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90LessThan(String value) {
            addCriterion("revise_amt_cum_90 <", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90LessThanOrEqualTo(String value) {
            addCriterion("revise_amt_cum_90 <=", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90Like(String value) {
            addCriterion("revise_amt_cum_90 like", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90NotLike(String value) {
            addCriterion("revise_amt_cum_90 not like", value, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90In(List<String> values) {
            addCriterion("revise_amt_cum_90 in", values, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90NotIn(List<String> values) {
            addCriterion("revise_amt_cum_90 not in", values, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90Between(String value1, String value2) {
            addCriterion("revise_amt_cum_90 between", value1, value2, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum90NotBetween(String value1, String value2) {
            addCriterion("revise_amt_cum_90 not between", value1, value2, "reviseAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90IsNull() {
            addCriterion("revise_profit_amt_cum_90 is null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90IsNotNull() {
            addCriterion("revise_profit_amt_cum_90 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90EqualTo(String value) {
            addCriterion("revise_profit_amt_cum_90 =", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90NotEqualTo(String value) {
            addCriterion("revise_profit_amt_cum_90 <>", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90GreaterThan(String value) {
            addCriterion("revise_profit_amt_cum_90 >", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90GreaterThanOrEqualTo(String value) {
            addCriterion("revise_profit_amt_cum_90 >=", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90LessThan(String value) {
            addCriterion("revise_profit_amt_cum_90 <", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90LessThanOrEqualTo(String value) {
            addCriterion("revise_profit_amt_cum_90 <=", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90Like(String value) {
            addCriterion("revise_profit_amt_cum_90 like", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90NotLike(String value) {
            addCriterion("revise_profit_amt_cum_90 not like", value, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90In(List<String> values) {
            addCriterion("revise_profit_amt_cum_90 in", values, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90NotIn(List<String> values) {
            addCriterion("revise_profit_amt_cum_90 not in", values, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90Between(String value1, String value2) {
            addCriterion("revise_profit_amt_cum_90 between", value1, value2, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum90NotBetween(String value1, String value2) {
            addCriterion("revise_profit_amt_cum_90 not between", value1, value2, "reviseProfitAmtCum90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90IsNull() {
            addCriterion("revise_profit_rate_90 is null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90IsNotNull() {
            addCriterion("revise_profit_rate_90 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90EqualTo(String value) {
            addCriterion("revise_profit_rate_90 =", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90NotEqualTo(String value) {
            addCriterion("revise_profit_rate_90 <>", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90GreaterThan(String value) {
            addCriterion("revise_profit_rate_90 >", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90GreaterThanOrEqualTo(String value) {
            addCriterion("revise_profit_rate_90 >=", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90LessThan(String value) {
            addCriterion("revise_profit_rate_90 <", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90LessThanOrEqualTo(String value) {
            addCriterion("revise_profit_rate_90 <=", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90Like(String value) {
            addCriterion("revise_profit_rate_90 like", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90NotLike(String value) {
            addCriterion("revise_profit_rate_90 not like", value, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90In(List<String> values) {
            addCriterion("revise_profit_rate_90 in", values, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90NotIn(List<String> values) {
            addCriterion("revise_profit_rate_90 not in", values, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90Between(String value1, String value2) {
            addCriterion("revise_profit_rate_90 between", value1, value2, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate90NotBetween(String value1, String value2) {
            addCriterion("revise_profit_rate_90 not between", value1, value2, "reviseProfitRate90");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180IsNull() {
            addCriterion("revise_num_cum_180 is null");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180IsNotNull() {
            addCriterion("revise_num_cum_180 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180EqualTo(String value) {
            addCriterion("revise_num_cum_180 =", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180NotEqualTo(String value) {
            addCriterion("revise_num_cum_180 <>", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180GreaterThan(String value) {
            addCriterion("revise_num_cum_180 >", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180GreaterThanOrEqualTo(String value) {
            addCriterion("revise_num_cum_180 >=", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180LessThan(String value) {
            addCriterion("revise_num_cum_180 <", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180LessThanOrEqualTo(String value) {
            addCriterion("revise_num_cum_180 <=", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180Like(String value) {
            addCriterion("revise_num_cum_180 like", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180NotLike(String value) {
            addCriterion("revise_num_cum_180 not like", value, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180In(List<String> values) {
            addCriterion("revise_num_cum_180 in", values, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180NotIn(List<String> values) {
            addCriterion("revise_num_cum_180 not in", values, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180Between(String value1, String value2) {
            addCriterion("revise_num_cum_180 between", value1, value2, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseNumCum180NotBetween(String value1, String value2) {
            addCriterion("revise_num_cum_180 not between", value1, value2, "reviseNumCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180IsNull() {
            addCriterion("revise_amt_cum_180 is null");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180IsNotNull() {
            addCriterion("revise_amt_cum_180 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180EqualTo(String value) {
            addCriterion("revise_amt_cum_180 =", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180NotEqualTo(String value) {
            addCriterion("revise_amt_cum_180 <>", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180GreaterThan(String value) {
            addCriterion("revise_amt_cum_180 >", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180GreaterThanOrEqualTo(String value) {
            addCriterion("revise_amt_cum_180 >=", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180LessThan(String value) {
            addCriterion("revise_amt_cum_180 <", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180LessThanOrEqualTo(String value) {
            addCriterion("revise_amt_cum_180 <=", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180Like(String value) {
            addCriterion("revise_amt_cum_180 like", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180NotLike(String value) {
            addCriterion("revise_amt_cum_180 not like", value, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180In(List<String> values) {
            addCriterion("revise_amt_cum_180 in", values, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180NotIn(List<String> values) {
            addCriterion("revise_amt_cum_180 not in", values, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180Between(String value1, String value2) {
            addCriterion("revise_amt_cum_180 between", value1, value2, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseAmtCum180NotBetween(String value1, String value2) {
            addCriterion("revise_amt_cum_180 not between", value1, value2, "reviseAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180IsNull() {
            addCriterion("revise_profit_amt_cum_180 is null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180IsNotNull() {
            addCriterion("revise_profit_amt_cum_180 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180EqualTo(String value) {
            addCriterion("revise_profit_amt_cum_180 =", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180NotEqualTo(String value) {
            addCriterion("revise_profit_amt_cum_180 <>", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180GreaterThan(String value) {
            addCriterion("revise_profit_amt_cum_180 >", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180GreaterThanOrEqualTo(String value) {
            addCriterion("revise_profit_amt_cum_180 >=", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180LessThan(String value) {
            addCriterion("revise_profit_amt_cum_180 <", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180LessThanOrEqualTo(String value) {
            addCriterion("revise_profit_amt_cum_180 <=", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180Like(String value) {
            addCriterion("revise_profit_amt_cum_180 like", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180NotLike(String value) {
            addCriterion("revise_profit_amt_cum_180 not like", value, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180In(List<String> values) {
            addCriterion("revise_profit_amt_cum_180 in", values, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180NotIn(List<String> values) {
            addCriterion("revise_profit_amt_cum_180 not in", values, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180Between(String value1, String value2) {
            addCriterion("revise_profit_amt_cum_180 between", value1, value2, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitAmtCum180NotBetween(String value1, String value2) {
            addCriterion("revise_profit_amt_cum_180 not between", value1, value2, "reviseProfitAmtCum180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180IsNull() {
            addCriterion("revise_profit_rate_180 is null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180IsNotNull() {
            addCriterion("revise_profit_rate_180 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180EqualTo(String value) {
            addCriterion("revise_profit_rate_180 =", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180NotEqualTo(String value) {
            addCriterion("revise_profit_rate_180 <>", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180GreaterThan(String value) {
            addCriterion("revise_profit_rate_180 >", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180GreaterThanOrEqualTo(String value) {
            addCriterion("revise_profit_rate_180 >=", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180LessThan(String value) {
            addCriterion("revise_profit_rate_180 <", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180LessThanOrEqualTo(String value) {
            addCriterion("revise_profit_rate_180 <=", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180Like(String value) {
            addCriterion("revise_profit_rate_180 like", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180NotLike(String value) {
            addCriterion("revise_profit_rate_180 not like", value, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180In(List<String> values) {
            addCriterion("revise_profit_rate_180 in", values, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180NotIn(List<String> values) {
            addCriterion("revise_profit_rate_180 not in", values, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180Between(String value1, String value2) {
            addCriterion("revise_profit_rate_180 between", value1, value2, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andReviseProfitRate180NotBetween(String value1, String value2) {
            addCriterion("revise_profit_rate_180 not between", value1, value2, "reviseProfitRate180");
            return (Criteria) this;
        }

        public Criteria andRetailPriceIsNull() {
            addCriterion("retail_price is null");
            return (Criteria) this;
        }

        public Criteria andRetailPriceIsNotNull() {
            addCriterion("retail_price is not null");
            return (Criteria) this;
        }

        public Criteria andRetailPriceEqualTo(String value) {
            addCriterion("retail_price =", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceNotEqualTo(String value) {
            addCriterion("retail_price <>", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceGreaterThan(String value) {
            addCriterion("retail_price >", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceGreaterThanOrEqualTo(String value) {
            addCriterion("retail_price >=", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceLessThan(String value) {
            addCriterion("retail_price <", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceLessThanOrEqualTo(String value) {
            addCriterion("retail_price <=", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceLike(String value) {
            addCriterion("retail_price like", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceNotLike(String value) {
            addCriterion("retail_price not like", value, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceIn(List<String> values) {
            addCriterion("retail_price in", values, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceNotIn(List<String> values) {
            addCriterion("retail_price not in", values, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceBetween(String value1, String value2) {
            addCriterion("retail_price between", value1, value2, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andRetailPriceNotBetween(String value1, String value2) {
            addCriterion("retail_price not between", value1, value2, "retailPrice");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}