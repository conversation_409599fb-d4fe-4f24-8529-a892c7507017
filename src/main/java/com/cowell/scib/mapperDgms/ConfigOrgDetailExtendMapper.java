package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.ConfigOrgDetailExtend;
import com.cowell.scib.entityDgms.ConfigOrgDetailExtendExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ConfigOrgDetailExtendMapper {
    long countByExample(ConfigOrgDetailExtendExample example);

    int deleteByExample(ConfigOrgDetailExtendExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigOrgDetailExtend record);

    int insertSelective(ConfigOrgDetailExtend record);

    List<ConfigOrgDetailExtend> selectByExample(ConfigOrgDetailExtendExample example);

    ConfigOrgDetailExtend selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigOrgDetailExtend record, @Param("example") ConfigOrgDetailExtendExample example);

    int updateByExample(@Param("record") ConfigOrgDetailExtend record, @Param("example") ConfigOrgDetailExtendExample example);

    int updateByPrimaryKeySelective(ConfigOrgDetailExtend record);

    int updateByPrimaryKey(ConfigOrgDetailExtend record);
}
