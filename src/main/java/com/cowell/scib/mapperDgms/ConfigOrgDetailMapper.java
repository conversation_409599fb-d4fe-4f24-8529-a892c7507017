package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.ConfigOrgDetail;
import com.cowell.scib.entityDgms.ConfigOrgDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ConfigOrgDetailMapper {
    long countByExample(ConfigOrgDetailExample example);

    int deleteByExample(ConfigOrgDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigOrgDetail record);

    int insertSelective(ConfigOrgDetail record);

    List<ConfigOrgDetail> selectByExample(ConfigOrgDetailExample example);

    ConfigOrgDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigOrgDetail record, @Param("example") ConfigOrgDetailExample example);

    int updateByExample(@Param("record") ConfigOrgDetail record, @Param("example") ConfigOrgDetailExample example);

    int updateByPrimaryKeySelective(ConfigOrgDetail record);

    int updateByPrimaryKey(ConfigOrgDetail record);
}