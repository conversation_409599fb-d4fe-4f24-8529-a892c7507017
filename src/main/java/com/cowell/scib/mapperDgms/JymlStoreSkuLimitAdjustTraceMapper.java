package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTrace;
import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustTraceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlStoreSkuLimitAdjustTraceMapper {
    long countByExample(JymlStoreSkuLimitAdjustTraceExample example);

    int deleteByExample(JymlStoreSkuLimitAdjustTraceExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuLimitAdjustTrace record);

    int insertSelective(JymlStoreSkuLimitAdjustTrace record);

    List<JymlStoreSkuLimitAdjustTrace> selectByExample(JymlStoreSkuLimitAdjustTraceExample example);

    JymlStoreSkuLimitAdjustTrace selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuLimitAdjustTrace record, @Param("example") JymlStoreSkuLimitAdjustTraceExample example);

    int updateByExample(@Param("record") JymlStoreSkuLimitAdjustTrace record, @Param("example") JymlStoreSkuLimitAdjustTraceExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuLimitAdjustTrace record);

    int updateByPrimaryKey(JymlStoreSkuLimitAdjustTrace record);
}