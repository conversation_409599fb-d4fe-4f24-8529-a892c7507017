package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryStoreTypeGoods;
import com.cowell.scib.entityDgms.NecessaryStoreTypeGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryStoreTypeGoodsMapper {
    long countByExample(NecessaryStoreTypeGoodsExample example);

    int deleteByExample(NecessaryStoreTypeGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryStoreTypeGoods record);

    int insertSelective(NecessaryStoreTypeGoods record);

    List<NecessaryStoreTypeGoods> selectByExample(NecessaryStoreTypeGoodsExample example);

    NecessaryStoreTypeGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryStoreTypeGoods record, @Param("example") NecessaryStoreTypeGoodsExample example);

    int updateByExample(@Param("record") NecessaryStoreTypeGoods record, @Param("example") NecessaryStoreTypeGoodsExample example);

    int updateByPrimaryKeySelective(NecessaryStoreTypeGoods record);

    int updateByPrimaryKey(NecessaryStoreTypeGoods record);
}