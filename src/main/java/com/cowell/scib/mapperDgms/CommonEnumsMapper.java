package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.CommonEnums;
import com.cowell.scib.entityDgms.CommonEnumsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CommonEnumsMapper {
    long countByExample(CommonEnumsExample example);

    int deleteByExample(CommonEnumsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CommonEnums record);

    int insertSelective(CommonEnums record);

    List<CommonEnums> selectByExample(CommonEnumsExample example);

    CommonEnums selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CommonEnums record, @Param("example") CommonEnumsExample example);

    int updateByExample(@Param("record") CommonEnums record, @Param("example") CommonEnumsExample example);

    int updateByPrimaryKeySelective(CommonEnums record);

    int updateByPrimaryKey(CommonEnums record);
}