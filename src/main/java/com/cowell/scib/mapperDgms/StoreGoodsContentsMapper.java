package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.entityDgms.StoreGoodsContentsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreGoodsContentsMapper {
    long countByExample(StoreGoodsContentsExample example);

    int deleteByExample(StoreGoodsContentsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreGoodsContents record);

    int insertSelective(StoreGoodsContents record);

    List<StoreGoodsContents> selectByExample(StoreGoodsContentsExample example);

    StoreGoodsContents selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreGoodsContents record, @Param("example") StoreGoodsContentsExample example);

    int updateByExample(@Param("record") StoreGoodsContents record, @Param("example") StoreGoodsContentsExample example);

    int updateByPrimaryKeySelective(StoreGoodsContents record);

    int updateByPrimaryKey(StoreGoodsContents record);
}