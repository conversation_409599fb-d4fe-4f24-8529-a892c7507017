package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.entityDgms.BundlingTaskInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BundlingTaskInfoMapper {
    long countByExample(BundlingTaskInfoExample example);

    int deleteByExample(BundlingTaskInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BundlingTaskInfo record);

    int insertSelective(BundlingTaskInfo record);

    List<BundlingTaskInfo> selectByExample(BundlingTaskInfoExample example);

    BundlingTaskInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BundlingTaskInfo record, @Param("example") BundlingTaskInfoExample example);

    int updateByExample(@Param("record") BundlingTaskInfo record, @Param("example") BundlingTaskInfoExample example);

    int updateByPrimaryKeySelective(BundlingTaskInfo record);

    int updateByPrimaryKey(BundlingTaskInfo record);
}