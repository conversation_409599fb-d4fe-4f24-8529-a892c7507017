package com.cowell.scib.mapperDgms;

import com.cowell.scib.entity.DevelopModuleRecordFile;
import com.cowell.scib.entity.DevelopModuleRecordFileExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DevelopModuleRecordFileMapper {
    long countByExample(DevelopModuleRecordFileExample example);

    int deleteByExample(DevelopModuleRecordFileExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DevelopModuleRecordFile record);

    int insertSelective(DevelopModuleRecordFile record);

    List<DevelopModuleRecordFile> selectByExample(DevelopModuleRecordFileExample example);

    DevelopModuleRecordFile selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") DevelopModuleRecordFile record, @Param("example") DevelopModuleRecordFileExample example);

    int updateByExample(@Param("record") DevelopModuleRecordFile record, @Param("example") DevelopModuleRecordFileExample example);

    int updateByPrimaryKeySelective(DevelopModuleRecordFile record);

    int updateByPrimaryKey(DevelopModuleRecordFile record);
}