package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.DgmsCommonLock;
import com.cowell.scib.entityDgms.DgmsCommonLockExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DgmsCommonLockMapper {
    long countByExample(DgmsCommonLockExample example);

    int deleteByExample(DgmsCommonLockExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DgmsCommonLock record);

    int insertSelective(DgmsCommonLock record);

    List<DgmsCommonLock> selectByExample(DgmsCommonLockExample example);

    DgmsCommonLock selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DgmsCommonLock record, @Param("example") DgmsCommonLockExample example);

    int updateByExample(@Param("record") DgmsCommonLock record, @Param("example") DgmsCommonLockExample example);

    int updateByPrimaryKeySelective(DgmsCommonLock record);

    int updateByPrimaryKey(DgmsCommonLock record);
}