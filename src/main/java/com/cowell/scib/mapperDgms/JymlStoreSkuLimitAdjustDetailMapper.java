package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetail;
import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlStoreSkuLimitAdjustDetailMapper {
    long countByExample(JymlStoreSkuLimitAdjustDetailExample example);

    int deleteByExample(JymlStoreSkuLimitAdjustDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuLimitAdjustDetail record);

    int insertSelective(JymlStoreSkuLimitAdjustDetail record);

    List<JymlStoreSkuLimitAdjustDetail> selectByExample(JymlStoreSkuLimitAdjustDetailExample example);

    JymlStoreSkuLimitAdjustDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuLimitAdjustDetail record, @Param("example") JymlStoreSkuLimitAdjustDetailExample example);

    int updateByExample(@Param("record") JymlStoreSkuLimitAdjustDetail record, @Param("example") JymlStoreSkuLimitAdjustDetailExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuLimitAdjustDetail record);

    int updateByPrimaryKey(JymlStoreSkuLimitAdjustDetail record);
}