package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryChooseStoreTypeGoods;
import com.cowell.scib.entityDgms.NecessaryChooseStoreTypeGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryChooseStoreTypeGoodsMapper {
    long countByExample(NecessaryChooseStoreTypeGoodsExample example);

    int deleteByExample(NecessaryChooseStoreTypeGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryChooseStoreTypeGoods record);

    int insertSelective(NecessaryChooseStoreTypeGoods record);

    List<NecessaryChooseStoreTypeGoods> selectByExample(NecessaryChooseStoreTypeGoodsExample example);

    NecessaryChooseStoreTypeGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryChooseStoreTypeGoods record, @Param("example") NecessaryChooseStoreTypeGoodsExample example);

    int updateByExample(@Param("record") NecessaryChooseStoreTypeGoods record, @Param("example") NecessaryChooseStoreTypeGoodsExample example);

    int updateByPrimaryKeySelective(NecessaryChooseStoreTypeGoods record);

    int updateByPrimaryKey(NecessaryChooseStoreTypeGoods record);
}