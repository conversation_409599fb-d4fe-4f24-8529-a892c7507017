package com.cowell.scib.mapperDgms;

import com.cowell.scib.entity.DevelopModuleRecord;
import com.cowell.scib.entity.DevelopModuleRecordExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DevelopModuleRecordMapper {
    long countByExample(DevelopModuleRecordExample example);

    int deleteByExample(DevelopModuleRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DevelopModuleRecord record);

    int insertSelective(DevelopModuleRecord record);

    List<DevelopModuleRecord> selectByExample(DevelopModuleRecordExample example);

    DevelopModuleRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") DevelopModuleRecord record, @Param("example") DevelopModuleRecordExample example);

    int updateByExample(@Param("record") DevelopModuleRecord record, @Param("example") DevelopModuleRecordExample example);

    int updateByPrimaryKeySelective(DevelopModuleRecord record);

    int updateByPrimaryKey(DevelopModuleRecord record);
}