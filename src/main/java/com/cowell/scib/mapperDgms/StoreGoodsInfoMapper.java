package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.StoreGoodsInfo;
import com.cowell.scib.entityDgms.StoreGoodsInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreGoodsInfoMapper {
    long countByExample(StoreGoodsInfoExample example);

    int deleteByExample(StoreGoodsInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreGoodsInfo record);

    int insertSelective(StoreGoodsInfo record);

    List<StoreGoodsInfo> selectByExample(StoreGoodsInfoExample example);

    StoreGoodsInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreGoodsInfo record, @Param("example") StoreGoodsInfoExample example);

    int updateByExample(@Param("record") StoreGoodsInfo record, @Param("example") StoreGoodsInfoExample example);

    int updateByPrimaryKeySelective(StoreGoodsInfo record);

    int updateByPrimaryKey(StoreGoodsInfo record);
}