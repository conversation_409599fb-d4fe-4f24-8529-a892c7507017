package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.PlanMedicalInsuranceContents;
import com.cowell.scib.entityDgms.PlanMedicalInsuranceContentsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PlanMedicalInsuranceContentsMapper {
    long countByExample(PlanMedicalInsuranceContentsExample example);

    int deleteByExample(PlanMedicalInsuranceContentsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PlanMedicalInsuranceContents record);

    int insertSelective(PlanMedicalInsuranceContents record);

    List<PlanMedicalInsuranceContents> selectByExample(PlanMedicalInsuranceContentsExample example);

    PlanMedicalInsuranceContents selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") PlanMedicalInsuranceContents record, @Param("example") PlanMedicalInsuranceContentsExample example);

    int updateByExample(@Param("record") PlanMedicalInsuranceContents record, @Param("example") PlanMedicalInsuranceContentsExample example);

    int updateByPrimaryKeySelective(PlanMedicalInsuranceContents record);

    int updateByPrimaryKey(PlanMedicalInsuranceContents record);
}