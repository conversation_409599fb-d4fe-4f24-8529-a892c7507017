package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.MdmPushStoreGoodsErrorRecord;
import com.cowell.scib.entityDgms.MdmPushStoreGoodsErrorRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MdmPushStoreGoodsErrorRecordMapper {
    long countByExample(MdmPushStoreGoodsErrorRecordExample example);

    int deleteByExample(MdmPushStoreGoodsErrorRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MdmPushStoreGoodsErrorRecord record);

    int insertSelective(MdmPushStoreGoodsErrorRecord record);

    List<MdmPushStoreGoodsErrorRecord> selectByExample(MdmPushStoreGoodsErrorRecordExample example);

    MdmPushStoreGoodsErrorRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MdmPushStoreGoodsErrorRecord record, @Param("example") MdmPushStoreGoodsErrorRecordExample example);

    int updateByExample(@Param("record") MdmPushStoreGoodsErrorRecord record, @Param("example") MdmPushStoreGoodsErrorRecordExample example);

    int updateByPrimaryKeySelective(MdmPushStoreGoodsErrorRecord record);

    int updateByPrimaryKey(MdmPushStoreGoodsErrorRecord record);
}