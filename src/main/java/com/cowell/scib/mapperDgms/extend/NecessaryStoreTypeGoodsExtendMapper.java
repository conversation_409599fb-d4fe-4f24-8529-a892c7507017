package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.NecessaryStoreTypeGoods;
import com.cowell.scib.entityDgms.NecessaryStoreTypeGoodsExample;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NecessaryStoreTypeGoodsExtendMapper {

    int batchInsert(List<NecessaryStoreTypeGoods> record);

    Long countNecessary(@Param("param") NecessaryQueryParam param);

    List<NecessaryCommonDTO> queryNecessary(@Param("param") NecessaryQueryParam param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    List<String> selectExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("companyOrgId") Long companyOrgId, @Param("citys") List<String> citys, @Param("storeTypes") List<String> storeTypes, @Param("goodsNos") List<String> goodsNos);

    int batchDel(@Param("ids")List<Long> ids);

    int updateVersion(@Param("ids")List<Long> ids);
}
