package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.StoreGoodsContents;
import com.cowell.scib.entityDgms.StoreGoodsContentsExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StoreGoodsContentsExtendMapper {
    int batchInsert(List<StoreGoodsContents> record);

    int batchUpdate(@Param("list")List<StoreGoodsContents> update, @Param("storeId") Long storeId);

    int batchUpdateManageStatus(@Param("list") List<StoreGoodsContents> update, @Param("storeId") Long storeId);
    int batchUpdateCategory(@Param("list") List<StoreGoodsContents> update, @Param("storeId") Long storeId);
}
