package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect;
import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffectExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JymlStoreSkuLimitAdjustEffectExtendMapper {

    int batchInsert(List<JymlStoreSkuLimitAdjustEffect> record);

    List<JymlStoreSkuLimitAdjustEffect> selectWithCtrlCategory(@Param("storeOrgIds") List<Long> storeOrgIds, @Param("ctrlCategorys") List<String> ctrlCategorys, @Param("ctrlCategoryNames") List<String> ctrlCategoryNames);

}
