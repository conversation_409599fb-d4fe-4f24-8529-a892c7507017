package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.NecessaryPlatformGoods;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryPlatformCountDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryTaskGoodsCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NecessaryPlatformGoodsExtendMapper {

    int batchInsert(List<NecessaryPlatformGoods> record);

    Long countNecessary(@Param("param") NecessaryQueryParam param);

    List<NecessaryCommonDTO> queryNecessary(@Param("param") NecessaryQueryParam param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    List<String> selectExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("storeTypes") List<String> storeType, @Param("goodsNos") List<String> goodsNos);

    List<NecessaryPlatformCountDTO> selectCompanyNecessaryExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("goodsNos") List<String> goodsNos);

    List<NecessaryTaskGoodsCommonDTO> selectExistsGoodsAndStoreType(@Param("platformOrgId") Long platformOrgId, @Param("storeTypes") List<String> storeTypes, @Param("goodsNos") List<String> goodsNos);

    int batchDel(@Param("ids")List<Long> ids);

    int updateVersion(@Param("ids")List<Long> ids);
}
