package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.StoreGoodsInfo;
import com.cowell.scib.service.dto.StoreGoodEffectStatusDTO;
import com.cowell.scib.service.dto.StoreGoodNecessaryTagDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface StoreGoodsInfoExtendMapper {

    int batchInsert(List<StoreGoodsInfo> record);

    int batchDel(@Param("ids")List<Long> ids,@Param("storeId")Long storeId,@Param("updatedName") String updatedName);

    int batchUpdateNecessaryTag(@Param("list")List<StoreGoodNecessaryTagDTO> list, @Param("storeId")Long storeId);

    int batchUpdateEffectStatus(@Param("list")List<StoreGoodEffectStatusDTO> list, @Param("storeId")Long storeId);

    int batchUpdateNecessaryTagByGoodsNo(@Param("list")List<Long> list, @Param("goodsNo")String goodsNo);

    List<String> selectExistsGoods(@Param("storeIds") List<Long> storeIds, @Param("goodsNos")List<String> goodsNos);

    int updateNecessaryTagByIds(@Param("storeId") Long storeId, @Param("ids") List<Long> ids, @Param("necessaryTag") Byte necessaryTag, @Param("minDisplayQuantity") BigDecimal minDisplayQuantity);

    List<String> selectExistsGoodsNoStatus(@Param("storeId")Long storeId, @Param("goodsNos")List<String> goodsNos);

    int updateMinDisplayQuantity(@Param("storeId")Long storeId, @Param("goodsNos") List<String> goodsNos, @Param("minDisplayQuantity") BigDecimal minDisplayQuantity);
}
