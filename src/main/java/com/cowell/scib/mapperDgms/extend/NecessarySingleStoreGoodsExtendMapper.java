package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.NecessarySingleStoreGoods;
import com.cowell.scib.entityDgms.NecessarySingleStoreGoodsExample;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NecessarySingleStoreGoodsExtendMapper {

    int batchInsert(List<NecessarySingleStoreGoods> record);

    Long countNecessary(@Param("param") NecessaryQueryParam param);

    List<NecessaryCommonDTO> queryNecessary(@Param("param") NecessaryQueryParam param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    List<String> selectExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("storeOrgIds") List<Long> storeOrgIds, @Param("goodsNos") List<String> goodsNos);

    int batchDel(@Param("ids")List<Long> ids);

    int updateVersion(@Param("ids")List<Long> ids);
}
