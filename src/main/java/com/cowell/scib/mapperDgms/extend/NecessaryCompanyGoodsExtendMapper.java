package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.NecessaryCompanyGoods;
import com.cowell.scib.entityDgms.NecessaryCompanyGoodsExample;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import com.cowell.scib.service.dto.necessaryContents.NecessaryTaskGoodsCommonDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NecessaryCompanyGoodsExtendMapper {

    int batchInsert(List<NecessaryCompanyGoods> record);

    Long countNecessary(@Param("param") NecessaryQueryParam param);

    List<NecessaryCommonDTO> queryNecessary(@Param("param") NecessaryQueryParam param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    List<NecessaryTaskGoodsCommonDTO> selectExistsGoodsAndCity(@Param("platformOrgId") Long platformOrgId, @Param("companyOrgIdList") List<Long> companyOrgIdList, @Param("goodsNos") List<String> goodsNos);

    List<String> selectExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("companyOrgId") Long companyOrgId, @Param("citys") List<String> citys, @Param("goodsNos")List<String> goodsNos);

    int batchDel(@Param("ids")List<Long> ids);

    int updateVersion(@Param("ids")List<Long> ids);
}
