package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.MdmTaskDetail;
import com.cowell.scib.entityDgms.MdmTaskDetailExample;
import com.cowell.scib.service.dto.necessaryContents.MdmTaskDetailCountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MdmTaskDetailExtendMapper {

    int batchInsert(List<MdmTaskDetail> record);

    List<MdmTaskDetailCountDTO> selectDetailCount(@Param("taskIds") List<Long> taskIds);

    int deleteByTaskId(@Param("taskId") Long taskId);

    int batchUpdatePushStatus(@Param("taskId") Long taskId,@Param("ids")List<Long> ids,@Param("pushStatus")Byte pushStatus);

    List<MdmTaskDetail> selectMdmDetail(@Param("taskId") Long taskId,@Param("pushStatus")Byte pushStatus,@Param("id")Long id,@Param("createdBy")Long createdBy,@Param("size")Integer size);
}
