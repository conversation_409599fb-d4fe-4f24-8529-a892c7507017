package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entity.DevelopModuleRecord;
import com.cowell.scib.service.param.DevelopListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/29 18:16
 */
public interface DevelopModuleRecordExtendMapper {

    long searchModuleRecordCount(DevelopListParam developListParam);

    List<DevelopModuleRecord> searchModuleRecordList(DevelopListParam developListParam);

    DevelopModuleRecord searchRencentModuleRecordList(@Param("moduleCode") String moduleCode, @Param("day") Integer day);
}
