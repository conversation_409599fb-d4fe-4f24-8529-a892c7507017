package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.service.param.BundlTaskListParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/10 16:59
 */
@Component
public interface BundlingTaskInfoExtendMapper {

    /**
     * 获取数量
     * @param listParam
     * @return
     */
    long searchBundlTaskInfoCount(@Param("listParam") BundlTaskListParam listParam);

    /**
     * 查询列表
     * @param listParam
     * @return
     */
    List<BundlingTaskInfo> searchBundlTaskInfoList(@Param("listParam") BundlTaskListParam listParam);

    /**
     * 查询平台下的任务
     * @param orgId
     * @return
     */
    List<Long> searchTaskIdWithOrgIdAndTaskType(@Param("orgId") Long orgId, @Param("taskStatusList") List<Byte> taskStatusList, @Param("taskTypeList") List<Byte> taskTypeList);
}
