package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entity.DevelopModuleRecordFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/30 18:07
 */
public interface DevelopModuleRecordFileExtendMapper {
    int batchInsertSelective(List<DevelopModuleRecordFile> list);
    int batchDeleteSelective(@Param("idList") List<Integer> idList);

    int batchDeleteByKey(@Param("codeVersionKey") String codeVersionKey);

    int deleteFileByCodeAndVersion(@Param("code") String code, @Param("version") String version);
}
