package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.NecessaryGroupGoods;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCategoryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NecessaryGroupGoodsExtendMapper {

    int batchInsert(List<NecessaryGroupGoods> record);

    List<NecessaryCommonDTO> queryNecessary(@Param("param") NecessaryQueryParam param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    Long countNecessary(@Param("param")NecessaryQueryParam param);

    List<String> selectExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("goodsNos") List<String> goodsNos, @Param("necessaryTag") Byte necessaryTag);

    List<NecessaryCategoryCommonDTO> selectExistsGoodsByPlat(@Param("platformOrgId") Long platformOrgId, @Param("necessaryTag") Byte necessaryTag);

    List<String> selectGroupExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("goodsNos") List<String> goodsNos, @Param("necessaryTag") Byte necessaryTag);
}
