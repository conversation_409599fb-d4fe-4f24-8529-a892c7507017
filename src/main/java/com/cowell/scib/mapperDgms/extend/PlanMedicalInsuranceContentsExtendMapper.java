package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.PlanMedicalInsuranceContents;
import com.cowell.scib.entityDgms.PlanMedicalInsuranceContentsExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlanMedicalInsuranceContentsExtendMapper {

    int batchInsert(List<PlanMedicalInsuranceContents> record);

    List<PlanMedicalInsuranceContents> selectExistsByExample(PlanMedicalInsuranceContentsExample example);

}
