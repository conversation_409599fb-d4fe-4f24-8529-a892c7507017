package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.OnlineHotGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1 16:04
 */
public interface OnlineHotGoodsExtendMapper {

    /**
     * 获取
     * @param goodsSource
     * @param goodsYear
     * @param goodsTimeDimension
     * @param goodsTimeFrame
     * @return
     */
    int exitHotGoodsCount(@Param("goodsSource") String goodsSource, @Param("goodsYear") String goodsYear, @Param("goodsTimeFrame") String goodsTimeFrame, @Param("goodsTimeDimension") String goodsTimeDimension);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(List<OnlineHotGoods> list);
}
