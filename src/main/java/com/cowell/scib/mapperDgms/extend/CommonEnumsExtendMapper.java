package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.CommonEnums;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 10:46
 */
public interface CommonEnumsExtendMapper {

    List<CommonEnums> selectByDicCode(@Param("dicCode") String dicCode);

    List<CommonEnums> selectByDicCodeList(@Param("dicCodeList") List<String> dicCodeList);

    List<CommonEnums> selectByDicName(@Param("dicName") String dicName);

    List<CommonEnums> selectByDicCodeAndValueList(@Param("dicCodeList") List<String> dicCodeList, @Param("enumValueList") List<String> enumValueList);
}
