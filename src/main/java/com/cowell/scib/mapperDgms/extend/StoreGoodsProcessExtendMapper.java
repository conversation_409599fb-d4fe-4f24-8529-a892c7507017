package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.StoreGoodsProcess;
import com.cowell.scib.entityDgms.StoreGoodsProcessExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StoreGoodsProcessExtendMapper {
    int batchInsert(List<StoreGoodsProcess> record);

    List<StoreGoodsProcess> selectGoodsNecessaryTag(@Param("storeId") Long storeId, @Param("goodsNos") List<String> goodsNos);
}
