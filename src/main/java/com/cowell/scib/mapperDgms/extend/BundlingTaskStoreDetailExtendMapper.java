package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.BundlingTaskStoreDetail;
import com.cowell.scib.service.param.BundlStoreConfirmParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13 12:18
 */
public interface BundlingTaskStoreDetailExtendMapper {

    /**
     * 获取数量
     * @param listParam
     * @return
     */
    long searchBundlTaskStoreCount(@Param("confirmParam") BundlStoreConfirmParam listParam);

    /**
     * 查询列表
     * @param listParam
     * @return
     */
    List<BundlingTaskStoreDetail> searchBundlTaskStoreList(@Param("confirmParam") BundlStoreConfirmParam listParam);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<BundlingTaskStoreDetail> list);

    /**
     * 删除门店明细扩展
     * @param taskId
     * @param taskId
     * @return
     */
    int deleteDetailByTaskId(@Param("taskId") Long taskId);

    List<BundlingTaskStoreDetail> searchBundlTaskStoreListByTaskId(@Param("taskId") Long taskId);

    long countBundlTaskStoreListByTaskId(@Param("taskId") Long taskId);

    List<BundlingTaskStoreDetail> searchBundlTaskStoreListByConfirm(@Param("taskId") Long taskId, @Param("bundlConfirmAble")  Byte bundlConfirmAble);

    long countBundlTaskStoreListByConfirm(@Param("taskId") Long taskId, @Param("bundlConfirmAble")  Byte bundlConfirmAble);

    List<BundlingTaskStoreDetail> searchBundlTaskStoreListByTaskIdAndStoreType(@Param("taskId") Long taskId, @Param("platStoreTypeCodeList") List<String> platStoreTypeCodeList, @Param("storeTypeCodeList") List<String> storeTypeCodeList, @Param("zsStoreTypeCodeList") List<String> zsStoreTypeCodeList);

    List<Long> searchStoreIdListByTaskIdAndStoreType(@Param("taskId") Long taskId, @Param("companyOrgId") Long companyOrgId, @Param("city") String city, @Param("platStoreTypeCodeList") List<String> platStoreTypeCodeList, @Param("storeTypeCodeList") List<String> storeTypeCodeList, @Param("zsStoreTypeCodeList") List<String> zsStoreTypeCodeList);

    List<String> searchStoreCodeByTaskCode(@Param("taskIdList") List<Long> taskIdList);

    List<String> searchTaskCodeBytaskIdListAndStoreCode(@Param("taskIdList") List<Long> taskIdList, @Param("storeSapCode") String storeSapCode);

    int batchUpdateType(List<BundlingTaskStoreDetail> list);
}
