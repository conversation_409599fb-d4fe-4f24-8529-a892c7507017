package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.ConfigOrg;
import com.cowell.scib.service.dto.rule.ConfigOrgDBStyleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 18:35
 */
public interface ConfigOrgExtendMapper {

    ConfigOrg selectConfigByOrgId(@Param("orgId")Long orgId, @Param("configType")Byte configType, @Param("version")Integer version);

    List<ConfigOrgDBStyleDTO> selectConfigDBByOrgId(@Param("orgId")Long orgId, @Param("configType")Byte configType);

}
