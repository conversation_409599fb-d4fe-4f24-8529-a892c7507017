package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.ConfigOrgDetail;
import com.cowell.scib.service.dto.rule.ConfigOrgDetailDBStyleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ConfigOrgDetailExMapper {


    List<ConfigOrgDetail> selectByConfigIdAndDictCodes(@Param("configId")  Long configId, @Param("list") List<String> list);

    List<ConfigOrgDetail> selectByConfigIdAndTypeAndDictCodes(@Param("configId")  Long configId, @Param("perprotyType")  Byte perprotyType, @Param("list") List<String> list);

    int insertConfigOrgDetailList(@Param("list") List<ConfigOrgDetail> list);

    int deleteByconfigIdAndDictCodes(@Param("configId") Long configId, @Param("list") List<String> list);

    List<ConfigOrgDetailDBStyleDTO> selectConfigDetailDBByConfigIds(@Param("configIds")  List<Long> configIds);

}
