package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.NecessaryChooseStoreTypeGoods;
import com.cowell.scib.entityDgms.NecessaryChooseStoreTypeGoodsExample;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NecessaryChooseStoreTypeGoodsExtendMapper {

    int batchInsert(List<NecessaryChooseStoreTypeGoods> record);

    Long countNecessary(@Param("param") NecessaryQueryParam param);

    List<NecessaryCommonDTO> queryNecessary(@Param("param") NecessaryQueryParam param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    List<String> selectExistsGoods(@Param("platformOrgId") Long platformOrgId, @Param("companyOrgId") Long companyOrgId, @Param("city") String city, @Param("storeType") String storeType, @Param("goodsNos") List<String> goodsNos);

    int batchDel(@Param("ids")List<Long> ids);

    int updateVersion(@Param("ids")List<Long> ids);
}
