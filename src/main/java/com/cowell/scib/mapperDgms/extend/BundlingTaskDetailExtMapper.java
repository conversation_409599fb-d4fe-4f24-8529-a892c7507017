package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.BundlingTaskDetail;
import com.cowell.scib.service.param.BundlTaskListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/11 16:49
 */
public interface BundlingTaskDetailExtMapper {

    /**
     * 通过查询条件获取任务明细
     * @param listParam
     * @return
     */
    List<BundlingTaskDetail> listTaskDetailByListParam(@Param("listParam") BundlTaskListParam listParam);

    List<BundlingTaskDetail> listTaskDetailByStep(@Param("listParam") BundlTaskListParam listParam);

    /**
     * 获取某一任务下的组货、中参、配方三种店型值集合
     * @param taskId
     * @param storeTypeList
     * @return
     */
    List<String> listStoreZsDisposeType(@Param("taskId") Long taskId, @Param("storeTypeList") List<String> storeTypeList);

    List<BundlingTaskDetail> selectByTaskIdAndDictCodes(@Param("taskId") Long taskId, @Param("dictCodeList") List<String> dictCodeList);

    List<String> selectDictValueListByCode(@Param("taskId") Long taskId, @Param("dictCodeList") List<String> dictCodeList);

    List<BundlingTaskDetail> selectByTaskIdAndStep(@Param("taskId") Long taskId, @Param("taskStep") Byte taskStep);

    /**
     * 按步骤删除明细
     * @param taskId
     * @param taskStep
     * @return
     */
    int deleteDetailByStep(@Param("taskId") Long taskId, @Param("taskStep") Byte taskStep);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<BundlingTaskDetail> list);

    /**
     * 通过任务ID获取明细集合
     * @param taskId
     * @return
     */
    List<BundlingTaskDetail> selectDetailByTaskId(@Param("taskId") Long taskId);
}
