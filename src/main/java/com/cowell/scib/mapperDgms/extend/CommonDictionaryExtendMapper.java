package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.CommonDictionary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 10:39
 */
@Component
public interface CommonDictionaryExtendMapper {

    List<CommonDictionary> selectByScopeCode(@Param("scopeCode") String scopeCode);

    List<CommonDictionary> selectListByScopeCode(@Param("scopeCodeList") List<String> scopeCodeList);

    List<CommonDictionary> selectByDicCode(@Param("dicCode") String dicCode);

    /**
     * 获取有默认值的字典
     * @param scopeCode
     * @return
     */
    List<CommonDictionary> selectDefValueByScopeCode(@Param("scopeCode") String scopeCode);
}
