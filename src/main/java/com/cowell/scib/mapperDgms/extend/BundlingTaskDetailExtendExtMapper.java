package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.BundlingTaskDetailExtend;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/15 10:01
 */
public interface BundlingTaskDetailExtendExtMapper {

    /**
     * 删除明细扩展
     * @param taskId
     * @param taskId
     * @return
     */
    int deleteDetailByTaskId(@Param("taskId") Long taskId);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<BundlingTaskDetailExtend> list);

    /**
     * 通过明细ID查询
     * @param taskId
     * @param taskDetailIdList
     * @return
     */
    List<BundlingTaskDetailExtend> selectExtendByDetailId(@Param("taskId") Long taskId, @Param("taskDetailIdList") List<Long> taskDetailIdList);

    List<BundlingTaskDetailExtend> selectDetailExtendByPage(@Param("taskId") Long taskId, @Param("taskDetailIdList") List<Long> taskDetailIdList, @Param("page") int page, @Param("perPage") int perPage);

    Long countTaskDetailExtend(@Param("taskId") Long taskId, @Param("taskDetailIdList") List<Long> taskDetailIdList);

}
