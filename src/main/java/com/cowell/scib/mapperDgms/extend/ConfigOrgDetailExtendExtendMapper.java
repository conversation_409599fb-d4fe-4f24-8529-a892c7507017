package com.cowell.scib.mapperDgms.extend;

import com.cowell.scib.entityDgms.ConfigOrgDetailExtend;
import com.cowell.scib.service.dto.rule.ConfigOrgDetailExtendDBStyleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ConfigOrgDetailExtendExtendMapper {
    int batchInsert(List<ConfigOrgDetailExtend> record);

    int delByConfinIdAndDetailIdAndGoods(@Param("configId") Long configId,@Param("detailId") Long detailId,@Param("list") List<String> list);

    List<ConfigOrgDetailExtend> selectByDetailId(@Param("configId") Long configId, @Param("detailId") Long detailId);

    List<ConfigOrgDetailExtend> selectByDetailIdAndType(@Param("configId") Long configId, @Param("detailId") Long detailId, @Param("extendType") Byte extendType);

    List<ConfigOrgDetailExtend> selectByDetailExtendByPage(@Param("configId") Long configId, @Param("detailId") Long detailId,@Param("page") int page,@Param("perPage") int perPage);

    Long countConfigOrgDetailExtend(@Param("configId") Long configId, @Param("detailId") Long detailId);

    int insertConfigOrgDetailExtendList(@Param("list") List<ConfigOrgDetailExtend> list);

    int deleteByconfigIdAndType(@Param("configId") Long configId,@Param("extendType") Byte extendType);

    List<ConfigOrgDetailExtendDBStyleDTO> selectConfigExtendDBByConfigIds(@Param("configIds") List<Long> configIds);

}
