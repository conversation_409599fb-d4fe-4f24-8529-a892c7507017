package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.DgmsCommonQueue;
import com.cowell.scib.entityDgms.DgmsCommonQueueExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DgmsCommonQueueMapper {
    long countByExample(DgmsCommonQueueExample example);

    int deleteByExample(DgmsCommonQueueExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DgmsCommonQueue record);

    int insertSelective(DgmsCommonQueue record);

    List<DgmsCommonQueue> selectByExample(DgmsCommonQueueExample example);

    DgmsCommonQueue selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DgmsCommonQueue record, @Param("example") DgmsCommonQueueExample example);

    int updateByExample(@Param("record") DgmsCommonQueue record, @Param("example") DgmsCommonQueueExample example);

    int updateByPrimaryKeySelective(DgmsCommonQueue record);

    int updateByPrimaryKey(DgmsCommonQueue record);
}