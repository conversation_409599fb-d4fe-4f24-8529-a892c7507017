package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.BundlingTaskDetailExtend;
import com.cowell.scib.entityDgms.BundlingTaskDetailExtendExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BundlingTaskDetailExtendMapper {
    long countByExample(BundlingTaskDetailExtendExample example);

    int deleteByExample(BundlingTaskDetailExtendExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BundlingTaskDetailExtend record);

    int insertSelective(BundlingTaskDetailExtend record);

    List<BundlingTaskDetailExtend> selectByExample(BundlingTaskDetailExtendExample example);

    BundlingTaskDetailExtend selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BundlingTaskDetailExtend record, @Param("example") BundlingTaskDetailExtendExample example);

    int updateByExample(@Param("record") BundlingTaskDetailExtend record, @Param("example") BundlingTaskDetailExtendExample example);

    int updateByPrimaryKeySelective(BundlingTaskDetailExtend record);

    int updateByPrimaryKey(BundlingTaskDetailExtend record);
}