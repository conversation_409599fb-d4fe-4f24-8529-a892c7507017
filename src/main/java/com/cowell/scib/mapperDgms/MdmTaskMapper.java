package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.MdmTask;
import com.cowell.scib.entityDgms.MdmTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MdmTaskMapper {
    long countByExample(MdmTaskExample example);

    int deleteByExample(MdmTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MdmTask record);

    int insertSelective(MdmTask record);

    List<MdmTask> selectByExample(MdmTaskExample example);

    MdmTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MdmTask record, @Param("example") MdmTaskExample example);

    int updateByExample(@Param("record") MdmTask record, @Param("example") MdmTaskExample example);

    int updateByPrimaryKeySelective(MdmTask record);

    int updateByPrimaryKey(MdmTask record);
}