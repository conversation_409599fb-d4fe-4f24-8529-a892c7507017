package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryContents;
import com.cowell.scib.entityDgms.NecessaryContentsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryContentsMapper {
    long countByExample(NecessaryContentsExample example);

    int deleteByExample(NecessaryContentsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryContents record);

    int insertSelective(NecessaryContents record);

    List<NecessaryContents> selectByExample(NecessaryContentsExample example);

    NecessaryContents selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryContents record, @Param("example") NecessaryContentsExample example);

    int updateByExample(@Param("record") NecessaryContents record, @Param("example") NecessaryContentsExample example);

    int updateByPrimaryKeySelective(NecessaryContents record);

    int updateByPrimaryKey(NecessaryContents record);
}