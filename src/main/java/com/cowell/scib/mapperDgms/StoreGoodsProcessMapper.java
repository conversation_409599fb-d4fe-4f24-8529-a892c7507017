package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.StoreGoodsProcess;
import com.cowell.scib.entityDgms.StoreGoodsProcessExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreGoodsProcessMapper {
    long countByExample(StoreGoodsProcessExample example);

    int deleteByExample(StoreGoodsProcessExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreGoodsProcess record);

    int insertSelective(StoreGoodsProcess record);

    List<StoreGoodsProcess> selectByExample(StoreGoodsProcessExample example);

    StoreGoodsProcess selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreGoodsProcess record, @Param("example") StoreGoodsProcessExample example);

    int updateByExample(@Param("record") StoreGoodsProcess record, @Param("example") StoreGoodsProcessExample example);

    int updateByPrimaryKeySelective(StoreGoodsProcess record);

    int updateByPrimaryKey(StoreGoodsProcess record);
}