package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryGroupGoods;
import com.cowell.scib.entityDgms.NecessaryGroupGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryGroupGoodsMapper {
    long countByExample(NecessaryGroupGoodsExample example);

    int deleteByExample(NecessaryGroupGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryGroupGoods record);

    int insertSelective(NecessaryGroupGoods record);

    List<NecessaryGroupGoods> selectByExample(NecessaryGroupGoodsExample example);

    NecessaryGroupGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryGroupGoods record, @Param("example") NecessaryGroupGoodsExample example);

    int updateByExample(@Param("record") NecessaryGroupGoods record, @Param("example") NecessaryGroupGoodsExample example);

    int updateByPrimaryKeySelective(NecessaryGroupGoods record);

    int updateByPrimaryKey(NecessaryGroupGoods record);
}