package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlAdjustmentCycle;
import com.cowell.scib.entityDgms.JymlAdjustmentCycleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JymlAdjustmentCycleMapper {
    long countByExample(JymlAdjustmentCycleExample example);

    int deleteByExample(JymlAdjustmentCycleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlAdjustmentCycle record);

    int insertSelective(JymlAdjustmentCycle record);

    List<JymlAdjustmentCycle> selectByExample(JymlAdjustmentCycleExample example);

    JymlAdjustmentCycle selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlAdjustmentCycle record, @Param("example") JymlAdjustmentCycleExample example);

    int updateByExample(@Param("record") JymlAdjustmentCycle record, @Param("example") JymlAdjustmentCycleExample example);

    int updateByPrimaryKeySelective(JymlAdjustmentCycle record);

    int updateByPrimaryKey(JymlAdjustmentCycle record);
}