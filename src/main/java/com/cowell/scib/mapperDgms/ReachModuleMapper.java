package com.cowell.scib.mapperDgms;

import com.cowell.scib.entity.ReachModule;
import com.cowell.scib.entity.ReachModuleExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReachModuleMapper {
    long countByExample(ReachModuleExample example);

    int deleteByExample(ReachModuleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ReachModule record);

    int insertSelective(ReachModule record);

    List<ReachModule> selectByExample(ReachModuleExample example);

    ReachModule selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ReachModule record, @Param("example") ReachModuleExample example);

    int updateByExample(@Param("record") ReachModule record, @Param("example") ReachModuleExample example);

    int updateByPrimaryKeySelective(ReachModule record);

    int updateByPrimaryKey(ReachModule record);
}