package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryLevelConfig;
import com.cowell.scib.entityDgms.NecessaryLevelConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryLevelConfigMapper {
    long countByExample(NecessaryLevelConfigExample example);

    int deleteByExample(NecessaryLevelConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryLevelConfig record);

    int insertSelective(NecessaryLevelConfig record);

    List<NecessaryLevelConfig> selectByExample(NecessaryLevelConfigExample example);

    NecessaryLevelConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryLevelConfig record, @Param("example") NecessaryLevelConfigExample example);

    int updateByExample(@Param("record") NecessaryLevelConfig record, @Param("example") NecessaryLevelConfigExample example);

    int updateByPrimaryKeySelective(NecessaryLevelConfig record);

    int updateByPrimaryKey(NecessaryLevelConfig record);
}