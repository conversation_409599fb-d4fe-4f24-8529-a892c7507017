package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryCompanyGoods;
import com.cowell.scib.entityDgms.NecessaryCompanyGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryCompanyGoodsMapper {
    long countByExample(NecessaryCompanyGoodsExample example);

    int deleteByExample(NecessaryCompanyGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryCompanyGoods record);

    int insertSelective(NecessaryCompanyGoods record);

    List<NecessaryCompanyGoods> selectByExample(NecessaryCompanyGoodsExample example);

    NecessaryCompanyGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryCompanyGoods record, @Param("example") NecessaryCompanyGoodsExample example);

    int updateByExample(@Param("record") NecessaryCompanyGoods record, @Param("example") NecessaryCompanyGoodsExample example);

    int updateByPrimaryKeySelective(NecessaryCompanyGoods record);

    int updateByPrimaryKey(NecessaryCompanyGoods record);
}