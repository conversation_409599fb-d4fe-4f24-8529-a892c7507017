package com.cowell.scib.mapperDgms;

import com.cowell.scib.entity.DevelopModule;
import com.cowell.scib.entity.DevelopModuleExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DevelopModuleMapper {
    long countByExample(DevelopModuleExample example);

    int deleteByExample(DevelopModuleExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DevelopModule record);

    int insertSelective(DevelopModule record);

    List<DevelopModule> selectByExample(DevelopModuleExample example);

    DevelopModule selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") DevelopModule record, @Param("example") DevelopModuleExample example);

    int updateByExample(@Param("record") DevelopModule record, @Param("example") DevelopModuleExample example);

    int updateByPrimaryKeySelective(DevelopModule record);

    int updateByPrimaryKey(DevelopModule record);

    int updateDevelopModule(DevelopModule record);
}