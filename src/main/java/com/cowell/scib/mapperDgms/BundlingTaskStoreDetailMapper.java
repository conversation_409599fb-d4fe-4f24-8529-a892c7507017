package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.BundlingTaskStoreDetail;
import com.cowell.scib.entityDgms.BundlingTaskStoreDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BundlingTaskStoreDetailMapper {
    long countByExample(BundlingTaskStoreDetailExample example);

    int deleteByExample(BundlingTaskStoreDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BundlingTaskStoreDetail record);

    int insertSelective(BundlingTaskStoreDetail record);

    List<BundlingTaskStoreDetail> selectByExample(BundlingTaskStoreDetailExample example);

    BundlingTaskStoreDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BundlingTaskStoreDetail record, @Param("example") BundlingTaskStoreDetailExample example);

    int updateByExample(@Param("record") BundlingTaskStoreDetail record, @Param("example") BundlingTaskStoreDetailExample example);

    int updateByPrimaryKeySelective(BundlingTaskStoreDetail record);

    int updateByPrimaryKey(BundlingTaskStoreDetail record);
}