package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlCustomizeChooseSku;
import com.cowell.scib.entityDgms.JymlCustomizeChooseSkuExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlCustomizeChooseSkuMapper {
    long countByExample(JymlCustomizeChooseSkuExample example);

    int deleteByExample(JymlCustomizeChooseSkuExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlCustomizeChooseSku record);

    int insertSelective(JymlCustomizeChooseSku record);

    List<JymlCustomizeChooseSku> selectByExample(JymlCustomizeChooseSkuExample example);

    JymlCustomizeChooseSku selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlCustomizeChooseSku record, @Param("example") JymlCustomizeChooseSkuExample example);

    int updateByExample(@Param("record") JymlCustomizeChooseSku record, @Param("example") JymlCustomizeChooseSkuExample example);

    int updateByPrimaryKeySelective(JymlCustomizeChooseSku record);

    int updateByPrimaryKey(JymlCustomizeChooseSku record);
}