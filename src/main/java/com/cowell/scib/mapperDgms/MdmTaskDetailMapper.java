package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.MdmTaskDetail;
import com.cowell.scib.entityDgms.MdmTaskDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MdmTaskDetailMapper {
    long countByExample(MdmTaskDetailExample example);

    int deleteByExample(MdmTaskDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MdmTaskDetail record);

    int insertSelective(MdmTaskDetail record);

    List<MdmTaskDetail> selectByExample(MdmTaskDetailExample example);

    MdmTaskDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MdmTaskDetail record, @Param("example") MdmTaskDetailExample example);

    int updateByExample(@Param("record") MdmTaskDetail record, @Param("example") MdmTaskDetailExample example);

    int updateByPrimaryKeySelective(MdmTaskDetail record);

    int updateByPrimaryKey(MdmTaskDetail record);
}