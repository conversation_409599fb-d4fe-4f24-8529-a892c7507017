package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryLevelRoleConfig;
import com.cowell.scib.entityDgms.NecessaryLevelRoleConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryLevelRoleConfigMapper {
    long countByExample(NecessaryLevelRoleConfigExample example);

    int deleteByExample(NecessaryLevelRoleConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryLevelRoleConfig record);

    int insertSelective(NecessaryLevelRoleConfig record);

    List<NecessaryLevelRoleConfig> selectByExample(NecessaryLevelRoleConfigExample example);

    NecessaryLevelRoleConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryLevelRoleConfig record, @Param("example") NecessaryLevelRoleConfigExample example);

    int updateByExample(@Param("record") NecessaryLevelRoleConfig record, @Param("example") NecessaryLevelRoleConfigExample example);

    int updateByPrimaryKeySelective(NecessaryLevelRoleConfig record);

    int updateByPrimaryKey(NecessaryLevelRoleConfig record);
}