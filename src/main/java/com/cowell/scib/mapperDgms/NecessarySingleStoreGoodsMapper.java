package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessarySingleStoreGoods;
import com.cowell.scib.entityDgms.NecessarySingleStoreGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessarySingleStoreGoodsMapper {
    long countByExample(NecessarySingleStoreGoodsExample example);

    int deleteByExample(NecessarySingleStoreGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessarySingleStoreGoods record);

    int insertSelective(NecessarySingleStoreGoods record);

    List<NecessarySingleStoreGoods> selectByExample(NecessarySingleStoreGoodsExample example);

    NecessarySingleStoreGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessarySingleStoreGoods record, @Param("example") NecessarySingleStoreGoodsExample example);

    int updateByExample(@Param("record") NecessarySingleStoreGoods record, @Param("example") NecessarySingleStoreGoodsExample example);

    int updateByPrimaryKeySelective(NecessarySingleStoreGoods record);

    int updateByPrimaryKey(NecessarySingleStoreGoods record);
}