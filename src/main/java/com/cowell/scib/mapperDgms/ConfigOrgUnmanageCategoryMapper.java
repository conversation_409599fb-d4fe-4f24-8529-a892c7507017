package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.ConfigOrgUnmanageCategory;
import com.cowell.scib.entityDgms.ConfigOrgUnmanageCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ConfigOrgUnmanageCategoryMapper {
    long countByExample(ConfigOrgUnmanageCategoryExample example);

    int deleteByExample(ConfigOrgUnmanageCategoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigOrgUnmanageCategory record);

    int insertSelective(ConfigOrgUnmanageCategory record);

    List<ConfigOrgUnmanageCategory> selectByExample(ConfigOrgUnmanageCategoryExample example);

    ConfigOrgUnmanageCategory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigOrgUnmanageCategory record, @Param("example") ConfigOrgUnmanageCategoryExample example);

    int updateByExample(@Param("record") ConfigOrgUnmanageCategory record, @Param("example") ConfigOrgUnmanageCategoryExample example);

    int updateByPrimaryKeySelective(ConfigOrgUnmanageCategory record);

    int updateByPrimaryKey(ConfigOrgUnmanageCategory record);
}