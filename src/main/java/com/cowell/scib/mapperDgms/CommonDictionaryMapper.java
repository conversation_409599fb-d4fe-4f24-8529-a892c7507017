package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.CommonDictionary;
import com.cowell.scib.entityDgms.CommonDictionaryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CommonDictionaryMapper {
    long countByExample(CommonDictionaryExample example);

    int deleteByExample(CommonDictionaryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CommonDictionary record);

    int insertSelective(CommonDictionary record);

    List<CommonDictionary> selectByExample(CommonDictionaryExample example);

    CommonDictionary selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CommonDictionary record, @Param("example") CommonDictionaryExample example);

    int updateByExample(@Param("record") CommonDictionary record, @Param("example") CommonDictionaryExample example);

    int updateByPrimaryKeySelective(CommonDictionary record);

    int updateByPrimaryKey(CommonDictionary record);
}