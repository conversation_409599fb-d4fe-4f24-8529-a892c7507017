package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffect;
import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustEffectExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlStoreSkuLimitAdjustEffectMapper {
    long countByExample(JymlStoreSkuLimitAdjustEffectExample example);

    int deleteByExample(JymlStoreSkuLimitAdjustEffectExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuLimitAdjustEffect record);

    int insertSelective(JymlStoreSkuLimitAdjustEffect record);

    List<JymlStoreSkuLimitAdjustEffect> selectByExample(JymlStoreSkuLimitAdjustEffectExample example);

    JymlStoreSkuLimitAdjustEffect selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuLimitAdjustEffect record, @Param("example") JymlStoreSkuLimitAdjustEffectExample example);

    int updateByExample(@Param("record") JymlStoreSkuLimitAdjustEffect record, @Param("example") JymlStoreSkuLimitAdjustEffectExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuLimitAdjustEffect record);

    int updateByPrimaryKey(JymlStoreSkuLimitAdjustEffect record);
}