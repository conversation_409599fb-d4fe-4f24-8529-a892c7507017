package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.ConfigOrg;
import com.cowell.scib.entityDgms.ConfigOrgExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ConfigOrgMapper {
    long countByExample(ConfigOrgExample example);

    int deleteByExample(ConfigOrgExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigOrg record);

    int insertSelective(ConfigOrg record);

    List<ConfigOrg> selectByExample(ConfigOrgExample example);

    ConfigOrg selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigOrg record, @Param("example") ConfigOrgExample example);

    int updateByExample(@Param("record") ConfigOrg record, @Param("example") ConfigOrgExample example);

    int updateByPrimaryKeySelective(ConfigOrg record);

    int updateByPrimaryKey(ConfigOrg record);
}