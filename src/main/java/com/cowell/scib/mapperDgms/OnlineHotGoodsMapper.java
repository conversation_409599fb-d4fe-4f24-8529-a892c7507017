package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.OnlineHotGoods;
import com.cowell.scib.entityDgms.OnlineHotGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface OnlineHotGoodsMapper {
    long countByExample(OnlineHotGoodsExample example);

    int deleteByExample(OnlineHotGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(OnlineHotGoods record);

    int insertSelective(OnlineHotGoods record);

    List<OnlineHotGoods> selectByExample(OnlineHotGoodsExample example);

    OnlineHotGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") OnlineHotGoods record, @Param("example") OnlineHotGoodsExample example);

    int updateByExample(@Param("record") OnlineHotGoods record, @Param("example") OnlineHotGoodsExample example);

    int updateByPrimaryKeySelective(OnlineHotGoods record);

    int updateByPrimaryKey(OnlineHotGoods record);
}