package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjust;
import com.cowell.scib.entityDgms.JymlStoreSkuLimitAdjustExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlStoreSkuLimitAdjustMapper {
    long countByExample(JymlStoreSkuLimitAdjustExample example);

    int deleteByExample(JymlStoreSkuLimitAdjustExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuLimitAdjust record);

    int insertSelective(JymlStoreSkuLimitAdjust record);

    List<JymlStoreSkuLimitAdjust> selectByExample(JymlStoreSkuLimitAdjustExample example);

    JymlStoreSkuLimitAdjust selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuLimitAdjust record, @Param("example") JymlStoreSkuLimitAdjustExample example);

    int updateByExample(@Param("record") JymlStoreSkuLimitAdjust record, @Param("example") JymlStoreSkuLimitAdjustExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuLimitAdjust record);

    int updateByPrimaryKey(JymlStoreSkuLimitAdjust record);
}