package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.MinDisplayChangeRecord;
import com.cowell.scib.entityDgms.MinDisplayChangeRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MinDisplayChangeRecordMapper {
    long countByExample(MinDisplayChangeRecordExample example);

    int deleteByExample(MinDisplayChangeRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MinDisplayChangeRecord record);

    int insertSelective(MinDisplayChangeRecord record);

    List<MinDisplayChangeRecord> selectByExample(MinDisplayChangeRecordExample example);

    MinDisplayChangeRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MinDisplayChangeRecord record, @Param("example") MinDisplayChangeRecordExample example);

    int updateByExample(@Param("record") MinDisplayChangeRecord record, @Param("example") MinDisplayChangeRecordExample example);

    int updateByPrimaryKeySelective(MinDisplayChangeRecord record);

    int updateByPrimaryKey(MinDisplayChangeRecord record);
}