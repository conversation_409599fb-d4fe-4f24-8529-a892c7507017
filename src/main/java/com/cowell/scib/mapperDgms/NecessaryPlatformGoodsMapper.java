package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.NecessaryPlatformGoods;
import com.cowell.scib.entityDgms.NecessaryPlatformGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NecessaryPlatformGoodsMapper {
    long countByExample(NecessaryPlatformGoodsExample example);

    int deleteByExample(NecessaryPlatformGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(NecessaryPlatformGoods record);

    int insertSelective(NecessaryPlatformGoods record);

    List<NecessaryPlatformGoods> selectByExample(NecessaryPlatformGoodsExample example);

    NecessaryPlatformGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") NecessaryPlatformGoods record, @Param("example") NecessaryPlatformGoodsExample example);

    int updateByExample(@Param("record") NecessaryPlatformGoods record, @Param("example") NecessaryPlatformGoodsExample example);

    int updateByPrimaryKeySelective(NecessaryPlatformGoods record);

    int updateByPrimaryKey(NecessaryPlatformGoods record);
}