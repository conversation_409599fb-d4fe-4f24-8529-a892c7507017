package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.BundlingTaskDetail;
import com.cowell.scib.entityDgms.BundlingTaskDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BundlingTaskDetailMapper {
    long countByExample(BundlingTaskDetailExample example);

    int deleteByExample(BundlingTaskDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BundlingTaskDetail record);

    int insertSelective(BundlingTaskDetail record);

    List<BundlingTaskDetail> selectByExample(BundlingTaskDetailExample example);

    BundlingTaskDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BundlingTaskDetail record, @Param("example") BundlingTaskDetailExample example);

    int updateByExample(@Param("record") BundlingTaskDetail record, @Param("example") BundlingTaskDetailExample example);

    int updateByPrimaryKeySelective(BundlingTaskDetail record);

    int updateByPrimaryKey(BundlingTaskDetail record);
}