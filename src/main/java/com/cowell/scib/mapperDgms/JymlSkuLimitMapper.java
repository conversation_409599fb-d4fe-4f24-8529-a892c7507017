package com.cowell.scib.mapperDgms;

import com.cowell.scib.entityDgms.JymlSkuLimit;
import com.cowell.scib.entityDgms.JymlSkuLimitExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlSkuLimitMapper {
    long countByExample(JymlSkuLimitExample example);

    int deleteByExample(JymlSkuLimitExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlSkuLimit record);

    int insertSelective(JymlSkuLimit record);

    List<JymlSkuLimit> selectByExample(JymlSkuLimitExample example);

    JymlSkuLimit selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlSkuLimit record, @Param("example") JymlSkuLimitExample example);

    int updateByExample(@Param("record") JymlSkuLimit record, @Param("example") JymlSkuLimitExample example);

    int updateByPrimaryKeySelective(JymlSkuLimit record);

    int updateByPrimaryKey(JymlSkuLimit record);
}