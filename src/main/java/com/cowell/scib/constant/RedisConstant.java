package com.cowell.scib.constant;

/**
 * <AUTHOR>
 * @date 2022/8/31 19:03
 */
public class RedisConstant {

    public final static String DEVELOP_RECORD_REMINDER_KEY = "DEVELOP_RECORD_REMINDER_KEY_";

    public final static Integer DEVELOP_RECORD_REMINDER_TIME = 1;

    /**
     * 更新平台一店一目数据的key
     */
    public final static String NECESSARY_ADD_STORE_GOODS_CACHE_KEY="SCIB_NECESSARY_ADD_STORE_GOODS_";

    /**
     * 判断更新平台一店一目数据是否完成key
     */
    public final static String NECESSARY_FINISHED_ADD_STORE_GOODS_CACHE_KEY = "SCIB_NECESSARY_FINISHED_ADD_STORE_GOODS_";


    /**
     * 判断任务+门店是否已经处理过 原来已存在本次不存在删除动作  https://doc.weixin.qq.com/sheet/e3_ADQAWgZUAB0gfC9UKdYQjSKkSslR9?scode=AGcA1wdVABAPSNYESSADQAWgZUAB0&tab=79jj7g
     */
    public final static String NECESSARY_TASK_STORE_DEL_EXIST_CACHE_KEY = "SCIB_NECESSARY_TASK_STORE_DEL_EXISTS_";

    /**
     * 推送平台数据key
     */
    public static final String PUSH_PLATFORM_GOODS ="SCIB_PUSH_PLATFORM_GOODS_" ;
    /**
     * 推送平台数据完成key
     */
    public static final String PUSH_FINISH_PLATFORM_GOODS ="SCIB_PUSH_FINISH_PLATFORM_GOODS_" ;
    /**
     * 推送企业数据key
     */
    public static final String PUSH_COMPANY_GOODS ="SCIB_PUSH_COMPANY_GOODS_" ;
    /**
     * 推送企业数据完成key
     */
    public static final String PUSH_FINISH_COMPANY_GOODS ="SCIB_PUSH_FINISH_COMPANY_GOODS_" ;
    /**
     * 推送店型必备数据key
     */
    public static final String PUSH_STORE_TYPE_GOODS ="SCIB_PUSH_STORE_TYPE_GOODS_" ;
    /**
     * 推送店型必备数据完成key
     */
    public static final String PUSH_FINISH_STORE_TYPE_GOODS ="SCIB_PUSH_FINISH_STORE_TYPE_GOODS_" ;
    /**
     * 推送店型选配数据key
     */
    public static final String PUSH_CHOOSE_TYPE_GOODS ="SCIB_PUSH_CHOOSE_PUSH_TYPE_GOODS_" ;
    /**
     * 推送店型选配完成数据key
     */
    public static final String PUSH_FINISH_CHOOSE_TYPE_GOODS ="SCIB_PUSH_FINISH_CHOOSE_PUSH_TYPE_GOODS_" ;
    /**
     * 推送单店数据key
     */
    public static final String PUSH_SINGLE_STORE_GOODS ="SCIB_PUSH_SINGLE_STORE_GOODS_" ;
    /**
     * 推送单店完成数据key
     */
    public static final String PUSH_FINISH_SINGLE_STORE_GOODS ="SCIB_PUSH_FINISH_SINGLE_STORE_GOODS_" ;
    /**
     * 获取用户组织/角色
     */
    public static final  String GET_EMPLOYEES_BY_ORG_ID_AND_ROLE = "SCIB_EMPLOYEES_BY_ORG_ID_AND_ROLE_";

    /**
     * 获取商品属性从forest
     */
    public static final String SCIB_BUSINESS_GOODS_SPU_PROPERTY ="SCIB_BUSINESS_GOODS_SPU_PROPERTY_BASE:%s:%s" ;
}
