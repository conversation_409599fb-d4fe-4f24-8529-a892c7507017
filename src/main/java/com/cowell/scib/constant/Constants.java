package com.cowell.scib.constant;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/8/29 14:41
 */
public class Constants {
    public static String KLINE = "_";
    public static String DATE_REGLAR = "^[0-9]+(.[0-9]{1,3})?$";
    public static String CATALOG = "develop/";
    public static Byte DEL_STATUS = -1;
    public static Byte NORMAL_STATUS = 0;
    public static Integer DEF_VERSION = 0;
    public static Long ROOT_ORG_ID = 3L;

    public static Byte STORE = 1;
    public static Byte GOODS = 2;

    public static Byte OBJECT = 1;
    public static Byte CHECKBOX = 2;
    public static Byte COLLECTION = 3;

    public static String STORE_WHITE_LIST = "storeWhiteList";
    public static String STORE_BLACK_LIST = "storeBlackList";
    public static String GOODS_BLACK_LIST = "goodsBlackList";
    public static String JYMU_STORE_BLACK_LIST = "jymu_storeBlackList";
    public static String JYMU_BUSINESS_ORG_LIST = "jymu_businesslist";
    public static String UNMANAGE_CATEGORY_LIST = "unmanageCategoryList";
    public static String JYMU_STORE_WHITE_LIST = "jymu_storeWhiteList";
    public static String BLKC_4_1_BUSINESSLIST = "blkc_4_1_businesslist";
    public static String BLKC_4_1_STOREWHITELIST = "blkc_4_1_storeWhiteList";
    public static String BLKC_4_1_STOREBLACKLIST = "blkc_4_1_storeBlackList";
    public static String BLKC_4_1_GOODSBLACKLIST = "blkc_4_1_goodsBlackList";
    public static String BLKC_4_1_5 = "blkc_4_1_5";
    public static String BLKC_4_1_6 = "blkc_4_1_6";
    public static String BLKC_4_1_7 = "blkc_4_1_7";
    public static String BLKC_4_2_BUSINESSLIST = "blkc_4_2_businesslist";
    public static String BLKC_4_2_STOREWHITELIST = "blkc_4_2_storeWhiteList";
    public static String BLKC_4_2_STOREBLACKLIST = "blkc_4_2_storeBlackList";
    public static String BLKC_4_2_GOODSBLACKLIST = "blkc_4_2_goodsBlackList";

    public static String BLKC_4_3_BUSINESSLIST = "blkc_4_3_businesslist";
    public static String BLKC_4_3_STOREWHITELIST = "blkc_4_3_storeWhiteList";
    public static String BLKC_4_3_STOREBLACKLIST = "blkc_4_3_storeBlackList";
    public static String BLKC_4_3_GOODSBLACKLIST = "blkc_4_3_goodsBlackList";

    public static Integer QUERY_SEARCH_PAGESIZE = 50;

    public static Integer INSERT_MAX_SIZE = 200;
    public static Integer SELECT_MAX_SIZE = 200;

    public static Integer DELETE_MAX_SIZE = 200;

    public static final Integer EXPORT_ONCE_QUERY_MAX = 500;

    public static String UNDER_LINE = "_";

    public static String SMARTGOODS_MENU = "smartGoods";

    public static String SUPPER_ROLE = "SZHSPGLY";

    public static String UP_NUM = "\\d+";

    public static String ZXCY_RX = "BB_3_4";
    public static String ZXCY_OTC = "BB_3_5";
    public static String ZYSR_YSZY = "BB_3_6";
    public static String ZYSR_PFZY = "BB_3_7";
    public static String BJSP = "BB_3_8";
    public static String YLQX = "BB_3_9";


    public static String DXBE_BJSP = "BB_5_3_4";

    public static Pattern YEAR_PATTERN = Pattern.compile("^(19|2[0-9])[0-9]{2}$");

    public static String VALUE_SPLITE = ";";

    public static String COMMA_SPLITE = ",";

    public static String QUERY_LIKE = "%";

    //尖括号
    public static final String GT = ">";

    public static final Integer FEIGN_ONCE_QUERY_MAX = 100;

    public static final Integer FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE = 100;
    public static final Integer TIDB_UPDATE_MAX_VALUE = 10000;

    /**
     * 店长角色编码
     */
    public static final String DZ_ROLE_CODE = "DZ";

    /**
     * 店员角色编码
     */
    public static final  String DY_ROLE_CODE = "DY";
    /**
     * orgId
     */
    public static final  Integer PERMISSION_ORG_ID_TYPE_ORG_ID = 1;

    /**
     * outId
     */
    public static final  Integer PERMISSION_ORG_ID_TYPE_OUT_ID = 2;

    /**
     * 当前组织
     */
    public static final Integer PERMISSION_QUERY_SCOPE_NOT = 1;
    /**
     * 下载中心平台类型
     */
    public static final Integer PLATFORM_TYPE = 1;

    /**
     * 文件下载状态： 2-成功
     */
    public static final Integer FILE_DOWN_STATUS_SUCCESS = 2;

    /**
     * 文件下载状态： 3-失败
     */
    public static final Integer FILE_DOWN_STATUS_FAIL = 3;

    /**
     * 商品大类是平台组货店型的
     */
    public static final List<String> GOOD_FIRST= Arrays.asList("11", "13", "14");
    /**
     * 商品中类类是中参店型
     */
    public static final String GOOD_SECOND= "1201";

    // 平台店型 店型对应的商品大类
    public static final List<Long> STORE_TYPE_CATEGORY = Lists.newArrayList(11L,13L,14L,15L,16L,17L,18L);

    // 中参店型对应的商品中类
    public static final List<Long> ZS_STORE_TYPE_CATEGORY = Lists.newArrayList(1201L);

    // 配方店型对应的商品中类
    public static final List<Long> PF_STORE_TYPE_CATEGORY = Lists.newArrayList(1202L);

    //动态建表
    public static final String TRACK_RESULT_ALL_DETAIL="track_retult_all_detail_";

    public static final String NEW_STORE_RECOMMEND_DETAIL="track_retult_new_store_all_detail_";

    public static final String NEW_STORE_RECOMMEND_RANK_DETAIL="track_retult_new_store_rank_detail_";

    public static final String BIZCODE = "ISCM";
    public static final String BIZTYPE = "1001";

    public static final Integer SEND_NECESSARY_DATA = 40;

    public static final Integer SEND_NECESSARY_DATA_STORE_GOOD = 100;

    public static final Integer BATCH_INSERT_MAX_VALUE = 100;

    public static final String NECESSARY_DATA_VERSION_ONE = "1";

    public static final String NECESSARY_DATA_VERSION_ZERO = "0";

    public static final Integer INIT_VERSION = 0;

    public static final Integer INTEGER_NEGATIVE_ONE= -1;

    public static final Integer INTEGER_ZERO=0;

    public static final Integer INTEGER_ONE=1;

    public static final Integer INTEGER_TWO=2;

    public static final Long ZERO = 0L;

    public static final String GOODS_OTC = "OTC";

    public static final String GOODS_RX = "RX";

    public static Byte NO_PUSH_MDM_STATUS = 9;

    public static final String TASK_EXECUTION = "9999";

    public static final String IMPORT_NULL = "有%s行门店编码为空未导入，请注意";

    public static final Long SYS_USER_ID = -1L;

    public static final String SYS_USER_NAME = "系统管理员";
    public static final String STORE_START = "营业";

    public static final String FORMAT = "药店连锁";

    public static final List<String> EXCLUDE_STORE= Arrays.asList("共享药房", "电商店");

    public static final List<String> STORE_ATTR= Arrays.asList("直营-自建", "直营-收购");

    public static final String MANAGE_STATE = "常规经营门店";

    public static final Integer ADD_SEASON_GOODS = 1;

    public static final Integer FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE = 20;
    public static final Integer FEIGN_START_PAGE = 1;

    public static final String GOODS_LINE_OUTLIER = "goodslineOutlier";

    public static final Integer INSERT_MAX_VALUE = 1000;
}
