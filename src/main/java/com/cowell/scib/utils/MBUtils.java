package com.cowell.scib.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.service.dto.MbCommonDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.*;

@Component
public class MBUtils<T> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RestTemplate restTemplate;
    /**
     * 获取url
     * @param json
     * @return
     */
    public static String getMbUrl(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject.get("url").toString();
    }

    /**
     * 获取userName
     * @param json
     * @return
     */
    public static String getUserName(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject.get("userName").toString();
    }

    /**
     * 获取password
     * @param json
     * @return
     */
    public static String getPassword(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject.get("password").toString();
    }

    /**
     * MB 配置
     * @return
     */
    public static MbCommonDTO getMbConfig(String json) {
        MbCommonDTO mbCommonDTO = JSONObject.parseObject(json, MbCommonDTO.class);
        mbCommonDTO.setBdatetime(DateUtils.conventDateStrByDate(new Date()));
        mbCommonDTO.setBguid("DGMS" + UUID.randomUUID().toString());
        return mbCommonDTO;
    }

    /**
     * 组装请求数据
     * @param json mb配置json
     * @param datas 请求参数
     * @return
     * @throws JsonProcessingException
     */
    public static <T>String assembleParam(String json, List<T> datas) throws JsonProcessingException {
        List<MbCommonDTO> mbTable = new ArrayList<>();
        String dateTime = DateUtils.conventDateStrByDate(new Date());
        datas.forEach(data -> {
            MbCommonDTO mbConfig = JSONObject.parseObject(json, MbCommonDTO.class);
            mbConfig.setBdatetime(dateTime);
            mbConfig.setBguid("DGMS" + UUID.randomUUID().toString());
            mbConfig.setBdata(JSON.toJSONString(data));
            mbTable.add(mbConfig);
        });
        Map<String, List<MbCommonDTO>> mbMsg = new HashMap<>();
        mbMsg.put("Table", mbTable);
        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
    }

    /**
     * 组装请求数据
     * @param json mb配置json
     * @param data 请求参数
     * @return
     * @throws JsonProcessingException
     */
    public static <T>String assembleParam(String json, T data) throws JsonProcessingException {
        MbCommonDTO mbConfig = getMbConfig(json);
        mbConfig.setBdata(JSON.toJSONString(data));
        List<MbCommonDTO> mbTable = new ArrayList<>();
        mbTable.add(mbConfig);
        Map<String, List<MbCommonDTO>> mbMsg = new HashMap<>();
        mbMsg.put("Table", mbTable);
        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
    }

    /**
     * 推送至MB
     * @param mbUrl url地址
     * @param jsonParam 参数
     * @return
     * @throws Exception
     */
    @Retryable(value = RestClientException.class, maxAttempts = 3,
            backoff = @Backoff(delay = 5000L, multiplier = 1))
    public String pushToMB(String mbUrl, String jsonParam) throws Exception {
        try {
            logger.info("MBUtils|MB address: {}", mbUrl);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(jsonParam, headers);
            restTemplate.getMessageConverters().forEach(v -> {
                if (v instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) v).setDefaultCharset(Charset.forName("UTF-8"));
                }
            });
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(mbUrl, request, String.class);
            logger.info("MBUtils|pushToMB|HttpStatus: {}", responseEntity.getStatusCode());
            // 判断请求是否发生异常
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.info("MBUtils|pushToMB|请求失败 -> {}", jsonParam);
                throw new RestClientException(responseEntity.getBody());
            }
            if (HttpStatus.OK.equals(responseEntity.getStatusCode()) || HttpStatus.ACCEPTED.equals(responseEntity.getStatusCode())) {
                logger.info("MBUtils|pushToMB成功 -> {}");
            }
            return responseEntity.getBody();
        } catch (Exception e) {
            if (e instanceof RestClientException) {
                logger.error("MBUtils|pushToMB|请求异常,即将进行重试 -> {}", jsonParam, e);
                throw e;
            }
            logger.error("MBUtils|pushToMB|发生异常", e);
            throw e;
        }
    }

    /**
     * 推送至MB
     * @param mbUrl url地址
     * @param jsonParam 参数
     * @return
     * @throws Exception
     */
    @Retryable(value = RestClientException.class, maxAttempts = 3,
            backoff = @Backoff(delay = 5000L, multiplier = 1))
    public String pushToMBByUserInfo(String mbUrl, String jsonParam, String userName, String password) throws Exception {
        try {
            logger.info("MBUtils|发送消息到MB: {}", jsonParam);
            logger.info("MBUtils|MB address: {}", mbUrl);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (StringUtils.isNotEmpty(userName) && StringUtils.isNotEmpty(password)) {
                String byteArrayToBase64 = new String(Base64.encodeBase64((userName + ":" + password).getBytes()));
                headers.add("Authorization", "Basic " + byteArrayToBase64);
            }

            HttpEntity<String> request = new HttpEntity<>(jsonParam, headers);
            restTemplate.getMessageConverters().forEach(v -> {
                if (v instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) v).setDefaultCharset(Charset.forName("UTF-8"));
                }
            });
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(mbUrl, request, String.class);
            logger.info("MBUtils|pushToMB|HttpStatus: {}", responseEntity.getStatusCode());
            // 判断请求是否发生异常
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.info("MBUtils|pushToMB|请求失败 -> {}", jsonParam);
                throw new RestClientException(responseEntity.getBody());
            }
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())
                    || HttpStatus.ACCEPTED.equals(responseEntity.getStatusCode())) {
                logger.info("MBUtils|pushToMB成功 -> {}", responseEntity);
            }
            String body = responseEntity.getBody();
            logger.info("responseEntity.getBody() -> {}", responseEntity.getBody());
            if (StringUtils.isBlank(responseEntity.getBody()) || "\"\"".equals(responseEntity.getBody())) {
                logger.info("MBUtils|pushToMB成功 but response is empty, return");
                return "";
            }
            JSONObject resultJson = JSONObject.parseObject(responseEntity.getBody());
            if (!resultJson.containsKey("DATA")) {
                logger.info("MBUtils|pushToMB|请求失败,返回值没有DATA -> {}", resultJson);
                throw new RestClientException(responseEntity.getBody());
            }
            logger.info("MBUtils|pushToMB成功 resultJson -> {}", resultJson);
            return JSON.toJSONString(resultJson.get("DATA"));
        } catch (Exception e) {
            if (e instanceof RestClientException) {
                logger.error("MBUtils|pushToMB|请求异常,即将进行重试 -> {}", jsonParam, e);
                throw e;
            }
            logger.error("MBUtils|pushToMB|发生异常", e);
            throw e;
        }
    }

}
