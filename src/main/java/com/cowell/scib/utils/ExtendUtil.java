package com.cowell.scib.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

public class ExtendUtil {

    public static String putExtendValue(String extendStr, String key, Object value) {
        if (StringUtils.isBlank(key)) {
            return extendStr;
        }
        JSONObject jsonObject;
        if (StringUtils.isNotEmpty(extendStr)) {
            jsonObject = JSON.parseObject(extendStr);
        } else {
            jsonObject = new JSONObject();
        }
        jsonObject.put(key, value);
        return jsonObject.toJSONString();
    }

    public static <M> M getExtendValue(String extendStr, String key, Class<M> clazz) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        if (StringUtils.isBlank(extendStr)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(extendStr);
        return jsonObject.getObject(key, clazz);
    }

    public static <M> List<M> getExtendList(String extendStr, String key, Class<M> clazz) {
        if (StringUtils.isBlank(key)) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(extendStr)) {
            return Collections.emptyList();
        }
        JSONObject jsonObject = JSON.parseObject(extendStr);
        if (jsonObject.containsKey(key)) {
            return jsonObject.getJSONArray(key).toJavaList(clazz);
        }
        return Collections.emptyList();
    }
}
