package com.cowell.scib.utils;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.cowell.scib.service.AsyncImportTrackResultGoodsConfig;
import com.cowell.scib.service.dto.necessaryContents.NecessaryAddMsg;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;

public class ExcelCheck {

    private static Logger logger = LoggerFactory.getLogger(ExcelCheck.class);

    /**
     * 检验导入Excel的格式
     * @param excel
     * @return
     * @throws Exception
     */
    public static String checkExcelPattern(MultipartFile excel, Object object,List<String> CollNameList ) throws Exception {
        if(excel == null || excel.isEmpty()){
            throw new Exception("导入文件不能为空！");
        }
        //用于拼接校验结果
        StringBuilder builder = new StringBuilder();

        //校验文件表头
        BufferedInputStream fin = new BufferedInputStream(excel.getInputStream());
        Workbook wb = null;
        try{
            wb = WorkbookFactory.create(fin);;
        }catch(Exception e){
            wb = WorkbookFactory.create(fin);;
        }
        //获取注解当中的值
        Map<String, List<String>> annotationValue = getAnnotationValue(object);
        logger.info("annotationValue:{}", JSONObject.toJSONString(annotationValue));
        List<String> annotationName = annotationValue.get("annotationName");
        logger.info("annotationName:{}", JSONObject.toJSONString(annotationName));
        //获取到的实体注解名称顺序要与excel表头顺序保持一样
        String[] columnName = annotationName.toArray(new String[]{});
        Sheet sheet = wb.getSheetAt(0);
        Row row = sheet.getRow(1);
        if (row != null && row.getLastCellNum() >= columnName.length-CollNameList.size()) {
            int lastCellNum = row.getLastCellNum();
            for (int idx = 0; idx < lastCellNum; idx++) {
                String value = getCellValue(row.getCell(idx)).trim();
                if (idx <  columnName.length) {
                    if (CollectionUtils.isNotEmpty(CollNameList)&&CollNameList.contains(columnName[idx])){
                        continue;
                    }
                    if (StringUtils.isBlank(value) || !columnName[idx].equals(value)) {
                        builder.append("第" + (idx + 1) + "列表头应为" + columnName[idx]+"!");
                    }
                } else {
                    if (idx ==  columnName.length-CollNameList.size()) {
                        builder.append("导入文件只应该有:"+ columnName.length+"列!");
                    }
                }
            }
        } else {
            builder.append("上传文件应与模板文件表头保持一致；");
        }
        if(builder.length()>0){
            builder.append("请下载模板按照模板表头顺序进行上传!");
            //builder.setCharAt(builder.length()-1, '！');
        }
        return builder.toString();
    }

    /**
     * 获取cell值
     *
     * @param cell
     * @return
     */
    private static String getCellValue(Cell cell) {
        String cellValue = "";
        // 以下是判断数据的类型
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC: // 数字
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    cellValue = sdf.format(DateUtil.getJavaDate(cell.getNumericCellValue())).toString();
                } else {
                    DataFormatter dataFormatter = new DataFormatter();
                    cellValue = dataFormatter.formatCellValue(cell);
                }
                break;
            case Cell.CELL_TYPE_STRING: // 字符串
                cellValue = cell.getStringCellValue();
                break;
            case Cell.CELL_TYPE_BOOLEAN: // Boolean
                cellValue = cell.getBooleanCellValue() + "";
                break;
            case Cell.CELL_TYPE_FORMULA: // 公式
                cellValue = cell.getCellFormula() + "";
                break;
            case Cell.CELL_TYPE_BLANK: // 空值
                cellValue = "";
                break;
            case Cell.CELL_TYPE_ERROR: // 故障
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }
        return cellValue;
    }

    /**
     * 获取该注解对象的属性值(字段名称和注解的value值)
     * @param object
     * @return
     */
    public static Map<String,List<String>> getAnnotationValue(Object object) {
        Map<String,List<String>>map=new HashMap<>();
        List<String> fieldList=new ArrayList<>();
        List<String> annotationList=new ArrayList<>();
        Field[] fields = object.getClass().getDeclaredFields();
        for(int i = 0 ; i < fields.length ; i++) {
            //设置是否允许访问，不是修改原来的访问权限修饰词。
            fields[i].setAccessible(true);
            ExcelProperty annotation = fields[i].getAnnotation(ExcelProperty.class);
            String[] value = annotation.value();
            for (String s : value) {
                annotationList.add(s);
            }
            fieldList.add(fields[i].getName());
        }
        map.put("fieldName",fieldList);
        map.put("annotationName",annotationList);
        return map;
    }

}
