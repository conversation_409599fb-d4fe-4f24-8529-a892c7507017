package com.cowell.scib.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * Description:获取dao
 * @version V1.0
 */
@Component
public class GetApplicationContextUtils implements ApplicationContextAware  {

	/** ApplicationContext实例*/
	private static ApplicationContext applicationContext=null;


   @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if(GetApplicationContextUtils.applicationContext == null) {
        	GetApplicationContextUtils.applicationContext = applicationContext;
        }
    }

    //获取applicationContext
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    //通过name获取 Bean.
    public static Object getBean(String name){
        return getApplicationContext().getBean(name);
    }

    //通过class获取Bean.
    public static <T> T getBean(Class<T> clazz){
        return getApplicationContext().getBean(clazz);
    }


}
