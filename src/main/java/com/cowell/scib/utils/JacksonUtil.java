package com.cowell.scib.utils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class JacksonUtil {
    private static final Logger logger = LoggerFactory.getLogger(JacksonUtil.class);

    private static final Object locker = new Object();
    private static volatile ObjectMapper objectMapper;

    public static ObjectMapper getObjectMapper() {
        if (objectMapper == null){
            synchronized (locker) {
                if (objectMapper == null){
                    objectMapper = new ObjectMapper();
                }
            }
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
            objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        }
        return objectMapper;
    }

    /**
     * 获取JsonProperty注解
     *
     * @param cls
     * @param fieldName
     * @return
     */
    public static JsonProperty getJsonProperty(Class<?> cls, String fieldName) {
        Field field;
        try {
            field = cls.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            logger.error("field '{}' is not found in Class {}.", fieldName, cls.getName(), e);
            return null;
        }
        return field.getAnnotation(JsonProperty.class);
    }

    /**
     * 获取JsonProperty注解的值
     *
     * @param cls
     * @param fieldName
     * @return
     */
    public static <T> String getJsonPropertyValue(Class<T> cls, String fieldName) {
        Field field;
        try {
            field = cls.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            logger.error("field '{}' is not found in Class {}.", fieldName, cls.getName(), e);
            return "";
        }
        JsonProperty annotation = field.getAnnotation(JsonProperty.class);
        if (annotation == null) {
            return field.getName();
        } else {
            return annotation.value();
        }
    }

    public static String packingJsonData(String jsonKey, List<?> dataList) throws JsonProcessingException {
        Map<String, List<?>> map = new HashMap<>();
        map.put(jsonKey, dataList);
        return JacksonUtil.getObjectMapper().writeValueAsString(map);
    }
}
