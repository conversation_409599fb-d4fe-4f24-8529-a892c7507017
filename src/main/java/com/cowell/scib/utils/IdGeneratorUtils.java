package com.cowell.scib.utils;

import com.cowell.springboot.id.support.IdUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ID生成器
 * <AUTHOR>
 * @date 2023/3/27 18:25
 */
@Slf4j
public class IdGeneratorUtils {

    /**
     * 获取分布式ID
     * @param idCount
     * @param table
     * @return
     */
    public static List<Long> getGeneratorIdBatch(int idCount, String table) {
        if(idCount<=0) {
            idCount = 1;
        }
        List<Long> idList=new ArrayList<Long>(idCount);
        //批量请求数量
        int batchSize=200;
        int pageCount = (idCount + batchSize - 1)/batchSize;
        do {
            idList.addAll(IdUtils.getNextIdBatch(table, idCount));
            pageCount--;
        } while (pageCount > 0);
        log.info("getGeneratorIdBatch|idCount:{}.|table:{}.|idList:{}.", idCount, table, idList);
        return idList.stream().map(v -> Long.valueOf(v.toString())).collect(Collectors.toList());
    }

}
