package com.cowell.scib.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class sendErrorMsgWechatUtil {

    private static final Logger log = LoggerFactory.getLogger(sendErrorMsgWechatUtil.class);

    @Autowired
    private RestTemplate restTemplate;

    @Value("${iscm.recalculation.warn.wxrobot.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=43912d52-14e5-4750-a55a-38ed48fa7568}")
    private String wxRobotUrl;

    private void sendJobErrorMsgWechat(Long taskId, String errorMsg) {
        StringBuilder content = new StringBuilder();
        content.append("【SCIB数字化商品报警】\n")
                .append(">任务编码：<font color=\\\"info\\\">").append(taskId).append("</font>\n ")
                .append(">告警名称：<font color=\\\"info\\\">数字化商品报警</font>\n ")
                .append(">时间：<font color=\\\"info\\\">").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</font>\n")
                .append(">异常原因：<font color=\\\"info\\\">").append(errorMsg).append("</font>\n");
        try {
            String textTemplate = "{\n" +
                    "    \"msgtype\": \"markdown\",\n" +
                    "    \"markdown\": {\n" +
                    "        \"content\": \"" + content.toString() + "\",\n" +
                    "    }\n" +
                    "}";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            HttpEntity<String> request = new HttpEntity<>(textTemplate, headers);
            restTemplate.postForEntity(wxRobotUrl, request, String.class);
        }catch (Exception e){
            log.error("发送错误信息失败", e);
        }
    }
}
