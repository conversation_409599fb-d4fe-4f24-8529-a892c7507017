package com.cowell.scib.utils;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.service.dto.MdmStoreBaseDTO;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2022/8/29 14:38
 */
public class KeyUtil {

    /**
     * 生成key
     * @param moduleCode
     * @param developVersion
     * @return
     */
    public static String delevopFileKey(String moduleCode, String developVersion){
        return new StringBuilder().append(moduleCode).append(Constants.KLINE).append(developVersion).toString();
    }

//
//    public static void main(String[] args) {
//        MdmStoreBaseDTO store = new MdmStoreBaseDTO();
//        store.setSalesLevel("1");
//        MdmStoreExDTO exDTO = new MdmStoreExDTO();
//        BeanUtils.copyProperties(store, exDTO);
//    }
//

}
