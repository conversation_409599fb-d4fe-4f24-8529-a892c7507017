package com.cowell.scib.utils;

import com.cowell.scib.constant.Constants;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;


/**
 * 日期工具类
 */
public class DateUtils {
    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);
    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final String DATE_PATTERN_1= "yyyy/MM/dd";
    public static final String DATE_PATTERN_2= "yyyy/MM/dd HH:mm:ss";
    public static final String DATE_SAP_PATTERN = "yyyyMMdd";
    public static final String DATE_MONTH_PATTERN = "yyyy-MM";
    public static final String DATE_MONTH_PATTERN_1 = "yyyy/MM";
    public static final String DATE_MDM_PATTERN = "yyyyMMddHHmmss";
    public static final String DATE_AZN_PATTERN = "yyyyMMddHHmm";
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_MINUTE_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String DATE_DIRECT_PATTERN = "yyyy_MM_dd";
    public static final String DATE_YEAR_PATTERN = "yyyy";
    public static final String DATE_YL_PATTERN = "yyyyMMdd";
    public static final String DATE_COMMON_PATTERN = "yyyyMM";
    public static final String TIME_PATTERN = "HHmmss";
    public static final String TIME_LINE_PATTERN = "HH:mm:ss";
    public static final String FULL_TIME = "yyyyMMddHHmmssSSS";


    public static final String FORMATTERYYYYMMDD="([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))";

    /**
     * JDK8日期转换 instant->string
     *
     * @param instant
     * @return
     */
    public static String conventDateStrByDate(Instant instant) {
        Date date = Date.from(instant);

        SimpleDateFormat format = new SimpleDateFormat(DATETIME_PATTERN);
        String dateStr = format.format(date);
        return dateStr;
    }

    public static Date dealDateTime(Date date) {
        if(date == null){
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 时
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        // 分
        calendar.set(Calendar.MINUTE, 59);
        // 秒
        calendar.set(Calendar.SECOND, 59);

        return calendar.getTime();

    }

    /**
     * 日期转换 date->string
     *
     * @param date
     * @return
     */
    public static String conventDateStrByDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat format = new SimpleDateFormat(DATETIME_PATTERN);
        String dateStr = format.format(date);
        return dateStr;
    }

    /**
     * 日期转换 date->string
     *
     * @param date
     * @return
     */
    public static String conventDateStrByDate(Date date, String type) {
        if (date == null) {
            return "";
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(type);
            String dateStr = format.format(date);
            return dateStr;
        } catch (Exception e) {
            log.warn("时间格式错误。", e);
            return "";
        }
    }

    /**
     * Transform string instance to date with specific pattern
     *
     * @param dateStr
     * @param
     * @return
     */
    public static Date parse(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(DATE_PATTERN);
        try {
            return sf.parse(dateStr);
        } catch (ParseException e) {
            log.error("=====================>>时间格式化失败:", e);
            return null;
        }
    }

    public static Long transeMilliSecond(String dateStr, String dateType) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(dateType);
        try {
            Date date = sf.parse(dateStr);
            return date.getTime()/1000;
        } catch (ParseException e) {
            log.error("=========transeMilliSecond============>>时间格式化失败:", e);
            return null;
        }
    }

    /**
     * Transform string instance to date with specific pattern
     *
     * @param dateStr
     * @param
     * @return
     */
    public static Date parseMdm(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(DATE_MDM_PATTERN);
        try {
            return sf.parse(dateStr);
        } catch (ParseException e) {
            log.error("=====================>>时间格式化失败:", e);
            return null;
        }
    }

    public static Date parseDateTime(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(DATETIME_PATTERN);
        try {
            return sf.parse(dateStr);
        } catch (ParseException e) {
            log.error("=====================>>时间格式化失败:", e);
            return null;
        }
    }

    public static Date parseDateTime(String dateStr, String dateType) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(dateType);
        try {
            return sf.parse(dateStr);
        } catch (ParseException e) {
            log.error("=====================>>时间格式化失败:", e);
            return null;
        }
    }

    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(DATE_PATTERN_1);
        try {
            return sf.parse(dateStr);
        } catch (ParseException e) {
            log.error("=====================>>时间格式化失败:", e);
            return null;
        }
    }

    /**
     * 获取未来 第 past 天的日期
     * @param past
     * @return
     */
    public static Date getFetureDate(int past) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + past);
            Date today = calendar.getTime();
            SimpleDateFormat format = new SimpleDateFormat(DATETIME_PATTERN);
            String result = format.format(today);
            log.debug("getFetureDate|result,{}.",result);
            return parseDateTime(result);
        }catch (Exception e){
            log.error("获取未来日期异常",e);
        }
        return null;
    }

    public static String conventDateStrByPattern(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        String dateStr = format.format(date);
        return dateStr;
    }

    /**
     * 根据日期获取星期几
     *
     * @param date
     * @return
     */
    public static String getWeekOfDate(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    /**
     * 将日期格式变成从0开始
     * @param dateStr
     * @return
     */
    public static Date dealDateTimeStart(String dateStr){
        Date date = DateUtils.parse(dateStr);
        if(null == date) {
            return date;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 时
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        // 分
        calendar.set(Calendar.MINUTE, 0);
        // 秒
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();

    }

    /**
     * 将日期格式变成结束时间
     * @param date
     * @return
     */
    public static Date dealDateTimeEnd(String date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.parse(date));
        // 时
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        // 分
        calendar.set(Calendar.MINUTE, 59);
        // 秒
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();

    }

    /**
     * 前n天日期
     * @param date 日期
     * @param subDays 前n天
     * @param patten 格式化格式
     * @return
     */
    public static String subDayAndConventDateByPatten(Date date, Integer subDays, String patten){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -Math.abs(subDays));
        return conventDateStrByPattern(calendar.getTime(),patten);
    }

    /**
     * 获取cout月前的月末
     * @param count
     * @param patten
     * @return
     */
    public static String getYmDate(int count, String patten){
        DateTime now = new DateTime(System.currentTimeMillis());
        DateTime dateTime1 = now.minusMonths(count).dayOfMonth().withMaximumValue();
        return dateTime1.toString(patten);
    }

    /**
     * yyyy-MM-dd
     * @param date
     * @return
     */
    public static boolean isFormatterYYYYMMDD(String date){
        return Pattern.matches(FORMATTERYYYYMMDD, date);
    }

//    public static void main(String[] args) {
//        System.out.println(isFormatterYYYYMMDD("2019-01-01"));;
//    }

    /**
     * 获取一天的剩余秒数
     * @param currentDate
     * @return
     */
    public static long getDayRemainingTime(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        return ChronoUnit.SECONDS.between(currentDateTime, midnight);
    }

    /**
     * 获取date的前后days天 负数为前,正数为后
     * @param date
     * @param days
     * @return
     */
    public static Date getFetureDateBydays(Date date, int days){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }



    /**
     * 获取月初或月末时间
     * @param isFirst
     * @return
     */
    public static Date getFirstAndLastDayOfMonth(Date date, boolean isFirst){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if(isFirst) {
            calendar.add(Calendar.MONTH, 0);
            calendar.add(Calendar.MONTH, 0);
        }else {
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 0);
        }
        return calendar.getTime();
    }

    /**
     * 计算当前时间是否在controlTime之间
     * @param controlTime
     * @return
     */
    public static boolean betweenTime(String controlTime) {
        log.info("betweenTime|controlTime:{}", controlTime);
        if(StringUtils.isBlank(controlTime) || "0-0".equals(controlTime)){
            return false;
        }
        try {
            Integer startDate = Integer.parseInt(controlTime.split("-")[0].split(":")[0]);
            Integer endDate = Integer.parseInt(controlTime.split("-")[1].split(":")[0]);

            int now = LocalDateTime.now().getHour();
            if(now<=endDate && now>=startDate){
                return true;
            }
        } catch (NumberFormatException e) {
            log.warn("betweenTime|时间比较失败", e);
            return false;
        }
        return false;
    }

    /**
     * 判断日期格式
     * @param date
     * @param dateType
     * @return
     */
    public static boolean judgeDateFormat(String date, String dateType) {
        log.info("judgeDateFormat|date:{}.", date);
        if(StringUtils.isBlank(date)){
            return false;
        }
        SimpleDateFormat sf = new SimpleDateFormat(dateType);
        try {
            String result = sf.format(sf.parse(date));
            if(result.length()!=dateType.length()){
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 允许为空
     * @param date
     * @param dateType
     * @return
     */
    public static boolean judgeDateFormatNull(String date, String dateType) {
        log.info("judgeDateFormatNull|date:{}.", date);
        if(StringUtils.isBlank(date)){
            return true;
        }
        SimpleDateFormat sf = new SimpleDateFormat(dateType);
        try {
            String result = sf.format(sf.parse(date));
            if(result.length()!=dateType.length()){
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 校验时间date是否符合预期格式dateType
     * @param date
     * @param dateType
     * @return
     */
    public static boolean checkDateFormat(String date, String dateType) {
        if(StringUtils.isBlank(date)){
            return false;
        }
        SimpleDateFormat sf = new SimpleDateFormat(dateType);
        try {
            String result = sf.format(sf.parse(date));
            if(result.length()!=dateType.length()){
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 校验日期是否在合法范围内
     * @param date
     * @return
     */
    public static boolean checkDateScope(String date) {
        if(!date.contains("-")){
            return false;
        }
        String[] split = date.split(" ")[0].split("-");
        if(split == null || split.length == 0){
            return false;
        }

        String monthStr = split[1];
        String dayStr = split[2];
        if(StringUtils.isBlank(monthStr) || !monthStr.matches(Constants.DATE_REGLAR)){
            return false;
        }
        if(StringUtils.isBlank(dayStr) || !dayStr.matches(Constants.DATE_REGLAR)){
            return false;
        }

        DateTime dateTime = new DateTime(DateUtils.parse(date));
        int realDay = dateTime.dayOfMonth().get();
        int realMonth = dateTime.monthOfYear().get();
        if(realDay!=Integer.parseInt(dayStr) || realMonth!=Integer.parseInt(monthStr)){
            return false;
        }
        return true;
    }

}
