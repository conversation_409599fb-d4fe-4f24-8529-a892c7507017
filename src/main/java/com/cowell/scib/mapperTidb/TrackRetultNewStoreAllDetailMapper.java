package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail;
import com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultNewStoreAllDetailMapper {
    long countByExample(TrackRetultNewStoreAllDetailExample example);

    int deleteByExample(TrackRetultNewStoreAllDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackRetultNewStoreAllDetail record);

    int insertSelective(TrackRetultNewStoreAllDetail record);

    List<TrackRetultNewStoreAllDetail> selectByExample(TrackRetultNewStoreAllDetailExample example);

    TrackRetultNewStoreAllDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultNewStoreAllDetail record, @Param("example") TrackRetultNewStoreAllDetailExample example);

    int updateByExample(@Param("record") TrackRetultNewStoreAllDetail record, @Param("example") TrackRetultNewStoreAllDetailExample example);

    int updateByPrimaryKeySelective(TrackRetultNewStoreAllDetail record);

    int updateByPrimaryKey(TrackRetultNewStoreAllDetail record);
}