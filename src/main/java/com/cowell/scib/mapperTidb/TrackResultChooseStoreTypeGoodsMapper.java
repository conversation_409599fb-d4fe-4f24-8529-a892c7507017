package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackResultChooseStoreTypeGoods;
import com.cowell.scib.entityTidb.TrackResultChooseStoreTypeGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackResultChooseStoreTypeGoodsMapper {
    long countByExample(TrackResultChooseStoreTypeGoodsExample example);

    int deleteByExample(TrackResultChooseStoreTypeGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackResultChooseStoreTypeGoods record);

    int insertSelective(TrackResultChooseStoreTypeGoods record);

    List<TrackResultChooseStoreTypeGoods> selectByExample(TrackResultChooseStoreTypeGoodsExample example);

    TrackResultChooseStoreTypeGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackResultChooseStoreTypeGoods record, @Param("example") TrackResultChooseStoreTypeGoodsExample example);

    int updateByExample(@Param("record") TrackResultChooseStoreTypeGoods record, @Param("example") TrackResultChooseStoreTypeGoodsExample example);

    int updateByPrimaryKeySelective(TrackResultChooseStoreTypeGoods record);

    int updateByPrimaryKey(TrackResultChooseStoreTypeGoods record);
}