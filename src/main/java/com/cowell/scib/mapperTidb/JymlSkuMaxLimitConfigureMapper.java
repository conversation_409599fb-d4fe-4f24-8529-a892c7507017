package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample;
import java.util.List;

import com.cowell.scib.service.dto.manageContents.ManageDropOptionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface JymlSkuMaxLimitConfigureMapper {
    long countByExample(JymlSkuMaxLimitConfigureExample example);

    int deleteByExample(JymlSkuMaxLimitConfigureExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlSkuMaxLimitConfigure record);

    int insertSelective(JymlSkuMaxLimitConfigure record);

    List<JymlSkuMaxLimitConfigure> selectByExample(JymlSkuMaxLimitConfigureExample example);

    JymlSkuMaxLimitConfigure selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlSkuMaxLimitConfigure record, @Param("example") JymlSkuMaxLimitConfigureExample example);

    int updateByExample(@Param("record") JymlSkuMaxLimitConfigure record, @Param("example") JymlSkuMaxLimitConfigureExample example);

    int updateByPrimaryKeySelective(JymlSkuMaxLimitConfigure record);

    int updateByPrimaryKey(JymlSkuMaxLimitConfigure record);

    List<ManageDropOptionDTO> selectOptionsByExample(@Param("field") String field, @Param("example")JymlSkuMaxLimitConfigureExample example);
}