package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackResultLevelNecessary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackResultLevelNecessaryExtendMapper {

    long countTrackRetultLevelNecessary(@Param("taskId") Long taskId);

    List<TrackResultLevelNecessary> selectTrackRetultLevelNecessaryByPage(@Param("taskId") Long taskId, @Param("page") int page, @Param("perPage") int perPage);

    int batchDel(@Param("ids")List<Long> ids);
}
