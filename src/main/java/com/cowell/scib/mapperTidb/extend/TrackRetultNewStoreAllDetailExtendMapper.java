package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetail;
import com.cowell.scib.entityTidb.TrackRetultNewStoreAllDetailExample;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface TrackRetultNewStoreAllDetailExtendMapper {
    int batchInsert(@Param("taskId") Long taskId, @Param("list") List<TrackRetultNewStoreAllDetail> record);
    int batchUpdate(@Param("taskId") Long taskId, @Param("list") List<TrackRetultNewStoreAllDetail> record);
    Long count(@Param("taskId") Long taskId);

    List<TrackRetultNewStoreAllDetail> selectByTaskId(@Param("taskId") Long taskId, @Param("start") Integer start, @Param("pageSize") Integer pageSize);


    List<TrackRetultNewStoreAllDetail> selectByTaskIdAndGoodsIds(@Param("taskId")Long taskId, @Param("goodsNoList") List<String> goodsNoList);
}
