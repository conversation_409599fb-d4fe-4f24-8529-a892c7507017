package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord;
import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecordExample;
import com.cowell.scib.service.dto.customize.StoreGoodsCountDTO;
import com.cowell.scib.service.dto.customize.StoreGoodsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface JymlStoreSkuExemptionRecordExtendMapper {
    List<Long> getAllStoreIds();

    List<String> getGoodsNosByStoreId(@Param("storeId") Long storeId);

    int batchInsert(List<JymlStoreSkuExemptionRecord> record);

    List<StoreGoodsCountDTO> getGoodsCountByStore(@Param("storeCodes") List<String> storeCodes);
    List<StoreGoodsDTO> getGoodsByStore(@Param("storeCodes") List<String> storeCodes);
}
