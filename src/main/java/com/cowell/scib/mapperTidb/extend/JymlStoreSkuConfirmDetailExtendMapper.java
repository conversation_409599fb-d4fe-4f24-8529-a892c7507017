package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail;
import com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetailExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface JymlStoreSkuConfirmDetailExtendMapper {

    /**
     * 批量插入商品确认明细记录
     * @param list 待插入的记录列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<JymlStoreSkuConfirmDetail> list);

    int batchUpdateByPrimaryKeySelective(List<JymlStoreSkuConfirmDetail> record);

}
