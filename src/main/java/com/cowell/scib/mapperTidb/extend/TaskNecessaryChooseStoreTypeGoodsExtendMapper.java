package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.service.dto.NecessaryBusinessIdDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskNecessaryChooseStoreTypeGoodsExtendMapper {
    /**
     * 查询连锁
     * @param
     * @return
     */
    List<NecessaryBusinessIdDTO> queryNecessaryChooseStoreTypeGoodsBusinessIdList(@Param("taskId") Long taskId);

    int batchDel(@Param("ids")List<Long> ids);

}
