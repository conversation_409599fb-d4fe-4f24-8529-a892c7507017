package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackRetultEfficiencyAnalyseExtendMapper {

    long countTrackRetultEfficiencyAnalyse(@Param("taskId") Long taskId);

    List<TrackRetultEfficiencyAnalyse> selectTrackRetultEfficiencyAnalyseByPage(@Param("taskId") Long taskId, @Param("page") int page, @Param("perPage") int perPage);

}
