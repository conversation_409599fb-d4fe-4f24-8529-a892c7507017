package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TaskNecessaryCompanyGoods;
import com.cowell.scib.entityTidb.TaskNecessaryPlatformGoods;
import com.cowell.scib.service.dto.NecessaryBusinessIdDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskNecessaryCompanyGoodsExtendMapper {

    /**
     * 查询总数
     * @param
     * @return
     */
    Long countNecessaryCompanyGoods(@Param("taskId") Long taskId);

    /**
     * 分页查询
     * @param
     * @return
     */
    List<TaskNecessaryCompanyGoods> queryNecessaryCompanyGoodsListByPage(@Param("taskId") Long taskId, @Param("start") Integer start, @Param("pageSize") Integer pageSize);


    /**
     * 查询连锁
     * @param
     * @return
     */
    List<NecessaryBusinessIdDTO> queryNecessaryCompanyGoodsBusinessIdList(@Param("taskId") Long taskId);

    int batchDel(@Param("ids")List<Long> ids);

}
