package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultLevelReview;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackRetultLevelReviewExtendMapper {

    long countTrackRetultLevelReview(@Param("taskId") Long taskId);

    List<TrackRetultLevelReview> selectTrackRetultLevelReviewByPage(@Param("taskId") Long taskId, @Param("page") int page, @Param("perPage") int perPage);

    List<TrackRetultLevelReview> selectTrackRetultLevelReviewByChainName(@Param("taskId") Long taskId, @Param("chainName") String chainName);

}
