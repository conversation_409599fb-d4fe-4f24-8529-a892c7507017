package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigureExample;
import com.cowell.scib.service.dto.manageContents.ManageDropOptionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface JymlSkuMaxLimitConfigureExtendMapper {
    List<JymlSkuMaxLimitConfigure> selectAllCategory(@Param("version") Long version);

    /**
     * 根据管控类别查询
     * @param version
     * @return
     */
    List<JymlSkuMaxLimitConfigure> selectWithCtrlCategory(@Param("version") Long version,
                                                          @Param("businessOrgIds") List<Long> businessOrgIds,
                                                          @Param("citys") List<String> citys,
                                                          @Param("storeTypes") List<String> storeTypes,
                                                          @Param("storeTypeNames") List<String> storeTypeNames,
                                                          @Param("ctrlCategorys") List<String> ctrlCategorys,
                                                          @Param("ctrlCategoryNames") List<String> ctrlCategoryNames);

    int updateByExampleSelective(@Param("record") JymlSkuMaxLimitConfigure record, @Param("example") JymlSkuMaxLimitConfigureExample example, @Param("limit") Integer limit);
}
