package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackRetultTop4levelStoregroupExtendMapper {
    long countTrackRetultTop4level(@Param("taskId") Long taskId, @Param("orgId")String orgId);

    List<TrackRetultTop4levelStoregroup> selectTrackRetultTop4levelByPage(@Param("taskId") Long taskId, @Param("orgId")String orgId, @Param("id") Long id, @Param("perPage") int perPage);

    List<String> selectTrackRetultCompidTop4level(@Param("taskId") Long taskId);

    int batchDel(@Param("ids")List<Long> ids);
}
