package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultAllDetail;
import com.cowell.scib.service.dto.TrackResultTaskCompidDTO;
import com.cowell.scib.service.dto.TrackRetultDetailParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackRetultAllDetailExtendMapper {

    long countTrackRetultAllDetail(@Param("taskId") Long taskId, @Param("compid") String compid, @Param("levelList") List<String> levelList);

    List<TrackRetultAllDetail> selectTrackRetultAllDetailByPage(@Param("taskId") Long taskId, @Param("compid") String compid, @Param("levelList") List<String> levelList, @Param("id") Long id, @Param("perPage") int perPage);

    List<TrackResultTaskCompidDTO> selectTrackRetultCompid(@Param("taskId") Long taskId, @Param("levelList") List<String> levelList);

    List<TrackRetultAllDetail> queryTrackRetultDetailByGoods(TrackRetultDetailParam param);

    void delTrackRetultAllDetailById(@Param("taskId") Long taskId, @Param("idList") List<Long> idList);

    void updateLevel(@Param("taskId") Long taskId, @Param("idList") List<Long> idList,@Param("level") String level);

    void batchInsert(@Param("taskId") Long taskId, @Param("list") List<TrackRetultAllDetail> list);

    /**
     * 动态建表
     *
     * @param tableName
     */
    void createTable(@Param(value = "tableName") String tableName);

    void createNewStoreRecommendTable(@Param(value = "tableName") String tableName);

    void createNewStoreRecommendRankTable(@Param(value = "tableName") String tableName);

    int batchDel(@Param("taskId") Long taskId, @Param("ids")List<Long> ids);
}
