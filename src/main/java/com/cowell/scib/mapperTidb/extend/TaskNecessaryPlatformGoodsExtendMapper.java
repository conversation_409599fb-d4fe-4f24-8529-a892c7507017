package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TaskNecessaryPlatformGoods;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import com.cowell.scib.service.param.TaskNecessaryPlatformParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskNecessaryPlatformGoodsExtendMapper {


    /**
     * 查询列表
     * @param listParam
     * @return
     */
    List<TaskNecessaryPlatformGoods> queryNecessaryPlatformGoodList(@Param("listParam") TaskNecessaryPlatformParam listParam);

    /**
     * 查询总数
     * @param param
     * @return
     */
    Long countNecessaryPlatform(@Param("param")TaskNecessaryPlatformParam param);

    /**
     * 分页查询
     * @param param
     * @return
     */
    List<TaskNecessaryPlatformGoods> queryNecessaryPlatformGoodListByPage(@Param("param")TaskNecessaryPlatformParam param,@Param("start") Integer start, @Param("pageSize") Integer pageSize);


    int batchDel(@Param("ids")List<Long> ids);
}
