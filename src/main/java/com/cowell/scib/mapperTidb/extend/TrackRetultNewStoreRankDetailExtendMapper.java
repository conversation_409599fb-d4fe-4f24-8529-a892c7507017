package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultNewStoreRankDetail;
import com.cowell.scib.entityTidb.TrackRetultNewStoreRankDetailExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackRetultNewStoreRankDetailExtendMapper {
    Long count(@Param("taskId") Long taskId);

    List<TrackRetultNewStoreRankDetail> selectByTaskId(@Param("taskId") Long taskId, @Param("start") Integer start, @Param("pageSize") Integer pageSize);
}
