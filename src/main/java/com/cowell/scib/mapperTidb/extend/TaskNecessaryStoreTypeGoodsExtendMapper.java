package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TaskNecessaryStoreTypeGoods;
import com.cowell.scib.service.dto.NecessaryBusinessIdDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskNecessaryStoreTypeGoodsExtendMapper {

   int batchInsert(@Param("list") List<TaskNecessaryStoreTypeGoods> list);

   /**
    * 查询连锁
    * @param
    * @return
    */
   List<NecessaryBusinessIdDTO> queryTaskNecessaryStoreTypeGoodBusinessIdList(@Param("taskId") Long taskId);

   int batchDel(@Param("ids")List<Long> ids);
}