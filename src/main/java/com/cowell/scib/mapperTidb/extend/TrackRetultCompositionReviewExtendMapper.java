package com.cowell.scib.mapperTidb.extend;

import com.cowell.scib.entityTidb.TrackRetultCompositionReview;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrackRetultCompositionReviewExtendMapper {

    long countTrackRetultCompositionReview(@Param("taskId") Long taskId);

    List<TrackRetultCompositionReview> selectTrackRetultCompositionReviewByPage(@Param("taskId") Long taskId, @Param("page") int page, @Param("perPage") int perPage);

    int batchDel(@Param("ids")List<Long> ids);
}
