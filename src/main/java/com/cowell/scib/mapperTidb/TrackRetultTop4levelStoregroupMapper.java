package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroup;
import com.cowell.scib.entityTidb.TrackRetultTop4levelStoregroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultTop4levelStoregroupMapper {
    long countByExample(TrackRetultTop4levelStoregroupExample example);

    int deleteByExample(TrackRetultTop4levelStoregroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackRetultTop4levelStoregroup record);

    int insertSelective(TrackRetultTop4levelStoregroup record);

    List<TrackRetultTop4levelStoregroup> selectByExample(TrackRetultTop4levelStoregroupExample example);

    TrackRetultTop4levelStoregroup selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultTop4levelStoregroup record, @Param("example") TrackRetultTop4levelStoregroupExample example);

    int updateByExample(@Param("record") TrackRetultTop4levelStoregroup record, @Param("example") TrackRetultTop4levelStoregroupExample example);

    int updateByPrimaryKeySelective(TrackRetultTop4levelStoregroup record);

    int updateByPrimaryKey(TrackRetultTop4levelStoregroup record);
}