package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultCompositionReview;
import com.cowell.scib.entityTidb.TrackRetultCompositionReviewExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultCompositionReviewMapper {
    long countByExample(TrackRetultCompositionReviewExample example);

    int deleteByExample(TrackRetultCompositionReviewExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackRetultCompositionReview record);

    int insertSelective(TrackRetultCompositionReview record);

    List<TrackRetultCompositionReview> selectByExample(TrackRetultCompositionReviewExample example);

    TrackRetultCompositionReview selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultCompositionReview record, @Param("example") TrackRetultCompositionReviewExample example);

    int updateByExample(@Param("record") TrackRetultCompositionReview record, @Param("example") TrackRetultCompositionReviewExample example);

    int updateByPrimaryKeySelective(TrackRetultCompositionReview record);

    int updateByPrimaryKey(TrackRetultCompositionReview record);
}