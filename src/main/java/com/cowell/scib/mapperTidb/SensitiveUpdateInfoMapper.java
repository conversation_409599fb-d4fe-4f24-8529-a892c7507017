package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.SensitiveUpdateInfo;
import com.cowell.scib.entityTidb.SensitiveUpdateInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SensitiveUpdateInfoMapper {
    long countByExample(SensitiveUpdateInfoExample example);

    int deleteByExample(SensitiveUpdateInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SensitiveUpdateInfo record);

    int insertSelective(SensitiveUpdateInfo record);

    List<SensitiveUpdateInfo> selectByExample(SensitiveUpdateInfoExample example);

    SensitiveUpdateInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SensitiveUpdateInfo record, @Param("example") SensitiveUpdateInfoExample example);

    int updateByExample(@Param("record") SensitiveUpdateInfo record, @Param("example") SensitiveUpdateInfoExample example);

    int updateByPrimaryKeySelective(SensitiveUpdateInfo record);

    int updateByPrimaryKey(SensitiveUpdateInfo record);
}