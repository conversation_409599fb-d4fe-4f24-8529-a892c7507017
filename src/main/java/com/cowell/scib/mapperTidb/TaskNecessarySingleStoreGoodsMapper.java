package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TaskNecessarySingleStoreGoods;
import com.cowell.scib.entityTidb.TaskNecessarySingleStoreGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TaskNecessarySingleStoreGoodsMapper {
    long countByExample(TaskNecessarySingleStoreGoodsExample example);

    int deleteByExample(TaskNecessarySingleStoreGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskNecessarySingleStoreGoods record);

    int insertSelective(TaskNecessarySingleStoreGoods record);

    List<TaskNecessarySingleStoreGoods> selectByExample(TaskNecessarySingleStoreGoodsExample example);

    TaskNecessarySingleStoreGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskNecessarySingleStoreGoods record, @Param("example") TaskNecessarySingleStoreGoodsExample example);

    int updateByExample(@Param("record") TaskNecessarySingleStoreGoods record, @Param("example") TaskNecessarySingleStoreGoodsExample example);

    int updateByPrimaryKeySelective(TaskNecessarySingleStoreGoods record);

    int updateByPrimaryKey(TaskNecessarySingleStoreGoods record);
}