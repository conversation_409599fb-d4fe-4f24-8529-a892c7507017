package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultLevelReview;
import com.cowell.scib.entityTidb.TrackRetultLevelReviewExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultLevelReviewMapper {
    long countByExample(TrackRetultLevelReviewExample example);

    int deleteByExample(TrackRetultLevelReviewExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackRetultLevelReview record);

    int insertSelective(TrackRetultLevelReview record);

    List<TrackRetultLevelReview> selectByExample(TrackRetultLevelReviewExample example);

    TrackRetultLevelReview selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultLevelReview record, @Param("example") TrackRetultLevelReviewExample example);

    int updateByExample(@Param("record") TrackRetultLevelReview record, @Param("example") TrackRetultLevelReviewExample example);

    int updateByPrimaryKeySelective(TrackRetultLevelReview record);

    int updateByPrimaryKey(TrackRetultLevelReview record);
}