package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetail;
import com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface JymlStoreSkuSuggestProcessDetailMapper {
    long countByExample(JymlStoreSkuSuggestProcessDetailExample example);

    int deleteByExample(JymlStoreSkuSuggestProcessDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuSuggestProcessDetail record);

    int insertSelective(JymlStoreSkuSuggestProcessDetail record);

    List<JymlStoreSkuSuggestProcessDetail> selectByExample(JymlStoreSkuSuggestProcessDetailExample example);

    JymlStoreSkuSuggestProcessDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuSuggestProcessDetail record, @Param("example") JymlStoreSkuSuggestProcessDetailExample example);

    int updateByExample(@Param("record") JymlStoreSkuSuggestProcessDetail record, @Param("example") JymlStoreSkuSuggestProcessDetailExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuSuggestProcessDetail record);

    int updateByPrimaryKey(JymlStoreSkuSuggestProcessDetail record);

    int batchInsertSelective(List<JymlStoreSkuSuggestProcessDetail> list);
}