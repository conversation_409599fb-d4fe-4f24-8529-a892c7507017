package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TaskNecessaryChooseStoreTypeGoods;
import com.cowell.scib.entityTidb.TaskNecessaryChooseStoreTypeGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TaskNecessaryChooseStoreTypeGoodsMapper {
    long countByExample(TaskNecessaryChooseStoreTypeGoodsExample example);

    int deleteByExample(TaskNecessaryChooseStoreTypeGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskNecessaryChooseStoreTypeGoods record);

    int insertSelective(TaskNecessaryChooseStoreTypeGoods record);

    List<TaskNecessaryChooseStoreTypeGoods> selectByExample(TaskNecessaryChooseStoreTypeGoodsExample example);

    TaskNecessaryChooseStoreTypeGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskNecessaryChooseStoreTypeGoods record, @Param("example") TaskNecessaryChooseStoreTypeGoodsExample example);

    int updateByExample(@Param("record") TaskNecessaryChooseStoreTypeGoods record, @Param("example") TaskNecessaryChooseStoreTypeGoodsExample example);

    int updateByPrimaryKeySelective(TaskNecessaryChooseStoreTypeGoods record);

    int updateByPrimaryKey(TaskNecessaryChooseStoreTypeGoods record);
}