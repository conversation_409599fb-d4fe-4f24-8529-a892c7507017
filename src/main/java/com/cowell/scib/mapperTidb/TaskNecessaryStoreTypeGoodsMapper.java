package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TaskNecessaryStoreTypeGoods;
import com.cowell.scib.entityTidb.TaskNecessaryStoreTypeGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TaskNecessaryStoreTypeGoodsMapper {
    long countByExample(TaskNecessaryStoreTypeGoodsExample example);

    int deleteByExample(TaskNecessaryStoreTypeGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskNecessaryStoreTypeGoods record);

    int insertSelective(TaskNecessaryStoreTypeGoods record);

    List<TaskNecessaryStoreTypeGoods> selectByExample(TaskNecessaryStoreTypeGoodsExample example);

    TaskNecessaryStoreTypeGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskNecessaryStoreTypeGoods record, @Param("example") TaskNecessaryStoreTypeGoodsExample example);

    int updateByExample(@Param("record") TaskNecessaryStoreTypeGoods record, @Param("example") TaskNecessaryStoreTypeGoodsExample example);

    int updateByPrimaryKeySelective(TaskNecessaryStoreTypeGoods record);

    int updateByPrimaryKey(TaskNecessaryStoreTypeGoods record);
}