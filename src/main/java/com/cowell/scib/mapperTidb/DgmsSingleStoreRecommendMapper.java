package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.DgmsSingleStoreRecommend;
import com.cowell.scib.entityTidb.DgmsSingleStoreRecommendExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DgmsSingleStoreRecommendMapper {
    long countByExample(DgmsSingleStoreRecommendExample example);

    int deleteByExample(DgmsSingleStoreRecommendExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DgmsSingleStoreRecommend record);

    int insertSelective(DgmsSingleStoreRecommend record);

    List<DgmsSingleStoreRecommend> selectByExample(DgmsSingleStoreRecommendExample example);

    DgmsSingleStoreRecommend selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DgmsSingleStoreRecommend record, @Param("example") DgmsSingleStoreRecommendExample example);

    int updateByExample(@Param("record") DgmsSingleStoreRecommend record, @Param("example") DgmsSingleStoreRecommendExample example);

    int updateByPrimaryKeySelective(DgmsSingleStoreRecommend record);

    int updateByPrimaryKey(DgmsSingleStoreRecommend record);
}