package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackResultLevelNecessary;
import com.cowell.scib.entityTidb.TrackResultLevelNecessaryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackResultLevelNecessaryMapper {
    long countByExample(TrackResultLevelNecessaryExample example);

    int deleteByExample(TrackResultLevelNecessaryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackResultLevelNecessary record);

    int insertSelective(TrackResultLevelNecessary record);

    List<TrackResultLevelNecessary> selectByExample(TrackResultLevelNecessaryExample example);

    TrackResultLevelNecessary selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackResultLevelNecessary record, @Param("example") TrackResultLevelNecessaryExample example);

    int updateByExample(@Param("record") TrackResultLevelNecessary record, @Param("example") TrackResultLevelNecessaryExample example);

    int updateByPrimaryKeySelective(TrackResultLevelNecessary record);

    int updateByPrimaryKey(TrackResultLevelNecessary record);
}