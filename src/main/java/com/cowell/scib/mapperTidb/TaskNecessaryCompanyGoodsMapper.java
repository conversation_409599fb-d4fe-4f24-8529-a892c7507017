package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TaskNecessaryCompanyGoods;
import com.cowell.scib.entityTidb.TaskNecessaryCompanyGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TaskNecessaryCompanyGoodsMapper {
    long countByExample(TaskNecessaryCompanyGoodsExample example);

    int deleteByExample(TaskNecessaryCompanyGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskNecessaryCompanyGoods record);

    int insertSelective(TaskNecessaryCompanyGoods record);

    List<TaskNecessaryCompanyGoods> selectByExample(TaskNecessaryCompanyGoodsExample example);

    TaskNecessaryCompanyGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskNecessaryCompanyGoods record, @Param("example") TaskNecessaryCompanyGoodsExample example);

    int updateByExample(@Param("record") TaskNecessaryCompanyGoods record, @Param("example") TaskNecessaryCompanyGoodsExample example);

    int updateByPrimaryKeySelective(TaskNecessaryCompanyGoods record);

    int updateByPrimaryKey(TaskNecessaryCompanyGoods record);
}