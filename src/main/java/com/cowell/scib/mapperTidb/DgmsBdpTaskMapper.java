package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.DgmsBdpTask;
import com.cowell.scib.entityTidb.DgmsBdpTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DgmsBdpTaskMapper {
    long countByExample(DgmsBdpTaskExample example);

    int deleteByExample(DgmsBdpTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DgmsBdpTask record);

    int insertSelective(DgmsBdpTask record);

    List<DgmsBdpTask> selectByExample(DgmsBdpTaskExample example);

    DgmsBdpTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DgmsBdpTask record, @Param("example") DgmsBdpTaskExample example);

    int updateByExample(@Param("record") DgmsBdpTask record, @Param("example") DgmsBdpTaskExample example);

    int updateByPrimaryKeySelective(DgmsBdpTask record);

    int updateByPrimaryKey(DgmsBdpTask record);
}