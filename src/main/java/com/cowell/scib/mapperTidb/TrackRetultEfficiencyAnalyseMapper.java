package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyse;
import com.cowell.scib.entityTidb.TrackRetultEfficiencyAnalyseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultEfficiencyAnalyseMapper {
    long countByExample(TrackRetultEfficiencyAnalyseExample example);

    int deleteByExample(TrackRetultEfficiencyAnalyseExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackRetultEfficiencyAnalyse record);

    int insertSelective(TrackRetultEfficiencyAnalyse record);

    List<TrackRetultEfficiencyAnalyse> selectByExample(TrackRetultEfficiencyAnalyseExample example);

    TrackRetultEfficiencyAnalyse selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultEfficiencyAnalyse record, @Param("example") TrackRetultEfficiencyAnalyseExample example);

    int updateByExample(@Param("record") TrackRetultEfficiencyAnalyse record, @Param("example") TrackRetultEfficiencyAnalyseExample example);

    int updateByPrimaryKeySelective(TrackRetultEfficiencyAnalyse record);

    int updateByPrimaryKey(TrackRetultEfficiencyAnalyse record);
}