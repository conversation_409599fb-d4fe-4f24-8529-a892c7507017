package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackResultFile;
import com.cowell.scib.entityTidb.TrackResultFileExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackResultFileMapper {
    long countByExample(TrackResultFileExample example);

    int deleteByExample(TrackResultFileExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackResultFile record);

    int insertSelective(TrackResultFile record);

    List<TrackResultFile> selectByExample(TrackResultFileExample example);

    TrackResultFile selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackResultFile record, @Param("example") TrackResultFileExample example);

    int updateByExample(@Param("record") TrackResultFile record, @Param("example") TrackResultFileExample example);

    int updateByPrimaryKeySelective(TrackResultFile record);

    int updateByPrimaryKey(TrackResultFile record);
}