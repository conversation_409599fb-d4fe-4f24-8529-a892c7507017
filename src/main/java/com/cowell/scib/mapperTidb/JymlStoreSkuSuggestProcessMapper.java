package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcess;
import com.cowell.scib.entityTidb.JymlStoreSkuSuggestProcessExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface JymlStoreSkuSuggestProcessMapper {
    long countByExample(JymlStoreSkuSuggestProcessExample example);

    int deleteByExample(JymlStoreSkuSuggestProcessExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuSuggestProcess record);

    int insertSelective(JymlStoreSkuSuggestProcess record);

    List<JymlStoreSkuSuggestProcess> selectByExample(JymlStoreSkuSuggestProcessExample example);

    JymlStoreSkuSuggestProcess selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuSuggestProcess record, @Param("example") JymlStoreSkuSuggestProcessExample example);

    int updateByExample(@Param("record") JymlStoreSkuSuggestProcess record, @Param("example") JymlStoreSkuSuggestProcessExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuSuggestProcess record);

    int updateByPrimaryKey(JymlStoreSkuSuggestProcess record);
}