package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TaskNecessaryPlatformGoods;
import com.cowell.scib.entityTidb.TaskNecessaryPlatformGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TaskNecessaryPlatformGoodsMapper {
    long countByExample(TaskNecessaryPlatformGoodsExample example);

    int deleteByExample(TaskNecessaryPlatformGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskNecessaryPlatformGoods record);

    int insertSelective(TaskNecessaryPlatformGoods record);

    List<TaskNecessaryPlatformGoods> selectByExample(TaskNecessaryPlatformGoodsExample example);

    TaskNecessaryPlatformGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskNecessaryPlatformGoods record, @Param("example") TaskNecessaryPlatformGoodsExample example);

    int updateByExample(@Param("record") TaskNecessaryPlatformGoods record, @Param("example") TaskNecessaryPlatformGoodsExample example);

    int updateByPrimaryKeySelective(TaskNecessaryPlatformGoods record);

    int updateByPrimaryKey(TaskNecessaryPlatformGoods record);
}