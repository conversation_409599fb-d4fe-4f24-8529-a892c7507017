package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.JymlStoreIncreaseLimitConfigure;
import com.cowell.scib.entityTidb.JymlStoreIncreaseLimitConfigureExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlStoreIncreaseLimitConfigureMapper {
    long countByExample(JymlStoreIncreaseLimitConfigureExample example);

    int deleteByExample(JymlStoreIncreaseLimitConfigureExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreIncreaseLimitConfigure record);

    int insertSelective(JymlStoreIncreaseLimitConfigure record);

    List<JymlStoreIncreaseLimitConfigure> selectByExample(JymlStoreIncreaseLimitConfigureExample example);

    JymlStoreIncreaseLimitConfigure selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreIncreaseLimitConfigure record, @Param("example") JymlStoreIncreaseLimitConfigureExample example);

    int updateByExample(@Param("record") JymlStoreIncreaseLimitConfigure record, @Param("example") JymlStoreIncreaseLimitConfigureExample example);

    int updateByPrimaryKeySelective(JymlStoreIncreaseLimitConfigure record);

    int updateByPrimaryKey(JymlStoreIncreaseLimitConfigure record);
}