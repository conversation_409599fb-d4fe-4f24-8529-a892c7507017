package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetail;
import com.cowell.scib.entityTidb.JymlStoreSkuConfirmDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface JymlStoreSkuConfirmDetailMapper {
    long countByExample(JymlStoreSkuConfirmDetailExample example);

    int deleteByExample(JymlStoreSkuConfirmDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuConfirmDetail record);

    int insertSelective(JymlStoreSkuConfirmDetail record);

    List<JymlStoreSkuConfirmDetail> selectByExample(JymlStoreSkuConfirmDetailExample example);

    JymlStoreSkuConfirmDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuConfirmDetail record, @Param("example") JymlStoreSkuConfirmDetailExample example);

    int updateByExample(@Param("record") JymlStoreSkuConfirmDetail record, @Param("example") JymlStoreSkuConfirmDetailExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuConfirmDetail record);

    int updateByPrimaryKey(JymlStoreSkuConfirmDetail record);
    /**
     * 批量插入商品确认明细记录
     * @param list 待插入的记录列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<JymlStoreSkuConfirmDetail> list);
}