package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord;
import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JymlStoreSkuExemptionRecordMapper {
    long countByExample(JymlStoreSkuExemptionRecordExample example);

    int deleteByExample(JymlStoreSkuExemptionRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuExemptionRecord record);

    int insertSelective(JymlStoreSkuExemptionRecord record);

    List<JymlStoreSkuExemptionRecord> selectByExample(JymlStoreSkuExemptionRecordExample example);

    JymlStoreSkuExemptionRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuExemptionRecord record, @Param("example") JymlStoreSkuExemptionRecordExample example);

    int updateByExample(@Param("record") JymlStoreSkuExemptionRecord record, @Param("example") JymlStoreSkuExemptionRecordExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuExemptionRecord record);

    int updateByPrimaryKey(JymlStoreSkuExemptionRecord record);
}