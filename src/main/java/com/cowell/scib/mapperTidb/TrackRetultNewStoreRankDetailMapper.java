package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultNewStoreRankDetail;
import com.cowell.scib.entityTidb.TrackRetultNewStoreRankDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultNewStoreRankDetailMapper {
    long countByExample(TrackRetultNewStoreRankDetailExample example);

    int deleteByExample(TrackRetultNewStoreRankDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrackRetultNewStoreRankDetail record);

    int insertSelective(TrackRetultNewStoreRankDetail record);

    List<TrackRetultNewStoreRankDetail> selectByExample(TrackRetultNewStoreRankDetailExample example);

    TrackRetultNewStoreRankDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultNewStoreRankDetail record, @Param("example") TrackRetultNewStoreRankDetailExample example);

    int updateByExample(@Param("record") TrackRetultNewStoreRankDetail record, @Param("example") TrackRetultNewStoreRankDetailExample example);

    int updateByPrimaryKeySelective(TrackRetultNewStoreRankDetail record);

    int updateByPrimaryKey(TrackRetultNewStoreRankDetail record);
}