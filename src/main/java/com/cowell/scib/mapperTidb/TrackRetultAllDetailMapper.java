package com.cowell.scib.mapperTidb;

import com.cowell.scib.entityTidb.TrackRetultAllDetail;
import com.cowell.scib.entityTidb.TrackRetultAllDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrackRetultAllDetailMapper {
    long countByExample(TrackRetultAllDetailExample example);

    int deleteByExample(TrackRetultAllDetailExample example);

    //int deleteByPrimaryKey(Long id);

    int insert(TrackRetultAllDetail record);

    int insertSelective(TrackRetultAllDetail record);

    List<TrackRetultAllDetail> selectByExample(TrackRetultAllDetailExample example);

   // TrackRetultAllDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrackRetultAllDetail record, @Param("example") TrackRetultAllDetailExample example);

    int updateByExample(@Param("record") TrackRetultAllDetail record, @Param("example") TrackRetultAllDetailExample example);

    int updateByPrimaryKeySelective(TrackRetultAllDetail record);

    int updateByPrimaryKey(TrackRetultAllDetail record);
}
