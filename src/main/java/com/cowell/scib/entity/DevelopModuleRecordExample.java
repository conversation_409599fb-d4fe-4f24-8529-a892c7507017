package com.cowell.scib.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DevelopModuleRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public DevelopModuleRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andModuleCodeIsNull() {
            addCriterion("module_code is null");
            return (Criteria) this;
        }

        public Criteria andModuleCodeIsNotNull() {
            addCriterion("module_code is not null");
            return (Criteria) this;
        }

        public Criteria andModuleCodeEqualTo(String value) {
            addCriterion("module_code =", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotEqualTo(String value) {
            addCriterion("module_code <>", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeGreaterThan(String value) {
            addCriterion("module_code >", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("module_code >=", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeLessThan(String value) {
            addCriterion("module_code <", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeLessThanOrEqualTo(String value) {
            addCriterion("module_code <=", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeLike(String value) {
            addCriterion("module_code like", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotLike(String value) {
            addCriterion("module_code not like", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeIn(List<String> values) {
            addCriterion("module_code in", values, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotIn(List<String> values) {
            addCriterion("module_code not in", values, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeBetween(String value1, String value2) {
            addCriterion("module_code between", value1, value2, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotBetween(String value1, String value2) {
            addCriterion("module_code not between", value1, value2, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionIsNull() {
            addCriterion("develop_version is null");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionIsNotNull() {
            addCriterion("develop_version is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionEqualTo(String value) {
            addCriterion("develop_version =", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionNotEqualTo(String value) {
            addCriterion("develop_version <>", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionGreaterThan(String value) {
            addCriterion("develop_version >", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionGreaterThanOrEqualTo(String value) {
            addCriterion("develop_version >=", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionLessThan(String value) {
            addCriterion("develop_version <", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionLessThanOrEqualTo(String value) {
            addCriterion("develop_version <=", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionLike(String value) {
            addCriterion("develop_version like", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionNotLike(String value) {
            addCriterion("develop_version not like", value, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionIn(List<String> values) {
            addCriterion("develop_version in", values, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionNotIn(List<String> values) {
            addCriterion("develop_version not in", values, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionBetween(String value1, String value2) {
            addCriterion("develop_version between", value1, value2, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopVersionNotBetween(String value1, String value2) {
            addCriterion("develop_version not between", value1, value2, "developVersion");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeIsNull() {
            addCriterion("develop_type is null");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeIsNotNull() {
            addCriterion("develop_type is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeEqualTo(Byte value) {
            addCriterion("develop_type =", value, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeNotEqualTo(Byte value) {
            addCriterion("develop_type <>", value, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeGreaterThan(Byte value) {
            addCriterion("develop_type >", value, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("develop_type >=", value, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeLessThan(Byte value) {
            addCriterion("develop_type <", value, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeLessThanOrEqualTo(Byte value) {
            addCriterion("develop_type <=", value, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeIn(List<Byte> values) {
            addCriterion("develop_type in", values, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeNotIn(List<Byte> values) {
            addCriterion("develop_type not in", values, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeBetween(Byte value1, Byte value2) {
            addCriterion("develop_type between", value1, value2, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("develop_type not between", value1, value2, "developType");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleIsNull() {
            addCriterion("develop_title is null");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleIsNotNull() {
            addCriterion("develop_title is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleEqualTo(String value) {
            addCriterion("develop_title =", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleNotEqualTo(String value) {
            addCriterion("develop_title <>", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleGreaterThan(String value) {
            addCriterion("develop_title >", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleGreaterThanOrEqualTo(String value) {
            addCriterion("develop_title >=", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleLessThan(String value) {
            addCriterion("develop_title <", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleLessThanOrEqualTo(String value) {
            addCriterion("develop_title <=", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleLike(String value) {
            addCriterion("develop_title like", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleNotLike(String value) {
            addCriterion("develop_title not like", value, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleIn(List<String> values) {
            addCriterion("develop_title in", values, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleNotIn(List<String> values) {
            addCriterion("develop_title not in", values, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleBetween(String value1, String value2) {
            addCriterion("develop_title between", value1, value2, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopTitleNotBetween(String value1, String value2) {
            addCriterion("develop_title not between", value1, value2, "developTitle");
            return (Criteria) this;
        }

        public Criteria andDevelopContentIsNull() {
            addCriterion("develop_content is null");
            return (Criteria) this;
        }

        public Criteria andDevelopContentIsNotNull() {
            addCriterion("develop_content is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopContentEqualTo(String value) {
            addCriterion("develop_content =", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentNotEqualTo(String value) {
            addCriterion("develop_content <>", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentGreaterThan(String value) {
            addCriterion("develop_content >", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentGreaterThanOrEqualTo(String value) {
            addCriterion("develop_content >=", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentLessThan(String value) {
            addCriterion("develop_content <", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentLessThanOrEqualTo(String value) {
            addCriterion("develop_content <=", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentLike(String value) {
            addCriterion("develop_content like", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentNotLike(String value) {
            addCriterion("develop_content not like", value, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentIn(List<String> values) {
            addCriterion("develop_content in", values, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentNotIn(List<String> values) {
            addCriterion("develop_content not in", values, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentBetween(String value1, String value2) {
            addCriterion("develop_content between", value1, value2, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopContentNotBetween(String value1, String value2) {
            addCriterion("develop_content not between", value1, value2, "developContent");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusIsNull() {
            addCriterion("develop_status is null");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusIsNotNull() {
            addCriterion("develop_status is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusEqualTo(Byte value) {
            addCriterion("develop_status =", value, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusNotEqualTo(Byte value) {
            addCriterion("develop_status <>", value, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusGreaterThan(Byte value) {
            addCriterion("develop_status >", value, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("develop_status >=", value, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusLessThan(Byte value) {
            addCriterion("develop_status <", value, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusLessThanOrEqualTo(Byte value) {
            addCriterion("develop_status <=", value, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusIn(List<Byte> values) {
            addCriterion("develop_status in", values, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusNotIn(List<Byte> values) {
            addCriterion("develop_status not in", values, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusBetween(Byte value1, Byte value2) {
            addCriterion("develop_status between", value1, value2, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("develop_status not between", value1, value2, "developStatus");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeIsNull() {
            addCriterion("develop_time is null");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeIsNotNull() {
            addCriterion("develop_time is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeEqualTo(Date value) {
            addCriterion("develop_time =", value, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeNotEqualTo(Date value) {
            addCriterion("develop_time <>", value, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeGreaterThan(Date value) {
            addCriterion("develop_time >", value, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("develop_time >=", value, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeLessThan(Date value) {
            addCriterion("develop_time <", value, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeLessThanOrEqualTo(Date value) {
            addCriterion("develop_time <=", value, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeIn(List<Date> values) {
            addCriterion("develop_time in", values, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeNotIn(List<Date> values) {
            addCriterion("develop_time not in", values, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeBetween(Date value1, Date value2) {
            addCriterion("develop_time between", value1, value2, "developTime");
            return (Criteria) this;
        }

        public Criteria andDevelopTimeNotBetween(Date value1, Date value2) {
            addCriterion("develop_time not between", value1, value2, "developTime");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIsNull() {
            addCriterion("image_urls is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIsNotNull() {
            addCriterion("image_urls is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlsEqualTo(String value) {
            addCriterion("image_urls =", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotEqualTo(String value) {
            addCriterion("image_urls <>", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsGreaterThan(String value) {
            addCriterion("image_urls >", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsGreaterThanOrEqualTo(String value) {
            addCriterion("image_urls >=", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLessThan(String value) {
            addCriterion("image_urls <", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLessThanOrEqualTo(String value) {
            addCriterion("image_urls <=", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLike(String value) {
            addCriterion("image_urls like", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotLike(String value) {
            addCriterion("image_urls not like", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIn(List<String> values) {
            addCriterion("image_urls in", values, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotIn(List<String> values) {
            addCriterion("image_urls not in", values, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsBetween(String value1, String value2) {
            addCriterion("image_urls between", value1, value2, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotBetween(String value1, String value2) {
            addCriterion("image_urls not between", value1, value2, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andReachChannelsIsNull() {
            addCriterion("reach_channels is null");
            return (Criteria) this;
        }

        public Criteria andReachChannelsIsNotNull() {
            addCriterion("reach_channels is not null");
            return (Criteria) this;
        }

        public Criteria andReachChannelsEqualTo(String value) {
            addCriterion("reach_channels =", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsNotEqualTo(String value) {
            addCriterion("reach_channels <>", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsGreaterThan(String value) {
            addCriterion("reach_channels >", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsGreaterThanOrEqualTo(String value) {
            addCriterion("reach_channels >=", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsLessThan(String value) {
            addCriterion("reach_channels <", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsLessThanOrEqualTo(String value) {
            addCriterion("reach_channels <=", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsLike(String value) {
            addCriterion("reach_channels like", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsNotLike(String value) {
            addCriterion("reach_channels not like", value, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsIn(List<String> values) {
            addCriterion("reach_channels in", values, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsNotIn(List<String> values) {
            addCriterion("reach_channels not in", values, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsBetween(String value1, String value2) {
            addCriterion("reach_channels between", value1, value2, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachChannelsNotBetween(String value1, String value2) {
            addCriterion("reach_channels not between", value1, value2, "reachChannels");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsIsNull() {
            addCriterion("reach_groupIds is null");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsIsNotNull() {
            addCriterion("reach_groupIds is not null");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsEqualTo(String value) {
            addCriterion("reach_groupIds =", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsNotEqualTo(String value) {
            addCriterion("reach_groupIds <>", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsGreaterThan(String value) {
            addCriterion("reach_groupIds >", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsGreaterThanOrEqualTo(String value) {
            addCriterion("reach_groupIds >=", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsLessThan(String value) {
            addCriterion("reach_groupIds <", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsLessThanOrEqualTo(String value) {
            addCriterion("reach_groupIds <=", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsLike(String value) {
            addCriterion("reach_groupIds like", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsNotLike(String value) {
            addCriterion("reach_groupIds not like", value, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsIn(List<String> values) {
            addCriterion("reach_groupIds in", values, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsNotIn(List<String> values) {
            addCriterion("reach_groupIds not in", values, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsBetween(String value1, String value2) {
            addCriterion("reach_groupIds between", value1, value2, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andReachGroupidsNotBetween(String value1, String value2) {
            addCriterion("reach_groupIds not between", value1, value2, "reachGroupids");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}