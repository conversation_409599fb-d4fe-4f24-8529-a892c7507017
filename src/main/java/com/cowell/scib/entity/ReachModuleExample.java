package com.cowell.scib.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReachModuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public ReachModuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReachModuleIsNull() {
            addCriterion("reach_module is null");
            return (Criteria) this;
        }

        public Criteria andReachModuleIsNotNull() {
            addCriterion("reach_module is not null");
            return (Criteria) this;
        }

        public Criteria andReachModuleEqualTo(Long value) {
            addCriterion("reach_module =", value, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleNotEqualTo(Long value) {
            addCriterion("reach_module <>", value, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleGreaterThan(Long value) {
            addCriterion("reach_module >", value, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleGreaterThanOrEqualTo(Long value) {
            addCriterion("reach_module >=", value, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleLessThan(Long value) {
            addCriterion("reach_module <", value, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleLessThanOrEqualTo(Long value) {
            addCriterion("reach_module <=", value, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleIn(List<Long> values) {
            addCriterion("reach_module in", values, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleNotIn(List<Long> values) {
            addCriterion("reach_module not in", values, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleBetween(Long value1, Long value2) {
            addCriterion("reach_module between", value1, value2, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachModuleNotBetween(Long value1, Long value2) {
            addCriterion("reach_module not between", value1, value2, "reachModule");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameIsNull() {
            addCriterion("reach_group_name is null");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameIsNotNull() {
            addCriterion("reach_group_name is not null");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameEqualTo(String value) {
            addCriterion("reach_group_name =", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameNotEqualTo(String value) {
            addCriterion("reach_group_name <>", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameGreaterThan(String value) {
            addCriterion("reach_group_name >", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("reach_group_name >=", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameLessThan(String value) {
            addCriterion("reach_group_name <", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameLessThanOrEqualTo(String value) {
            addCriterion("reach_group_name <=", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameLike(String value) {
            addCriterion("reach_group_name like", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameNotLike(String value) {
            addCriterion("reach_group_name not like", value, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameIn(List<String> values) {
            addCriterion("reach_group_name in", values, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameNotIn(List<String> values) {
            addCriterion("reach_group_name not in", values, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameBetween(String value1, String value2) {
            addCriterion("reach_group_name between", value1, value2, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andReachGroupNameNotBetween(String value1, String value2) {
            addCriterion("reach_group_name not between", value1, value2, "reachGroupName");
            return (Criteria) this;
        }

        public Criteria andSelectMethodIsNull() {
            addCriterion("select_method is null");
            return (Criteria) this;
        }

        public Criteria andSelectMethodIsNotNull() {
            addCriterion("select_method is not null");
            return (Criteria) this;
        }

        public Criteria andSelectMethodEqualTo(Integer value) {
            addCriterion("select_method =", value, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodNotEqualTo(Integer value) {
            addCriterion("select_method <>", value, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodGreaterThan(Integer value) {
            addCriterion("select_method >", value, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodGreaterThanOrEqualTo(Integer value) {
            addCriterion("select_method >=", value, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodLessThan(Integer value) {
            addCriterion("select_method <", value, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodLessThanOrEqualTo(Integer value) {
            addCriterion("select_method <=", value, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodIn(List<Integer> values) {
            addCriterion("select_method in", values, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodNotIn(List<Integer> values) {
            addCriterion("select_method not in", values, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodBetween(Integer value1, Integer value2) {
            addCriterion("select_method between", value1, value2, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andSelectMethodNotBetween(Integer value1, Integer value2) {
            addCriterion("select_method not between", value1, value2, "selectMethod");
            return (Criteria) this;
        }

        public Criteria andReachPersonsIsNull() {
            addCriterion("reach_persons is null");
            return (Criteria) this;
        }

        public Criteria andReachPersonsIsNotNull() {
            addCriterion("reach_persons is not null");
            return (Criteria) this;
        }

        public Criteria andReachPersonsEqualTo(String value) {
            addCriterion("reach_persons =", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsNotEqualTo(String value) {
            addCriterion("reach_persons <>", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsGreaterThan(String value) {
            addCriterion("reach_persons >", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsGreaterThanOrEqualTo(String value) {
            addCriterion("reach_persons >=", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsLessThan(String value) {
            addCriterion("reach_persons <", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsLessThanOrEqualTo(String value) {
            addCriterion("reach_persons <=", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsLike(String value) {
            addCriterion("reach_persons like", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsNotLike(String value) {
            addCriterion("reach_persons not like", value, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsIn(List<String> values) {
            addCriterion("reach_persons in", values, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsNotIn(List<String> values) {
            addCriterion("reach_persons not in", values, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsBetween(String value1, String value2) {
            addCriterion("reach_persons between", value1, value2, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonsNotBetween(String value1, String value2) {
            addCriterion("reach_persons not between", value1, value2, "reachPersons");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesIsNull() {
            addCriterion("reach_person_codes is null");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesIsNotNull() {
            addCriterion("reach_person_codes is not null");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesEqualTo(String value) {
            addCriterion("reach_person_codes =", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesNotEqualTo(String value) {
            addCriterion("reach_person_codes <>", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesGreaterThan(String value) {
            addCriterion("reach_person_codes >", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesGreaterThanOrEqualTo(String value) {
            addCriterion("reach_person_codes >=", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesLessThan(String value) {
            addCriterion("reach_person_codes <", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesLessThanOrEqualTo(String value) {
            addCriterion("reach_person_codes <=", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesLike(String value) {
            addCriterion("reach_person_codes like", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesNotLike(String value) {
            addCriterion("reach_person_codes not like", value, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesIn(List<String> values) {
            addCriterion("reach_person_codes in", values, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesNotIn(List<String> values) {
            addCriterion("reach_person_codes not in", values, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesBetween(String value1, String value2) {
            addCriterion("reach_person_codes between", value1, value2, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonCodesNotBetween(String value1, String value2) {
            addCriterion("reach_person_codes not between", value1, value2, "reachPersonCodes");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesIsNull() {
            addCriterion("reach_person_names is null");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesIsNotNull() {
            addCriterion("reach_person_names is not null");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesEqualTo(String value) {
            addCriterion("reach_person_names =", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesNotEqualTo(String value) {
            addCriterion("reach_person_names <>", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesGreaterThan(String value) {
            addCriterion("reach_person_names >", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesGreaterThanOrEqualTo(String value) {
            addCriterion("reach_person_names >=", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesLessThan(String value) {
            addCriterion("reach_person_names <", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesLessThanOrEqualTo(String value) {
            addCriterion("reach_person_names <=", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesLike(String value) {
            addCriterion("reach_person_names like", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesNotLike(String value) {
            addCriterion("reach_person_names not like", value, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesIn(List<String> values) {
            addCriterion("reach_person_names in", values, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesNotIn(List<String> values) {
            addCriterion("reach_person_names not in", values, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesBetween(String value1, String value2) {
            addCriterion("reach_person_names between", value1, value2, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andReachPersonNamesNotBetween(String value1, String value2) {
            addCriterion("reach_person_names not between", value1, value2, "reachPersonNames");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNull() {
            addCriterion("creator_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNotNull() {
            addCriterion("creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdEqualTo(Long value) {
            addCriterion("creator_id =", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotEqualTo(Long value) {
            addCriterion("creator_id <>", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThan(Long value) {
            addCriterion("creator_id >", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("creator_id >=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThan(Long value) {
            addCriterion("creator_id <", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThanOrEqualTo(Long value) {
            addCriterion("creator_id <=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIn(List<Long> values) {
            addCriterion("creator_id in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotIn(List<Long> values) {
            addCriterion("creator_id not in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdBetween(Long value1, Long value2) {
            addCriterion("creator_id between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotBetween(Long value1, Long value2) {
            addCriterion("creator_id not between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("creator_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("creator_name =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("creator_name <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("creator_name >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("creator_name >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("creator_name <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("creator_name <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("creator_name like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("creator_name not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("creator_name in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("creator_name not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("creator_name between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("creator_name not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Long value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Long value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Long value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Long value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Long value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Long> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Long> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Long value1, Long value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Long value1, Long value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}