package com.cowell.scib.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 发版模块记录表
 */
public class DevelopModuleRecord implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 发版记录版本号
     */
    private String developVersion;

    /**
     * 发版类型  1-BUG修复  2-新功能  3-功能优化
     */
    private Byte developType;

    /**
     * 发版记录标题
     */
    private String developTitle;

    /**
     * 发版记录内容
     */
    private String developContent;

    /**
     * 发版状态  0-未发布 1-已发布
     */
    private Byte developStatus;

    /**
     * 发布时间
     */
    private Date developTime;

    /**
     * 指引图片 逗号隔开
     */
    private String imageUrls;

    /**
     * 触达通道 逗号隔开
     */
    private String reachChannels;

    /**
     * 触达组 逗号隔开
     */
    private String reachGroupids;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人名字，对应登录返回的name字段
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人名字，对应登录返回的name字段
     */
    private String updatedName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间（操作时间）
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getDevelopVersion() {
        return developVersion;
    }

    public void setDevelopVersion(String developVersion) {
        this.developVersion = developVersion;
    }

    public Byte getDevelopType() {
        return developType;
    }

    public void setDevelopType(Byte developType) {
        this.developType = developType;
    }

    public String getDevelopTitle() {
        return developTitle;
    }

    public void setDevelopTitle(String developTitle) {
        this.developTitle = developTitle;
    }

    public String getDevelopContent() {
        return developContent;
    }

    public void setDevelopContent(String developContent) {
        this.developContent = developContent;
    }

    public Byte getDevelopStatus() {
        return developStatus;
    }

    public void setDevelopStatus(Byte developStatus) {
        this.developStatus = developStatus;
    }

    public Date getDevelopTime() {
        return developTime;
    }

    public void setDevelopTime(Date developTime) {
        this.developTime = developTime;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public String getReachChannels() {
        return reachChannels;
    }

    public void setReachChannels(String reachChannels) {
        this.reachChannels = reachChannels;
    }

    public String getReachGroupids() {
        return reachGroupids;
    }

    public void setReachGroupids(String reachGroupids) {
        this.reachGroupids = reachGroupids;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DevelopModuleRecord other = (DevelopModuleRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getModuleCode() == null ? other.getModuleCode() == null : this.getModuleCode().equals(other.getModuleCode()))
            && (this.getDevelopVersion() == null ? other.getDevelopVersion() == null : this.getDevelopVersion().equals(other.getDevelopVersion()))
            && (this.getDevelopType() == null ? other.getDevelopType() == null : this.getDevelopType().equals(other.getDevelopType()))
            && (this.getDevelopTitle() == null ? other.getDevelopTitle() == null : this.getDevelopTitle().equals(other.getDevelopTitle()))
            && (this.getDevelopContent() == null ? other.getDevelopContent() == null : this.getDevelopContent().equals(other.getDevelopContent()))
            && (this.getDevelopStatus() == null ? other.getDevelopStatus() == null : this.getDevelopStatus().equals(other.getDevelopStatus()))
            && (this.getDevelopTime() == null ? other.getDevelopTime() == null : this.getDevelopTime().equals(other.getDevelopTime()))
            && (this.getImageUrls() == null ? other.getImageUrls() == null : this.getImageUrls().equals(other.getImageUrls()))
            && (this.getReachChannels() == null ? other.getReachChannels() == null : this.getReachChannels().equals(other.getReachChannels()))
            && (this.getReachGroupids() == null ? other.getReachGroupids() == null : this.getReachGroupids().equals(other.getReachGroupids()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getModuleCode() == null) ? 0 : getModuleCode().hashCode());
        result = prime * result + ((getDevelopVersion() == null) ? 0 : getDevelopVersion().hashCode());
        result = prime * result + ((getDevelopType() == null) ? 0 : getDevelopType().hashCode());
        result = prime * result + ((getDevelopTitle() == null) ? 0 : getDevelopTitle().hashCode());
        result = prime * result + ((getDevelopContent() == null) ? 0 : getDevelopContent().hashCode());
        result = prime * result + ((getDevelopStatus() == null) ? 0 : getDevelopStatus().hashCode());
        result = prime * result + ((getDevelopTime() == null) ? 0 : getDevelopTime().hashCode());
        result = prime * result + ((getImageUrls() == null) ? 0 : getImageUrls().hashCode());
        result = prime * result + ((getReachChannels() == null) ? 0 : getReachChannels().hashCode());
        result = prime * result + ((getReachGroupids() == null) ? 0 : getReachGroupids().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", moduleCode=").append(moduleCode);
        sb.append(", developVersion=").append(developVersion);
        sb.append(", developType=").append(developType);
        sb.append(", developTitle=").append(developTitle);
        sb.append(", developContent=").append(developContent);
        sb.append(", developStatus=").append(developStatus);
        sb.append(", developTime=").append(developTime);
        sb.append(", imageUrls=").append(imageUrls);
        sb.append(", reachChannels=").append(reachChannels);
        sb.append(", reachGroupids=").append(reachGroupids);
        sb.append(", extend=").append(extend);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}