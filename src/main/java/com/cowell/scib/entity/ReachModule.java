package com.cowell.scib.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 触达模块表
 */
public class ReachModule implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 触达模块
     */
    private Long reachModule;

    /**
     * 触达组名称
     */
    private String reachGroupName;

    /**
     * 选择方式 1：按角色、2：指定人员
     */
    private Integer selectMethod;

    /**
     * 触达人。select_method等于1时存角色id。等于2时存人员id 逗号隔开
     */
    private String reachPersons;

    /**
     * 触达人编码。select_method等于1时存角色编码  逗号隔开
     */
    private String reachPersonCodes;

    /**
     * 触达人名称。select_method等于1时存角色名称。等于2时存人员名称 逗号隔开
     */
    private String reachPersonNames;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 启用状态  -1-停用 0-启用
     */
    private Integer status;

    /**
     * 扩展字段
     */
    private String extend;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReachModule() {
        return reachModule;
    }

    public void setReachModule(Long reachModule) {
        this.reachModule = reachModule;
    }

    public String getReachGroupName() {
        return reachGroupName;
    }

    public void setReachGroupName(String reachGroupName) {
        this.reachGroupName = reachGroupName;
    }

    public Integer getSelectMethod() {
        return selectMethod;
    }

    public void setSelectMethod(Integer selectMethod) {
        this.selectMethod = selectMethod;
    }

    public String getReachPersons() {
        return reachPersons;
    }

    public void setReachPersons(String reachPersons) {
        this.reachPersons = reachPersons;
    }

    public String getReachPersonCodes() {
        return reachPersonCodes;
    }

    public void setReachPersonCodes(String reachPersonCodes) {
        this.reachPersonCodes = reachPersonCodes;
    }

    public String getReachPersonNames() {
        return reachPersonNames;
    }

    public void setReachPersonNames(String reachPersonNames) {
        this.reachPersonNames = reachPersonNames;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ReachModule other = (ReachModule) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getReachModule() == null ? other.getReachModule() == null : this.getReachModule().equals(other.getReachModule()))
            && (this.getReachGroupName() == null ? other.getReachGroupName() == null : this.getReachGroupName().equals(other.getReachGroupName()))
            && (this.getSelectMethod() == null ? other.getSelectMethod() == null : this.getSelectMethod().equals(other.getSelectMethod()))
            && (this.getReachPersons() == null ? other.getReachPersons() == null : this.getReachPersons().equals(other.getReachPersons()))
            && (this.getReachPersonCodes() == null ? other.getReachPersonCodes() == null : this.getReachPersonCodes().equals(other.getReachPersonCodes()))
            && (this.getReachPersonNames() == null ? other.getReachPersonNames() == null : this.getReachPersonNames().equals(other.getReachPersonNames()))
            && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
            && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateId() == null ? other.getUpdateId() == null : this.getUpdateId().equals(other.getUpdateId()))
            && (this.getUpdateName() == null ? other.getUpdateName() == null : this.getUpdateName().equals(other.getUpdateName()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getReachModule() == null) ? 0 : getReachModule().hashCode());
        result = prime * result + ((getReachGroupName() == null) ? 0 : getReachGroupName().hashCode());
        result = prime * result + ((getSelectMethod() == null) ? 0 : getSelectMethod().hashCode());
        result = prime * result + ((getReachPersons() == null) ? 0 : getReachPersons().hashCode());
        result = prime * result + ((getReachPersonCodes() == null) ? 0 : getReachPersonCodes().hashCode());
        result = prime * result + ((getReachPersonNames() == null) ? 0 : getReachPersonNames().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateId() == null) ? 0 : getUpdateId().hashCode());
        result = prime * result + ((getUpdateName() == null) ? 0 : getUpdateName().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", reachModule=").append(reachModule);
        sb.append(", reachGroupName=").append(reachGroupName);
        sb.append(", selectMethod=").append(selectMethod);
        sb.append(", reachPersons=").append(reachPersons);
        sb.append(", reachPersonCodes=").append(reachPersonCodes);
        sb.append(", reachPersonNames=").append(reachPersonNames);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateId=").append(updateId);
        sb.append(", updateName=").append(updateName);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", status=").append(status);
        sb.append(", extend=").append(extend);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}