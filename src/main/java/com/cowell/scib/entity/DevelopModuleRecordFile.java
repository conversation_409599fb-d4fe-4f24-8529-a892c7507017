package com.cowell.scib.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * develop_module_record_file
 * <AUTHOR>
public class DevelopModuleRecordFile implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 发版记录版本号
     */
    private String developVersion;

    /**
     * 发版记录模块+版本号
     */
    private String codeVersionKey;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间（操作时间）
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getDevelopVersion() {
        return developVersion;
    }

    public void setDevelopVersion(String developVersion) {
        this.developVersion = developVersion;
    }

    public String getCodeVersionKey() {
        return codeVersionKey;
    }

    public void setCodeVersionKey(String codeVersionKey) {
        this.codeVersionKey = codeVersionKey;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DevelopModuleRecordFile other = (DevelopModuleRecordFile) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getModuleCode() == null ? other.getModuleCode() == null : this.getModuleCode().equals(other.getModuleCode()))
            && (this.getDevelopVersion() == null ? other.getDevelopVersion() == null : this.getDevelopVersion().equals(other.getDevelopVersion()))
            && (this.getCodeVersionKey() == null ? other.getCodeVersionKey() == null : this.getCodeVersionKey().equals(other.getCodeVersionKey()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getFileUrl() == null ? other.getFileUrl() == null : this.getFileUrl().equals(other.getFileUrl()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getModuleCode() == null) ? 0 : getModuleCode().hashCode());
        result = prime * result + ((getDevelopVersion() == null) ? 0 : getDevelopVersion().hashCode());
        result = prime * result + ((getCodeVersionKey() == null) ? 0 : getCodeVersionKey().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getFileUrl() == null) ? 0 : getFileUrl().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", moduleCode=").append(moduleCode);
        sb.append(", developVersion=").append(developVersion);
        sb.append(", codeVersionKey=").append(codeVersionKey);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}