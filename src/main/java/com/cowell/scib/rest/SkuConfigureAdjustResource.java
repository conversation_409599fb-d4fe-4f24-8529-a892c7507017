package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.entityTidb.JymlSkuMaxLimitConfigure;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.SkuConfigureAdjustService;
import com.cowell.scib.service.dto.MdmStoreExDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.skuadjust.*;
import com.cowell.scib.service.vo.PageResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/sku/configure/adjust")
@Api(tags = "门店SKU数配置调整", description = "门店SKU数配置调整")
public class SkuConfigureAdjustResource {

    private final Logger logger = LoggerFactory.getLogger(ManageContentResource.class);

    @Resource
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Resource
    private SkuConfigureAdjustService skuConfigureAdjustService;

    @ApiOperation("获取全集团四级分类树")
    @Timed
    @GetMapping("/getSkuConfigureTree")
    public ResponseEntity<List<JymlSkuMaxLimitConfigureTreeDTO>> getSkuConfigureTree() {
        try{
            return ResponseEntity.ok(skuConfigureAdjustService.getSkuConfigureTree());
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
    @ApiOperation("根据门店选择商品管控类别树")
    @Timed
    @GetMapping("/getSkuConfigureTreeByStore")
    public ResponseEntity<List<JymlSkuMaxLimitConfigureTreeDTO>> getSkuConfigureTreeByStore(@RequestParam("storeOrgId") Long storeOrgId) {
        logger.info("SkuConfigureAdjustResource.getSkuConfigureTreeByStore storeOrgId={}",storeOrgId);
        Assert.notNull(storeOrgId, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        try{
            return ResponseEntity.ok(skuConfigureAdjustService.getSkuConfigureTreeByStore(storeOrgId));
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("根据门店+管控类别获取老管控标准")
    @Timed
    @PostMapping("/getOldConfigure")
    public ResponseEntity<List<JymlSkuMaxLimitConfigureTreeDTO>> getOldConfigure(@RequestBody AdjustQueryParam param) {
        logger.info("SkuConfigureAdjustResource.getOldConfigure param={}",JSON.toJSONString(param));
        try{
            return ResponseEntity.ok(skuConfigureAdjustService.getOldConfigure(param));
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("根据管控类别选择新管控标准")
    @Timed
    @GetMapping("/getSkuConfigureByConfigureId")
    public ResponseEntity<List<JymlSkuMaxLimitConfigure>> getSkuConfigureById(@RequestParam("id") Long id, @RequestParam("level") Integer level, @RequestParam("storeOrgId") Long storeOrgId){
        Assert.notNull(id, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        Assert.notNull(level, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        Assert.notNull(storeOrgId, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        try{
            return ResponseEntity.ok(skuConfigureAdjustService.getSkuConfigureById(id, level, storeOrgId));
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("获取调整明细")
    @Timed
    @GetMapping("/getAdjustDetails")
    public ResponseEntity<JymlStoreSkuLimitAdjustDTO> getAdjustDetails(@RequestParam("adjustId") Long adjustId){
        Assert.notNull(adjustId, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        try{
            return ResponseEntity.ok(skuConfigureAdjustService.getAdjustDetails(adjustId));
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("创建调整单")
    @Timed
    @PostMapping("/createAdjust")
    public ResponseEntity<String> createAdjust(HttpServletRequest request, @RequestBody SkuAdjustAddParam param){
        try{
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},param={}",userDTO, "SkuConfigureAdjustResource.createAdjust", JSON.toJSONString(param));
            skuConfigureAdjustService.createAdjust(param, userDTO);
            return ResponseEntity.ok("创建成功");
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("提交审核调整单")
    @Timed
    @PostMapping("/sumbitApprove")
    public ResponseEntity<String> sumbitApprove(HttpServletRequest request, @RequestBody List<Long> ids){
        try{
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},param={}",userDTO, "SkuConfigureAdjustResource.sumbitApprove", JSON.toJSONString(ids));
            skuConfigureAdjustService.sumbitApprove(ids, userDTO);
            return ResponseEntity.ok("审核成功");
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("获取调整记录")
    @Timed
    @PostMapping("/getAdjustList")
    public PageResponse<List<JymlStoreSkuLimitAdjustDTO>> getAdjustList(HttpServletRequest request, @RequestBody SkuAdjustQueryParam param){
        try{
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},param={}",userDTO, "SkuConfigureAdjustResource.getAdjustList", JSON.toJSONString(param));
            return skuConfigureAdjustService.getAdjustList(param, userDTO);
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("获取生效记录")
    @Timed
    @PostMapping("/getAdjustEffectList")
    public PageResponse<List<JymlStoreSkuLimitAdjustEffectDTO>> getAdjustEffectList(HttpServletRequest request, @RequestBody SkuAdjustQueryParam param){
        try{
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},param={}",userDTO, "SkuConfigureAdjustResource.getAdjustEffectList", JSON.toJSONString(param));
            return skuConfigureAdjustService.getAdjustEffectList(param, userDTO);
        }catch (Exception e){
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @Timed
    @ApiOperation(value = "导出SKU数配置上限管理/生效记录")
    @PostMapping(value = "/exportAdjustList")
    public ResponseEntity exportAdjustList(HttpServletRequest request, @RequestBody SkuAdjustQueryParam param){
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},param={}", userDTO, "SkuConfigureAdjustResource.exportAdjustList", JSON.toJSONString(param));
            if (null == param.getExportType()) {
                throw new BusinessErrorException("请选择导出类别");
            }
            switch (param.getExportType()) {
                case 1:
                    skuConfigureAdjustService.exportAdjustList(userDTO, param);
                    break;
                case 2:
                    skuConfigureAdjustService.exportAdjustEffectList(userDTO, param);
                    break;
                default:throw new BusinessErrorException("未定义的导出类别");
            }
            return ResponseEntity.ok(CommonResult.ok("正在导出,请稍后去下载中心查看"));
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
    @ApiOperation("导入调整记录")
    @Timed
    @PostMapping("/importAdjust")
    public ResponseEntity<ImportResult> importAdjust(MultipartFile file, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = skuConfigureAdjustService.importAdjust(file, userDTO);
        return ResponseEntity.ok().body(importResult);
    }


    @ApiOperation("获取门店详情")
    @Timed
    @GetMapping("/getStoreInfoByStoreOrdIds")
    public ResponseEntity<MdmStoreExDTO> getStoreInfoByStoreOrdIds(HttpServletRequest request, @RequestParam(value = "storeOrgId") Long storeOrgId) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok().body(skuConfigureAdjustService.getStoreInfoByStoreOrdIds(storeOrgId));
    }

}
