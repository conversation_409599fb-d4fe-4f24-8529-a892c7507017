package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.entityDgms.NecessaryLevelConfig;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.ImportDataFactory;
import com.cowell.scib.service.NecessaryLevelConfigService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelRoleConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryRoleConfigUpdateParam;
import com.cowell.scib.service.dto.config.NecessaryUpdateParam;
import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "必备层级配置接口", description = "必备层级配置接口")
public class NecessaryLevelConfigResource {


    private final Logger logger = LoggerFactory.getLogger(NecessaryLevelConfigResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Resource
    private NecessaryLevelConfigService necessaryLevelConfigService;
    @Timed
    @ApiOperation("查询必备层级配置")
    @PostMapping("/necessary/config/level/list")
    public ResponseEntity<CommonResponse<NecessaryLevelConfigDTO>> list(HttpServletRequest request, @RequestBody AmisPageParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(necessaryLevelConfigService.list(param));
    }
    @Timed
    @ApiOperation("编辑必备层级配置")
    @PostMapping("/necessary/config/level/edit")
    public ResponseEntity<CommonResult> edit(HttpServletRequest request, @RequestBody NecessaryUpdateParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        necessaryLevelConfigService.edit(param, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }
    @Timed
    @ApiOperation("查询必备层级角色配置")
    @PostMapping("/necessary/config/level/role/list")
    public ResponseEntity<CommonResponse<NecessaryLevelRoleConfigDTO>> roleList(HttpServletRequest request, @RequestBody NecessaryRoleConfigUpdateParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(necessaryLevelConfigService.roleList(param.getPlatformOrgId()));
    }
    @Timed
    @ApiOperation("编辑必备层级角色配置")
    @PostMapping("/necessary/config/level/role/edit")
    public ResponseEntity<CommonResult> roleEdit(HttpServletRequest request, @RequestBody NecessaryRoleConfigUpdateParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        necessaryLevelConfigService.roleEdit(param, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }
    @Timed
    @ApiOperation("根据角色获取必备标签配置")
    @PostMapping("/necessary/config/level/role/getNecessaryTagByRole")
    public ResponseEntity<CommonResult<NecessaryLevelConfigDTO>> getNecessaryTagByRole(HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        necessaryLevelConfigService.getNecessaryTagByRole(userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }
}
