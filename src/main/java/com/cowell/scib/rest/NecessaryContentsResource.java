package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codahale.metrics.annotation.Timed;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.MdmTaskDetail;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.MdmGoodsStatusUpdateService;
import com.cowell.scib.service.NecessaryContentsService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.necessaryContents.*;
import com.cowell.scib.service.dto.rule.HotGoodsResponseDTO;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.SelectorResult;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 必备目录管理
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "必备目录接口", description = "必备目录接口")
public class NecessaryContentsResource {

    private final Logger logger = LoggerFactory.getLogger(NecessaryContentsResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private NecessaryContentsService necessaryContentsService;

    @Autowired
    private MdmGoodsStatusUpdateService mdmGoodsStatusUpdateService;


    @Autowired
    @Qualifier("mdmGoodStatusUpdateSixNecessaryExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    /**
     * 分页获取必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "分页获取必备目录")
    @PostMapping(value = "/necessary/getContents")
    public ResponseEntity<CommonResult<PageResult<NecessaryCommonDTO>>> getContents(HttpServletRequest request, @RequestBody NecessaryQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getContents", JSON.toJSONString(param));
        PageResult<NecessaryCommonDTO> pageResult = necessaryContentsService.getContents(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok(pageResult));
    }

    /**
     * 定位必备层级
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "定位必备层级")
    @PostMapping(value = "/necessary/getNecessaryDirections")
    public ResponseEntity<CommonResult<NecessaryDirectionsDTO>> getNecessaryDirections(HttpServletRequest request, @RequestBody NecessaryDirectionsParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getNecessaryDirections", JSON.toJSONString(param));
        NecessaryDirectionsDTO necessaryDirectionsDTO = necessaryContentsService.getDirections(param, userDTO);
        logger.info("用户={},操作={},param={},return={}", userDTO, "NecessaryContentsResource.getNecessaryDirections", JSON.toJSONString(param), necessaryDirectionsDTO);
        return ResponseEntity.ok(CommonResult.ok(necessaryDirectionsDTO));
    }

    /**
     * 添加必备目录商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "添加必备目录商品")
    @PostMapping(value = "/necessary/addNecessaryGoods")
    public ResponseEntity<CommonResult<NecessaryAddMsg>> addNecessaryGoods(HttpServletRequest request, @RequestBody NecessaryAddParam param) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        param.setCheckGoodsQty(true);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.addNecessaryGoods", JSON.toJSONString(param));
        NecessaryAddMsg addMsg = new NecessaryAddMsg();
        String msg = necessaryContentsService.addNecessaryGoods(userDTO, param);
        addMsg.setMessage(msg);
        if (StringUtils.isNotBlank(msg) && msg.endsWith("是否排除掉重复商品，继续添加？")) {
            addMsg.setExistsIgnore(param.getExistsIgnore());
        } else {
            addMsg.setExistsIgnore(true);
        }
        return ResponseEntity.ok(CommonResult.ok(msg, addMsg));
    }

    /**
     * 删除必备目录商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "删除必备目录商品")
    @PostMapping("/necessary/delNecessaryGoods")
    public ResponseEntity delNecessaryGoods(HttpServletRequest request, @RequestBody NecessaryDelParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.delNecessaryGoods", JSON.toJSONString(param));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.delNecessaryGoods(userDTO, param)));
    }

    /**
     * 启用停用集团必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "启用停用集团必备目录")
    @PostMapping("/necessary/modifyNecessaryGroup")
    public ResponseEntity modifyNecessaryGroup(HttpServletRequest request, @RequestBody NecessaryAddParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.modifyNecessaryGroup", JSON.toJSONString(param));
        necessaryContentsService.modifyNecessaryGroup(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 导出六级必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "导出六级必备目录")
    @PostMapping("/necessary/exportNecessaryContent")
    public ResponseEntity exportNecessaryContent(HttpServletRequest request, @RequestBody NecessaryQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.exportNecessaryContent", JSON.toJSONString(param));
        necessaryContentsService.exportNecessaryContent(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok("正在导出,请稍后去下载中心查看"));
    }

    /**
     * 导出一店一目信息
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "导出一店一目信息")
    @PostMapping("/necessary/exportStoreGoodsInfo")
    public ResponseEntity exportStoreGoodsInfo(HttpServletRequest request, @RequestBody NecessaryQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.exportStoreGoodsInfo", JSON.toJSONString(param));
        necessaryContentsService.exportStoreGoodsInfo(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok("正在导出,请稍后去下载中心查看"));
    }


    /**
     * 复制门店必备目录-获取门店信息
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "复制门店必备目录-获取门店信息")
    @GetMapping("/necessary/getCopyStoreInfo")
    public ResponseEntity<CommonResult<MdmStoreExDTO>> getCopyStoreInfo(HttpServletRequest request, @RequestParam("storeId") Long storeId){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getCopyStoreInfo", storeId);
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getCopyStoreInfo(storeId)));
    }

    /**
     * 复制门店必备目录-获取原门店必备目录信息
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "复制门店必备目录-获取原门店必备目录信息")
    @PostMapping("/necessary/getSourceStoreInfos")
    public ResponseEntity<CommonResult<PageResult<StoreGoodsInfoDTO>>> getSourceStoreInfos(HttpServletRequest request, @RequestBody StoreGoodsQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getSourceStoreInfos", JSON.toJSONString(param));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getSourceStoreInfos(param, userDTO)));
    }

    /**
     * 复制门店必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "复制门店必备目录-复制")
    @PostMapping("/necessary/copyStoreGoods")
    public ResponseEntity copyStoreGoods(HttpServletRequest request, @RequestBody NecessaryCopyParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},sourceStoreId={},targetStoreId={}", userDTO, "NecessaryContentsResource.copyStoreGoods", JSON.toJSONString(param));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.copyStoreGoods(userDTO, param)));
    }

    /**
     * 复制门店必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "复制门店必备目录-修改复制的门店状态")
    @GetMapping("/necessary/updateCopyStoreGoodsStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(name="sourceStoreId",value="原门店id",required = true),
            @ApiImplicitParam(name="goodsNo",value="商品编码",required = true),
            @ApiImplicitParam(name="effectStatus",value="是否有效(0 否 1 是)",required = true),
    })
    public ResponseEntity updateCopyStoreGoodsStatus(HttpServletRequest request, @RequestParam(value = "sourceStoreId") Long sourceStoreId, @RequestParam(value = "goodsNo") String goodsNo, @RequestParam(value = "effectStatus") Byte effectStatus){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},sourceStoreId={},targetStoreId={}", userDTO, "NecessaryContentsResource.updateCopyStoreGoodsStatus", sourceStoreId, goodsNo, effectStatus);
        necessaryContentsService.updateCopyStoreGoodsStatus(sourceStoreId, goodsNo, effectStatus, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 根据企业获取城市列表
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "根据企业获取城市列表")
    @GetMapping("/necessary/getCityByCompany")
    public ResponseEntity<CommonResult<NecessaryCityDTO>> getCityByCompany(HttpServletRequest request, @RequestParam(value = "companyOrgId") Long companyOrgId){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getCityByCompany", companyOrgId);
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getCityByCompany(userDTO, companyOrgId)));
    }

    /**
     * MDM任务管理-获取列表页
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "MDM任务管理-获取列表页")
    @PostMapping("/necessary/getMdmTaskList")
    public ResponseEntity<CommonResult<PageResult<MdmTaskDTO>>> getMdmTaskList(HttpServletRequest request,
                                                                                    @RequestBody MdmTaskQueryParam queryParam){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getMdmTaskList", JSON.toJSONString(queryParam));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getMdmTaskList(queryParam, userDTO)));
    }

    @Timed
    @ApiOperation(value = "MDM任务管理-获取列表页-noauth")
    @PostMapping({"/noauth/necessary/getMdmTaskList"})
    public ResponseEntity<CommonResult<PageResult<MdmTaskDTO>>> getMdmTaskListNoauth(HttpServletRequest request,
                                                                                    @RequestBody MdmTaskQueryParam queryParam){
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getMdmTaskList(queryParam, null)));
    }

    /**
     * MDM任务管理-获取列表页
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "MDM任务管理-获取失败明细")
    @PostMapping("/necessary/getMdmTaskErrorList")
    public ResponseEntity<CommonResult<PageResult<MdmTaskDetailDTO>>> getMdmTaskErrorList(HttpServletRequest request,
                                                                                  @RequestBody MdmTaskErrorQueryParam queryParam){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getMdmTaskErrorList", JSON.toJSONString(queryParam));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getMdmTaskErrorList(queryParam, userDTO)));
    }

    @Timed
    @ApiOperation(value = "MDM任务管理-获取失败明细-noauth")
    @PostMapping("/noauth/necessary/getMdmTaskErrorList")
    public ResponseEntity<CommonResult<PageResult<MdmTaskDetailDTO>>> getMdmTaskErrorListNoauth(HttpServletRequest request,
                                                                                  @RequestBody MdmTaskErrorQueryParam queryParam){
        queryParam.setAllAble(true);
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getMdmTaskErrorList(queryParam, null)));
    }

    /**
     * MDM任务管理-获取列表页
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "MDM任务管理-再次更新")
    @GetMapping("/necessary/refushUpdate")
    public ResponseEntity refushUpdate(HttpServletRequest request, @RequestParam(value = "taskId") Long taskId,@RequestParam(value = "taskSource",required = false) Integer taskSource){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsResource.getMdmTaskErrorList", taskId);
        String s = necessaryContentsService.refushUpdate(taskId, taskSource, userDTO);
        return ResponseEntity.ok(CommonResult.ok(s));
    }

    @ApiOperation("搜索条件列表")
    @Timed
    @GetMapping("/necessary/listSearch")
    @ApiImplicitParams({
            @ApiImplicitParam(name="searchType",value="// 1 必备标识 2 mdm任务状态 3 mdm任务来源",required = true),
    })
    public ResponseEntity<CommonResult<SelectorResult>> listSearch(@RequestParam(value = "searchType") Integer searchType, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        SelectorResult selectorResult = necessaryContentsService.listSearch(searchType);
        return ResponseEntity.ok(CommonResult.ok(selectorResult));
    }

    @ApiOperation("单店必备-通过店型查门店")
    @Timed
    @GetMapping("/necessary/getStoreByStoreType")
    @ApiImplicitParams({
            @ApiImplicitParam(name="companyOrgId",value="企业orgId",required = true),
            @ApiImplicitParam(name="storeType",value="店型",required = true),
    })
    public ResponseEntity<List<OrgDTO>> getStoreByStoreType(@RequestParam(value = "companyOrgId") Long companyOrgId, @RequestParam(value = "storeType") String storeType, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(necessaryContentsService.getStoreByStoreType(companyOrgId, storeType));
    }

    @ApiOperation("手工推送任务至mdm")
    @Timed
    @GetMapping("/necessary/manualPushMdm")
    @ApiImplicitParams({
            @ApiImplicitParam(name="taskId",value="// 任务ID",required = true),
    })
    public ResponseEntity<CommonResult<SelectorResult>> manualPushMdm(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        necessaryContentsService.manualPushMdm(taskId);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 接收mdm回传的一店一目数据
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("接收mdm回传的一店一目数据")
    @PostMapping("/noauth/necessary/receiveMdmStoreGoodsInfo")
    public ResponseEntity receiveMdmStoreGoodsInfo(HttpServletRequest request, @RequestBody JSONObject param){
        logger.info("用户={},操作={}", "NecessaryContentsResource.receiveMdmStoreGoodsInfo");
        necessaryContentsService.receiveMdmStoreGoodsInfo(param);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 初始化缓存
     * @return
     */
    @Timed
    @PostMapping("/necessary/initCache")
    public ResponseEntity initCache(){
        logger.info("用户={},操作={},param={}", "NecessaryContentsResource.initCache");
        necessaryContentsService.permOrgCacheInit();
        return null;
    }

    @Timed
    @ApiOperation(value = "根据门店Id获取门店连锁平台简单信息", notes = "根据门店Id获取门店连锁平台简单信息")
    @PostMapping("/intranet/necessary/getPlatformBusinessInfo")
    public ResponseEntity<List<OrgInfoBaseCache>> getPlatformBusinessInfo( @RequestBody NecessaryPlatformBusinessQueryParam necessaryPlatformBusinessQueryParam){
        logger.info("操作:{},param:{}", "NecessaryContentsResource.getPlatformBusinessInfo",necessaryPlatformBusinessQueryParam.getStoreId());
        List<OrgInfoBaseCache> platformBusinessInfo = necessaryContentsService.getPlatformBusinessInfo(necessaryPlatformBusinessQueryParam.getStoreId());
        return new ResponseEntity<>(platformBusinessInfo,HttpStatus.OK);
    }

    @Timed
    @ApiOperation(value = "初始化必备目录", notes = "初始化必备目录")
    @PostMapping("/initNecessary")
    public ResponseEntity<List<InitImportNecessaryDTO>> initNecessary(HttpServletRequest request,
                                                             @RequestParam("necessaryTag") Byte necessaryTag,
                                                             @RequestParam("file") MultipartFile file) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},necessaryTag={}", userDTO, "NecessaryContentsResource.initNecessary", necessaryTag);
        List<InitImportNecessaryDTO> errors = necessaryContentsService.initNecessary(userDTO, necessaryTag, file);
        return new ResponseEntity<>(errors, HttpStatus.OK);
    }

    /**
     * 接收mdm下发的一店一目数据(初始化用)
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("接收mdm回传的一店一目数据(初始化用)")
    @PostMapping("/noauth/necessary/receiveInitMdmPush")
    public ResponseEntity receiveInitMdmPush(HttpServletRequest request, @RequestBody JSONObject param){
        logger.info("用户={},操作={}", "NecessaryContentsResource.receiveInitMdmPush");
        necessaryContentsService.receiveInitMdmPush(param);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 根据门店编码获取门店带店型缓存(为空则获取所有门店)
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("根据门店编码获取门店带店型缓存(为空则获取所有门店)")
    @PostMapping("/necessary/getStoreTypeCache")
    public ResponseEntity<Object> getStoreTypeCache(HttpServletRequest request, @RequestBody(required = false) List<String> storeCodes){
        if (CollectionUtils.isEmpty(storeCodes)) {
            return new ResponseEntity<>(CacheVar.storeExMap, HttpStatus.OK);
        }
        return new ResponseEntity<>(CacheVar.storeExMap.get(storeCodes.get(0)), HttpStatus.OK);
    }

    /**
     * 根据id获取对应的组织缓存
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("根据id获取对应的组织缓存")
    @GetMapping("/necessary/getOrgCache")
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgCache(HttpServletRequest request, @RequestParam(value = "orgIds") List<Long> orgIds, @RequestParam(value = "orgType") Integer orgType){
        if (OrgTypeEnum.PLATFORM.getCode().equals(orgType)) {
            return new ResponseEntity<>(orgIds.stream().map(v -> CacheVar.getPlatformByOrgId(v).orElse(null)).filter(Objects::nonNull).collect(Collectors.toList()), HttpStatus.OK);
        }
        if (OrgTypeEnum.BUSINESS.getCode().equals(orgType)) {
            return new ResponseEntity<>(CacheVar.getBusinessOrgByOrgIdList(orgIds).orElse(new ArrayList<>()), HttpStatus.OK);
        }
        if (OrgTypeEnum.PLATFORM.getCode().equals(orgType)) {
            return new ResponseEntity<>(CacheVar.getStoreByStoreOrgIdList(orgIds).orElse(new ArrayList<>()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
    }

    /**
     * 根据门店 商品 必备层级刷新一店一目数据
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("根据门店 商品 必备层级刷新一店一目数据")
    @PostMapping("/necessary/refreshStoreGoods")
    public ResponseEntity refreshStoreGoods(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam(value = "necessaryTag") Integer necessaryTag, @RequestParam(value = "necessaryTagName") String necessaryTagName, @RequestParam(value = "sourceNecessaryTag", required = false) Integer sourceNecessaryTag, @RequestParam(value = "pushMdmAble") Boolean pushMdmAble) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            userDTO.setUserId(Constants.SYS_USER_ID);
            userDTO.setName(Constants.SYS_USER_NAME);
            necessaryContentsService.importRefreshStoreGoods(file, necessaryTag, necessaryTagName, sourceNecessaryTag, pushMdmAble, userDTO);
            return new ResponseEntity<>("", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    /**
     * 接收mdm下发的商品数据
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("接收mdm下发的商品数据")
    @PostMapping("/noauth/mdmGoods/receiveMdmGoodsUpdate")
    public ResponseEntity receiveMdmGoodsUpdate(HttpServletRequest request, @RequestBody JSONObject param){
        logger.info("mdm下发的商品数据为：{}",param);
//        asyncTaskExecutor.submit(() -> {
//            mdmGoodsStatusUpdateService.receiveMdmGoodsUpdate(param);
//        });
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 接收mdm下发的门店数据，更新mdm最小陈列量
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("mdm下发的门店数据为")
    @PostMapping("/noauth/mdmStore/receiveMdmStoreUpdate")
    public ResponseEntity receiveMdmStoreUpdate(HttpServletRequest request, @RequestBody JSONObject param){
        logger.info("mdm下发的门店数据为：{}",param);
        asyncTaskExecutor.submit(() -> {
            mdmGoodsStatusUpdateService.receiveMdmStoreUpdate(param);
        });
        return ResponseEntity.ok(CommonResult.ok());
    }
    /**
     * 新增-导入商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("新增-导入商品")
    @PostMapping("/necessary/importGoods")
    public ResponseEntity importGoods(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("necessaryTag") Byte necessaryTag, @RequestParam(value = "businessId", required = false) Long businessId){
        logger.info("用户={},操作={}", "NecessaryContentsResource.importGoods");
        List<String> goodNos = necessaryContentsService.importGoods(businessId, necessaryTag, file);
        NecessaryImportGoodsDTO goodsDTO = new NecessaryImportGoodsDTO();
        goodsDTO.setGoodsNos(CollectionUtils.isEmpty(goodNos) ? "" : goodNos.stream().collect(Collectors.joining(",")));
        return ResponseEntity.ok(CommonResult.ok(goodsDTO));
    }

    /**
     * 获取店型
     * @return
     */
    @Timed
    @ApiOperation("新增-获取店型")
    @GetMapping("/necessary/getStoreTypeList")
    public ResponseEntity<CommonResult<SelectorResult>> getStoreTypeList(HttpServletRequest request,
                                           @RequestParam("platformId") Long platformId,
                                           @RequestParam("necessaryTag") Byte necessaryTag,
                                           @RequestParam(value = "companyOrgId", required = false) Long companyOrgId,
                                           @RequestParam(value = "citys", required = false) String citys){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getStoreTypeList(platformId, necessaryTag, companyOrgId, citys, userDTO)));
    }

    @Timed
    @ApiOperation(value = "导入淘汰数据更新MDM必备", notes = "导入淘汰数据更新MDM必备")
    @PostMapping("/importDiscardGoodsUpdateMdmNecessaryTag")
    public ResponseEntity<CommonResult> importDiscardGoodsUpdateMdmNecessaryTag(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        mdmGoodsStatusUpdateService.importDiscardGoodsUpdateMdmNecessaryTag(file, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 根据门店 商品 必备层级刷新一店一目数据
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("根据门店编码删除一店一目数据")
    @PostMapping("/necessary/deleteStoreGoodsByStoreCode")
    public ResponseEntity<CommonResult> deleteStoreGoodsByStoreCode(HttpServletRequest request, @RequestBody List<Long> orgIds) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            asyncTaskExecutor.submit(() -> {
                necessaryContentsService.deleteStoreGoodsByStoreCode(orgIds, userDTO,null);
            });
            return ResponseEntity.ok(CommonResult.ok());
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    /**
     * 门店复制-导入门店
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("门店复制-导入门店")
    @PostMapping("/necessary/copy/importStore")
    public ResponseEntity importCopyStore(HttpServletRequest request, @RequestParam("file") MultipartFile file){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", JSON.toJSONString(userDTO), "NecessaryContentsResource.importCopyStore");
        return ResponseEntity.ok(necessaryContentsService.importCopyStore(file, userDTO));
    }

    /**
     * 门店复制-导入门店进度
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("门店复制-导入门店进度")
    @GetMapping("/necessary/copy/getImportProcess")
    public ResponseEntity<CommonProcessDTO<List<ImportStoreDTO>>> getImportProcess(HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", JSON.toJSONString(userDTO), "NecessaryContentsResource.getImportProcess");
        return ResponseEntity.ok(necessaryContentsService.getImportProcess(userDTO));
    }

    /**
     * 批量导入商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("批量导入商品")
    @PostMapping("/necessary/batchImportGoods")
    public ResponseEntity batchImportGoods(HttpServletRequest request, @RequestParam("file") MultipartFile file,
                                           @RequestParam("necessaryTag") Byte necessaryTag,
                                           @RequestParam("platformId") Long platformId,
                                           @RequestParam(value = "companyOrgId") Long companyOrgId,
                                           @RequestParam(value = "city", required = false) String city){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},necessaryTag={}platformId={},companyOrgId={},city={}", JSON.toJSONString(userDTO), "NecessaryContentsResource.batchImportGoods", necessaryTag, platformId, companyOrgId, city);
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.batchImportGoods(file, necessaryTag, platformId, companyOrgId, city, userDTO)));
    }

    /**
     * 批量导入商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("批量导入商品")
    @GetMapping("/necessary/getBatchImportProcess")
    public ResponseEntity getBatchImportProcess(HttpServletRequest request,
                                           @RequestParam("necessaryTag") Byte necessaryTag,
                                           @RequestParam("platformId") Long platformId,
                                           @RequestParam(value = "companyOrgId") Long companyOrgId,
                                           @RequestParam(value = "city", required = false) String city){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},necessaryTag={}platformId={},companyOrgId={},city={}", JSON.toJSONString(userDTO), "NecessaryContentsResource.batchImportGoods", necessaryTag, platformId, companyOrgId, city);
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsService.getBatchImportProcess(platformId, companyOrgId, city, necessaryTag, userDTO)));
    }
}
