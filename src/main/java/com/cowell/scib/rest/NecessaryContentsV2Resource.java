package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.entityDgms.NecessaryLevelConfig;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.NecessaryContentsV2Service;
import com.cowell.scib.service.dto.CommonProcessDTO;
import com.cowell.scib.service.dto.ImportStoreDTO;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.necessaryComtentsV2.*;
import com.cowell.scib.service.dto.necessaryContents.NecessaryAddMsg;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCopyParam;
import com.cowell.scib.service.dto.necessaryContents.StoreGoodsQueryParam;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 必备目录管理
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/necessary/contents")
@Api(tags = "必备目录接口V2", description = "必备目录接口V2")
public class NecessaryContentsV2Resource {

    private final Logger logger = LoggerFactory.getLogger(NecessaryContentsV2Resource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private NecessaryContentsV2Service necessaryContentsV2Service;

    @Autowired
    @Qualifier("mdmGoodStatusUpdateSixNecessaryExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    /**
     * 分页获取必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "分页获取必备目录")
    @PostMapping(value = "/getContents")
    public ResponseEntity<CommonResult<PageResult<NecessaryContentsDTO>>> getContents(HttpServletRequest request, @RequestBody NecessaryQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsV2Resource.getContents", JSON.toJSONString(param));
        PageResult<NecessaryContentsDTO> pageResult = necessaryContentsV2Service.getContents(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok(pageResult));
    }

    /**
     * 添加必备目录商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "添加必备目录商品")
    @PostMapping(value = "/addNecessaryGoods")
    public ResponseEntity<CommonResult<NecessaryAddMsg>> addNecessaryGoods(HttpServletRequest request, @RequestBody NecessaryAddParam param) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsV2Resource.addNecessaryGoods", JSON.toJSONString(param));
        NecessaryAddMsg addMsg = new NecessaryAddMsg();
        String msg = necessaryContentsV2Service.editNecessaryGoods(userDTO, param, null);
        addMsg.setMessage(msg);
        if (StringUtils.isNotBlank(msg) && msg.endsWith("是否排除掉重复商品，继续添加？")) {
            addMsg.setExistsIgnore(param.getExistsIgnore());
        } else {
            addMsg.setExistsIgnore(true);
        }
        return ResponseEntity.ok(CommonResult.ok(msg, addMsg));
    }

    /**
     * 删除必备目录商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "停用/启用必备目录商品")
    @PostMapping("/modify")
    public ResponseEntity<CommonResult<NecessaryAddMsg>> modify(HttpServletRequest request, @RequestBody NecessaryModifyParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsV2Resource.modify", JSON.toJSONString(param));
        try {
            NecessaryAddMsg addMsg = new NecessaryAddMsg();
            addMsg.setMessage(necessaryContentsV2Service.modify(userDTO, param.getIds(), param.getStatus(), param.getInvalidReason(), null, null));
            return ResponseEntity.ok(CommonResult.ok(addMsg.getMessage(),addMsg));
        } catch (Exception e) {
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    @ApiOperation("导入必备数据")
    @Timed
    @PostMapping("/importContent")
    public ResponseEntity<ImportResult> importNecessaryContent(MultipartFile file, Integer bizType, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = bizType.equals(1) ? necessaryContentsV2Service.importContent(file, bizType, userDTO) : necessaryContentsV2Service.importDelContent(file, bizType, userDTO);
        return ResponseEntity.ok().body(importResult);
    }

    @Timed
    @ApiOperation(value = "导出必备目录")
    @PostMapping(value = "/exportContents")
    public ResponseEntity exportContents(HttpServletRequest request, @RequestBody NecessaryQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsV2Resource.exportContents", JSON.toJSONString(param));
        necessaryContentsV2Service.exportContents(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok("正在导出,请稍后去下载中心查看"));
    }

    /**
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("根据门店id删除一店一目数据")
    @PostMapping("/deleteStoreGoods")
    public ResponseEntity<CommonResult> deleteStoreGoodsByStoreCode(HttpServletRequest request, @RequestBody List<Long> orgIds) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            asyncTaskExecutor.submit(() -> {
                necessaryContentsV2Service.deleteStoreGoods(orgIds, userDTO, null);
            });
            return ResponseEntity.ok(CommonResult.ok());
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    /**
     * 复制门店必备目录-获取原门店必备目录信息
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "复制门店必备目录-获取原门店必备目录信息")
    @PostMapping("/getSourceStoreInfos")
    public ResponseEntity<CommonResult<PageResult<StoreGoodsContentDTO>>> getSourceStoreInfos(HttpServletRequest request, @RequestBody StoreGoodsQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "NecessaryContentsV2Resource.getSourceStoreInfos", JSON.toJSONString(param));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsV2Service.getSourceStoreInfos(param, userDTO)));
    }

    /**
     * 复制门店必备目录
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "复制门店必备目录-复制")
    @PostMapping("/copyStoreGoods")
    public ResponseEntity copyStoreGoods(HttpServletRequest request, @RequestBody NecessaryCopyParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},sourceStoreId={},targetStoreId={}", userDTO, "NecessaryContentsV2Resource.copyStoreGoods", JSON.toJSONString(param));
        return ResponseEntity.ok(CommonResult.ok(necessaryContentsV2Service.copyStoreGoods(userDTO, param)));
    }

    @Timed
    @ApiOperation(value = "复制门店必备目录-修改复制的门店状态")
    @GetMapping("/necessary/updateCopyStoreGoodsStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(name="sourceStoreId",value="原门店id",required = true),
            @ApiImplicitParam(name="goodsNo",value="商品编码",required = true),
            @ApiImplicitParam(name="effectStatus",value="是否有效(0 否 1 是)",required = true),
    })
    public ResponseEntity updateCopyStoreGoodsStatus(HttpServletRequest request, @RequestParam(value = "sourceStoreId") Long sourceStoreId, @RequestParam(value = "goodsNo") String goodsNo, @RequestParam(value = "effectStatus") Byte effectStatus){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},sourceStoreId={},targetStoreId={}", userDTO, "NecessaryContentsResource.updateCopyStoreGoodsStatus", sourceStoreId, goodsNo, effectStatus);
        necessaryContentsV2Service.updateCopyStoreGoodsStatus(sourceStoreId, goodsNo, effectStatus, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 门店复制-导入门店
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("门店复制-导入门店")
    @PostMapping("/copy/importStore")
    public ResponseEntity importCopyStore(HttpServletRequest request, @RequestParam("file") MultipartFile file){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", JSON.toJSONString(userDTO), "NecessaryContentsV2Resource.importCopyStore");
        return ResponseEntity.ok(necessaryContentsV2Service.importCopyStore(file, userDTO));
    }

    /**
     * 门店复制-导入门店进度
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("门店复制-导入门店进度")
    @GetMapping("/copy/getImportProcess")
    public ResponseEntity<CommonProcessDTO<List<ImportStoreDTO>>> getImportProcess(HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", JSON.toJSONString(userDTO), "NecessaryContentsV2Resource.getImportProcess");
        return ResponseEntity.ok(necessaryContentsV2Service.getImportProcess(userDTO));
    }

    @Timed
    @ApiOperation("根据必备标签获取配置")
    @GetMapping("/getConfigLevelByNecessaryTag")
    public ResponseEntity<NecessaryLevelConfig> getConfigLevelByNecessaryTag(HttpServletRequest request, @RequestParam(value = "necessaryTag") Integer necessaryTag){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", JSON.toJSONString(userDTO), "NecessaryContentsV2Resource.getConfigLevelByNecessaryTag");
        return ResponseEntity.ok(necessaryContentsV2Service.getConfigLevelByNecessaryTag(necessaryTag));
    }

    @Timed
    @ApiOperation("判断当前登录人是否有编辑权限")
    @GetMapping("/checkHasEditPerm")
    public ResponseEntity<Boolean> checkHasEditPerm(HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", JSON.toJSONString(userDTO), "NecessaryContentsV2Resource.checkHasEditPerm");
        return ResponseEntity.ok(necessaryContentsV2Service.checkHasEditPerm(userDTO));
    }

    @ApiOperation("导入修改一店一目经营状态 bizType 1 修改经营状态 2 修改商品分类")
    @Timed
    @PostMapping("/importUpdateManageStauts")
    public ResponseEntity<String> importUpdateManageStauts(MultipartFile file, HttpServletRequest request, Integer bizType) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        if (bizType.equals(1)) {
            necessaryContentsV2Service.importUpdateManageStauts(file, userDTO);
        } else if (bizType.equals(2)) {
            necessaryContentsV2Service.importUpdateCategory(file, userDTO);
        }
        return ResponseEntity.ok("导入成功");
    }

    @Timed
    @ApiOperation("海峰的选择器")
    @GetMapping("/selectMdmStoreIdFilterSelector")
    public ResponseEntity<List<Long>> selectMdmStoreIdFilterSelector(HttpServletRequest request, @RequestParam(value = "platfromOrgId") Long platformOrgId){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param{}", JSON.toJSONString(userDTO), "NecessaryContentsV2Resource.selectMdmStoreIdFilterSelector", platformOrgId);
        return ResponseEntity.ok(necessaryContentsV2Service.selectMdmStoreIdFilterSelector(platformOrgId, userDTO));
    }

//    @ApiOperation("导入修改商品子类")
//    @Timed
//    @PostMapping("/importUpdateCategory")
//    public ResponseEntity<String> importUpdateCategory(MultipartFile file, HttpServletRequest request) throws Exception {
//        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
//        necessaryContentsV2Service.importUpdateCategory(file, userDTO);
//        return ResponseEntity.ok("导入成功");
//    }

}
