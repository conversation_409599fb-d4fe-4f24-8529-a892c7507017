package com.cowell.scib.rest;

import cn.hutool.core.lang.Assert;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.GoodsCommissionService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@RequestMapping("/api")
@Api(tags = "单店推荐", description = "单店推荐")
public class GoodsCommissionResource {

    @Autowired
    private GoodsCommissionService goodsCommissionService;

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @ApiOperation(value = "查询单店推荐获取提成")
    @GetMapping(value = "/intranet/GoodsCommission/replenishGoodsCommission")
    @Timed
    public ResponseEntity<CommonResult> replenishGoodsCommission() {
        goodsCommissionService.queryGoodsCommionService();
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "从智慧门店获取提成数据")
    @GetMapping(value = "/GoodsCommission/getGoodsCommissionForRest")
    @Timed
    public ResponseEntity<CommonResult> getGoodsCommissionForRest(HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Assert.notNull(userDTO,"用户信息不能为空");
        goodsCommissionService.queryGoodsCommionService();
        return ResponseEntity.ok(CommonResult.ok());
    }
}
