/*
package com.cowell.scib.rest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.service.MinDisplayService;
import com.cowell.scib.service.dto.MdmQueryParam;
import com.cowell.scib.service.dto.SeasonalGoodsPushDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

*/
/**
 * <AUTHOR>
 * @date 2022/8/26 10:27
 *//*

@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/test", "/api/test"})
@Api(tags = "SCIB测试接口", description = "SCIB测试接口")
public class TestResource {

    @Timed
    @GetMapping("/hello")
    public ResponseEntity<String> goodsList(@RequestParam(value = "name") String name, HttpServletRequest request){
        try {
            JSONObject responseJson = new JSONObject();
            responseJson.put("status", 0);
            responseJson.put("msg","");

            JSONObject dataJson = new JSONObject();
            dataJson.put("type","tabs");

            JSONArray tabsArr = new JSONArray();
            JSONObject tab1 = new JSONObject();
            tab1.put("title","TabA");
            tab1.put("body","卡片A内容");
            JSONObject tab2 = new JSONObject();
            tab2.put("title","TabB");
            tab2.put("body","卡片B内容");
            tabsArr.add(tab1);
            tabsArr.add(tab2);
            dataJson.put("tabs",tabsArr);
            responseJson.put("data", dataJson);

            return new ResponseEntity(responseJson, HttpStatus.OK);
        } catch (BusinessErrorException e){
            throw new FailRequestAlertException(HttpStatus.BAD_REQUEST.name(), e.getMessage());
        } catch (Exception e) {
            throw new FailRequestAlertException(HttpStatus.BAD_REQUEST.name(), ErrorCodeEnum.SYSTEM_ERROR.getMsg());
        }
    }
}
*/
