package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.service.ITencentCOSService;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.UrlResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传
 * <AUTHOR>
 * @date 2022/8/31 15:09
 */
@Slf4j
@RestController
@RequestMapping(value = {"/api/internal/file", "/api/file"})
@Api(tags = "文件上传接口", description = "文件上传接口")
public class TencentCOSResource {

    @Autowired
    private ITencentCOSService tencentCOSService;

    @ApiOperation(value = "文件上传和获取接口")
    @PostMapping(value = "/uploadAndGetFile")
    @Timed
    public ResponseEntity<CommonResult<UrlResult>> uploadAndGetFile(@RequestParam(value = "fileName", required = false) String fileName, @RequestParam(value = "file", required = false) MultipartFile file) {
        try {
            UrlResult urlResult = tencentCOSService.uploadAndGetFile(fileName, file);
            return ResponseEntity.ok(CommonResult.ok(urlResult));
        } catch (Exception be) {
            log.warn("uploadAndGetFile|warn.",be);
            return ResponseEntity.ok(CommonResult.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), be.getMessage()));
        }
    }
}
