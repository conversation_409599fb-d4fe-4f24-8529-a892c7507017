package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.DevelopModuleReadService;
import com.cowell.scib.service.DevelopModuleWriteService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.service.param.DevelopSelectParam;
import com.cowell.scib.service.vo.DevelopModuleRecordVO;
import com.cowell.scib.service.vo.DevelopModuleVO;
import com.cowell.scib.service.vo.amis.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/29 13:35
 */
@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/develop", "/api/develop"})
@Api(tags = "发版管理接口", description = "发版管理接口")
public class DevelopModuleResource {

    @Autowired
    private DevelopModuleReadService developModuleReadService;
    @Autowired
    private DevelopModuleWriteService developModuleWriteService;
    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @ApiOperation(value = "模块列表查询")
    @GetMapping(value = "/moduleList")
    @Timed
    public ResponseEntity<CommonResult<ListResult<TabResult>>> moduleList(@RequestParam Integer useStatus) {
        List<TabResult> moduleVOList = developModuleReadService.listModuleVO(useStatus);
        return ResponseEntity.ok(CommonResult.ok(new ListResult(moduleVOList)));
    }

    @ApiOperation(value = "模块详情查询")
    @PostMapping(value = "/moduleDetail")
    @Timed
    public ResponseEntity<CommonResult<DevelopModuleVO>> moduleDetail(@RequestBody DevelopListParam developListParam) {
        DevelopModuleVO moduleVODetail = developModuleReadService.moduleDetail(developListParam);
        return ResponseEntity.ok(CommonResult.ok(moduleVODetail));
    }

    @ApiOperation(value = "模块添加或保存")
    @PostMapping(value = "/moduleAddOrEdit")
    @Timed
    public ResponseEntity<CommonResult> moduleAddOrEdit(@RequestBody DevelopAddParam developAddParam, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        developModuleWriteService.moduleAddOrEdit(developAddParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "模块记录列表查询")
    @PostMapping(value = "/recordList")
    @Timed
    public ResponseEntity<CommonResult<PageResult<DevelopModuleRecordVO>>> recordList(@RequestBody DevelopListParam developListParam) {
        PageResult<DevelopModuleRecordVO> moduleVOList = developModuleReadService.listPageModuleRecordVO(developListParam);
        return ResponseEntity.ok(CommonResult.ok(moduleVOList));
    }

    @ApiOperation(value = "模块记录详情查询")
    @PostMapping(value = "/recordDetail")
    @Timed
    public ResponseEntity<CommonResult<DevelopModuleRecordVO>> recordDetail(@RequestBody DevelopListParam developListParam) {
        DevelopModuleRecordVO moduleVODetail = developModuleReadService.recordDetail(developListParam);
        return ResponseEntity.ok(CommonResult.ok(moduleVODetail));
    }

    @ApiOperation(value = "发版记录添加或保存")
    @PostMapping(value = "/recordAddOrEdit")
    @Timed
    public ResponseEntity<CommonResult> recordAddOrEdit(@Validated  @RequestBody DevelopRecordAddParam developRecordAddParam, HttpServletRequest request) {
        log.info("recordAddOrEdit|developRecordAddParam:{}.", developRecordAddParam);
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        developModuleWriteService.recordAddOrEdit(developRecordAddParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "下拉框接口")
    @PostMapping(value = "/developSelectUnifyList")
    @Timed
    public ResponseEntity<CommonResult<SelectorResult>> developSelectUnifyList(@RequestBody DevelopSelectParam param) {
        if(Objects.isNull(param) || Objects.isNull(param.getApiCode())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        List<OptionDto> optionDtoList = developModuleReadService.developSelectUnifyList(param);
        return ResponseEntity.ok(CommonResult.ok(new SelectorResult(optionDtoList)));
    }

    @ApiOperation(value = "删除文件接口")
    @PostMapping(value = "/deleteFile")
    @Timed
    public ResponseEntity<CommonResult> deleteFile(@RequestParam(value = "id", required = false) Integer id) {
        developModuleWriteService.deleteFile(id);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "模块发版记录提醒")
    @GetMapping(value = "/recordDetailReminder")
    @Timed
    public ResponseEntity<CommonResult<DevelopModuleRecordVO>> recordDetailReminder(@RequestParam(value = "moduleCode") String moduleCode, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        log.info("recordDetailReminder|userDTO:{}.", userDTO);
        DevelopModuleRecordVO moduleVODetail = developModuleReadService.recordDetailReminder(moduleCode, userDTO);
        return ResponseEntity.ok(CommonResult.ok(moduleVODetail));
    }

    @ApiOperation(value = "是否有编辑和创建权限")
    @GetMapping(value = "/editAblePermission")
    @Timed
    public ResponseEntity<CommonResult<ObjectResult>> editAblePermission() {
        Boolean result = developModuleReadService.editAblePermission();
        ObjectResult objectResult = new ObjectResult();
        objectResult.setResult(result);
        return ResponseEntity.ok(CommonResult.ok(objectResult));
    }

    @ApiOperation(value = "删除redis缓存")
    @GetMapping(value = "/delReminderKey")
    @Timed
    public ResponseEntity<CommonResult> delReminderKey(@RequestParam(value = "redisKey") String redisKey) {
        developModuleReadService.delReminderKey(redisKey);
        return ResponseEntity.ok(CommonResult.ok());
    }

}
