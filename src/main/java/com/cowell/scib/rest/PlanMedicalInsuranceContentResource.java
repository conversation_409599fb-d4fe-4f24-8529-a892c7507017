package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.NecessaryLevelConfigService;
import com.cowell.scib.service.PlanMedicalInsuranceContentService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryLevelRoleConfigDTO;
import com.cowell.scib.service.dto.config.NecessaryRoleConfigUpdateParam;
import com.cowell.scib.service.dto.config.NecessaryUpdateParam;
import com.cowell.scib.service.dto.planContents.PlanContentParam;
import com.cowell.scib.service.dto.planContents.PlanMedicalInsuranceContentsDTO;
import com.cowell.scib.service.dto.rule.HotGoodsResponseDTO;
import com.cowell.scib.service.vo.amis.AmisPageParam;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "统筹医保目录接口", description = "统筹医保目录接口")
public class PlanMedicalInsuranceContentResource {


    private final Logger logger = LoggerFactory.getLogger(PlanMedicalInsuranceContentResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Resource
    private PlanMedicalInsuranceContentService planMedicalInsuranceContentService;
    @Timed
    @ApiOperation("查询统筹医保目录")
    @PostMapping("/plan/medical/insurance/list")
    public ResponseEntity<PageResult<PlanMedicalInsuranceContentsDTO>> list(HttpServletRequest request, @RequestBody PlanContentParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(planMedicalInsuranceContentService.list(param));
    }
    @Timed
    @ApiOperation("根据查询条件删除统筹医保目录")
    @PostMapping("/plan/medical/insurance/deleteBySelect")
    public ResponseEntity<CommonResult> deleteBySelect(HttpServletRequest request, @RequestBody PlanContentParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        planMedicalInsuranceContentService.deleteBySelect(param);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @Timed
    @ApiOperation(value = "统筹医保目录导入excel", notes = "统筹医保目录导入excel")
    @PostMapping("/plan/medical/insurance/import")
    public ResponseEntity<CommonResult<HotGoodsResponseDTO>> importPlan(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        HotGoodsResponseDTO resulMessage = planMedicalInsuranceContentService.importPlan(file, userDTO);
        return ResponseEntity.ok(CommonResult.ok(resulMessage));
    }

}
