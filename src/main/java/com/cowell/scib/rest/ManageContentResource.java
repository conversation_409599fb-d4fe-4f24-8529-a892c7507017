package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.entityDgms.JymlSkuLimit;
import com.cowell.scib.entityTidb.JymlStoreSkuExemptionRecord;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.ExemptionSourceEnum;
import com.cowell.scib.enums.ScibCommonEnums;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.ManageContentsService;
import com.cowell.scib.service.dto.OrgInfoBaseCache;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.dto.customize.CustomizeQueryParam;
import com.cowell.scib.service.dto.customize.JymlSkyExemptionParam;
import com.cowell.scib.service.dto.customize.JymlStoreIncreaseLimitConfigureDTO;
import com.cowell.scib.service.dto.manageContents.*;
import com.cowell.scib.service.dto.necessaryComtentsV2.NecessaryQueryParam;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "经营目录接口", description = "经营目录接口")
public class ManageContentResource {

    private final Logger logger = LoggerFactory.getLogger(ManageContentResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private ManageContentsService manageContentsService;

    //intranet（intranet，仅限内网访问、调用的接口，外部ip请求nginx限制 /api/intranet  ，场景：如海典、华南、大数据调用等）
    // STORE_TYPE_SKU_UPPER_LIMIT_CTRL(1, "店型级SKU数配置上限管控"),
    // STORE_MANAGE_CONTENTS_SUGGEST(2, "门店经营目录建议结果"),
    // businessIds 本次 businessIds="17996,12501"  当前版本有数据时才历史当前连锁数据清除
    @ApiOperation("大数据经营目录推荐结果通知")
    @Timed
    @GetMapping("/intranet/manage/bdpManageSuggestResultNotify")
    public ResponseEntity<CommonResult> bdpManageSuggestResultNotify(@RequestParam("version") Long version,@RequestParam("pushType") Integer pushType,@RequestParam(value = "businessOrgIds",required = false) String businessOrgIds) {
        logger.info("ManageContentResource.bdpManageSuggestResultNotify version={},pushType={} businessIds={}", version, pushType, businessOrgIds);
        Assert.notNull(version, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        Assert.isTrue(ScibCommonEnums.ManagePushTypeEnum.containsCode(pushType), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        try{
            logger.info("大数据经营目录推荐结果通知");
            CommonResult result = CommonResult.ok();
            if(ScibCommonEnums.ManagePushTypeEnum.STORE_TYPE_SKU_UPPER_LIMIT_CTRL.getCode().equals(pushType)){
                if (StringUtils.isBlank(businessOrgIds)) {
                    return ResponseEntity.ok(CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION));
                }
                result =   manageContentsService.bdpManageSkuLimitResultNotify(version, businessOrgIds);
            }else if(ScibCommonEnums.ManagePushTypeEnum.STORE_MANAGE_CONTENTS_SUGGEST.getCode().equals(pushType)){
                if(StringUtils.isNotBlank(businessOrgIds)){
                    result =   manageContentsService.bdpManageSuggestResultNotify(version, businessOrgIds);
                }else {
                    return ResponseEntity.ok(CommonResult.error(ErrorCodeEnum.PARAM_ERROR_EXCEPTION));
                }
            }
            return ResponseEntity.ok(result);
        }catch (Exception e){
            logger.error("大数据经营目录推荐结果通知",e);
            return ResponseEntity.ok(CommonResult.ok(-1,"失败"));
        }
    }

    @ApiOperation("查询经营配置下拉框")
    @PostMapping({"/manage/manageContentsDropBox"})
    public ResponseEntity<CommonResponse<OptionDto>> manageContentsDropBox(HttpServletRequest request, @RequestBody ManageQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "ManageContentResource.manageContentsDropBox", userDTO, JSON.toJSONString(param));
        return ResponseEntity.ok(manageContentsService.manageContentsDropBox(userDTO,param));
    }

    @ApiOperation("经营分类上限")
    @PostMapping({"/intranet/manage/manageCategoryLimit","/manage/manageCategoryLimit"})
    public ResponseEntity<CommonResult> manageCategoryLimit(HttpServletRequest request, @RequestBody ManageQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "ManageContentResource.manageCategoryLimit", userDTO, JSON.toJSONString(param));
        return ResponseEntity.ok(manageContentsService.manageCategoryLimit(userDTO,param));
    }

    @Timed
    @ApiOperation("查询经营目录接口")
    @PostMapping({"/intranet/manage/list","/manage/list"})
    public ResponseEntity<CommonResponse> list(HttpServletRequest request,  @RequestBody ManageQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "ManageContentResource.list",userDTO, JSON.toJSONString(param));
        return ResponseEntity.ok( manageContentsService.manageContentsList(userDTO,param));
    }

    @ApiOperation("查询经营目录左侧树接口")
    @PostMapping({"/intranet/manage/manageContentsTree", "/manage/manageContentsTree"})
    public ResponseEntity<CommonResponse> manageContentsTree(HttpServletRequest request, @RequestBody ManageQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={} 参数={}", "ManageContentResource.manageContentsTree", userDTO,JSON.toJSONString(param));
        return ResponseEntity.ok(manageContentsService.manageContentsTree(userDTO,param));
    }

    @ApiOperation("批量确认经营目录")
    @PostMapping({"/intranet/manage/batchUpdate", "/manage/batchUpdate"})
    public ResponseEntity<CommonResult> batchUpdate(HttpServletRequest request, @RequestBody ManageBatchUpdateParam manageCommonDTOList){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}  参数={}", "ManageContentResource.batchUpdate", userDTO, JSON.toJSONString(manageCommonDTOList));
        return ResponseEntity.ok(manageContentsService.batchUpdate(userDTO,manageCommonDTOList));
    }

    @ApiOperation("经营目录管理进度数据汇总")
    @PostMapping({"/intranet/manage/summarizeData", "/manage/summarizeData"})
    public ResponseEntity<CommonResult> summarizeData(HttpServletRequest request, @RequestBody ManageQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}  参数={}", "ManageContentResource.summarizeData", userDTO, JSON.toJSONString(param));
        return ResponseEntity.ok(manageContentsService.summarizeMdProcessData(userDTO,param));
    }

    @ApiOperation("经营目录单独处理连锁或门店数据")
    @PostMapping({"/manage/handleSuggest"})
    public ResponseEntity<CommonResult> handleSuggest(@RequestBody ManageSuggestMqDTO suggestMqDTO){
        logger.info("操作={}  参数={}", "ManageContentResource.suggestMqDTO", JSON.toJSONString(suggestMqDTO));
        return ResponseEntity.ok(CommonResult.ok(manageContentsService.handleSuggestByStoreOrBusiness(suggestMqDTO)+""));
    }

    @ApiOperation("经营目录-获取SKU数配置上限管理")
    @PostMapping({"/intranet/manage/getSkuLimitList", "/manage/getSkuLimitList"})
    public ResponseEntity<PageResult<JymlSkuMaxLimitConfigureDTO>> getSkuLimitList(HttpServletRequest request, @RequestBody ManageJymlSkuMaxLimitQueryParam queryParam){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("操作={}  参数={}", "ManageContentResource.skuLimitCheck", JSON.toJSONString(queryParam));
        return ResponseEntity.ok(manageContentsService.getSkuLimitList(queryParam,userDTO));
    }

    @Timed
    @ApiOperation(value = "导出SKU数配置上限管理")
    @PostMapping(value = "/exportSkuLimitList")
    public ResponseEntity exportSkuLimitList(HttpServletRequest request, @RequestBody ManageJymlSkuMaxLimitQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "ManageContentResource.exportSkuLimitList", JSON.toJSONString(param));
        manageContentsService.exportSkuLimitList(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok("正在导出,请稍后去下载中心查看"));
    }

    @ApiOperation("经营目录-获取集团标准列表")
    @PostMapping({"/intranet/manage/getJtJymlLimitList", "/manage/getJtJymlLimitList"})
    public ResponseEntity<PageResult<JymlSkuLimit>> getJtJymlLimitList(HttpServletRequest request, @RequestBody JtQueryParam queryParam){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("操作={}  参数={}", "ManageContentResource.getJtJymlLimitList", JSON.toJSONString(queryParam));
        return ResponseEntity.ok(manageContentsService.getJtJymlLimitList(queryParam,userDTO));
    }

    @ApiOperation("经营目录-SKU数配置上限管理")
    @PostMapping({"/intranet/manage/skuLimitUpdate", "/manage/skuLimitUpdate"})
    public ResponseEntity<CommonResult> skuLimitCheck(HttpServletRequest request, @RequestBody ManageJymlSkuMaxLimitParam limitParam){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("操作={}  参数={}", "ManageContentResource.skuLimitCheck", JSON.toJSONString(limitParam));
        return ResponseEntity.ok(manageContentsService.skuLimitUpdate(limitParam,userDTO));
    }

    @ApiOperation("经营目录-selectMdmStoreIdFilterSelector")
    @PostMapping({"/manage/selectMdmStoreIdFilterSelector"})
    public ResponseEntity<CommonResult> selectMdmStoreIdFilterSelector(HttpServletRequest request, @RequestBody OrgInfoBaseCache infoBaseCache){
        logger.info("操作={}  参数={}", "ManageContentResource.skuLimitCheck", JSON.toJSONString(infoBaseCache));
        return ResponseEntity.ok(CommonResult.ok(JSONObject.toJSONString(manageContentsService.selectMdmStoreIdFilterSelector(infoBaseCache))));
    }

    @ApiOperation("经营目录-查询豁免商品")
    @PostMapping({"/intranet/manage/getNonJyGoods", "/manage/getNonJyGoods"})
    public ResponseEntity<CommonResponse> getNonJyGoods(HttpServletRequest request, @RequestBody ManageJymlNonJyGoodsParam goodsParam){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("操作={} 参数={}", "ManageContentResource.getNonJyGoods", JSON.toJSONString(goodsParam));
        return ResponseEntity.ok(manageContentsService.getNonJyGoods(goodsParam,userDTO));
    }

    @ApiOperation("经营目录-查询已添加豁免商品列表")
    @PostMapping({"/intranet/manage/nonJyGoodsPage", "/manage/nonJyGoodsPage"})
    public ResponseEntity<CommonResponse> nonJyGoodsPage(HttpServletRequest request, @RequestBody JymlStoreSkuExemptionRecord record){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("操作={} 参数={}", "ManageContentResource.nonJyGoodsPage", JSON.toJSONString(record));
        return ResponseEntity.ok(manageContentsService.nonJyGoodsPage(record,userDTO));
    }

    @ApiOperation("经营目录-添加或者删除豁免商品")
    @PostMapping({"/intranet/manage/nonJyGoodsUpdate", "/manage/nonJyGoodsUpdate"})
    public ResponseEntity<CommonResult> nonJyGoodsUpdate(HttpServletRequest request, @RequestBody JymlStoreSkuExemptionRecord record){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("操作={} 参数={}", "ManageContentResource.nonJyGoodsUpdate", JSON.toJSONString(record));
        record.setSource(ExemptionSourceEnum.STORE_ADD.getCode());
        return ResponseEntity.ok(manageContentsService.nonJyGoodsUpdate(record,userDTO));
    }

    @ApiOperation("经营目录-修改不匹配的经营目录")
    @PostMapping({ "/manage/updateDiffManageStatus"})
    public ResponseEntity updateDiffManageStatus(HttpServletRequest request, @RequestBody List<String> storeCodes){
        logger.info("操作={} 参数={}", "ManageContentResource.udateDiffManageStatus", JSON.toJSONString(storeCodes));
        manageContentsService.updateDiffManageStatus(storeCodes);
        return ResponseEntity.ok("修改成功");
    }

    @ApiOperation("经营目录-导入批量更新经营目录")
    @PostMapping({ "/manage/importUpdateManageStatus"})
    public ResponseEntity importUpdateManageStatus(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file){
        manageContentsService.importUpdateManageStatus(file);
        return ResponseEntity.ok("修改成功");
    }

    @ApiOperation("经营目录-批量导入豁免商品")
    @PostMapping({"/intranet/manage/importNonJyGoods", "/manage/importNonJyGoods"})
    public ResponseEntity<ImportResult> importNonJyGoods(HttpServletRequest request, MultipartFile file){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        try {
            return ResponseEntity.ok(manageContentsService.importNonJyGoods(file,userDTO));
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("经营目录-二轮选配商品批量维护查询")
    @PostMapping({"/intranet/manage/getMaintenanceNonJyGoods", "/manage/getMaintenanceNonJyGoods"})
    public PageResult<JymlStoreIncreaseLimitConfigureDTO> getMaintenanceNonJyGoods(HttpServletRequest request, @RequestBody JymlSkyExemptionParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        try {
            return manageContentsService.getMaintenanceNonJyGoods(param,userDTO, new ArrayList<>());
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("导出")
    @PostMapping({"/intranet/manage/exportMaintenanceNonJyGoods", "/manage/exportMaintenanceNonJyGoods"})
    public CommonResult export(HttpServletRequest request, @RequestBody JymlSkyExemptionParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "JymlCustomizeChooseSkuResource.exportMaintenanceNonJyGoods", userDTO, JSON.toJSONString(param));
        manageContentsService.exportMaintenanceNonJyGoods(userDTO,param);
        return CommonResult.ok("正在导出,请稍后去下载中心查看");
    }


}
