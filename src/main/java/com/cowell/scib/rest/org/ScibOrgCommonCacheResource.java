package com.cowell.scib.rest.org;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.cache.CacheVar;
import com.cowell.scib.enums.OrgTypeEnum;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年04月27日15:19:27
 */
@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/org/cache", "/api/org/cache"})
@Api(tags = "机构信息Common缓存", description = "机构信息Common缓存")
public class ScibOrgCommonCacheResource {

    @ApiOperation(value = "根据sapCodeList和orgType(500/800)获取机构信息缓存")
    @PostMapping(value = "/getOrgBaseCacheBySapCode")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheBySapCode(@RequestParam List<String> sapCodeList,@RequestParam int orgType) {
        if(OrgTypeEnum.BUSINESS.getCode().equals(orgType)){
            return ResponseEntity.ok(CacheVar.getBusinessBySapCodeList(sapCodeList).orElseGet(null));
        }else if(OrgTypeEnum.STORE.getCode().equals(orgType)){
            return ResponseEntity.ok(CacheVar.getStoreByStoreCodeList(sapCodeList).orElseGet(null));
        }
        return ResponseEntity.ok(null);
    }
    @ApiOperation(value = "根据outIdList和orgType(500/800)获取机构信息缓存")
    @PostMapping(value = "/getOrgBaseCacheByOutId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheByOutId(@RequestParam List<Long> outIdList ,@RequestParam int orgType) {
        if(OrgTypeEnum.BUSINESS.getCode().equals(orgType)){
            return ResponseEntity.ok(CacheVar.getBusinessOrgByBusinessIdList(outIdList).orElseGet(null));
        }else if(OrgTypeEnum.STORE.getCode().equals(orgType)){
            return ResponseEntity.ok(CacheVar.getStoreByStoreIdList(outIdList).orElseGet(null));
        }
        return ResponseEntity.ok(null);
    }
    @ApiOperation(value = "根据orgIdList和orgType(300/500/800)获取机构信息缓存")
    @PostMapping(value = "/getOrgBaseCacheByOrgId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheByOrgId(@RequestParam(required = false) List<Long> orgIdList ,@RequestParam int orgType) {
        if(OrgTypeEnum.BUSINESS.getCode().equals(orgType)){
            return ResponseEntity.ok(CacheVar.getBusinessOrgByOrgIdList(orgIdList).orElseGet(null));
        }else if(OrgTypeEnum.STORE.getCode().equals(orgType)){
            return ResponseEntity.ok(CacheVar.getStoreByStoreOrgIdList(orgIdList).orElseGet(null));
        }else if(OrgTypeEnum.PLATFORM.getCode().equals(orgType)){
            // 兼容获取所有平台
            if (CollectionUtils.isEmpty(orgIdList)) {
                List<OrgInfoBaseCache> collect = CacheVar.platformCacheMap.values().stream().collect(Collectors.toList());
                return ResponseEntity.ok(collect);
            }
            List<OrgInfoBaseCache> collect = orgIdList.stream()
                    .map(key -> CacheVar.getPlatformByOrgId(key).orElse(null)).filter(v -> Objects.nonNull(v))
                    .collect(Collectors.toList());
            return ResponseEntity.ok(collect);
        }
        return ResponseEntity.ok(null);
    }

    @ApiOperation(value = "根据businessId获取全部门店信息缓存")
    @GetMapping(value = "/getStoreListByBusinessId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getStoreListByBusinessId(@RequestParam Long businessId) {
        return ResponseEntity.ok(CacheVar.getStoreListByBusinessId(businessId));
    }

    @ApiOperation(value = "根据platformOrgId获取全部门店信息缓存")
    @GetMapping(value = "/getStoreListByPlatformOrgId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getStoreListByPlatformOrgId(@RequestParam Long platformOrgId) {
        return ResponseEntity.ok(CacheVar.getStoreListByPlatformOrgId(platformOrgId));
    }

    @ApiOperation(value = "批量获取门店/连锁缓存列表(By (type&orgIdList) 或storeIdList/businessIdList)")
    @PostMapping(value = "/getOrgBaseCacheListByOutIdList")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheListByOutIdList(@RequestBody CommonOrgCacheDTO cacheDTO) {
        if(Objects.nonNull(cacheDTO) && !CollectionUtils.isEmpty(cacheDTO.getStoreIdList())){
            return ResponseEntity.ok(CacheVar.getStoreByStoreIdList(cacheDTO.getStoreIdList()).orElse(new ArrayList<>()));
        }
        if(Objects.nonNull(cacheDTO) && !CollectionUtils.isEmpty(cacheDTO.getBusinessIdList())){
            return ResponseEntity.ok(CacheVar.getBusinessOrgByBusinessIdList(cacheDTO.getBusinessIdList()).orElse(new ArrayList<>()));
        }
        if(Objects.nonNull(cacheDTO) && !CollectionUtils.isEmpty(cacheDTO.getOrgIdList()) &&  OrgTypeEnum.BUSINESS.getCode().equals(cacheDTO.getType())){
            return ResponseEntity.ok(CacheVar.getBusinessOrgByOrgIdList(cacheDTO.getOrgIdList()).orElse(new ArrayList<>()));
        }
        if(Objects.nonNull(cacheDTO) && !CollectionUtils.isEmpty(cacheDTO.getOrgIdList()) &&  OrgTypeEnum.STORE.getCode().equals(cacheDTO.getType())){
            return ResponseEntity.ok(CacheVar.getStoreByStoreOrgIdList(cacheDTO.getOrgIdList()).orElse(new ArrayList<>()));
        }
        return ResponseEntity.ok(new ArrayList<>());
    }
    @ApiOperation(value = "批量获缓存的省市列表")
    @PostMapping(value = "/getProvinceListCache")
    @Timed
    public ResponseEntity<CommonResponse<ProvinceCityCache>> getProvinceListCache() {
        List<ProvinceCityCache> provinceCityCaches = new ArrayList<>();
        CacheVar.provinceMap.forEach((k,v) -> {
            ProvinceCityCache cache = new ProvinceCityCache();
            cache.setProvince(k);
            cache.setCity(Lists.newArrayList(v));
            provinceCityCaches.add(cache);
        });
        return ResponseEntity.ok(CommonResponse.ok(provinceCityCaches));
    }
    @ApiOperation(value = "根据省获取市列表")
    @PostMapping(value = "/getCityListCacheByProvince")
    @Timed
    public ResponseEntity<CommonResponse<CityCache>> getCityListCacheByProvince(@RequestParam(value = "provinces") String provinces) {
        List<CityCache> cityCaches = new ArrayList<>();
        if (StringUtils.isBlank(provinces)) {
            return ResponseEntity.ok(CommonResponse.ok(cityCaches));
        }
        Arrays.stream(StringUtils.split(provinces, ",")).forEach(province -> {
            Optional.ofNullable(CacheVar.provinceMap.get(province)).orElse(new HashSet<>()).stream().forEach(city -> {
                CityCache cache = new CityCache();
                cache.setCity(city);
                cityCaches.add(cache);
            });
        });
        return ResponseEntity.ok(CommonResponse.ok(cityCaches));
    }
    @ApiOperation(value = "根据省获取市列表")
    @PostMapping(value = "/getAreaListCacheByCity")
    @Timed
    public ResponseEntity<CommonResponse<AreaCache>> getAreaListCacheByCity(@RequestParam(value = "citys") String citys) {
        List<AreaCache> areaCaches = new ArrayList<>();
        if (StringUtils.isBlank(citys)) {
            return ResponseEntity.ok(CommonResponse.ok(areaCaches));
        }
        Arrays.stream(StringUtils.split(citys, ",")).forEach(city -> {
            Optional.ofNullable(CacheVar.cityMap.get(city)).orElse(new HashSet<>()).stream().forEach(area -> {
                AreaCache cache = new AreaCache();
                cache.setArea(area);
                areaCaches.add(cache);
            });
        });
        return ResponseEntity.ok(CommonResponse.ok(areaCaches));
    }
}
