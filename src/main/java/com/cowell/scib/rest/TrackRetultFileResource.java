package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.nyuwa.cos.util.CosService;
import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.entityTidb.TrackResultFile;
import com.cowell.scib.entityTidb.TrackResultLevelNecessary;
import com.cowell.scib.enums.BundlTaskStatusEnum;
import com.cowell.scib.enums.BundlTaskTypeEnum;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.TaskStatusChangeEnum;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.AmisDataInInterface;
import com.cowell.scib.service.BundlTaskWriteService;
import com.cowell.scib.service.TrackRetultFileService;;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.RuleDetailDTO;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.vo.amis.AmisMap;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.List;
import java.util.Map;


@RestController
@Slf4j
@RequestMapping("/api")
@Api(tags = "组货任务结果通知", description = "组货任务结果通知")
public class TrackRetultFileResource {

    private final Logger logger = LoggerFactory.getLogger(TrackRetultFileResource.class);

    @Autowired
    private TrackRetultFileService trackRetultFileService;

    @Autowired
    private CosService cosService;

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private BundlTaskWriteService bundlTaskWriteService;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper; ;

    @ApiOperation("1企业复盘文件生成")
    @Timed
    @GetMapping("/createFileLevelReviewFile")
    public ResponseEntity<CommonResult> createFileLevelReviewFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createFileLevelReviewFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("7生成组货结果明细生成")
    @Timed
    @GetMapping("/createGroupGoodsResultsFile")
    public ResponseEntity<CommonResult> createGroupGoodsResultsFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createGroupGoodsResultsFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("2生成成分复盘文件")
    @Timed
    @GetMapping("/createTrackRetultCompositionReviewFile")
    public ResponseEntity<CommonResult> createTrackRetultCompositionReviewFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createTrackRetultCompositionReviewFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("3生成组货效率分析文件")
    @Timed
    @GetMapping("/createTrackRetultEfficiencyAnalyseFile")
    public ResponseEntity<CommonResult> createTrackRetultEfficiencyAnalyseFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createTrackRetultEfficiencyAnalyseFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("4生成必备目录全量表(一店一目级别)文件")
    @Timed
    @GetMapping("/createTrackRetultAllDetailFile")
    public ResponseEntity<CommonResult> createTrackRetultAllDetailFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createTrackRetultAllDetailFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }



    @ApiOperation("5前四级必备全量（店型级）文件")
    @Timed
    @GetMapping("/createTrackRetultTop4levelStoreGroupFile")
    public ResponseEntity<CommonResult> createTrackRetultTop4levelStoreGroupFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createTrackRetultTop4levelStoreGroupFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("6单店级必备明细（后2级）")
    @Timed
    @GetMapping("/createTrackRetult2levelSingleFile")
    public ResponseEntity<CommonResult> createTrackRetult2levelSingleFile(@RequestParam("taskId")Long taskId, @RequestParam("version") Integer version) {
        trackRetultFileService.createTrackRetult2levelSingleFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok());
    }
    @ApiOperation("新店推荐目录")
    @Timed
    @GetMapping("/createFileNewStoreFile")
    public ResponseEntity<CommonResult> createFileNewStoreFile(@RequestParam("taskId")Long taskId, HttpServletResponse response){
        try {
            trackRetultFileService.createFileNewStoreFile(taskId, response);
            return ResponseEntity.ok(CommonResult.ok());
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation("获取文件下载路径")
    @Timed
    @GetMapping("/getDownloadUrl")
    public ResponseEntity<CommonResult> getDownloadUrl(@RequestParam("key")String key) throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, +7);
        String presignatureUrl = cosService.getPresignatureUrl(key, calendar.getTime());
        logger.info("获取下载地址信息：{}",presignatureUrl);
        return ResponseEntity.ok(CommonResult.ok(presignatureUrl));
    }


    @ApiOperation("单独消费数据")
    @Timed
    @GetMapping("/sixLevelTest")
    public ResponseEntity<CommonResult> sixLevelTest(@RequestParam("taskId")Long taskId,@RequestParam("page")int page,@RequestParam("perPage")int perPage) {
         trackRetultFileService.selectTrackRetultLevelNecessaryByPage(taskId, page, perPage);
        return ResponseEntity.ok(CommonResult.ok());
    }

    //intranet（intranet，仅限内网访问、调用的接口，外部ip请求nginx限制 /api/intranet  ，场景：如海典、华南、大数据调用等）
    @ApiOperation("大数据组货任务结果通知")
    @Timed
    @GetMapping("/intranet/bdpGroupingTaskResultNotify")
    public ResponseEntity<CommonResult> bdpGroupingTaskResultNotify(@RequestParam("taskId") Long taskId, @RequestParam("version") Integer version) {
        Assert.notNull(taskId, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        try{
            logger.info("大数据通知组货任务结果");
            BundlingTaskInfo bundlingTaskInfo = bundlingTaskInfoMapper.selectByPrimaryKey(taskId);
            Assert.notNull(bundlingTaskInfo, ErrorCodeEnum.TASK_ID_NOTEXIT.getMsg());
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.getCode().equals(bundlingTaskInfo.getTaskType())){
                bundlTaskWriteService.unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.COMPUTED,new TokenUserDTO());
            }else {
                trackRetultFileService.createFileLevelReviewFile(taskId, version);
                trackRetultFileService.createTrackRetultCompositionReviewFile(taskId, version);
                trackRetultFileService.createTrackRetultEfficiencyAnalyseFile(taskId, version);
                trackRetultFileService.createTrackRetultAllDetailFile(taskId, version);
                trackRetultFileService.createTrackRetultTop4levelStoreGroupFile(taskId, version);
                trackRetultFileService.createTrackRetult2levelSingleFile(taskId, version);
                if (0 == version) {
                    trackRetultFileService.selectTrackRetultLevelNecessary(taskId);
                }
                trackRetultFileService.createGroupGoodsResultsFile(taskId, version);
                bundlTaskWriteService.unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.COMPUTED,new TokenUserDTO());
            }
            return ResponseEntity.ok(CommonResult.ok(0,"成功"));
        }catch (Exception e){
            logger.error("大数据组货任务结果通知报错",e);
            return ResponseEntity.ok(CommonResult.ok(-1,"失败"));
        }
    }
    @ApiOperation("获取大数据组货任务结果生成的文件")
    @Timed
    @GetMapping("/queryLevelNecessaryFile")
    public ResponseEntity<CommonResult> queryLevelNecessaryFile(@RequestParam("taskId") Long taskId, @RequestParam("version") Integer version, HttpServletRequest request) {
        Assert.notNull(taskId, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Map<Integer, List<TrackResultFile>> integerListMap = trackRetultFileService.queryLevelNecessaryFile(taskId, version);
        return ResponseEntity.ok(CommonResult.ok(AmisMap.getAmisMap("resultMap",integerListMap)));
    }
}
