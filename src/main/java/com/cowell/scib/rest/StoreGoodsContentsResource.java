package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.StoreGoodsContentsService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.ScibCommonResultDTO;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * StoreGoodsContents REST Controller
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "门店商品内容接口", description = "门店商品内容管理接口")
public class StoreGoodsContentsResource {

    private final Logger logger = LoggerFactory.getLogger(StoreGoodsContentsResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private StoreGoodsContentsService storeGoodsContentsService;

    /**
     * 获取门店商品内容
     * 
     * @param request HTTP请求
     * @param storeId 门店ID
     * @param manageStatus 经营状态
     * @param goodsNoList 商品编码列表
     * @return 门店商品内容结果
     */
    @Timed
    @ApiOperation("获取门店商品内容")
    @GetMapping({"/storeGoodsContents", "/internal/storeGoodsContents"})
    public ResponseEntity<CommonResult<ScibCommonResultDTO>> getStoreGoodsContents(
            HttpServletRequest request,
            @RequestParam("storeId") Long storeId,
            @RequestParam("manageStatus") Integer manageStatus,
            @RequestParam(value = "goodsNoList", required = false) List<String> goodsNoList) {
        
        ScibCommonResultDTO result = storeGoodsContentsService.getStoreGoodsContents(storeId, manageStatus, goodsNoList);
        
        return ResponseEntity.ok(CommonResult.ok(result));
    }
}
