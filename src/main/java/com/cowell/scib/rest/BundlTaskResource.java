package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.enums.TaskStatusChangeEnum;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.BundlTaskService;
import com.cowell.scib.service.BundlTaskWriteService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.param.*;
import com.cowell.scib.service.vo.amis.AmisMap;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import com.cowell.scib.service.vo.amis.SelectorResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/9 17:34
 */
@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/task", "/api/task"})
@Api(tags = "组货任务管理接口", description = "组货任务管理接口")
public class BundlTaskResource {

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;
    @Autowired
    private BundlTaskWriteService bundlTaskWriteService;
    @Autowired
    private BundlTaskService bundlTaskService;

    @ApiOperation("组货任务列表")
    @Timed
    @PostMapping("/listBundlTask")
    public ResponseEntity<CommonResult<PageResult<BundlTaskListDTO>>> listBundlTask(@RequestBody BundlTaskListParam listParam, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        PageResult<BundlTaskListDTO> bundlTaskListDTOList = bundlTaskService.listBundlTask(listParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(bundlTaskListDTOList));
    }

    @ApiOperation("组货公司列表")
    @Timed
    @GetMapping("/listBundlCompany")
    public ResponseEntity<CommonResult<SelectorResult>> listBundlCompany(@RequestParam(value = "orgId") Long plateOrgId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        SelectorResult selectorResult = bundlTaskService.listBundlCompany(plateOrgId, userDTO);
        return ResponseEntity.ok(CommonResult.ok(selectorResult));
    }

    @ApiOperation("组货任务类型列表")
    @Timed
    @GetMapping("/listBundlType")
    public ResponseEntity<CommonResult<SelectorResult>> listBundlType(@RequestParam(value = "orgId") Long plateOrgId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        SelectorResult selectorResult = bundlTaskService.listBundlType(plateOrgId, userDTO);
        return ResponseEntity.ok(CommonResult.ok(selectorResult));
    }

    @ApiOperation("组货商品类型列表")
    @Timed
    @GetMapping("/listBundlGoods")
    public ResponseEntity<CommonResult<SelectorResult>> listBundlGoods(@RequestParam(value = "orgId") Long plateOrgId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        SelectorResult selectorResult = bundlTaskService.listBundlGoods(plateOrgId, userDTO);
        return ResponseEntity.ok(CommonResult.ok(selectorResult));
    }

    @ApiOperation("搜索条件列表")
    @Timed
    @GetMapping("/listSearch")
    public ResponseEntity<CommonResult<SelectorResult>> listSearch(@RequestParam(value = "type") Integer searchType, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        SelectorResult selectorResult = bundlTaskService.listSearch(searchType, userDTO);
        return ResponseEntity.ok(CommonResult.ok(selectorResult));
    }

    @ApiOperation("确认组货门店范围列表")
    @Timed
    @PostMapping("/listConfirmBundlStore")
    public ResponseEntity<CommonResult<PageResult<BundlStoreConfirmDTO>>> listConfirmBundlStore(@RequestBody BundlStoreConfirmParam confirmParam, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        PageResult<BundlStoreConfirmDTO> storeConfirmDTOPageResult = bundlTaskService.listConfirmBundlStore(confirmParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(storeConfirmDTOPageResult));
    }

    @ApiOperation("查询任务规则")
    @Timed
    @PostMapping("/getTaskRuleList")
    public ResponseEntity<CommonResult> getTaskRuleList(@RequestBody TaskDictParam taskDictParam, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Map<String, BundlTaskDetailDTO> detailDTOAmisMap=bundlTaskService.getTaskRuleList(taskDictParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(AmisMap.getAmisMap("resultMap", detailDTOAmisMap)));
    }

    @ApiOperation("查询任务信息")
    @Timed
    @GetMapping("/getTaskInfo")
    public ResponseEntity<CommonResult<BundlTaskInfoDTO>> getTaskInfo(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        BundlTaskInfoDTO bundlTaskInfoDTO = bundlTaskService.getTaskInfo(taskId, userDTO);
        return ResponseEntity.ok(CommonResult.ok(bundlTaskInfoDTO));
    }

    @ApiOperation(value = "组货商品黑名单查询-编辑场景")
    @PostMapping(value = "/queryTaskGroupBlackGoods")
    @Timed
    public ResponseEntity<CommonResult<PageResult<CommonGoodsDTO>>> queryTaskGroupBlackGoods(@RequestBody TaskDictParam taskDictParam, HttpServletRequest request) {
        Assert.notNull(taskDictParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(taskDictParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        PageResult<CommonGoodsDTO> spuListVoPageResult = bundlTaskService.queryTaskGroupBlackGoods(taskDictParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(spuListVoPageResult));
    }

    @ApiOperation("判断某任务是否有选择器ID")
    @Timed
    @GetMapping("/judgeAndReturnSelectId")
    public ResponseEntity<Long> judgeSelectIdByTask(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Long selectId =bundlTaskService.judgeSelectIdByTask(taskId, userDTO);
        return ResponseEntity.ok(selectId);
    }

    @ApiOperation("选择器参数组装")
    @Timed
    @PostMapping("/buildSelectParam")
    public ResponseEntity<CommonResult> buildSelectParam(@RequestBody RuleParam ruleParam, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Map<String, List<String>> selectorResult = bundlTaskService.buildSelectParam(ruleParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(AmisMap.getAmisMap("resultMap", selectorResult)));
    }

    /**
     * 组货门店明细导出excel
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "组货门店明细导出excel", notes = "组货门店明细导出excel")
    @GetMapping("/exportStores")
    public ResponseEntity<String> exportStores(HttpServletRequest request, HttpServletResponse response, @RequestParam("taskId") Long taskId) throws Exception{
        Assert.notNull(taskId, ErrorCodeEnum.TASK_ID_NOTEXIT.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskService.exportStores(taskId, response, userDTO);
        return new ResponseEntity<>("导出成功", HttpStatus.OK);
    }


    @ApiOperation(value = "查询任务分页结果")
    @PostMapping(value = "/queryTaskResult")
    @Timed
    public ResponseEntity<CommonResult<PageResult<?>>>  queryTaskResult(@RequestBody TaskDictParam taskDictParam, HttpServletRequest request) {
        Assert.notNull(taskDictParam.getTaskId(), ErrorCodeEnum.TASK_ID_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        PageResult<?> spuListVoPageResult = bundlTaskService.queryTaskResult(taskDictParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(spuListVoPageResult));
    }
    /**
     * **********************写操作**************************************
     **/
    @ApiOperation("创建或编辑组货任务")
    @Timed
    @PostMapping("/addOrEditBundlTask")
    public ResponseEntity<Object> addOrEditBundlTask(@RequestBody BundlTaskAddParam bundlTaskAddParam, HttpServletRequest request) {
        log.info("addOrEditBundlTask|bundlTaskAddParam:{}.", bundlTaskAddParam);
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(bundlTaskWriteService.add(bundlTaskAddParam, userDTO));
    }

    @ApiOperation("更新组货门店明细")
    @Timed
    @PostMapping("/updateMdmStore")
    public ResponseEntity addMdmStore(@RequestBody BundlStoreConfirmParam confirmParam, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskWriteService.updateMdmStore(confirmParam, userDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("更新组货门店确认状态")
    @Timed
    @PostMapping("/updateBundlStoreConfirm")
    public ResponseEntity<CommonResult> updateBundlStoreConfirm(@RequestBody BundlUpdateStoreConfirmParam confirmParam, HttpServletRequest request) {
        Assert.notNull(confirmParam.getId(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        Assert.notNull(confirmParam.getTargetBundlConfirmAble(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskWriteService.updateBundlConfirm(confirmParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("提交大数据")
    @Timed
    @GetMapping("/commitTaskBdp")
    public ResponseEntity commitTaskBdp(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        BdpResponseDTO bdpResponseDTO = bundlTaskWriteService.commitTaskToBdp(taskId, userDTO);
        return new ResponseEntity<>(bdpResponseDTO, HttpStatus.OK);
    }

    @ApiOperation("saveTask")
    @Timed
    @GetMapping("/saveTask")
    public ResponseEntity saveTask(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskWriteService.unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.SAVE, userDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("cancelTask")
    @Timed
    @GetMapping("/cancelTask")
    public ResponseEntity<CommonResult> cancelTask(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskWriteService.unifyUpdateStasktatus(taskId, TaskStatusChangeEnum.CANCEL, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("查询任务详情信息")
    @Timed
    @GetMapping("/getTaskDetailInfo")
    public ResponseEntity<CommonResult<BundlTaskInfoDTO>> getTaskDetailInfo(@RequestParam(value = "taskId") Long taskId, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        BundlTaskInfoDTO bundlTaskInfoDTO = bundlTaskService.getTaskDetailInfo(taskId, userDTO);
        return ResponseEntity.ok(CommonResult.ok(bundlTaskInfoDTO));
    }

    @ApiOperation("更新组货任务状态")
    @Timed
    @GetMapping("/updateTaskStatus")
    public ResponseEntity updateTaskStatus(@RequestParam(value = "taskId") Long taskId, @RequestParam(value = "taskStatus") Byte taskStatus, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskWriteService.updateTaskStatus(taskId, taskStatus,userDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("更新组货任务状态通过编码")
    @Timed
    @GetMapping("/updateTaskStatusByCode")
    public ResponseEntity updateTaskStatusByCode(@RequestParam(value = "taskCode") String taskCode, @RequestParam(value = "taskStatus") Byte taskStatus, HttpServletRequest request) {
        tokenAuthenticationManager.getUserInfobyToken(request);
        bundlTaskWriteService.updateTaskStatusByCode(taskCode, taskStatus);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
