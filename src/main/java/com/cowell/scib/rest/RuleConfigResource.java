package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.service.RuleConfigService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.param.rule.RuleDictParam;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:20
 */
@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/ruleConfig", "/api/ruleConfig"})
@Api(tags = "规则配置接口", description = "规则配置接口")
public class RuleConfigResource {

    @Autowired
    private RuleConfigService ruleConfigService;

    @ApiOperation(value = "规则配置添加或保存")
    @PostMapping(value = "/addOrEdit")
    @Timed
    public ResponseEntity<CommonResult> addOrEdit(@RequestBody RuleDictParam ruleDictParam, HttpServletRequest request) {
        ruleConfigService.addOrEdit(ruleDictParam);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "绑定/解绑门店标签")
    @GetMapping(value = "/bindingStoreTag")
    @Timed
    public ResponseEntity<CommonResult> bindingStoreTag(@RequestParam(value = "id") Long id, @RequestParam(value = "tagId", required = false) Long tagId, @RequestParam(value = "type") Integer type, HttpServletRequest request) {
        ruleConfigService.bindingStoreTag(id, tagId, type);
        return ResponseEntity.ok(CommonResult.ok());
    }

}
