package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.AmisBadRequestException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.DevelopModuleReadService;
import com.cowell.scib.service.DevelopModuleWriteService;
import com.cowell.scib.service.IReachModuleService;
import com.cowell.scib.service.dto.ReachModuleQueryParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryCommonDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import com.cowell.scib.service.dto.necessaryContents.ReachModuleDTO;
import com.cowell.scib.service.dto.necessaryContents.RoleDTO;
import com.cowell.scib.service.impl.RoleCacheServiceImpl;
import com.cowell.scib.service.param.DevelopAddParam;
import com.cowell.scib.service.param.DevelopListParam;
import com.cowell.scib.service.param.DevelopRecordAddParam;
import com.cowell.scib.service.param.DevelopSelectParam;
import com.cowell.scib.service.vo.DevelopModuleRecordVO;
import com.cowell.scib.service.vo.DevelopModuleVO;
import com.cowell.scib.service.vo.amis.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/15 14:22
 */
@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/reach", "/api/reach"})
@Api(tags = "触达组管理接口", description = "触达组管理接口")
public class ReachModuleResource {

    @Autowired
    private IReachModuleService reachModuleService;
    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;
    @Autowired
    private RoleCacheServiceImpl roleCacheServiceImpl;

    @ApiOperation(value = "触达组列表查询")
    @PostMapping(value = "/reachList")
    @Timed
    public ResponseEntity<CommonResult<PageResult<ReachModuleDTO>>> getReachModuleList(HttpServletRequest request, @RequestBody ReachModuleQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        log.info("用户={},操作={},param={}", userDTO, "ReachModuleResource.getReachModuleList", JSON.toJSONString(param));
        PageResult<ReachModuleDTO> pageResult = reachModuleService.getReachModuleList(userDTO, param);
        return ResponseEntity.ok(CommonResult.ok(pageResult));
    }

    @ApiOperation(value = "触达组维护")
    @PostMapping(value = "/reachManage")
    @Timed
    public ResponseEntity<CommonResult> reachManage(@RequestBody ReachModuleDTO reachModuleDTO, HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        reachModuleService.reachManage(reachModuleDTO, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "根据角色名称模糊查询")
    @GetMapping(value = "/findRoleByRoleNameLike")
    @Timed
    public ResponseEntity<CommonResponse<RoleDTO>> findRoleByRoleNameLike(HttpServletRequest request, @RequestParam(required = false) String keyword){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        log.info("用户={},操作={},keyword={}", userDTO, "ReachModuleResource.findRoleByRoleNameLike", keyword);
        List<RoleDTO> roleList = roleCacheServiceImpl.findRoleByRoleNameLike(keyword);
        return ResponseEntity.ok(CommonResponse.ok(roleList));
    }
}
