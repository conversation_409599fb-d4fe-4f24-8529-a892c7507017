package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.cowell.scib.entityDgms.JymlCustomizeChooseSku;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.JymlCustomizeChooseSkuService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.customize.CustomizeQueryParam;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "自定义选配商品接口", description = "自定义选配商品接口")
public class JymlCustomizeChooseSkuResource {
    private final Logger logger = LoggerFactory.getLogger(JymlCustomizeChooseSkuResource.class);
    @Resource
    private TokenAuthenticationManager tokenAuthenticationManager;
    @Resource
    private JymlCustomizeChooseSkuService jymlCustomizeChooseSkuService;

    @ApiOperation("获取自定义选配商品列表")
    @PostMapping({"/customize/choose/list"})
    public PageResult<JymlCustomizeChooseSku> getCustomizeChooseSkyList(HttpServletRequest request, @RequestBody CustomizeQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "JymlCustomizeChooseSkuResource.list", userDTO, JSON.toJSONString(param));
        return jymlCustomizeChooseSkuService.getCustomizeChooseSkyList(userDTO,param);
    }

    @ApiOperation("按查询结果删除")
    @PostMapping({"/customize/choose/delete"})
    public CommonResult delete(HttpServletRequest request, @RequestBody CustomizeQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "JymlCustomizeChooseSkuResource.delete", userDTO, JSON.toJSONString(param));
        jymlCustomizeChooseSkuService.delete(userDTO,param);
        return CommonResult.ok("删除成功");
    }

    @ApiOperation("导出")
    @PostMapping({"/customize/choose/export"})
    public CommonResult export(HttpServletRequest request, @RequestBody CustomizeQueryParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "JymlCustomizeChooseSkuResource.delete", userDTO, JSON.toJSONString(param));
        jymlCustomizeChooseSkuService.export(userDTO,param);
        return CommonResult.ok("正在导出,请稍后去下载中心查看");
    }
}
