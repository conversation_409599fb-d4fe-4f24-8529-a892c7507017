package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.service.MinDisplayService;
import com.cowell.scib.service.dto.SeasonalGoodsPushDTO;
import com.cowell.scib.service.dto.SensitiveUpdateParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.necessaryContents.NecessaryQueryParam;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@Slf4j
@RequestMapping("/api")
@Api(tags = "修改MDM最小陈列量", description = "修改MDM最小陈列量")
public class MinDisplayResource {

    @Autowired
    private MinDisplayService minDisplayService;

    @Autowired
    @Qualifier("trackResultFileUploadExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    @ApiOperation(value = "修改MDM最小陈列量")
    @PostMapping("/internal/seasonGoods/updateMinDisplayBySeasonGoods")
    @Timed
    public ResponseEntity<CommonResult> updateMinDisplayBySeasonGoods(@RequestBody SeasonalGoodsPushDTO seasonalGoodsDTO) {
        log.info("接收iscm发送的季节品数据:{}",seasonalGoodsDTO);
        asyncTaskExecutor.submit(() -> {
            minDisplayService.getMinDisplayFromMdm(seasonalGoodsDTO);
        });
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "敏感品修改单店必备")
    @GetMapping({"/intranet/sensitive/updateSingleNecessary", "/sensitive/updateSingleNecessary"})
    @Timed
    public ResponseEntity<CommonResult> updateSingleNecessary(@RequestParam(value = "paramUniqueMark", required = false) String paramUniqueMark, @RequestParam(value =  "storeCodes", required = false)String storeCodes) {
        log.info("bdp回调,开始根据敏感品修改单店必备, paramUniqueMark:{}, storeCodes:{}", paramUniqueMark, storeCodes);
        SensitiveUpdateParam param = new SensitiveUpdateParam();
        if (StringUtils.isNotBlank(paramUniqueMark)) {
            param.setParamUniqueMark(paramUniqueMark);
        }
        if (StringUtils.isNotBlank(storeCodes)) {
            param.setStoreCodes(Lists.newArrayList(StringUtils.split(storeCodes, ",")));
        }
        asyncTaskExecutor.submit(() -> {
            minDisplayService.updateSingleNecessary(param);
        });
        return ResponseEntity.ok(CommonResult.ok());
    }

    @Timed
    @ApiOperation(value = "导出mdmTask")
    @GetMapping("/noauth/sensitive/exportMdmTask")
    public void exportMdmTask(HttpServletRequest request, @RequestParam(value = "taskId") Long taskId, HttpServletResponse response){
        try {
            minDisplayService.exportMdmTask(taskId, response);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }


}
