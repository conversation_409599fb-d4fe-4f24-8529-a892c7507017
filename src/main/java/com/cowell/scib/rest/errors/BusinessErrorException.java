package com.cowell.scib.rest.errors;

import com.cowell.scib.enums.ErrorCodeEnum;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

public class BusinessErrorException extends AbstractThrowableProblem {

    private final String errorCode;

    private final String errorMessage;

    public BusinessErrorException(ErrorCodeEnum errorCodeEnum) {
        this(ErrorConstants.DEFAULT_TYPE, errorCodeEnum.getCode().toString(), errorCodeEnum.getMsg());
    }
    public BusinessErrorException(String errorCode, String errorMessage) {
        this(ErrorConstants.DEFAULT_TYPE, errorCode, errorMessage);
    }

    public BusinessErrorException(String errorMessage) {
        this(ErrorConstants.DEFAULT_TYPE, "400", errorMessage);
    }

    public BusinessErrorException(URI type, String errorCode, String errorMessage) {
        super(type, errorMessage, Status.BAD_REQUEST, null, null, null, getErrorParameters(errorCode, errorMessage));
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

	public String getErrorCode() {
		return errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	private static Map<String, Object> getErrorParameters(String errorCode, String errorMessage) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("errorCode", errorCode);
        parameters.put("errorMessage", errorMessage);
        return parameters;
    }

}
