package com.cowell.scib.rest.errors;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义统一返回异常
 */
public class FailRequestAlertException extends AbstractThrowableProblem {

    private final String errorCode;

    private final String errorMessage;


    public FailRequestAlertException(URI type, String errorCode, String errorMessage) {
        super(type, errorMessage, Status.BAD_REQUEST, null, null, null, getErrorParameters(errorCode, errorMessage));
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     *
     * @param errorCode  错误代码
     * @param errorMessage  错误信息
     */
    public FailRequestAlertException( String errorCode, String errorMessage) {
        this(ErrorConstants.DEFAULT_TYPE, errorCode,errorMessage);
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    private static Map<String, Object> getErrorParameters(String errorCode, String errorMessage) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("errorCode", errorCode);
        parameters.put("errorMessage", errorMessage);
        return parameters;
    }
}
