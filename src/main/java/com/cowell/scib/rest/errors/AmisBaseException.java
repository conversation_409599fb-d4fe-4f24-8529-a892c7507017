package com.cowell.scib.rest.errors;

import com.cowell.scib.enums.ErrorCodeEnum;
import org.zalando.problem.AbstractThrowableProblem;

import java.util.Optional;

/**
 * @program: pricecenter
 * @description: Amis框架基础异常
 * @author: jmlu
 * @create: 2022-04-01 15:13
 **/

public class AmisBaseException extends AbstractThrowableProblem {

    private static final long serialVersionUID = 1516461082293136750L;

    private final Integer code;

    private final String message;

    private final Optional<Exception> exOptional;

    public AmisBaseException(ErrorCodeEnum returnCodeEnum, Exception ex, String message) {
        this.code = returnCodeEnum.getCode();
        this.message = message != null ? message : returnCodeEnum.getMsg();
        this.exOptional = ex == null ? Optional.empty() : Optional.of(ex);
    }

    public AmisBaseException(ErrorCodeEnum returnCodeEnum, String message) {
        this(returnCodeEnum, null, message);
    }

    public AmisBaseException(ErrorCodeEnum returnCodeEnum, Exception ex) {
        this(returnCodeEnum, ex, null);
    }

    public AmisBaseException(ErrorCodeEnum returnCodeEnum) {
        this(returnCodeEnum, null, null);
    }

    public AmisBaseException() {
        this(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
    }

    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Optional<Exception> getExOptional() {
        return exOptional;
    }
}
