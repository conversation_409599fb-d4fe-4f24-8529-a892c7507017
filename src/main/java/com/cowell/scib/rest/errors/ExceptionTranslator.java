package com.cowell.scib.rest.errors;

import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.utils.HeaderUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.zalando.problem.*;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.validation.ConstraintViolationProblem;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller advice to translate the server side exceptions to client-friendly json structures.
 * The error response follows RFC7807 - Problem Details for HTTP APIs (https://tools.ietf.org/html/rfc7807)
 */
@ControllerAdvice
public class ExceptionTranslator implements ProblemHandling {

    /**
     * Post-process Problem payload to add the message key for front-end if needed
     */
    @Override
    public ResponseEntity<Problem> process(@Nullable ResponseEntity<Problem> entity, NativeWebRequest request) {
        if (entity == null || entity.getBody() == null) {
            return entity;
        }
        Problem problem = entity.getBody();
        if (!(problem instanceof ConstraintViolationProblem || problem instanceof DefaultProblem)) {
            return entity;
        }
        ProblemBuilder builder = Problem.builder()
            .withType(Problem.DEFAULT_TYPE.equals(problem.getType()) ? ErrorConstants.DEFAULT_TYPE : problem.getType())
            .withStatus(problem.getStatus())
            .withTitle(problem.getTitle())
            .with("path", request.getNativeRequest(HttpServletRequest.class).getRequestURI());

        if (problem instanceof ConstraintViolationProblem) {
            builder
                .with("violations", ((ConstraintViolationProblem) problem).getViolations())
                .with("message", ErrorConstants.ERR_VALIDATION);
            return new ResponseEntity<>(builder.build(), entity.getHeaders(), entity.getStatusCode());
        } else {
            builder
                .withCause(((DefaultProblem) problem).getCause())
                .withDetail(problem.getDetail())
                .withInstance(problem.getInstance());
            problem.getParameters().forEach(builder::with);
            if (!problem.getParameters().containsKey("message") && problem.getStatus() != null) {
                builder.with("message", "error.http." + problem.getStatus().getStatusCode());
            }
            return new ResponseEntity<>(builder.build(), entity.getHeaders(), entity.getStatusCode());
        }
    }

    @Override
    public ResponseEntity<Problem> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, @Nonnull NativeWebRequest request) {
        BindingResult result = ex.getBindingResult();
        List<FieldErrorVM> fieldErrors = result.getFieldErrors().stream()
            .map(f -> new FieldErrorVM(f.getObjectName(), f.getField(), StringUtils.isBlank(f.getDefaultMessage()) ?  f.getCode() : f.getDefaultMessage()))
            .collect(Collectors.toList());

        StringBuilder builder = new StringBuilder();
        if(fieldErrors.size() > 0){
            for(FieldErrorVM vm : fieldErrors){
                builder.append(vm.getMessage()).append(",");
            }
        }
        String errorStr = builder.toString().substring(0,builder.toString().lastIndexOf(","));
        Problem problem = Problem.builder()
            .withType(ErrorConstants.CONSTRAINT_VIOLATION_TYPE)
            .withTitle("Method argument not valid")
            .withStatus(defaultConstraintViolationStatus())
            .with("message", ErrorConstants.ERR_VALIDATION)
            .with("fieldErrors", errorStr)
            .build();
        HttpHeaders httpHeaders = HeaderUtil.createFailRequestFailureAlert(ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getCode().toString(), errorStr);
        return create(ex, problem, request, httpHeaders);
    }

    @ExceptionHandler(AmisBadRequestException.class)
    public ResponseEntity<CommonResult> handleAmisBadRequestAlertException(AmisBadRequestException ex, NativeWebRequest request) {
        return ResponseEntity.ok(CommonResult.error(ex.getCode(), ex.getMessage()));
    }

    @ExceptionHandler(AmisBusinessException.class)
    public ResponseEntity<CommonResult> handleAmisBusinessException(AmisBusinessException ex, NativeWebRequest request) {
        return ResponseEntity.ok(CommonResult.error(ex.getCode(), ex.getMessage()));
    }

    @ExceptionHandler(BadRequestAlertException.class)
    public ResponseEntity<Problem> handleBadRequestAlertException(BadRequestAlertException ex, NativeWebRequest request) {
        return create(ex, request, HeaderUtil.createFailureAlert(ex.getEntityName(), ex.getErrorKey(), ex.getMessage()));
    }
    @ExceptionHandler(FailRequestAlertException.class)
    public ResponseEntity<Problem> handleFailRequestAlertException(FailRequestAlertException ex, NativeWebRequest request) {
        return create(ex, request, HeaderUtil.createFailRequestFailureAlert(ex.getErrorCode(), ex.getErrorMessage()));
    }
    @ExceptionHandler(BusinessErrorException.class)
    public ResponseEntity<Problem> handleBadRequestAlertException(BusinessErrorException ex, NativeWebRequest request) {
        return create(ex, request, HeaderUtil.createBusinessFailureAlert(ex.getErrorCode(), ex.getErrorMessage()));
    }
    @ExceptionHandler(ConcurrencyFailureException.class)
    public ResponseEntity<Problem> handleConcurrencyFailure(ConcurrencyFailureException ex, NativeWebRequest request) {
        Problem problem = Problem.builder()
            .withStatus(Status.BAD_REQUEST)
            .with("message", ErrorConstants.ERR_CONCURRENCY_FAILURE)
            .build();
        return create(ex, problem, request);
    }
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Problem> handleUnexpectedException(NativeWebRequest request) {
        ThrowableProblem problem = Problem.builder()
            .withStatus(Status.BAD_REQUEST)
            .with("message", ErrorConstants.ERR_CONCURRENCY_FAILURE)
            .build();
        return create(problem, request, HeaderUtil.createFailRequestFailureAlert(ErrorCodeEnum.SYSTEM_ERROR.getCode().toString(), ErrorCodeEnum.SYSTEM_ERROR.getMsg()));
    }
}
