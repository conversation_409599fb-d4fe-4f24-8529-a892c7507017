package com.cowell.scib.rest.errors;

import com.cowell.scib.enums.ErrorCodeEnum;

/**
 * @program: pricecenter
 * @description: 提供Amis框架参数异常类
 * @author: jmlu
 * @create: 2022-03-23 09:44
 **/

public class AmisBadRequestException extends AmisBaseException  {

    public AmisBadRequestException(ErrorCodeEnum returnCodeEnum, Exception ex, String message) {
        super(returnCodeEnum, ex, message);
    }

    public AmisBadRequestException(ErrorCodeEnum returnCodeEnum, String message) {
        super(returnCodeEnum, message);
    }

    public AmisBadRequestException(ErrorCodeEnum returnCodeEnum) {
        super(returnCodeEnum);
    }

    public AmisBadRequestException(ErrorCodeEnum returnCodeEnum, Exception ex) {
        super(returnCodeEnum, ex);
    }

    public AmisBadRequestException() {
        super(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
    }

    public AmisBadRequestException(String message) {
        super(ErrorCodeEnum.PARAM_ERROR_EXCEPTION, message);
    }

}
