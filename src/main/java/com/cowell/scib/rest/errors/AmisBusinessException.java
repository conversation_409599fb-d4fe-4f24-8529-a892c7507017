package com.cowell.scib.rest.errors;

import com.cowell.scib.enums.ErrorCodeEnum;

/**
 * @program: pricecenter
 * @description: Amis框架业务异常类
 * @author: jmlu
 * @create: 2022-04-01 15:16
 **/

public class AmisBusinessException extends AmisBaseException {

    public AmisBusinessException(ErrorCodeEnum returnCodeEnum, Exception ex, String message) {
        super(returnCodeEnum, ex, message);
    }

    public AmisBusinessException(ErrorCodeEnum returnCodeEnum, Exception ex) {
        super(returnCodeEnum, ex);
    }

    public AmisBusinessException(ErrorCodeEnum returnCodeEnum, String message) {
        super(returnCodeEnum, message);
    }

    public AmisBusinessException(ErrorCodeEnum returnCodeEnum) {
        super(returnCodeEnum);
    }

    public AmisBusinessException() {
        super(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
    }

}
