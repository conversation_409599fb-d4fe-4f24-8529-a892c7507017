package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.constant.Constants;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.rest.errors.BusinessErrorException;
import com.cowell.scib.rest.errors.FailRequestAlertException;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.RuleService;
import com.cowell.scib.service.dto.*;
import com.cowell.scib.service.dto.rule.*;
import com.cowell.scib.service.param.RuleParam;
import com.cowell.scib.service.param.rule.RuleAddParam;
import com.cowell.scib.service.vo.amis.AmisMap;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.OptionDto;
import com.cowell.scib.service.vo.amis.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:20
 */
@RestController
@Slf4j
@RequestMapping(value = {"/api/internal/rule", "/api/rule"})
@Api(tags = "规则接口", description = "规则接口")
public class RuleResource {

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;
    @Autowired
    private RuleService ruleService;

    @ApiOperation("查询规则")
    @Timed
    @PostMapping("/getRuleList")
    public ResponseEntity<CommonResult> getRuleList(@RequestBody RuleParam ruleParam, HttpServletRequest request) {
        Assert.notNull(ruleParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(ruleParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = null;
        try {
            userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        } catch (Exception e) {
            log.info("获取userDTO为空获取userDTO为空", e);
        }
        Map<String, RuleDetailDTO> detailDTOAmisMap=ruleService.getRuleList(ruleParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(AmisMap.getAmisMap("resultMap", detailDTOAmisMap)));
    }

    @ApiOperation("查询规则枚举")
    @Timed
    @PostMapping("/getRuleEnumList")
    public ResponseEntity<CommonResult> getRuleEnumList(@RequestBody RuleParam ruleParam, HttpServletRequest request) {
        Assert.notNull(ruleParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Map<String, List<OptionDto>> optionDtoMap=ruleService.getRuleEnumList(ruleParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(AmisMap.getAmisMap("resultMap", optionDtoMap)));
    }

    @ApiOperation(value = "初始化规则")
    @GetMapping(value = "/initConfigRuleInfo")
    @Timed
    public ResponseEntity<CommonResult> initConfigRuleInfo(@RequestParam("userId") Long userId, @RequestParam("orgId") Long orgId, @RequestParam("version") Integer version, HttpServletRequest request) {
        Assert.notNull(orgId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(userId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(userId);
        ruleService.initConfigRuleInfo(orgId, userDTO, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "初始化批量规则")
    @GetMapping(value = "/initBatchConfigRuleInfo")
    @Timed
    public ResponseEntity<CommonResult> initBatchConfigRuleInfo(@RequestParam("userId") Long userId, @RequestParam("orgIdList") List<Long> orgIdList, @RequestParam("version") Integer version, HttpServletRequest request) {
        Assert.notNull(orgIdList, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(userId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(userId);
        orgIdList.forEach(orgId->{
            ruleService.initConfigRuleInfo(orgId, userDTO, version);
        });
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "删除规则")
    @GetMapping(value = "/delConfigRuleInfo")
    @Timed
    public ResponseEntity<CommonResult> delConfigRuleInfo(@RequestParam("userId") Long userId, @RequestParam("orgId") Long orgId, @RequestParam("version") Integer version, HttpServletRequest request) {
        Assert.notNull(orgId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(userId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(userId);
        ruleService.delConfigRuleInfo(orgId, userDTO, version);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "规则添加或保存")
    @PostMapping(value = "/add")
    @Timed
    public ResponseEntity<CommonResult> add(@RequestBody RuleAddParam ruleAddParam, HttpServletRequest request) {
        Assert.notNull(ruleAddParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getConfigType(), ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getDetailMap(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ruleService.add(ruleAddParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "规则修改")
    @PostMapping(value = "/update")
    @Timed
    public ResponseEntity<CommonResult> updateRule(@RequestBody RuleAddParam ruleAddParam, HttpServletRequest request) {
        Assert.notNull(ruleAddParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getConfigType(), ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getDetailMap(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ruleService.updateRule(ruleAddParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "组货商品黑名单查询添加")
    @PostMapping(value = "/addGroupBlackGoods")
    @Timed
    public ResponseEntity<CommonResult> addGroupBlackGoods(@RequestBody RuleAddParam ruleAddParam, HttpServletRequest request) {
        Assert.notNull(ruleAddParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getConfigType(), ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getGoodsNoList(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ruleService.addGroupBlackGoods(ruleAddParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }


    @ApiOperation(value = "组货商品黑名单查询")
    @PostMapping(value = "/queryGroupBlackGoods")
    @Timed
    public ResponseEntity<CommonResult<PageResult<CommonGoodsDTO>>> queryGroupBlackGoods(@RequestBody RuleParam ruleParam, HttpServletRequest request) {
        Assert.notNull(ruleParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(ruleParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleParam.getConfigType(), ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        PageResult<CommonGoodsDTO> spuListVoPageResult = ruleService.queryGroupBlackGoods(ruleParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok(spuListVoPageResult));
    }

    /**
     * 规则商品导入excel
     * @param request
     * @param file
     * @return
     */
    @Timed
    @ApiOperation(value = "规则商品导入excel", notes = "规则商品导入excel")
    @PostMapping("/importGoods")
    public ResponseEntity<List<ErrorGoodsDTO>> importGoods(HttpServletRequest request,
                                                      @RequestParam("file") MultipartFile file,
                                                      @RequestParam("orgId") Long orgId,
                                                      @RequestParam("scopeCode") String scopeCode,
                                                      @RequestParam("configType") String configType,
                                                      @RequestParam(value = "goodsDictCode", required = false) String dictCode) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Optional.ofNullable(file).orElseThrow(() -> new BusinessErrorException("导入文件不能为空"));
        // 兼容老接口  如果dictCode为空 默认组货商品黑名单
        if (StringUtils.isEmpty(dictCode)) {
            dictCode = Constants.GOODS_BLACK_LIST;
        }
        List<ErrorGoodsDTO> result = ruleService.importGoods(file, orgId, scopeCode, configType, dictCode, userDTO);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 规则商品导出excel
     * @param request
     * @return
     */
    @Timed
    @ApiOperation(value = "规则商品导出excel", notes = "规则商品导出excel")
    @GetMapping("/exportGoods")
    public ResponseEntity<String> exportGoods(HttpServletRequest request, HttpServletResponse response,
                                              @RequestParam("orgId") Long orgId,
                                              @RequestParam("dictCode") String dictCode,
                                              @RequestParam("configType") String configType) throws Exception {
        Assert.notNull(orgId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(dictCode, "字典编码不能为空");
        Assert.notNull(configType, ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ruleService.exportGoods(orgId, dictCode, configType, response, userDTO);
        return new ResponseEntity<>("导出成功", HttpStatus.OK);
    }

    /**
     * 线上热销商品导入excel
     * @param request
     * @param file
     * @return
     */
    @Timed
    @ApiOperation(value = "线上热销商品导入excel", notes = "线上热销商品导入excel")
    @PostMapping("/importHotGoods")
    public ResponseEntity<CommonResult<HotGoodsResponseDTO>> importHotGoods(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
//        TokenUserDTO userDTO = new TokenUserDTO();
        HotGoodsResponseDTO resulMessage = ruleService.importHotGoods(file, userDTO);
        return ResponseEntity.ok(CommonResult.ok(resulMessage));
    }

    @ApiOperation(value = "批量删除热销品")
    @PostMapping(value = "/batchDelHotGoods")
    @Timed
    public ResponseEntity<CommonResult> batchDelHotGoods(@RequestBody HotGoodsImportDTO hotGoodsImportDTO, HttpServletRequest request) {
        ruleService.batchDelHotGoods(hotGoodsImportDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation(value = "删除商品黑名单")
    @PostMapping(value = "/delGroupBlackGoods")
    @Timed
    public ResponseEntity<CommonResult> delGroupBlackGoods(@RequestBody RuleAddParam ruleAddParam, HttpServletRequest request) {
        Assert.notNull(ruleAddParam.getOrgId(), ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getScopeCode(), ErrorCodeEnum.RULE_TYPE_SCOPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getConfigType(), ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        Assert.notNull(ruleAddParam.getGoodsNoList(), ErrorCodeEnum.PARAM_ERROR_EXCEPTION.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ruleService.delGroupBlackGoods(ruleAddParam, userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    /**
     * 门店无法经营的商品类型导入excel
     * @param request
     * @param file
     * @return
     */
    @Timed
    @ApiOperation(value = "门店无法经营的商品类型导入excel", notes = "门店无法经营的商品类型导入excel")
    @PostMapping("/importUnmanageGoods")
    public ResponseEntity<List<ErrorUnmanageDTO>> importUnmanageGoods(HttpServletRequest request,
                                                                      @RequestParam("file") MultipartFile file,
                                                                      @RequestParam("orgId") Long orgId,
                                                                      @RequestParam("scopeCode") String scopeCode,
                                                                      @RequestParam("configType") String configType) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Optional.ofNullable(file).orElseThrow(() -> new BusinessErrorException("导入文件不能为空"));
        List<ErrorUnmanageDTO> result = ruleService.importUnmanageGoods(file, orgId, scopeCode, configType, userDTO);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation(value = "推送参数至bdp")
    @GetMapping(value = "/pushConfigToBdp")
    @Timed
    public ResponseEntity<BdpResponseDTO> pushConfigToBdp(@RequestParam(value = "orgId") Long orgId, @RequestParam(value = "taskId") Long taskId, @RequestParam(value = "month") Integer month, @RequestParam(value = "configType") Byte configType, @RequestParam(value = "dataVersion") String dataVersion) {
        Assert.notNull(orgId, ErrorCodeEnum.RULE_ORG_NULL_ERROR.getMsg());
        Assert.notNull(configType, ErrorCodeEnum.RULE_TYPE_NULL_ERROR.getMsg());
        Assert.notNull(dataVersion, ErrorCodeEnum.PARAM_ERROR.getMsg());
        Assert.notNull(taskId, ErrorCodeEnum.PARAM_ERROR.getMsg());
        try {
            return ResponseEntity.ok(ruleService.pushConfigToBdp(orgId, taskId, month, configType, dataVersion));
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

}
