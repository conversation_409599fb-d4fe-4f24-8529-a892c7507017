package com.cowell.scib.rest;

import com.alibaba.fastjson.JSON;
import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.CommonService;
import com.cowell.scib.service.ImportDataFactory;
import com.cowell.scib.service.NecessaryContentsV2Service;
import com.cowell.scib.service.dto.DropBoxParam;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.rule.ScibSelectorResultDTO;
import com.cowell.scib.service.vo.amis.CommonResponse;
import com.cowell.scib.service.vo.amis.CommonResult;
import com.cowell.scib.service.vo.amis.OptionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@RequestMapping(value = "/api")
@Api(tags = "通用接口", description = "通用接口")
public class CommonResource {


    private final Logger logger = LoggerFactory.getLogger(CommonResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private ImportDataFactory importDataFactory;

    @Autowired
    private CommonService commonService;
    @Autowired
    private NecessaryContentsV2Service necessaryContentsV2Service;

    /**
     * 新增-导入商品
     * @param request
     * @return
     */
    @Timed
    @ApiOperation("新增-导入数据")
    @PostMapping("/common/importData")
    public ResponseEntity importData(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("Type") String type){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "NecessaryContentsResource.importData",userDTO.getName(),type);
        importDataFactory.getPolicy(type,file).importData(file,userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }
    @ApiOperation("通用下拉框")
    @PostMapping("/common/dropBox")
    public ResponseEntity<CommonResponse<OptionDto>> dropBox(HttpServletRequest request, @RequestBody DropBoxParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", "CommonResource.dropBox", JSON.toJSONString(param));
        return ResponseEntity.ok(CommonResponse.ok(commonService.dropBox(param, userDTO)));
    }
    @ApiOperation("bdp回调")
    @PostMapping({"/intranet/common/bdpCallback","/internal/common/bdpCallback"})
    public ResponseEntity bdpCallback(HttpServletRequest request){
        logger.info("操作={}", "CommonResource.bdpCallback");
        necessaryContentsV2Service.bdpTaskCallback();
        return ResponseEntity.ok("回调成功");
    }
    @ApiOperation("bdp回调-按门店")
    @PostMapping({"/intranet/common/bdpCallbackByStore","/internal/common/bdpCallbackByStore"})
    public ResponseEntity bdpCallbackByStore(HttpServletRequest request, @RequestParam(value = "storeCode") String storeCode, @RequestParam(value = "bizType") Integer bizType){
        logger.info("操作={}", "CommonResource.bdpCallback");
        necessaryContentsV2Service.bdpTaskCallback(storeCode, bizType);
        return ResponseEntity.ok("回调成功");
    }
    @Timed
    @ApiOperation("根据机构和配置参数去获取门店id列表")
    @PostMapping("/internal/common/getStoreIdListByStoreSelectorAndConfig")
    public ResponseEntity getStoreIdListByStoreSelectorAndConfig(HttpServletRequest request,  @RequestParam("orgId") Long orgId, @RequestParam("scopeCode") String scopeCode, @RequestParam("configType") String configType){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},orgId={} scopeCode={} configType={} ",userDTO.getName(),orgId,scopeCode,configType);
        ScibSelectorResultDTO storeIdListByStoreSelectorAndConfig = commonService.getStoreIdListByStoreSelectorAndConfig(orgId, scopeCode, configType);
        return ResponseEntity.ok(CommonResult.ok(storeIdListByStoreSelectorAndConfig));
    }

}
