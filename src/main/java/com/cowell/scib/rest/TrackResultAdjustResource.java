package com.cowell.scib.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.scib.constant.RedisConstant;
import com.cowell.scib.entityDgms.BundlingTaskInfo;
import com.cowell.scib.enums.ErrorCodeEnum;
import com.cowell.scib.mapperDgms.BundlingTaskInfoMapper;
import com.cowell.scib.mapperDgms.extend.BundlingTaskInfoExtendMapper;
import com.cowell.scib.security.oauth2.TokenAuthenticationManager;
import com.cowell.scib.service.TrackResultAdjustService;
import com.cowell.scib.service.dto.TokenUserDTO;
import com.cowell.scib.service.dto.TrackResult.ImportResult;
import com.cowell.scib.service.vo.amis.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
@Api(tags = "组货任务结果调整", description = "组货任务结果调整")
public class TrackResultAdjustResource {

    private final Logger logger = LoggerFactory.getLogger(TrackResultAdjustResource.class);

    @Autowired
    private TrackResultAdjustService trackResultAdjustService;

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private BundlingTaskInfoMapper bundlingTaskInfoMapper;

    @Resource
    private RedissonClient redissonClient;

    @ApiOperation("导入店型级必备删除明细")
    @Timed
    @PostMapping("/importStoreTypeDelGoods")
    public ResponseEntity<ImportResult> importStoreTypeDelGoods(MultipartFile file,Long taskId, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importStoreTypeDelGoods(file, taskId, userDTO);
        return ResponseEntity.ok().body(importResult);
    }


    @ApiOperation("导入单店级必备删除明细")
    @Timed
    @PostMapping("/importSingleStoreDelGoods")
    public ResponseEntity<ImportResult> importSingleStoreDelGoods(MultipartFile file, @RequestParam("taskId") Long taskId, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importSingleStoreDelGoods(file, taskId, userDTO);
        return ResponseEntity.ok().body(importResult);
    }


    @ApiOperation("导入平台必备明细")
    @Timed
    @PostMapping("/importPlatformGoods")
    public ResponseEntity<ImportResult> importPlatformGoods(@RequestParam(value = "file") MultipartFile file, @RequestParam("taskId") Long taskId, @RequestParam("taskType") Byte taskType, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importPlatformGoods(file, taskId, taskType, userDTO);
        return ResponseEntity.ok().body(importResult);
    }

    @ApiOperation("导入企业必备明细")
    @Timed
    @PostMapping("/importCompanyGoods")
    public ResponseEntity<ImportResult> importCompanyGoods(@RequestParam(value = "file") MultipartFile file, @RequestParam("taskId") Long taskId, @RequestParam("taskType") Byte taskType, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importCompanyGoods(file, taskId, taskType, userDTO);
        return ResponseEntity.ok().body(importResult);
    }

    @ApiOperation("导入店型必备明细")
    @Timed
    @PostMapping("/importStoreTypeGoods")
    public ResponseEntity<ImportResult> importStoreTypeGoods(@RequestParam(value = "file") MultipartFile file, @RequestParam("taskId") Long taskId, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importStoreTypeGoods(file, taskId, userDTO);
        return ResponseEntity.ok().body(importResult);
    }

    @ApiOperation("导入单店必备明细")
    @Timed
    @PostMapping("/importSingleStoreGoods")
    public ResponseEntity<ImportResult> importSingleStoreGoods(@RequestParam(value = "file") MultipartFile file, @RequestParam("taskId") Long taskId, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importSingleStoreGoods(file, taskId, userDTO);
        return ResponseEntity.ok().body(importResult);
    }

    @ApiOperation("导入新店")
    @Timed
    @PostMapping("/importAdjustNewStoreSuggest")
    public ResponseEntity<ImportResult> importAdjustNewStoreSuggest(@RequestParam(value = "file") MultipartFile file, @RequestParam("taskId") Long taskId, @RequestParam("taskType") Byte taskType, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        ImportResult importResult = trackResultAdjustService.importAdjustNewStoreSuggest(file, taskId, taskType, userDTO);
        return ResponseEntity.ok().body(importResult);
    }

    @ApiOperation("查看导入结果")
    @Timed
    @GetMapping("/getImportResult")
    public ResponseEntity<ImportResult> getImportResult(@RequestParam("key") String key,HttpServletRequest request) {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        RBucket<ImportResult> rBucket = redissonClient.getBucket(key);
        ImportResult dtoList = rBucket.get();
        return ResponseEntity.ok().body(dtoList);
    }

    @ApiOperation("更新MDM数据")
    @Timed
    @PostMapping("/updateMDMData")
    public ResponseEntity<CommonResult> updateMDMData( @RequestParam("taskId") Long taskId, @RequestParam("taskType") Byte taskType, @RequestParam("version") Integer version, HttpServletRequest request) throws Exception {
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        trackResultAdjustService.updateMDMData(taskId, taskType, version,userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("上传是否可操作权限")
    @Timed
    @GetMapping("/uploadOperationalPermission")
    public ResponseEntity<Boolean> uploadOperationalPermission( @RequestParam("taskId") Long taskId, @RequestParam("version") Integer version, HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        Boolean aBoolean = trackResultAdjustService.uploadOperationalPermission(taskId, version, userDTO);
        return ResponseEntity.ok().body(aBoolean);
    }

    @ApiOperation("删除数据")
    @Timed
    @GetMapping("/deleteFiveData")
    public ResponseEntity<String> deleteFiveData( @RequestParam("taskId") Long taskId,@RequestParam("type") Integer type, HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        trackResultAdjustService.deleteFiveData(taskId,type);
        return ResponseEntity.ok().body("成功");
    }

    @ApiOperation("下发一店一目")
    @Timed
    @GetMapping("/updateOneStoreOneData")
    public ResponseEntity<CommonResult> updateOneStoreOneData( @RequestParam("taskId") Long taskId, @RequestParam("orgNo") String orgNo,HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        trackResultAdjustService.updateOneStoreOneData(taskId,orgNo,userDTO);
        return ResponseEntity.ok(CommonResult.ok());
    }

    @ApiOperation("获取redisKey")
    @Timed
    @GetMapping("/getRedisKeyByTaskId")
    public ResponseEntity<String> getRedisKeyByTaskId( @RequestParam("taskId") Long taskId, HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        String redisKeyByTaskId = trackResultAdjustService.getRedisKeyByTaskId(taskId, userDTO);
        return ResponseEntity.ok().body(redisKeyByTaskId);
    }
    @ApiOperation("更新RedisKey")
    @Timed
    @GetMapping("/updateRedisKeyByTaskId")
    public ResponseEntity<String> updateRedisKeyByTaskId( @RequestParam("taskId") Long taskId, HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        String redisKeyByTaskId = trackResultAdjustService.updateRedisKeyByTaskId(taskId, userDTO);
        return ResponseEntity.ok().body(redisKeyByTaskId);
    }

    @ApiOperation("更新RedisKeyLong")
    @Timed
    @GetMapping("/updateRedisKeyLongByTaskId")
    public ResponseEntity<Long> updateRedisKeyLongByTaskId( @RequestParam("taskId") Long taskId, HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        String finishedKey = RedisConstant.NECESSARY_ADD_STORE_GOODS_CACHE_KEY +taskId;
        RAtomicLong atomicLong = redissonClient.getAtomicLong(finishedKey);
        atomicLong.set(0L);
        return ResponseEntity.ok().body(atomicLong.get());
    }

    @ApiOperation("数据清理")
    @Timed
    @GetMapping("/cleanGroupData")
    public ResponseEntity<CommonResult> cleanGroupData(@RequestParam("date") String date, HttpServletRequest request){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        trackResultAdjustService.cleanGroupData(date);
        return ResponseEntity.ok().body(CommonResult.ok());
    }

    @ApiOperation("调整结果,提交大数据复盘")
    @Timed
    @GetMapping("/adjustBdpTrackResult")
    public ResponseEntity<CommonResult> adjustBdpTrackResult(@RequestParam("taskId") Long taskId, @RequestParam("version") Integer version, HttpServletRequest request) {
        Assert.notNull(taskId, ErrorCodeEnum.INVALID_TOKEN.getMsg());
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        return ResponseEntity.ok(CommonResult.ok(trackResultAdjustService.adjustBdpTrackResult(taskId, version)));
    }

}
