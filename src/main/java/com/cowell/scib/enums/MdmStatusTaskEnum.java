package com.cowell.scib.enums;

/**
 * mdm 任务状态 1更新中 2 失败 3 成功
 */
public enum MdmStatusTaskEnum {
    CREATEING((byte)0, "任务创建中"),
    UPDATING((byte)1, "更新中"),
    FAIL((byte)2, "失败"),
    SUCCESS((byte)3, "成功"),
    ;
    private byte code;
    private String message;

    MdmStatusTaskEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (MdmStatusTaskEnum mdmStatusTaskEnum : MdmStatusTaskEnum.values()) {
            if (mdmStatusTaskEnum.getCode() == code) {
                return mdmStatusTaskEnum.getMessage();
            }
        }
        return "";
    }
    public static MdmStatusTaskEnum getEnumByCode(Byte code) {
        if(code == null){
            return null;
        }
        for (MdmStatusTaskEnum necessaryGoodsLevelEnum : MdmStatusTaskEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code.byteValue()) {
                return necessaryGoodsLevelEnum;
            }
        }
        return null;
    }
}
