package com.cowell.scib.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/24 14:34
 */
public enum TaskStatusChangeEnum {
    SAVE(BundlTaskStatusEnum.SAVE.getCode(), Lists.newArrayList(BundlTaskStatusEnum.CREATING.getCode())),
    COMPUTING(BundlTaskStatusEnum.COMPUTING.getCode(), Lists.newArrayList(BundlTaskStatusEnum.CREATING.getCode(), BundlTaskStatusEnum.SAVE.getCode())),
    CANCEL(BundlTaskStatusEnum.CANCEL.getCode(), Lists.newArrayList(BundlTaskStatusEnum.COMPUTED.getCode(), BundlTaskStatusEnum.SAVE.getCode())),
    CANCEL_XD_RECOMMEND(BundlTaskStatusEnum.CANCEL.getCode(), Lists.newArrayList(BundlTaskStatusEnum.COMPUTED.getCode(), BundlTaskStatusEnum.SAVE.getCode(),BundlTaskStatusEnum.UPDATED.getCode())),
    COMMIT_FAIL(BundlTaskStatusEnum.SAVE.getCode(), Lists.newArrayList(BundlTaskStatusEnum.SAVE.getCode())),
    COMPUTED(BundlTaskStatusEnum.COMPUTED.getCode(), Lists.newArrayList(BundlTaskStatusEnum.COMPUTING.getCode())),
    UPDATED(BundlTaskStatusEnum.UPDATED.getCode(), Lists.newArrayList(BundlTaskStatusEnum.COMPUTED.getCode()));

    private Byte targetStatus;
    private List<Byte> conditionStatusList;

    TaskStatusChangeEnum(Byte targetStatus, List<Byte> conditionStatusList) {
        this.targetStatus = targetStatus;
        this.conditionStatusList = conditionStatusList;
    }

    public Byte getTargetStatus() {
        return targetStatus;
    }

    public void setTargetStatus(Byte targetStatus) {
        this.targetStatus = targetStatus;
    }

    public List<Byte> getConditionStatusList() {
        return conditionStatusList;
    }

    public void setConditionStatusList(List<Byte> conditionStatusList) {
        this.conditionStatusList = conditionStatusList;
    }
}
