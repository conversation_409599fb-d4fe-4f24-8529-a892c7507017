package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2022/8/31 16:48
 */
public enum SelectApiEnum {
    DEVELOP_TYPE(0, "发版类型"),
    MODULE(1, "功能模块");

    private Integer code;
    private String message;

    SelectApiEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    /**
     * 根据code获取对应枚举
     */
    public static SelectApiEnum getEnum(Integer code) {
        for (SelectApiEnum commonEnum : SelectApiEnum.values()) {
            if (commonEnum.getCode().equals(code) ) {
                return commonEnum;
            }
        }
        return null;
    }
}
