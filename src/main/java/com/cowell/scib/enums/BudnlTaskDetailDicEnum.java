package com.cowell.scib.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组货明细字典
 * <AUTHOR>
 * @date 2023/3/11 18:23
 */
public enum BudnlTaskDetailDicEnum {
    GOODS("goodsType"),
    BUSINESS("CompanyGroup"),
    STORE("StoreGroup"),
    ZS("ZsStoreGroup"),
    DISPOSE("disposeType");


    public static List<String> storeTypeList(){
        return Arrays.stream(BudnlTaskDetailDicEnum.values()).filter(v->(!BudnlTaskDetailDicEnum.GOODS.equals(v) && !BudnlTaskDetailDicEnum.BUSINESS.equals(v))).map(v->v.getCode()).collect(Collectors.toList());
    }

    public static List<String> storeTypeAllList(){
        return Arrays.stream(BudnlTaskDetailDicEnum.values()).map(v->v.getCode()).collect(Collectors.toList());
    }

    private String code;

    BudnlTaskDetailDicEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static String keyGoods(String taskCode){
        return new StringBuilder().append(taskCode).append("_").append(BudnlTaskDetailDicEnum.GOODS.getCode()).toString();
    }

    public static String keyBusiness(String taskCode){
        return new StringBuilder().append(taskCode).append("_").append(BudnlTaskDetailDicEnum.BUSINESS.getCode()).toString();
    }

    public static String keyStore(String taskCode){
        return new StringBuilder().append(taskCode).append("_").append(BudnlTaskDetailDicEnum.STORE.getCode()).toString();
    }

    public static String keyZs(String taskCode){
        return new StringBuilder().append(taskCode).append("_").append(BudnlTaskDetailDicEnum.ZS.getCode()).toString();
    }

    public static String keyDispose(String taskCode){
        return new StringBuilder().append(taskCode).append("_").append(BudnlTaskDetailDicEnum.DISPOSE.getCode()).toString();
    }
}
