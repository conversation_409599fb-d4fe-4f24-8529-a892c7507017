package com.cowell.scib.enums;

import java.util.Arrays;

public enum FileTypeEnum {
    TRACK_RESULT_LEVEL_REVIEW_FILE(1, "企业店型级复盘文件"),
    TRACK_RETULT_COMPOSITION_REVIEW_FILE(2, "成分复盘文件"),
    TRACK_RESULT_EFFICIENCY_ANALYSE_FILE(3, "组货效率分析"),
    SINFGL_STORE_SIX_DETAIL_FILE(4, "单店级必备明细（6级）"),
    STORE_GROUP_DETAIL_FILE(5, "店型级必备明细（前4级）"),
    SINFGL_STORE_TWO_DETAIL_FILE(6, "单店级必备明细（后2级）"),
    GROUP_GOODS_RESULT_FILE(7, "组货结果文件");


    private int code;
    private String message;

    FileTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static boolean exitCode(int code){
        return Arrays.stream(FileTypeEnum.values()).filter(v->v.getCode()==code).count()>0;
    }

    public static String getUseStatusName(int code) {
        for (FileTypeEnum o : FileTypeEnum.values()) {
            if (o.getCode()==code) {
                return o.getMessage();
            }
        }
        return "";
    }
}
