package com.cowell.scib.enums;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 必备标识枚举
 */
public enum NecessaryTagEnum {
    NONE_NECESSARY((byte)0, "非必备"),
    GROUP_NECESSARY((byte)1, "集团必备"),
    PLATFORM_NECESSARY((byte)2, "平台必备"),
    COMPANY_NECESSARY((byte)3, "企业必备"),
    STORE_TYPE_NECESSARY((byte)4, "店型必备"),
    STORE_CHOOSE_NECESSARY((byte)5, "店型选配"),
    SINGLE_STORE_NECESSARY((byte)6, "单店必备"),
    SEASON_GOODS((byte)7, "季节品"),
    STORE_TYPE_NECESSARY_CATALOGUE((byte)8, "店型必备-挂网补充"),
    UNSALABLE_CANCEL_MINDISPLAY((byte)9, "长期滞销取消最小陈列量"),
    ;
    private byte code;
    private String message;

    NecessaryTagEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (NecessaryTagEnum necessaryGoodsLevelEnum : NecessaryTagEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code) {
                return necessaryGoodsLevelEnum.getMessage();
            }
        }
        return "";
    }
    public static NecessaryTagEnum getEnumByCode(Byte code) {
        if(code == null){
            return null;
        }
        for (NecessaryTagEnum necessaryGoodsLevelEnum : NecessaryTagEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code.byteValue()) {
                return necessaryGoodsLevelEnum;
            }
        }
        return null;
    }
    public static NecessaryTagEnum getEnumByMessage(String message) {
        if(StringUtils.isBlank(message)){
            return null;
        }
        for (NecessaryTagEnum necessaryGoodsLevelEnum : NecessaryTagEnum.values()) {
            if (necessaryGoodsLevelEnum.getMessage().equals(message)) {
                return necessaryGoodsLevelEnum;
            }
        }
        return null;
    }

    public static List<Byte> getSixNecessaryTag() {
        return Arrays.asList(NecessaryTagEnum.GROUP_NECESSARY.getCode(),
                NecessaryTagEnum.PLATFORM_NECESSARY.getCode(),
                NecessaryTagEnum.COMPANY_NECESSARY.getCode(),
                NecessaryTagEnum.STORE_TYPE_NECESSARY.getCode(),
                NecessaryTagEnum.STORE_CHOOSE_NECESSARY.getCode(),
                NecessaryTagEnum.SINGLE_STORE_NECESSARY.getCode());
    }
}
