package com.cowell.scib.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 01_中心大店：中心大店
 * 11_社商_大店：大店&选址商圈店型=商业店、社区及商住店
 * 12_社商_中店：中店&选址商圈店型=商业店、社区及商住店
 * 13_社商_小店：小店&选址商圈店型=商业店、社区及商住店
 * 21_乡镇_大店：大店&选址商圈店型=乡镇店
 * 22_乡镇_中店：中店&选址商圈店型=乡镇店
 * 23_乡镇_小店：小店&选址商圈店型=乡镇店
 * 31_院边_大店：大店&选址商圈店型=院边店
 * 32_院边_中店：中店&选址商圈店型=院边店
 * 33_院边_小店：小店&选址商圈店型=院边店
 * <AUTHOR>
 * @date 2023/3/14 22:50
 */
public enum StoreTradingAreaEnum {
    YBD("院边店","院边店"),
    XZD("乡镇店","乡镇店"),
    SSD("社区及商住店","社商店");

    private String area;
    private String message;

    public static String getMessageByCode(String level) {
        if(StringUtils.isBlank(level)){
            return "";
        }
        for (StoreTradingAreaEnum salesLevelEnum : StoreTradingAreaEnum.values()) {
            if (salesLevelEnum.getArea().equals(level)) {
                return salesLevelEnum.getMessage();
            }
        }
        return "";
    }

    StoreTradingAreaEnum(String area, String message) {
        this.area = area;
        this.message = message;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
