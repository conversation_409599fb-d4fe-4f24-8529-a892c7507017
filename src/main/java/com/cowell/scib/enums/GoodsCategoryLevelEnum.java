package com.cowell.scib.enums;

/**
 * 类目层级，1大类，2中类，3小类，4子类
 */
public enum GoodsCategoryLevelEnum {

    /**
     类目层级，1大类，2中类，3小类，4子类
     */
    BIG_CATEGORY((byte)1, "大类"),
    MIDDLE_CATEGORY((byte)2, "中类"),
    SMALL_CATEGORY((byte)3, "小类"),
    CHILD_CATEGORY((byte)4, "子类"),
    ;

    GoodsCategoryLevelEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    private byte code;
    private String name;

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(byte code) {
        for (GoodsCategoryLevelEnum goodsCategoryLevelEnum : GoodsCategoryLevelEnum.values()) {
            if (goodsCategoryLevelEnum.getCode() == code) {
                return goodsCategoryLevelEnum.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static GoodsCategoryLevelEnum getEnumByCode(byte code) {
        for (GoodsCategoryLevelEnum goodsCategoryLevelEnum : GoodsCategoryLevelEnum.values()) {
            if (goodsCategoryLevelEnum.getCode() == code) {
                return goodsCategoryLevelEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static GoodsCategoryLevelEnum getEnumByName(String name) {
        for (GoodsCategoryLevelEnum goodsCategoryLevelEnum : GoodsCategoryLevelEnum.values()) {
            if (goodsCategoryLevelEnum.getName().equals(name)) {
                return goodsCategoryLevelEnum;
            }
        }
        return null;
    }
}
