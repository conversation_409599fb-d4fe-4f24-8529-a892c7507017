package com.cowell.scib.enums;

/**
 * mdm任务来源
 */
public enum MdmTaskSourceEnum {
    NECESSARY_CONTENT_ADJUST((byte)1, "必备目录调整"),
    GROUP_GOODS_TASK((byte)2, "组货任务"),
    STORE_COPY((byte)3, "单门店必备复制"),
    SEASON_GOODS((byte)4, "季节品"),
    AUTO_POLICY_CHANGE((byte)5, "商品淘汰取消必备"),
    SINGLE_STORE_GOODS_REMOVE((byte)6, "单门店取消必备"),
    GOODS_CATALOGUE_SUPPLE((byte)7, "挂网目录补充"),
    SENSITIVE_ADD((byte)8, "自动化策略-敏感品打标"),
    SENSITIVE_CANCEL((byte)9, "自动化策略-敏感品取消"),
    SENSITIVE_DEL_DISPLAY((byte)10, "自动化策略-180天滞销取消最小陈列量"),
    CHOOSE_GOODS_TO_NON((byte)11, "选配商品改不经营"),
    NON_GOODS_TO_CHOOSE((byte)12, "不经营商品改选配"),
    NON_CHANGE_FORBID_DISTR((byte)13, "自动化策略-不经营商品修改请货标签"),
    ;
    private byte code;
    private String message;

    MdmTaskSourceEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (MdmTaskSourceEnum necessaryGoodsLevelEnum : MdmTaskSourceEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code) {
                return necessaryGoodsLevelEnum.getMessage();
            }
        }
        return "";
    }
    public static MdmTaskSourceEnum getEnumByCode(Byte code) {
        if(code == null){
            return null;
        }
        for (MdmTaskSourceEnum necessaryGoodsLevelEnum : MdmTaskSourceEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code.byteValue()) {
                return necessaryGoodsLevelEnum;
            }
        }
        return null;
    }
}
