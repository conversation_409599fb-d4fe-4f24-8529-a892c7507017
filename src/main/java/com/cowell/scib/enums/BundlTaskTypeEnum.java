package com.cowell.scib.enums;

import com.beust.jcommander.internal.Lists;
import com.cowell.scib.service.vo.amis.OptionDto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 组货任务类型
 * <AUTHOR>
 * @date 2023/3/9 16:17
 */
public enum BundlTaskTypeEnum {
    PLATE_BUNDL((byte)1, "全层级必备组货"),
    BUSINESS_BUNDL((byte)2, "企业必备组货"),
    STORE_BUNDL((byte)3, "店型必备组货"),
    KEY_STORE_O2O_BUNDL((byte)4, "O2O重点门店组货"),
    NEW_STORE_RECOMMEND_BUNDL((byte)5, "新店推荐目录组货");

    private Byte code;
    private String message;

    BundlTaskTypeEnum(Byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (BundlTaskTypeEnum bundlTaskTypeEnum : BundlTaskTypeEnum.values()) {
            if (bundlTaskTypeEnum.getCode() == code) {
                return bundlTaskTypeEnum.getMessage();
            }
        }
        return "";
    }

    public static List<OptionDto> listValue() {
        List<OptionDto> optionDtoList = Lists.newArrayList();
        for (BundlTaskTypeEnum bundlTaskTypeEnum : BundlTaskTypeEnum.values()) {
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.equals(bundlTaskTypeEnum)){
                continue;
            }
            optionDtoList.add(new OptionDto(bundlTaskTypeEnum.getMessage(), bundlTaskTypeEnum.getCode().toString()));
        }
        return optionDtoList;
    }

    public static List<OptionDto> listBusinessValue() {
        List<OptionDto> optionDtoList = Lists.newArrayList();
        for (BundlTaskTypeEnum bundlTaskTypeEnum : BundlTaskTypeEnum.values()) {
            if(BundlTaskTypeEnum.NEW_STORE_RECOMMEND_BUNDL.equals(bundlTaskTypeEnum)){
                continue;
            }
            if(BundlTaskTypeEnum.PLATE_BUNDL.equals(bundlTaskTypeEnum)){
                continue;
            }
            optionDtoList.add(new OptionDto(bundlTaskTypeEnum.getMessage(), bundlTaskTypeEnum.getCode().toString()));
        }
        return optionDtoList;
    }

    public static Boolean bbPlateType(Byte type){
        return BundlTaskTypeEnum.PLATE_BUNDL.getCode().equals(type);
    }

    public static Boolean bbBusinessType(Byte type){
        return BundlTaskTypeEnum.BUSINESS_BUNDL.getCode().equals(type);
    }

    public static Boolean bbStoreType(Byte type){
        return BundlTaskTypeEnum.STORE_BUNDL.getCode().equals(type);
    }

    public static List<Byte> getNecessaryCheckTypeList(){
        return Lists.newArrayList(BundlTaskTypeEnum.PLATE_BUNDL.getCode(), BundlTaskTypeEnum.BUSINESS_BUNDL.getCode(), BundlTaskTypeEnum.STORE_BUNDL.getCode(), BundlTaskTypeEnum.KEY_STORE_O2O_BUNDL.getCode());
    }
}
