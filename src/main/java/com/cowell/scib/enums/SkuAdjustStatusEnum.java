package com.cowell.scib.enums;

/**
 * 调整状态 0 未提交 1 已提交 2审批中 3 已生效 4 已作废
 */
public enum SkuAdjustStatusEnum {
    NO_COMMIT(0, "未提交"),
    COMMITED(1, "已提交"),
    APPROVING(2, "审批中"),
    EFFECTED(3, "已生效（审批通过）"),
    INVALID(4, "已作废（审批拒绝）"),
    ;
    private Integer code;
    private String name;

    SkuAdjustStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(Integer code) {
        for (SkuAdjustStatusEnum skuAdjustStatusEnum : SkuAdjustStatusEnum.values()) {
            if (skuAdjustStatusEnum.getCode().equals(code)) {
                return skuAdjustStatusEnum.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static SkuAdjustStatusEnum getEnumByCode(Integer code) {
        for (SkuAdjustStatusEnum skuAdjustStatusEnum : SkuAdjustStatusEnum.values()) {
            if (skuAdjustStatusEnum.getCode().equals(code)) {
                return skuAdjustStatusEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static SkuAdjustStatusEnum getEnumByName(String name) {
        for (SkuAdjustStatusEnum skuAdjustStatusEnum : SkuAdjustStatusEnum.values()) {
            if (skuAdjustStatusEnum.getName().equals(name)) {
                return skuAdjustStatusEnum;
            }
        }
        return null;
    }

}
