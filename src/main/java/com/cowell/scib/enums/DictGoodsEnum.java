package com.cowell.scib.enums;

/**
 * 组货商品字典枚举
 * <AUTHOR>
 * @date 2023/4/14 14:26
 */
public enum DictGoodsEnum {
    GOODSTYPE("goodsType"),
    GOODSLINE("goodsline"),
    EXCLUDEATTRIBUTE("excludeAttribute"),
    GOODSBLACKLIST("goodsBlackList");

    private String code;

    DictGoodsEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
