package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/3/12 23:10
 */
public enum BundlSearchEnum {
    TASK(1),GOODS(2),STORE(3),ZS(4),STATUS(5);
    private Integer type;

    BundlSearchEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 根据type获取对应枚举
     */
    public static BundlSearchEnum getEnum(Integer type) {
        for (BundlSearchEnum commonEnum : BundlSearchEnum.values()) {
            if (commonEnum.getType().equals(type) ) {
                return commonEnum;
            }
        }
        return null;
    }
}
