package com.cowell.scib.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 经营状态枚举
 */
public enum SuggestManageStatusEnum {
    STORE_NEW_GOODS(1,"店级新品"),
    MANAGE_NECESARY( 2,"经营-必备"),
    SUGGEST_MANAGE_CHOOSE( 3,"建议经营-选配"),
    SUGGEST_NON_MANAGE(4, "建议不经营"),
    MUST_NON_MANAGE(5, "必须不经营"),
    MUST_MANAGE_CHOOSE(6, "必须经营-选配"),
    NONE(99,"无"), // 无

    ;
    private Integer code;
    private String message;

    SuggestManageStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public final static List<Integer> jySuggestStatusList = Lists.newArrayList(SUGGEST_MANAGE_CHOOSE.getCode(),SUGGEST_NON_MANAGE.getCode());

    public final static List<Integer> zyStatusList = Lists.newArrayList(MANAGE_NECESARY.getCode(),SUGGEST_MANAGE_CHOOSE.getCode(),SUGGEST_NON_MANAGE.getCode(),MUST_NON_MANAGE.getCode());
    public static SuggestManageStatusEnum getEnumByCode(Integer code) {
        if(null == code){
            return null;
        }
        for (SuggestManageStatusEnum suggestManageStatusEnum : SuggestManageStatusEnum.values()) {
            if (suggestManageStatusEnum.getCode().equals(code)) {
                return suggestManageStatusEnum;
            }
        }
        return null;
    }
    public static SuggestManageStatusEnum getEnumByMessage(String message) {
        if(StringUtils.isBlank(message)){
            return null;
        }
        for (SuggestManageStatusEnum suggestManageStatusEnum : SuggestManageStatusEnum.values()) {
            if (suggestManageStatusEnum.getMessage().equals(message)) {
                return suggestManageStatusEnum;
            }
        }
        return null;
    }
}
