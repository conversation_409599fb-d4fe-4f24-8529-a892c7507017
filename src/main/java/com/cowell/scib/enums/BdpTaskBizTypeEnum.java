package com.cowell.scib.enums;

/**
 * bdp任务biztype枚举
 */
public enum BdpTaskBizTypeEnum {
    TO_NO_MANAGE(1, "不经营商品"),
    TO_MANAGE(2, "异常转正常"),
    FORBID_APPLY(3, "禁止请货"),
    CATEGORY_CHANGED(4, "子类变更"),
    ;
    private Integer code;
    private String name;

    BdpTaskBizTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(Integer code) {
        for (BdpTaskBizTypeEnum bdpTaskBizTypeEnum : BdpTaskBizTypeEnum.values()) {
            if (bdpTaskBizTypeEnum.getCode().equals(code)) {
                return bdpTaskBizTypeEnum.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static BdpTaskBizTypeEnum getEnumByCode(Integer code) {
        for (BdpTaskBizTypeEnum bdpTaskBizTypeEnum : BdpTaskBizTypeEnum.values()) {
            if (bdpTaskBizTypeEnum.getCode().equals(code)) {
                return bdpTaskBizTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static BdpTaskBizTypeEnum getEnumByName(String name) {
        for (BdpTaskBizTypeEnum bdpTaskBizTypeEnum : BdpTaskBizTypeEnum.values()) {
            if (bdpTaskBizTypeEnum.getName().equals(name)) {
                return bdpTaskBizTypeEnum;
            }
        }
        return null;
    }

}
