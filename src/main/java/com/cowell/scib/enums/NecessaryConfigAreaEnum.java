package com.cowell.scib.enums;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 必备标识枚举
 */
public enum NecessaryConfigAreaEnum {
    COUNTRY(1,"全国"),
    PROVINCE( 2,"省份"),
    CITY( 3,"城市"),
    AREA(4, "区县"),
    ;
    private Integer code;
    private String message;

    NecessaryConfigAreaEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static NecessaryConfigAreaEnum getEnumByCode(Integer code) {
        if(null == code){
            return null;
        }
        for (NecessaryConfigAreaEnum necessaryConfigAreaEnum : NecessaryConfigAreaEnum.values()) {
            if (necessaryConfigAreaEnum.getCode().equals(code)) {
                return necessaryConfigAreaEnum;
            }
        }
        return null;
    }
    public static NecessaryConfigAreaEnum getEnumByMessage(String message) {
        if(StringUtils.isBlank(message)){
            return null;
        }
        for (NecessaryConfigAreaEnum necessaryConfigAreaEnum : NecessaryConfigAreaEnum.values()) {
            if (necessaryConfigAreaEnum.getMessage().equals(message)) {
                return necessaryConfigAreaEnum;
            }
        }
        return null;
    }
}
