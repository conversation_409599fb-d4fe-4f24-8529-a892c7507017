package com.cowell.scib.enums;

public enum SelectMethodEnum {
    BY_ROLE(1, "按角色"),
    SPECIFIC_PERSON(2, "指定人员"),
    ;
    private Integer code;
    private String name;

    SelectMethodEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public static SelectMethodEnum getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SelectMethodEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
