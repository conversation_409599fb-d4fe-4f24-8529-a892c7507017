package com.cowell.scib.enums;

/**
 * 二轮选配来源 1：门店添加 2：规划导入 3：系统建议
 */
public enum ExemptionSourceEnum {
    STORE_ADD((byte)1, "门店添加"),
    IMPORT((byte)2, "规划导入"),
    SUGGEST((byte)3, "系统建议"),
    ;
    private Byte code;
    private String name;

    ExemptionSourceEnum(Byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(Byte code) {
        for (ExemptionSourceEnum bdpTaskBizTypeEnum : ExemptionSourceEnum.values()) {
            if (bdpTaskBizTypeEnum.getCode().equals(code)) {
                return bdpTaskBizTypeEnum.getName();
            }
        }
        return "";
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static ExemptionSourceEnum getEnumByCode(Byte code) {
        for (ExemptionSourceEnum bdpTaskBizTypeEnum : ExemptionSourceEnum.values()) {
            if (bdpTaskBizTypeEnum.getCode().equals(code)) {
                return bdpTaskBizTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static ExemptionSourceEnum getEnumByName(String name) {
        for (ExemptionSourceEnum bdpTaskBizTypeEnum : ExemptionSourceEnum.values()) {
            if (bdpTaskBizTypeEnum.getName().equals(name)) {
                return bdpTaskBizTypeEnum;
            }
        }
        return null;
    }

}
