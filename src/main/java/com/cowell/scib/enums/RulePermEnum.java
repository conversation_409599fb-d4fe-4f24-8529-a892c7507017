package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/3/2 10:57
 */
public enum RulePermEnum {
    P_SUPPER(0, "超级管理员权限"),
    P_EDIT(1, "可编辑权限"),
    P_UNEDIT(2, "不可编辑权限");

    private int code;
    private String message;

    RulePermEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 超级管理员权限
     * @param paramCode
     * @return
     */
    public static boolean isSupper(int paramCode) {
        return RulePermEnum.P_SUPPER.getCode()== paramCode;
    }

    /**
     * 全平台权限
     * @param paramCode
     * @return
     */
    public static boolean isEdit(int paramCode) {
        return RulePermEnum.P_EDIT.getCode()== paramCode;
    }

    /**
     * 编辑权限
     * @param paramCode
     * @return
     */
    public static boolean isUnEdit(int paramCode) {
        return RulePermEnum.P_UNEDIT.getCode()== paramCode;
    }

    /**
     * 参数比较结果
     * @param paramCode
     * @return
     */
    public static boolean isEquals(int paramCode, int targetCode) {
        return paramCode == targetCode;
    }
}
