package com.cowell.scib.enums;

import java.util.concurrent.TimeUnit;

public enum AsyncExportActionEnum {
    STORE_GOODS_INFO("store-goods-info-", "一店一目数据", 60, TimeUnit.SECONDS, "/dgms/store/goods/export/"),
    NECESSARY_CONTENT("necessary-content-info-", "必备目录数据", 60, TimeUnit.SECONDS, "/dgms/necessary/content/export/"),
    JYML_SKU_MAX_LIMIT("jyml-sku-max-limit-", "SKU数配置管理", 60, TimeUnit.SECONDS, "/dgms/necessary/content/export/"),
    SKU_ADJUST_LIST("jyml-sku-adjust-list-", "SKU数配置调整记录", 60, TimeUnit.SECONDS, "/dgms/sku/adjust/export/"),
    SKU_ADJUST_EFFECT_LIST("jyml-sku-adjust-effect-list-", "SKU数配置调整生效记录", 60, TimeUnit.SECONDS, "/dgms/sku/effect/export/"),
    CUSTOMIZE_CHOOSE_SKU("jyml-customize_choose_sku-", "自定义选配商品", 60, TimeUnit.SECONDS, "/dgms/sku/customize/export/"),
    SECOND_CHOOSE("jyml-second-choose-sku-", "二轮选配商品批量维护", 60, TimeUnit.SECONDS, "/dgms/sku/second/export/"),
    ;
    private String action;
    private String name;
    private Integer timeInterval;
    private TimeUnit timeUnit;
    private String url;

    AsyncExportActionEnum(String action, String name, Integer timeInterval, TimeUnit timeUnit, String url) {
        this.action = action;
        this.name = name;
        this.timeInterval = timeInterval;
        this.timeUnit = timeUnit;
        this.url = url;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getTimeInterval() {
        return timeInterval;
    }

    public void setTimeInterval(Integer timeInterval) {
        this.timeInterval = timeInterval;
    }

    public TimeUnit getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(TimeUnit timeUnit) {
        this.timeUnit = timeUnit;
    }

}
