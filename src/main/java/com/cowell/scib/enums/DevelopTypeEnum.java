package com.cowell.scib.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/8/30 17:23
 */
public enum DevelopTypeEnum {
    BUG((byte)1, "BUG修复"),
    NEW((byte)2, "新功能"),
    OPT((byte)3, "功能优化");

    private byte code;
    private String message;

    DevelopTypeEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static boolean exitCode(byte code){
        return Arrays.stream(DevelopTypeEnum.values()).filter(v->v.getCode()==code).count()>0;
    }

    public static String getNameByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (DevelopTypeEnum developTypeEnum : DevelopTypeEnum.values()) {
            if (developTypeEnum.getCode() == code) {
                return developTypeEnum.getMessage();
            }
        }
        return "";
    }
}
