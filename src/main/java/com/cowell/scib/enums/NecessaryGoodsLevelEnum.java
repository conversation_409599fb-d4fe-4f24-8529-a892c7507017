package com.cowell.scib.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum  NecessaryGoodsLevelEnum {
    FIRST((byte)1, "集团必备"),
    SECEND((byte)2, "平台必备"),
    THIRD((byte)3, "企业必备"),
    FOURTH((byte)4, "店型必备"),
    FIFTH((byte)5, "店型选配"),
    SIXTH((byte)6, "单店必备"),
    ;
    private byte code;
    private String message;

    NecessaryGoodsLevelEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (NecessaryGoodsLevelEnum necessaryGoodsLevelEnum : NecessaryGoodsLevelEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code) {
                return necessaryGoodsLevelEnum.getMessage();
            }
        }
        return "";
    }
    public static NecessaryGoodsLevelEnum getEnumByCode(Byte code) {
        if(code == null){
            return null;
        }
        for (NecessaryGoodsLevelEnum necessaryGoodsLevelEnum : NecessaryGoodsLevelEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code.byteValue()) {
                return necessaryGoodsLevelEnum;
            }
        }
        return null;
    }

    public static List<String> getAllLevel() {
       return Arrays.stream(NecessaryGoodsLevelEnum.values()).collect(Collectors.toList()).stream().map(NecessaryGoodsLevelEnum->String.valueOf(NecessaryGoodsLevelEnum.getCode())).collect(Collectors.toList());
    }
}
