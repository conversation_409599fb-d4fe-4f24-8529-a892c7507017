package com.cowell.scib.enums;

import com.cowell.scib.constant.OrderNoSequenceConstant;

/**
 * <AUTHOR>
 * @date 2023/3/13 18:17
 */
public enum OrderTypeEnum {
    SCIB_ZH_TASK(1, "ZH", OrderNoSequenceConstant.SCIB_ZH_TASK, "组货任务单号"),
    SCIB_XDML_TASK(2, "XD", OrderNoSequenceConstant.SCIB_XDML_TASK, "新店目录推荐任务单号"),
    SCIB_ADJUST_ORDER(3, "DXZH", OrderNoSequenceConstant.SCIB_ADJUST_ORDER, "门店SKU数配置调整单号"),
    ;
    OrderTypeEnum(int key, String type, String redisKey, String name) {
        this.key = key;
        this.type = type;
        this.redisKey = redisKey;
        this.name = name;
    }
    private int key;
    private String type;
    private String redisKey;
    private String name;

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getRedisKeyByType(String type) {
        for (OrderTypeEnum orderType : OrderTypeEnum.values()) {
            if (orderType.type.equals(type)) {
                return orderType.redisKey;
            }
        }
        return null;
    }
}
