package com.cowell.scib.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 必备业务类型
 */
public enum StoreContentBizTypeEnum {
    NECESSARY(1,"必备"),
    MONTH_MANAGE(2,"月度经营"),
    DAILY_NEW_GOODS( 3,"每天店级新品弥补"),
    DAILY_GOODS_EXCHANGE( 4,"新品到期转选配"),
    MANAGE_CHOOSE( 5,"经营目录选配"),
    NON_MANAGE(6, "经营目录不经营"),
    MDM_PUSH(7, "MDM属性下发"),
    ;
    private Integer code;
    private String message;

    StoreContentBizTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static StoreContentBizTypeEnum getEnumByCode(Integer code) {
        if(null == code){
            return null;
        }
        for (StoreContentBizTypeEnum bizTypeEnum : StoreContentBizTypeEnum.values()) {
            if (bizTypeEnum.getCode().equals(code)) {
                return bizTypeEnum;
            }
        }
        return null;
    }
    public static StoreContentBizTypeEnum getEnumByMessage(String message) {
        if(StringUtils.isBlank(message)){
            return null;
        }
        for (StoreContentBizTypeEnum bizTypeEnum : StoreContentBizTypeEnum.values()) {
            if (bizTypeEnum.getMessage().equals(message)) {
                return bizTypeEnum;
            }
        }
        return null;
    }
}
