package com.cowell.scib.enums;

public enum GoodsStatusEnum {
    //集团商品状态  正常 废弃 质管废弃
    //连锁商品状态 正常 废弃

    NORMAL("正常"),
    DISCARD("废弃"),
    QUALITYCONTROLDISCORD("质管废弃");

    private String code;

    GoodsStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
