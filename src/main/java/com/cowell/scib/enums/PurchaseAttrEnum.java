package com.cowell.scib.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 采购属性枚举
 * <AUTHOR>
 * @date 2023/3/13 15:12
 */
public enum PurchaseAttrEnum {
    TC("统采(P)"),
    TP("贴牌(O)"),
    ZX("专销(F)"),
    JC("集采(J)"),
    DC("地采(D)"),
    ;

    public static PurchaseAttrEnum getEnumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (PurchaseAttrEnum purchaseAttrEnum : PurchaseAttrEnum.values()) {
            if (purchaseAttrEnum.getCode().equals(code)) {
                return purchaseAttrEnum;
            }
        }
        return null;
    }

    private String code;

    PurchaseAttrEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}

