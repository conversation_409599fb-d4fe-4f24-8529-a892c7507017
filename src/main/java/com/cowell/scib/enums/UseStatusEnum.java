package com.cowell.scib.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/8/30 16:56
 */
public enum UseStatusEnum {
    STOP((byte)0, "停用"),
    START((byte)1, "启用");

    private byte code;
    private String message;

    UseStatusEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static boolean exitCode(byte code){
        return Arrays.stream(UseStatusEnum.values()).filter(v->v.getCode()==code).count()>0;
    }

    public static String getUseStatusName(byte code) {
        for (UseStatusEnum o : UseStatusEnum.values()) {
            if (o.getCode()==code) {
                return o.getMessage();
            }
        }
        return "";
    }

}
