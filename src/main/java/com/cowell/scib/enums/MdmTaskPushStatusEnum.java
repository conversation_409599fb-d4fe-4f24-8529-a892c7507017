package com.cowell.scib.enums;

/**
 * mdm 下发状态 0 已下发 1 失败 2 成功
 */
public enum MdmTaskPushStatusEnum {
    PUSHED((byte)0, "已下发"),
    FAIL((byte)1, "失败"),
    SUCCESS((byte)2, "成功"),
    NO_PUSH((byte)9, "未下发"),
    ;
    private byte code;
    private String message;

    MdmTaskPushStatusEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (MdmTaskPushStatusEnum mdmTaskPushStatusEnum : MdmTaskPushStatusEnum.values()) {
            if (mdmTaskPushStatusEnum.getCode() == code) {
                return mdmTaskPushStatusEnum.getMessage();
            }
        }
        return "";
    }
    public static MdmTaskPushStatusEnum getEnumByCode(Byte code) {
        if(code == null){
            return null;
        }
        for (MdmTaskPushStatusEnum necessaryGoodsLevelEnum : MdmTaskPushStatusEnum.values()) {
            if (necessaryGoodsLevelEnum.getCode() == code.byteValue()) {
                return necessaryGoodsLevelEnum;
            }
        }
        return null;
    }
}
