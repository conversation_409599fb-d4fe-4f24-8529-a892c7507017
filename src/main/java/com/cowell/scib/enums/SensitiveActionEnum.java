package com.cowell.scib.enums;

/**
 * 敏感品动作枚举
 * <AUTHOR>
 * @date 2023/3/13 15:12
 */
public enum SensitiveActionEnum {
    ADD_SINGLE_NECESSARY((byte)1, "增加单店必备"),
    CANCEL_SINGLE_NECESSARY((byte)2, "取消单店必备"),
    MIN_DISPLAY_ZERO((byte)3, "最小陈列量归零");

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (SensitiveActionEnum sensitiveActionEnum : SensitiveActionEnum.values()) {
            if (sensitiveActionEnum.getCode() == code) {
                return sensitiveActionEnum.getMessage();
            }
        }
        return "";
    }

    private Byte code;
    private String message;

    SensitiveActionEnum(Byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
