package com.cowell.scib.enums;

/**
 * 是否拥有全部或部分或没有权限
 */
public enum PermissionFullScopeEnum {
    NOTHING(0, "无权限"),
    ALL(1, "全部权限"),
    PART(2, "部分权限");

    private Integer code;
    private String name;

    PermissionFullScopeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(Integer code) {
        for (PermissionFullScopeEnum permissionFullScopeEnum : PermissionFullScopeEnum.values()) {
            if (permissionFullScopeEnum.getCode().equals(code)) {
                return permissionFullScopeEnum.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static PermissionFullScopeEnum getEnumByCode(Integer code) {
        for (PermissionFullScopeEnum permissionFullScopeEnum : PermissionFullScopeEnum.values()) {
            if (permissionFullScopeEnum.getCode().equals(code)) {
                return permissionFullScopeEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static PermissionFullScopeEnum getEnumByName(String name) {
        for (PermissionFullScopeEnum permissionFullScopeEnum : PermissionFullScopeEnum.values()) {
            if (permissionFullScopeEnum.getName().equals(name)) {
                return permissionFullScopeEnum;
            }
        }
        return null;
    }

    public static boolean isAll(Integer isFullScope){
        return PermissionFullScopeEnum.ALL.getCode().equals(isFullScope);
    }

    public static boolean isPart(Integer isFullScope){
        return PermissionFullScopeEnum.PART.getCode().equals(isFullScope);
    }

    public static boolean isNone(Integer isFullScope){
        return PermissionFullScopeEnum.NOTHING.getCode().equals(isFullScope);
    }
}
