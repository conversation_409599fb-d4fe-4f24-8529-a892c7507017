package com.cowell.scib.enums;

import com.beust.jcommander.internal.Lists;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/24 10:13
 */
public enum DicApiEnum {
    ZH_STORE("BB_1", "组货门店范围配置","storeRules", (byte)2, (byte)2),
    ZH_GOODS("BB_2", "组货商品范围配置","goodsRules", (byte)2, (byte)2),
    ZH_NORMAL("BB_3", "组货通用规则（含平台必备规则）配置","commonRules", (byte)2, (byte)1),
    COMMUST_ZXCY("BB_4_1","企业必备规则配置-中西成药", "companyRules", (byte)2, (byte)1),
    COMMUST_ZYSR("BB_4_2", "企业必备规则配置-中药参茸-养生中药", "companyRules", (byte)2, (byte)1),
    COMMUST_BJSP("BB_4_3", "企业必备规则配置-保健食品", "companyRules", (byte)2, (byte)1),
    COMMUST_YLQX("BB_4_4", "企业必备规则配置-医疗器械", "companyRules", (byte)2, (byte)1),
    STOMUST_ZXCY("BB_5_1", "店型必备/店型选配规则配置-中西成药", "storeTypeRules", (byte)2, (byte)1),
    STOMUST_ZYSR("BB_5_2", "店型必备/店型选配规则配置-中药参茸-养生中药", "storeTypeRules", (byte)2, (byte)1),
    STOMUST_BJSP("BB_5_3", "店型必备/店型选配规则配置-保健食品", "storeTypeRules", (byte)2, (byte)1),
    STOMUST_YLQX("BB_5_4", "店型必备/店型选配规则配置-医疗器械", "storeTypeRules", (byte)2, (byte)1),
    STOMUST_NORMAL("BB_5_5", "店型必备/店型选配规则配置-通用", "storeTypeRules", (byte)2, (byte)1),
    ZH_STORE_O2O("ZH_STORE_O2O", "O2O重点门店组货规则配置", "", (byte)2, (byte)1),
    PROPOSE("TJ_1", "建议规则", "", (byte)1, (byte)1),
    RECOMMEND("TJ_2", "推荐规则", "", (byte)1, (byte)1),
    JYML_BUSINESS("JYMU_1", "经营目录管控连锁范围配置","BusinessRules", (byte)5, (byte)2),
    JYML_STORE("JYMU_2", "经营目录管控门店范围配置","storeRules", (byte)5, (byte)2),
    NEWSTORE_TYXD("XD_1_1", "新店经营目录_小店组货规则配置-通用_小店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_SSXD("XD_1_2", "新店经营目录_小店组货规则配置-社商_小店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_XZXD("XD_1_3", "新店经营目录_小店组货规则配置-乡镇_小店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_YBXD("XD_1_4", "新店经营目录_小店组货规则配置-院边_小店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_TYZD("XD_2_1", "新店经营目录_中店组货规则配置-通用_中店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_SSZD("XD_2_2", "新店经营目录_中店组货规则配置-社商_中店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_XZZD("XD_2_3", "新店经营目录_中店组货规则配置-乡镇_中店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_YBZD("XD_2_4", "新店经营目录_中店组货规则配置-院边_中店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_TYDD("XD_3_1", "新店经营目录_大店组货规则配置-通用_大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_SSDD("XD_3_2", "新店经营目录_大店组货规则配置-社商_大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_XZDD("XD_3_3", "新店经营目录_大店组货规则配置-乡镇_大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_YBDD("XD_3_4", "新店经营目录_大店组货规则配置-院边_大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_TYZXCDD("XD_4_1", "新店经营目录_中心超大店组货规则配置-通用_中心超大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_SSZXCDD("XD_4_2", "新店经营目录_中心超大店组货规则配置-社商_中心超大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_XZZXCDD("XD_4_3", "新店经营目录_中心超大店组货规则配置-乡镇_中心超大店", "newstore_param", (byte)4, (byte)1),
    NEWSTORE_YBZXCDD("XD_4_4", "新店经营目录_中心超大店组货规则配置-院边_中心超大店", "newstore_param", (byte)4, (byte)1),
    BLKC_1("BLKC_1", "不良库存计算门店范围配置（通用）", "blkc_param", (byte)6, (byte)1),
    BLKC_2("BLKC_2", "不良库存计算商品范围配置（通用）", "blkc_param", (byte)6, (byte)1),
    BLKC_3("BLKC_3", "不良库存计算时间配置", "blkc_param", (byte)6, (byte)1),
    BLKC_4_1("BLKC_4_1", "效期品", "blkc_param", (byte)6, (byte)1),
    BLKC_4_2("BLKC_4_2", "定淘品", "blkc_param", (byte)6, (byte)1),
    BLKC_4_3("BLKC_4_3", "不经营品", "blkc_param", (byte)6, (byte)1),
    BLKC_5_1("BLKC_5_1", "不良库存执行配置_门店退仓", "blkc_param", (byte)6, (byte)1),
    BLKC_5_2("BLKC_5_2", "不良库存执行配置_门店报损", "blkc_param", (byte)6, (byte)1),
    BLKC_5_3("BLKC_5_3", "不良库存执行配置_门店销售", "blkc_param", (byte)6, (byte)1),
    ;

    private String code;
    private String message;
    private String action;
    /**
     *  1.推荐/2.必备/3.淘汰规则  初始化用
     */
    private Byte configType;
    /**
     * 属性类型 1.obj/2.checkBox/3.collection   初始化用(扩展表类型3不在初始化范围内，初始化只涉及1和2)
     */
    private Byte perprotyType;

    /**
     * 第三步任务需要
     * @return
     */
    public static List<String> listTaskThreeDict(){
        List<String> dictCodeList = Lists.newArrayList();
        dictCodeList.add(DicApiEnum.ZH_NORMAL.getCode());
        dictCodeList.add(DicApiEnum.COMMUST_ZXCY.getCode());
        dictCodeList.add(DicApiEnum.COMMUST_ZYSR.getCode());
        dictCodeList.add(DicApiEnum.COMMUST_BJSP.getCode());
        dictCodeList.add(DicApiEnum.COMMUST_YLQX.getCode());
        dictCodeList.add(DicApiEnum.STOMUST_ZXCY.getCode());
        dictCodeList.add(DicApiEnum.STOMUST_ZYSR.getCode());
        dictCodeList.add(DicApiEnum.STOMUST_BJSP.getCode());
        dictCodeList.add(DicApiEnum.STOMUST_YLQX.getCode());
        dictCodeList.add(DicApiEnum.STOMUST_NORMAL.getCode());
        return dictCodeList;
    }

    DicApiEnum(String code, String message, String action, Byte configType, Byte perprotyType) {
        this.code = code;
        this.message = message;
        this.action = action;
        this.configType = configType;
        this.perprotyType = perprotyType;
    }

    public Byte getPerprotyType() {
        return perprotyType;
    }

    public void setPerprotyType(Byte perprotyType) {
        this.perprotyType = perprotyType;
    }

    public Byte getConfigType() {
        return configType;
    }

    public void setConfigType(Byte configType) {
        this.configType = configType;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    /**
     * 判断两个映射结果
     * @param paramCode
     * @param permAction
     * @return
     */
    public static boolean mappingAction(String paramCode, String permAction) {
        if(StringUtils.isEmpty(paramCode) || StringUtils.isEmpty(permAction)){
            return false;
        }
        for (DicApiEnum dicEnableEnum : DicApiEnum.values()) {
            if (dicEnableEnum.getCode().equals(paramCode)) {
                return permAction.equals(dicEnableEnum.getAction());
            }
        }
        return false;
    }

    /**
     * 获取action
     * @param paramCode
     * @return
     */
    public static String getActionByCode(String paramCode) {
        if(StringUtils.isEmpty(paramCode)){
            return "";
        }
        for (DicApiEnum dicEnableEnum : DicApiEnum.values()) {
            if (dicEnableEnum.getCode().equals(paramCode)) {
                return dicEnableEnum.getAction();
            }
        }
        return "";
    }

    public static boolean isZhStoreAndGoods(String paramCode){
        if(StringUtils.isEmpty(paramCode)){
            return false;
        }
        return DicApiEnum.ZH_STORE.getCode().equals(paramCode) || DicApiEnum.ZH_GOODS.getCode().equals(paramCode);
    }

    public static boolean isTj(String paramCode){
        if(StringUtils.isEmpty(paramCode)){
            return false;
        }
        return DicApiEnum.PROPOSE.getCode().equals(paramCode)
                || DicApiEnum.RECOMMEND.getCode().equals(paramCode)
                || DicApiEnum.BLKC_1.getCode().equals(paramCode)
                || DicApiEnum.BLKC_2.getCode().equals(paramCode)
                || DicApiEnum.BLKC_3.getCode().equals(paramCode)
                || DicApiEnum.BLKC_4_1.getCode().equals(paramCode)
                || DicApiEnum.BLKC_4_2.getCode().equals(paramCode)
                || DicApiEnum.BLKC_4_3.getCode().equals(paramCode)
                || DicApiEnum.BLKC_5_1.getCode().equals(paramCode)
                || DicApiEnum.BLKC_5_2.getCode().equals(paramCode)
                || DicApiEnum.BLKC_5_3.getCode().equals(paramCode)
                ;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
