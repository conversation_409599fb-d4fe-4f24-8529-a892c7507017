package com.cowell.scib.enums;

import java.util.Objects;

/**
 * 配置类型
 * <AUTHOR>
 * @date 2023/3/15 15:45
 */
public enum ConfigTypeEnum {
    TJ((byte)1, "推荐"),
    BB((byte)2, "必备"),
    TT((byte)3, "淘汰规则"),
    XD((byte)4, "新店"),
    JY((byte)5, "经营"),
    BLCK((byte)6, "不良库存"),
    ;


    private Byte type;
    private String message;

    ConfigTypeEnum(Byte type, String message) {
        this.type = type;
        this.message = message;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    public static String getMessageByType(Byte type) {
        if(type == null){
            return "";
        }
        for (ConfigTypeEnum configTypeEnum : ConfigTypeEnum.values()) {
            if (Objects.equals(configTypeEnum.getType(), type)) {
                return configTypeEnum.getMessage();
            }
        }
        return "";
    }
}
