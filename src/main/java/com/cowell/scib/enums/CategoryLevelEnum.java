package com.cowell.scib.enums;

/**
 *
 */
public enum CategoryLevelEnum {
    CATEGROY(1, "大类"),
    MIDDLE(2, "中类"),
    SMALL(3, "小类"),
    SUB(4, "子类"),
    ;
    private Integer code;
    private String name;

    CategoryLevelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(Integer code) {
        for (CategoryLevelEnum skuAdjustStatusEnum : CategoryLevelEnum.values()) {
            if (skuAdjustStatusEnum.getCode().equals(code)) {
                return skuAdjustStatusEnum.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static CategoryLevelEnum getEnumByCode(Integer code) {
        for (CategoryLevelEnum skuAdjustStatusEnum : CategoryLevelEnum.values()) {
            if (skuAdjustStatusEnum.getCode().equals(code)) {
                return skuAdjustStatusEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static CategoryLevelEnum getEnumByName(String name) {
        for (CategoryLevelEnum skuAdjustStatusEnum : CategoryLevelEnum.values()) {
            if (skuAdjustStatusEnum.getName().equals(name)) {
                return skuAdjustStatusEnum;
            }
        }
        return null;
    }

}
