package com.cowell.scib.enums;


import java.util.Objects;

/**
 * 一店一目 是否有效(0 否 1 是) 枚举
 * <AUTHOR>
 * @date 2023/3/5 16:31
 */
public enum StoreGoodsEffectStatusEnum {
    NO((byte)0, "否"),
    YES((byte)1, "是"),
    ;
    private byte code;
    private String message;

    StoreGoodsEffectStatusEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static StoreGoodsEffectStatusEnum getEnumByCode(Byte code) {
        if(Objects.isNull(code)){
            return null;
        }
        for (StoreGoodsEffectStatusEnum storeGoodsEffectStatusEnum : StoreGoodsEffectStatusEnum.values()) {
            if (storeGoodsEffectStatusEnum.getCode() == code.byteValue()) {
                return storeGoodsEffectStatusEnum;
            }
        }
        return null;
    }

}
