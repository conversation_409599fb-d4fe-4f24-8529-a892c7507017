package com.cowell.scib.enums;

import lombok.Getter;

/**
 * 告警类型
 * <AUTHOR>
 * @date 2021/12/17 14:50
 */
@Getter
public enum AlertTypeEnum {
    EMAIL("EMAIL","邮箱")
    , QYWX("QYWX","企业微信（个人）")
    , QYWX_GROUP("QYWX_GROUP","企业微信（内部群）")
    ;
    private final String alertType;

    private final String desc;

    AlertTypeEnum(String alertType, String desc) {
        this.alertType = alertType;
        this.desc = desc;
    }

    /**
     * 验证alertType是否存在于枚举中
     *
     * @param alertType 要验证的alertType
     * @return 如果存在返回true，否则返回false
     */
    public static boolean isExist(String alertType) {
        for (AlertTypeEnum type : AlertTypeEnum.values()) {
            if (type.getAlertType().equals(alertType)) {
                return true;
            }
        }
        return false;
    }
}
