package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/4/13 15:03
 */
public enum DictAliasEnum {
    MANAGSTATE("managstate"),
    STORESTATUS("storestatus"),
    FORMAT("format"),
    STORETYPE("storetype"),
    OPERATIONTYPE("operationtype"),
    SPECIALTYPE("specialtype"),
    ORGIDS("orgIds"),
    EXCLUDEORGIDS("excludeOrgIds"),
    STOREATTR("storeattr");

    private String alias;

    DictAliasEnum(String alias) {
        this.alias = alias;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
