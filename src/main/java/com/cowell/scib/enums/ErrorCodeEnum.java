package com.cowell.scib.enums;

public enum ErrorCodeEnum {
    SUCCESS(0, "成功"),
    HTTP_STATUS_OK(200, "成功"),
    FAIL(201, "失败"),
    PROPERTY_EXISTS(400,"主键ID已存在"),
    INVALID_TOKEN(401, "参数为空"),
    SYSTEM_ERROR(500, "系统错误"),
    PARAM_ERROR(502, "参数错误"),
    GOODS_NOT_FIND_ERROR(1000, "未查询到商品信息"),
    PARAM_ERROR_EXCEPTION(10001, "入参未通过校验"),
    DEVELOP_MODULE_DATA_NULL(10002, "模块数据为空"),
    DEVELOP_MODULE_INPUT_LIMIT(10003, "最多可输入%s个字"),
    DEVELOP_MODULE_NAME_EXIT(10004, "该名称已被使用"),
    DEVELOP_MODULE_CODE_EXIT(10016, "该编码已被使用"),
    DEVELOP_MODULE_USE_ILLEGAL(10005, "状态值不合法"),
    DEVELOP_MODULE_CODE_ILLEGAL(10006, "功能模块不存在或未启用"),
    DEVELOP_MODULE_TIME_ILLEGAL(10007, "发布时间格式错误"),
    UPLOAD_FILE_NOT_EXISTS(10008, "上传文件不存在"),
    FILE_UPLOAD_FORMAT_ERROR(10009, "请上传正确格式的文件"),
    VERSION_NULL_ERROR(10010, "请填写版本号"),
    TITLE_NULL_ERROR(10011, "请填写标题"),
    CONTENT_NULL_ERROR(10012, "请填写发版内容"),
    TYPE_NULL_ERROR(10013, "请选择发版类型"),
    MODULE_NULL_ERROR(10014, "请选择功能模块"),
    TIME_NULL_ERROR(10015, "请填写发布时间"),

    RULE_ORG_NULL_ERROR(10016, "规则机构为空错误"),
    RULE_TYPE_SCOPE_NULL_ERROR(10017, "规则类型域不能为空"),
    RULE_TYPE_NOT_EXIST(10018, "规则类型不存在"),
    ORG_INFO_PLATFORM_NOT_EXIST(10019, "组织平台机构不存在"),
    RULE_ENUM_NOT_EXIST(10020, "枚举值不存在"),
    RULE_TYPE_NULL_ERROR(10021, "规则类型不能为空"),
    RULE_FILE_EMPTY(10022, "导入文件为空"),
    RULE_FILE_MUST(10023, "商品来源、年份、时间维度、时间范围等必填项为空"),
    RULE_SIZE_SUPPER(10024, "超过最大行数"),
    HOT_SOURCE_WRONG(10025, "商品目录来源错误"),
    HOT_YEAR_WRONG(10026, "年份错误"),
    HOT_DIMENSION_WRONG(10027, "时间维度错误"),
    HOT_FRAME_WRONG(10028, "时间范围格式错误"),
    HOT_DIMENSION_FRAME_WRONG(10029, "时间维度和范围不匹配"),

    TASK_ID_NOTEXIT(10030, "任务ID不存在"),

    CALL_TOC_ERROR(10031, "调用分发器异常"),

    TASK_BDP_ERROR(10032, "大数据处理任务异常"),
    TASK_ID_NULL_ERROR(10033, "任务Id不能为空"),

    SELECTED_NODE_NULL_ERROR(10034, "请选择新开门店"),

    SELECTED_NODE_NOT_FIND_ERROR(10035, "新开门店机构信息查询有误"),
    OPEN_DATE_NULL_ERROR(10036, "预计开业日期不能为空"),

    SELECTED_NODE_DJ_ERROR(10037, "新开门店月销额等级为空"),

    SELECTED_NODE_SQDX_ERROR(10038, "新开门店选址商圈店型为空"),

    SELECTED_NODE_SCDW_ERROR(10039, "新开门店市场地位为空"),
    SELECTED_NODE_SHENGSHIXIAN_ERROR(10040, "新开门店省/城市/区县为空"),
    PLAN_FILE_MUST(10041, "省份、城市、商品编码等必填项为空"),

    MANAGE_PUSH_TYPE_ERROR(10042, "推荐目录回调类型不正确"),
    MANAGE_STORE_ERROR(10043, "未查询到门店信息"),
    MANAGE_PROCESS_ERROR(10043, "暂无数据"),
    MANAGE_CATEGORY_ERROR(10043, "请先选中左侧分类"),
    MANAGE_VERSION_ERROR(10044, "版本参数不能为空"),
    MANAGE_PARAM_ROWS_NULL(10045, "修改数据不能为空"),
    MANAGE_ORG_ERROR(10046, "平台/连锁参数不能为空"),
    MANAGE_NOT_IN_TIME_ERROR(10047, "不在调整周期内"),
    MANAGE_SKU_LIMIT_ERROR(10043, "本类下经营商品（必备+选配）已选%s/上限%s，请先从选配商品中选择%s个不经营，再做调整。"),
    MANAGE_SKU_LOWER_LIMIT_ERROR(10043, "本类下经营商品（必备+选配）已选%s/下限%s，请先从不经营商品中选择%s个改为选配，再做调整。"),
    MANAGE_GROUP_LIMIT_ERROR(10048, "本店型SKU配置数上限集团标准为X，您修改后超过集团标准的105%，请改小。"),
    MANAGE_MAX_ADD_ERROR(10049, "豁免商品已达最大个数，不可再添加"),
    MANAGE_HAS_IN_ERROR(10050, "商品已维护进二轮选配商品清单"),
    MANAGE_PROCESS_STATUS_ERROR(10051, "当前分类状态非待审核"),
    DEVELOP_MODULE_REACH_CHANNEL_NULL(10052, "请选择触达通道"),
    DEVELOP_MODULE_REACH_CHANNEL_ILLEGAL(10053, "存在未识别的触达通道"),
    DEVELOP_MODULE_REACH_GROUPID_NULL(10054, "请选择触达组"),
    MANAGE_MIN_ADD_ERROR(10055, "豁免商品已触底下限，不可删除"),
    ;

    private Integer code;
    private String msg;

    ErrorCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
