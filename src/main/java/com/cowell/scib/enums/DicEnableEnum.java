package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/2/27 15:54
 */
public enum DicEnableEnum {
    DISABLE((byte)0, true),
    UNDISABLE((byte)1, false);

    private byte code;
    private boolean value;

    DicEnableEnum(byte code, boolean value) {
        this.code = code;
        this.value = value;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public boolean isValue() {
        return value;
    }

    public void setValue(boolean value) {
        this.value = value;
    }

    public static boolean getResultByCode(Byte code) {
        if(code == null){
            return false;
        }
        for (DicEnableEnum dicEnableEnum : DicEnableEnum.values()) {
            if (dicEnableEnum.getCode() == code) {
                return dicEnableEnum.isValue();
            }
        }
        return false;
    }
}
