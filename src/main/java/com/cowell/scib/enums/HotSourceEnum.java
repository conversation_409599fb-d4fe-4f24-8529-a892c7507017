package com.cowell.scib.enums;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/3 17:10
 */
public enum HotSourceEnum {
    ELM("饿了"),
    MT_HOT("美团应季热销"),
    MT_RX("美团RX热销");

    String message;

    HotSourceEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static boolean messageExit(String param) {
        if(StringUtils.isEmpty(param)){
            return false;
        }
        for (HotSourceEnum hotSourceEnum : HotSourceEnum.values()) {
            if (hotSourceEnum.getMessage().equals(param)) {
                return true;
            }
        }
        return false;
    }
}
