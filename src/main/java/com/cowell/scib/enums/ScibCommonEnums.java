package com.cowell.scib.enums;

import com.cowell.scib.service.dto.manageContents.ManageCommonDTO;
import lombok.Getter;

import java.util.Arrays;

public interface ScibCommonEnums {


    enum StatusEnum {

        DELETE(-1, "删除"),
        NORMAL(0, "正常"),
        ;
        private Integer code;
        private String name;

        StatusEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(StatusEnum.values()).anyMatch(v -> v.getCode().equals(code));
        }

        public static StatusEnum getEnumByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (StatusEnum type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 经营目录推荐结果BDP回调类型
     */
    enum ManagePushTypeEnum {
        STORE_TYPE_SKU_UPPER_LIMIT_CTRL(1, "店型级SKU数配置上限管控"),
        STORE_MANAGE_CONTENTS_SUGGEST(2, "门店经营目录建议结果"),
        ;

        private Integer code;
        private String message;

        ManagePushTypeEnum(Integer code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(ManageDropTypeEnum.values()).anyMatch(v -> v.getCode().equals(code));
        }

        public static ManagePushTypeEnum getEnumByCode(Integer code) {
            for (ManagePushTypeEnum o : ManagePushTypeEnum.values()) {
                if (o.getCode().equals(code)) {
                    return o;
                }
            }
            return null;
        }
    }

    /**
     * 经营目录推荐结果查询页面 下拉框类型
     */
    enum ManageDropTypeEnum {
        MANAGE_DROP_TYPE_VERSION(1, "版本列表"),
        MANAGE_DROP_TYPE_CITY(2, "城市列表"),
        MANAGE_DROP_TYPE_CATEGORY(3, "分类列表"),
        ;

        private Integer code;
        private String message;

        ManageDropTypeEnum(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(ManageDropTypeEnum.values()).anyMatch(v -> v.getCode().equals(code));
        }

        public static ManageDropTypeEnum getEnumByCode(Integer code) {
            for (ManageDropTypeEnum o : ManageDropTypeEnum.values()) {
                if (o.getCode().equals(code)) {
                    return o;
                }
            }
            return null;
        }
    }
    /**
     * 经营目录
     * 快速定位类型(1:快速定位(传分类id) 2:待确认类别 3:已确认类别 4:通过商品查询 5:已提交未确认类别)
     */
    enum ManageTreeQuickTypeEnum {
        MANAGE_TREE_QUICK_TYPE_CATEGORY(1, "快速定位"),
        MANAGE_TREE_QUICK_TYPE_NEED_CONFIRMED(2, "待确认类别"),
        MANAGE_TREE_QUICK_TYPE_CONFIRMED(3, "已确认类别"),
        MANAGE_TREE_QUICK_TYPE_SUBMIT_FOR_REVIEW(4, "已提交未确认类别"),
        MANAGE_TREE_QUICK_TYPE_GOODS_FILTER(5, "通过商品查询"),
        ;

        private Integer code;
        private String message;

        ManageTreeQuickTypeEnum(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(ManageTreeQuickTypeEnum.values()).anyMatch(v -> v.getCode().equals(code) );
        }

        public static ManageTreeQuickTypeEnum getEnumByCode(Integer code) {
            for (ManageTreeQuickTypeEnum o : ManageTreeQuickTypeEnum.values()) {
                if (o.getCode().equals(code)) {
                    return o;
                }
            }
            return null;
        }
    }

    /**
     * 经营目录-店型级SKU配置数集团标准维护
     */
    enum MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM {
        MANAGE_SKU_LIMIT_UPDATE_BDP_CALLBACK(0, "BDP推送"),
        MANAGE_SKU_LIMIT_UPDATE_BDP_UPDATE(1, "更新上下限"),
        MANAGE_SKU_LIMIT_UPDATE_BDP_DELETE(2, "删除"),
        ;

        private Integer code;
        private String message;

        MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM.values()).anyMatch(v -> v.getCode().equals(code) );
        }

        public static MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM getEnumByCode(Integer code) {
            for (MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM o : MANAGE_SKU_LIMIT_UPDATE_EVENT_ENUM.values()) {
                if (o.getCode().equals(code)) {
                    return o;
                }
            }
            return null;
        }
    }


    /**
     * 经营目录确认页面 支持排序字段
     * 系统建议、我的确认、小类、子类、成分
     */
    enum ManageOrderByEnum {
        MANAGE_ORDER_BY_SUGGEST("suggestManageStatus", "系统建议", ManageCommonDTO::getSuggestManageStatus),
        MANAGE_ORDER_BY_MANAGE("manageStatus", "我的确认", ManageCommonDTO::getManageStatus),
        MANAGE_ORDER_BY_SMALL("smallCategory", "小类", ManageCommonDTO::getSmallCategory),
        MANAGE_ORDER_BY_SUB("subCategory", "子类", ManageCommonDTO::getSubCategory),
        MANAGE_ORDER_BY_COMPONENT("component", "成分", ManageCommonDTO::getComponent),
        ;

        @Getter
        private final String field;
        private final String description;
        @Getter
        private final java.util.function.Function<ManageCommonDTO, String> getter;

        public static boolean containsCode(String code){
            return Arrays.stream(ManageOrderByEnum.values()).anyMatch(v -> v.getField().equals(code) );
        }

        ManageOrderByEnum(String field, String description, java.util.function.Function<ManageCommonDTO, String> getter) {
            this.field = field;
            this.description = description;
            this.getter = getter;
        }

        public static ManageOrderByEnum fromField(String field) {
            for (ManageOrderByEnum orderBy : values()) {
                if (orderBy.getField().equals(field)) {
                    return orderBy;
                }
            }
            return null;
        }
    }

    /**
     * 经营目录 确认进度
     */
    enum ManageConfirmStatusEnum {
        JYML_PROCESS_NEED_CONFIRM(0, "待确认"),
        JYML_PROCESS_SUBMIT_REVIEW(-1, "已提交待确认"),
        JYML_PROCESS_REVIEW_CONFIRM(1, "已确认"),

        ;

        private Integer code;
        private String message;

        ManageConfirmStatusEnum(Integer code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(ManageConfirmStatusEnum.values()).anyMatch(v -> v.getCode().equals(code));
        }

        public static ManageConfirmStatusEnum getEnumByCode(Integer code) {
            for (ManageConfirmStatusEnum o : ManageConfirmStatusEnum.values()) {
                if (o.getCode().equals(code)) {
                    return o;
                }
            }
            return null;
        }
    }

    /**
     * 经营目录确认操作 提交 审核
     */
    enum ManageBatchUpdateActionEnum {
        JYML_BATCH_UPDATE_CONFIRM_SUBMIT(0, "确认提交"),
        JYML_BATCH_UPDATE_SUBMIT_REVIEW(1, "提交审核"),
        JYML_BATCH_UPDATE_REVIEW_CONFIRM(2, "审核确认"),

        ;

        private Integer code;
        private String message;

        ManageBatchUpdateActionEnum(Integer code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static boolean containsCode(Integer code){
            return Arrays.stream(ManageBatchUpdateActionEnum.values()).anyMatch(v -> v.getCode().equals(code));
        }

        public static ManageBatchUpdateActionEnum getEnumByCode(Integer code) {
            for (ManageBatchUpdateActionEnum o : ManageBatchUpdateActionEnum.values()) {
                if (o.getCode().equals(code)) {
                    return o;
                }
            }
            return null;
        }
    }



}