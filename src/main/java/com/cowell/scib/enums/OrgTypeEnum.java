package com.cowell.scib.enums;

import java.util.Objects;

/**
 *   组织机构type
 *  @params:  * @param null
 *  @Description:
 *  @version: 1.0
 */

public enum  OrgTypeEnum {
    GROUP(100),PLATFORM(300),BUSINESS(500),AREA(700),STORE(800);

    private Integer code;
    private OrgTypeEnum(Integer code){
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static OrgTypeEnum getEnumByCode (Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (OrgTypeEnum orgTypeEnum : OrgTypeEnum.values()) {
            if (orgTypeEnum.getCode().equals(code)) {
                return orgTypeEnum;
            }
        }
        return null;
    }
}
