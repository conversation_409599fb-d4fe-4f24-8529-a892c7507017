package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/3/24 10:47
 */
public enum ResponseCodeEnum {
    SUCCESS(200,"组货任务%s成功提交大数据平台，预计3小时可返回建议结果，请耐心等待。"),
    ING(201, "重复提交。"),
    BUSY(202, "大数据平台组货任务繁忙，暂时无法接收新任务。建议您暂存任务单，30分钟后重试。"),
    STORE(203, "本次组货门店存在正在处理的组货任务，不可重复创建。"),
    INTERVAL(204, "大数据平台组货任务需于每天%s之间提交，当前无法提交。建议您暂存任务单。"),
    ERROR(300, "错误或异常。"),
    COMPUTING(302, "大数据平台复盘任务繁忙，暂时无法接收新任务。建议您30分钟后重试。"),
    COMPUTING_XD(302, "大数据平台任务繁忙，暂时无法接收新任务。建议您暂存任务单，10分钟后重试。"),


    ;

    private Integer code;
    private String message;

    ResponseCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
