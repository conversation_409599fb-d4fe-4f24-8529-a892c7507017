package com.cowell.scib.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 销售等级
 * 中心大店：门店月销售额等级=AA类店
 * 大店：门店月销额等级=A类店、B类型
 * 中店：门店月销额等级=C类店、D类店
 * 小店：门店月销额等级=E类店、F类店。
 * <AUTHOR>
 * @date 2023/3/14 22:42
 */
public enum StoreSalesLevelEnum {
    AA("AA","中心大店"),
    A("A","大店"),
    B("B","大店"),
    C("C","中店"),
    D("D","中店"),
    D1("D1","中店"),
    D2("D2","中店"),
    E("E","小店"),
    F("F","小店"),
    F1("F1","小店"),
    F2("F2","小店"),
    F3("F3","小店"),
    ;

    public static String getMessageByCode(String level) {
        if(StringUtils.isBlank(level)){
            return "";
        }
        for (StoreSalesLevelEnum salesLevelEnum : StoreSalesLevelEnum.values()) {
            if (salesLevelEnum.getLevel().equals(level)) {
                return salesLevelEnum.getMessage();
            }
        }
        return "";
    }

    private String level;
    private String message;

    StoreSalesLevelEnum(String level, String message) {
        this.level = level;
        this.message = message;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
