package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/3/11 16:39
 */
public enum BundlTaskStatusEnum {
    CREATING((byte)0, "创建中"),
    SAVE((byte)1, "暂存"),
    COMPUTING((byte)2, "计算中"),
    COMPUTED((byte)3, "计算完成"),
    CANCEL((byte)4, "已作废"),
    UPDATED((byte)5, "已更新");

    private byte code;
    private String message;

    BundlTaskStatusEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (BundlTaskStatusEnum bundlTaskStatusEnum : BundlTaskStatusEnum.values()) {
            if (bundlTaskStatusEnum.getCode() == code) {
                return bundlTaskStatusEnum.getMessage();
            }
        }
        return "";
    }

    public static Boolean taskEdit(Byte code){
        return BundlTaskStatusEnum.SAVE.getCode() == code;
    }

    public static Boolean taskView(Byte code){
        return BundlTaskStatusEnum.SAVE.getCode() != code;
    }

    public static Boolean taskCancel(Byte code){
        return BundlTaskStatusEnum.SAVE.getCode() == code || BundlTaskStatusEnum.COMPUTED.getCode() == code;
    }

    public static Boolean resultEdit(Byte code){
        return BundlTaskStatusEnum.COMPUTED.getCode() == code;
    }

    public static Boolean resultView(Byte code){
        return BundlTaskStatusEnum.UPDATED.getCode() == code;
    }

    public static Boolean resultMdm(Byte code){
        return false;
    }
}
