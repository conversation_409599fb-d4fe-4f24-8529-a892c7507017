package com.cowell.scib.enums;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/3 17:20
 */
public enum HotTimeRelationEnum {
    Q1("季度","Q1"),
    Q2("季度","Q2"),
    Q3("季度","Q3"),
    Q4("季度","Q4"),

    M1("月度","M1"),
    M2("月度","M2"),
    M3("月度","M3"),
    M4("月度","M4"),
    M5("月度","M5"),
    M6("月度","M6"),
    M7("月度","M7"),
    M8("月度","M8"),
    M9("月度","M9"),
    M10("月度","M10"),
    M11("月度","M11"),
    M12("月度","M12");

    private String dimension;
    private String frame;

    public static boolean exit(String param){
        if(StringUtils.isEmpty(param)){
            return false;
        }
        for (HotTimeRelationEnum hotTimeRelationEnum : HotTimeRelationEnum.values()) {
            if (hotTimeRelationEnum.getDimension().equals(param) || hotTimeRelationEnum.getFrame().equals(param)) {
                return true;
            }
        }
        return false;
    }

    public static boolean dimensionExit(String param) {
        if(StringUtils.isEmpty(param)){
            return false;
        }
        for (HotTimeRelationEnum hotTimeRelationEnum : HotTimeRelationEnum.values()) {
            if (hotTimeRelationEnum.getDimension().equals(param)) {
                return true;
            }
        }
        return false;
    }

    public static boolean frameExit(String param) {
        if(StringUtils.isEmpty(param)){
            return false;
        }
        for (HotTimeRelationEnum hotTimeRelationEnum : HotTimeRelationEnum.values()) {
            if (hotTimeRelationEnum.getFrame().equals(param)) {
                return true;
            }
        }
        return false;
    }

    public static boolean dimensionAndFrameExit(String dimension, String frame) {
        if(StringUtils.isEmpty(dimension) || StringUtils.isEmpty(frame)){
            return false;
        }
        for (HotTimeRelationEnum hotTimeRelationEnum : HotTimeRelationEnum.values()) {
            if (hotTimeRelationEnum.getFrame().equals(frame) && hotTimeRelationEnum.getDimension().equals(dimension)) {
                return true;
            }
        }
        return false;
    }

    HotTimeRelationEnum(String dimension, String frame) {
        this.dimension = dimension;
        this.frame = frame;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getFrame() {
        return frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }
}
