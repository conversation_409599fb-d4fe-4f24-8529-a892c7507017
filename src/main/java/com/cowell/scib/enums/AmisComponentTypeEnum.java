package com.cowell.scib.enums;


/**
 * @program: scib
 * @description: Amis组件类型
 * @author: jmlu
 * @create: 2022-04-02 17:20
 **/
public enum AmisComponentTypeEnum {

    TEXT("text", "编辑框"),
    INPUT_NUMBER("input-number", "数字输入框"),
    SELECT("select", "选择框"),
    OPERATION("operation", "操作"),
    TABS("tabs", "选项卡");


    String code;

    String desc;

    AmisComponentTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
