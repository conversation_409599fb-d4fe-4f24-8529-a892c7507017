package com.cowell.scib.enums;

/**
 * <AUTHOR>
 * @date 2023/3/5 16:31
 */
public enum PerprotyTypeEnum {
    OBJ((byte)1),CHECKBOX((byte)2),COLLECTION((byte)3);
    private byte code;

    PerprotyTypeEnum(byte code) {
        this.code = code;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public static boolean isObj(byte paramCode){
        return PerprotyTypeEnum.OBJ.getCode() == paramCode;
    }

    public static boolean isCheckbox(byte paramCode){
        return PerprotyTypeEnum.CHECKBOX.getCode() == paramCode;
    }
}
