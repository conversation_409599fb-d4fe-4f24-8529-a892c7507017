package com.cowell.scib.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static cn.hutool.core.util.NumberUtil.add;

/**
 * 经营状态枚举
 */
public enum ManageStatusEnum {
    NONE(0,""), // 无
    STORE_NEW_GOODS(1,"店级新品"),
    MANAGE_NECESARY( 2,"经营-必备"),
    MANAGE_CHOOSE( 3,"经营-选配"),
    NON_MANAGE(4, "不经营"),
    ORDER(5, "订购(DG)"),
    OBSOLETE(6, "淘汰(T)"),
    SITE_CLEARANCE(7, "清场(C)"),
    INVALID(8, "作废(Z)"),
    PROPOSED_OBSOLETE(9, "拟淘汰"),
    NON_GOODS(10, "非商品(F)"),
    ;
    private Integer code;
    private String message;

    ManageStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ManageStatusEnum getEnumBySuggestCode(Integer code) {
        if (null == code) {
            return null;
        }
        SuggestManageStatusEnum suggestEunm = SuggestManageStatusEnum.getEnumByCode(code);
        if (null == suggestEunm) {
            return null;
        }
        switch (suggestEunm) {
            case STORE_NEW_GOODS:return STORE_NEW_GOODS;
            case MANAGE_NECESARY:return MANAGE_NECESARY;
            case SUGGEST_MANAGE_CHOOSE:return MANAGE_CHOOSE;
            case SUGGEST_NON_MANAGE: return NON_MANAGE;
            case MUST_NON_MANAGE: return NON_MANAGE;
            case MUST_MANAGE_CHOOSE: return MANAGE_CHOOSE;
            default:return null;
        }
    }
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public final static List<Integer> jyStatusList = Lists.newArrayList(MANAGE_NECESARY.getCode(),MANAGE_CHOOSE.getCode());

    public static ManageStatusEnum getEnumByCode(Integer code) {
        if(null == code){
            return null;
        }
        for (ManageStatusEnum manageStatusEnum : ManageStatusEnum.values()) {
            if (manageStatusEnum.getCode().equals(code)) {
                return manageStatusEnum;
            }
        }
        return null;
    }
    public static ManageStatusEnum getEnumByMessage(String message) {
        if(StringUtils.isBlank(message)){
            return null;
        }
        for (ManageStatusEnum manageStatusEnum : ManageStatusEnum.values()) {
            if (manageStatusEnum.getMessage().equals(message)) {
                return manageStatusEnum;
            }
        }
        return null;
    }
}
