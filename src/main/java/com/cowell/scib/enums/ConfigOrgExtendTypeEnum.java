package com.cowell.scib.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/8/30 16:56
 */
public enum ConfigOrgExtendTypeEnum {
    STORE((byte)1, "门店"),
    GOODS((byte)2, "商品");

    private byte code;
    private String message;

    ConfigOrgExtendTypeEnum(byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static boolean exitCode(byte code){
        return Arrays.stream(ConfigOrgExtendTypeEnum.values()).filter(v->v.getCode()==code).count()>0;
    }

    public static String getUseStatusName(byte code) {
        for (ConfigOrgExtendTypeEnum o : ConfigOrgExtendTypeEnum.values()) {
            if (o.getCode()==code) {
                return o.getMessage();
            }
        }
        return "";
    }

}
