package com.cowell.scib.enums;

public enum OperationTypeEnum {
    SAVE(1, "保存"),
    EDIT(2, "编辑"),
    DELETE(3, "删除");

    private final int code;
    private final String description;

    OperationTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取对应的枚举值
     * @param code 枚举代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static OperationTypeEnum fromCode(int code) {
        for (OperationTypeEnum type : OperationTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证是否存在指定代码的枚举值
     * @param code 要验证的代码
     * @return 如果存在对应的枚举值，返回true；否则返回false
     */
    public static boolean existsCode(int code) {
        for (OperationTypeEnum type : OperationTypeEnum.values()) {
            if (type.getCode() == code) {
                return true;
            }
        }
        return false;
    }
}
