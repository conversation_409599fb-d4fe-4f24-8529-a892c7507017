package com.cowell.scib.enums;

/**
 * 组货通用类布尔枚举
 * <AUTHOR>
 * @date 2023/3/13 15:12
 */
public enum BundlBoolEnum {
    NO((byte)0, "否"),
    YES((byte)1, "是");

    public static String getMessageByCode(Byte code) {
        if(code == null){
            return "";
        }
        for (BundlBoolEnum bundlBoolEnum : BundlBoolEnum.values()) {
            if (bundlBoolEnum.getCode() == code) {
                return bundlBoolEnum.getMessage();
            }
        }
        return BundlBoolEnum.NO.getMessage();
    }

    private Byte code;
    private String message;

    BundlBoolEnum(Byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
