package com.cowell.scib;

import com.cowell.scib.constant.Constants;
import com.cowell.scib.entityDgms.ConfigOrg;
import com.cowell.scib.mapperDgms.ConfigOrgMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ScibApplication.class})
public class ScibApplicationTests {

    @Autowired
    ConfigOrgMapper configOrgMapper;

    @Test
    public void contextLoads() {
        try {
            ConfigOrg configOrg = new ConfigOrg();
            configOrg.setConfigType((byte)1);
            configOrg.setOrgId(16L);
            configOrg.setOrgName("西南");
            configOrg.setOutId(15555L);
            configOrg.setSapCode("3034");
            configOrg.setStatus(Constants.NORMAL_STATUS);
            configOrg.setExtend("");
            configOrg.setVersion(0);
            configOrg.setCreatedBy(29552792110000L);
            configOrg.setCreatedName("userDTO.getName()");
            configOrg.setUpdatedBy(29552792110000L);
            configOrg.setUpdatedName("问问");

            int id = configOrgMapper.insert(configOrg);
            System.out.println(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
